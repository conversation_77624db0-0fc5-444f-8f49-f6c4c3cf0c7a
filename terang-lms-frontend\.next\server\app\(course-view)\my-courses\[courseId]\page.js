try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="93907019-c573-4552-aa3d-698e190b9f07",e._sentryDebugIdIdentifier="sentry-dbid-93907019-c573-4552-aa3d-698e190b9f07")}catch(e){}(()=>{var e={};e.id=9188,e.ids=[9188],e.modules={94:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(55732).A)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},1708:e=>{"use strict";e.exports=require("node:process")},2457:(e,t,s)=>{"use strict";s.d(t,{Bc:()=>n,ZI:()=>d,k$:()=>o,m_:()=>l});var a=s(91754);s(93491);var r=s(99830),i=s(82233);function n({delayDuration:e=0,...t}){return(0,a.jsx)(r.Kq,{"data-slot":"tooltip-provider",delayDuration:e,...t,"data-sentry-element":"TooltipPrimitive.Provider","data-sentry-component":"TooltipProvider","data-sentry-source-file":"tooltip.tsx"})}function l({...e}){return(0,a.jsx)(n,{"data-sentry-element":"TooltipProvider","data-sentry-component":"Tooltip","data-sentry-source-file":"tooltip.tsx",children:(0,a.jsx)(r.bL,{"data-slot":"tooltip",...e,"data-sentry-element":"TooltipPrimitive.Root","data-sentry-source-file":"tooltip.tsx"})})}function o({...e}){return(0,a.jsx)(r.l9,{"data-slot":"tooltip-trigger",...e,"data-sentry-element":"TooltipPrimitive.Trigger","data-sentry-component":"TooltipTrigger","data-sentry-source-file":"tooltip.tsx"})}function d({className:e,sideOffset:t=0,children:s,...n}){return(0,a.jsx)(r.ZL,{"data-sentry-element":"TooltipPrimitive.Portal","data-sentry-component":"TooltipContent","data-sentry-source-file":"tooltip.tsx",children:(0,a.jsxs)(r.UC,{"data-slot":"tooltip-content",sideOffset:t,className:(0,i.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",e),...n,"data-sentry-element":"TooltipPrimitive.Content","data-sentry-source-file":"tooltip.tsx",children:[s,(0,a.jsx)(r.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]","data-sentry-element":"TooltipPrimitive.Arrow","data-sentry-source-file":"tooltip.tsx"})]})})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6281:(e,t,s)=>{Promise.resolve().then(s.bind(s,9688))},7441:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>m,tree:()=>o});var a=s(95500),r=s(56947),i=s(26052),n=s(13636),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);s.d(t,l);let o={children:["",{children:["(course-view)",{children:["my-courses",{children:["[courseId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,9688)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\(course-view)\\my-courses\\[courseId]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,6294)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\(course-view)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,98036,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,72309,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e),async e=>(await Promise.resolve().then(s.bind(s,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,4082)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(s.bind(s,26052)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,76679)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,98036,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,72309,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e),async e=>(await Promise.resolve().then(s.bind(s,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\(course-view)\\my-courses\\[courseId]\\page.tsx"],c={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/(course-view)/my-courses/[courseId]/page",pathname:"/my-courses/[courseId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},8086:e=>{"use strict";e.exports=require("module")},9688:(e,t,s)=>{"use strict";let a;s.r(t),s.d(t,{default:()=>x,generateImageMetadata:()=>m,generateMetadata:()=>c,generateViewport:()=>u});var r=s(63033),i=s(1472),n=s(7688),l=(0,i.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\(course-view)\\\\my-courses\\\\[courseId]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\(course-view)\\my-courses\\[courseId]\\page.tsx","default");let o={...r},d="workUnitAsyncStorage"in o?o.workUnitAsyncStorage:"requestAsyncStorage"in o?o.requestAsyncStorage:void 0;a="function"==typeof l?new Proxy(l,{apply:(e,t,s)=>{let a,r,i;try{let e=d?.getStore();a=e?.headers.get("sentry-trace")??void 0,r=e?.headers.get("baggage")??void 0,i=e?.headers}catch{}return n.wrapServerComponentWithSentry(e,{componentRoute:"/(course-view)/my-courses/[courseId]",componentType:"Page",sentryTraceHeader:a,baggageHeader:r,headers:i}).apply(t,s)}}):l;let c=void 0,m=void 0,u=void 0,x=a},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},20388:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(55732).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},21381:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(55732).A)("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},21820:e=>{"use strict";e.exports=require("os")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},32777:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>eu});var a=s(91754),r=s(93491),i=s.n(r),n=s(56682),l=s(5827),o=s(93438),d=s(54090),c=s(80601),m=s(37615),u=s(15349);let x=(0,u.A)("Building02Icon",[["path",{d:"M15 2H9C5.69067 2 5 2.69067 5 6V22H19V6C19 2.69067 18.3093 2 15 2Z",stroke:"currentColor",key:"k0"}],["path",{d:"M3 22H21",stroke:"currentColor",key:"k1"}],["path",{d:"M15 22V19C15 17.3453 14.6547 17 13 17H11C9.34533 17 9 17.3453 9 19V22",stroke:"currentColor",key:"k2"}],["path",{d:"M13.5 6H10.5M13.5 9.5H10.5M13.5 13H10.5",stroke:"currentColor",key:"k3"}]]),p=(0,u.A)("Calendar01Icon",[["path",{d:"M18 2V4M6 2V4",stroke:"currentColor",key:"k0"}],["path",{d:"M10 17L9.99999 13.3472C9.99999 13.1555 9.86325 13 9.69458 13H9M13.6297 17L14.9842 13.3492C15.0475 13.1785 14.9128 13 14.7207 13H13",stroke:"currentColor",key:"k1"}],["path",{d:"M2.5 12.2432C2.5 7.88594 2.5 5.70728 3.75212 4.35364C5.00424 3 7.01949 3 11.05 3H12.95C16.9805 3 18.9958 3 20.2479 4.35364C21.5 5.70728 21.5 7.88594 21.5 12.2432V12.7568C21.5 17.1141 21.5 19.2927 20.2479 20.6464C18.9958 22 16.9805 22 12.95 22H11.05C7.01949 22 5.00424 22 3.75212 20.6464C2.5 19.2927 2.5 17.1141 2.5 12.7568V12.2432Z",stroke:"currentColor",key:"k2"}],["path",{d:"M6 8H18",stroke:"currentColor",key:"k3"}]]);var h=s(66064);let f=(0,u.A)("ChartIcon",[["path",{d:"M2 21.5L22 21.5",stroke:"currentColor",key:"k0"}],["path",{d:"M18 15.5H18.009M18 18.5H18.009",stroke:"currentColor",key:"k1"}],["path",{d:"M6 18.5H6.00898M6 15.5H6.00898M6 12.5H6.00898M6 9.5H6.00898",stroke:"currentColor",key:"k2"}],["path",{d:"M7.79063 5.39186L16.2183 9.5904M8 4.5C8 5.60457 7.10457 6.5 6 6.5C4.89543 6.5 4 5.60457 4 4.5C4 3.39543 4.89543 2.5 6 2.5C7.10457 2.5 8 3.39543 8 4.5ZM20 10.5C20 11.6046 19.1046 12.5 18 12.5C16.8954 12.5 16 11.6046 16 10.5C16 9.39543 16.8954 8.5 18 8.5C19.1046 8.5 20 9.39543 20 10.5Z",stroke:"currentColor",key:"k3"}]]);var g=s(68451);let y=(0,u.A)("Download01Icon",[["path",{d:"M3.09502 10C3.03241 10.457 3 10.9245 3 11.4C3 16.7019 7.02944 21 12 21C16.9706 21 21 16.7019 21 11.4C21 10.9245 20.9676 10.457 20.905 10",stroke:"currentColor",key:"k0"}],["path",{d:"M12 13L12 3M12 13C11.2998 13 9.99153 11.0057 9.5 10.5M12 13C12.7002 13 14.0085 11.0057 14.5 10.5",stroke:"currentColor",key:"k1"}]]);var b=s(16041),v=s.n(b),j=s(21372),N=s(74829),k=s(82763),w=s(76328),C=s(33093),A=s(44331),z=s(47028),P=s(79898),q=s(2457);let S=({title:e,icon:t,isUnlocked:s,isCompleted:r=!1,children:i,level:n,isExpanded:l=!1,onToggle:o,onClick:d,hasChildren:c=!1,isActive:m=!1})=>{let u=e.length>20;return(0,a.jsxs)("div",{className:"select-none","data-sentry-component":"TreeNode","data-sentry-source-file":"tree-node.tsx",children:[(0,a.jsx)(q.Bc,{"data-sentry-element":"TooltipProvider","data-sentry-source-file":"tree-node.tsx",children:(0,a.jsxs)(q.m_,{delayDuration:500,"data-sentry-element":"Tooltip","data-sentry-source-file":"tree-node.tsx",children:[(0,a.jsx)(q.k$,{asChild:!0,"data-sentry-element":"TooltipTrigger","data-sentry-source-file":"tree-node.tsx",children:(0,a.jsxs)("button",{onClick:()=>{c&&o?o():d&&d()},className:`flex w-full items-center space-x-3 rounded-lg px-4 py-3 text-left text-base transition-colors ${s?m?"bg-blue-100 text-blue-800 border-2 border-blue-300 hover:bg-blue-150":"text-blue-700 hover:bg-blue-50":"cursor-not-allowed text-gray-400"}`,style:{paddingLeft:`${20*n+16}px`},disabled:!s,children:[c&&(0,a.jsx)("span",{className:"flex h-5 w-5 items-center justify-center",children:l?(0,a.jsx)(C.A,{className:"h-4 w-4"}):(0,a.jsx)(A.A,{className:"h-4 w-4"})}),(0,a.jsx)("span",{className:"flex h-5 w-5 items-center justify-center",children:t}),(0,a.jsx)("span",{className:`flex-1 font-medium ${e.length>35?"text-sm leading-tight":"truncate"}`,children:e.length>35?(0,a.jsx)("span",{className:"block",children:e}):e}),!s&&(0,a.jsx)(z.A,{className:"h-4 w-4"}),r&&(0,a.jsx)(P.A,{className:"h-4 w-4 text-green-500"})]})}),u&&(0,a.jsx)(q.ZI,{side:"right",className:"max-w-xs",children:(0,a.jsx)("p",{children:e})})]})}),l&&i&&(0,a.jsx)("div",{children:i})]})};var T=s(9260),D=s(55732);let M=(0,D.A)("List",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]]);var E=s(41867),$=s(63890),I=s(21381),U=s(85629);let L=(0,D.A)("BookMarked",[["path",{d:"M10 2v8l3-3 3 3V2",key:"sqw3rj"}],["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20",key:"k3hazp"}]]);var _=s(96249);let R=(0,D.A)("Trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]]),B=({course:e,onNavigate:t,expandedModules:s,expandedChapters:r,onToggleModule:i,onToggleChapter:n,currentModuleIndex:l})=>(0,a.jsxs)(T.Zp,{className:"sticky top-4 h-fit w-full max-w-full","data-sentry-element":"Card","data-sentry-component":"TableOfContents","data-sentry-source-file":"table-of-contents.tsx",children:[(0,a.jsx)(T.aR,{className:"pb-4","data-sentry-element":"CardHeader","data-sentry-source-file":"table-of-contents.tsx",children:(0,a.jsxs)(T.ZB,{className:"flex items-center text-xl","data-sentry-element":"CardTitle","data-sentry-source-file":"table-of-contents.tsx",children:[(0,a.jsx)(M,{className:"mr-3 h-6 w-6","data-sentry-element":"List","data-sentry-source-file":"table-of-contents.tsx"}),"Table of Contents"]})}),(0,a.jsxs)(T.Wu,{className:"max-h-[calc(100vh-12rem)] space-y-1 overflow-y-auto overflow-x-hidden","data-sentry-element":"CardContent","data-sentry-source-file":"table-of-contents.tsx",children:[e.modules.map((e,o)=>{let d=e.chapters.every(e=>e.contents.every(e=>e.isCompleted)&&e.quiz.isPassed)&&e.moduleQuiz.isPassed;return(0,a.jsxs)(S,{id:e.id,title:e.title,icon:(0,a.jsx)(E.A,{className:"h-5 w-5"}),isUnlocked:e.isUnlocked,isCompleted:d,level:0,hasChildren:!0,isExpanded:s[e.id],onToggle:()=>i(e.id),isActive:l===o,children:[e.chapters.map(s=>{let i=s.contents.every(e=>e.isCompleted)&&s.quiz.isPassed;return(0,a.jsxs)(S,{id:s.id,title:s.title,icon:(0,a.jsx)($.A,{className:"h-4 w-4"}),isUnlocked:s.isUnlocked,isCompleted:i,level:1,hasChildren:!0,isExpanded:r[s.id],onToggle:()=>n(s.id),children:[s.contents.map(r=>(0,a.jsx)(S,{id:r.id,title:r.title,icon:"video"===r.type?(0,a.jsx)(I.A,{className:"h-4 w-4 text-red-500"}):"pdf"===r.type?(0,a.jsx)(U.A,{className:"h-4 w-4 text-red-600"}):"zoom-recording"===r.type?(0,a.jsx)(I.A,{className:"h-4 w-4 text-blue-500"}):(0,a.jsx)(L,{className:"h-4 w-4 text-blue-500"}),isUnlocked:s.isUnlocked,isCompleted:r.isCompleted,level:2,onClick:()=>t(e.id,s.id,r.id)},r.id)),(0,a.jsx)(S,{id:`${s.id}-quiz`,title:"Chapter Quiz",icon:(0,a.jsx)(_.A,{className:"h-4 w-4"}),isUnlocked:s.contents.every(e=>e.isCompleted),isCompleted:s.quiz.isPassed,level:2,onClick:()=>t(e.id,s.id)},`${s.id}-quiz`)]},s.id)}),(0,a.jsx)(S,{id:`${e.id}-quiz`,title:"Module Quiz",icon:(0,a.jsx)(L,{className:"h-4 w-4"}),isUnlocked:e.chapters.every(e=>e.contents.every(e=>e.isCompleted)&&(e.quiz.isPassed||e.quiz.attempts>=e.quiz.maxAttempts)),isCompleted:e.moduleQuiz.isPassed,level:1,onClick:()=>t(e.id)},`${e.id}-quiz`)]},e.id)}),(0,a.jsx)(S,{id:"final-exam",title:"Final Exam",icon:(0,a.jsx)(R,{className:"h-5 w-5"}),isUnlocked:e.modules.every(e=>e.chapters.every(e=>e.contents.every(e=>e.isCompleted)&&(e.quiz.isPassed||e.quiz.attempts>=e.quiz.maxAttempts))&&(e.moduleQuiz.isPassed||e.moduleQuiz.attempts>=e.moduleQuiz.maxAttempts)),isCompleted:e.finalExam.isPassed,level:0,"data-sentry-element":"TreeNode","data-sentry-source-file":"table-of-contents.tsx"},"final-exam")]})]});var K=s(40515),Q=s(78918),H=s(7018);let O=(0,D.A)("Timer",[["line",{x1:"10",x2:"14",y1:"2",y2:"2",key:"14vaq8"}],["line",{x1:"12",x2:"15",y1:"14",y2:"11",key:"17fdiu"}],["circle",{cx:"12",cy:"14",r:"8",key:"1e1u0o"}]]);var F=s(20388),G=s(69931),Z=s(80506);let V=(0,D.A)("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]),W=({content:e,onToggleComplete:t,isExpanded:s,onToggleExpand:r})=>{console.log("ContentItem received content:",e);let i=e.title||("object"==typeof e.content&&null!==e.content&&"value"in e.content&&"string"==typeof e.content.value?(e=>{for(let t of e.split("\n")){let e=t.match(/^#{1,4}\s+(.*)$/);if(e&&e[1])return e[1].trim()}return null})(e.content.value):null)||"No Title";return(0,a.jsx)(T.Zp,{id:`content-${e.id}`,className:"my-2 ml-6 border-l-4 border-l-blue-200 scroll-mt-20","data-sentry-element":"Card","data-sentry-component":"ContentItem","data-sentry-source-file":"content-item.tsx",children:(0,a.jsx)(T.Wu,{className:"py-3","data-sentry-element":"CardContent","data-sentry-source-file":"content-item.tsx",children:(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsxs)("div",{className:"flex cursor-pointer items-center justify-between",onClick:r,children:[(0,a.jsxs)("div",{className:"flex flex-1 items-center space-x-3",children:[(()=>{switch(e.type){case"video":return(0,a.jsx)(I.A,{className:"h-4 w-4 text-red-500"});case"pdf":return(0,a.jsx)(U.A,{className:"h-4 w-4 text-red-600"});case"zoom-recording":return(0,a.jsx)(I.A,{className:"h-4 w-4 text-blue-500"});case"image":return(0,a.jsx)(H.A,{className:"h-4 w-4 text-green-500"});default:return(0,a.jsx)(L,{className:"h-4 w-4 text-blue-500"})}})(),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:i}),(0,a.jsxs)("div",{className:"mt-1 flex items-center space-x-2",children:[(0,a.jsx)(c.E,{variant:"outline",className:"text-xs","data-sentry-element":"Badge","data-sentry-source-file":"content-item.tsx",children:(()=>{switch(e.type){case"video":return"Video";case"pdf":return"PDF Document";case"zoom-recording":return"Zoom Recording";case"image":return"Image";default:return"Reading Material"}})()}),e.duration&&(0,a.jsxs)(c.E,{variant:"outline",className:"text-xs",children:[(0,a.jsx)(O,{className:"mr-1 h-3 w-3"}),e.duration," min"]})]})]})]}),(0,a.jsx)("div",{className:"flex items-center space-x-2",children:s?(0,a.jsx)(F.A,{className:"h-4 w-4 text-gray-400"}):(0,a.jsx)(C.A,{className:"h-4 w-4 text-gray-400"})})]}),s&&(0,a.jsxs)("div",{className:"mt-4 border-t pt-4 pl-7",children:["text"===e.type?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"prose prose-sm max-w-none text-gray-700",children:(0,a.jsx)(K.oz,{remarkPlugins:[Q.A],components:{h1:({node:e,...t})=>(0,a.jsx)("h1",{className:"mb-4 text-2xl font-bold text-gray-900",...t}),h2:({node:e,...t})=>(0,a.jsx)("h2",{className:"mb-3 text-xl font-semibold text-gray-800",...t}),h3:({node:e,...t})=>(0,a.jsx)("h3",{className:"mb-2 text-lg font-semibold text-gray-800",...t}),h4:({node:e,...t})=>(0,a.jsx)("h4",{className:"mb-2 text-base font-semibold text-gray-700",...t}),p:({node:e,...t})=>(0,a.jsx)("p",{className:"mb-3 leading-relaxed",...t}),ul:({node:e,...t})=>(0,a.jsx)("ul",{className:"mb-3 ml-4 list-disc",...t}),ol:({node:e,...t})=>(0,a.jsx)("ol",{className:"mb-3 ml-4 list-decimal",...t}),li:({node:e,...t})=>(0,a.jsx)("li",{className:"mb-1",...t}),blockquote:({node:e,...t})=>(0,a.jsx)("blockquote",{className:"mb-4 rounded-r border-l-4 border-blue-200 bg-blue-50 py-2 pl-4 italic",...t}),code:({node:e,className:t,children:s,...r})=>/language-(\w+)/.exec(t||"")?(0,a.jsx)("code",{className:"block overflow-x-auto rounded bg-gray-900 p-4 font-mono text-sm text-gray-100",...r,children:s}):(0,a.jsx)("code",{className:"rounded bg-gray-100 px-1 py-0.5 font-mono text-sm",...r,children:s}),pre:({node:e,...t})=>(0,a.jsx)("pre",{className:"mb-4",...t}),table:({node:e,...t})=>(0,a.jsx)("div",{className:"mb-4 overflow-x-auto",children:(0,a.jsx)("table",{className:"min-w-full rounded border border-gray-200",...t})}),thead:({node:e,...t})=>(0,a.jsx)("thead",{className:"bg-gray-50",...t}),th:({node:e,...t})=>(0,a.jsx)("th",{className:"border border-gray-200 px-3 py-2 text-left font-semibold",...t}),td:({node:e,...t})=>(0,a.jsx)("td",{className:"border border-gray-200 px-3 py-2",...t}),hr:({node:e,...t})=>(0,a.jsx)("hr",{className:"my-6 border-gray-300",...t}),strong:({node:e,...t})=>(0,a.jsx)("strong",{className:"font-semibold text-gray-900",...t}),em:({node:e,...t})=>(0,a.jsx)("em",{className:"italic",...t})},children:"string"==typeof e.content?e.content:"object"==typeof e.content&&null!==e.content&&"value"in e.content&&"string"==typeof e.content.value?e.content.value:Array.isArray(e.content)?e.content.map(e=>"text"===e.type?e.value:"").join(""):""})}),(0,a.jsxs)(n.$,{size:"sm",variant:"outline",className:"border-blue-200 text-blue-600 hover:bg-blue-50",onClick:()=>{let t=window.open("","_blank");if(!t)return;let s=`
      <!DOCTYPE html>
      <html>
        <head>
          <title>${e.title}</title>
          <style>
            @media print {
              @page {
                size: A4;
                margin: 2cm;
              }
              body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
                line-height: 1.6;
                color: #333;
              }
            }
            body {
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
              line-height: 1.6;
              color: #333;
              max-width: 800px;
              margin: 0 auto;
              padding: 20px;
            }
            h1 { color: #2d3748; font-size: 2em; margin-bottom: 1em; }
            h2 { color: #4a5568; font-size: 1.5em; margin: 1.5em 0 0.5em; }
            h3 { color: #4a5568; font-size: 1.25em; margin: 1.2em 0 0.5em; }
            h4 { color: #718096; font-size: 1.1em; margin: 1em 0 0.5em; }
            p { margin-bottom: 1em; }
            ul, ol { margin-bottom: 1em; padding-left: 2em; }
            li { margin-bottom: 0.25em; }
            blockquote {
              border-left: 4px solid #3182ce;
              background: #ebf8ff;
              padding: 1em;
              margin: 1em 0;
              font-style: italic;
            }
            code {
              background: #f7fafc;
              padding: 0.2em 0.4em;
              border-radius: 3px;
              font-family: 'Courier New', monospace;
              font-size: 0.9em;
            }
            pre {
              background: #2d3748;
              color: #f7fafc;
              padding: 1em;
              border-radius: 5px;
              overflow-x: auto;
              margin: 1em 0;
            }
            pre code {
              background: none;
              padding: 0;
              color: inherit;
            }
            table {
              border-collapse: collapse;
              width: 100%;
              margin: 1em 0;
            }
            th, td {
              border: 1px solid #e2e8f0;
              padding: 0.5em;
              text-align: left;
            }
            th {
              background: #f7fafc;
              font-weight: 600;
            }
            hr {
              border: none;
              height: 1px;
              background: #e2e8f0;
              margin: 2em 0;
            }
            strong { font-weight: 600; }
            em { font-style: italic; }
          </style>
        </head>
        <body>
          <h1>${e.title}</h1>
          <div id="markdown-content"></div>
        </body>
      </html>
    `;t.document.write(s),t.document.close();let a=t.document.getElementById("markdown-content");if(a){let t="";"string"==typeof e.content?t=e.content:"object"==typeof e.content&&null!==e.content&&"value"in e.content&&"string"==typeof e.content.value?t=e.content.value:Array.isArray(e.content)&&(t=e.content.map(e=>"text"===e.type?e.value:"").join("")),a.innerHTML=t=(t="<p>"+(t=(t=(t=(t=(t=(t=(t=(t=(t=t.replace(/^### (.*$)/gim,"<h3>$1</h3>")).replace(/^## (.*$)/gim,"<h2>$1</h2>")).replace(/^# (.*$)/gim,"<h1>$1</h1>")).replace(/\*\*(.*)\*\*/gim,"<strong>$1</strong>")).replace(/\*(.*)\*/gim,"<em>$1</em>")).replace(/^\* (.*$)/gim,"<li>$1</li>")).replace(/(<li>.*<\/li>)/gim,"<ul>$1</ul>")).replace(/^\d+\. (.*$)/gim,"<li>$1</li>")).replace(/\n\n/g,"</p><p>"))+"</p>").replace(/<p><\/p>/g,"")}setTimeout(()=>{t.focus(),t.print(),t.close()},250)},children:[(0,a.jsx)(G.A,{className:"mr-2 h-4 w-4"}),"Download as PDF"]})]}):"pdf"===e.type?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("iframe",{src:`${"string"==typeof e.content?e.content:"object"==typeof e.content&&null!==e.content&&"value"in e.content&&"string"==typeof e.content.value?e.content.value:""}#toolbar=0&navpanes=0`,className:"w-full h-96 rounded border",title:e.title}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)(n.$,{size:"sm",variant:"outline",className:"border-blue-200 text-blue-600 hover:bg-blue-50",onClick:()=>{let t="string"==typeof e.content?e.content:"object"==typeof e.content&&null!==e.content&&"value"in e.content&&"string"==typeof e.content.value?e.content.value:"";t&&window.open(t,"_blank")},children:[(0,a.jsx)(Z.A,{className:"mr-2 h-4 w-4"}),"Open in New Tab"]}),(0,a.jsxs)(n.$,{size:"sm",variant:"outline",className:"text-gray-600 hover:bg-gray-50",onClick:()=>{let t="string"==typeof e.content?e.content:"object"==typeof e.content&&null!==e.content&&"value"in e.content&&"string"==typeof e.content.value?e.content.value:"";if(t){let s=document.createElement("a");s.href=t,s.download=e.title||"document.pdf",document.body.appendChild(s),s.click(),document.body.removeChild(s)}},children:[(0,a.jsx)(G.A,{className:"mr-2 h-4 w-4"}),"Download"]})]})]}):"image"===e.type?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("img",{src:"string"==typeof e.content?e.content:"object"==typeof e.content&&null!==e.content&&"value"in e.content&&"string"==typeof e.content.value?e.content.value:"",alt:e.title||"Image",className:"max-w-full h-auto rounded border"}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)(n.$,{size:"sm",variant:"outline",className:"border-blue-200 text-blue-600 hover:bg-blue-50",onClick:()=>{let t="string"==typeof e.content?e.content:"object"==typeof e.content&&null!==e.content&&"value"in e.content&&"string"==typeof e.content.value?e.content.value:"";t&&window.open(t,"_blank")},children:[(0,a.jsx)(V,{className:"mr-2 h-4 w-4"}),"Open in New Tab"]}),(0,a.jsxs)(n.$,{size:"sm",variant:"outline",className:"text-gray-600 hover:bg-gray-50",onClick:()=>{let t="string"==typeof e.content?e.content:"object"==typeof e.content&&null!==e.content&&"value"in e.content&&"string"==typeof e.content.value?e.content.value:"";if(t){let s=document.createElement("a");s.href=t,s.download=e.title||"image.jpg",document.body.appendChild(s),s.click(),document.body.removeChild(s)}},children:[(0,a.jsx)(G.A,{className:"mr-2 h-4 w-4"}),"Download"]})]})]}):"video"===e.type||"zoom-recording"===e.type?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"aspect-video w-full overflow-hidden rounded-lg bg-gray-100",children:(()=>{let t="string"==typeof e.content?e.content:"object"==typeof e.content&&null!==e.content&&"value"in e.content&&"string"==typeof e.content.value?e.content.value:"";if(!t)return(0,a.jsx)("div",{className:"flex h-full w-full items-center justify-center text-center text-gray-500",children:"No video URL provided."});if(t.includes("youtube.com/watch?v=")||t.includes("youtu.be/")){let s=t.split("v=")[1]?.split("&")[0]||t.split("/").pop();return(0,a.jsx)("iframe",{className:"h-full w-full",src:`https://www.youtube.com/embed/${s}`,frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowFullScreen:!0,title:e.title})}if(!t.includes("vimeo.com/"))return(0,a.jsx)("video",{controls:!0,className:"h-full w-full",src:t,title:e.title,children:"Your browser does not support the video tag."});{let s=t.split("/").pop();return(0,a.jsx)("iframe",{className:"h-full w-full",src:`https://player.vimeo.com/video/${s}`,frameBorder:"0",allow:"autoplay; fullscreen; picture-in-picture",allowFullScreen:!0,title:e.title})}})()}),(0,a.jsx)("div",{className:"flex space-x-2",children:(0,a.jsxs)(n.$,{size:"sm",variant:"outline",className:"border-blue-200 text-blue-600 hover:bg-blue-50",onClick:()=>{let t="string"==typeof e.content?e.content:"object"==typeof e.content&&null!==e.content&&"value"in e.content&&"string"==typeof e.content.value?e.content.value:"";t&&window.open(t,"_blank")},children:[(0,a.jsx)(V,{className:"mr-2 h-4 w-4"}),"Open in New Tab"]})})]}):(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsx)("div",{className:"flex aspect-video items-center justify-center rounded-lg bg-gray-100",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(I.A,{className:"mx-auto mb-2 h-12 w-12 text-gray-400"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Unsupported Media Type or Invalid Content."})]})})}),(0,a.jsx)("div",{className:"mt-4 pt-4 border-t",children:(0,a.jsxs)(n.$,{size:"sm",variant:e.isCompleted?"default":"outline",className:`min-w-[120px] ${e.isCompleted?"bg-green-600 text-white hover:bg-green-700":"text-gray-600 hover:bg-gray-50"}`,onClick:e=>{e.stopPropagation(),t()},children:[(0,a.jsx)(P.A,{className:"mr-2 h-4 w-4"}),e.isCompleted?"Completed":"Mark Complete"]})})]})]})})})};var J=s(51897),X=s(86857);let Y=({quiz:e,isUnlocked:t,onStartQuiz:s})=>(0,a.jsx)(T.Zp,{className:`my-3 ml-6 border-2 ${!t?"opacity-50":""}`,"data-sentry-element":"Card","data-sentry-component":"QuizCard","data-sentry-source-file":"quiz-card.tsx",children:(0,a.jsx)(T.Wu,{className:"p-4","data-sentry-element":"CardContent","data-sentry-source-file":"quiz-card.tsx",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:`rounded-lg p-2 ${(()=>{switch(e.type){case"chapter":return"bg-blue-100 text-blue-700 border-blue-200";case"module":return"bg-purple-100 text-purple-700 border-purple-200";case"final":return"bg-red-100 text-red-700 border-red-200";default:return"bg-gray-100 text-gray-700 border-gray-200"}})()}`,children:(()=>{switch(e.type){case"chapter":default:return(0,a.jsx)(_.A,{className:"h-4 w-4"});case"module":return(0,a.jsx)(L,{className:"h-4 w-4"});case"final":return(0,a.jsx)(R,{className:"h-4 w-4"})}})()}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium",children:e.title}),(0,a.jsxs)("div",{className:"mt-1 flex items-center space-x-3",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["Min. Score: ",e.minimumScore,"%"]}),e.timeLimit&&(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:[(0,a.jsx)(J.A,{className:"mr-1 inline h-3 w-3"}),e.timeLimit," min"]}),(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["Attempts: ",e.attempts,"/",e.maxAttempts]})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[e.isPassed&&(0,a.jsxs)(c.E,{className:"bg-green-100 text-green-700",children:[(0,a.jsx)(X.A,{className:"mr-1 h-3 w-3"}),"Passed (",e.lastScore,"%)"]}),(0,a.jsx)(n.$,{size:"sm",disabled:!t||e.attempts>=e.maxAttempts,onClick:s,variant:e.isPassed?"outline":"default","data-sentry-element":"Button","data-sentry-source-file":"quiz-card.tsx",children:0===e.attempts?"Start Quiz":e.isPassed?"Retake":"Continue"})]})]})})}),ee=({quiz:e,isOpen:t,onClose:s,onComplete:d})=>{let[m,u]=(0,r.useState)(0),[x,p]=(0,r.useState)({}),[h,f]=(0,r.useState)(e.timeLimit?60*e.timeLimit:null),[g,y]=(0,r.useState)(!1);i().useEffect(()=>{if(t&&null!==h&&h>0){let e=setInterval(()=>{f(e=>null===e||e<=1?(v(),0):e-1)},1e3);return()=>clearInterval(e)}},[t,h]);let b=(e,t)=>{p(s=>({...s,[e]:t}))},v=()=>{if(g)return;y(!0);let t=0;e.questions.forEach(e=>{let s=x[e.id];if("multiple_choice"===e.type||"multiple-choice"===e.type)if(s===e.correctAnswer)t++;else{let a=e.options?.[s];a&&"object"==typeof a&&a.isCorrect&&t++}else"true_false"===e.type||"true-false"===e.type?s===e.correctAnswer&&t++:"essay"===e.type&&s&&""!==s.trim()&&t++});let s=Math.round(t/e.questions.length*100);setTimeout(()=>{d(s),y(!1),p({}),u(0),e.timeLimit&&f(60*e.timeLimit)},1e3)};if(!t||0===e.questions.length)return null;let j=e.questions[m],N=m===e.questions.length-1,k=void 0!==x[j.id];return(0,a.jsx)(o.lG,{open:t,onOpenChange:s,"data-sentry-element":"Dialog","data-sentry-component":"QuizModal","data-sentry-source-file":"quiz-modal.tsx",children:(0,a.jsxs)(o.Cf,{className:"max-h-[90vh] max-w-4xl overflow-y-auto p-6","data-sentry-element":"DialogContent","data-sentry-source-file":"quiz-modal.tsx",children:[(0,a.jsx)(o.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"quiz-modal.tsx",children:(0,a.jsxs)(o.L3,{className:"flex items-center justify-between","data-sentry-element":"DialogTitle","data-sentry-source-file":"quiz-modal.tsx",children:[(0,a.jsx)("span",{children:e.title}),null!==h&&(0,a.jsxs)(c.E,{variant:"outline",className:"border-red-200 text-red-600",children:[(0,a.jsx)(J.A,{className:"mr-1 h-4 w-4"}),(e=>{let t=Math.floor(e/60);return`${t}:${(e%60).toString().padStart(2,"0")}`})(h)]})]})}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm text-gray-600",children:[(0,a.jsxs)("span",{children:["Question ",m+1," of ",e.questions.length]}),(0,a.jsxs)("span",{children:[Math.round((m+1)/e.questions.length*100),"% Complete"]})]}),(0,a.jsx)(l.k,{value:(m+1)/e.questions.length*100,"data-sentry-element":"Progress","data-sentry-source-file":"quiz-modal.tsx"})]}),(0,a.jsx)(T.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"quiz-modal.tsx",children:(0,a.jsxs)(T.Wu,{className:"p-6","data-sentry-element":"CardContent","data-sentry-source-file":"quiz-modal.tsx",children:[(0,a.jsx)("h3",{className:"mb-4 text-lg font-medium",children:"string"==typeof j.question?j.question:Array.isArray(j.question)?j.question.map((e,t)=>(0,a.jsxs)(i().Fragment,{children:["text"===e.type&&(0,a.jsx)("span",{children:e.value}),"image"===e.type&&e.value&&(0,a.jsx)("img",{src:e.value,alt:`Question image ${t}`,className:"inline-block max-h-16 object-contain ml-2"})]},t)):(0,a.jsx)("span",{children:String(j.question)})}),("multiple_choice"===j.type||"multiple-choice"===j.type)&&j.options&&(0,a.jsx)("div",{className:"space-y-3",children:j.options.map((e,t)=>(0,a.jsxs)("label",{className:"flex cursor-pointer items-center space-x-3",children:[(0,a.jsx)("input",{type:"radio",name:j.id,value:t,checked:x[j.id]===t,onChange:()=>b(j.id,t),className:"h-4 w-4 text-blue-600"}),(0,a.jsx)("span",{children:"string"==typeof e?e:Array.isArray(e.content)?e.content.map((e,t)=>(0,a.jsxs)(i().Fragment,{children:["text"===e.type&&(0,a.jsx)("span",{children:e.value}),"image"===e.type&&e.value&&(0,a.jsx)("img",{src:e.value,alt:`Option image ${t}`,className:"inline-block max-h-8 object-contain ml-1"})]},t)):(0,a.jsx)("span",{children:String(e.content||e)})})]},t))}),("true_false"===j.type||"true-false"===j.type)&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("label",{className:"flex cursor-pointer items-center space-x-3",children:[(0,a.jsx)("input",{type:"radio",name:j.id,value:"true",checked:"true"===x[j.id],onChange:()=>b(j.id,"true"),className:"h-4 w-4 text-blue-600"}),(0,a.jsx)("span",{children:"True"})]}),(0,a.jsxs)("label",{className:"flex cursor-pointer items-center space-x-3",children:[(0,a.jsx)("input",{type:"radio",name:j.id,value:"false",checked:"false"===x[j.id],onChange:()=>b(j.id,"false"),className:"h-4 w-4 text-blue-600"}),(0,a.jsx)("span",{children:"False"})]})]}),"essay"===j.type&&(0,a.jsx)("textarea",{className:"w-full resize-none rounded-lg border p-3 focus:border-transparent focus:ring-2 focus:ring-blue-500",rows:6,placeholder:"Type your answer here...",value:x[j.id]||"",onChange:e=>b(j.id,e.target.value)})]})}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)(n.$,{variant:"outline",onClick:()=>u(e=>Math.max(0,e-1)),disabled:0===m||g,"data-sentry-element":"Button","data-sentry-source-file":"quiz-modal.tsx",children:"Previous"}),(0,a.jsx)("div",{className:"flex space-x-2",children:N?(0,a.jsx)(n.$,{onClick:v,disabled:!k||g,className:"bg-green-600 hover:bg-green-700",children:g?"Submitting...":"Submit Quiz"}):(0,a.jsx)(n.$,{onClick:()=>u(e=>e+1),disabled:!k||g,children:"Next"})})]})]})]})})},et=({chapter:e,expandedContents:t,onToggleContent:s,onToggleContentComplete:r,onStartQuiz:i,isExpanded:n,onToggleExpanded:o})=>{let d=e.contents.filter(e=>e.isCompleted).length,c=e.contents.length;return(0,a.jsx)(T.Zp,{id:`chapter-${e.id}`,className:`my-3 border-l-4 scroll-mt-20 ${e.isUnlocked?"border-l-green-400":"border-l-gray-300"} ${!e.isUnlocked?"opacity-60":""}`,"data-sentry-element":"Card","data-sentry-component":"ChapterSection","data-sentry-source-file":"chapter-section.tsx",children:(0,a.jsx)(T.Wu,{className:"p-4","data-sentry-element":"CardContent","data-sentry-source-file":"chapter-section.tsx",children:(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsxs)("div",{className:"flex cursor-pointer items-center justify-between",onClick:()=>e.isUnlocked&&o(),children:[(0,a.jsxs)("div",{className:"flex flex-1 items-center space-x-3",children:[(0,a.jsx)("div",{className:`rounded-lg p-2 ${e.isUnlocked?"bg-blue-100 text-blue-700":"bg-gray-100 text-gray-500"}`,children:(0,a.jsx)($.A,{className:"h-5 w-5","data-sentry-element":"Book","data-sentry-source-file":"chapter-section.tsx"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"font-medium",children:e.title}),!e.isUnlocked&&(0,a.jsx)(z.A,{className:"h-4 w-4 text-gray-400"})]}),(0,a.jsx)("div",{className:"mt-2 max-w-md",children:(0,a.jsx)(l.k,{value:c>0?d/c*100:0,className:"h-2","data-sentry-element":"Progress","data-sentry-source-file":"chapter-section.tsx"})}),(0,a.jsxs)("p",{className:"mt-1 text-sm text-gray-500",children:[d,"/",c," contents completed",e.quiz.isPassed&&" • Quiz passed"]})]})]}),e.isUnlocked&&(n?(0,a.jsx)(F.A,{className:"h-5 w-5 text-gray-400"}):(0,a.jsx)(C.A,{className:"h-5 w-5 text-gray-400"}))]}),n&&e.isUnlocked&&(0,a.jsxs)("div",{className:"mt-4 border-t pt-4",children:[(0,a.jsx)("div",{className:"space-y-2",children:e.contents.map(e=>(0,a.jsx)(W,{content:e,onToggleComplete:()=>r(e.id),isExpanded:t[e.id]||!1,onToggleExpand:()=>s(e.id)},e.id))}),(0,a.jsx)(Y,{quiz:e.quiz,isUnlocked:d===c,onStartQuiz:()=>i(e.quiz.id)})]})]})})})},es=({module:e,expandedContents:t,expandedChapters:s,onToggleContent:r,onToggleContentComplete:i,onStartQuiz:n,isExpanded:o,onToggleExpanded:d,onToggleChapter:c,onExpandAllChapters:m,onCollapseAllChapters:u})=>{let x=e.chapters.length,p=e.chapters.filter(e=>e.contents.every(e=>e.isCompleted)&&(e.quiz.isPassed||e.quiz.attempts>=e.quiz.maxAttempts)).length,h=e.chapters.filter(e=>e.isUnlocked);return h.length>0&&h.every(e=>!0===s[e.id]),(0,a.jsxs)(T.Zp,{id:`module-${e.id}`,className:`mb-6 shadow-md scroll-mt-20 ${e.isUnlocked?"border-green-200":"border-gray-200"}`,"data-sentry-element":"Card","data-sentry-component":"ModuleSection","data-sentry-source-file":"module-section.tsx",children:[(0,a.jsx)(T.aR,{className:`border-b ${!e.isUnlocked?"opacity-60":""}`,"data-sentry-element":"CardHeader","data-sentry-source-file":"module-section.tsx",children:(0,a.jsxs)("div",{className:"flex cursor-pointer items-center justify-between",onClick:()=>e.isUnlocked&&d(),children:[(0,a.jsxs)("div",{className:"flex flex-1 items-center space-x-4",children:[(0,a.jsx)("div",{className:`rounded-lg p-3 ${e.isUnlocked?"bg-purple-100 text-purple-700":"bg-gray-100 text-gray-500"}`,children:(0,a.jsx)(E.A,{className:"h-6 w-6","data-sentry-element":"BookOpen","data-sentry-source-file":"module-section.tsx"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(T.ZB,{className:"text-lg","data-sentry-element":"CardTitle","data-sentry-source-file":"module-section.tsx",children:e.title}),!e.isUnlocked&&(0,a.jsx)(z.A,{className:"h-4 w-4 text-gray-400"})]}),(0,a.jsx)("p",{className:"mt-1 text-gray-600",children:e.description}),(0,a.jsx)("div",{className:"mt-3 max-w-md",children:(0,a.jsx)(l.k,{value:x>0?p/x*100:0,className:"h-3","data-sentry-element":"Progress","data-sentry-source-file":"module-section.tsx"})}),(0,a.jsxs)("p",{className:"mt-2 text-sm text-gray-500",children:[p,"/",x," chapters completed",e.moduleQuiz.isPassed&&" • Module quiz passed"]})]})]}),e.isUnlocked&&(o?(0,a.jsx)(F.A,{className:"h-6 w-6 text-gray-400"}):(0,a.jsx)(C.A,{className:"h-6 w-6 text-gray-400"}))]})}),o&&e.isUnlocked&&(0,a.jsxs)(T.Wu,{className:"pt-6",children:[(0,a.jsx)("div",{className:"space-y-2",children:e.chapters.map(e=>(0,a.jsx)(et,{chapter:e,expandedContents:t,onToggleContent:r,onToggleContentComplete:i,onStartQuiz:n,isExpanded:s[e.id]||!1,onToggleExpanded:()=>c(e.id)},e.id))}),(0,a.jsxs)("div",{className:"mt-6 border-t pt-4",children:[(0,a.jsx)("h4",{className:"mb-3 font-medium text-purple-700",children:"Module Assessment"}),(0,a.jsx)(Y,{quiz:e.moduleQuiz,isUnlocked:p===x,onStartQuiz:()=>n(e.moduleQuiz.id)})]})]})]})};s(15854);var ea=s(31619),er=s(69622),ei=s(37980);let en=({courseData:e,expandedModules:t,expandedChapters:s,expandedContents:i,onToggleModule:l,onToggleChapter:o,onToggleContent:d,onToggleContentComplete:c,onStartQuiz:m,onNavigateToSection:u,onExpandAllModules:x,onCollapseAllModules:p,onExpandAllChaptersInModule:h,onCollapseAllChaptersInModule:f})=>{let[g,y]=(0,r.useState)(!0),[b,v]=(0,r.useState)(0);e.modules.filter(e=>e.isUnlocked).every(e=>t[e.id]);let j=e.modules[b],N=b>0,k=b<e.modules.length-1,w=t=>{let s=e.modules.findIndex(e=>e.id===t);-1!==s&&v(s)};return(0,a.jsx)("div",{className:"h-[calc(100vh-210px)] overflow-hidden","data-sentry-component":"CourseTab","data-sentry-source-file":"course-tab.tsx",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-4 min-w-0 h-full",children:[(0,a.jsx)("div",{className:"lg:col-span-1 min-w-0 overflow-y-auto",children:(0,a.jsx)("div",{className:"w-full",children:(0,a.jsx)(B,{course:e,onNavigate:(e,t,s)=>{w(e),u(e,t,s)},expandedModules:t,expandedChapters:s,onToggleModule:l,onToggleChapter:o,currentModuleIndex:b,"data-sentry-element":"TableOfContents","data-sentry-source-file":"course-tab.tsx"})})}),(0,a.jsxs)("div",{className:"space-y-4 lg:col-span-3 min-w-0 overflow-y-auto",children:[g&&(0,a.jsxs)("div",{className:"rounded-lg border border-blue-200 bg-blue-50 p-4 relative",children:[(0,a.jsx)("button",{onClick:()=>y(!1),className:"absolute top-3 right-3 p-1 rounded-full hover:bg-blue-100 transition-colors","aria-label":"Tutup struktur kursus",children:(0,a.jsx)(ea.A,{className:"h-4 w-4 text-blue-600"})}),(0,a.jsx)("h3",{className:"mb-2 font-semibold text-blue-900 pr-8",children:"Struktur Kursus"}),(0,a.jsx)("p",{className:"mb-3 text-sm text-blue-800",children:"Selesaikan semua modul secara berurutan. Setiap bab harus diselesaikan sebelum mengakses kuis. Kuis modul akan terbuka setelah menyelesaikan semua kuis bab."}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4 text-sm md:grid-cols-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(L,{className:"h-4 w-4 text-blue-600"}),(0,a.jsx)("span",{children:"Jalur Pembelajaran Berurutan"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(_.A,{className:"h-4 w-4 text-blue-600"}),(0,a.jsx)("span",{children:"Kuis Interaktif"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(er.A,{className:"h-4 w-4 text-blue-600"}),(0,a.jsx)("span",{children:"Sertifikasi Profesional"})]})]})]}),(0,a.jsx)("div",{className:"space-y-6",children:j&&(0,a.jsx)("div",{"data-module-id":j.id,children:(0,a.jsx)(es,{module:j,expandedContents:i,expandedChapters:s,onToggleContent:d,onToggleContentComplete:c,onStartQuiz:m,isExpanded:t[j.id]||!1,onToggleExpanded:()=>l(j.id),onToggleChapter:o,onExpandAllChapters:()=>h(j.id),onCollapseAllChapters:()=>f(j.id)})},j.id)}),(0,a.jsxs)("div",{className:"mb-6 flex items-center justify-between rounded-lg border bg-white p-4",children:[(0,a.jsxs)(n.$,{variant:"outline",onClick:()=>{N&&v(b-1)},disabled:!N,className:"flex items-center space-x-2","data-sentry-element":"Button","data-sentry-source-file":"course-tab.tsx",children:[(0,a.jsx)(ei.A,{className:"h-4 w-4","data-sentry-element":"ChevronLeft","data-sentry-source-file":"course-tab.tsx"}),(0,a.jsx)("span",{children:"Modul Sebelumnya"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold",children:["Modul ",b+1," dari ",e.modules.length]}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:j?.title})]}),(0,a.jsxs)(n.$,{variant:"outline",onClick:()=>{k&&v(b+1)},disabled:!k,className:"flex items-center space-x-2","data-sentry-element":"Button","data-sentry-source-file":"course-tab.tsx",children:[(0,a.jsx)("span",{children:"Modul Selanjutnya"}),(0,a.jsx)(A.A,{className:"h-4 w-4","data-sentry-element":"ChevronRight","data-sentry-source-file":"course-tab.tsx"})]})]})]})]})})};var el=s(84386);let eo=({courseData:e,overallProgress:t})=>(0,a.jsxs)("div",{className:"grid gap-6","data-sentry-component":"ProgressTab","data-sentry-source-file":"progress-tab.tsx",children:[(0,a.jsxs)(T.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"progress-tab.tsx",children:[(0,a.jsx)(T.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"progress-tab.tsx",children:(0,a.jsxs)(T.ZB,{className:"flex items-center space-x-2","data-sentry-element":"CardTitle","data-sentry-source-file":"progress-tab.tsx",children:[(0,a.jsx)(el.A,{className:"h-5 w-5","data-sentry-element":"BarChart3","data-sentry-source-file":"progress-tab.tsx"}),(0,a.jsx)("span",{children:"Ringkasan Kemajuan Belajar"})]})}),(0,a.jsx)(T.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"progress-tab.tsx",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-3",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"mb-2 text-3xl font-bold text-blue-600",children:[Math.round(t),"%"]}),(0,a.jsx)("p",{className:"text-gray-600",children:"Kemajuan Keseluruhan"}),(0,a.jsx)(l.k,{value:t,className:"mt-2","data-sentry-element":"Progress","data-sentry-source-file":"progress-tab.tsx"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"mb-2 text-3xl font-bold text-green-600",children:e.modules.filter(e=>e.moduleQuiz.isPassed).length}),(0,a.jsx)("p",{className:"text-gray-600",children:"Modul Selesai"}),(0,a.jsx)(l.k,{value:e.modules.filter(e=>e.moduleQuiz.isPassed).length/e.modules.length*100,className:"mt-2","data-sentry-element":"Progress","data-sentry-source-file":"progress-tab.tsx"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"mb-2 text-3xl font-bold text-purple-600",children:"0"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Jam Belajar"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Pelacakan waktu segera tersedia"})]})]})})]}),(0,a.jsxs)(T.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"progress-tab.tsx",children:[(0,a.jsx)(T.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"progress-tab.tsx",children:(0,a.jsx)(T.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"progress-tab.tsx",children:"Detail Kemajuan Modul"})}),(0,a.jsx)(T.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"progress-tab.tsx",children:(0,a.jsx)("div",{className:"space-y-4",children:e.modules.map(e=>{let t=e.chapters.filter(e=>e.contents.every(e=>e.isCompleted)&&e.quiz.isPassed).length,s=t/e.chapters.length*100;return(0,a.jsxs)("div",{className:"rounded-lg border p-4",children:[(0,a.jsxs)("div",{className:"mb-3 flex items-center justify-between",children:[(0,a.jsx)("h4",{className:"font-medium",children:e.title}),(0,a.jsx)(c.E,{variant:e.moduleQuiz.isPassed?"default":"outline",children:e.moduleQuiz.isPassed?"Selesai":"Sedang Belajar"})]}),(0,a.jsx)(l.k,{value:s,className:"mb-2"}),(0,a.jsxs)("div",{className:"flex justify-between text-sm text-gray-600",children:[(0,a.jsxs)("span",{children:[t,"/",e.chapters.length," bab selesai"]}),(0,a.jsxs)("span",{children:[Math.round(s),"%"]})]})]},e.id)})})})]}),(0,a.jsxs)(T.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"progress-tab.tsx",children:[(0,a.jsx)(T.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"progress-tab.tsx",children:(0,a.jsx)(T.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"progress-tab.tsx",children:"Performa Kuis"})}),(0,a.jsx)(T.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"progress-tab.tsx",children:(()=>{let t=[];return(e.modules.forEach(e=>{e.chapters.forEach(s=>{s.quiz.attempts>0&&t.push({...s.quiz,moduleName:e.title,chapterName:s.title,type:"chapter"})}),e.moduleQuiz.attempts>0&&t.push({...e.moduleQuiz,moduleName:e.title,chapterName:null,type:"module"})}),e.finalExam.attempts>0&&t.push({...e.finalExam,moduleName:"Final Assessment",chapterName:null,type:"final"}),0===t.length)?(0,a.jsxs)("div",{className:"py-8 text-center text-gray-500",children:[(0,a.jsx)(_.A,{className:"mx-auto mb-4 h-12 w-12 text-gray-400"}),(0,a.jsx)("p",{children:"Hasil kuis akan muncul di sini saat Anda menyelesaikan penilaian"})]}):(0,a.jsx)("div",{className:"space-y-4",children:t.map(e=>(0,a.jsxs)("div",{className:"rounded-lg border p-4",children:[(0,a.jsxs)("div",{className:"mb-2 flex items-start justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium",children:e.title}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[e.moduleName,e.chapterName?` • ${e.chapterName}`:""]})]}),(0,a.jsx)(c.E,{variant:e.isPassed?"default":"destructive",className:e.isPassed?"bg-green-600 hover:bg-green-700 text-white":"",children:e.isPassed?"Lulus":"Tidak Lulus"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm md:grid-cols-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"Skor:"}),(0,a.jsx)("div",{className:"font-medium",children:void 0!==e.lastScore?`${e.lastScore}%`:"N/A"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"Diperlukan:"}),(0,a.jsxs)("div",{className:"font-medium",children:[e.minimumScore,"%"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"Percobaan:"}),(0,a.jsxs)("div",{className:"font-medium",children:[e.attempts,"/",e.maxAttempts]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"Jenis:"}),(0,a.jsx)("div",{className:"font-medium capitalize",children:e.type})]})]}),void 0!==e.lastScore&&(0,a.jsx)("div",{className:"mt-3",children:(0,a.jsx)(l.k,{value:e.lastScore,className:`h-2 ${e.isPassed?"text-green-600":"text-red-600"}`})})]},e.id))})})()})]})]});var ed=s(94);let ec=({courseData:e,onStartQuiz:t})=>{let s=e.modules.every(e=>e.chapters.every(e=>e.contents.every(e=>e.isCompleted)&&e.quiz.isPassed)&&e.moduleQuiz.isPassed);return(0,a.jsxs)(T.Zp,{className:"shadow-sm","data-sentry-element":"Card","data-sentry-component":"ExamTab","data-sentry-source-file":"exam-tab.tsx",children:[(0,a.jsx)(T.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"exam-tab.tsx",children:(0,a.jsxs)(T.ZB,{className:"flex items-center space-x-2","data-sentry-element":"CardTitle","data-sentry-source-file":"exam-tab.tsx",children:[(0,a.jsx)(R,{className:"h-6 w-6","data-sentry-element":"Trophy","data-sentry-source-file":"exam-tab.tsx"}),(0,a.jsx)("span",{children:"Ujian Akhir Sertifikasi"})]})}),(0,a.jsx)(T.Wu,{className:"p-6","data-sentry-element":"CardContent","data-sentry-source-file":"exam-tab.tsx",children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"rounded-lg border border-amber-200 bg-amber-50 p-4",children:[(0,a.jsx)("h4",{className:"mb-2 font-semibold text-amber-800",children:"Persyaratan Ujian"}),(0,a.jsxs)("ul",{className:"space-y-1 text-sm text-amber-700",children:[(0,a.jsx)("li",{children:"• Selesaikan semua modul dan lulus semua kuis modul"}),(0,a.jsxs)("li",{children:["• Nilai minimum lulus: ",e.finalExam.minimumScore,"%"]}),(0,a.jsxs)("li",{children:["• Batas waktu: ",e.finalExam.timeLimit," menit"]}),(0,a.jsxs)("li",{children:["• Maksimal percobaan: ",e.finalExam.maxAttempts]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"Status Prasyarat"}),e.modules.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between rounded-lg bg-gray-50 p-3",children:[(0,a.jsx)("span",{className:"text-sm",children:e.title}),e.moduleQuiz.isPassed?(0,a.jsx)(X.A,{className:"h-5 w-5 text-green-600"}):(0,a.jsx)(ed.A,{className:"h-5 w-5 text-red-500"})]},e.id))]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"Informasi Ujian"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Percobaan Digunakan"}),(0,a.jsxs)("span",{className:"font-medium",children:[e.finalExam.attempts,"/",e.finalExam.maxAttempts]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Skor Terakhir"}),(0,a.jsx)("span",{className:"font-medium",children:e.finalExam.lastScore?`${e.finalExam.lastScore}%`:"N/A"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Status"}),(0,a.jsx)(c.E,{variant:e.finalExam.isPassed?"default":"outline","data-sentry-element":"Badge","data-sentry-source-file":"exam-tab.tsx",children:e.finalExam.isPassed?"Lulus":"Belum Diambil"})]})]})]})]}),(0,a.jsxs)("div",{className:"border-t pt-4 text-center",children:[(0,a.jsxs)(n.$,{size:"lg",variant:"iai",disabled:e.finalExam.attempts>=e.finalExam.maxAttempts,onClick:()=>t(e.finalExam.id),"data-sentry-element":"Button","data-sentry-source-file":"exam-tab.tsx",children:[(0,a.jsx)(R,{className:"mr-2 h-5 w-5","data-sentry-element":"Trophy","data-sentry-source-file":"exam-tab.tsx"}),0===e.finalExam.attempts?"Mulai Ujian Akhir":"Ulangi Ujian Akhir"]}),!s&&(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"Selesaikan semua modul untuk membuka ujian akhir"})]})]})})]})},em=({courseData:e,institution:t,overallProgress:s,onGenerateCertificate:r,onShowCertificate:i,onDownloadPDF:l})=>{let o=async()=>{let s=w.qs.getUser(),a={studentName:s?.name||"John Doe",courseName:e.name,courseCode:e.code,completionDate:e.certificate.completionDate||new Date().toISOString().split("T")[0],finalScore:e.finalExam.lastScore||0,instructorName:e.instructor,institutionName:t.name,certificateId:(0,k.K2)()};await (0,k.Ct)(a)};return(0,a.jsxs)(T.Zp,{"data-sentry-element":"Card","data-sentry-component":"CertificateTab","data-sentry-source-file":"certificate-tab.tsx",children:[(0,a.jsx)(T.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"certificate-tab.tsx",children:(0,a.jsxs)(T.ZB,{className:"flex items-center space-x-2",style:{color:t.certificateTemplate?.primaryColor},"data-sentry-element":"CardTitle","data-sentry-source-file":"certificate-tab.tsx",children:[(0,a.jsx)(er.A,{className:"h-6 w-6","data-sentry-element":"Award","data-sentry-source-file":"certificate-tab.tsx"}),(0,a.jsx)("span",{children:"Sertifikasi Profesional"})]})}),(0,a.jsx)(T.Wu,{className:"p-6","data-sentry-element":"CardContent","data-sentry-source-file":"certificate-tab.tsx",children:0===e.finalExam.attempts?(0,a.jsx)("div",{className:"space-y-6 text-center",children:(0,a.jsxs)("div",{className:"rounded-lg border-2 border-yellow-200 bg-gradient-to-r from-yellow-50 to-orange-50 p-8",children:[(0,a.jsx)(z.A,{className:"mx-auto mb-4 h-16 w-16 text-yellow-600"}),(0,a.jsx)("h3",{className:"mb-2 text-2xl font-bold text-yellow-800",children:"Belum Mengikuti Final Exam"}),(0,a.jsx)("p",{className:"text-yellow-700",children:"Kamu belum mengikuti final exam. Selesaikan final exam terlebih dahulu untuk mendapatkan sertifikat."})]})}):e.certificate.isEligible&&e.certificate.isGenerated||e.certificate.isEligible?(0,a.jsxs)("div",{className:"space-y-6 text-center",children:[(0,a.jsxs)("div",{className:"rounded-lg border-2 border-green-200 bg-gradient-to-r from-green-50 to-blue-50 p-8",children:[(0,a.jsx)(er.A,{className:"mx-auto mb-4 h-16 w-16 text-green-600"}),(0,a.jsx)("h3",{className:"mb-2 text-2xl font-bold text-green-800",children:"Selamat!"}),(0,a.jsxs)("p",{className:"text-green-700",children:["Anda telah berhasil menyelesaikan kursus ",e.name," dan memperoleh sertifikasi."]}),e.certificate.completionDate&&(0,a.jsxs)("p",{className:"mt-2 text-sm text-green-600",children:["Diselesaikan pada:"," ",new Date(e.certificate.completionDate).toLocaleDateString()]})]}),(0,a.jsxs)("div",{className:"flex justify-center space-x-4",children:[(0,a.jsxs)(n.$,{onClick:i,className:"bg-green-600 hover:bg-green-700",children:[(0,a.jsx)(Z.A,{className:"mr-2 h-4 w-4"}),"Lihat Sertifikat"]}),(0,a.jsxs)(n.$,{variant:"outline",className:"border-green-600 text-green-600 hover:bg-green-50",onClick:o,children:[(0,a.jsx)(G.A,{className:"mr-2 h-4 w-4"}),"Unduh PDF"]})]})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"rounded-lg border-2 border-gray-200 bg-gray-50 p-8 text-center",children:[(0,a.jsx)(z.A,{className:"mx-auto mb-4 h-16 w-16 text-gray-400"}),(0,a.jsx)("h3",{className:"mb-2 text-2xl font-bold text-gray-700",children:"Persyaratan Sertifikat"}),(0,a.jsx)("p",{className:"mb-4 text-gray-600",children:"Selesaikan semua persyaratan kursus untuk memperoleh sertifikasi profesional Anda."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"Penyelesaian Modul"}),e.modules.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between rounded-lg bg-gray-50 p-3",children:[(0,a.jsx)("span",{className:"text-sm",children:e.title}),e.moduleQuiz.isPassed?(0,a.jsx)(X.A,{className:"h-5 w-5 text-green-600"}):(0,a.jsx)(ed.A,{className:"h-5 w-5 text-gray-400"})]},e.id))]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"Persyaratan Akhir"}),(0,a.jsxs)("div",{className:"flex items-center justify-between rounded-lg bg-gray-50 p-3",children:[(0,a.jsxs)("span",{className:"text-sm",children:["Final Exam (Min. ",e.finalExam.minimumScore,"%)"]}),e.finalExam.isPassed?(0,a.jsx)(X.A,{className:"h-5 w-5 text-green-600"}):(0,a.jsx)(ed.A,{className:"h-5 w-5 text-gray-400"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between rounded-lg bg-gray-50 p-3",children:[(0,a.jsx)("span",{className:"text-sm",children:"Skor Keseluruhan (Min. 70%)"}),s>=70?(0,a.jsx)(X.A,{className:"h-5 w-5 text-green-600"}):(0,a.jsx)(ed.A,{className:"h-5 w-5 text-gray-400"})]})]})]})]})})]})};s(93824);let eu=()=>{let e=(0,j.useParams)(),t=(0,j.useRouter)(),s=(0,j.useSearchParams)(),i=e.courseId,{courseData:u,updateCourseProgress:b,getCourseById:C,isEnrolledInCourse:A}=(0,N.q)(),z=C(i)||u,[P,q]=(0,r.useState)({}),[S,T]=(0,r.useState)({}),[D,M]=(0,r.useState)({}),[E,$]=(0,r.useState)(null),[I,U]=(0,r.useState)(!1),[L,_]=(0,r.useState)("course");(0,r.useEffect)(()=>{let e=s.get("tab");e&&_(e)},[s]);let R=(0,r.useCallback)(e=>{q(t=>({...t,[e]:!t[e]}))},[]),B=(0,r.useCallback)(e=>{T(t=>({...t,[e]:!t[e]}))},[]),K=(0,r.useCallback)(e=>{M(t=>({...t,[e]:!t[e]}))},[]),Q=(0,r.useCallback)(()=>{let e={};z.modules.forEach(t=>{t.isUnlocked&&(e[t.id]=!0)}),T(e)},[z.modules]),H=(0,r.useCallback)(()=>{T({})},[]),O=(0,r.useCallback)(e=>{let t=z.modules.find(t=>t.id===e);if(!t)return;let s={...D};t.chapters.forEach(e=>{e.isUnlocked&&(s[e.id]=!0)}),M(s)},[z.modules,D]),F=(0,r.useCallback)(e=>{let t=z.modules.find(t=>t.id===e);if(!t)return;T(t=>({...t,[e]:!1}));let s={...D};t.chapters.forEach(e=>{delete s[e.id]}),M(s)},[z.modules,D]),G=(0,r.useCallback)(e=>{let t=JSON.parse(JSON.stringify(z)),s=!1;for(let a of t.modules){for(let r of a.chapters){let i=r.contents.find(t=>t.id===e);if(i){i.isCompleted=!i.isCompleted,s=!0;let e=r.contents.filter(e=>e.isCompleted).length,n=r.order,l=a.chapters.find(e=>e.order===n+1);if(l&&e===r.contents.length&&r.quiz.isPassed&&(l.isUnlocked=!0),a.chapters.every(e=>e.contents.every(e=>e.isCompleted)&&e.quiz.isPassed)&&a.moduleQuiz.isPassed){let e=a.order,s=t.modules.find(t=>t.order===e+1);s&&(s.isUnlocked=!0,s.chapters.length>0&&(s.chapters[0].isUnlocked=!0))}break}}if(s)break}b(t)},[z,b]),Z=(0,r.useCallback)(e=>{let s;if(z.finalExam.id===e)return void t.push(`/my-courses/${i}/exam?type=final&examId=${e}`);for(let t of z.modules){for(let s of t.chapters)if(s.quiz.id===e)return void $({...s.quiz});if(t.moduleQuiz.id===e)return void $({...t.moduleQuiz})}},[z,i,t]),V=(0,r.useCallback)(e=>{if(!E)return;let t=JSON.parse(JSON.stringify(z)),s=t=>{t.attempts+=1,t.lastScore=e,t.isPassed=e>=t.minimumScore};for(let e of t.modules){for(let t of e.chapters)if(t.quiz.id===E.id){s(t.quiz);let a=t.contents.every(e=>e.isCompleted);if((t.quiz.isPassed||t.quiz.attempts>=t.quiz.maxAttempts)&&a){let s=e.chapters.find(e=>e.order===t.order+1);s&&(s.isUnlocked=!0)}break}if(e.moduleQuiz.id===E.id){s(e.moduleQuiz);let a=e.chapters.every(e=>e.contents.every(e=>e.isCompleted)&&(e.quiz.isPassed||e.quiz.attempts>=e.quiz.maxAttempts));if((e.moduleQuiz.isPassed||e.moduleQuiz.attempts>=e.moduleQuiz.maxAttempts)&&a){let s=t.modules.find(t=>t.order===e.order+1);s&&(s.isUnlocked=!0,s.chapters.length>0&&(s.chapters[0].isUnlocked=!0))}}}if(t.finalExam.id===E.id){s(t.finalExam);let e=t.modules.every(e=>e.chapters.every(e=>e.contents.every(e=>e.isCompleted)&&(e.quiz.isPassed||e.quiz.attempts>=e.quiz.maxAttempts))&&(e.moduleQuiz.isPassed||e.moduleQuiz.attempts>=e.moduleQuiz.maxAttempts));t.finalExam.isPassed&&e&&(t.certificate.isEligible=!0,t.certificate.completionDate=new Date().toISOString().split("T")[0],t.status="completed")}b(t),$(null)},[E,z,b]),W=(0,r.useCallback)(()=>{z.certificate.isEligible&&(b({...z,certificate:{...z.certificate,isGenerated:!0,certificateUrl:`#certificate-${z.id}`}}),U(!0))},[z,b]),J=(0,r.useCallback)((e,t,s)=>{_("course"),T(t=>({...t,[e]:!0})),t&&M(e=>({...e,[t]:!0})),s&&q(e=>({...e,[s]:!0})),setTimeout(()=>{let a;a=s?`content-${s}`:t?`chapter-${t}`:`module-${e}`;let r=document.getElementById(a);r&&(r.scrollIntoView({behavior:"smooth",block:"start",inline:"nearest"}),r.classList.add("ring-2","ring-blue-400","ring-opacity-75"),setTimeout(()=>{r.classList.remove("ring-2","ring-blue-400","ring-opacity-75")},2e3))},100)},[]),X=z.modules.reduce((e,t)=>e+t.chapters.filter(e=>e.contents.every(e=>e.isCompleted)&&e.quiz.isPassed).length,0),Y=z.modules.reduce((e,t)=>e+t.chapters.length,0),et=Y>0?X/Y*100:0;return(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 overflow-auto","data-sentry-component":"CoursePage","data-sentry-source-file":"page.tsx",children:(0,a.jsxs)("div",{className:"mx-auto max-w-full space-y-6 p-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(v(),{href:"/my-courses","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,a.jsxs)(n.$,{variant:"outline",size:"sm",className:"flex items-center space-x-2","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(m.A,{className:"h-4 w-4","data-sentry-element":"ArrowLeftIcon","data-sentry-source-file":"page.tsx"}),(0,a.jsx)("span",{children:"Kembali ke Kursus Saya"})]})}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(x,{className:"h-8 w-8 text-[var(--iai-primary)]","data-sentry-element":"BuildingIcon","data-sentry-source-file":"page.tsx"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:z.name}),(0,a.jsxs)("p",{className:"text-gray-600",children:["Kode Kursus: ",z.code]}),(0,a.jsxs)("p",{className:"text-gray-600",children:["Instruktur: ",z.instructor]}),(0,a.jsxs)("div",{className:"mt-2 flex items-center space-x-4",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:[(0,a.jsx)(p,{className:"mr-1 inline h-4 w-4","data-sentry-element":"CalendarIcon","data-sentry-source-file":"page.tsx"}),z.startDate," - ",z.endDate]}),(0,a.jsx)(c.E,{variant:"completed"===z.status?"default":"secondary","data-sentry-element":"Badge","data-sentry-source-file":"page.tsx",children:"completed"===z.status?"Selesai":"Sedang Belajar"})]})]})]})]}),(0,a.jsx)("div",{className:"flex items-center space-x-4",children:(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Kemajuan Keseluruhan"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(l.k,{value:et,className:"w-32","data-sentry-element":"Progress","data-sentry-source-file":"page.tsx"}),(0,a.jsxs)("span",{className:"text-sm font-medium",children:[Math.round(et),"%"]})]})]})})]}),(0,a.jsxs)(d.tU,{value:L,onValueChange:_,className:"mb-6","data-sentry-element":"Tabs","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)(d.j7,{className:"grid w-full grid-cols-4","data-sentry-element":"TabsList","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)(d.Xi,{value:"course",className:"flex items-center space-x-2","data-sentry-element":"TabsTrigger","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(h.A,{className:"h-4 w-4","data-sentry-element":"BookOpenIcon","data-sentry-source-file":"page.tsx"}),(0,a.jsx)("span",{children:"Konten Kursus"})]}),(0,a.jsxs)(d.Xi,{value:"progress",className:"flex items-center space-x-2","data-sentry-element":"TabsTrigger","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(f,{className:"h-4 w-4","data-sentry-element":"BarChartIcon","data-sentry-source-file":"page.tsx"}),(0,a.jsx)("span",{children:"Kemajuan"})]}),(0,a.jsxs)(d.Xi,{value:"exam",className:"flex items-center space-x-2","data-sentry-element":"TabsTrigger","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(g.A,{className:"h-4 w-4","data-sentry-element":"TrophyIcon","data-sentry-source-file":"page.tsx"}),(0,a.jsx)("span",{children:"Final Exam"})]}),(0,a.jsxs)(d.Xi,{value:"certificate",className:"flex items-center space-x-2","data-sentry-element":"TabsTrigger","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(g.A,{className:"h-4 w-4","data-sentry-element":"AwardIcon","data-sentry-source-file":"page.tsx"}),(0,a.jsx)("span",{children:"Sertifikat"})]})]}),(0,a.jsx)(d.av,{value:"course",className:"mt-4","data-sentry-element":"TabsContent","data-sentry-source-file":"page.tsx",children:(0,a.jsx)(en,{courseData:z,expandedModules:S,expandedChapters:D,expandedContents:P,onToggleModule:B,onToggleChapter:K,onToggleContent:R,onToggleContentComplete:G,onStartQuiz:Z,onNavigateToSection:J,onExpandAllModules:Q,onCollapseAllModules:H,onExpandAllChaptersInModule:O,onCollapseAllChaptersInModule:F,"data-sentry-element":"CourseTab","data-sentry-source-file":"page.tsx"})}),(0,a.jsx)(d.av,{value:"progress",className:"mt-4","data-sentry-element":"TabsContent","data-sentry-source-file":"page.tsx",children:(0,a.jsx)(eo,{courseData:z,overallProgress:et,"data-sentry-element":"ProgressTab","data-sentry-source-file":"page.tsx"})}),(0,a.jsx)(d.av,{value:"exam",className:"mt-4","data-sentry-element":"TabsContent","data-sentry-source-file":"page.tsx",children:(0,a.jsx)(ec,{courseData:z,onStartQuiz:Z,"data-sentry-element":"ExamTab","data-sentry-source-file":"page.tsx"})}),(0,a.jsx)(d.av,{value:"certificate",className:"mt-4","data-sentry-element":"TabsContent","data-sentry-source-file":"page.tsx",children:(0,a.jsx)(em,{courseData:z,institution:{id:"iai-indonesia",name:"Indonesian Institute of Architects",shortName:"IAI",website:"https://iai.or.id",certificateTemplate:{primaryColor:"#1e40af",secondaryColor:"#f59e0b",signatoryName:"Ar. Georgius Budi Yulianto, IAI, AA",signatoryTitle:"Ketua Umum IAI 2024-2027"}},overallProgress:et,onGenerateCertificate:W,onShowCertificate:()=>U(!0),"data-sentry-element":"CertificateTab","data-sentry-source-file":"page.tsx"})})]}),E&&(0,a.jsx)(ee,{quiz:E,isOpen:!0,onComplete:V,onClose:()=>$(null)}),(0,a.jsx)(o.lG,{open:I,onOpenChange:U,"data-sentry-element":"Dialog","data-sentry-source-file":"page.tsx",children:(0,a.jsx)(o.Cf,{className:"max-h-[90vh] max-w-7xl w-[95vw] p-0","data-sentry-element":"DialogContent","data-sentry-source-file":"page.tsx",children:(0,a.jsxs)("div",{className:"flex h-full max-h-[90vh] flex-col",children:[(0,a.jsx)(o.c7,{className:"flex-shrink-0 border-b px-6 py-4","data-sentry-element":"DialogHeader","data-sentry-source-file":"page.tsx",children:(0,a.jsxs)(o.L3,{className:"flex items-center space-x-2","data-sentry-element":"DialogTitle","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(g.A,{className:"h-5 w-5","data-sentry-element":"AwardIcon","data-sentry-source-file":"page.tsx"}),(0,a.jsx)("span",{children:"Sertifikat Anda"})]})}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto px-6 py-6",children:(0,a.jsx)("div",{className:"w-full h-full",dangerouslySetInnerHTML:{__html:(0,k.nE)({studentName:w.qs.getUser()?.name||"John Doe",courseName:z.name,courseCode:z.code,completionDate:z.certificate.completionDate||new Date().toISOString().split("T")[0],finalScore:z.finalExam.lastScore||0,instructorName:z.instructor,institutionName:"Indonesian Institute of Architects",certificateId:(0,k.K2)()})}})}),(0,a.jsx)("div",{className:"flex-shrink-0 border-t bg-white px-6 py-4",children:(0,a.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,a.jsx)(n.$,{variant:"outline",onClick:()=>U(!1),"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:"Tutup"}),(0,a.jsxs)(n.$,{onClick:async()=>{let e={studentName:w.qs.getUser()?.name||"John Doe",courseName:z.name,courseCode:z.code,completionDate:z.certificate.completionDate||new Date().toISOString().split("T")[0],finalScore:z.finalExam.lastScore||0,instructorName:z.instructor,institutionName:"Indonesian Institute of Architects",certificateId:(0,k.K2)()};await (0,k.Ct)(e)},"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(y,{className:"mr-2 h-4 w-4","data-sentry-element":"DownloadIcon","data-sentry-source-file":"page.tsx"}),"Unduh PDF"]})]})})]})})})]})})}},33093:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(55732).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},47028:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(55732).A)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},48161:e=>{"use strict";e.exports=require("node:os")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},54090:(e,t,s)=>{"use strict";s.d(t,{Xi:()=>d,av:()=>c,j7:()=>o,tU:()=>l});var a=s(91754),r=s(93491),i=s(36991),n=s(82233);let l=i.bL,o=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(i.B8,{ref:s,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));o.displayName=i.B8.displayName;let d=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(i.l9,{ref:s,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm cursor-pointer",e),...t}));d.displayName=i.l9.displayName;let c=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(i.UC,{ref:s,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));c.displayName=i.UC.displayName},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66064:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(15349).A)("BookOpen01Icon",[["path",{d:"M12 6L12 20",stroke:"currentColor",key:"k0"}],["path",{d:"M5.98056 3.28544C9.32175 3.9216 11.3131 5.25231 12 6.01628C12.6869 5.25231 14.6782 3.9216 18.0194 3.28544C19.7121 2.96315 20.5584 2.80201 21.2792 3.41964C22 4.03727 22 5.04022 22 7.04612V14.255C22 16.0891 22 17.0061 21.5374 17.5787C21.0748 18.1512 20.0564 18.3451 18.0194 18.733C16.2037 19.0787 14.7866 19.6295 13.7608 20.1831C12.7516 20.7277 12.247 21 12 21C11.753 21 11.2484 20.7277 10.2392 20.1831C9.21344 19.6295 7.79633 19.0787 5.98056 18.733C3.94365 18.3451 2.9252 18.1512 2.4626 17.5787C2 17.0061 2 16.0891 2 14.255V7.04612C2 5.04022 2 4.03727 2.72078 3.41964C3.44157 2.80201 4.2879 2.96315 5.98056 3.28544Z",stroke:"currentColor",key:"k1"}]])},69931:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(55732).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},73024:e=>{"use strict";e.exports=require("node:fs")},73136:e=>{"use strict";e.exports=require("node:url")},73566:e=>{"use strict";e.exports=require("worker_threads")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76328:(e,t,s)=>{"use strict";s.d(t,{WG:()=>r,cl:()=>n,qs:()=>a});let a={setUser:e=>{},getUser:()=>null,removeUser:()=>{},isAuthenticated:()=>null!==a.getUser(),hasRole:e=>{let t=a.getUser();return t?.role===e},isSuperAdmin:()=>a.hasRole("super_admin"),isTeacher:()=>a.hasRole("teacher"),isStudent:()=>a.hasRole("student")},r=e=>{switch(e.role){case"super_admin":return"/dashboard/admin";case"teacher":return"/dashboard/teacher";case"student":return"/courses";default:return"/dashboard"}},i=()=>{let e=a.getUser();return e||null},n=e=>{let t=i();return t&&t.role===e?t:null}},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79898:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(55732).A)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},80481:e=>{"use strict";e.exports=require("node:readline")},82763:(e,t,s)=>{"use strict";s.d(t,{Ct:()=>n,K2:()=>a,a7:()=>l,hr:()=>o,nE:()=>i});let a=()=>{let e=new Date().getFullYear(),t=Math.floor(1e4*Math.random()).toString().padStart(4,"0");return`CERT-${e}-${t}`},r=e=>`
  <!DOCTYPE html>
  <html lang="id">
  <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Sertifikat Kelulusan</title>
      <!-- Font -->
      <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Playfair+Display:wght@700&display=swap" rel="stylesheet">
      <style>
          :root{
              --primary:#4a90e2;
              --accent:#a370e8;
              --text:#2c3e50;
              --muted:#7f8c8d;
              --light:#f0f4f8;
              --white:#ffffff;
          }
          *{box-sizing:border-box;margin:0;padding:0}
          body{
              background:linear-gradient(135deg,var(--light),#e2e8f0);
              font-family:'Montserrat',sans-serif;
              display:flex;
              align-items:center;
              justify-content:center;
              min-height:100vh;
              padding:20px;
          }
          .certificate{
              width:100%;
              max-width:900px;
              background:var(--white);
              border-radius:16px;
              box-shadow:0 20px 40px rgba(0,0,0,.08);
              position:relative;
              overflow:hidden;
              padding:80px 80px 110px;
          }
          .certificate::before,
          .certificate::after{
              content:'';
              position:absolute;
              width:300px;
              height:300px;
              border-radius:50%;
              opacity:.05;
              z-index:0;
          }
          .certificate::before{top:-80px;left:-80px;background:radial-gradient(var(--primary),transparent 70%)}
          .certificate::after{bottom:-80px;right:-80px;background:radial-gradient(var(--accent),transparent 70%)}

          .watermark{
              position:absolute;
              top:50%;left:50%;
              transform:translate(-50%,-50%) rotate(-45deg);
              font-family:'Playfair Display',serif;
              font-size:150px;
              color:rgba(0,0,0,.03);
              font-weight:700;
              pointer-events:none;
              z-index:0;
          }

          .header{text-align:center;margin-bottom:50px}
          .title{
              font-family:'Playfair Display',serif;
              font-size:44px;
              color:var(--text);
              margin:0;
          }
          .subtitle{
              font-size:16px;
              color:var(--muted);
              margin-top:8px;
          }

          .main-content{
              text-align:center;
              margin-bottom:60px;
          }
          .awarded-to{
              font-size:16px;
              color:var(--muted);
              margin-bottom:8px;
          }
          .student-name{
              font-family:'Playfair Display',serif;
              font-size:42px;
              color:var(--text);
              position:relative;
              display:inline-block;
              margin-bottom:20px;
          }
          .student-name::after{
              content:'';
              position:absolute;
              left:50%;
              bottom:-6px;
              transform:translateX(-50%);
              width:80%;
              height:3px;
              background:linear-gradient(90deg,var(--primary),var(--accent));
              border-radius:2px;
          }
          .completion-text{
              font-size:18px;
              color:#555;
              line-height:1.6;
              max-width:600px;
              margin:0 auto 25px;
          }
          .course-details{
              display:inline-block;
              background:var(--light);
              border-radius:12px;
              padding:20px 35px;
              box-shadow:0 4px 15px rgba(0,0,0,.05);
              margin-bottom:25px;
          }
          .course-name{
              font-size:24px;
              font-weight:600;
              color:var(--text);
              margin:0;
          }
          .course-code{
              font-size:15px;
              color:var(--muted);
              margin-top:4px;
          }
          .score{
              font-size:20px;
              font-weight:700;
              color:var(--primary);
          }

          .footer{
              display:flex;
              justify-content:space-around;
              align-items:flex-end;
              border-top:1px solid #ecf0f1;
              padding-top:30px;
          }
          .signature-section{
              text-align:center;
              flex:1;
          }
          .signature-line{
              width:180px;
              height:1px;
              background:var(--muted);
              margin:0 auto 8px;
          }
          .signature-label{
              font-size:14px;
              color:var(--muted);
              line-height:1.4;
          }

          .id-date-row{
              margin-top:30px;
              display:flex;
              justify-content:space-between;
              font-size:13px;
              color:#95a5a6;
          }
      </style>
  </head>
  <body>
      <div class="certificate">
          <div class="watermark">TERANG</div>

          <!-- Konten utama -->
          <div class="header">
              <h1 class="title">Sertifikat Kelulusan</h1>
              <p class="subtitle">${e.institutionName}</p>
          </div>

          <div class="main-content">
              <p class="awarded-to">Dengan bangga mempersembahkan sertifikat ini kepada</p>
              <h2 class="student-name">${e.studentName}</h2>
              <p class="completion-text">
                  karena telah berhasil menyelesaikan dan lulus dari program
              </p>

              <div class="course-details">
                  <h3 class="course-name">${e.courseName}</h3>
                  <div class="course-code">Kode Kursus: ${e.courseCode}</div>
              </div>

              <p class="score">Nilai Akhir: ${e.finalScore}%</p>
          </div>

          <div class="footer">
              <div class="signature-section">
                  <div class="signature-line"></div>
                  <p class="signature-label">${e.instructorName}<br>Instruktur Kursus</p>
              </div>
              <div class="signature-section">
                  <div class="signature-line"></div>
                  <p class="signature-label">Tanggal Kelulusan<br>${e.completionDate}</p>
              </div>
          </div>

          <div class="id-date-row">
              <span>ID Sertifikat: ${e.certificateId}</span>
              <span>Diterbitkan pada: ${e.completionDate}</span>
          </div>
      </div>
  </body>
  </html>
  `,i=e=>`
  <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Playfair+Display:wght@700&display=swap" rel="stylesheet">
  <style>
      .certificate-modal-container {
          --primary:#4a90e2;
          --accent:#a370e8;
          --text:#2c3e50;
          --muted:#7f8c8d;
          --light:#f0f4f8;
          --white:#ffffff;
          font-family:'Montserrat',sans-serif;
          width: 100%;
          min-height: 600px;
          background: var(--white);
          position: relative;
      }
      .certificate-modal{
          width:100%;
          background:var(--white);
          border-radius:16px;
          box-shadow:0 20px 40px rgba(0,0,0,.08);
          position:relative;
          overflow:hidden;
          padding:60px 60px 80px;
          margin: 0;
      }
      .certificate-modal *{box-sizing:border-box;}
      
      .certificate-modal::before,
      .certificate-modal::after{
          content:'';
          position:absolute;
          width:250px;
          height:250px;
          border-radius:50%;
          opacity:.05;
          z-index:0;
      }
      .certificate-modal::before{top:-60px;left:-60px;background:radial-gradient(var(--primary),transparent 70%)}
      .certificate-modal::after{bottom:-60px;right:-60px;background:radial-gradient(var(--accent),transparent 70%)}

      .certificate-modal .watermark{
          position:absolute;
          top:50%;left:50%;
          transform:translate(-50%,-50%) rotate(-45deg);
          font-family:'Playfair Display',serif;
          font-size:100px;
          color:rgba(0,0,0,.03);
          font-weight:700;
          pointer-events:none;
          z-index:0;
      }

      .certificate-modal .header{text-align:center;margin-bottom:40px;position:relative;z-index:1;}
      .certificate-modal .title{
          font-family:'Playfair Display',serif;
          font-size:32px;
          color:var(--text);
          margin:0;
      }
      .certificate-modal .subtitle{
          font-size:14px;
          color:var(--muted);
          margin-top:8px;
      }

      .certificate-modal .main-content{
          text-align:center;
          margin-bottom:50px;
          position:relative;
          z-index:1;
      }
      .certificate-modal .awarded-to{
          font-size:14px;
          color:var(--muted);
          margin-bottom:8px;
      }
      .certificate-modal .student-name{
          font-family:'Playfair Display',serif;
          font-size:28px;
          color:var(--text);
          position:relative;
          display:inline-block;
          margin-bottom:20px;
      }
      .certificate-modal .student-name::after{
          content:'';
          position:absolute;
          left:50%;
          bottom:-6px;
          transform:translateX(-50%);
          width:80%;
          height:3px;
          background:linear-gradient(90deg,var(--primary),var(--accent));
          border-radius:2px;
      }
      .certificate-modal .completion-text{
          font-size:15px;
          color:#555;
          line-height:1.6;
          max-width:500px;
          margin:0 auto 20px;
      }
      .certificate-modal .course-details{
          display:inline-block;
          background:var(--light);
          border-radius:12px;
          padding:16px 30px;
          box-shadow:0 4px 15px rgba(0,0,0,.05);
          margin-bottom:20px;
      }
      .certificate-modal .course-name{
          font-size:18px;
          font-weight:600;
          color:var(--text);
          margin:0;
      }
      .certificate-modal .course-code{
          font-size:13px;
          color:var(--muted);
          margin-top:4px;
      }
      .certificate-modal .score{
          font-size:16px;
          font-weight:700;
          color:var(--primary);
      }

      .certificate-modal .footer{
          display:flex;
          justify-content:space-around;
          align-items:flex-end;
          border-top:1px solid #ecf0f1;
          padding-top:25px;
          position:relative;
          z-index:1;
      }
      .certificate-modal .signature-section{
          text-align:center;
          flex:1;
      }
      .certificate-modal .signature-line{
          width:140px;
          height:1px;
          background:var(--muted);
          margin:0 auto 8px;
      }
      .certificate-modal .signature-label{
          font-size:12px;
          color:var(--muted);
          line-height:1.4;
      }

      .certificate-modal .id-date-row{
          margin-top:25px;
          display:flex;
          justify-content:space-between;
          font-size:11px;
          color:#95a5a6;
          position:relative;
          z-index:1;
      }
  </style>
  
  <div class="certificate-modal-container">
      <div class="certificate-modal">
          <div class="watermark">TERANG</div>

          <div class="header">
              <h1 class="title">Sertifikat Kelulusan</h1>
              <p class="subtitle">${e.institutionName}</p>
          </div>

          <div class="main-content">
              <p class="awarded-to">Dengan bangga mempersembahkan sertifikat ini kepada</p>
              <h2 class="student-name">${e.studentName}</h2>
              <p class="completion-text">
                  karena telah berhasil menyelesaikan dan lulus dari program
              </p>

              <div class="course-details">
                  <h3 class="course-name">${e.courseName}</h3>
                  <div class="course-code">Kode Kursus: ${e.courseCode}</div>
              </div>

              <p class="score">Nilai Akhir: ${e.finalScore}%</p>
          </div>

          <div class="footer">
              <div class="signature-section">
                  <div class="signature-line"></div>
                  <p class="signature-label">${e.instructorName}<br>Instruktur Kursus</p>
              </div>
              <div class="signature-section">
                  <div class="signature-line"></div>
                  <p class="signature-label">Tanggal Kelulusan<br>${e.completionDate}</p>
              </div>
          </div>

          <div class="id-date-row">
              <span>ID Sertifikat: ${e.certificateId}</span>
              <span>Diterbitkan pada: ${e.completionDate}</span>
          </div>
      </div>
  </div>
  `,n=async e=>{try{let t=r(e),s=await fetch("/api/certificates",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({htmlContent:t})});if(!s.ok)throw Error("Failed to generate PDF");let a=await s.blob(),i=URL.createObjectURL(a),n=document.createElement("a");n.href=i,n.download=`certificate-${e.certificateId}.pdf`,document.body.appendChild(n),n.click(),document.body.removeChild(n),URL.revokeObjectURL(i)}catch(i){console.error("Error generating PDF:",i);let t=new Blob([r(e)],{type:"text/html"}),s=URL.createObjectURL(t),a=document.createElement("a");a.href=s,a.download=`certificate-${e.certificateId}.html`,document.body.appendChild(a),a.click(),document.body.removeChild(a),URL.revokeObjectURL(s)}},l=e=>{let t=r(e),s=window.open("","_blank");s&&(s.document.write(t),s.document.close())},o=async e=>{if(navigator.share)try{await navigator.share({title:`Certificate of Completion - ${e.courseName}`,text:`I've completed ${e.courseName} with a score of ${e.finalScore}%!`,url:window.location.href})}catch(t){console.error("Error sharing certificate:",t),d(`I've completed ${e.courseName} with a score of ${e.finalScore}%! Certificate ID: ${e.certificateId}`)}else d(`I've completed ${e.courseName} with a score of ${e.finalScore}%! Certificate ID: ${e.certificateId}`)},d=e=>{navigator.clipboard.writeText(e).then(()=>{console.log("Certificate details copied to clipboard")}).catch(e=>{console.error("Failed to copy to clipboard:",e)})}},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},87785:(e,t,s)=>{Promise.resolve().then(s.bind(s,32777))},93438:(e,t,s)=>{"use strict";s.d(t,{Cf:()=>m,Es:()=>x,L3:()=>p,c7:()=>u,lG:()=>l,rr:()=>h,zM:()=>o});var a=s(91754);s(93491);var r=s(18227),i=s(31619),n=s(82233);function l({...e}){return(0,a.jsx)(r.bL,{"data-slot":"dialog",...e,"data-sentry-element":"DialogPrimitive.Root","data-sentry-component":"Dialog","data-sentry-source-file":"dialog.tsx"})}function o({...e}){return(0,a.jsx)(r.l9,{"data-slot":"dialog-trigger",...e,"data-sentry-element":"DialogPrimitive.Trigger","data-sentry-component":"DialogTrigger","data-sentry-source-file":"dialog.tsx"})}function d({...e}){return(0,a.jsx)(r.ZL,{"data-slot":"dialog-portal",...e,"data-sentry-element":"DialogPrimitive.Portal","data-sentry-component":"DialogPortal","data-sentry-source-file":"dialog.tsx"})}function c({className:e,...t}){return(0,a.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,n.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t,"data-sentry-element":"DialogPrimitive.Overlay","data-sentry-component":"DialogOverlay","data-sentry-source-file":"dialog.tsx"})}function m({className:e,children:t,...s}){return(0,a.jsxs)(d,{"data-slot":"dialog-portal","data-sentry-element":"DialogPortal","data-sentry-component":"DialogContent","data-sentry-source-file":"dialog.tsx",children:[(0,a.jsx)(c,{"data-sentry-element":"DialogOverlay","data-sentry-source-file":"dialog.tsx"}),(0,a.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,n.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...s,"data-sentry-element":"DialogPrimitive.Content","data-sentry-source-file":"dialog.tsx",children:[t,(0,a.jsxs)(r.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4","data-sentry-element":"DialogPrimitive.Close","data-sentry-source-file":"dialog.tsx",children:[(0,a.jsx)(i.A,{"data-sentry-element":"XIcon","data-sentry-source-file":"dialog.tsx"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function u({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"dialog-header",className:(0,n.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t,"data-sentry-component":"DialogHeader","data-sentry-source-file":"dialog.tsx"})}function x({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"dialog-footer",className:(0,n.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t,"data-sentry-component":"DialogFooter","data-sentry-source-file":"dialog.tsx"})}function p({className:e,...t}){return(0,a.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,n.cn)("text-lg leading-none font-semibold",e),...t,"data-sentry-element":"DialogPrimitive.Title","data-sentry-component":"DialogTitle","data-sentry-source-file":"dialog.tsx"})}function h({className:e,...t}){return(0,a.jsx)(r.VY,{"data-slot":"dialog-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...t,"data-sentry-element":"DialogPrimitive.Description","data-sentry-component":"DialogDescription","data-sentry-source-file":"dialog.tsx"})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[5250,7688,881,4836,7969,6483,3077,1481,8134,2095,8560,9678],()=>s(7441));module.exports=a})();
//# sourceMappingURL=page.js.map