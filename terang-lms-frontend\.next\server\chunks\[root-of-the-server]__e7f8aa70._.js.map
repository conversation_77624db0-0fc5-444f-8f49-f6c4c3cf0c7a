{"version": 3, "sources": [], "sections": [{"offset": {"line": 263, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/instrumentation.ts"], "sourcesContent": ["import * as Sentry from '@sentry/nextjs';\r\n\r\nconst sentryOptions: Sentry.NodeOptions | Sentry.EdgeOptions = {\r\n  // Sentry DSN\r\n  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,\r\n\r\n  // Enable Spotlight in development\r\n  spotlight: process.env.NODE_ENV === 'development',\r\n\r\n  // Adds request headers and IP for users, for more info visit\r\n  sendDefaultPii: true,\r\n\r\n  // Adjust this value in production, or use tracesSampler for greater control\r\n  tracesSampleRate: 1,\r\n\r\n  // Setting this option to true will print useful information to the console while you're setting up Sentry.\r\n  debug: false\r\n};\r\n\r\nexport async function register() {\r\n  if (!process.env.NEXT_PUBLIC_SENTRY_DISABLED) {\r\n    if (process.env.NEXT_RUNTIME === 'nodejs') {\r\n      // Node.js Sentry configuration\r\n      Sentry.init(sentryOptions);\r\n    }\r\n\r\n    if (process.env.NEXT_RUNTIME === 'edge') {\r\n      // Edge Sentry configuration\r\n      Sentry.init(sentryOptions);\r\n    }\r\n  }\r\n}\r\n\r\nexport const onRequestError = Sentry.captureRequestError;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,gBAAyD;IAC7D,aAAa;IACb,KAAK,QAAQ,GAAG,CAAC,sBAAsB;IAEvC,kCAAkC;IAClC,WAAW,oDAAyB;IAEpC,6DAA6D;IAC7D,gBAAgB;IAEhB,4EAA4E;IAC5E,kBAAkB;IAElB,2GAA2G;IAC3G,OAAO;AACT;AAEO,eAAe;IACpB,IAAI,CAAC,QAAQ,GAAG,CAAC,2BAA2B,EAAE;QAC5C,wCAA2C;YACzC,+BAA+B;YAC/B,CAAA,GAAA,0KAAA,CAAA,OAAW,AAAD,EAAE;QACd;QAEA,uCAAyC;;QAGzC;IACF;AACF;AAEO,MAAM,iBAAiB,0KAAA,CAAA,sBAA0B", "debugId": null}}]}