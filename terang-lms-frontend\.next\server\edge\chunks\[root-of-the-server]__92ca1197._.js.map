{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/instrumentation.ts"], "sourcesContent": ["import * as Sentry from '@sentry/nextjs';\r\n\r\nconst sentryOptions: Sentry.NodeOptions | Sentry.EdgeOptions = {\r\n  // Sentry DSN\r\n  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,\r\n\r\n  // Enable Spotlight in development\r\n  spotlight: process.env.NODE_ENV === 'development',\r\n\r\n  // Adds request headers and IP for users, for more info visit\r\n  sendDefaultPii: true,\r\n\r\n  // Adjust this value in production, or use tracesSampler for greater control\r\n  tracesSampleRate: 1,\r\n\r\n  // Setting this option to true will print useful information to the console while you're setting up Sentry.\r\n  debug: false\r\n};\r\n\r\nexport async function register() {\r\n  if (!process.env.NEXT_PUBLIC_SENTRY_DISABLED) {\r\n    if (process.env.NEXT_RUNTIME === 'nodejs') {\r\n      // Node.js Sentry configuration\r\n      Sentry.init(sentryOptions);\r\n    }\r\n\r\n    if (process.env.NEXT_RUNTIME === 'edge') {\r\n      // Edge Sentry configuration\r\n      Sentry.init(sentryOptions);\r\n    }\r\n  }\r\n}\r\n\r\nexport const onRequestError = Sentry.captureRequestError;\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAEA,MAAM,gBAAyD;IAC7D,aAAa;IACb,KAAK,QAAQ,GAAG,CAAC,sBAAsB;IAEvC,kCAAkC;IAClC,WAAW,oDAAyB;IAEpC,6DAA6D;IAC7D,gBAAgB;IAEhB,4EAA4E;IAC5E,kBAAkB;IAElB,2GAA2G;IAC3G,OAAO;AACT;AAEO,eAAe;IACpB,IAAI,CAAC,QAAQ,GAAG,CAAC,2BAA2B,EAAE;QAC5C,uCAA2C;;QAG3C;QAEA,wCAAyC;YACvC,4BAA4B;YAC5B,CAAA,GAAA,gMAAA,CAAA,OAAW,AAAD,EAAE;QACd;IACF;AACF;AAEO,MAAM,iBAAiB,gMAAA,CAAA,sBAA0B"}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/edge-wrapper.js"], "sourcesContent": ["self._ENTRIES ||= {};\nconst modProm = import('MODULE');\nmodProm.catch(() => {});\nself._ENTRIES[\"middleware_instrumentation\"] = new Proxy(modProm, {\n    get(modProm, name) {\n        if (name === \"then\") {\n            return (res, rej) => modProm.then(res, rej);\n        }\n        let result = (...args) => modProm.then((mod) => (0, mod[name])(...args));\n        result.then = (res, rej) => modProm.then((mod) => mod[name]).then(res, rej);\n        return result;\n    },\n});\n"], "names": [], "mappings": "AAAA,KAAK,QAAQ,KAAK,CAAC;AACnB,MAAM;AACN,QAAQ,KAAK,CAAC,KAAO;AACrB,KAAK,QAAQ,CAAC,6BAA6B,GAAG,IAAI,MAAM,SAAS;IAC7D,KAAI,OAAO,EAAE,IAAI;QACb,IAAI,SAAS,QAAQ;YACjB,OAAO,CAAC,KAAK,MAAQ,QAAQ,IAAI,CAAC,KAAK;QAC3C;QACA,IAAI,SAAS,CAAC,GAAG,OAAS,QAAQ,IAAI,CAAC,CAAC,MAAQ,CAAC,GAAG,GAAG,CAAC,KAAK,KAAK;QAClE,OAAO,IAAI,GAAG,CAAC,KAAK,MAAQ,QAAQ,IAAI,CAAC,CAAC,MAAQ,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK;QACvE,OAAO;IACX;AACJ"}}]}