{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\r\n\r\nexport function middleware(request: NextRequest) {\r\n  const { pathname } = request.nextUrl;\r\n\r\n  // Skip middleware for API routes, static files, and auth pages\r\n  if (\r\n    pathname.startsWith('/api/') ||\r\n    pathname.startsWith('/_next/') ||\r\n    pathname.startsWith('/static/') ||\r\n    pathname.startsWith('/auth/') ||\r\n    pathname === '/' ||\r\n    pathname.includes('.')\r\n  ) {\r\n    return NextResponse.next();\r\n  }\r\n\r\n  // Check if accessing dashboard routes\r\n  if (pathname.startsWith('/dashboard/')) {\r\n    // For now, just allow access - client-side auth will handle role checking\r\n    // In a production app, you might want to verify JWT tokens here\r\n    return NextResponse.next();\r\n  }\r\n\r\n  return NextResponse.next();\r\n}\r\n\r\nexport const config = {\r\n  matcher: ['/((?!_next|static|.*\\\\..*|_static|_vercel).*)']\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAEO,SAAS,WAAW,OAAoB;IAC7C,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,+DAA+D;IAC/D,IACE,SAAS,UAAU,CAAC,YACpB,SAAS,UAAU,CAAC,cACpB,SAAS,UAAU,CAAC,eACpB,SAAS,UAAU,CAAC,aACpB,aAAa,OACb,SAAS,QAAQ,CAAC,MAClB;QACA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,sCAAsC;IACtC,IAAI,SAAS,UAAU,CAAC,gBAAgB;QACtC,0EAA0E;QAC1E,gEAAgE;QAChE,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,SAAS;QAAC;KAAgD;AAC5D"}}]}