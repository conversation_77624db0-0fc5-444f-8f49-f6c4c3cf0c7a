try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="9b614ba5-6d6a-49a6-bd16-a73201d950eb",e._sentryDebugIdIdentifier="sentry-dbid-9b614ba5-6d6a-49a6-bd16-a73201d950eb")}catch(e){}exports.id=9646,exports.ids=[9646],exports.modules={5827:(e,t,s)=>{"use strict";s.d(t,{k:()=>l});var a=s(91754),n=s(93491),r=s(66536),i=s(82233);let l=n.forwardRef(({className:e,value:t,...s},n)=>(0,a.jsx)(r.bL,{ref:n,className:(0,i.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...s,children:(0,a.jsx)(r.C1,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})}));l.displayName=r.bL.displayName},8934:(e,t,s)=>{"use strict";s.d(t,{Fc:()=>l,TN:()=>c,XL:()=>d});var a=s(91754);s(93491);var n=s(25758),r=s(82233);let i=(0,n.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,...s}){return(0,a.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,r.cn)(i({variant:t}),e),...s,"data-sentry-component":"Alert","data-sentry-source-file":"alert.tsx"})}function d({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"alert-title",className:(0,r.cn)("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",e),...t,"data-sentry-component":"AlertTitle","data-sentry-source-file":"alert.tsx"})}function c({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"alert-description",className:(0,r.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...t,"data-sentry-component":"AlertDescription","data-sentry-source-file":"alert.tsx"})}},9260:(e,t,s)=>{"use strict";s.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>r,aR:()=>i,wL:()=>o});var a=s(91754);s(93491);var n=s(82233);function r({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function l({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",e),...t,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function d({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...t,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function c({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",e),...t,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,n.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},14621:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var a=s(91754);function n({children:e}){return(0,a.jsx)(a.Fragment,{children:e})}s(93491),s(76328)},21626:(e,t,s)=>{"use strict";s.d(t,{J:()=>i});var a=s(91754);s(93491);var n=s(66207),r=s(82233);function i({className:e,...t}){return(0,a.jsx)(n.b,{"data-slot":"label",className:(0,r.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t,"data-sentry-element":"LabelPrimitive.Root","data-sentry-component":"Label","data-sentry-source-file":"label.tsx"})}},49742:(e,t,s)=>{"use strict";s.d(t,{q:()=>eq});var a=s(91754),n=s(93491),r=s.n(n),i=s(56682),l=s(9260),d=s(5827),c=s(92501),o=s(87435),m=s(37980),u=s(44331),x=s(82233),p=s(59672),h=s(21626),y=s(58428),f=s(69122),j=s(80601),g=s(15624),v=s(83939),b=s(73225),N=s(49056),k=s(85108),C=s(46934),w=s(31619),A=s(8238),D=s(24727),z=s(79233),S=s(81012);function T({data:e,onUpdate:t}){let[s,r]=(0,n.useState)(!1),[l,d]=(0,n.useState)(!!(e.startDate||e.endDate)),c=(0,n.useRef)(null);return(0,a.jsxs)("div",{className:"space-y-6","data-sentry-component":"BasicInfoStep","data-sentry-source-file":"basic-info-step.tsx",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(h.J,{htmlFor:"courseName","data-sentry-element":"Label","data-sentry-source-file":"basic-info-step.tsx",children:"Nama Course *"}),(0,a.jsx)(p.p,{id:"courseName",placeholder:"Masukkan nama course",value:e.name,onChange:e=>t({name:e.target.value}),"data-sentry-element":"Input","data-sentry-source-file":"basic-info-step.tsx"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(h.J,{htmlFor:"instructor","data-sentry-element":"Label","data-sentry-source-file":"basic-info-step.tsx",children:"Nama Instruktur *"}),(0,a.jsx)(p.p,{id:"instructor",placeholder:"Masukkan nama instruktur",value:e.instructor,onChange:e=>t({instructor:e.target.value}),"data-sentry-element":"Input","data-sentry-source-file":"basic-info-step.tsx"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(h.J,{htmlFor:"courseCode","data-sentry-element":"Label","data-sentry-source-file":"basic-info-step.tsx",children:"Kode Course *"}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(p.p,{id:"courseCode",placeholder:"Kode unik untuk course",value:e.courseCode,onChange:e=>t({courseCode:e.target.value.toUpperCase()}),className:"flex-1","data-sentry-element":"Input","data-sentry-source-file":"basic-info-step.tsx"}),(0,a.jsxs)(i.$,{type:"button",variant:"outline",onClick:()=>{r(!0),setTimeout(()=>{t({courseCode:Math.random().toString(36).substring(2,8).toUpperCase()}),r(!1),S.oR.success("Kode course berhasil dibuat")},1e3)},disabled:s,"data-sentry-element":"Button","data-sentry-source-file":"basic-info-step.tsx",children:[(0,a.jsx)(C.A,{className:"w-4 h-4 mr-2","data-sentry-element":"Shuffle","data-sentry-source-file":"basic-info-step.tsx"}),s?"Membuat...":"Generate"]})]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Kode ini akan digunakan siswa untuk mendaftar ke course"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(h.J,{htmlFor:"description","data-sentry-element":"Label","data-sentry-source-file":"basic-info-step.tsx",children:"Deskripsi Course *"}),(0,a.jsx)(y.T,{id:"description",placeholder:"Jelaskan tentang course ini...",value:e.description,onChange:e=>t({description:e.target.value}),rows:4,"data-sentry-element":"Textarea","data-sentry-source-file":"basic-info-step.tsx"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(h.J,{"data-sentry-element":"Label","data-sentry-source-file":"basic-info-step.tsx",children:"Cover Image"}),e.coverImagePreview?(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("img",{src:e.coverImagePreview,alt:"Course cover",className:"w-full h-auto object-cover rounded-md aspect-video"}),(0,a.jsx)(i.$,{type:"button",variant:"destructive",size:"sm",className:"absolute top-2 right-2",onClick:()=>{e.coverImagePreview&&URL.revokeObjectURL(e.coverImagePreview),t({coverImage:void 0,coverImagePreview:void 0})},children:(0,a.jsx)(w.A,{className:"w-4 h-4"})})]}):(0,a.jsxs)("div",{className:"border-2 border-dashed border-muted-foreground/25 rounded-md p-6 text-center cursor-pointer hover:border-muted-foreground/50 transition-colors aspect-video flex flex-col items-center justify-center",onClick:()=>c.current?.click(),children:[(0,a.jsx)(A.A,{className:"w-8 h-8 mx-auto mb-2 text-muted-foreground"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Klik untuk upload cover image"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"PNG, JPG hingga 5MB"})]}),(0,a.jsx)("input",{ref:c,type:"file",accept:"image/*",onChange:e=>{let s=e.target.files?.[0];if(!s)return;if(!s.type.startsWith("image/"))return void S.oR.error("File harus berupa gambar");if(s.size>5242880)return void S.oR.error("Ukuran file maksimal 5MB");let a=URL.createObjectURL(s);t({coverImage:s,coverImagePreview:a}),S.oR.success("Gambar berhasil diunggah")},className:"hidden"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(h.J,{"data-sentry-element":"Label","data-sentry-source-file":"basic-info-step.tsx",children:"Tipe Course *"}),(0,a.jsxs)(v.AM,{"data-sentry-element":"Popover","data-sentry-source-file":"basic-info-step.tsx",children:[(0,a.jsx)(v.Wv,{asChild:!0,"data-sentry-element":"PopoverTrigger","data-sentry-source-file":"basic-info-step.tsx",children:(0,a.jsx)(i.$,{variant:"ghost",size:"sm",className:"h-auto p-1","data-sentry-element":"Button","data-sentry-source-file":"basic-info-step.tsx",children:(0,a.jsx)(D.A,{className:"h-4 w-4 text-muted-foreground","data-sentry-element":"Info","data-sentry-source-file":"basic-info-step.tsx"})})}),(0,a.jsx)(v.hl,{className:"w-96",align:"start","data-sentry-element":"PopoverContent","data-sentry-source-file":"basic-info-step.tsx",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h4",{className:"font-medium text-sm",children:"Informasi Tipe Course"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(0,a.jsx)(j.E,{variant:"secondary","data-sentry-element":"Badge","data-sentry-source-file":"basic-info-step.tsx",children:"Self-paced"})}),(0,a.jsxs)("ul",{className:"text-xs text-muted-foreground space-y-1",children:[(0,a.jsx)("li",{children:"• Siswa belajar dengan kecepatan sendiri"}),(0,a.jsx)("li",{children:"• Tidak ada deadline ketat"}),(0,a.jsx)("li",{children:"• Akses selamanya setelah enrollment"}),(0,a.jsx)("li",{children:"• Cocok untuk pembelajaran mandiri"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(0,a.jsx)(j.E,{variant:"default","data-sentry-element":"Badge","data-sentry-source-file":"basic-info-step.tsx",children:"Verified"})}),(0,a.jsxs)("ul",{className:"text-xs text-muted-foreground space-y-1",children:[(0,a.jsx)("li",{children:"• Course dengan jadwal dan deadline"}),(0,a.jsx)("li",{children:"• Sertifikat resmi setelah selesai"}),(0,a.jsx)("li",{children:"• Monitoring progress lebih ketat"}),(0,a.jsx)("li",{children:"• Cocok untuk pembelajaran formal"})]})]})]})]})})]})]}),(0,a.jsxs)(f.l6,{value:e.type,onValueChange:e=>t({type:e}),"data-sentry-element":"Select","data-sentry-source-file":"basic-info-step.tsx",children:[(0,a.jsx)(f.bq,{"data-sentry-element":"SelectTrigger","data-sentry-source-file":"basic-info-step.tsx",children:(0,a.jsx)(f.yv,{"data-sentry-element":"SelectValue","data-sentry-source-file":"basic-info-step.tsx"})}),(0,a.jsxs)(f.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"basic-info-step.tsx",children:[(0,a.jsx)(f.eb,{value:"self_paced","data-sentry-element":"SelectItem","data-sentry-source-file":"basic-info-step.tsx",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(j.E,{variant:"secondary","data-sentry-element":"Badge","data-sentry-source-file":"basic-info-step.tsx",children:"Self-paced"}),(0,a.jsx)("span",{children:"Siswa belajar dengan kecepatan sendiri"})]})}),(0,a.jsx)(f.eb,{value:"verified","data-sentry-element":"SelectItem","data-sentry-source-file":"basic-info-step.tsx",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(j.E,{variant:"default","data-sentry-element":"Badge","data-sentry-source-file":"basic-info-step.tsx",children:"Verified"}),(0,a.jsx)("span",{children:"Course dengan jadwal dan deadline"})]})})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(h.J,{"data-sentry-element":"Label","data-sentry-source-file":"basic-info-step.tsx",children:"Tipe Pendaftaran *"}),(0,a.jsxs)(f.l6,{value:e.enrollmentType,onValueChange:s=>{let a={enrollmentType:s};"purchase"!==s&&"both"!==s||e.currency||(a.currency="IDR"),t(a)},"data-sentry-element":"Select","data-sentry-source-file":"basic-info-step.tsx",children:[(0,a.jsx)(f.bq,{"data-sentry-element":"SelectTrigger","data-sentry-source-file":"basic-info-step.tsx",children:(0,a.jsx)(f.yv,{"data-sentry-element":"SelectValue","data-sentry-source-file":"basic-info-step.tsx"})}),(0,a.jsxs)(f.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"basic-info-step.tsx",children:[(0,a.jsx)(f.eb,{value:"code","data-sentry-element":"SelectItem","data-sentry-source-file":"basic-info-step.tsx",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(j.E,{variant:"outline","data-sentry-element":"Badge","data-sentry-source-file":"basic-info-step.tsx",children:"Kode"}),(0,a.jsx)("span",{children:"Siswa mendaftar dengan kode"})]})}),(0,a.jsx)(f.eb,{value:"invitation","data-sentry-element":"SelectItem","data-sentry-source-file":"basic-info-step.tsx",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(j.E,{variant:"outline","data-sentry-element":"Badge","data-sentry-source-file":"basic-info-step.tsx",children:"Undangan"}),(0,a.jsx)("span",{children:"Hanya dengan undangan"})]})}),(0,a.jsx)(f.eb,{value:"both","data-sentry-element":"SelectItem","data-sentry-source-file":"basic-info-step.tsx",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(j.E,{variant:"outline","data-sentry-element":"Badge","data-sentry-source-file":"basic-info-step.tsx",children:"Keduanya"}),(0,a.jsx)("span",{children:"Kode atau undangan"})]})}),(0,a.jsx)(f.eb,{value:"purchase","data-sentry-element":"SelectItem","data-sentry-source-file":"basic-info-step.tsx",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(j.E,{variant:"default","data-sentry-element":"Badge","data-sentry-source-file":"basic-info-step.tsx",children:"Berbayar"}),(0,a.jsx)("span",{children:"Siswa harus membeli"})]})})]})]})]}),("purchase"===e.enrollmentType||"both"===e.enrollmentType)&&(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2 md:col-span-2",children:[(0,a.jsx)(h.J,{htmlFor:"price",children:"Harga *"}),(0,a.jsx)(p.p,{id:"price",type:"number",placeholder:"0",value:e.price||"",onChange:e=>t({price:parseFloat(e.target.value)||0}),min:"0",step:"1000"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(h.J,{children:"Mata Uang *"}),(0,a.jsxs)(f.l6,{value:e.currency||"IDR",onValueChange:e=>t({currency:e}),children:[(0,a.jsx)(f.bq,{children:(0,a.jsx)(f.yv,{})}),(0,a.jsxs)(f.gC,{children:[(0,a.jsx)(f.eb,{value:"IDR",children:"IDR (Rupiah)"}),(0,a.jsx)(f.eb,{value:"USD",children:"USD (Dollar)"}),(0,a.jsx)(f.eb,{value:"EUR",children:"EUR (Euro)"})]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(b.S,{id:"enableDateRange",checked:l,onCheckedChange:e=>{d(e),e||t({startDate:null,endDate:null})},"data-sentry-element":"Checkbox","data-sentry-source-file":"basic-info-step.tsx"}),(0,a.jsx)(h.J,{htmlFor:"enableDateRange","data-sentry-element":"Label","data-sentry-source-file":"basic-info-step.tsx",children:"Atur Tanggal Mulai & Selesai"})]}),l&&(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(h.J,{children:"Tanggal Mulai"}),(0,a.jsxs)(v.AM,{children:[(0,a.jsx)(v.Wv,{asChild:!0,children:(0,a.jsxs)(i.$,{variant:"outline",className:(0,x.cn)("w-full justify-start text-left font-normal",!e.startDate&&"text-muted-foreground"),children:[(0,a.jsx)(z.A,{className:"mr-2 h-4 w-4"}),e.startDate?(0,N.GP)(e.startDate,"PPP",{locale:k.id}):"Pilih tanggal mulai"]})}),(0,a.jsx)(v.hl,{className:"w-auto p-0",align:"start",children:(0,a.jsx)(g.V,{mode:"single",selected:e.startDate||void 0,onSelect:e=>t({startDate:e}),disabled:e=>e<new Date,initialFocus:!0})})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(h.J,{children:"Tanggal Selesai"}),(0,a.jsxs)(v.AM,{children:[(0,a.jsx)(v.Wv,{asChild:!0,children:(0,a.jsxs)(i.$,{variant:"outline",className:(0,x.cn)("w-full justify-start text-left font-normal",!e.endDate&&"text-muted-foreground"),children:[(0,a.jsx)(z.A,{className:"mr-2 h-4 w-4"}),e.endDate?(0,N.GP)(e.endDate,"PPP",{locale:k.id}):"Pilih tanggal selesai"]})}),(0,a.jsx)(v.hl,{className:"w-auto p-0",align:"start",children:(0,a.jsx)(g.V,{mode:"single",selected:e.endDate||void 0,onSelect:e=>t({endDate:e}),disabled:t=>!!(t<new Date||e.startDate&&t<=e.startDate),initialFocus:!0})})]})]})]})]})]})}var P=s(37607);function B({className:e,...t}){return(0,a.jsx)(P.bL,{"data-slot":"switch",className:(0,x.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,"data-sentry-element":"SwitchPrimitive.Root","data-sentry-component":"Switch","data-sentry-source-file":"switch.tsx",children:(0,a.jsx)(P.zi,{"data-slot":"switch-thumb",className:(0,x.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0"),"data-sentry-element":"SwitchPrimitive.Thumb","data-sentry-source-file":"switch.tsx"})})}var E=s(93438),q=s(91269),$=s(41939),I=s(41867),L=s(57850),F=s(39547),R=s(99462),M=s(93626),Q=s(33093),U=s(85629);function J({data:e,onUpdate:t}){let[s,r]=(0,n.useState)(new Set),[d,c]=(0,n.useState)(null),[o,m]=(0,n.useState)({moduleId:"",chapter:null}),[x,f]=(0,n.useState)(!1),[g,v]=(0,n.useState)(!1),b=e=>{let t=new Set(s);t.has(e)?t.delete(e):t.add(e),r(t)},N=()=>{c({id:`module-${Date.now()}`,name:"",description:"",orderIndex:e.modules.length,chapters:[],hasModuleQuiz:!1}),f(!0)},k=e=>{c({...e}),f(!0)},C=s=>{t({modules:e.modules.filter(e=>e.id!==s).map((e,t)=>({...e,orderIndex:t}))}),S.oR.success("Modul berhasil dihapus")},w=t=>{let s=e.modules.find(e=>e.id===t);s&&(m({moduleId:t,chapter:{id:`chapter-${Date.now()}`,name:"",content:[],orderIndex:s.chapters.length,hasChapterQuiz:!1}}),v(!0))},A=(e,t)=>{m({moduleId:e,chapter:{...t}}),v(!0)},D=(s,a)=>{t({modules:e.modules.map(e=>{if(e.id===s){let t=e.chapters.filter(e=>e.id!==a).map((e,t)=>({...e,orderIndex:t}));return{...e,chapters:t}}return e})}),S.oR.success("Chapter berhasil dihapus")},z=(s,a)=>{let n=e.modules.findIndex(e=>e.id===s);if(-1===n)return;let r="up"===a?n-1:n+1;if(r<0||r>=e.modules.length)return;let i=[...e.modules];[i[n],i[r]]=[i[r],i[n]],i.forEach((e,t)=>{e.orderIndex=t}),t({modules:i})};return(0,a.jsxs)("div",{className:"space-y-6","data-sentry-component":"ModuleStructureStep","data-sentry-source-file":"module-structure-step.tsx",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Struktur Modul Course"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Buat modul dan chapter untuk mengorganisir konten course"})]}),(0,a.jsxs)(i.$,{onClick:N,"data-sentry-element":"Button","data-sentry-source-file":"module-structure-step.tsx",children:[(0,a.jsx)($.A,{className:"w-4 h-4 mr-2","data-sentry-element":"Plus","data-sentry-source-file":"module-structure-step.tsx"}),"Tambah Modul"]})]}),0===e.modules.length?(0,a.jsx)(l.Zp,{children:(0,a.jsxs)(l.Wu,{className:"flex flex-col items-center justify-center py-12",children:[(0,a.jsx)(I.A,{className:"w-12 h-12 text-muted-foreground mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Belum ada modul"}),(0,a.jsx)("p",{className:"text-muted-foreground text-center mb-4",children:"Mulai dengan membuat modul pertama untuk course Anda"}),(0,a.jsxs)(i.$,{onClick:N,children:[(0,a.jsx)($.A,{className:"w-4 h-4 mr-2"}),"Buat Modul Pertama"]})]})}):(0,a.jsx)("div",{className:"space-y-4",children:e.modules.map((t,n)=>{let r=s.has(t.id);return(0,a.jsxs)(l.Zp,{className:"overflow-hidden",children:[(0,a.jsx)(l.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(L.A,{className:"w-4 h-4 text-muted-foreground cursor-move"}),(0,a.jsxs)(j.E,{variant:"outline",children:["Modul ",n+1]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(l.ZB,{className:"text-base",children:t.name||"Modul Tanpa Nama"}),t.description&&(0,a.jsx)(l.BT,{className:"mt-1",children:t.description})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[t.hasModuleQuiz&&(0,a.jsxs)(j.E,{variant:"secondary",children:[(0,a.jsx)(F.A,{className:"w-3 h-3 mr-1"}),"Quiz Modul"]}),(0,a.jsxs)(j.E,{variant:"outline",children:[t.chapters.length," Chapter"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>z(t.id,"up"),disabled:0===n,children:"↑"}),(0,a.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>z(t.id,"down"),disabled:n===e.modules.length-1,children:"↓"}),(0,a.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>k(t),children:(0,a.jsx)(R.A,{className:"w-4 h-4"})}),(0,a.jsxs)(q.Lt,{children:[(0,a.jsx)(q.tv,{asChild:!0,children:(0,a.jsx)(i.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(M.A,{className:"w-4 h-4"})})}),(0,a.jsxs)(q.EO,{children:[(0,a.jsxs)(q.wd,{children:[(0,a.jsx)(q.r7,{children:"Hapus Modul"}),(0,a.jsxs)(q.$v,{children:["Apakah Anda yakin ingin menghapus modul “",t.name,"”? Semua chapter di dalam modul ini juga akan terhapus."]})]}),(0,a.jsxs)(q.ck,{children:[(0,a.jsx)(q.Zr,{children:"Batal"}),(0,a.jsx)(q.Rx,{onClick:()=>C(t.id),children:"Hapus"})]})]})]}),(0,a.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>b(t.id),children:r?(0,a.jsx)(Q.A,{className:"w-4 h-4"}):(0,a.jsx)(u.A,{className:"w-4 h-4"})})]})]})]})}),r&&(0,a.jsx)(l.Wu,{className:"pt-0",children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h4",{className:"text-sm font-medium",children:"Chapters"}),(0,a.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>w(t.id),children:[(0,a.jsx)($.A,{className:"w-4 h-4 mr-2"}),"Tambah Chapter"]})]}),0===t.chapters.length?(0,a.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,a.jsx)(U.A,{className:"w-8 h-8 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-sm",children:"Belum ada chapter"})]}):(0,a.jsx)("div",{className:"space-y-2",children:t.chapters.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-muted/50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(L.A,{className:"w-4 h-4 text-muted-foreground cursor-move"}),(0,a.jsx)(j.E,{variant:"outline",className:"text-xs",children:s+1}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:e.name||"Chapter Tanpa Nama"}),e.hasChapterQuiz&&(0,a.jsxs)(j.E,{variant:"secondary",className:"text-xs mt-1",children:[(0,a.jsx)(F.A,{className:"w-3 h-3 mr-1"}),"Quiz"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>A(t.id,e),children:(0,a.jsx)(R.A,{className:"w-4 h-4"})}),(0,a.jsxs)(q.Lt,{children:[(0,a.jsx)(q.tv,{asChild:!0,children:(0,a.jsx)(i.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(M.A,{className:"w-4 h-4"})})}),(0,a.jsxs)(q.EO,{children:[(0,a.jsxs)(q.wd,{children:[(0,a.jsx)(q.r7,{children:"Hapus Chapter"}),(0,a.jsxs)(q.$v,{children:["Apakah Anda yakin ingin menghapus chapter “",e.name,"”?"]})]}),(0,a.jsxs)(q.ck,{children:[(0,a.jsx)(q.Zr,{children:"Batal"}),(0,a.jsx)(q.Rx,{onClick:()=>D(t.id,e.id),children:"Hapus"})]})]})]})]})]},e.id))})]})})]},t.id)})}),(0,a.jsx)(E.lG,{open:x,onOpenChange:f,"data-sentry-element":"Dialog","data-sentry-source-file":"module-structure-step.tsx",children:(0,a.jsxs)(E.Cf,{className:"sm:max-w-md","data-sentry-element":"DialogContent","data-sentry-source-file":"module-structure-step.tsx",children:[(0,a.jsxs)(E.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"module-structure-step.tsx",children:[(0,a.jsx)(E.L3,{"data-sentry-element":"DialogTitle","data-sentry-source-file":"module-structure-step.tsx",children:d?.name?"Edit Modul":"Tambah Modul Baru"}),(0,a.jsx)(E.rr,{"data-sentry-element":"DialogDescription","data-sentry-source-file":"module-structure-step.tsx",children:"Isi informasi dasar untuk modul ini"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(h.J,{htmlFor:"moduleName","data-sentry-element":"Label","data-sentry-source-file":"module-structure-step.tsx",children:"Nama Modul *"}),(0,a.jsx)(p.p,{id:"moduleName",placeholder:"Masukkan nama modul",value:d?.name||"",onChange:e=>c(t=>t?{...t,name:e.target.value}:null),"data-sentry-element":"Input","data-sentry-source-file":"module-structure-step.tsx"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(h.J,{htmlFor:"moduleDescription","data-sentry-element":"Label","data-sentry-source-file":"module-structure-step.tsx",children:"Deskripsi"}),(0,a.jsx)(y.T,{id:"moduleDescription",placeholder:"Jelaskan tentang modul ini...",value:d?.description||"",onChange:e=>c(t=>t?{...t,description:e.target.value}:null),rows:3,"data-sentry-element":"Textarea","data-sentry-source-file":"module-structure-step.tsx"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(B,{id:"hasModuleQuiz",checked:d?.hasModuleQuiz||!1,onCheckedChange:e=>c(t=>t?{...t,hasModuleQuiz:e}:null),"data-sentry-element":"Switch","data-sentry-source-file":"module-structure-step.tsx"}),(0,a.jsx)(h.J,{htmlFor:"hasModuleQuiz","data-sentry-element":"Label","data-sentry-source-file":"module-structure-step.tsx",children:"Tambahkan quiz di akhir modul"})]})]}),(0,a.jsxs)(E.Es,{"data-sentry-element":"DialogFooter","data-sentry-source-file":"module-structure-step.tsx",children:[(0,a.jsx)(i.$,{variant:"outline",onClick:()=>f(!1),"data-sentry-element":"Button","data-sentry-source-file":"module-structure-step.tsx",children:"Batal"}),(0,a.jsxs)(i.$,{onClick:()=>{if(!d||!d.name.trim())return void S.oR.error("Nama modul harus diisi");let s=[...e.modules],a=s.findIndex(e=>e.id===d.id);a>=0?(s[a]=d,S.oR.success("Modul berhasil diperbarui")):(s.push(d),S.oR.success("Modul berhasil ditambahkan")),t({modules:s}),f(!1),c(null)},"data-sentry-element":"Button","data-sentry-source-file":"module-structure-step.tsx",children:[d?.name?"Perbarui":"Tambah"," Modul"]})]})]})}),(0,a.jsx)(E.lG,{open:g,onOpenChange:v,"data-sentry-element":"Dialog","data-sentry-source-file":"module-structure-step.tsx",children:(0,a.jsxs)(E.Cf,{className:"sm:max-w-md","data-sentry-element":"DialogContent","data-sentry-source-file":"module-structure-step.tsx",children:[(0,a.jsxs)(E.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"module-structure-step.tsx",children:[(0,a.jsx)(E.L3,{"data-sentry-element":"DialogTitle","data-sentry-source-file":"module-structure-step.tsx",children:o.chapter?.name?"Edit Chapter":"Tambah Chapter Baru"}),(0,a.jsx)(E.rr,{"data-sentry-element":"DialogDescription","data-sentry-source-file":"module-structure-step.tsx",children:"Isi informasi dasar untuk chapter ini"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(h.J,{htmlFor:"chapterName","data-sentry-element":"Label","data-sentry-source-file":"module-structure-step.tsx",children:"Nama Chapter *"}),(0,a.jsx)(p.p,{id:"chapterName",placeholder:"Masukkan nama chapter",value:o.chapter?.name||"",onChange:e=>m(t=>({...t,chapter:t.chapter?{...t.chapter,name:e.target.value}:null})),"data-sentry-element":"Input","data-sentry-source-file":"module-structure-step.tsx"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(B,{id:"hasChapterQuiz",checked:o.chapter?.hasChapterQuiz||!1,onCheckedChange:e=>m(t=>({...t,chapter:t.chapter?{...t.chapter,hasChapterQuiz:e}:null})),"data-sentry-element":"Switch","data-sentry-source-file":"module-structure-step.tsx"}),(0,a.jsx)(h.J,{htmlFor:"hasChapterQuiz","data-sentry-element":"Label","data-sentry-source-file":"module-structure-step.tsx",children:"Tambahkan quiz untuk chapter ini"})]})]}),(0,a.jsxs)(E.Es,{"data-sentry-element":"DialogFooter","data-sentry-source-file":"module-structure-step.tsx",children:[(0,a.jsx)(i.$,{variant:"outline",onClick:()=>v(!1),"data-sentry-element":"Button","data-sentry-source-file":"module-structure-step.tsx",children:"Batal"}),(0,a.jsxs)(i.$,{onClick:()=>{if(!o.chapter||!o.chapter.name.trim())return void S.oR.error("Nama chapter harus diisi");t({modules:e.modules.map(e=>{if(e.id===o.moduleId){let t=[...e.chapters],s=t.findIndex(e=>e.id===o.chapter.id);return s>=0?t[s]=o.chapter:t.push(o.chapter),{...e,chapters:t}}return e})}),v(!1),m({moduleId:"",chapter:null}),S.oR.success("Chapter berhasil disimpan")},"data-sentry-element":"Button","data-sentry-source-file":"module-structure-step.tsx",children:[o.chapter?.name?"Perbarui":"Tambah"," Chapter"]})]})]})}),e.modules.length>0&&(0,a.jsxs)(l.Zp,{children:[(0,a.jsx)(l.aR,{children:(0,a.jsx)(l.ZB,{className:"text-lg",children:"Ringkasan Struktur"})}),(0,a.jsx)(l.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-primary",children:e.modules.length}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Modul"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-primary",children:e.modules.reduce((e,t)=>e+t.chapters.length,0)}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Chapter"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-primary",children:e.modules.filter(e=>e.hasModuleQuiz).length}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Quiz Modul"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-primary",children:e.modules.reduce((e,t)=>e+t.chapters.filter(e=>e.hasChapterQuiz).length,0)}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Quiz Chapter"})]})]})})]})]})}var Z=s(42352),O=s(33772),H=s(7018),K=s(44620),W=s(54264),_=s(15854);let G=(0,s(24247).default)(async()=>{},{loadableGenerated:{modules:["components\\dynamic-content-editor.tsx -> ./mdx-editor-wrapper"]},ssr:!1,loading:()=>(0,a.jsx)("div",{className:"min-h-[200px] flex items-center justify-center",children:"Loading editor..."})});function V({initialContent:e,onContentChange:t,allowImages:s=!0,placeholder:r,contentRefs:d}){let[c,o]=(0,n.useState)(e),[m,u]=(0,n.useState)(!1),[x,f]=(0,n.useState)("image"),[j,g]=(0,n.useState)(""),v=e=>{let s=[...c,{id:`block-${Date.now()}-${Math.random().toString(36).substring(2,9)}`,type:e,value:""}];o(s),t(s)},b=e=>{f(e),u(!0),g("")},N=(e,s)=>{let a=c.map(t=>t.id===e?{...t,value:s}:t);o(a),t(a)},k=e=>{let s=c.filter(t=>t.id!==e);o(s),t(s)},C=(0,n.useCallback)(async(e,t,s)=>{if(!e||0===e.length)return void S.oR.error("No file selected for upload.");let a=e[0];S.oR.info(`Uploading ${a.name}...`);try{let e=await fetch(`/api/upload?filename=${a.name}`,{method:"POST",body:a});if(!e.ok)throw Error(`Upload failed: ${e.statusText}`);let n=await e.json();N(t,n.url),S.oR.success(`${s.charAt(0).toUpperCase()+s.slice(1)} uploaded successfully!`)}catch(e){console.error(`Error uploading ${s}:`,e),S.oR.error(`Failed to upload ${s}: ${e.message}`)}},[N]);return(0,a.jsxs)("div",{className:"space-y-4","data-sentry-component":"DynamicContentEditor","data-sentry-source-file":"dynamic-content-editor.tsx",children:[c.map((e,t)=>(0,a.jsxs)(l.Zp,{className:"relative p-4 mb-4 scroll-mt-4",ref:s=>{d&&(d.current[e.id||`block-${t}`]=s)},id:e.id||`block-${t}`,children:["text"===e.type?(0,a.jsx)(G,{markdown:e.value||"",onChange:t=>N(e.id,t),placeholder:r||"Enter your content here...",className:"min-h-[200px]"}):"image"===e.type?(0,a.jsx)("div",{className:"space-y-2",children:e.value?(0,a.jsx)("div",{className:"relative w-full h-48 border rounded-md overflow-hidden",children:(0,a.jsx)(_.default,{src:e.value,alt:"Uploaded content",layout:"fill",objectFit:"contain",className:"rounded-md"})}):(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"text-sm text-muted-foreground mb-2",children:"Pilih cara menambahkan gambar:"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>{let t=document.createElement("input");t.type="file",t.accept="image/*",t.onchange=t=>{let s=t.target.files;s&&C(Array.from(s),e.id,"image")},t.click()},children:[(0,a.jsx)(A.A,{className:"h-4 w-4 mr-2"}),"Upload File"]}),(0,a.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>{let t=prompt("Masukkan URL gambar:");t&&N(e.id,t)},children:[(0,a.jsx)(Z.A,{className:"h-4 w-4 mr-2"}),"Dari Link"]})]})]})}):"video"===e.type?(0,a.jsx)("div",{className:"space-y-2",children:e.value?(0,a.jsx)("video",{controls:!0,src:e.value,className:"w-full h-auto max-h-96 rounded-md"}):(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"text-sm text-muted-foreground mb-2",children:"Pilih cara menambahkan video:"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>{let t=document.createElement("input");t.type="file",t.accept="video/*",t.onchange=t=>{let s=t.target.files;s&&C(Array.from(s),e.id,"video")},t.click()},children:[(0,a.jsx)(A.A,{className:"h-4 w-4 mr-2"}),"Upload File"]}),(0,a.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>{let t=prompt("Masukkan URL video:");t&&N(e.id,t)},children:[(0,a.jsx)(Z.A,{className:"h-4 w-4 mr-2"}),"Dari Link"]})]})]})}):"pdf"===e.type?(0,a.jsx)("div",{className:"space-y-2",children:e.value?(0,a.jsx)("iframe",{src:e.value,className:"w-full h-96 rounded-md"}):(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"text-sm text-muted-foreground mb-2",children:"Pilih cara menambahkan PDF:"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>{let t=document.createElement("input");t.type="file",t.accept="application/pdf",t.onchange=t=>{let s=t.target.files;s&&C(Array.from(s),e.id,"pdf")},t.click()},children:[(0,a.jsx)(A.A,{className:"h-4 w-4 mr-2"}),"Upload File"]}),(0,a.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>{let t=prompt("Masukkan URL PDF:");t&&N(e.id,t)},children:[(0,a.jsx)(Z.A,{className:"h-4 w-4 mr-2"}),"Dari Link"]})]})]})}):"zoom-recording"===e.type?(0,a.jsx)("div",{className:"space-y-2",children:e.value?(0,a.jsx)("iframe",{src:e.value,className:"w-full h-96 rounded-md"}):(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"text-sm text-muted-foreground mb-2",children:"Pilih cara menambahkan Zoom Recording:"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>{let t=document.createElement("input");t.type="file",t.accept="video/*",t.onchange=t=>{let s=t.target.files;s&&C(Array.from(s),e.id,"zoom-recording")},t.click()},children:[(0,a.jsx)(A.A,{className:"h-4 w-4 mr-2"}),"Upload File"]}),(0,a.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>{let t=prompt("Masukkan URL Zoom Recording:");t&&N(e.id,t)},children:[(0,a.jsx)(Z.A,{className:"h-4 w-4 mr-2"}),"Dari Link"]})]})]})}):(0,a.jsx)(y.T,{placeholder:`Enter ${e.type} URL`,value:e.value,onChange:t=>N(e.id,t.target.value),rows:3}),(0,a.jsx)(i.$,{variant:"ghost",size:"icon",className:"absolute top-2 right-2 text-muted-foreground hover:text-destructive",onClick:()=>k(e.id),children:(0,a.jsx)(M.A,{className:"h-4 w-4"})})]},e.id)),(0,a.jsxs)("div",{className:"flex flex-wrap gap-2 pt-2",children:[(0,a.jsxs)(i.$,{variant:"outline",onClick:()=>v("text"),size:"sm","data-sentry-element":"Button","data-sentry-source-file":"dynamic-content-editor.tsx",children:[(0,a.jsx)(O.A,{className:"h-4 w-4 mr-2","data-sentry-element":"TextIcon","data-sentry-source-file":"dynamic-content-editor.tsx"})," Add Text Block"]}),s&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(i.$,{variant:"outline",onClick:()=>b("image"),size:"sm",children:[(0,a.jsx)(H.A,{className:"h-4 w-4 mr-2"})," Add Image Block"]}),(0,a.jsxs)(i.$,{variant:"outline",onClick:()=>b("video"),size:"sm",children:[(0,a.jsx)(K.A,{className:"h-4 w-4 mr-2"})," Add Video Block"]}),(0,a.jsxs)(i.$,{variant:"outline",onClick:()=>b("pdf"),size:"sm",children:[(0,a.jsx)(U.A,{className:"h-4 w-4 mr-2"})," Add PDF Block"]}),(0,a.jsxs)(i.$,{variant:"outline",onClick:()=>b("zoom-recording"),size:"sm",children:[(0,a.jsx)(W.A,{className:"h-4 w-4 mr-2"})," Add Zoom Recording Block"]})]})]}),(0,a.jsx)(E.lG,{open:m,onOpenChange:u,"data-sentry-element":"Dialog","data-sentry-source-file":"dynamic-content-editor.tsx",children:(0,a.jsxs)(E.Cf,{"data-sentry-element":"DialogContent","data-sentry-source-file":"dynamic-content-editor.tsx",children:[(0,a.jsxs)(E.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"dynamic-content-editor.tsx",children:[(0,a.jsxs)(E.L3,{"data-sentry-element":"DialogTitle","data-sentry-source-file":"dynamic-content-editor.tsx",children:["Tambah ",x.charAt(0).toUpperCase()+x.slice(1)," Block"]}),(0,a.jsxs)(E.rr,{"data-sentry-element":"DialogDescription","data-sentry-source-file":"dynamic-content-editor.tsx",children:["Pilih cara menambahkan ",x,": upload file atau masukkan link."]})]}),(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(h.J,{htmlFor:"link-url","data-sentry-element":"Label","data-sentry-source-file":"dynamic-content-editor.tsx",children:["URL ",x.charAt(0).toUpperCase()+x.slice(1)]}),(0,a.jsx)(p.p,{id:"link-url",placeholder:`Masukkan URL ${x}...`,value:j,onChange:e=>g(e.target.value),"data-sentry-element":"Input","data-sentry-source-file":"dynamic-content-editor.tsx"})]})}),(0,a.jsxs)(E.Es,{className:"flex gap-2","data-sentry-element":"DialogFooter","data-sentry-source-file":"dynamic-content-editor.tsx",children:[(0,a.jsxs)(i.$,{variant:"outline",onClick:()=>{v(x),u(!1)},"data-sentry-element":"Button","data-sentry-source-file":"dynamic-content-editor.tsx",children:[(0,a.jsx)(A.A,{className:"h-4 w-4 mr-2","data-sentry-element":"Upload","data-sentry-source-file":"dynamic-content-editor.tsx"}),"Upload File"]}),(0,a.jsxs)(i.$,{onClick:()=>{if(j.trim()){let e=[...c,{id:`block-${Date.now()}-${Math.random().toString(36).substring(2,9)}`,type:x,value:j.trim()}];o(e),t(e),u(!1),g(""),S.oR.success("Block berhasil ditambahkan dari link!")}else S.oR.error("Silakan masukkan URL yang valid")},disabled:!j.trim(),"data-sentry-element":"Button","data-sentry-source-file":"dynamic-content-editor.tsx",children:[(0,a.jsx)(Z.A,{className:"h-4 w-4 mr-2","data-sentry-element":"Link","data-sentry-source-file":"dynamic-content-editor.tsx"}),"Gunakan Link"]})]})]})})]})}var X=s(28280),Y=s(65289),ee=s(86857),et=s(51897),es=s(36445),ea=s(80506),en=s(73562),er=s(40515),ei=s(78918);function el({data:e,onUpdate:t}){let[s,d]=(0,n.useState)(e.modules[0]?.id||""),[c,o]=(0,n.useState)(""),[m,u]=(0,n.useState)({type:"chapter",quiz:null}),[g,v]=(0,n.useState)(!1),[N,k]=(0,n.useState)(null),[C,w]=(0,n.useState)(!1),[A,D]=(0,n.useState)(!1),z=(0,n.useRef)({}),T=e.modules.find(e=>e.id===s),P=T?.chapters.find(e=>e.id===c),B=e=>{let t=z.current[e];t&&t.scrollIntoView({behavior:"smooth",block:"start",inline:"nearest"})},L=e=>{switch(e){case"text":return(0,a.jsx)(X.A,{className:"w-3 h-3"});case"image":return(0,a.jsx)(H.A,{className:"w-3 h-3"});case"video":case"zoom-recording":return(0,a.jsx)(W.A,{className:"w-3 h-3"});case"pdf":return(0,a.jsx)(U.A,{className:"w-3 h-3"});default:return(0,a.jsx)(Y.A,{className:"w-3 h-3"})}},Q=e=>"text"===e.type?e.value?.slice(0,30)+(e.value&&e.value.length>30?"...":"")||"Empty text":e.type.charAt(0).toUpperCase()+e.type.slice(1),J=t=>{let s={id:`quiz-${Date.now()}`,name:"chapter"===t?`Quiz ${P?.name}`:"module"===t?`Quiz ${T?.name}`:`Final Exam - ${e.name}`,description:"",questions:[],minimumScore:70,timeLimit:"final"===t?120:void 0};u({type:t,quiz:s}),v(!0)},Z=(e,t)=>{u({type:e,quiz:{...t}}),v(!0)},O=e=>{k({...e}),w(!0)},K=e=>{if(!m.quiz)return;let t=m.quiz.questions.filter(t=>t.id!==e).map((e,t)=>({...e,orderIndex:t}));u(e=>({...e,quiz:e.quiz?{...e.quiz,questions:t}:null})),S.oR.success("Pertanyaan berhasil dihapus")},_=(()=>{let t=e.modules.reduce((e,t)=>e+t.chapters.length,0),s=e.modules.reduce((e,t)=>e+t.chapters.filter(e=>e.content&&e.content.length>0).length,0);return{total:t,completed:s,percentage:t>0?Math.round(s/t*100):0}})();return 0===e.modules.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(I.A,{className:"w-12 h-12 text-muted-foreground mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Belum ada modul"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Kembali ke langkah sebelumnya untuk membuat struktur modul terlebih dahulu"})]}):(0,a.jsxs)("div",{className:"space-y-6","data-sentry-component":"ContentCreationStep","data-sentry-source-file":"content-creation-step.tsx",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Pembuatan Konten"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Tambahkan konten dan quiz untuk setiap chapter"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"text-sm font-medium",children:[_.completed," / ",_.total," Chapter"]}),(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:[_.percentage,"% selesai"]})]}),(0,a.jsx)("div",{className:(0,x.cn)("w-12 h-12 rounded-full flex items-center justify-center",100===_.percentage?"bg-green-100 text-green-600":"bg-muted text-muted-foreground"),children:100===_.percentage?(0,a.jsx)(ee.A,{className:"w-6 h-6"}):(0,a.jsx)(et.A,{className:"w-6 h-6"})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[(0,a.jsx)("div",{className:"lg:col-span-1",children:(0,a.jsxs)(l.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"content-creation-step.tsx",children:[(0,a.jsx)(l.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"content-creation-step.tsx",children:(0,a.jsx)(l.ZB,{className:"text-base","data-sentry-element":"CardTitle","data-sentry-source-file":"content-creation-step.tsx",children:"Navigasi Konten"})}),(0,a.jsxs)(l.Wu,{className:"space-y-4 max-h-[70vh] overflow-y-auto","data-sentry-element":"CardContent","data-sentry-source-file":"content-creation-step.tsx",children:[(0,a.jsx)("div",{className:"space-y-2",children:(0,a.jsxs)("div",{className:"p-3 rounded-lg border-2 border-dashed border-primary/30 bg-primary/5",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-sm text-primary",children:"Final Exam"}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"Ujian akhir untuk seluruh course"})]}),e.finalExam&&(0,a.jsx)(ee.A,{className:"w-4 h-4 text-green-600"})]}),(0,a.jsxs)(i.$,{variant:e.finalExam?"outline":"default",size:"sm",className:"w-full",onClick:()=>{e.finalExam?Z("final",e.finalExam):J("final")},"data-sentry-element":"Button","data-sentry-source-file":"content-creation-step.tsx",children:[(0,a.jsx)(F.A,{className:"w-4 h-4 mr-2","data-sentry-element":"HelpCircle","data-sentry-source-file":"content-creation-step.tsx"}),e.finalExam?"Edit Final Exam":"Buat Final Exam"]})]})}),e.modules.map(e=>(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:(0,x.cn)("p-2 rounded-lg cursor-pointer transition-colors",s===e.id?"bg-primary text-primary-foreground":"bg-muted hover:bg-muted/80"),onClick:()=>{d(e.id),o("")},children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-sm",children:e.name}),(0,a.jsxs)("div",{className:"text-xs opacity-75",children:[e.chapters.length," chapters"]})]}),e.moduleQuiz&&(0,a.jsxs)(j.E,{variant:"secondary",className:"text-xs",children:[(0,a.jsx)(F.A,{className:"w-3 h-3 mr-1"}),"Quiz"]})]})}),s===e.id&&(0,a.jsxs)("div",{className:"ml-4 space-y-2",children:[(0,a.jsx)("div",{className:"p-2 rounded bg-secondary/50",children:(0,a.jsxs)(i.$,{variant:e.moduleQuiz?"outline":"secondary",size:"sm",className:"w-full text-xs",onClick:()=>{e.moduleQuiz?Z("module",e.moduleQuiz):J("module")},children:[(0,a.jsx)(F.A,{className:"w-3 h-3 mr-1"}),e.moduleQuiz?"Edit Module Quiz":"Buat Module Quiz"]})}),e.chapters.map(e=>{let t=e.content&&e.content.length>0;return(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:(0,x.cn)("p-2 rounded text-xs cursor-pointer transition-colors flex items-center justify-between",c===e.id?"bg-primary/20 text-primary":"hover:bg-muted/50"),onClick:()=>o(e.id),children:[(0,a.jsx)("span",{children:e.name}),t&&(0,a.jsx)(ee.A,{className:"w-3 h-3 text-green-600"})]}),c===e.id&&t&&e.content&&(0,a.jsxs)("div",{className:"ml-4 space-y-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1 text-xs text-muted-foreground mb-1",children:[(0,a.jsx)(es.A,{className:"w-3 h-3"}),(0,a.jsx)("span",{children:"Content Blocks"})]}),e.content.map((e,t)=>(0,a.jsxs)("button",{onClick:()=>B(e.id||`block-${t}`),className:"w-full text-left p-1.5 rounded text-xs hover:bg-primary/10 transition-colors flex items-center space-x-2",children:[L(e.type),(0,a.jsxs)("span",{className:"truncate flex-1",children:[t+1,". ",Q(e)]})]},e.id||t))]})]},e.id)})]})]},e.id))]})]})}),(0,a.jsx)("div",{className:"lg:col-span-3",children:c?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(l.Zp,{children:(0,a.jsx)(l.aR,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)(l.ZB,{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{children:P?.name}),P?.hasChapterQuiz&&(0,a.jsxs)(j.E,{variant:"secondary",children:[(0,a.jsx)(F.A,{className:"w-3 h-3 mr-1"}),"Chapter Quiz"]}),T?.moduleQuiz&&(0,a.jsxs)(j.E,{variant:"outline",children:[(0,a.jsx)(F.A,{className:"w-3 h-3 mr-1"}),"Module Quiz"]})]}),(0,a.jsxs)(l.BT,{children:["Modul: ",T?.name,T?.moduleQuiz&&(0,a.jsx)("span",{className:"ml-2 text-xs text-primary",children:"• Module ini memiliki quiz"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>D(!A),children:[(0,a.jsx)(ea.A,{className:"w-4 h-4 mr-2"}),A?"Edit":"Preview"]}),T?.moduleQuiz&&(0,a.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>Z("module",T.moduleQuiz),children:[(0,a.jsx)(F.A,{className:"w-4 h-4 mr-2"}),"Edit Module Quiz"]}),P?.hasChapterQuiz&&(0,a.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>{P.chapterQuiz?Z("chapter",P.chapterQuiz):J("chapter")},children:[(0,a.jsx)(F.A,{className:"w-4 h-4 mr-2"}),P.chapterQuiz?"Edit Chapter Quiz":"Buat Chapter Quiz"]})]})]})})}),(0,a.jsxs)(l.Zp,{children:[(0,a.jsxs)(l.aR,{children:[(0,a.jsx)(l.ZB,{className:"text-base",children:"Konten Chapter"}),(0,a.jsx)(l.BT,{children:A?"Preview konten seperti yang akan dilihat siswa":"Gunakan Markdown untuk memformat konten"})]}),(0,a.jsx)(l.Wu,{children:A?(0,a.jsx)("div",{className:"max-h-[60vh] overflow-y-auto prose max-w-none pr-4",children:P?.content&&P.content.length>0?(0,a.jsx)(a.Fragment,{children:P.content.map((e,t)=>(0,a.jsxs)("div",{ref:s=>{z.current[e.id||`block-${t}`]=s},className:"mb-6 scroll-mt-4",id:e.id||`block-${t}`,children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2 py-1 px-2 bg-muted/30 rounded text-xs text-muted-foreground",children:[L(e.type),(0,a.jsxs)("span",{children:["Content-",t+1]}),(0,a.jsxs)("span",{children:["(",e.type,")"]})]}),"text"===e.type?(0,a.jsx)(er.oz,{remarkPlugins:[ei.A],components:{h1:({node:e,...t})=>(0,a.jsx)("h1",{className:"mb-4 text-2xl font-bold text-gray-900",...t}),h2:({node:e,...t})=>(0,a.jsx)("h2",{className:"mb-3 text-xl font-semibold text-gray-800",...t}),h3:({node:e,...t})=>(0,a.jsx)("h3",{className:"mb-2 text-lg font-semibold text-gray-800",...t}),h4:({node:e,...t})=>(0,a.jsx)("h4",{className:"mb-2 text-base font-semibold text-gray-700",...t}),p:({node:e,...t})=>(0,a.jsx)("p",{className:"mb-3 leading-relaxed",...t}),ul:({node:e,...t})=>(0,a.jsx)("ul",{className:"mb-3 ml-4 list-disc",...t}),ol:({node:e,...t})=>(0,a.jsx)("ol",{className:"mb-3 ml-4 list-decimal",...t}),li:({node:e,...t})=>(0,a.jsx)("li",{className:"mb-1",...t}),blockquote:({node:e,...t})=>(0,a.jsx)("blockquote",{className:"mb-4 rounded-r border-l-4 border-blue-200 bg-blue-50 py-2 pl-4 italic",...t}),code:({node:e,className:t,children:s,...n})=>/language-(\w+)/.exec(t||"")?(0,a.jsx)("code",{className:"block overflow-x-auto rounded bg-gray-900 p-4 font-mono text-sm text-gray-100",...n,children:s}):(0,a.jsx)("code",{className:"rounded bg-gray-100 px-1 py-0.5 font-mono text-sm",...n,children:s}),pre:({node:e,...t})=>(0,a.jsx)("pre",{className:"mb-4",...t}),table:({node:e,...t})=>(0,a.jsx)("div",{className:"mb-4 overflow-x-auto",children:(0,a.jsx)("table",{className:"min-w-full rounded border border-gray-200",...t})}),thead:({node:e,...t})=>(0,a.jsx)("thead",{className:"bg-gray-50",...t}),th:({node:e,...t})=>(0,a.jsx)("th",{className:"border border-gray-200 px-3 py-2 text-left font-semibold",...t}),td:({node:e,...t})=>(0,a.jsx)("td",{className:"border border-gray-200 px-3 py-2",...t}),hr:({node:e,...t})=>(0,a.jsx)("hr",{className:"my-6 border-gray-300",...t}),strong:({node:e,...t})=>(0,a.jsx)("strong",{className:"font-semibold text-gray-900",...t}),em:({node:e,...t})=>(0,a.jsx)("em",{className:"italic",...t})},children:e.value}):"image"===e.type?(0,a.jsx)("div",{className:"my-4",children:(0,a.jsx)("img",{src:e.value,alt:"Content",className:"max-w-full h-auto rounded-md"})}):"video"===e.type?(0,a.jsx)("div",{className:"my-4",children:(0,a.jsx)("video",{src:e.value,controls:!0,className:"max-w-full rounded-md"})}):"pdf"===e.type?(0,a.jsx)("div",{className:"my-4",children:(0,a.jsx)("iframe",{src:e.value,className:"w-full h-96 rounded-md",title:"PDF Content"})}):(0,a.jsx)("div",{className:"my-4 p-4 bg-muted rounded-md",children:(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["zoom-recording"===e.type?"Zoom Recording: ":"File: ",(0,a.jsx)("a",{href:e.value,target:"_blank",rel:"noopener noreferrer",className:"text-primary hover:underline",children:e.value})]})})]},e.id||t))}):(0,a.jsx)("p",{className:"text-muted-foreground italic",children:"Belum ada konten untuk chapter ini"})}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"max-h-[60vh] overflow-y-auto pr-4",children:(0,a.jsx)(V,{initialContent:P?.content||[],onContentChange:a=>{T&&P&&t({modules:e.modules.map(e=>{if(e.id===s){let t=e.chapters.map(e=>e.id===c?{...e,content:a}:e);return{...e,chapters:t}}return e})})},contentRefs:z})}),(0,a.jsxs)("div",{className:"flex justify-between items-center text-sm text-muted-foreground",children:[(0,a.jsx)("span",{children:"Mendukung Markdown formatting"}),(0,a.jsxs)("span",{children:[P?.content?.length||0," blok konten"]})]})]})})]})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(l.Zp,{children:[(0,a.jsxs)(l.aR,{children:[(0,a.jsxs)(l.ZB,{className:"flex items-center space-x-2",children:[(0,a.jsx)(F.A,{className:"w-5 h-5 text-primary"}),(0,a.jsx)("span",{children:"Final Exam"}),e.finalExam&&(0,a.jsx)(j.E,{variant:"secondary",children:"Sudah dibuat"})]}),(0,a.jsx)(l.BT,{children:"Ujian akhir untuk menguji pemahaman siswa terhadap seluruh materi course"})]}),(0,a.jsx)(l.Wu,{children:e.finalExam?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center p-4 bg-muted rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-primary",children:e.finalExam.questions.length}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Pertanyaan"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-muted rounded-lg",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-primary",children:[e.finalExam.minimumScore,"%"]}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Nilai Minimum"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-muted rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-primary",children:e.finalExam.questions.reduce((e,t)=>e+t.points,0)}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Total Poin"})]}),e.finalExam.timeLimit&&(0,a.jsxs)("div",{className:"text-center p-4 bg-muted rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-primary",children:e.finalExam.timeLimit}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Menit"})]})]}),(0,a.jsx)("div",{className:"flex space-x-2",children:(0,a.jsxs)(i.$,{onClick:()=>Z("final",e.finalExam),className:"flex-1",children:[(0,a.jsx)(R.A,{className:"w-4 h-4 mr-2"}),"Edit Final Exam"]})})]}):(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(F.A,{className:"w-12 h-12 text-muted-foreground mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Belum ada Final Exam"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-4",children:"Final Exam adalah ujian akhir yang menguji pemahaman siswa terhadap seluruh materi course"}),(0,a.jsxs)(i.$,{onClick:()=>J("final"),children:[(0,a.jsx)($.A,{className:"w-4 h-4 mr-2"}),"Buat Final Exam"]})]})})]}),(0,a.jsxs)(l.Zp,{children:[(0,a.jsxs)(l.aR,{children:[(0,a.jsx)(l.ZB,{children:"Overview Modul"}),(0,a.jsx)(l.BT,{children:"Pilih chapter dari navigasi di sebelah kiri untuk mulai menambahkan konten"})]}),(0,a.jsx)(l.Wu,{children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:e.modules.map(e=>(0,a.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("h4",{className:"font-medium",children:e.name}),e.moduleQuiz&&(0,a.jsxs)(j.E,{variant:"secondary",children:[(0,a.jsx)(F.A,{className:"w-3 h-3 mr-1"}),"Quiz"]})]}),(0,a.jsxs)("div",{className:"text-sm text-muted-foreground mb-3",children:[e.chapters.length," chapters"]}),(0,a.jsx)("div",{className:"space-y-1",children:e.chapters.map(e=>{let t=e.content&&e.content.length>0;return(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs",children:[(0,a.jsx)("span",{children:e.name}),t?(0,a.jsx)(ee.A,{className:"w-3 h-3 text-green-600"}):(0,a.jsx)(et.A,{className:"w-3 h-3 text-muted-foreground"})]},e.id)})})]},e.id))})})]})]})})]}),(0,a.jsx)(E.lG,{open:g,onOpenChange:v,"data-sentry-element":"Dialog","data-sentry-source-file":"content-creation-step.tsx",children:(0,a.jsxs)(E.Cf,{className:"sm:max-w-4xl max-h-[80vh] overflow-y-auto p-6","data-sentry-element":"DialogContent","data-sentry-source-file":"content-creation-step.tsx",children:[(0,a.jsxs)(E.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"content-creation-step.tsx",children:[(0,a.jsx)(E.L3,{"data-sentry-element":"DialogTitle","data-sentry-source-file":"content-creation-step.tsx",children:m.quiz?.questions.length?"Edit Quiz":"Buat Quiz Baru"}),(0,a.jsx)(E.rr,{"data-sentry-element":"DialogDescription","data-sentry-source-file":"content-creation-step.tsx",children:"Buat pertanyaan untuk menguji pemahaman siswa"})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(h.J,{htmlFor:"quizName","data-sentry-element":"Label","data-sentry-source-file":"content-creation-step.tsx",children:"Nama Quiz *"}),(0,a.jsx)(p.p,{id:"quizName",placeholder:"Masukkan nama quiz",value:m.quiz?.name||"",onChange:e=>u(t=>({...t,quiz:t.quiz?{...t.quiz,name:e.target.value}:null})),"data-sentry-element":"Input","data-sentry-source-file":"content-creation-step.tsx"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(h.J,{htmlFor:"minimumScore","data-sentry-element":"Label","data-sentry-source-file":"content-creation-step.tsx",children:"Nilai Minimum (%)"}),(0,a.jsx)(p.p,{id:"minimumScore",type:"number",min:"0",max:"100",value:m.quiz?.minimumScore||70,onChange:e=>u(t=>({...t,quiz:t.quiz?{...t.quiz,minimumScore:parseInt(e.target.value)}:null})),"data-sentry-element":"Input","data-sentry-source-file":"content-creation-step.tsx"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(h.J,{htmlFor:"timeLimit","data-sentry-element":"Label","data-sentry-source-file":"content-creation-step.tsx",children:"Batas Waktu (menit)"}),(0,a.jsx)(p.p,{id:"timeLimit",type:"number",min:"1",value:m.quiz?.timeLimit||"",onChange:e=>u(t=>({...t,quiz:t.quiz?{...t.quiz,timeLimit:e.target.value?parseInt(e.target.value):void 0}:null})),placeholder:"Tanpa batas waktu","data-sentry-element":"Input","data-sentry-source-file":"content-creation-step.tsx"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(h.J,{htmlFor:"quizDescription","data-sentry-element":"Label","data-sentry-source-file":"content-creation-step.tsx",children:"Deskripsi"}),(0,a.jsx)(y.T,{id:"quizDescription",placeholder:"Jelaskan tentang quiz ini...",value:m.quiz?.description||"",onChange:e=>u(t=>({...t,quiz:t.quiz?{...t.quiz,description:e.target.value}:null})),rows:2,"data-sentry-element":"Textarea","data-sentry-source-file":"content-creation-step.tsx"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold",children:"Pertanyaan"}),(0,a.jsxs)(i.$,{onClick:()=>{k({id:N?.id||`question-${Date.now()}`,type:"multiple_choice",question:[{type:"text",value:""}],options:N?.type==="true_false"?[{content:[{type:"text",value:"True"}],isCorrect:!1},{content:[{type:"text",value:"False"}],isCorrect:!1}]:[{content:[{type:"text",value:""}],isCorrect:!1},{content:[{type:"text",value:""}],isCorrect:!1},{content:[{type:"text",value:""}],isCorrect:!1},{content:[{type:"text",value:""}],isCorrect:!1}],essayAnswer:"",explanation:[],points:1,orderIndex:m.quiz?.questions.length||0}),w(!0)},"data-sentry-element":"Button","data-sentry-source-file":"content-creation-step.tsx",children:[(0,a.jsx)($.A,{className:"w-4 h-4 mr-2","data-sentry-element":"Plus","data-sentry-source-file":"content-creation-step.tsx"}),"Tambah Pertanyaan"]})]}),m.quiz?.questions.length===0?(0,a.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,a.jsx)(F.A,{className:"w-8 h-8 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-sm",children:"Belum ada pertanyaan"})]}):(0,a.jsx)("div",{className:"space-y-3",children:m.quiz?.questions.map((e,t)=>(0,a.jsx)(l.Zp,{children:(0,a.jsx)(l.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)(j.E,{variant:"outline",children:t+1}),(0,a.jsx)(j.E,{variant:"secondary",children:"multiple_choice"===e.type?"Pilihan Ganda":"true_false"===e.type?"Benar/Salah":"Essay"}),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:[e.points," poin"]})]}),(0,a.jsx)("div",{className:"text-sm",children:e.question.map((e,t)=>(0,a.jsxs)(r().Fragment,{children:["text"===e.type&&(0,a.jsx)("p",{children:e.value}),"image"===e.type&&e.value&&(0,a.jsx)("img",{src:e.value,alt:`Question image ${t}`,className:"max-w-xs max-h-32 object-contain mt-2"})]},t))}),"multiple_choice"===e.type&&e.options&&(0,a.jsx)("div",{className:"mt-2 space-y-1",children:e.options.map((e,t)=>(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:[String.fromCharCode(65+t),".",e.content.map((e,t)=>(0,a.jsxs)(r().Fragment,{children:["text"===e.type&&(0,a.jsx)("span",{children:e.value}),"image"===e.type&&e.value&&(0,a.jsx)("img",{src:e.value,alt:`Option image ${t}`,className:"inline-block max-h-8 object-contain ml-1"})]},t))]},t))})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>O(e),children:(0,a.jsx)(R.A,{className:"w-4 h-4"})}),(0,a.jsxs)(q.Lt,{children:[(0,a.jsx)(q.tv,{asChild:!0,children:(0,a.jsx)(i.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(M.A,{className:"w-4 h-4"})})}),(0,a.jsxs)(q.EO,{children:[(0,a.jsxs)(q.wd,{children:[(0,a.jsx)(q.r7,{children:"Hapus Pertanyaan"}),(0,a.jsx)(q.$v,{children:"Apakah Anda yakin ingin menghapus pertanyaan ini?"})]}),(0,a.jsxs)(q.ck,{children:[(0,a.jsx)(q.Zr,{children:"Batal"}),(0,a.jsx)(q.Rx,{onClick:()=>K(e.id),children:"Hapus"})]})]})]})]})]})})},e.id))})]})]}),(0,a.jsxs)(E.Es,{"data-sentry-element":"DialogFooter","data-sentry-source-file":"content-creation-step.tsx",children:[(0,a.jsx)(i.$,{variant:"outline",onClick:()=>v(!1),"data-sentry-element":"Button","data-sentry-source-file":"content-creation-step.tsx",children:"Batal"}),(0,a.jsxs)(i.$,{onClick:()=>{if(!m.quiz||!m.quiz.name.trim())return void S.oR.error("Nama quiz harus diisi");"final"===m.type?t({finalExam:m.quiz}):t({modules:e.modules.map(e=>{if(e.id===s)if("module"===m.type)return{...e,moduleQuiz:m.quiz};else{let t=e.chapters.map(e=>e.id===c?{...e,chapterQuiz:m.quiz}:e);return{...e,chapters:t}}return e})}),v(!1),u({type:"chapter",quiz:null}),S.oR.success("Quiz berhasil disimpan")},"data-sentry-element":"Button","data-sentry-source-file":"content-creation-step.tsx",children:[(0,a.jsx)(en.A,{className:"w-4 h-4 mr-2","data-sentry-element":"Save","data-sentry-source-file":"content-creation-step.tsx"}),"Simpan Quiz"]})]})]})}),(0,a.jsx)(E.lG,{open:C,onOpenChange:w,"data-sentry-element":"Dialog","data-sentry-source-file":"content-creation-step.tsx",children:(0,a.jsxs)(E.Cf,{className:"sm:max-w-2xl max-h-[80vh] overflow-y-auto p-6","data-sentry-element":"DialogContent","data-sentry-source-file":"content-creation-step.tsx",children:[(0,a.jsx)(E.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"content-creation-step.tsx",children:(0,a.jsx)(E.L3,{"data-sentry-element":"DialogTitle","data-sentry-source-file":"content-creation-step.tsx",children:N?.question?"Edit Pertanyaan":"Tambah Pertanyaan Baru"})}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(h.J,{htmlFor:"questionType","data-sentry-element":"Label","data-sentry-source-file":"content-creation-step.tsx",children:"Tipe Pertanyaan"}),(0,a.jsxs)(f.l6,{value:N?.type||"multiple_choice",onValueChange:e=>{k(t=>{if(!t)return null;let s={...t,type:e};return"true_false"===e?s.options=[{content:[{type:"text",value:"True"}],isCorrect:!1},{content:[{type:"text",value:"False"}],isCorrect:!1}]:"multiple_choice"===e?s.options=[{content:[{type:"text",value:""}],isCorrect:!1},{content:[{type:"text",value:""}],isCorrect:!1},{content:[{type:"text",value:""}],isCorrect:!1},{content:[{type:"text",value:""}],isCorrect:!1}]:s.options=void 0,s})},"data-sentry-element":"Select","data-sentry-source-file":"content-creation-step.tsx",children:[(0,a.jsx)(f.bq,{"data-sentry-element":"SelectTrigger","data-sentry-source-file":"content-creation-step.tsx",children:(0,a.jsx)(f.yv,{"data-sentry-element":"SelectValue","data-sentry-source-file":"content-creation-step.tsx"})}),(0,a.jsxs)(f.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"content-creation-step.tsx",children:[(0,a.jsx)(f.eb,{value:"multiple_choice","data-sentry-element":"SelectItem","data-sentry-source-file":"content-creation-step.tsx",children:"Pilihan Ganda"}),(0,a.jsx)(f.eb,{value:"true_false","data-sentry-element":"SelectItem","data-sentry-source-file":"content-creation-step.tsx",children:"Benar/Salah"}),(0,a.jsx)(f.eb,{value:"essay","data-sentry-element":"SelectItem","data-sentry-source-file":"content-creation-step.tsx",children:"Essay"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(h.J,{htmlFor:"questionPoints","data-sentry-element":"Label","data-sentry-source-file":"content-creation-step.tsx",children:"Poin"}),(0,a.jsx)(p.p,{id:"questionPoints",type:"number",min:"1",value:N?.points||1,onChange:e=>k(t=>t?{...t,points:parseInt(e.target.value)}:null),"data-sentry-element":"Input","data-sentry-source-file":"content-creation-step.tsx"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(h.J,{htmlFor:"questionText","data-sentry-element":"Label","data-sentry-source-file":"content-creation-step.tsx",children:"Pertanyaan *"}),(0,a.jsx)(V,{initialContent:N?.question||[],onContentChange:e=>k(t=>t?{...t,question:e}:null),allowImages:!0,"data-sentry-element":"DynamicContentEditor","data-sentry-source-file":"content-creation-step.tsx"})]}),(N?.type==="multiple_choice"||N?.type==="true_false")&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(h.J,{children:"Pilihan Jawaban"}),N.options?.map((e,t)=>(0,a.jsxs)("div",{className:"flex flex-col space-y-2 border p-3 rounded-md",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:["multiple_choice"===N.type&&(0,a.jsxs)("span",{className:"text-sm font-medium w-6",children:[String.fromCharCode(65+t),"."]}),"multiple_choice"===N.type?(0,a.jsx)(V,{initialContent:e.content||[],onContentChange:e=>{let s=[...N.options||[]];s[t]={...s[t],content:e},k(e=>e?{...e,options:s}:null)},allowImages:!0}):(0,a.jsx)("span",{className:"text-base font-medium",children:e.content[0].value})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-2",children:[(0,a.jsx)(b.S,{id:`option-correct-${t}`,checked:e.isCorrect,onCheckedChange:e=>{let s=[...N.options||[]];s[t]={...s[t],isCorrect:e},k(e=>e?{...e,options:s}:null)}}),(0,a.jsx)(h.J,{htmlFor:`option-correct-${t}`,children:"Jawaban Benar"})]})]},t))]}),N&&"essay"===N.type&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(h.J,{htmlFor:"essay-answer",children:"Jawaban Esai"}),(0,a.jsx)(y.T,{id:"essay-answer",placeholder:"Masukkan jawaban esai untuk pertanyaan ini",value:N.essayAnswer||"",onChange:e=>k(t=>t?{...t,essayAnswer:e.target.value}:null),rows:4})]}),N&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(h.J,{htmlFor:"explanation",children:"Penjelasan Jawaban (Opsional)"}),(0,a.jsx)(V,{initialContent:N?.explanation||[],onContentChange:e=>{k(t=>t?{...t,explanation:e}:null)},placeholder:"Jelaskan jawaban yang benar atau berikan informasi tambahan",allowImages:!0})]})]}),(0,a.jsxs)(E.Es,{"data-sentry-element":"DialogFooter","data-sentry-source-file":"content-creation-step.tsx",children:[(0,a.jsx)(i.$,{variant:"outline",onClick:()=>w(!1),"data-sentry-element":"Button","data-sentry-source-file":"content-creation-step.tsx",children:"Batal"}),(0,a.jsxs)(i.$,{onClick:()=>{if(!N||0===N.question.length||"text"===N.question[0].type&&!N.question[0].value.trim())return void S.oR.error("Pertanyaan harus diisi");if(!m.quiz)return;let e=[...m.quiz.questions],t=e.findIndex(e=>e.id===N.id);t>=0?e[t]=N:e.push(N),u(t=>({...t,quiz:t.quiz?{...t.quiz,questions:e}:null})),w(!1),k(null),S.oR.success("Pertanyaan berhasil disimpan")},"data-sentry-element":"Button","data-sentry-source-file":"content-creation-step.tsx",children:[N?.question?"Perbarui":"Tambah"," Pertanyaan"]})]})]})})]})}var ed=s(8934),ec=s(15019),eo=s(14908),em=s(96249),eu=s(6410);function ex({data:e,onPublish:t,isPublishing:s}){let[r,o]=(0,n.useState)(!1),m=(()=>{let t=[];t.push({id:"course-name",label:"Nama Course",status:e.name.trim()?"complete":"incomplete",description:e.name.trim()?`"${e.name}"`:"Nama course harus diisi",required:!0}),t.push({id:"course-description",label:"Deskripsi Course",status:e.description.trim()?"complete":"incomplete",description:e.description.trim()?`${e.description.length} karakter`:"Deskripsi course harus diisi",required:!0}),t.push({id:"course-code",label:"Kode Course",status:e.courseCode.trim()?"complete":"incomplete",description:e.courseCode.trim()?e.courseCode:"Kode course harus diisi",required:!0}),t.push({id:"cover-image",label:"Cover Image",status:e.coverImage?"complete":"warning",description:e.coverImage?"Cover image telah diupload":"Disarankan menambahkan cover image",required:!1}),t.push({id:"course-dates",label:"Tanggal Course",status:e.startDate&&e.endDate?"complete":"warning",description:e.startDate&&e.endDate?`${new Date(e.startDate).toLocaleDateString()} - ${new Date(e.endDate).toLocaleDateString()}`:"Tanggal mulai dan selesai belum diatur",required:!1});let s=e.modules.length;t.push({id:"modules",label:"Struktur Modul",status:s>0?"complete":"incomplete",description:s>0?`${s} modul telah dibuat`:"Minimal 1 modul harus dibuat",required:!0});let a=e.modules.reduce((e,t)=>e+t.chapters.length,0);t.push({id:"chapters",label:"Chapter",status:a>0?"complete":"incomplete",description:a>0?`${a} chapter telah dibuat`:"Minimal 1 chapter harus dibuat",required:!0});let n=e.modules.reduce((e,t)=>e+t.chapters.filter(e=>e.content&&e.content.length>0).length,0);t.push({id:"content",label:"Konten Chapter",status:n===a?"complete":n>0?"warning":"incomplete",description:`${n} dari ${a} chapter memiliki konten`,required:!0});let r=e.modules.reduce((e,t)=>e+t.chapters.filter(e=>e.hasChapterQuiz&&e.chapterQuiz).length,0),i=e.modules.filter(e=>e.hasModuleQuiz&&e.moduleQuiz).length;return t.push({id:"quizzes",label:"Quiz",status:r>0||i>0?"complete":"warning",description:`${r} chapter quiz, ${i} module quiz`,required:!1}),t.push({id:"final-exam",label:"Final Exam",status:e.finalExam?"complete":"warning",description:e.finalExam?`${e.finalExam.questions.length} pertanyaan`:"Final exam belum dibuat",required:!1}),t})(),u=m.filter(e=>e.required),p=u.filter(e=>"complete"===e.status).length,h=p===u.length,y=m.filter(e=>"complete"===e.status).length,f=Math.round(y/m.length*100),g=(()=>{let t=e.modules.reduce((e,t)=>e+t.chapters.length,0),s=e.modules.reduce((e,t)=>e+t.chapters.filter(e=>e.hasChapterQuiz).length+ +!!t.hasModuleQuiz,0)+ +!!e.finalExam,a=e.modules.reduce((e,t)=>e+t.chapters.reduce((e,t)=>e+5*Math.ceil(t.content.filter(e=>"text"===e.type).reduce((e,t)=>e+t.value.length,0)/1e3),0),0);return{modules:e.modules.length,chapters:t,quizzes:s,estimatedDuration:Math.max(a,30)}})(),v=async()=>{if(!h)return void S.oR.error("Lengkapi semua item yang wajib diisi terlebih dahulu");try{await t(),S.oR.success("Course berhasil dipublikasi!")}catch(e){S.oR.error("Gagal mempublikasi course")}};return(0,a.jsxs)("div",{className:"space-y-6","data-sentry-component":"PublishingStep","data-sentry-source-file":"publishing-step.tsx",children:[(0,a.jsxs)("div",{className:"text-center space-y-2",children:[(0,a.jsx)("div",{className:(0,x.cn)("w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4",h?"bg-green-100 text-green-600":"bg-orange-100 text-orange-600"),children:h?(0,a.jsx)(ec.A,{className:"w-8 h-8"}):(0,a.jsx)(eo.A,{className:"w-8 h-8"})}),(0,a.jsx)("h3",{className:"text-2xl font-bold",children:h?"Siap untuk Dipublikasi!":"Hampir Selesai"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:h?"Course Anda sudah siap untuk dipublikasi dan dapat diakses oleh siswa":"Lengkapi beberapa item berikut untuk mempublikasi course"})]}),(0,a.jsxs)(l.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"publishing-step.tsx",children:[(0,a.jsx)(l.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"publishing-step.tsx",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)(l.ZB,{className:"flex items-center space-x-2","data-sentry-element":"CardTitle","data-sentry-source-file":"publishing-step.tsx",children:[(0,a.jsx)(em.A,{className:"w-5 h-5","data-sentry-element":"Target","data-sentry-source-file":"publishing-step.tsx"}),(0,a.jsx)("span",{children:"Progress Kelengkapan"})]}),(0,a.jsxs)(l.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"publishing-step.tsx",children:[y," dari ",m.length," item selesai"]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold",children:[f,"%"]}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Selesai"})]})]})}),(0,a.jsxs)(l.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"publishing-step.tsx",children:[(0,a.jsx)(d.k,{value:f,className:"mb-4","data-sentry-element":"Progress","data-sentry-source-file":"publishing-step.tsx"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-10 h-10 bg-blue-100 text-blue-600 rounded-full mx-auto mb-2",children:(0,a.jsx)(I.A,{className:"w-5 h-5","data-sentry-element":"BookOpen","data-sentry-source-file":"publishing-step.tsx"})}),(0,a.jsx)("div",{className:"text-sm font-medium",children:g.modules}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"Modul"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-10 h-10 bg-green-100 text-green-600 rounded-full mx-auto mb-2",children:(0,a.jsx)(U.A,{className:"w-5 h-5","data-sentry-element":"FileText","data-sentry-source-file":"publishing-step.tsx"})}),(0,a.jsx)("div",{className:"text-sm font-medium",children:g.chapters}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"Chapter"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-10 h-10 bg-purple-100 text-purple-600 rounded-full mx-auto mb-2",children:(0,a.jsx)(F.A,{className:"w-5 h-5","data-sentry-element":"HelpCircle","data-sentry-source-file":"publishing-step.tsx"})}),(0,a.jsx)("div",{className:"text-sm font-medium",children:g.quizzes}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"Quiz"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-10 h-10 bg-orange-100 text-orange-600 rounded-full mx-auto mb-2",children:(0,a.jsx)(et.A,{className:"w-5 h-5","data-sentry-element":"Clock","data-sentry-source-file":"publishing-step.tsx"})}),(0,a.jsx)("div",{className:"text-sm font-medium",children:g.estimatedDuration}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"Menit"})]})]})]})]}),(0,a.jsxs)(l.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"publishing-step.tsx",children:[(0,a.jsx)(l.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"publishing-step.tsx",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(l.ZB,{className:"flex items-center space-x-2","data-sentry-element":"CardTitle","data-sentry-source-file":"publishing-step.tsx",children:[(0,a.jsx)(ee.A,{className:"w-5 h-5","data-sentry-element":"CheckCircle","data-sentry-source-file":"publishing-step.tsx"}),(0,a.jsx)("span",{children:"Checklist Publikasi"})]}),(0,a.jsxs)(i.$,{variant:"ghost",size:"sm",onClick:()=>o(!r),"data-sentry-element":"Button","data-sentry-source-file":"publishing-step.tsx",children:[r?"Sembunyikan":"Lihat"," Detail"]})]})}),(0,a.jsx)(l.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"publishing-step.tsx",children:(0,a.jsx)("div",{className:"space-y-3",children:m.map(e=>(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:(0,x.cn)("w-5 h-5 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5","complete"===e.status?"bg-green-100 text-green-600":"warning"===e.status?"bg-orange-100 text-orange-600":"bg-gray-100 text-gray-400"),children:"complete"===e.status?(0,a.jsx)(ee.A,{className:"w-3 h-3"}):"warning"===e.status?(0,a.jsx)(eo.A,{className:"w-3 h-3"}):(0,a.jsx)("div",{className:"w-2 h-2 bg-current rounded-full"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:(0,x.cn)("text-sm font-medium","complete"===e.status?"text-green-700":"warning"===e.status?"text-orange-700":"text-gray-500"),children:e.label}),e.required&&(0,a.jsx)(j.E,{variant:"destructive",className:"text-xs px-1 py-0",children:"Wajib"})]}),r&&(0,a.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:e.description})]})]},e.id))})})]}),(0,a.jsxs)(l.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"publishing-step.tsx",children:[(0,a.jsxs)(l.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"publishing-step.tsx",children:[(0,a.jsxs)(l.ZB,{className:"flex items-center space-x-2","data-sentry-element":"CardTitle","data-sentry-source-file":"publishing-step.tsx",children:[(0,a.jsx)(ea.A,{className:"w-5 h-5","data-sentry-element":"Eye","data-sentry-source-file":"publishing-step.tsx"}),(0,a.jsx)("span",{children:"Preview Course"})]}),(0,a.jsx)(l.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"publishing-step.tsx",children:"Begini tampilan course Anda untuk siswa"})]}),(0,a.jsx)(l.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"publishing-step.tsx",children:(0,a.jsxs)("div",{className:"border rounded-lg p-4 space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[e.coverImage?(0,a.jsx)("img",{src:"string"==typeof e.coverImage?e.coverImage:URL.createObjectURL(e.coverImage),alt:e.name,className:"w-20 h-20 object-cover rounded-lg"}):(0,a.jsx)("div",{className:"w-20 h-20 bg-muted rounded-lg flex items-center justify-center",children:(0,a.jsx)(H.A,{className:"w-8 h-8 text-muted-foreground"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h4",{className:"font-semibold text-lg",children:e.name||"Nama Course"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-2",children:e.description||"Deskripsi course"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-xs text-muted-foreground",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(eu.A,{className:"w-3 h-3","data-sentry-element":"Code","data-sentry-source-file":"publishing-step.tsx"}),(0,a.jsx)("span",{children:e.courseCode||"COURSE-CODE"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(I.A,{className:"w-3 h-3","data-sentry-element":"BookOpen","data-sentry-source-file":"publishing-step.tsx"}),(0,a.jsxs)("span",{children:[g.modules," Modul"]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(et.A,{className:"w-3 h-3","data-sentry-element":"Clock","data-sentry-source-file":"publishing-step.tsx"}),(0,a.jsxs)("span",{children:["~",g.estimatedDuration," Menit"]})]}),e.startDate&&(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(z.A,{className:"w-3 h-3"}),(0,a.jsx)("span",{children:new Date(e.startDate).toLocaleDateString()})]})]})]})]}),(0,a.jsx)(c.Separator,{"data-sentry-element":"Separator","data-sentry-source-file":"publishing-step.tsx"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h5",{className:"font-medium text-sm",children:"Struktur Course:"}),e.modules.length>0?(0,a.jsxs)("div",{className:"space-y-2",children:[e.modules.slice(0,3).map((e,t)=>(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsxs)("div",{className:"font-medium",children:[t+1,". ",e.name]}),(0,a.jsxs)("div",{className:"ml-4 text-xs text-muted-foreground",children:[e.chapters.length," chapter",e.hasModuleQuiz&&" • Quiz modul"]})]},e.id)),e.modules.length>3&&(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:["... dan ",e.modules.length-3," modul lainnya"]})]}):(0,a.jsx)("p",{className:"text-sm text-muted-foreground italic",children:"Belum ada modul"})]})]})})]}),!h&&(0,a.jsxs)(ed.Fc,{children:[(0,a.jsx)(eo.A,{className:"h-4 w-4"}),(0,a.jsxs)(ed.TN,{children:[(0,a.jsx)("strong",{children:"Perhatian:"})," Beberapa item wajib belum lengkap. Course tidak dapat dipublikasi sampai semua item wajib diselesaikan."]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between pt-6",children:[(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:h?"Course siap dipublikasi dan dapat diakses siswa":`${p}/${u.length} item wajib selesai`}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)(i.$,{variant:"outline",disabled:s,"data-sentry-element":"Button","data-sentry-source-file":"publishing-step.tsx",children:[(0,a.jsx)(ea.A,{className:"w-4 h-4 mr-2","data-sentry-element":"Eye","data-sentry-source-file":"publishing-step.tsx"}),"Preview"]}),(0,a.jsx)(i.$,{onClick:v,disabled:!h||s,className:"min-w-[120px]","data-sentry-element":"Button","data-sentry-source-file":"publishing-step.tsx",children:s?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),"Publishing..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(ec.A,{className:"w-4 h-4 mr-2"}),"Publikasi Course"]})})]})]})]})}var ep=s(54090),eh=s(12258);function ey({data:e,onUpdate:t}){let s=e.admissions||{requirements:[],applicationDeadline:"",prerequisites:[]},[r,d]=(0,n.useState)(""),[c,o]=(0,n.useState)(""),m=(e,a)=>{t({admissions:{...s,[e]:a}})},u=()=>{""===r.trim()||s.requirements.includes(r.trim())||(m("requirements",[...s.requirements,r.trim()]),d(""))},x=e=>{m("requirements",s.requirements.filter((t,s)=>s!==e))},y=()=>{""===c.trim()||s.prerequisites.includes(c.trim())||(m("prerequisites",[...s.prerequisites,c.trim()]),o(""))},f=e=>{m("prerequisites",s.prerequisites.filter((t,s)=>s!==e))};return(0,a.jsxs)(l.Zp,{className:"w-full","data-sentry-element":"Card","data-sentry-component":"AdmissionsStep","data-sentry-source-file":"admissions-step.tsx",children:[(0,a.jsxs)(l.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"admissions-step.tsx",children:[(0,a.jsx)(l.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"admissions-step.tsx",children:"Informasi Pendaftaran"}),(0,a.jsx)(l.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"admissions-step.tsx",children:"Detail terkait persyaratan pendaftaran dan prasyarat kursus."})]}),(0,a.jsxs)(l.Wu,{className:"space-y-6","data-sentry-element":"CardContent","data-sentry-source-file":"admissions-step.tsx",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(eh.A,{className:"h-5 w-5 text-gray-500","data-sentry-element":"ClipboardList","data-sentry-source-file":"admissions-step.tsx"}),(0,a.jsx)(h.J,{htmlFor:"newRequirement","data-sentry-element":"Label","data-sentry-source-file":"admissions-step.tsx",children:"Persyaratan"})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(p.p,{id:"newRequirement",value:r,onChange:e=>d(e.target.value),placeholder:"Tambahkan persyaratan baru",onKeyPress:e=>{"Enter"===e.key&&(e.preventDefault(),u())},"data-sentry-element":"Input","data-sentry-source-file":"admissions-step.tsx"}),(0,a.jsx)(i.$,{type:"button",onClick:u,"data-sentry-element":"Button","data-sentry-source-file":"admissions-step.tsx",children:"Tambah"})]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:s.requirements.map((e,t)=>(0,a.jsxs)(j.E,{variant:"secondary",className:"pr-1",children:[e,(0,a.jsx)(i.$,{type:"button",variant:"ghost",size:"sm",className:"ml-1 h-auto px-1 py-0.5",onClick:()=>x(t),children:(0,a.jsx)(w.A,{className:"h-3 w-3"})})]},t))})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(z.A,{className:"h-5 w-5 text-gray-500","data-sentry-element":"Calendar","data-sentry-source-file":"admissions-step.tsx"}),(0,a.jsx)(h.J,{htmlFor:"applicationDeadline","data-sentry-element":"Label","data-sentry-source-file":"admissions-step.tsx",children:"Batas Waktu Pendaftaran"})]}),(0,a.jsx)(p.p,{id:"applicationDeadline",type:"text",value:s.applicationDeadline,onChange:e=>m("applicationDeadline",e.target.value),placeholder:"Contoh: 2024-12-31","data-sentry-element":"Input","data-sentry-source-file":"admissions-step.tsx"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(I.A,{className:"h-5 w-5 text-gray-500","data-sentry-element":"BookOpen","data-sentry-source-file":"admissions-step.tsx"}),(0,a.jsx)(h.J,{htmlFor:"newPrerequisite","data-sentry-element":"Label","data-sentry-source-file":"admissions-step.tsx",children:"Prasyarat"})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(p.p,{id:"newPrerequisite",value:c,onChange:e=>o(e.target.value),placeholder:"Tambahkan prasyarat baru",onKeyPress:e=>{"Enter"===e.key&&(e.preventDefault(),y())},"data-sentry-element":"Input","data-sentry-source-file":"admissions-step.tsx"}),(0,a.jsx)(i.$,{type:"button",onClick:y,"data-sentry-element":"Button","data-sentry-source-file":"admissions-step.tsx",children:"Tambah"})]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:s.prerequisites.map((e,t)=>(0,a.jsxs)(j.E,{variant:"secondary",className:"pr-1",children:[e,(0,a.jsx)(i.$,{type:"button",variant:"ghost",size:"sm",className:"ml-1 h-auto px-1 py-0.5",onClick:()=>f(t),children:(0,a.jsx)(w.A,{className:"h-3 w-3"})})]},t))})]})]})]})}var ef=s(63890),ej=s(11477),eg=s(69622);function ev({data:e,onUpdate:t}){let s=e.academics||{credits:0,workload:"",assessment:[]},[r,d]=(0,n.useState)(""),c=(e,a)=>{t({academics:{...s,[e]:a}})},o=()=>{""===r.trim()||s.assessment.includes(r.trim())||(c("assessment",[...s.assessment,r.trim()]),d(""))},m=e=>{c("assessment",s.assessment.filter((t,s)=>s!==e))};return(0,a.jsxs)(l.Zp,{className:"w-full","data-sentry-element":"Card","data-sentry-component":"AcademicsStep","data-sentry-source-file":"academics-step.tsx",children:[(0,a.jsxs)(l.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"academics-step.tsx",children:[(0,a.jsx)(l.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"academics-step.tsx",children:"Informasi Akademik"}),(0,a.jsx)(l.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"academics-step.tsx",children:"Detail terkait struktur akademik dan penilaian kursus."})]}),(0,a.jsxs)(l.Wu,{className:"space-y-6","data-sentry-element":"CardContent","data-sentry-source-file":"academics-step.tsx",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(ef.A,{className:"h-5 w-5 text-gray-500","data-sentry-element":"Book","data-sentry-source-file":"academics-step.tsx"}),(0,a.jsx)(h.J,{htmlFor:"credits","data-sentry-element":"Label","data-sentry-source-file":"academics-step.tsx",children:"Kredit"})]}),(0,a.jsx)(p.p,{id:"credits",type:"number",value:s.credits,onChange:e=>c("credits",parseInt(e.target.value)),placeholder:"Contoh: 12","data-sentry-element":"Input","data-sentry-source-file":"academics-step.tsx"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(ej.A,{className:"h-5 w-5 text-gray-500","data-sentry-element":"Hourglass","data-sentry-source-file":"academics-step.tsx"}),(0,a.jsx)(h.J,{htmlFor:"workload","data-sentry-element":"Label","data-sentry-source-file":"academics-step.tsx",children:"Beban Kerja"})]}),(0,a.jsx)(p.p,{id:"workload",type:"text",value:s.workload,onChange:e=>c("workload",e.target.value),placeholder:"Contoh: 12-15 jam/minggu","data-sentry-element":"Input","data-sentry-source-file":"academics-step.tsx"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(eg.A,{className:"h-5 w-5 text-gray-500","data-sentry-element":"Award","data-sentry-source-file":"academics-step.tsx"}),(0,a.jsx)(h.J,{htmlFor:"newAssessment","data-sentry-element":"Label","data-sentry-source-file":"academics-step.tsx",children:"Penilaian"})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(p.p,{id:"newAssessment",value:r,onChange:e=>d(e.target.value),placeholder:"Tambahkan metode penilaian baru",onKeyPress:e=>{"Enter"===e.key&&(e.preventDefault(),o())},"data-sentry-element":"Input","data-sentry-source-file":"academics-step.tsx"}),(0,a.jsx)(i.$,{type:"button",onClick:o,"data-sentry-element":"Button","data-sentry-source-file":"academics-step.tsx",children:"Tambah"})]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:s.assessment.map((e,t)=>(0,a.jsxs)(j.E,{variant:"secondary",className:"pr-1",children:[e,(0,a.jsx)(i.$,{type:"button",variant:"ghost",size:"sm",className:"ml-1 h-auto px-1 py-0.5",onClick:()=>m(t),children:(0,a.jsx)(w.A,{className:"h-3 w-3"})})]},t))})]})]})]})}var eb=s(28235),eN=s(73473),ek=s(12793);function eC({data:e,onUpdate:t}){let s=e.tuitionAndFinancing||{totalCost:0,paymentOptions:[],scholarships:[]},[r,d]=(0,n.useState)(""),[c,o]=(0,n.useState)(""),m=(e,a)=>{t({tuitionAndFinancing:{...s,[e]:a}})},u=()=>{""===r.trim()||s.paymentOptions.includes(r.trim())||(m("paymentOptions",[...s.paymentOptions,r.trim()]),d(""))},x=e=>{m("paymentOptions",s.paymentOptions.filter((t,s)=>s!==e))},y=()=>{""===c.trim()||s.scholarships.includes(c.trim())||(m("scholarships",[...s.scholarships,c.trim()]),o(""))},f=e=>{m("scholarships",s.scholarships.filter((t,s)=>s!==e))};return(0,a.jsxs)(l.Zp,{className:"w-full","data-sentry-element":"Card","data-sentry-component":"TuitionFinancingStep","data-sentry-source-file":"tuition-financing-step.tsx",children:[(0,a.jsxs)(l.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"tuition-financing-step.tsx",children:[(0,a.jsx)(l.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"tuition-financing-step.tsx",children:"Biaya & Pembiayaan"}),(0,a.jsx)(l.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"tuition-financing-step.tsx",children:"Detail terkait biaya kursus, opsi pembayaran, dan peluang beasiswa."})]}),(0,a.jsxs)(l.Wu,{className:"space-y-6","data-sentry-element":"CardContent","data-sentry-source-file":"tuition-financing-step.tsx",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(eb.A,{className:"h-5 w-5 text-gray-500","data-sentry-element":"DollarSign","data-sentry-source-file":"tuition-financing-step.tsx"}),(0,a.jsx)(h.J,{htmlFor:"totalCost","data-sentry-element":"Label","data-sentry-source-file":"tuition-financing-step.tsx",children:"Total Biaya"})]}),(0,a.jsx)(p.p,{id:"totalCost",type:"number",value:s.totalCost,onChange:e=>m("totalCost",parseFloat(e.target.value)),placeholder:"Contoh: 6000000","data-sentry-element":"Input","data-sentry-source-file":"tuition-financing-step.tsx"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(eN.A,{className:"h-5 w-5 text-gray-500","data-sentry-element":"CreditCard","data-sentry-source-file":"tuition-financing-step.tsx"}),(0,a.jsx)(h.J,{htmlFor:"newPaymentOption","data-sentry-element":"Label","data-sentry-source-file":"tuition-financing-step.tsx",children:"Opsi Pembayaran"})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(p.p,{id:"newPaymentOption",value:r,onChange:e=>d(e.target.value),placeholder:"Tambahkan opsi pembayaran baru",onKeyPress:e=>{"Enter"===e.key&&(e.preventDefault(),u())},"data-sentry-element":"Input","data-sentry-source-file":"tuition-financing-step.tsx"}),(0,a.jsx)(i.$,{type:"button",onClick:u,"data-sentry-element":"Button","data-sentry-source-file":"tuition-financing-step.tsx",children:"Tambah"})]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:s.paymentOptions.map((e,t)=>(0,a.jsxs)(j.E,{variant:"secondary",className:"pr-1",children:[e,(0,a.jsx)(i.$,{type:"button",variant:"ghost",size:"sm",className:"ml-1 h-auto px-1 py-0.5",onClick:()=>x(t),children:(0,a.jsx)(w.A,{className:"h-3 w-3"})})]},t))})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(ek.A,{className:"h-5 w-5 text-gray-500","data-sentry-element":"Gift","data-sentry-source-file":"tuition-financing-step.tsx"}),(0,a.jsx)(h.J,{htmlFor:"newScholarship","data-sentry-element":"Label","data-sentry-source-file":"tuition-financing-step.tsx",children:"Beasiswa"})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(p.p,{id:"newScholarship",value:c,onChange:e=>o(e.target.value),placeholder:"Tambahkan beasiswa baru",onKeyPress:e=>{"Enter"===e.key&&(e.preventDefault(),y())},"data-sentry-element":"Input","data-sentry-source-file":"tuition-financing-step.tsx"}),(0,a.jsx)(i.$,{type:"button",onClick:y,"data-sentry-element":"Button","data-sentry-source-file":"tuition-financing-step.tsx",children:"Tambah"})]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:s.scholarships.map((e,t)=>(0,a.jsxs)(j.E,{variant:"secondary",className:"pr-1",children:[e,(0,a.jsx)(i.$,{type:"button",variant:"ghost",size:"sm",className:"ml-1 h-auto px-1 py-0.5",onClick:()=>f(t),children:(0,a.jsx)(w.A,{className:"h-3 w-3"})})]},t))})]})]})]})}var ew=s(55863),eA=s(31667);function eD({data:e,onUpdate:t}){let s=e.careers||{outcomes:[],industries:[],averageSalary:""},[r,d]=(0,n.useState)(""),[c,o]=(0,n.useState)(""),m=(e,a)=>{t({careers:{...s,[e]:a}})},u=()=>{""===r.trim()||s.outcomes.includes(r.trim())||(m("outcomes",[...s.outcomes,r.trim()]),d(""))},x=e=>{m("outcomes",s.outcomes.filter((t,s)=>s!==e))},y=()=>{""===c.trim()||s.industries.includes(c.trim())||(m("industries",[...s.industries,c.trim()]),o(""))},f=e=>{m("industries",s.industries.filter((t,s)=>s!==e))};return(0,a.jsxs)(l.Zp,{className:"w-full","data-sentry-element":"Card","data-sentry-component":"CareersStep","data-sentry-source-file":"careers-step.tsx",children:[(0,a.jsxs)(l.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"careers-step.tsx",children:[(0,a.jsx)(l.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"careers-step.tsx",children:"Peluang Karir"}),(0,a.jsx)(l.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"careers-step.tsx",children:"Detail terkait hasil karir dan industri yang relevan setelah kursus."})]}),(0,a.jsxs)(l.Wu,{className:"space-y-6","data-sentry-element":"CardContent","data-sentry-source-file":"careers-step.tsx",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(ew.A,{className:"h-5 w-5 text-gray-500","data-sentry-element":"Briefcase","data-sentry-source-file":"careers-step.tsx"}),(0,a.jsx)(h.J,{htmlFor:"newOutcome","data-sentry-element":"Label","data-sentry-source-file":"careers-step.tsx",children:"Hasil"})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(p.p,{id:"newOutcome",value:r,onChange:e=>d(e.target.value),placeholder:"Tambahkan hasil karir baru",onKeyPress:e=>{"Enter"===e.key&&(e.preventDefault(),u())},"data-sentry-element":"Input","data-sentry-source-file":"careers-step.tsx"}),(0,a.jsx)(i.$,{type:"button",onClick:u,"data-sentry-element":"Button","data-sentry-source-file":"careers-step.tsx",children:"Tambah"})]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:s.outcomes.map((e,t)=>(0,a.jsxs)(j.E,{variant:"secondary",className:"pr-1",children:[e,(0,a.jsx)(i.$,{type:"button",variant:"ghost",size:"sm",className:"ml-1 h-auto px-1 py-0.5",onClick:()=>x(t),children:(0,a.jsx)(w.A,{className:"h-3 w-3"})})]},t))})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(eA.A,{className:"h-5 w-5 text-gray-500","data-sentry-element":"Building","data-sentry-source-file":"careers-step.tsx"}),(0,a.jsx)(h.J,{htmlFor:"newIndustry","data-sentry-element":"Label","data-sentry-source-file":"careers-step.tsx",children:"Industri"})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(p.p,{id:"newIndustry",value:c,onChange:e=>o(e.target.value),placeholder:"Tambahkan industri baru",onKeyPress:e=>{"Enter"===e.key&&(e.preventDefault(),y())},"data-sentry-element":"Input","data-sentry-source-file":"careers-step.tsx"}),(0,a.jsx)(i.$,{type:"button",onClick:y,"data-sentry-element":"Button","data-sentry-source-file":"careers-step.tsx",children:"Tambah"})]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:s.industries.map((e,t)=>(0,a.jsxs)(j.E,{variant:"secondary",className:"pr-1",children:[e,(0,a.jsx)(i.$,{type:"button",variant:"ghost",size:"sm",className:"ml-1 h-auto px-1 py-0.5",onClick:()=>f(t),children:(0,a.jsx)(w.A,{className:"h-3 w-3"})})]},t))})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(eb.A,{className:"h-5 w-5 text-gray-500","data-sentry-element":"DollarSign","data-sentry-source-file":"careers-step.tsx"}),(0,a.jsx)(h.J,{htmlFor:"averageSalary","data-sentry-element":"Label","data-sentry-source-file":"careers-step.tsx",children:"Rata-rata Gaji"})]}),(0,a.jsx)(p.p,{id:"averageSalary",type:"text",value:s.averageSalary,onChange:e=>m("averageSalary",e.target.value),placeholder:"Contoh: Rp780.000.000 - Rp1.140.000.000 per tahun","data-sentry-element":"Input","data-sentry-source-file":"careers-step.tsx"})]})]})}var ez=s(29602),eS=s(65090),eT=s(14687);function eP({data:e,onUpdate:t}){let s=e.studentExperience||{testimonials:[],facilities:[],support:[]},[r,d]=(0,n.useState)(""),[c,o]=(0,n.useState)(""),m=(e,a)=>{t({studentExperience:{...s,[e]:a}})},u=(e,t,a)=>{let n=[...s.testimonials];n[e]={...n[e],[t]:a},m("testimonials",n)},x=e=>{m("testimonials",s.testimonials.filter((t,s)=>s!==e))},f=()=>{""===r.trim()||s.facilities.includes(r.trim())||(m("facilities",[...s.facilities,r.trim()]),d(""))},g=e=>{m("facilities",s.facilities.filter((t,s)=>s!==e))},v=()=>{""===c.trim()||s.support.includes(c.trim())||(m("support",[...s.support,c.trim()]),o(""))},b=e=>{m("support",s.support.filter((t,s)=>s!==e))};return(0,a.jsxs)(l.Zp,{className:"w-full","data-sentry-element":"Card","data-sentry-component":"StudentExperienceStep","data-sentry-source-file":"student-experience-step.tsx",children:[(0,a.jsxs)(l.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"student-experience-step.tsx",children:[(0,a.jsx)(l.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"student-experience-step.tsx",children:"Pengalaman Mahasiswa"}),(0,a.jsx)(l.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"student-experience-step.tsx",children:"Detail terkait pengalaman, fasilitas, dan dukungan yang akan didapat mahasiswa."})]}),(0,a.jsxs)(l.Wu,{className:"space-y-6","data-sentry-element":"CardContent","data-sentry-source-file":"student-experience-step.tsx",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)(ez.A,{className:"h-5 w-5 text-gray-500","data-sentry-element":"MessageSquare","data-sentry-source-file":"student-experience-step.tsx"}),(0,a.jsx)(h.J,{"data-sentry-element":"Label","data-sentry-source-file":"student-experience-step.tsx",children:"Testimoni"})]}),s.testimonials.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-end space-x-2 mb-4",children:[(0,a.jsxs)("div",{className:"flex-grow space-y-2",children:[(0,a.jsx)(p.p,{placeholder:"Nama",value:e.name,onChange:e=>u(t,"name",e.target.value)}),(0,a.jsx)(y.T,{placeholder:"Umpan Balik",value:e.feedback,onChange:e=>u(t,"feedback",e.target.value)})]}),(0,a.jsx)(i.$,{variant:"destructive",size:"icon",onClick:()=>x(t),children:(0,a.jsx)(w.A,{className:"h-4 w-4"})})]},t)),(0,a.jsxs)(i.$,{variant:"outline",onClick:()=>{m("testimonials",[...s.testimonials,{name:"",feedback:""}])},"data-sentry-element":"Button","data-sentry-source-file":"student-experience-step.tsx",children:[(0,a.jsx)($.A,{className:"h-4 w-4 mr-2","data-sentry-element":"Plus","data-sentry-source-file":"student-experience-step.tsx"})," Tambah Testimoni"]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(eS.A,{className:"h-5 w-5 text-gray-500","data-sentry-element":"HardHat","data-sentry-source-file":"student-experience-step.tsx"}),(0,a.jsx)(h.J,{htmlFor:"newFacility","data-sentry-element":"Label","data-sentry-source-file":"student-experience-step.tsx",children:"Fasilitas"})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(p.p,{id:"newFacility",value:r,onChange:e=>d(e.target.value),placeholder:"Tambahkan fasilitas baru",onKeyPress:e=>{"Enter"===e.key&&(e.preventDefault(),f())},"data-sentry-element":"Input","data-sentry-source-file":"student-experience-step.tsx"}),(0,a.jsx)(i.$,{type:"button",onClick:f,"data-sentry-element":"Button","data-sentry-source-file":"student-experience-step.tsx",children:"Tambah"})]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:s.facilities.map((e,t)=>(0,a.jsxs)(j.E,{variant:"secondary",className:"pr-1",children:[e,(0,a.jsx)(i.$,{type:"button",variant:"ghost",size:"sm",className:"ml-1 h-auto px-1 py-0.5",onClick:()=>g(t),children:(0,a.jsx)(w.A,{className:"h-3 w-3"})})]},t))})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(eT.A,{className:"h-5 w-5 text-gray-500","data-sentry-element":"LifeBuoy","data-sentry-source-file":"student-experience-step.tsx"}),(0,a.jsx)(h.J,{htmlFor:"newSupport","data-sentry-element":"Label","data-sentry-source-file":"student-experience-step.tsx",children:"Dukungan"})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(p.p,{id:"newSupport",value:c,onChange:e=>o(e.target.value),placeholder:"Tambahkan dukungan baru",onKeyPress:e=>{"Enter"===e.key&&(e.preventDefault(),v())},"data-sentry-element":"Input","data-sentry-source-file":"student-experience-step.tsx"}),(0,a.jsx)(i.$,{type:"button",onClick:v,"data-sentry-element":"Button","data-sentry-source-file":"student-experience-step.tsx",children:"Tambah"})]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:s.support.map((e,t)=>(0,a.jsxs)(j.E,{variant:"secondary",className:"pr-1",children:[e,(0,a.jsx)(i.$,{type:"button",variant:"ghost",size:"sm",className:"ml-1 h-auto px-1 py-0.5",onClick:()=>b(t),children:(0,a.jsx)(w.A,{className:"h-3 w-3"})})]},t))})]})]})]})}function eB({data:e,onUpdate:t}){return(0,a.jsxs)(l.Zp,{"data-sentry-element":"Card","data-sentry-component":"CourseDetailsStep","data-sentry-source-file":"course-details-step.tsx",children:[(0,a.jsxs)(l.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"course-details-step.tsx",children:[(0,a.jsx)(l.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"course-details-step.tsx",children:"Detail Course"}),(0,a.jsx)(l.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"course-details-step.tsx",children:"Kelola detail penerimaan, akademik, pembiayaan, karir, dan pengalaman siswa."})]}),(0,a.jsx)(l.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"course-details-step.tsx",children:(0,a.jsxs)(ep.tU,{defaultValue:"admissions",className:"w-full","data-sentry-element":"Tabs","data-sentry-source-file":"course-details-step.tsx",children:[(0,a.jsxs)(ep.j7,{className:"grid w-full grid-cols-5","data-sentry-element":"TabsList","data-sentry-source-file":"course-details-step.tsx",children:[(0,a.jsx)(ep.Xi,{value:"admissions","data-sentry-element":"TabsTrigger","data-sentry-source-file":"course-details-step.tsx",children:"Penerimaan"}),(0,a.jsx)(ep.Xi,{value:"academics","data-sentry-element":"TabsTrigger","data-sentry-source-file":"course-details-step.tsx",children:"Akademik"}),(0,a.jsx)(ep.Xi,{value:"tuition-financing","data-sentry-element":"TabsTrigger","data-sentry-source-file":"course-details-step.tsx",children:"Biaya & Pembiayaan"}),(0,a.jsx)(ep.Xi,{value:"careers","data-sentry-element":"TabsTrigger","data-sentry-source-file":"course-details-step.tsx",children:"Karir"}),(0,a.jsx)(ep.Xi,{value:"student-experience","data-sentry-element":"TabsTrigger","data-sentry-source-file":"course-details-step.tsx",children:"Pengalaman Siswa"})]}),(0,a.jsxs)("div",{className:"h-[400px] overflow-y-auto pr-4",children:[" ",(0,a.jsx)(ep.av,{value:"admissions","data-sentry-element":"TabsContent","data-sentry-source-file":"course-details-step.tsx",children:(0,a.jsx)(ey,{data:e,onUpdate:t,"data-sentry-element":"AdmissionsStep","data-sentry-source-file":"course-details-step.tsx"})}),(0,a.jsx)(ep.av,{value:"academics","data-sentry-element":"TabsContent","data-sentry-source-file":"course-details-step.tsx",children:(0,a.jsx)(ev,{data:e,onUpdate:t,"data-sentry-element":"AcademicsStep","data-sentry-source-file":"course-details-step.tsx"})}),(0,a.jsx)(ep.av,{value:"tuition-financing","data-sentry-element":"TabsContent","data-sentry-source-file":"course-details-step.tsx",children:(0,a.jsx)(eC,{data:e,onUpdate:t,"data-sentry-element":"TuitionFinancingStep","data-sentry-source-file":"course-details-step.tsx"})}),(0,a.jsx)(ep.av,{value:"careers","data-sentry-element":"TabsContent","data-sentry-source-file":"course-details-step.tsx",children:(0,a.jsx)(eD,{data:e,onUpdate:t,"data-sentry-element":"CareersStep","data-sentry-source-file":"course-details-step.tsx"})}),(0,a.jsx)(ep.av,{value:"student-experience","data-sentry-element":"TabsContent","data-sentry-source-file":"course-details-step.tsx",children:(0,a.jsx)(eP,{data:e,onUpdate:t,"data-sentry-element":"StudentExperienceStep","data-sentry-source-file":"course-details-step.tsx"})})]})]})})]})}let eE=[{id:"basic-info",title:"Informasi Dasar",description:"Detail course dan pengaturan dasar"},{id:"module-structure",title:"Struktur Modul",description:"Buat modul dan chapter untuk course"},{id:"content-creation",title:"Pembuatan Konten",description:"Tambahkan konten dan quiz untuk setiap chapter"},{id:"course-details",title:"Informasi Tambahan",description:"Detail penerimaan, akademik, pembiayaan, karir, dan pengalaman siswa"},{id:"publishing",title:"Publikasi",description:"Review dan publikasikan course"}];function eq({onComplete:e,onCancel:t,initialData:s}){let[r,p]=(0,n.useState)(0),[h,y]=(0,n.useState)({name:s?.name||"",description:s?.description||"",instructor:s?.instructor||"",courseCode:s?.courseCode||"",type:s?.type||"self_paced",enrollmentType:s?.enrollmentType||"code",startDate:s?.startDate,endDate:s?.endDate,coverImage:s?.coverImage,coverImagePreview:s?.coverImagePreview,isPurchasable:s?.isPurchasable??!1,price:s?.price,currency:s?.currency||"",previewMode:s?.previewMode??!1,modules:s?.modules||[],isPublished:s?.isPublished??!1,assignedClasses:s?.assignedClasses||[],finalExam:s?.finalExam,admissions:s?.admissions||{requirements:[],applicationDeadline:"",prerequisites:[]},academics:s?.academics||{credits:0,workload:"",assessment:[]},tuitionAndFinancing:s?.tuitionAndFinancing||{totalCost:0,paymentOptions:[],scholarships:[]},careers:s?.careers||{outcomes:[],industries:[],averageSalary:""},studentExperience:s?.studentExperience||{testimonials:[],facilities:[],support:[]}}),[f,j]=(0,n.useState)(!1),g=e=>{y(t=>({...t,...e}))},v=e=>{switch(e){case 0:let t=!!h.name&&!!h.description&&!!h.instructor&&!!h.courseCode;if("purchase"===h.enrollmentType)return t&&!!h.price&&h.price>0&&!!h.currency;return t;case 1:return h.modules.length>0&&h.modules.every(e=>!!e.name&&e.chapters.length>0);case 2:return h.modules.every(e=>e.chapters.every(e=>!!e.content));case 3:let s=!!h.admissions&&(h.admissions.requirements.length>0||!!h.admissions.applicationDeadline||h.admissions.prerequisites.length>0),a=!!h.academics&&(h.academics.credits>0||!!h.academics.workload||h.academics.assessment.length>0),n=!!h.tuitionAndFinancing&&(!!h.tuitionAndFinancing.totalCost||h.tuitionAndFinancing.paymentOptions.length>0||h.tuitionAndFinancing.scholarships.length>0),r=!!h.careers&&(h.careers.outcomes.length>0||h.careers.industries.length>0||!!h.careers.averageSalary),i=!!h.studentExperience&&(h.studentExperience.testimonials.length>0||h.studentExperience.facilities.length>0||h.studentExperience.support.length>0);return s||a||n||r||i;case 4:return!0;default:return!1}},b=()=>v(r),N=async()=>{j(!0);try{await e(h)}catch(e){console.error("Error creating course:",e)}finally{j(!1)}},k=(r+1)/eE.length*100;return(0,a.jsxs)("div",{className:"w-full p-6 space-y-6","data-sentry-component":"CourseCreationWizard","data-sentry-source-file":"course-creation-wizard.tsx",children:[(0,a.jsx)(l.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"course-creation-wizard.tsx",children:(0,a.jsxs)(l.Wu,{className:"pt-6","data-sentry-element":"CardContent","data-sentry-source-file":"course-creation-wizard.tsx",children:[(0,a.jsx)("div",{className:"flex items-center justify-between gap-x-4 overflow-x-auto pb-4 px-4",children:eE.map((e,t)=>(0,a.jsxs)("div",{className:"flex flex-col items-center flex-grow",children:[(0,a.jsx)("div",{className:(0,x.cn)("w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold transition-colors duration-200",t===r?"bg-primary text-primary-foreground":"bg-muted text-muted-foreground",t<r&&"bg-green-500 text-white"),children:t<r?(0,a.jsx)(o.A,{className:"w-4 h-4"}):t+1}),(0,a.jsx)("span",{className:(0,x.cn)("mt-1 text-xs text-center whitespace-nowrap",t===r?"text-primary font-medium":"text-muted-foreground"),children:e.title})]},e.id))}),(0,a.jsx)(c.Separator,{className:"my-4","data-sentry-element":"Separator","data-sentry-source-file":"course-creation-wizard.tsx"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsxs)("span",{children:["Langkah ",r+1," dari ",eE.length]}),(0,a.jsxs)("span",{children:[Math.round(k),"% selesai"]})]}),(0,a.jsx)(d.k,{value:k,className:"h-2","data-sentry-element":"Progress","data-sentry-source-file":"course-creation-wizard.tsx"})]})]})}),(0,a.jsx)(l.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"course-creation-wizard.tsx",children:(0,a.jsx)(l.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"course-creation-wizard.tsx",children:(()=>{switch(r){case 0:return(0,a.jsx)(T,{data:h,onUpdate:g});case 1:return(0,a.jsx)(J,{data:h,onUpdate:g});case 2:return(0,a.jsx)(el,{data:h,onUpdate:g});case 3:return(0,a.jsx)(eB,{data:h,onUpdate:g});case 4:return(0,a.jsx)(ex,{data:h,onPublish:N,isPublishing:f});default:return null}})()})}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)(i.$,{variant:"outline",onClick:()=>{r>0&&p(r-1)},disabled:0===r,"data-sentry-element":"Button","data-sentry-source-file":"course-creation-wizard.tsx",children:[(0,a.jsx)(m.A,{className:"w-4 h-4 mr-2","data-sentry-element":"ChevronLeft","data-sentry-source-file":"course-creation-wizard.tsx"}),"Sebelumnya"]}),(0,a.jsx)("div",{className:"flex space-x-2",children:r===eE.length-1?(0,a.jsx)(i.$,{onClick:N,disabled:!b()||f,children:f?"Membuat Course...":"Selesai & Buat Course"}):(0,a.jsxs)(i.$,{onClick:()=>{r<eE.length-1&&p(r+1)},disabled:!b(),children:["Selanjutnya",(0,a.jsx)(u.A,{className:"w-4 h-4 ml-2"})]})})]})]})}},52377:(e,t,s)=>{Promise.resolve().then(s.bind(s,14621))},54090:(e,t,s)=>{"use strict";s.d(t,{Xi:()=>c,av:()=>o,j7:()=>d,tU:()=>l});var a=s(91754),n=s(93491),r=s(36991),i=s(82233);let l=r.bL,d=n.forwardRef(({className:e,...t},s)=>(0,a.jsx)(r.B8,{ref:s,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));d.displayName=r.B8.displayName;let c=n.forwardRef(({className:e,...t},s)=>(0,a.jsx)(r.l9,{ref:s,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm cursor-pointer",e),...t}));c.displayName=r.l9.displayName;let o=n.forwardRef(({className:e,...t},s)=>(0,a.jsx)(r.UC,{ref:s,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));o.displayName=r.UC.displayName},58428:(e,t,s)=>{"use strict";s.d(t,{T:()=>r});var a=s(91754);s(93491);var n=s(82233);function r({className:e,...t}){return(0,a.jsx)("textarea",{"data-slot":"textarea",className:(0,n.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...t,"data-sentry-component":"Textarea","data-sentry-source-file":"textarea.tsx"})}},64755:(e,t,s)=>{"use strict";let a;s.r(t),s.d(t,{default:()=>x,generateImageMetadata:()=>m,generateMetadata:()=>o,generateViewport:()=>u});var n=s(63033),r=s(1472),i=s(7688),l=(0,r.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\layout.tsx","default");let d={...n},c="workUnitAsyncStorage"in d?d.workUnitAsyncStorage:"requestAsyncStorage"in d?d.requestAsyncStorage:void 0;a="function"==typeof l?new Proxy(l,{apply:(e,t,s)=>{let a,n,r;try{let e=c?.getStore();a=e?.headers.get("sentry-trace")??void 0,n=e?.headers.get("baggage")??void 0,r=e?.headers}catch{}return i.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/teacher",componentType:"Layout",sentryTraceHeader:a,baggageHeader:n,headers:r}).apply(t,s)}}):l;let o=void 0,m=void 0,u=void 0,x=a},73225:(e,t,s)=>{"use strict";s.d(t,{S:()=>l});var a=s(91754);s(93491);var n=s(22646),r=s(87435),i=s(82233);function l({className:e,...t}){return(0,a.jsx)(n.bL,{"data-slot":"checkbox",className:(0,i.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,"data-sentry-element":"CheckboxPrimitive.Root","data-sentry-component":"Checkbox","data-sentry-source-file":"checkbox.tsx",children:(0,a.jsx)(n.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none","data-sentry-element":"CheckboxPrimitive.Indicator","data-sentry-source-file":"checkbox.tsx",children:(0,a.jsx)(r.A,{className:"size-3.5","data-sentry-element":"CheckIcon","data-sentry-source-file":"checkbox.tsx"})})})}},81753:(e,t,s)=>{Promise.resolve().then(s.bind(s,64755))},91269:(e,t,s)=>{"use strict";s.d(t,{$v:()=>h,EO:()=>m,Lt:()=>l,Rx:()=>y,Zr:()=>f,ck:()=>x,r7:()=>p,tv:()=>d,wd:()=>u});var a=s(91754);s(93491);var n=s(82546),r=s(82233),i=s(56682);function l({...e}){return(0,a.jsx)(n.bL,{"data-slot":"alert-dialog",...e,"data-sentry-element":"AlertDialogPrimitive.Root","data-sentry-component":"AlertDialog","data-sentry-source-file":"alert-dialog.tsx"})}function d({...e}){return(0,a.jsx)(n.l9,{"data-slot":"alert-dialog-trigger",...e,"data-sentry-element":"AlertDialogPrimitive.Trigger","data-sentry-component":"AlertDialogTrigger","data-sentry-source-file":"alert-dialog.tsx"})}function c({...e}){return(0,a.jsx)(n.ZL,{"data-slot":"alert-dialog-portal",...e,"data-sentry-element":"AlertDialogPrimitive.Portal","data-sentry-component":"AlertDialogPortal","data-sentry-source-file":"alert-dialog.tsx"})}function o({className:e,...t}){return(0,a.jsx)(n.hJ,{"data-slot":"alert-dialog-overlay",className:(0,r.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t,"data-sentry-element":"AlertDialogPrimitive.Overlay","data-sentry-component":"AlertDialogOverlay","data-sentry-source-file":"alert-dialog.tsx"})}function m({className:e,...t}){return(0,a.jsxs)(c,{"data-sentry-element":"AlertDialogPortal","data-sentry-component":"AlertDialogContent","data-sentry-source-file":"alert-dialog.tsx",children:[(0,a.jsx)(o,{"data-sentry-element":"AlertDialogOverlay","data-sentry-source-file":"alert-dialog.tsx"}),(0,a.jsx)(n.UC,{"data-slot":"alert-dialog-content",className:(0,r.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...t,"data-sentry-element":"AlertDialogPrimitive.Content","data-sentry-source-file":"alert-dialog.tsx"})]})}function u({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"alert-dialog-header",className:(0,r.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t,"data-sentry-component":"AlertDialogHeader","data-sentry-source-file":"alert-dialog.tsx"})}function x({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"alert-dialog-footer",className:(0,r.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t,"data-sentry-component":"AlertDialogFooter","data-sentry-source-file":"alert-dialog.tsx"})}function p({className:e,...t}){return(0,a.jsx)(n.hE,{"data-slot":"alert-dialog-title",className:(0,r.cn)("text-lg font-semibold",e),...t,"data-sentry-element":"AlertDialogPrimitive.Title","data-sentry-component":"AlertDialogTitle","data-sentry-source-file":"alert-dialog.tsx"})}function h({className:e,...t}){return(0,a.jsx)(n.VY,{"data-slot":"alert-dialog-description",className:(0,r.cn)("text-muted-foreground text-sm",e),...t,"data-sentry-element":"AlertDialogPrimitive.Description","data-sentry-component":"AlertDialogDescription","data-sentry-source-file":"alert-dialog.tsx"})}function y({className:e,...t}){return(0,a.jsx)(n.rc,{className:(0,r.cn)((0,i.r)(),e),...t,"data-sentry-element":"AlertDialogPrimitive.Action","data-sentry-component":"AlertDialogAction","data-sentry-source-file":"alert-dialog.tsx"})}function f({className:e,...t}){return(0,a.jsx)(n.ZD,{className:(0,r.cn)((0,i.r)({variant:"outline"}),e),...t,"data-sentry-element":"AlertDialogPrimitive.Cancel","data-sentry-component":"AlertDialogCancel","data-sentry-source-file":"alert-dialog.tsx"})}},93438:(e,t,s)=>{"use strict";s.d(t,{Cf:()=>m,Es:()=>x,L3:()=>p,c7:()=>u,lG:()=>l,rr:()=>h,zM:()=>d});var a=s(91754);s(93491);var n=s(18227),r=s(31619),i=s(82233);function l({...e}){return(0,a.jsx)(n.bL,{"data-slot":"dialog",...e,"data-sentry-element":"DialogPrimitive.Root","data-sentry-component":"Dialog","data-sentry-source-file":"dialog.tsx"})}function d({...e}){return(0,a.jsx)(n.l9,{"data-slot":"dialog-trigger",...e,"data-sentry-element":"DialogPrimitive.Trigger","data-sentry-component":"DialogTrigger","data-sentry-source-file":"dialog.tsx"})}function c({...e}){return(0,a.jsx)(n.ZL,{"data-slot":"dialog-portal",...e,"data-sentry-element":"DialogPrimitive.Portal","data-sentry-component":"DialogPortal","data-sentry-source-file":"dialog.tsx"})}function o({className:e,...t}){return(0,a.jsx)(n.hJ,{"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t,"data-sentry-element":"DialogPrimitive.Overlay","data-sentry-component":"DialogOverlay","data-sentry-source-file":"dialog.tsx"})}function m({className:e,children:t,...s}){return(0,a.jsxs)(c,{"data-slot":"dialog-portal","data-sentry-element":"DialogPortal","data-sentry-component":"DialogContent","data-sentry-source-file":"dialog.tsx",children:[(0,a.jsx)(o,{"data-sentry-element":"DialogOverlay","data-sentry-source-file":"dialog.tsx"}),(0,a.jsxs)(n.UC,{"data-slot":"dialog-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...s,"data-sentry-element":"DialogPrimitive.Content","data-sentry-source-file":"dialog.tsx",children:[t,(0,a.jsxs)(n.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4","data-sentry-element":"DialogPrimitive.Close","data-sentry-source-file":"dialog.tsx",children:[(0,a.jsx)(r.A,{"data-sentry-element":"XIcon","data-sentry-source-file":"dialog.tsx"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function u({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t,"data-sentry-component":"DialogHeader","data-sentry-source-file":"dialog.tsx"})}function x({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"dialog-footer",className:(0,i.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t,"data-sentry-component":"DialogFooter","data-sentry-source-file":"dialog.tsx"})}function p({className:e,...t}){return(0,a.jsx)(n.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",e),...t,"data-sentry-element":"DialogPrimitive.Title","data-sentry-component":"DialogTitle","data-sentry-source-file":"dialog.tsx"})}function h({className:e,...t}){return(0,a.jsx)(n.VY,{"data-slot":"dialog-description",className:(0,i.cn)("text-muted-foreground text-sm",e),...t,"data-sentry-element":"DialogPrimitive.Description","data-sentry-component":"DialogDescription","data-sentry-source-file":"dialog.tsx"})}}};
//# sourceMappingURL=9646.js.map