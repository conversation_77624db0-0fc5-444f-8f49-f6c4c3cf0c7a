{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/api/chapters/[id]", "regex": "^/api/chapters/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/chapters/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/class-enrollments/[id]", "regex": "^/api/class\\-enrollments/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/class\\-enrollments/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/classes/[id]", "regex": "^/api/classes/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/classes/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/courses/[id]", "regex": "^/api/courses/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/courses/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/enrollments/[id]", "regex": "^/api/enrollments/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/enrollments/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/institutions/[id]", "regex": "^/api/institutions/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/institutions/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/modules/[id]", "regex": "^/api/modules/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/modules/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/questions/[id]", "regex": "^/api/questions/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/questions/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/quizzes/[id]", "regex": "^/api/quizzes/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/quizzes/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/users/[id]", "regex": "^/api/users/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/users/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/auth/sign-in/[[...sign-in]]", "regex": "^/auth/sign\\-in(?:/(.+?))?(?:/)?$", "routeKeys": {"nxtPsignin": "nxtPsign-in"}, "namedRegex": "^/auth/sign\\-in(?:/(?<nxtPsignin>.+?))?(?:/)?$"}, {"page": "/auth/sign-up/[[...sign-up]]", "regex": "^/auth/sign\\-up(?:/(.+?))?(?:/)?$", "routeKeys": {"nxtPsignup": "nxtPsign-up"}, "namedRegex": "^/auth/sign\\-up(?:/(?<nxtPsignup>.+?))?(?:/)?$"}, {"page": "/dashboard/admin/institutions/[id]", "regex": "^/dashboard/admin/institutions/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/admin/institutions/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/dashboard/admin/users/[id]", "regex": "^/dashboard/admin/users/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/admin/users/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/dashboard/product/[productId]", "regex": "^/dashboard/product/([^/]+?)(?:/)?$", "routeKeys": {"nxtPproductId": "nxtPproductId"}, "namedRegex": "^/dashboard/product/(?<nxtPproductId>[^/]+?)(?:/)?$"}, {"page": "/dashboard/profile/[[...profile]]", "regex": "^/dashboard/profile(?:/(.+?))?(?:/)?$", "routeKeys": {"nxtPprofile": "nxtPprofile"}, "namedRegex": "^/dashboard/profile(?:/(?<nxtPprofile>.+?))?(?:/)?$"}, {"page": "/dashboard/student/courses/[id]", "regex": "^/dashboard/student/courses/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/student/courses/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/dashboard/teacher/classes/[id]", "regex": "^/dashboard/teacher/classes/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/teacher/classes/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/dashboard/teacher/classes/[id]/courses", "regex": "^/dashboard/teacher/classes/([^/]+?)/courses(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/teacher/classes/(?<nxtPid>[^/]+?)/courses(?:/)?$"}, {"page": "/dashboard/teacher/classes/[id]/students", "regex": "^/dashboard/teacher/classes/([^/]+?)/students(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/teacher/classes/(?<nxtPid>[^/]+?)/students(?:/)?$"}, {"page": "/dashboard/teacher/courses/[id]", "regex": "^/dashboard/teacher/courses/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/teacher/courses/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/my-courses/[courseId]", "regex": "^/my\\-courses/([^/]+?)(?:/)?$", "routeKeys": {"nxtPcourseId": "nxtPcourseId"}, "namedRegex": "^/my\\-courses/(?<nxtPcourseId>[^/]+?)(?:/)?$"}, {"page": "/my-courses/[courseId]/exam", "regex": "^/my\\-courses/([^/]+?)/exam(?:/)?$", "routeKeys": {"nxtPcourseId": "nxtPcourseId"}, "namedRegex": "^/my\\-courses/(?<nxtPcourseId>[^/]+?)/exam(?:/)?$"}, {"page": "/my-courses/[courseId]/exam/results", "regex": "^/my\\-courses/([^/]+?)/exam/results(?:/)?$", "routeKeys": {"nxtPcourseId": "nxtPcourseId"}, "namedRegex": "^/my\\-courses/(?<nxtPcourseId>[^/]+?)/exam/results(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/courses", "regex": "^/courses(?:/)?$", "routeKeys": {}, "namedRegex": "^/courses(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/dashboard/admin", "regex": "^/dashboard/admin(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/admin(?:/)?$"}, {"page": "/dashboard/admin/institutions", "regex": "^/dashboard/admin/institutions(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/admin/institutions(?:/)?$"}, {"page": "/dashboard/admin/institutions/new", "regex": "^/dashboard/admin/institutions/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/admin/institutions/new(?:/)?$"}, {"page": "/dashboard/admin/subscriptions", "regex": "^/dashboard/admin/subscriptions(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/admin/subscriptions(?:/)?$"}, {"page": "/dashboard/admin/users", "regex": "^/dashboard/admin/users(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/admin/users(?:/)?$"}, {"page": "/dashboard/admin/users/new", "regex": "^/dashboard/admin/users/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/admin/users/new(?:/)?$"}, {"page": "/dashboard/kanban", "regex": "^/dashboard/kanban(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/kanban(?:/)?$"}, {"page": "/dashboard/overview", "regex": "^/dashboard/overview(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/overview(?:/)?$"}, {"page": "/dashboard/product", "regex": "^/dashboard/product(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/product(?:/)?$"}, {"page": "/dashboard/student", "regex": "^/dashboard/student(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/student(?:/)?$"}, {"page": "/dashboard/student/certificates", "regex": "^/dashboard/student/certificates(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/student/certificates(?:/)?$"}, {"page": "/dashboard/student/courses", "regex": "^/dashboard/student/courses(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/student/courses(?:/)?$"}, {"page": "/dashboard/student/progress", "regex": "^/dashboard/student/progress(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/student/progress(?:/)?$"}, {"page": "/dashboard/teacher", "regex": "^/dashboard/teacher(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/teacher(?:/)?$"}, {"page": "/dashboard/teacher/classes", "regex": "^/dashboard/teacher/classes(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/teacher/classes(?:/)?$"}, {"page": "/dashboard/teacher/classes/new", "regex": "^/dashboard/teacher/classes/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/teacher/classes/new(?:/)?$"}, {"page": "/dashboard/teacher/courses", "regex": "^/dashboard/teacher/courses(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/teacher/courses(?:/)?$"}, {"page": "/dashboard/teacher/courses/generate", "regex": "^/dashboard/teacher/courses/generate(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/teacher/courses/generate(?:/)?$"}, {"page": "/dashboard/teacher/courses/new", "regex": "^/dashboard/teacher/courses/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/teacher/courses/new(?:/)?$"}, {"page": "/dashboard/teacher/reports", "regex": "^/dashboard/teacher/reports(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/teacher/reports(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/icon.png", "regex": "^/icon\\.png(?:/)?$", "routeKeys": {}, "namedRegex": "^/icon\\.png(?:/)?$"}, {"page": "/my-courses", "regex": "^/my\\-courses(?:/)?$", "routeKeys": {}, "namedRegex": "^/my\\-courses(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": [{"source": "/monitoring(/?)", "has": [{"type": "query", "key": "o", "value": "(?<orgid>\\d*)"}, {"type": "query", "key": "p", "value": "(?<projectid>\\d*)"}, {"type": "query", "key": "r", "value": "(?<region>[a-z]{2})"}], "destination": "https://o:orgid.ingest.:region.sentry.io/api/:projectid/envelope/?hsts=0", "regex": "^/monitoring(/?)(?:/)?$"}, {"source": "/monitoring(/?)", "has": [{"type": "query", "key": "o", "value": "(?<orgid>\\d*)"}, {"type": "query", "key": "p", "value": "(?<projectid>\\d*)"}], "destination": "https://o:orgid.ingest.sentry.io/api/:projectid/envelope/?hsts=0", "regex": "^/monitoring(/?)(?:/)?$"}]}