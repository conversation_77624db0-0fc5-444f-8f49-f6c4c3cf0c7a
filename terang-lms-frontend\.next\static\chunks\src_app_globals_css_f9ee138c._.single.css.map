{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/globals.css"], "sourcesContent": ["/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */\n@layer properties;\n@layer theme, base, components, utilities;\n@layer theme {\n  :root, :host {\n    --font-sans: ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\",\n      \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\",\n      \"Courier New\", monospace;\n    --color-red-50: oklch(97.1% 0.013 17.38);\n    --color-red-100: oklch(93.6% 0.032 17.717);\n    --color-red-200: oklch(88.5% 0.062 18.334);\n    --color-red-300: oklch(80.8% 0.114 19.571);\n    --color-red-400: oklch(70.4% 0.191 22.216);\n    --color-red-500: oklch(63.7% 0.237 25.331);\n    --color-red-600: oklch(57.7% 0.245 27.325);\n    --color-red-700: oklch(50.5% 0.213 27.518);\n    --color-red-800: oklch(44.4% 0.177 26.899);\n    --color-orange-50: oklch(98% 0.016 73.684);\n    --color-orange-100: oklch(95.4% 0.038 75.164);\n    --color-orange-400: oklch(75% 0.183 55.934);\n    --color-orange-600: oklch(64.6% 0.222 41.116);\n    --color-orange-700: oklch(55.3% 0.195 38.402);\n    --color-orange-800: oklch(47% 0.157 37.304);\n    --color-amber-50: oklch(98.7% 0.022 95.277);\n    --color-amber-200: oklch(92.4% 0.12 95.746);\n    --color-amber-500: oklch(76.9% 0.188 70.08);\n    --color-amber-600: oklch(66.6% 0.179 58.318);\n    --color-amber-700: oklch(55.5% 0.163 48.998);\n    --color-amber-800: oklch(47.3% 0.137 46.201);\n    --color-yellow-50: oklch(98.7% 0.026 102.212);\n    --color-yellow-100: oklch(97.3% 0.071 103.193);\n    --color-yellow-200: oklch(94.5% 0.129 101.54);\n    --color-yellow-300: oklch(90.5% 0.182 98.111);\n    --color-yellow-400: oklch(85.2% 0.199 91.936);\n    --color-yellow-500: oklch(79.5% 0.184 86.047);\n    --color-yellow-600: oklch(68.1% 0.162 75.834);\n    --color-yellow-700: oklch(55.4% 0.135 66.442);\n    --color-yellow-800: oklch(47.6% 0.114 61.907);\n    --color-lime-50: oklch(98.6% 0.031 120.757);\n    --color-lime-600: oklch(64.8% 0.2 131.684);\n    --color-green-50: oklch(98.2% 0.018 155.826);\n    --color-green-100: oklch(96.2% 0.044 156.743);\n    --color-green-200: oklch(92.5% 0.084 155.995);\n    --color-green-300: oklch(87.1% 0.15 154.449);\n    --color-green-400: oklch(79.2% 0.209 151.711);\n    --color-green-500: oklch(72.3% 0.219 149.579);\n    --color-green-600: oklch(62.7% 0.194 149.214);\n    --color-green-700: oklch(52.7% 0.154 150.069);\n    --color-green-800: oklch(44.8% 0.119 151.328);\n    --color-green-900: oklch(39.3% 0.095 152.535);\n    --color-sky-50: oklch(97.7% 0.013 236.62);\n    --color-sky-300: oklch(82.8% 0.111 230.318);\n    --color-sky-600: oklch(58.8% 0.158 241.966);\n    --color-sky-900: oklch(39.1% 0.09 240.876);\n    --color-blue-50: oklch(97% 0.014 254.604);\n    --color-blue-100: oklch(93.2% 0.032 255.585);\n    --color-blue-200: oklch(88.2% 0.059 254.128);\n    --color-blue-300: oklch(80.9% 0.105 251.813);\n    --color-blue-400: oklch(70.7% 0.165 254.624);\n    --color-blue-500: oklch(62.3% 0.214 259.815);\n    --color-blue-600: oklch(54.6% 0.245 262.881);\n    --color-blue-700: oklch(48.8% 0.243 264.376);\n    --color-blue-800: oklch(42.4% 0.199 265.638);\n    --color-blue-900: oklch(37.9% 0.146 265.522);\n    --color-indigo-50: oklch(96.2% 0.018 272.314);\n    --color-indigo-100: oklch(93% 0.034 272.788);\n    --color-indigo-600: oklch(51.1% 0.262 276.966);\n    --color-purple-50: oklch(97.7% 0.014 308.299);\n    --color-purple-100: oklch(94.6% 0.033 307.174);\n    --color-purple-200: oklch(90.2% 0.063 306.703);\n    --color-purple-400: oklch(71.4% 0.203 305.504);\n    --color-purple-600: oklch(55.8% 0.288 302.321);\n    --color-purple-700: oklch(49.6% 0.265 301.924);\n    --color-purple-800: oklch(43.8% 0.218 303.724);\n    --color-slate-50: oklch(98.4% 0.003 247.858);\n    --color-slate-800: oklch(27.9% 0.041 260.031);\n    --color-slate-900: oklch(20.8% 0.042 265.755);\n    --color-gray-50: oklch(98.5% 0.002 247.839);\n    --color-gray-100: oklch(96.7% 0.003 264.542);\n    --color-gray-200: oklch(92.8% 0.006 264.531);\n    --color-gray-300: oklch(87.2% 0.01 258.338);\n    --color-gray-400: oklch(70.7% 0.022 261.325);\n    --color-gray-500: oklch(55.1% 0.027 264.364);\n    --color-gray-600: oklch(44.6% 0.03 256.802);\n    --color-gray-700: oklch(37.3% 0.034 259.733);\n    --color-gray-800: oklch(27.8% 0.033 256.848);\n    --color-gray-900: oklch(21% 0.034 264.665);\n    --color-neutral-50: oklch(98.5% 0 0);\n    --color-neutral-500: oklch(55.6% 0 0);\n    --color-neutral-600: oklch(43.9% 0 0);\n    --color-black: #000;\n    --color-white: #fff;\n    --spacing: 0.25rem;\n    --container-xs: 20rem;\n    --container-sm: 24rem;\n    --container-md: 28rem;\n    --container-lg: 32rem;\n    --container-xl: 36rem;\n    --container-2xl: 42rem;\n    --container-3xl: 48rem;\n    --container-4xl: 56rem;\n    --container-6xl: 72rem;\n    --container-7xl: 80rem;\n    --text-xs: 0.75rem;\n    --text-xs--line-height: calc(1 / 0.75);\n    --text-sm: 0.875rem;\n    --text-sm--line-height: calc(1.25 / 0.875);\n    --text-base: 1rem;\n    --text-base--line-height: calc(1.5 / 1);\n    --text-lg: 1.125rem;\n    --text-lg--line-height: calc(1.75 / 1.125);\n    --text-xl: 1.25rem;\n    --text-xl--line-height: calc(1.75 / 1.25);\n    --text-2xl: 1.5rem;\n    --text-2xl--line-height: calc(2 / 1.5);\n    --text-3xl: 1.875rem;\n    --text-3xl--line-height: calc(2.25 / 1.875);\n    --text-4xl: 2.25rem;\n    --text-4xl--line-height: calc(2.5 / 2.25);\n    --text-5xl: 3rem;\n    --text-5xl--line-height: 1;\n    --text-6xl: 3.75rem;\n    --text-6xl--line-height: 1;\n    --font-weight-normal: 400;\n    --font-weight-medium: 500;\n    --font-weight-semibold: 600;\n    --font-weight-bold: 700;\n    --font-weight-extrabold: 800;\n    --font-weight-black: 900;\n    --tracking-tight: -0.025em;\n    --tracking-wide: 0.025em;\n    --tracking-widest: 0.1em;\n    --leading-tight: 1.25;\n    --leading-relaxed: 1.625;\n    --radius-xs: 0.125rem;\n    --radius-2xl: 1rem;\n    --radius-3xl: 1.5rem;\n    --drop-shadow-lg: 0 4px 4px rgb(0 0 0 / 0.15);\n    --ease-out: cubic-bezier(0, 0, 0.2, 1);\n    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);\n    --animate-spin: spin 1s linear infinite;\n    --animate-ping: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;\n    --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n    --animate-bounce: bounce 1s infinite;\n    --blur-sm: 8px;\n    --blur-md: 12px;\n    --aspect-video: 16 / 9;\n    --default-transition-duration: 150ms;\n    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n    --default-font-family: var(--font-sans);\n    --default-mono-font-family: var(--font-mono);\n    --color-border: var(--border);\n  }\n}\n@layer base {\n  *, ::after, ::before, ::backdrop, ::file-selector-button {\n    box-sizing: border-box;\n    margin: 0;\n    padding: 0;\n    border: 0 solid;\n  }\n  html, :host {\n    line-height: 1.5;\n    -webkit-text-size-adjust: 100%;\n    tab-size: 4;\n    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\");\n    font-feature-settings: var(--default-font-feature-settings, normal);\n    font-variation-settings: var(--default-font-variation-settings, normal);\n    -webkit-tap-highlight-color: transparent;\n  }\n  hr {\n    height: 0;\n    color: inherit;\n    border-top-width: 1px;\n  }\n  abbr:where([title]) {\n    -webkit-text-decoration: underline dotted;\n    text-decoration: underline dotted;\n  }\n  h1, h2, h3, h4, h5, h6 {\n    font-size: inherit;\n    font-weight: inherit;\n  }\n  a {\n    color: inherit;\n    -webkit-text-decoration: inherit;\n    text-decoration: inherit;\n  }\n  b, strong {\n    font-weight: bolder;\n  }\n  code, kbd, samp, pre {\n    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace);\n    font-feature-settings: var(--default-mono-font-feature-settings, normal);\n    font-variation-settings: var(--default-mono-font-variation-settings, normal);\n    font-size: 1em;\n  }\n  small {\n    font-size: 80%;\n  }\n  sub, sup {\n    font-size: 75%;\n    line-height: 0;\n    position: relative;\n    vertical-align: baseline;\n  }\n  sub {\n    bottom: -0.25em;\n  }\n  sup {\n    top: -0.5em;\n  }\n  table {\n    text-indent: 0;\n    border-color: inherit;\n    border-collapse: collapse;\n  }\n  :-moz-focusring {\n    outline: auto;\n  }\n  progress {\n    vertical-align: baseline;\n  }\n  summary {\n    display: list-item;\n  }\n  ol, ul, menu {\n    list-style: none;\n  }\n  img, svg, video, canvas, audio, iframe, embed, object {\n    display: block;\n    vertical-align: middle;\n  }\n  img, video {\n    max-width: 100%;\n    height: auto;\n  }\n  button, input, select, optgroup, textarea, ::file-selector-button {\n    font: inherit;\n    font-feature-settings: inherit;\n    font-variation-settings: inherit;\n    letter-spacing: inherit;\n    color: inherit;\n    border-radius: 0;\n    background-color: transparent;\n    opacity: 1;\n  }\n  :where(select:is([multiple], [size])) optgroup {\n    font-weight: bolder;\n  }\n  :where(select:is([multiple], [size])) optgroup option {\n    padding-inline-start: 20px;\n  }\n  ::file-selector-button {\n    margin-inline-end: 4px;\n  }\n  ::placeholder {\n    opacity: 1;\n  }\n  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {\n    ::placeholder {\n      color: currentcolor;\n      @supports (color: color-mix(in lab, red, red)) {\n        color: color-mix(in oklab, currentcolor 50%, transparent);\n      }\n    }\n  }\n  textarea {\n    resize: vertical;\n  }\n  ::-webkit-search-decoration {\n    -webkit-appearance: none;\n  }\n  ::-webkit-date-and-time-value {\n    min-height: 1lh;\n    text-align: inherit;\n  }\n  ::-webkit-datetime-edit {\n    display: inline-flex;\n  }\n  ::-webkit-datetime-edit-fields-wrapper {\n    padding: 0;\n  }\n  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {\n    padding-block: 0;\n  }\n  :-moz-ui-invalid {\n    box-shadow: none;\n  }\n  button, input:where([type=\"button\"], [type=\"reset\"], [type=\"submit\"]), ::file-selector-button {\n    appearance: button;\n  }\n  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {\n    height: auto;\n  }\n  [hidden]:where(:not([hidden=\"until-found\"])) {\n    display: none !important;\n  }\n}\n@layer utilities {\n  .\\@container\\/card {\n    container-type: inline-size;\n    container-name: card;\n  }\n  .\\@container\\/card-header {\n    container-type: inline-size;\n    container-name: card-header;\n  }\n  .pointer-events-none {\n    pointer-events: none;\n  }\n  .invisible {\n    visibility: hidden;\n  }\n  .visible {\n    visibility: visible;\n  }\n  .sr-only {\n    position: absolute;\n    width: 1px;\n    height: 1px;\n    padding: 0;\n    margin: -1px;\n    overflow: hidden;\n    clip: rect(0, 0, 0, 0);\n    white-space: nowrap;\n    border-width: 0;\n  }\n  .absolute {\n    position: absolute;\n  }\n  .fixed {\n    position: fixed;\n  }\n  .relative {\n    position: relative;\n  }\n  .static {\n    position: static;\n  }\n  .sticky {\n    position: sticky;\n  }\n  .inset-0 {\n    inset: calc(var(--spacing) * 0);\n  }\n  .inset-x-0 {\n    inset-inline: calc(var(--spacing) * 0);\n  }\n  .inset-y-0 {\n    inset-block: calc(var(--spacing) * 0);\n  }\n  .-top-0\\.5 {\n    top: calc(var(--spacing) * -0.5);\n  }\n  .-top-1 {\n    top: calc(var(--spacing) * -1);\n  }\n  .-top-4 {\n    top: calc(var(--spacing) * -4);\n  }\n  .top-0 {\n    top: calc(var(--spacing) * 0);\n  }\n  .top-1\\.5 {\n    top: calc(var(--spacing) * 1.5);\n  }\n  .top-1\\/2 {\n    top: calc(1/2 * 100%);\n  }\n  .top-2 {\n    top: calc(var(--spacing) * 2);\n  }\n  .top-2\\.5 {\n    top: calc(var(--spacing) * 2.5);\n  }\n  .top-3 {\n    top: calc(var(--spacing) * 3);\n  }\n  .top-3\\.5 {\n    top: calc(var(--spacing) * 3.5);\n  }\n  .top-4 {\n    top: calc(var(--spacing) * 4);\n  }\n  .top-24 {\n    top: calc(var(--spacing) * 24);\n  }\n  .top-\\[0\\.3rem\\] {\n    top: 0.3rem;\n  }\n  .top-\\[1px\\] {\n    top: 1px;\n  }\n  .top-\\[50\\%\\] {\n    top: 50%;\n  }\n  .top-\\[60\\%\\] {\n    top: 60%;\n  }\n  .top-full {\n    top: 100%;\n  }\n  .-right-0\\.5 {\n    right: calc(var(--spacing) * -0.5);\n  }\n  .-right-1 {\n    right: calc(var(--spacing) * -1);\n  }\n  .-right-4 {\n    right: calc(var(--spacing) * -4);\n  }\n  .right-0 {\n    right: calc(var(--spacing) * 0);\n  }\n  .right-1 {\n    right: calc(var(--spacing) * 1);\n  }\n  .right-2 {\n    right: calc(var(--spacing) * 2);\n  }\n  .right-3 {\n    right: calc(var(--spacing) * 3);\n  }\n  .right-4 {\n    right: calc(var(--spacing) * 4);\n  }\n  .right-8 {\n    right: calc(var(--spacing) * 8);\n  }\n  .right-\\[0\\.3rem\\] {\n    right: 0.3rem;\n  }\n  .-bottom-1 {\n    bottom: calc(var(--spacing) * -1);\n  }\n  .-bottom-4 {\n    bottom: calc(var(--spacing) * -4);\n  }\n  .bottom-0 {\n    bottom: calc(var(--spacing) * 0);\n  }\n  .bottom-4 {\n    bottom: calc(var(--spacing) * 4);\n  }\n  .-left-4 {\n    left: calc(var(--spacing) * -4);\n  }\n  .-left-8 {\n    left: calc(var(--spacing) * -8);\n  }\n  .left-0 {\n    left: calc(var(--spacing) * 0);\n  }\n  .left-1 {\n    left: calc(var(--spacing) * 1);\n  }\n  .left-1\\/2 {\n    left: calc(1/2 * 100%);\n  }\n  .left-2 {\n    left: calc(var(--spacing) * 2);\n  }\n  .left-3 {\n    left: calc(var(--spacing) * 3);\n  }\n  .left-\\[50\\%\\] {\n    left: 50%;\n  }\n  .isolate {\n    isolation: isolate;\n  }\n  .z-5 {\n    z-index: 5;\n  }\n  .z-10 {\n    z-index: 10;\n  }\n  .z-20 {\n    z-index: 20;\n  }\n  .z-50 {\n    z-index: 50;\n  }\n  .z-99999 {\n    z-index: 99999;\n  }\n  .z-\\[-1\\]\\! {\n    z-index: -1 !important;\n  }\n  .z-\\[1\\] {\n    z-index: 1;\n  }\n  .col-span-4 {\n    grid-column: span 4 / span 4;\n  }\n  .col-span-full {\n    grid-column: 1 / -1;\n  }\n  .col-start-2 {\n    grid-column-start: 2;\n  }\n  .row-span-2 {\n    grid-row: span 2 / span 2;\n  }\n  .row-start-1 {\n    grid-row-start: 1;\n  }\n  .container {\n    width: 100%;\n    @media (width >= 40rem) {\n      max-width: 40rem;\n    }\n    @media (width >= 48rem) {\n      max-width: 48rem;\n    }\n    @media (width >= 64rem) {\n      max-width: 64rem;\n    }\n    @media (width >= 80rem) {\n      max-width: 80rem;\n    }\n    @media (width >= 96rem) {\n      max-width: 96rem;\n    }\n  }\n  .-mx-1 {\n    margin-inline: calc(var(--spacing) * -1);\n  }\n  .mx-0\\.5 {\n    margin-inline: calc(var(--spacing) * 0.5);\n  }\n  .mx-2 {\n    margin-inline: calc(var(--spacing) * 2);\n  }\n  .mx-3\\.5 {\n    margin-inline: calc(var(--spacing) * 3.5);\n  }\n  .mx-4 {\n    margin-inline: calc(var(--spacing) * 4);\n  }\n  .mx-auto {\n    margin-inline: auto;\n  }\n  .my-0\\.5 {\n    margin-block: calc(var(--spacing) * 0.5);\n  }\n  .my-1 {\n    margin-block: calc(var(--spacing) * 1);\n  }\n  .my-2 {\n    margin-block: calc(var(--spacing) * 2);\n  }\n  .my-3 {\n    margin-block: calc(var(--spacing) * 3);\n  }\n  .my-4 {\n    margin-block: calc(var(--spacing) * 4);\n  }\n  .my-6 {\n    margin-block: calc(var(--spacing) * 6);\n  }\n  .prose {\n    color: var(--tw-prose-body);\n    max-width: 65ch;\n    :where(p):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 1.25em;\n      margin-bottom: 1.25em;\n    }\n    :where([class~=\"lead\"]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-lead);\n      font-size: 1.25em;\n      line-height: 1.6;\n      margin-top: 1.2em;\n      margin-bottom: 1.2em;\n    }\n    :where(a):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-links);\n      text-decoration: underline;\n      font-weight: 500;\n    }\n    :where(strong):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-bold);\n      font-weight: 600;\n    }\n    :where(a strong):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: inherit;\n    }\n    :where(blockquote strong):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: inherit;\n    }\n    :where(thead th strong):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: inherit;\n    }\n    :where(ol):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      list-style-type: decimal;\n      margin-top: 1.25em;\n      margin-bottom: 1.25em;\n      padding-inline-start: 1.625em;\n    }\n    :where(ol[type=\"A\"]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      list-style-type: upper-alpha;\n    }\n    :where(ol[type=\"a\"]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      list-style-type: lower-alpha;\n    }\n    :where(ol[type=\"A\" s]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      list-style-type: upper-alpha;\n    }\n    :where(ol[type=\"a\" s]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      list-style-type: lower-alpha;\n    }\n    :where(ol[type=\"I\"]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      list-style-type: upper-roman;\n    }\n    :where(ol[type=\"i\"]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      list-style-type: lower-roman;\n    }\n    :where(ol[type=\"I\" s]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      list-style-type: upper-roman;\n    }\n    :where(ol[type=\"i\" s]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      list-style-type: lower-roman;\n    }\n    :where(ol[type=\"1\"]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      list-style-type: decimal;\n    }\n    :where(ul):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      list-style-type: disc;\n      margin-top: 1.25em;\n      margin-bottom: 1.25em;\n      padding-inline-start: 1.625em;\n    }\n    :where(ol > li):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *))::marker {\n      font-weight: 400;\n      color: var(--tw-prose-counters);\n    }\n    :where(ul > li):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *))::marker {\n      color: var(--tw-prose-bullets);\n    }\n    :where(dt):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-headings);\n      font-weight: 600;\n      margin-top: 1.25em;\n    }\n    :where(hr):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      border-color: var(--tw-prose-hr);\n      border-top-width: 1;\n      margin-top: 3em;\n      margin-bottom: 3em;\n    }\n    :where(blockquote):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      font-weight: 500;\n      font-style: italic;\n      color: var(--tw-prose-quotes);\n      border-inline-start-width: 0.25rem;\n      border-inline-start-color: var(--tw-prose-quote-borders);\n      quotes: \"\\201C\"\"\\201D\"\"\\2018\"\"\\2019\";\n      margin-top: 1.6em;\n      margin-bottom: 1.6em;\n      padding-inline-start: 1em;\n    }\n    :where(blockquote p:first-of-type):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *))::before {\n      content: open-quote;\n    }\n    :where(blockquote p:last-of-type):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *))::after {\n      content: close-quote;\n    }\n    :where(h1):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-headings);\n      font-weight: 800;\n      font-size: 2.25em;\n      margin-top: 0;\n      margin-bottom: 0.8888889em;\n      line-height: 1.1111111;\n    }\n    :where(h1 strong):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      font-weight: 900;\n      color: inherit;\n    }\n    :where(h2):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-headings);\n      font-weight: 700;\n      font-size: 1.5em;\n      margin-top: 2em;\n      margin-bottom: 1em;\n      line-height: 1.3333333;\n    }\n    :where(h2 strong):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      font-weight: 800;\n      color: inherit;\n    }\n    :where(h3):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-headings);\n      font-weight: 600;\n      font-size: 1.25em;\n      margin-top: 1.6em;\n      margin-bottom: 0.6em;\n      line-height: 1.6;\n    }\n    :where(h3 strong):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      font-weight: 700;\n      color: inherit;\n    }\n    :where(h4):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-headings);\n      font-weight: 600;\n      margin-top: 1.5em;\n      margin-bottom: 0.5em;\n      line-height: 1.5;\n    }\n    :where(h4 strong):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      font-weight: 700;\n      color: inherit;\n    }\n    :where(img):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 2em;\n      margin-bottom: 2em;\n    }\n    :where(picture):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      display: block;\n      margin-top: 2em;\n      margin-bottom: 2em;\n    }\n    :where(video):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 2em;\n      margin-bottom: 2em;\n    }\n    :where(kbd):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      font-weight: 500;\n      font-family: inherit;\n      color: var(--tw-prose-kbd);\n      box-shadow: 0 0 0 1px rgb(var(--tw-prose-kbd-shadows) / 10%), 0 3px 0 rgb(var(--tw-prose-kbd-shadows) / 10%);\n      font-size: 0.875em;\n      border-radius: 0.3125rem;\n      padding-top: 0.1875em;\n      padding-inline-end: 0.375em;\n      padding-bottom: 0.1875em;\n      padding-inline-start: 0.375em;\n    }\n    :where(code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-code);\n      font-weight: 600;\n      font-size: 0.875em;\n    }\n    :where(code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *))::before {\n      content: \"`\";\n    }\n    :where(code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *))::after {\n      content: \"`\";\n    }\n    :where(a code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: inherit;\n    }\n    :where(h1 code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: inherit;\n    }\n    :where(h2 code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: inherit;\n      font-size: 0.875em;\n    }\n    :where(h3 code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: inherit;\n      font-size: 0.9em;\n    }\n    :where(h4 code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: inherit;\n    }\n    :where(blockquote code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: inherit;\n    }\n    :where(thead th code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: inherit;\n    }\n    :where(pre):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-pre-code);\n      background-color: var(--tw-prose-pre-bg);\n      overflow-x: auto;\n      font-weight: 400;\n      font-size: 0.875em;\n      line-height: 1.7142857;\n      margin-top: 1.7142857em;\n      margin-bottom: 1.7142857em;\n      border-radius: 0.375rem;\n      padding-top: 0.8571429em;\n      padding-inline-end: 1.1428571em;\n      padding-bottom: 0.8571429em;\n      padding-inline-start: 1.1428571em;\n    }\n    :where(pre code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      background-color: transparent;\n      border-width: 0;\n      border-radius: 0;\n      padding: 0;\n      font-weight: inherit;\n      color: inherit;\n      font-size: inherit;\n      font-family: inherit;\n      line-height: inherit;\n    }\n    :where(pre code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *))::before {\n      content: none;\n    }\n    :where(pre code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *))::after {\n      content: none;\n    }\n    :where(table):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      width: 100%;\n      table-layout: auto;\n      margin-top: 2em;\n      margin-bottom: 2em;\n      font-size: 0.875em;\n      line-height: 1.7142857;\n    }\n    :where(thead):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      border-bottom-width: 1px;\n      border-bottom-color: var(--tw-prose-th-borders);\n    }\n    :where(thead th):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-headings);\n      font-weight: 600;\n      vertical-align: bottom;\n      padding-inline-end: 0.5714286em;\n      padding-bottom: 0.5714286em;\n      padding-inline-start: 0.5714286em;\n    }\n    :where(tbody tr):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      border-bottom-width: 1px;\n      border-bottom-color: var(--tw-prose-td-borders);\n    }\n    :where(tbody tr:last-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      border-bottom-width: 0;\n    }\n    :where(tbody td):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      vertical-align: baseline;\n    }\n    :where(tfoot):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      border-top-width: 1px;\n      border-top-color: var(--tw-prose-th-borders);\n    }\n    :where(tfoot td):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      vertical-align: top;\n    }\n    :where(th, td):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      text-align: start;\n    }\n    :where(figure > *):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0;\n      margin-bottom: 0;\n    }\n    :where(figcaption):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-captions);\n      font-size: 0.875em;\n      line-height: 1.4285714;\n      margin-top: 0.8571429em;\n    }\n    --tw-prose-body: oklch(37.3% 0.034 259.733);\n    --tw-prose-headings: oklch(21% 0.034 264.665);\n    --tw-prose-lead: oklch(44.6% 0.03 256.802);\n    --tw-prose-links: oklch(21% 0.034 264.665);\n    --tw-prose-bold: oklch(21% 0.034 264.665);\n    --tw-prose-counters: oklch(55.1% 0.027 264.364);\n    --tw-prose-bullets: oklch(87.2% 0.01 258.338);\n    --tw-prose-hr: oklch(92.8% 0.006 264.531);\n    --tw-prose-quotes: oklch(21% 0.034 264.665);\n    --tw-prose-quote-borders: oklch(92.8% 0.006 264.531);\n    --tw-prose-captions: oklch(55.1% 0.027 264.364);\n    --tw-prose-kbd: oklch(21% 0.034 264.665);\n    --tw-prose-kbd-shadows: NaN NaN NaN;\n    --tw-prose-code: oklch(21% 0.034 264.665);\n    --tw-prose-pre-code: oklch(92.8% 0.006 264.531);\n    --tw-prose-pre-bg: oklch(27.8% 0.033 256.848);\n    --tw-prose-th-borders: oklch(87.2% 0.01 258.338);\n    --tw-prose-td-borders: oklch(92.8% 0.006 264.531);\n    --tw-prose-invert-body: oklch(87.2% 0.01 258.338);\n    --tw-prose-invert-headings: #fff;\n    --tw-prose-invert-lead: oklch(70.7% 0.022 261.325);\n    --tw-prose-invert-links: #fff;\n    --tw-prose-invert-bold: #fff;\n    --tw-prose-invert-counters: oklch(70.7% 0.022 261.325);\n    --tw-prose-invert-bullets: oklch(44.6% 0.03 256.802);\n    --tw-prose-invert-hr: oklch(37.3% 0.034 259.733);\n    --tw-prose-invert-quotes: oklch(96.7% 0.003 264.542);\n    --tw-prose-invert-quote-borders: oklch(37.3% 0.034 259.733);\n    --tw-prose-invert-captions: oklch(70.7% 0.022 261.325);\n    --tw-prose-invert-kbd: #fff;\n    --tw-prose-invert-kbd-shadows: 255 255 255;\n    --tw-prose-invert-code: #fff;\n    --tw-prose-invert-pre-code: oklch(87.2% 0.01 258.338);\n    --tw-prose-invert-pre-bg: rgb(0 0 0 / 50%);\n    --tw-prose-invert-th-borders: oklch(44.6% 0.03 256.802);\n    --tw-prose-invert-td-borders: oklch(37.3% 0.034 259.733);\n    font-size: 1rem;\n    line-height: 1.75;\n    :where(picture > img):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0;\n      margin-bottom: 0;\n    }\n    :where(li):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0.5em;\n      margin-bottom: 0.5em;\n    }\n    :where(ol > li):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      padding-inline-start: 0.375em;\n    }\n    :where(ul > li):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      padding-inline-start: 0.375em;\n    }\n    :where(.prose > ul > li p):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0.75em;\n      margin-bottom: 0.75em;\n    }\n    :where(.prose > ul > li > p:first-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 1.25em;\n    }\n    :where(.prose > ul > li > p:last-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-bottom: 1.25em;\n    }\n    :where(.prose > ol > li > p:first-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 1.25em;\n    }\n    :where(.prose > ol > li > p:last-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-bottom: 1.25em;\n    }\n    :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0.75em;\n      margin-bottom: 0.75em;\n    }\n    :where(dl):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 1.25em;\n      margin-bottom: 1.25em;\n    }\n    :where(dd):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0.5em;\n      padding-inline-start: 1.625em;\n    }\n    :where(hr + *):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0;\n    }\n    :where(h2 + *):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0;\n    }\n    :where(h3 + *):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0;\n    }\n    :where(h4 + *):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0;\n    }\n    :where(thead th:first-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      padding-inline-start: 0;\n    }\n    :where(thead th:last-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      padding-inline-end: 0;\n    }\n    :where(tbody td, tfoot td):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      padding-top: 0.5714286em;\n      padding-inline-end: 0.5714286em;\n      padding-bottom: 0.5714286em;\n      padding-inline-start: 0.5714286em;\n    }\n    :where(tbody td:first-child, tfoot td:first-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      padding-inline-start: 0;\n    }\n    :where(tbody td:last-child, tfoot td:last-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      padding-inline-end: 0;\n    }\n    :where(figure):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 2em;\n      margin-bottom: 2em;\n    }\n    :where(.prose > :first-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0;\n    }\n    :where(.prose > :last-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-bottom: 0;\n    }\n  }\n  .prose-sm {\n    font-size: 0.875rem;\n    line-height: 1.7142857;\n    :where(p):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 1.1428571em;\n      margin-bottom: 1.1428571em;\n    }\n    :where([class~=\"lead\"]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      font-size: 1.2857143em;\n      line-height: 1.5555556;\n      margin-top: 0.8888889em;\n      margin-bottom: 0.8888889em;\n    }\n    :where(blockquote):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 1.3333333em;\n      margin-bottom: 1.3333333em;\n      padding-inline-start: 1.1111111em;\n    }\n    :where(h1):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      font-size: 2.1428571em;\n      margin-top: 0;\n      margin-bottom: 0.8em;\n      line-height: 1.2;\n    }\n    :where(h2):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      font-size: 1.4285714em;\n      margin-top: 1.6em;\n      margin-bottom: 0.8em;\n      line-height: 1.4;\n    }\n    :where(h3):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      font-size: 1.2857143em;\n      margin-top: 1.5555556em;\n      margin-bottom: 0.4444444em;\n      line-height: 1.5555556;\n    }\n    :where(h4):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 1.4285714em;\n      margin-bottom: 0.5714286em;\n      line-height: 1.4285714;\n    }\n    :where(img):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 1.7142857em;\n      margin-bottom: 1.7142857em;\n    }\n    :where(picture):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 1.7142857em;\n      margin-bottom: 1.7142857em;\n    }\n    :where(picture > img):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0;\n      margin-bottom: 0;\n    }\n    :where(video):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 1.7142857em;\n      margin-bottom: 1.7142857em;\n    }\n    :where(kbd):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      font-size: 0.8571429em;\n      border-radius: 0.3125rem;\n      padding-top: 0.1428571em;\n      padding-inline-end: 0.3571429em;\n      padding-bottom: 0.1428571em;\n      padding-inline-start: 0.3571429em;\n    }\n    :where(code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      font-size: 0.8571429em;\n    }\n    :where(h2 code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      font-size: 0.9em;\n    }\n    :where(h3 code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      font-size: 0.8888889em;\n    }\n    :where(pre):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      font-size: 0.8571429em;\n      line-height: 1.6666667;\n      margin-top: 1.6666667em;\n      margin-bottom: 1.6666667em;\n      border-radius: 0.25rem;\n      padding-top: 0.6666667em;\n      padding-inline-end: 1em;\n      padding-bottom: 0.6666667em;\n      padding-inline-start: 1em;\n    }\n    :where(ol):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 1.1428571em;\n      margin-bottom: 1.1428571em;\n      padding-inline-start: 1.5714286em;\n    }\n    :where(ul):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 1.1428571em;\n      margin-bottom: 1.1428571em;\n      padding-inline-start: 1.5714286em;\n    }\n    :where(li):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0.2857143em;\n      margin-bottom: 0.2857143em;\n    }\n    :where(ol > li):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      padding-inline-start: 0.4285714em;\n    }\n    :where(ul > li):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      padding-inline-start: 0.4285714em;\n    }\n    :where(.prose-sm > ul > li p):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0.5714286em;\n      margin-bottom: 0.5714286em;\n    }\n    :where(.prose-sm > ul > li > p:first-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 1.1428571em;\n    }\n    :where(.prose-sm > ul > li > p:last-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-bottom: 1.1428571em;\n    }\n    :where(.prose-sm > ol > li > p:first-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 1.1428571em;\n    }\n    :where(.prose-sm > ol > li > p:last-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-bottom: 1.1428571em;\n    }\n    :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0.5714286em;\n      margin-bottom: 0.5714286em;\n    }\n    :where(dl):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 1.1428571em;\n      margin-bottom: 1.1428571em;\n    }\n    :where(dt):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 1.1428571em;\n    }\n    :where(dd):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0.2857143em;\n      padding-inline-start: 1.5714286em;\n    }\n    :where(hr):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 2.8571429em;\n      margin-bottom: 2.8571429em;\n    }\n    :where(hr + *):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0;\n    }\n    :where(h2 + *):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0;\n    }\n    :where(h3 + *):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0;\n    }\n    :where(h4 + *):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0;\n    }\n    :where(table):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      font-size: 0.8571429em;\n      line-height: 1.5;\n    }\n    :where(thead th):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      padding-inline-end: 1em;\n      padding-bottom: 0.6666667em;\n      padding-inline-start: 1em;\n    }\n    :where(thead th:first-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      padding-inline-start: 0;\n    }\n    :where(thead th:last-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      padding-inline-end: 0;\n    }\n    :where(tbody td, tfoot td):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      padding-top: 0.6666667em;\n      padding-inline-end: 1em;\n      padding-bottom: 0.6666667em;\n      padding-inline-start: 1em;\n    }\n    :where(tbody td:first-child, tfoot td:first-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      padding-inline-start: 0;\n    }\n    :where(tbody td:last-child, tfoot td:last-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      padding-inline-end: 0;\n    }\n    :where(figure):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 1.7142857em;\n      margin-bottom: 1.7142857em;\n    }\n    :where(figure > *):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0;\n      margin-bottom: 0;\n    }\n    :where(figcaption):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      font-size: 0.8571429em;\n      line-height: 1.3333333;\n      margin-top: 0.6666667em;\n    }\n    :where(.prose-sm > :first-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0;\n    }\n    :where(.prose-sm > :last-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-bottom: 0;\n    }\n  }\n  .mt-0\\! {\n    margin-top: calc(var(--spacing) * 0) !important;\n  }\n  .mt-0\\.5 {\n    margin-top: calc(var(--spacing) * 0.5);\n  }\n  .mt-1 {\n    margin-top: calc(var(--spacing) * 1);\n  }\n  .mt-1\\.5 {\n    margin-top: calc(var(--spacing) * 1.5);\n  }\n  .mt-2 {\n    margin-top: calc(var(--spacing) * 2);\n  }\n  .mt-3 {\n    margin-top: calc(var(--spacing) * 3);\n  }\n  .mt-4 {\n    margin-top: calc(var(--spacing) * 4);\n  }\n  .mt-6 {\n    margin-top: calc(var(--spacing) * 6);\n  }\n  .mt-8 {\n    margin-top: calc(var(--spacing) * 8);\n  }\n  .mt-12 {\n    margin-top: calc(var(--spacing) * 12);\n  }\n  .mt-16 {\n    margin-top: calc(var(--spacing) * 16);\n  }\n  .mt-20 {\n    margin-top: calc(var(--spacing) * 20);\n  }\n  .mt-64\\! {\n    margin-top: calc(var(--spacing) * 64) !important;\n  }\n  .mt-auto {\n    margin-top: auto;\n  }\n  .mr-1 {\n    margin-right: calc(var(--spacing) * 1);\n  }\n  .mr-2 {\n    margin-right: calc(var(--spacing) * 2);\n  }\n  .mr-3 {\n    margin-right: calc(var(--spacing) * 3);\n  }\n  .mr-auto {\n    margin-right: auto;\n  }\n  .mb-1 {\n    margin-bottom: calc(var(--spacing) * 1);\n  }\n  .mb-2 {\n    margin-bottom: calc(var(--spacing) * 2);\n  }\n  .mb-3 {\n    margin-bottom: calc(var(--spacing) * 3);\n  }\n  .mb-4 {\n    margin-bottom: calc(var(--spacing) * 4);\n  }\n  .mb-6 {\n    margin-bottom: calc(var(--spacing) * 6);\n  }\n  .mb-8 {\n    margin-bottom: calc(var(--spacing) * 8);\n  }\n  .mb-12 {\n    margin-bottom: calc(var(--spacing) * 12);\n  }\n  .mb-16 {\n    margin-bottom: calc(var(--spacing) * 16);\n  }\n  .-ml-1 {\n    margin-left: calc(var(--spacing) * -1);\n  }\n  .-ml-1\\.5 {\n    margin-left: calc(var(--spacing) * -1.5);\n  }\n  .-ml-2 {\n    margin-left: calc(var(--spacing) * -2);\n  }\n  .ml-1 {\n    margin-left: calc(var(--spacing) * 1);\n  }\n  .ml-2 {\n    margin-left: calc(var(--spacing) * 2);\n  }\n  .ml-4 {\n    margin-left: calc(var(--spacing) * 4);\n  }\n  .ml-6 {\n    margin-left: calc(var(--spacing) * 6);\n  }\n  .ml-auto {\n    margin-left: auto;\n  }\n  .line-clamp-1 {\n    overflow: hidden;\n    display: -webkit-box;\n    -webkit-box-orient: vertical;\n    -webkit-line-clamp: 1;\n  }\n  .line-clamp-2 {\n    overflow: hidden;\n    display: -webkit-box;\n    -webkit-box-orient: vertical;\n    -webkit-line-clamp: 2;\n  }\n  .line-clamp-3 {\n    overflow: hidden;\n    display: -webkit-box;\n    -webkit-box-orient: vertical;\n    -webkit-line-clamp: 3;\n  }\n  .block {\n    display: block;\n  }\n  .contents {\n    display: contents;\n  }\n  .flex {\n    display: flex;\n  }\n  .grid {\n    display: grid;\n  }\n  .hidden {\n    display: none;\n  }\n  .inline {\n    display: inline;\n  }\n  .inline-block {\n    display: inline-block;\n  }\n  .inline-flex {\n    display: inline-flex;\n  }\n  .table {\n    display: table;\n  }\n  .table-caption {\n    display: table-caption;\n  }\n  .table-cell {\n    display: table-cell;\n  }\n  .table-row {\n    display: table-row;\n  }\n  .field-sizing-content {\n    field-sizing: content;\n  }\n  .aspect-\\[4\\/3\\] {\n    aspect-ratio: 4/3;\n  }\n  .aspect-auto {\n    aspect-ratio: auto;\n  }\n  .aspect-square {\n    aspect-ratio: 1 / 1;\n  }\n  .aspect-video {\n    aspect-ratio: var(--aspect-video);\n  }\n  .size-2 {\n    width: calc(var(--spacing) * 2);\n    height: calc(var(--spacing) * 2);\n  }\n  .size-2\\.5 {\n    width: calc(var(--spacing) * 2.5);\n    height: calc(var(--spacing) * 2.5);\n  }\n  .size-3 {\n    width: calc(var(--spacing) * 3);\n    height: calc(var(--spacing) * 3);\n  }\n  .size-3\\.5 {\n    width: calc(var(--spacing) * 3.5);\n    height: calc(var(--spacing) * 3.5);\n  }\n  .size-4 {\n    width: calc(var(--spacing) * 4);\n    height: calc(var(--spacing) * 4);\n  }\n  .size-7 {\n    width: calc(var(--spacing) * 7);\n    height: calc(var(--spacing) * 7);\n  }\n  .size-8 {\n    width: calc(var(--spacing) * 8);\n    height: calc(var(--spacing) * 8);\n  }\n  .size-9 {\n    width: calc(var(--spacing) * 9);\n    height: calc(var(--spacing) * 9);\n  }\n  .size-full {\n    width: 100%;\n    height: 100%;\n  }\n  .h-1 {\n    height: calc(var(--spacing) * 1);\n  }\n  .h-1\\.5 {\n    height: calc(var(--spacing) * 1.5);\n  }\n  .h-2 {\n    height: calc(var(--spacing) * 2);\n  }\n  .h-2\\.5 {\n    height: calc(var(--spacing) * 2.5);\n  }\n  .h-3 {\n    height: calc(var(--spacing) * 3);\n  }\n  .h-4 {\n    height: calc(var(--spacing) * 4);\n  }\n  .h-5 {\n    height: calc(var(--spacing) * 5);\n  }\n  .h-6 {\n    height: calc(var(--spacing) * 6);\n  }\n  .h-7 {\n    height: calc(var(--spacing) * 7);\n  }\n  .h-8 {\n    height: calc(var(--spacing) * 8);\n  }\n  .h-9 {\n    height: calc(var(--spacing) * 9);\n  }\n  .h-10 {\n    height: calc(var(--spacing) * 10);\n  }\n  .h-11 {\n    height: calc(var(--spacing) * 11);\n  }\n  .h-12 {\n    height: calc(var(--spacing) * 12);\n  }\n  .h-16 {\n    height: calc(var(--spacing) * 16);\n  }\n  .h-20 {\n    height: calc(var(--spacing) * 20);\n  }\n  .h-24 {\n    height: calc(var(--spacing) * 24);\n  }\n  .h-32 {\n    height: calc(var(--spacing) * 32);\n  }\n  .h-48 {\n    height: calc(var(--spacing) * 48);\n  }\n  .h-52 {\n    height: calc(var(--spacing) * 52);\n  }\n  .h-64 {\n    height: calc(var(--spacing) * 64);\n  }\n  .h-96 {\n    height: calc(var(--spacing) * 96);\n  }\n  .h-\\[1\\.15rem\\] {\n    height: 1.15rem;\n  }\n  .h-\\[1px\\] {\n    height: 1px;\n  }\n  .h-\\[75vh\\] {\n    height: 75vh;\n  }\n  .h-\\[90vh\\] {\n    height: 90vh;\n  }\n  .h-\\[250px\\] {\n    height: 250px;\n  }\n  .h-\\[280px\\] {\n    height: 280px;\n  }\n  .h-\\[300px\\] {\n    height: 300px;\n  }\n  .h-\\[316px\\] {\n    height: 316px;\n  }\n  .h-\\[400px\\] {\n    height: 400px;\n  }\n  .h-\\[calc\\(100dvh-52px\\)\\] {\n    height: calc(100dvh - 52px);\n  }\n  .h-\\[calc\\(100vh-64px\\)\\] {\n    height: calc(100vh - 64px);\n  }\n  .h-\\[calc\\(100vh-210px\\)\\] {\n    height: calc(100vh - 210px);\n  }\n  .h-\\[var\\(--radix-navigation-menu-viewport-height\\)\\] {\n    height: var(--radix-navigation-menu-viewport-height);\n  }\n  .h-\\[var\\(--radix-select-trigger-height\\)\\] {\n    height: var(--radix-select-trigger-height);\n  }\n  .h-auto {\n    height: auto;\n  }\n  .h-fit {\n    height: fit-content;\n  }\n  .h-full {\n    height: 100%;\n  }\n  .h-px {\n    height: 1px;\n  }\n  .h-screen {\n    height: 100vh;\n  }\n  .h-svh {\n    height: 100svh;\n  }\n  .max-h-\\(--radix-context-menu-content-available-height\\) {\n    max-height: var(--radix-context-menu-content-available-height);\n  }\n  .max-h-\\(--radix-dropdown-menu-content-available-height\\) {\n    max-height: var(--radix-dropdown-menu-content-available-height);\n  }\n  .max-h-\\(--radix-select-content-available-height\\) {\n    max-height: var(--radix-select-content-available-height);\n  }\n  .max-h-8 {\n    max-height: calc(var(--spacing) * 8);\n  }\n  .max-h-16 {\n    max-height: calc(var(--spacing) * 16);\n  }\n  .max-h-32 {\n    max-height: calc(var(--spacing) * 32);\n  }\n  .max-h-48 {\n    max-height: calc(var(--spacing) * 48);\n  }\n  .max-h-96 {\n    max-height: calc(var(--spacing) * 96);\n  }\n  .max-h-\\[18\\.75rem\\] {\n    max-height: 18.75rem;\n  }\n  .max-h-\\[60vh\\] {\n    max-height: 60vh;\n  }\n  .max-h-\\[70vh\\] {\n    max-height: 70vh;\n  }\n  .max-h-\\[75vh\\] {\n    max-height: 75vh;\n  }\n  .max-h-\\[80vh\\] {\n    max-height: 80vh;\n  }\n  .max-h-\\[85vh\\] {\n    max-height: 85vh;\n  }\n  .max-h-\\[90vh\\] {\n    max-height: 90vh;\n  }\n  .max-h-\\[95vh\\] {\n    max-height: 95vh;\n  }\n  .max-h-\\[300px\\] {\n    max-height: 300px;\n  }\n  .max-h-\\[400px\\] {\n    max-height: 400px;\n  }\n  .max-h-\\[calc\\(100vh-12rem\\)\\] {\n    max-height: calc(100vh - 12rem);\n  }\n  .max-h-full {\n    max-height: 100%;\n  }\n  .min-h-0 {\n    min-height: calc(var(--spacing) * 0);\n  }\n  .min-h-4 {\n    min-height: calc(var(--spacing) * 4);\n  }\n  .min-h-16 {\n    min-height: calc(var(--spacing) * 16);\n  }\n  .min-h-\\[32px\\] {\n    min-height: 32px;\n  }\n  .min-h-\\[60px\\] {\n    min-height: 60px;\n  }\n  .min-h-\\[200px\\] {\n    min-height: 200px;\n  }\n  .min-h-\\[400px\\] {\n    min-height: 400px;\n  }\n  .min-h-\\[calc\\(100vh-200px\\)\\] {\n    min-height: calc(100vh - 200px);\n  }\n  .min-h-full {\n    min-height: 100%;\n  }\n  .min-h-screen {\n    min-height: 100vh;\n  }\n  .min-h-svh {\n    min-height: 100svh;\n  }\n  .w-\\(--radix-dropdown-menu-trigger-width\\) {\n    width: var(--radix-dropdown-menu-trigger-width);\n  }\n  .w-\\(--sidebar-width\\) {\n    width: var(--sidebar-width);\n  }\n  .w-0 {\n    width: calc(var(--spacing) * 0);\n  }\n  .w-1 {\n    width: calc(var(--spacing) * 1);\n  }\n  .w-1\\.5 {\n    width: calc(var(--spacing) * 1.5);\n  }\n  .w-1\\/2 {\n    width: calc(1/2 * 100%);\n  }\n  .w-2 {\n    width: calc(var(--spacing) * 2);\n  }\n  .w-2\\.5 {\n    width: calc(var(--spacing) * 2.5);\n  }\n  .w-2\\/3 {\n    width: calc(2/3 * 100%);\n  }\n  .w-3 {\n    width: calc(var(--spacing) * 3);\n  }\n  .w-3\\/4 {\n    width: calc(3/4 * 100%);\n  }\n  .w-4 {\n    width: calc(var(--spacing) * 4);\n  }\n  .w-5 {\n    width: calc(var(--spacing) * 5);\n  }\n  .w-6 {\n    width: calc(var(--spacing) * 6);\n  }\n  .w-7 {\n    width: calc(var(--spacing) * 7);\n  }\n  .w-8 {\n    width: calc(var(--spacing) * 8);\n  }\n  .w-9 {\n    width: calc(var(--spacing) * 9);\n  }\n  .w-10 {\n    width: calc(var(--spacing) * 10);\n  }\n  .w-12 {\n    width: calc(var(--spacing) * 12);\n  }\n  .w-16 {\n    width: calc(var(--spacing) * 16);\n  }\n  .w-20 {\n    width: calc(var(--spacing) * 20);\n  }\n  .w-24 {\n    width: calc(var(--spacing) * 24);\n  }\n  .w-28 {\n    width: calc(var(--spacing) * 28);\n  }\n  .w-32 {\n    width: calc(var(--spacing) * 32);\n  }\n  .w-40 {\n    width: calc(var(--spacing) * 40);\n  }\n  .w-44 {\n    width: calc(var(--spacing) * 44);\n  }\n  .w-48 {\n    width: calc(var(--spacing) * 48);\n  }\n  .w-56 {\n    width: calc(var(--spacing) * 56);\n  }\n  .w-64 {\n    width: calc(var(--spacing) * 64);\n  }\n  .w-72 {\n    width: calc(var(--spacing) * 72);\n  }\n  .w-80 {\n    width: calc(var(--spacing) * 80);\n  }\n  .w-96 {\n    width: calc(var(--spacing) * 96);\n  }\n  .w-\\[--radix-dropdown-menu-trigger-width\\] {\n    width: --radix-dropdown-menu-trigger-width;\n  }\n  .w-\\[1px\\] {\n    width: 1px;\n  }\n  .w-\\[4\\.5rem\\] {\n    width: 4.5rem;\n  }\n  .w-\\[12\\.5rem\\] {\n    width: 12.5rem;\n  }\n  .w-\\[70px\\] {\n    width: 70px;\n  }\n  .w-\\[80px\\] {\n    width: 80px;\n  }\n  .w-\\[90vw\\] {\n    width: 90vw;\n  }\n  .w-\\[95vw\\] {\n    width: 95vw;\n  }\n  .w-\\[100px\\] {\n    width: 100px;\n  }\n  .w-\\[120px\\] {\n    width: 120px;\n  }\n  .w-\\[140px\\] {\n    width: 140px;\n  }\n  .w-\\[150px\\] {\n    width: 150px;\n  }\n  .w-\\[160px\\] {\n    width: 160px;\n  }\n  .w-\\[180px\\] {\n    width: 180px;\n  }\n  .w-\\[200px\\] {\n    width: 200px;\n  }\n  .w-\\[250px\\] {\n    width: 250px;\n  }\n  .w-\\[300px\\] {\n    width: 300px;\n  }\n  .w-\\[350px\\] {\n    width: 350px;\n  }\n  .w-auto {\n    width: auto;\n  }\n  .w-fit {\n    width: fit-content;\n  }\n  .w-full {\n    width: 100%;\n  }\n  .w-max {\n    width: max-content;\n  }\n  .w-px {\n    width: 1px;\n  }\n  .max-w-\\(--skeleton-width\\) {\n    max-width: var(--skeleton-width);\n  }\n  .max-w-2xl {\n    max-width: var(--container-2xl);\n  }\n  .max-w-3xl {\n    max-width: var(--container-3xl);\n  }\n  .max-w-4xl {\n    max-width: var(--container-4xl);\n  }\n  .max-w-6xl {\n    max-width: var(--container-6xl);\n  }\n  .max-w-7xl {\n    max-width: var(--container-7xl);\n  }\n  .max-w-\\[600px\\] {\n    max-width: 600px;\n  }\n  .max-w-\\[calc\\(100\\%-2rem\\)\\] {\n    max-width: calc(100% - 2rem);\n  }\n  .max-w-full {\n    max-width: 100%;\n  }\n  .max-w-max {\n    max-width: max-content;\n  }\n  .max-w-md {\n    max-width: var(--container-md);\n  }\n  .max-w-none {\n    max-width: none;\n  }\n  .max-w-sm {\n    max-width: var(--container-sm);\n  }\n  .max-w-xl {\n    max-width: var(--container-xl);\n  }\n  .max-w-xs {\n    max-width: var(--container-xs);\n  }\n  .min-w-0 {\n    min-width: calc(var(--spacing) * 0);\n  }\n  .min-w-5 {\n    min-width: calc(var(--spacing) * 5);\n  }\n  .min-w-8 {\n    min-width: calc(var(--spacing) * 8);\n  }\n  .min-w-9 {\n    min-width: calc(var(--spacing) * 9);\n  }\n  .min-w-10 {\n    min-width: calc(var(--spacing) * 10);\n  }\n  .min-w-56 {\n    min-width: calc(var(--spacing) * 56);\n  }\n  .min-w-\\[8rem\\] {\n    min-width: 8rem;\n  }\n  .min-w-\\[12rem\\] {\n    min-width: 12rem;\n  }\n  .min-w-\\[120px\\] {\n    min-width: 120px;\n  }\n  .min-w-\\[300px\\] {\n    min-width: 300px;\n  }\n  .min-w-\\[320px\\] {\n    min-width: 320px;\n  }\n  .min-w-\\[var\\(--radix-select-trigger-width\\)\\] {\n    min-width: var(--radix-select-trigger-width);\n  }\n  .min-w-full {\n    min-width: 100%;\n  }\n  .flex-1 {\n    flex: 1;\n  }\n  .flex-none {\n    flex: none;\n  }\n  .flex-shrink-0 {\n    flex-shrink: 0;\n  }\n  .shrink-0 {\n    flex-shrink: 0;\n  }\n  .flex-grow {\n    flex-grow: 1;\n  }\n  .grow {\n    flex-grow: 1;\n  }\n  .caption-bottom {\n    caption-side: bottom;\n  }\n  .border-collapse {\n    border-collapse: collapse;\n  }\n  .origin-\\(--radix-context-menu-content-transform-origin\\) {\n    transform-origin: var(--radix-context-menu-content-transform-origin);\n  }\n  .origin-\\(--radix-dropdown-menu-content-transform-origin\\) {\n    transform-origin: var(--radix-dropdown-menu-content-transform-origin);\n  }\n  .origin-\\(--radix-hover-card-content-transform-origin\\) {\n    transform-origin: var(--radix-hover-card-content-transform-origin);\n  }\n  .origin-\\(--radix-menubar-content-transform-origin\\) {\n    transform-origin: var(--radix-menubar-content-transform-origin);\n  }\n  .origin-\\(--radix-popover-content-transform-origin\\) {\n    transform-origin: var(--radix-popover-content-transform-origin);\n  }\n  .origin-\\(--radix-select-content-transform-origin\\) {\n    transform-origin: var(--radix-select-content-transform-origin);\n  }\n  .origin-\\(--radix-tooltip-content-transform-origin\\) {\n    transform-origin: var(--radix-tooltip-content-transform-origin);\n  }\n  .-translate-x-1\\/2 {\n    --tw-translate-x: calc(calc(1/2 * 100%) * -1);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .-translate-x-px {\n    --tw-translate-x: -1px;\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-x-\\[-50\\%\\] {\n    --tw-translate-x: -50%;\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-x-px {\n    --tw-translate-x: 1px;\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .-translate-y-1\\/2 {\n    --tw-translate-y: calc(calc(1/2 * 100%) * -1);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .-translate-y-12\\! {\n    --tw-translate-y: calc(var(--spacing) * -12) !important;\n    translate: var(--tw-translate-x) var(--tw-translate-y) !important;\n  }\n  .translate-y-0\\.5 {\n    --tw-translate-y: calc(var(--spacing) * 0.5);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-y-\\[-50\\%\\] {\n    --tw-translate-y: -50%;\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-y-\\[calc\\(-50\\%_-_2px\\)\\] {\n    --tw-translate-y: calc(-50% - 2px);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .scale-100 {\n    --tw-scale-x: 100%;\n    --tw-scale-y: 100%;\n    --tw-scale-z: 100%;\n    scale: var(--tw-scale-x) var(--tw-scale-y);\n  }\n  .scale-125 {\n    --tw-scale-x: 125%;\n    --tw-scale-y: 125%;\n    --tw-scale-z: 125%;\n    scale: var(--tw-scale-x) var(--tw-scale-y);\n  }\n  .rotate-3 {\n    rotate: 3deg;\n  }\n  .rotate-45 {\n    rotate: 45deg;\n  }\n  .rotate-180 {\n    rotate: 180deg;\n  }\n  .transform {\n    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);\n  }\n  .animate-bounce {\n    animation: var(--animate-bounce);\n  }\n  .animate-caret-blink {\n    animation: caret-blink 1.25s ease-out infinite;\n  }\n  .animate-in {\n    animation: enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none);\n  }\n  .animate-ping {\n    animation: var(--animate-ping);\n  }\n  .animate-pulse {\n    animation: var(--animate-pulse);\n  }\n  .animate-spin {\n    animation: var(--animate-spin);\n  }\n  .cursor-default {\n    cursor: default;\n  }\n  .cursor-grab {\n    cursor: grab;\n  }\n  .cursor-move {\n    cursor: move;\n  }\n  .cursor-not-allowed {\n    cursor: not-allowed;\n  }\n  .cursor-pointer {\n    cursor: pointer;\n  }\n  .touch-none {\n    touch-action: none;\n  }\n  .resize-none {\n    resize: none;\n  }\n  .snap-none {\n    scroll-snap-type: none;\n  }\n  .snap-center {\n    scroll-snap-align: center;\n  }\n  .scroll-my-1 {\n    scroll-margin-block: calc(var(--spacing) * 1);\n  }\n  .scroll-mt-4 {\n    scroll-margin-top: calc(var(--spacing) * 4);\n  }\n  .scroll-mt-20 {\n    scroll-margin-top: calc(var(--spacing) * 20);\n  }\n  .scroll-py-1 {\n    scroll-padding-block: calc(var(--spacing) * 1);\n  }\n  .list-decimal {\n    list-style-type: decimal;\n  }\n  .list-disc {\n    list-style-type: disc;\n  }\n  .list-none {\n    list-style-type: none;\n  }\n  .grid-flow-col {\n    grid-auto-flow: column;\n  }\n  .auto-rows-min {\n    grid-auto-rows: min-content;\n  }\n  .grid-cols-1 {\n    grid-template-columns: repeat(1, minmax(0, 1fr));\n  }\n  .grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n  .grid-cols-3 {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n  .grid-cols-4 {\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\n  .grid-cols-5 {\n    grid-template-columns: repeat(5, minmax(0, 1fr));\n  }\n  .grid-cols-6 {\n    grid-template-columns: repeat(6, minmax(0, 1fr));\n  }\n  .grid-cols-\\[0_1fr\\] {\n    grid-template-columns: 0 1fr;\n  }\n  .grid-rows-\\[auto_auto\\] {\n    grid-template-rows: auto auto;\n  }\n  .flex-col {\n    flex-direction: column;\n  }\n  .flex-col-reverse {\n    flex-direction: column-reverse;\n  }\n  .flex-row {\n    flex-direction: row;\n  }\n  .flex-wrap {\n    flex-wrap: wrap;\n  }\n  .place-items-center {\n    place-items: center;\n  }\n  .items-center {\n    align-items: center;\n  }\n  .items-end {\n    align-items: flex-end;\n  }\n  .items-start {\n    align-items: flex-start;\n  }\n  .items-stretch {\n    align-items: stretch;\n  }\n  .justify-around {\n    justify-content: space-around;\n  }\n  .justify-between {\n    justify-content: space-between;\n  }\n  .justify-center {\n    justify-content: center;\n  }\n  .justify-end {\n    justify-content: flex-end;\n  }\n  .justify-start {\n    justify-content: flex-start;\n  }\n  .justify-items-start {\n    justify-items: start;\n  }\n  .gap-0 {\n    gap: calc(var(--spacing) * 0);\n  }\n  .gap-0\\.5 {\n    gap: calc(var(--spacing) * 0.5);\n  }\n  .gap-1 {\n    gap: calc(var(--spacing) * 1);\n  }\n  .gap-1\\.5 {\n    gap: calc(var(--spacing) * 1.5);\n  }\n  .gap-2 {\n    gap: calc(var(--spacing) * 2);\n  }\n  .gap-2\\.5 {\n    gap: calc(var(--spacing) * 2.5);\n  }\n  .gap-3 {\n    gap: calc(var(--spacing) * 3);\n  }\n  .gap-4 {\n    gap: calc(var(--spacing) * 4);\n  }\n  .gap-6 {\n    gap: calc(var(--spacing) * 6);\n  }\n  .gap-8 {\n    gap: calc(var(--spacing) * 8);\n  }\n  .gap-12 {\n    gap: calc(var(--spacing) * 12);\n  }\n  .space-y-0 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 0) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-1 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-2 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-3 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-4 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-6 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-8 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-px {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(1px * var(--tw-space-y-reverse));\n      margin-block-end: calc(1px * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .gap-x-4 {\n    column-gap: calc(var(--spacing) * 4);\n  }\n  .space-x-1 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-2 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-3 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-4 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-6 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 6) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-8 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-12 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 12) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 12) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .gap-y-0\\.5 {\n    row-gap: calc(var(--spacing) * 0.5);\n  }\n  .self-start {\n    align-self: flex-start;\n  }\n  .justify-self-end {\n    justify-self: flex-end;\n  }\n  .truncate {\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n  }\n  .overflow-auto {\n    overflow: auto;\n  }\n  .overflow-hidden {\n    overflow: hidden;\n  }\n  .overflow-x-auto {\n    overflow-x: auto;\n  }\n  .overflow-x-hidden {\n    overflow-x: hidden;\n  }\n  .overflow-y-auto {\n    overflow-y: auto;\n  }\n  .rounded {\n    border-radius: 0.25rem;\n  }\n  .rounded-2xl {\n    border-radius: var(--radius-2xl);\n  }\n  .rounded-3xl {\n    border-radius: var(--radius-3xl);\n  }\n  .rounded-\\[0\\.5rem\\] {\n    border-radius: 0.5rem;\n  }\n  .rounded-\\[2px\\] {\n    border-radius: 2px;\n  }\n  .rounded-\\[4px\\] {\n    border-radius: 4px;\n  }\n  .rounded-\\[inherit\\] {\n    border-radius: inherit;\n  }\n  .rounded-full {\n    border-radius: calc(infinity * 1px);\n  }\n  .rounded-lg {\n    border-radius: var(--radius);\n  }\n  .rounded-md {\n    border-radius: calc(var(--radius) - 2px);\n  }\n  .rounded-none {\n    border-radius: 0;\n  }\n  .rounded-sm {\n    border-radius: calc(var(--radius) - 4px);\n  }\n  .rounded-xl {\n    border-radius: calc(var(--radius) + 4px);\n  }\n  .rounded-xs {\n    border-radius: var(--radius-xs);\n  }\n  .rounded-t-lg {\n    border-top-left-radius: var(--radius);\n    border-top-right-radius: var(--radius);\n  }\n  .rounded-tl-sm {\n    border-top-left-radius: calc(var(--radius) - 4px);\n  }\n  .rounded-r {\n    border-top-right-radius: 0.25rem;\n    border-bottom-right-radius: 0.25rem;\n  }\n  .rounded-r-md {\n    border-top-right-radius: calc(var(--radius) - 2px);\n    border-bottom-right-radius: calc(var(--radius) - 2px);\n  }\n  .border {\n    border-style: var(--tw-border-style);\n    border-width: 1px;\n  }\n  .border-0 {\n    border-style: var(--tw-border-style);\n    border-width: 0px;\n  }\n  .border-2 {\n    border-style: var(--tw-border-style);\n    border-width: 2px;\n  }\n  .border-4 {\n    border-style: var(--tw-border-style);\n    border-width: 4px;\n  }\n  .border-\\[1\\.5px\\] {\n    border-style: var(--tw-border-style);\n    border-width: 1.5px;\n  }\n  .border-y {\n    border-block-style: var(--tw-border-style);\n    border-block-width: 1px;\n  }\n  .border-t {\n    border-top-style: var(--tw-border-style);\n    border-top-width: 1px;\n  }\n  .border-t-2 {\n    border-top-style: var(--tw-border-style);\n    border-top-width: 2px;\n  }\n  .border-r {\n    border-right-style: var(--tw-border-style);\n    border-right-width: 1px;\n  }\n  .border-b {\n    border-bottom-style: var(--tw-border-style);\n    border-bottom-width: 1px;\n  }\n  .border-b-2 {\n    border-bottom-style: var(--tw-border-style);\n    border-bottom-width: 2px;\n  }\n  .border-l {\n    border-left-style: var(--tw-border-style);\n    border-left-width: 1px;\n  }\n  .border-l-4 {\n    border-left-style: var(--tw-border-style);\n    border-left-width: 4px;\n  }\n  .border-dashed {\n    --tw-border-style: dashed;\n    border-style: dashed;\n  }\n  .border-none {\n    --tw-border-style: none;\n    border-style: none;\n  }\n  .border-\\(--color-border\\) {\n    border-color: var(--color-border);\n  }\n  .border-\\[var\\(--iai-primary\\)\\] {\n    border-color: var(--iai-primary);\n  }\n  .border-amber-200 {\n    border-color: var(--color-amber-200);\n  }\n  .border-amber-500 {\n    border-color: var(--color-amber-500);\n  }\n  .border-blue-200 {\n    border-color: var(--color-blue-200);\n  }\n  .border-blue-300 {\n    border-color: var(--color-blue-300);\n  }\n  .border-blue-400 {\n    border-color: var(--color-blue-400);\n  }\n  .border-blue-500 {\n    border-color: var(--color-blue-500);\n  }\n  .border-blue-600 {\n    border-color: var(--color-blue-600);\n  }\n  .border-border {\n    border-color: var(--border);\n  }\n  .border-border\\/50 {\n    border-color: var(--border);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--border) 50%, transparent);\n    }\n  }\n  .border-gray-100 {\n    border-color: var(--color-gray-100);\n  }\n  .border-gray-200 {\n    border-color: var(--color-gray-200);\n  }\n  .border-gray-300 {\n    border-color: var(--color-gray-300);\n  }\n  .border-gray-400 {\n    border-color: var(--color-gray-400);\n  }\n  .border-green-200 {\n    border-color: var(--color-green-200);\n  }\n  .border-green-300 {\n    border-color: var(--color-green-300);\n  }\n  .border-green-400 {\n    border-color: var(--color-green-400);\n  }\n  .border-green-500 {\n    border-color: var(--color-green-500);\n  }\n  .border-green-600 {\n    border-color: var(--color-green-600);\n  }\n  .border-input {\n    border-color: var(--input);\n  }\n  .border-muted-foreground\\/25 {\n    border-color: var(--muted-foreground);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--muted-foreground) 25%, transparent);\n    }\n  }\n  .border-muted-foreground\\/50 {\n    border-color: var(--muted-foreground);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--muted-foreground) 50%, transparent);\n    }\n  }\n  .border-orange-400 {\n    border-color: var(--color-orange-400);\n  }\n  .border-primary {\n    border-color: var(--primary);\n  }\n  .border-primary\\/30 {\n    border-color: var(--primary);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--primary) 30%, transparent);\n    }\n  }\n  .border-purple-200 {\n    border-color: var(--color-purple-200);\n  }\n  .border-purple-400 {\n    border-color: var(--color-purple-400);\n  }\n  .border-red-200 {\n    border-color: var(--color-red-200);\n  }\n  .border-red-300 {\n    border-color: var(--color-red-300);\n  }\n  .border-red-400 {\n    border-color: var(--color-red-400);\n  }\n  .border-red-500 {\n    border-color: var(--color-red-500);\n  }\n  .border-red-600 {\n    border-color: var(--color-red-600);\n  }\n  .border-secondary {\n    border-color: var(--secondary);\n  }\n  .border-sidebar-border {\n    border-color: var(--sidebar-border);\n  }\n  .border-sky-600 {\n    border-color: var(--color-sky-600);\n  }\n  .border-transparent {\n    border-color: transparent;\n  }\n  .border-white {\n    border-color: var(--color-white);\n  }\n  .border-yellow-200 {\n    border-color: var(--color-yellow-200);\n  }\n  .border-yellow-300 {\n    border-color: var(--color-yellow-300);\n  }\n  .border-yellow-400 {\n    border-color: var(--color-yellow-400);\n  }\n  .border-t-transparent {\n    border-top-color: transparent;\n  }\n  .border-l-blue-200 {\n    border-left-color: var(--color-blue-200);\n  }\n  .border-l-gray-300 {\n    border-left-color: var(--color-gray-300);\n  }\n  .border-l-green-400 {\n    border-left-color: var(--color-green-400);\n  }\n  .border-l-transparent {\n    border-left-color: transparent;\n  }\n  .bg-\\(--color-bg\\) {\n    background-color: var(--color-bg);\n  }\n  .bg-\\[var\\(--iai-primary\\)\\] {\n    background-color: var(--iai-primary);\n  }\n  .bg-accent {\n    background-color: var(--accent);\n  }\n  .bg-accent\\/50 {\n    background-color: var(--accent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--accent) 50%, transparent);\n    }\n  }\n  .bg-amber-50 {\n    background-color: var(--color-amber-50);\n  }\n  .bg-amber-500 {\n    background-color: var(--color-amber-500);\n  }\n  .bg-background {\n    background-color: var(--background);\n  }\n  .bg-background\\/80 {\n    background-color: var(--background);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--background) 80%, transparent);\n    }\n  }\n  .bg-black {\n    background-color: var(--color-black);\n  }\n  .bg-black\\/20 {\n    background-color: color-mix(in srgb, #000 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-black) 20%, transparent);\n    }\n  }\n  .bg-black\\/50 {\n    background-color: color-mix(in srgb, #000 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-black) 50%, transparent);\n    }\n  }\n  .bg-blue-50 {\n    background-color: var(--color-blue-50);\n  }\n  .bg-blue-50\\/50 {\n    background-color: color-mix(in srgb, oklch(97% 0.014 254.604) 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-blue-50) 50%, transparent);\n    }\n  }\n  .bg-blue-100 {\n    background-color: var(--color-blue-100);\n  }\n  .bg-blue-200 {\n    background-color: var(--color-blue-200);\n  }\n  .bg-blue-400 {\n    background-color: var(--color-blue-400);\n  }\n  .bg-blue-500 {\n    background-color: var(--color-blue-500);\n  }\n  .bg-blue-600 {\n    background-color: var(--color-blue-600);\n  }\n  .bg-border {\n    background-color: var(--border);\n  }\n  .bg-card {\n    background-color: var(--card);\n  }\n  .bg-current {\n    background-color: currentcolor;\n  }\n  .bg-destructive {\n    background-color: var(--destructive);\n  }\n  .bg-foreground {\n    background-color: var(--foreground);\n  }\n  .bg-gray-50 {\n    background-color: var(--color-gray-50);\n  }\n  .bg-gray-100 {\n    background-color: var(--color-gray-100);\n  }\n  .bg-gray-200 {\n    background-color: var(--color-gray-200);\n  }\n  .bg-gray-300 {\n    background-color: var(--color-gray-300);\n  }\n  .bg-gray-600 {\n    background-color: var(--color-gray-600);\n  }\n  .bg-gray-900 {\n    background-color: var(--color-gray-900);\n  }\n  .bg-green-50 {\n    background-color: var(--color-green-50);\n  }\n  .bg-green-50\\/30 {\n    background-color: color-mix(in srgb, oklch(98.2% 0.018 155.826) 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-green-50) 30%, transparent);\n    }\n  }\n  .bg-green-100 {\n    background-color: var(--color-green-100);\n  }\n  .bg-green-400 {\n    background-color: var(--color-green-400);\n  }\n  .bg-green-500 {\n    background-color: var(--color-green-500);\n  }\n  .bg-green-600 {\n    background-color: var(--color-green-600);\n  }\n  .bg-muted {\n    background-color: var(--muted);\n  }\n  .bg-muted\\/30 {\n    background-color: var(--muted);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--muted) 30%, transparent);\n    }\n  }\n  .bg-muted\\/50 {\n    background-color: var(--muted);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--muted) 50%, transparent);\n    }\n  }\n  .bg-orange-50 {\n    background-color: var(--color-orange-50);\n  }\n  .bg-orange-100 {\n    background-color: var(--color-orange-100);\n  }\n  .bg-popover {\n    background-color: var(--popover);\n  }\n  .bg-primary {\n    background-color: var(--primary);\n  }\n  .bg-primary\\/5 {\n    background-color: var(--primary);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--primary) 5%, transparent);\n    }\n  }\n  .bg-primary\\/20 {\n    background-color: var(--primary);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--primary) 20%, transparent);\n    }\n  }\n  .bg-purple-50 {\n    background-color: var(--color-purple-50);\n  }\n  .bg-purple-100 {\n    background-color: var(--color-purple-100);\n  }\n  .bg-purple-400 {\n    background-color: var(--color-purple-400);\n  }\n  .bg-purple-600 {\n    background-color: var(--color-purple-600);\n  }\n  .bg-red-50 {\n    background-color: var(--color-red-50);\n  }\n  .bg-red-100 {\n    background-color: var(--color-red-100);\n  }\n  .bg-red-500 {\n    background-color: var(--color-red-500);\n  }\n  .bg-red-600 {\n    background-color: var(--color-red-600);\n  }\n  .bg-secondary {\n    background-color: var(--secondary);\n  }\n  .bg-secondary\\/50 {\n    background-color: var(--secondary);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--secondary) 50%, transparent);\n    }\n  }\n  .bg-sidebar {\n    background-color: var(--sidebar);\n  }\n  .bg-sidebar-border {\n    background-color: var(--sidebar-border);\n  }\n  .bg-transparent {\n    background-color: transparent;\n  }\n  .bg-white {\n    background-color: var(--color-white);\n  }\n  .bg-white\\/10 {\n    background-color: color-mix(in srgb, #fff 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-white) 10%, transparent);\n    }\n  }\n  .bg-white\\/20 {\n    background-color: color-mix(in srgb, #fff 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-white) 20%, transparent);\n    }\n  }\n  .bg-white\\/40 {\n    background-color: color-mix(in srgb, #fff 40%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-white) 40%, transparent);\n    }\n  }\n  .bg-white\\/95 {\n    background-color: color-mix(in srgb, #fff 95%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-white) 95%, transparent);\n    }\n  }\n  .bg-yellow-50 {\n    background-color: var(--color-yellow-50);\n  }\n  .bg-yellow-100 {\n    background-color: var(--color-yellow-100);\n  }\n  .bg-yellow-400 {\n    background-color: var(--color-yellow-400);\n  }\n  .bg-yellow-500 {\n    background-color: var(--color-yellow-500);\n  }\n  .bg-yellow-600 {\n    background-color: var(--color-yellow-600);\n  }\n  .bg-linear-to-b {\n    --tw-gradient-position: to bottom;\n    @supports (background-image: linear-gradient(in lab, red, red)) {\n      --tw-gradient-position: to bottom in oklab;\n    }\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .bg-linear-to-t {\n    --tw-gradient-position: to top;\n    @supports (background-image: linear-gradient(in lab, red, red)) {\n      --tw-gradient-position: to top in oklab;\n    }\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .bg-gradient-to-b {\n    --tw-gradient-position: to bottom in oklab;\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .bg-gradient-to-bl {\n    --tw-gradient-position: to bottom left in oklab;\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .bg-gradient-to-br {\n    --tw-gradient-position: to bottom right in oklab;\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .bg-gradient-to-r {\n    --tw-gradient-position: to right in oklab;\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .bg-gradient-to-t {\n    --tw-gradient-position: to top in oklab;\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .from-blue-50 {\n    --tw-gradient-from: var(--color-blue-50);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-blue-100 {\n    --tw-gradient-from: var(--color-blue-100);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-blue-500 {\n    --tw-gradient-from: var(--color-blue-500);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-foreground {\n    --tw-gradient-from: var(--foreground);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-gray-400 {\n    --tw-gradient-from: var(--color-gray-400);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-gray-900 {\n    --tw-gradient-from: var(--color-gray-900);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-green-50 {\n    --tw-gradient-from: var(--color-green-50);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-primary\\/5 {\n    --tw-gradient-from: var(--primary);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--primary) 5%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-slate-50 {\n    --tw-gradient-from: var(--color-slate-50);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-slate-800 {\n    --tw-gradient-from: var(--color-slate-800);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-transparent {\n    --tw-gradient-from: transparent;\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-white {\n    --tw-gradient-from: var(--color-white);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-yellow-50 {\n    --tw-gradient-from: var(--color-yellow-50);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-yellow-400 {\n    --tw-gradient-from: var(--color-yellow-400);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .via-gray-800 {\n    --tw-gradient-via: var(--color-gray-800);\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .via-white {\n    --tw-gradient-via: var(--color-white);\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .to-black {\n    --tw-gradient-to: var(--color-black);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-blue-50 {\n    --tw-gradient-to: var(--color-blue-50);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-gray-600 {\n    --tw-gradient-to: var(--color-gray-600);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-indigo-50 {\n    --tw-gradient-to: var(--color-indigo-50);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-indigo-100 {\n    --tw-gradient-to: var(--color-indigo-100);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-indigo-600 {\n    --tw-gradient-to: var(--color-indigo-600);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-orange-50 {\n    --tw-gradient-to: var(--color-orange-50);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-primary\\/20 {\n    --tw-gradient-to: var(--primary);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--primary) 20%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-purple-600 {\n    --tw-gradient-to: var(--color-purple-600);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-slate-900 {\n    --tw-gradient-to: var(--color-slate-900);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-transparent {\n    --tw-gradient-to: transparent;\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-yellow-600 {\n    --tw-gradient-to: var(--color-yellow-600);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .bg-clip-text {\n    background-clip: text;\n  }\n  .fill-current {\n    fill: currentcolor;\n  }\n  .fill-foreground {\n    fill: var(--foreground);\n  }\n  .fill-muted-foreground {\n    fill: var(--muted-foreground);\n  }\n  .fill-primary {\n    fill: var(--primary);\n  }\n  .object-contain {\n    object-fit: contain;\n  }\n  .object-cover {\n    object-fit: cover;\n  }\n  .\\!p-0 {\n    padding: calc(var(--spacing) * 0) !important;\n  }\n  .p-0 {\n    padding: calc(var(--spacing) * 0);\n  }\n  .p-0\\! {\n    padding: calc(var(--spacing) * 0) !important;\n  }\n  .p-1 {\n    padding: calc(var(--spacing) * 1);\n  }\n  .p-1\\.5 {\n    padding: calc(var(--spacing) * 1.5);\n  }\n  .p-2 {\n    padding: calc(var(--spacing) * 2);\n  }\n  .p-3 {\n    padding: calc(var(--spacing) * 3);\n  }\n  .p-4 {\n    padding: calc(var(--spacing) * 4);\n  }\n  .p-6 {\n    padding: calc(var(--spacing) * 6);\n  }\n  .p-8 {\n    padding: calc(var(--spacing) * 8);\n  }\n  .p-12 {\n    padding: calc(var(--spacing) * 12);\n  }\n  .p-px {\n    padding: 1px;\n  }\n  .px-1 {\n    padding-inline: calc(var(--spacing) * 1);\n  }\n  .px-1\\.5 {\n    padding-inline: calc(var(--spacing) * 1.5);\n  }\n  .px-2 {\n    padding-inline: calc(var(--spacing) * 2);\n  }\n  .px-2\\.5 {\n    padding-inline: calc(var(--spacing) * 2.5);\n  }\n  .px-3 {\n    padding-inline: calc(var(--spacing) * 3);\n  }\n  .px-4 {\n    padding-inline: calc(var(--spacing) * 4);\n  }\n  .px-5 {\n    padding-inline: calc(var(--spacing) * 5);\n  }\n  .px-6 {\n    padding-inline: calc(var(--spacing) * 6);\n  }\n  .px-8 {\n    padding-inline: calc(var(--spacing) * 8);\n  }\n  .\\!py-0 {\n    padding-block: calc(var(--spacing) * 0) !important;\n  }\n  .py-0 {\n    padding-block: calc(var(--spacing) * 0);\n  }\n  .py-0\\.5 {\n    padding-block: calc(var(--spacing) * 0.5);\n  }\n  .py-1 {\n    padding-block: calc(var(--spacing) * 1);\n  }\n  .py-1\\.5 {\n    padding-block: calc(var(--spacing) * 1.5);\n  }\n  .py-2 {\n    padding-block: calc(var(--spacing) * 2);\n  }\n  .py-2\\.5 {\n    padding-block: calc(var(--spacing) * 2.5);\n  }\n  .py-3 {\n    padding-block: calc(var(--spacing) * 3);\n  }\n  .py-4 {\n    padding-block: calc(var(--spacing) * 4);\n  }\n  .py-5 {\n    padding-block: calc(var(--spacing) * 5);\n  }\n  .py-6 {\n    padding-block: calc(var(--spacing) * 6);\n  }\n  .py-8 {\n    padding-block: calc(var(--spacing) * 8);\n  }\n  .py-12 {\n    padding-block: calc(var(--spacing) * 12);\n  }\n  .py-16 {\n    padding-block: calc(var(--spacing) * 16);\n  }\n  .\\!pt-3 {\n    padding-top: calc(var(--spacing) * 3) !important;\n  }\n  .pt-0 {\n    padding-top: calc(var(--spacing) * 0);\n  }\n  .pt-1 {\n    padding-top: calc(var(--spacing) * 1);\n  }\n  .pt-2 {\n    padding-top: calc(var(--spacing) * 2);\n  }\n  .pt-3 {\n    padding-top: calc(var(--spacing) * 3);\n  }\n  .pt-4 {\n    padding-top: calc(var(--spacing) * 4);\n  }\n  .pt-5 {\n    padding-top: calc(var(--spacing) * 5);\n  }\n  .pt-6 {\n    padding-top: calc(var(--spacing) * 6);\n  }\n  .pt-8 {\n    padding-top: calc(var(--spacing) * 8);\n  }\n  .pt-12 {\n    padding-top: calc(var(--spacing) * 12);\n  }\n  .pt-20 {\n    padding-top: calc(var(--spacing) * 20);\n  }\n  .pr-1 {\n    padding-right: calc(var(--spacing) * 1);\n  }\n  .pr-2 {\n    padding-right: calc(var(--spacing) * 2);\n  }\n  .pr-2\\.5 {\n    padding-right: calc(var(--spacing) * 2.5);\n  }\n  .pr-4 {\n    padding-right: calc(var(--spacing) * 4);\n  }\n  .pr-8 {\n    padding-right: calc(var(--spacing) * 8);\n  }\n  .pr-10 {\n    padding-right: calc(var(--spacing) * 10);\n  }\n  .pr-20 {\n    padding-right: calc(var(--spacing) * 20);\n  }\n  .pb-0 {\n    padding-bottom: calc(var(--spacing) * 0);\n  }\n  .pb-2 {\n    padding-bottom: calc(var(--spacing) * 2);\n  }\n  .pb-3 {\n    padding-bottom: calc(var(--spacing) * 3);\n  }\n  .pb-4 {\n    padding-bottom: calc(var(--spacing) * 4);\n  }\n  .pb-6 {\n    padding-bottom: calc(var(--spacing) * 6);\n  }\n  .pb-8 {\n    padding-bottom: calc(var(--spacing) * 8);\n  }\n  .pl-2 {\n    padding-left: calc(var(--spacing) * 2);\n  }\n  .pl-4 {\n    padding-left: calc(var(--spacing) * 4);\n  }\n  .pl-7 {\n    padding-left: calc(var(--spacing) * 7);\n  }\n  .pl-8 {\n    padding-left: calc(var(--spacing) * 8);\n  }\n  .pl-9 {\n    padding-left: calc(var(--spacing) * 9);\n  }\n  .pl-10 {\n    padding-left: calc(var(--spacing) * 10);\n  }\n  .text-center {\n    text-align: center;\n  }\n  .text-left {\n    text-align: left;\n  }\n  .text-right {\n    text-align: right;\n  }\n  .align-middle {\n    vertical-align: middle;\n  }\n  .font-mono {\n    font-family: var(--font-mono);\n  }\n  .font-sans {\n    font-family: var(--font-sans);\n  }\n  .text-2xl {\n    font-size: var(--text-2xl);\n    line-height: var(--tw-leading, var(--text-2xl--line-height));\n  }\n  .text-3xl {\n    font-size: var(--text-3xl);\n    line-height: var(--tw-leading, var(--text-3xl--line-height));\n  }\n  .text-4xl {\n    font-size: var(--text-4xl);\n    line-height: var(--tw-leading, var(--text-4xl--line-height));\n  }\n  .text-6xl {\n    font-size: var(--text-6xl);\n    line-height: var(--tw-leading, var(--text-6xl--line-height));\n  }\n  .text-base {\n    font-size: var(--text-base);\n    line-height: var(--tw-leading, var(--text-base--line-height));\n  }\n  .text-lg {\n    font-size: var(--text-lg);\n    line-height: var(--tw-leading, var(--text-lg--line-height));\n  }\n  .text-sm {\n    font-size: var(--text-sm);\n    line-height: var(--tw-leading, var(--text-sm--line-height));\n  }\n  .text-xl {\n    font-size: var(--text-xl);\n    line-height: var(--tw-leading, var(--text-xl--line-height));\n  }\n  .text-xs {\n    font-size: var(--text-xs);\n    line-height: var(--tw-leading, var(--text-xs--line-height));\n  }\n  .text-\\[0\\.8rem\\] {\n    font-size: 0.8rem;\n  }\n  .text-\\[10px\\] {\n    font-size: 10px;\n  }\n  .text-\\[10rem\\] {\n    font-size: 10rem;\n  }\n  .leading-none {\n    --tw-leading: 1;\n    line-height: 1;\n  }\n  .leading-relaxed {\n    --tw-leading: var(--leading-relaxed);\n    line-height: var(--leading-relaxed);\n  }\n  .leading-tight {\n    --tw-leading: var(--leading-tight);\n    line-height: var(--leading-tight);\n  }\n  .font-black {\n    --tw-font-weight: var(--font-weight-black);\n    font-weight: var(--font-weight-black);\n  }\n  .font-bold {\n    --tw-font-weight: var(--font-weight-bold);\n    font-weight: var(--font-weight-bold);\n  }\n  .font-extrabold {\n    --tw-font-weight: var(--font-weight-extrabold);\n    font-weight: var(--font-weight-extrabold);\n  }\n  .font-medium {\n    --tw-font-weight: var(--font-weight-medium);\n    font-weight: var(--font-weight-medium);\n  }\n  .font-normal {\n    --tw-font-weight: var(--font-weight-normal);\n    font-weight: var(--font-weight-normal);\n  }\n  .font-semibold {\n    --tw-font-weight: var(--font-weight-semibold);\n    font-weight: var(--font-weight-semibold);\n  }\n  .tracking-tight {\n    --tw-tracking: var(--tracking-tight);\n    letter-spacing: var(--tracking-tight);\n  }\n  .tracking-wide {\n    --tw-tracking: var(--tracking-wide);\n    letter-spacing: var(--tracking-wide);\n  }\n  .tracking-widest {\n    --tw-tracking: var(--tracking-widest);\n    letter-spacing: var(--tracking-widest);\n  }\n  .text-balance {\n    text-wrap: balance;\n  }\n  .break-words {\n    overflow-wrap: break-word;\n  }\n  .whitespace-nowrap {\n    white-space: nowrap;\n  }\n  .whitespace-pre-wrap {\n    white-space: pre-wrap;\n  }\n  .text-\\[var\\(--iai-primary\\)\\] {\n    color: var(--iai-primary);\n  }\n  .text-accent-foreground {\n    color: var(--accent-foreground);\n  }\n  .text-amber-600 {\n    color: var(--color-amber-600);\n  }\n  .text-amber-700 {\n    color: var(--color-amber-700);\n  }\n  .text-amber-800 {\n    color: var(--color-amber-800);\n  }\n  .text-blue-200 {\n    color: var(--color-blue-200);\n  }\n  .text-blue-500 {\n    color: var(--color-blue-500);\n  }\n  .text-blue-600 {\n    color: var(--color-blue-600);\n  }\n  .text-blue-700 {\n    color: var(--color-blue-700);\n  }\n  .text-blue-800 {\n    color: var(--color-blue-800);\n  }\n  .text-blue-900 {\n    color: var(--color-blue-900);\n  }\n  .text-card-foreground {\n    color: var(--card-foreground);\n  }\n  .text-current {\n    color: currentcolor;\n  }\n  .text-destructive {\n    color: var(--destructive);\n  }\n  .text-foreground {\n    color: var(--foreground);\n  }\n  .text-foreground\\/80 {\n    color: var(--foreground);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--foreground) 80%, transparent);\n    }\n  }\n  .text-gray-100 {\n    color: var(--color-gray-100);\n  }\n  .text-gray-200 {\n    color: var(--color-gray-200);\n  }\n  .text-gray-400 {\n    color: var(--color-gray-400);\n  }\n  .text-gray-500 {\n    color: var(--color-gray-500);\n  }\n  .text-gray-600 {\n    color: var(--color-gray-600);\n  }\n  .text-gray-700 {\n    color: var(--color-gray-700);\n  }\n  .text-gray-800 {\n    color: var(--color-gray-800);\n  }\n  .text-gray-900 {\n    color: var(--color-gray-900);\n  }\n  .text-green-400 {\n    color: var(--color-green-400);\n  }\n  .text-green-500 {\n    color: var(--color-green-500);\n  }\n  .text-green-600 {\n    color: var(--color-green-600);\n  }\n  .text-green-700 {\n    color: var(--color-green-700);\n  }\n  .text-green-800 {\n    color: var(--color-green-800);\n  }\n  .text-green-900 {\n    color: var(--color-green-900);\n  }\n  .text-muted-foreground {\n    color: var(--muted-foreground);\n  }\n  .text-muted-foreground\\/70 {\n    color: var(--muted-foreground);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--muted-foreground) 70%, transparent);\n    }\n  }\n  .text-orange-600 {\n    color: var(--color-orange-600);\n  }\n  .text-orange-700 {\n    color: var(--color-orange-700);\n  }\n  .text-orange-800 {\n    color: var(--color-orange-800);\n  }\n  .text-popover-foreground {\n    color: var(--popover-foreground);\n  }\n  .text-primary {\n    color: var(--primary);\n  }\n  .text-primary-foreground {\n    color: var(--primary-foreground);\n  }\n  .text-primary\\/50 {\n    color: var(--primary);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--primary) 50%, transparent);\n    }\n  }\n  .text-purple-600 {\n    color: var(--color-purple-600);\n  }\n  .text-purple-700 {\n    color: var(--color-purple-700);\n  }\n  .text-purple-800 {\n    color: var(--color-purple-800);\n  }\n  .text-red-500 {\n    color: var(--color-red-500);\n  }\n  .text-red-600 {\n    color: var(--color-red-600);\n  }\n  .text-red-700 {\n    color: var(--color-red-700);\n  }\n  .text-red-800 {\n    color: var(--color-red-800);\n  }\n  .text-secondary-foreground {\n    color: var(--secondary-foreground);\n  }\n  .text-secondary-foreground\\/50 {\n    color: var(--secondary-foreground);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--secondary-foreground) 50%, transparent);\n    }\n  }\n  .text-sidebar-foreground {\n    color: var(--sidebar-foreground);\n  }\n  .text-sidebar-foreground\\/70 {\n    color: var(--sidebar-foreground);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--sidebar-foreground) 70%, transparent);\n    }\n  }\n  .text-sidebar-primary-foreground {\n    color: var(--sidebar-primary-foreground);\n  }\n  .text-sky-600 {\n    color: var(--color-sky-600);\n  }\n  .text-sky-900 {\n    color: var(--color-sky-900);\n  }\n  .text-transparent {\n    color: transparent;\n  }\n  .text-white {\n    color: var(--color-white);\n  }\n  .text-white\\/90 {\n    color: color-mix(in srgb, #fff 90%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--color-white) 90%, transparent);\n    }\n  }\n  .text-yellow-300 {\n    color: var(--color-yellow-300);\n  }\n  .text-yellow-500 {\n    color: var(--color-yellow-500);\n  }\n  .text-yellow-600 {\n    color: var(--color-yellow-600);\n  }\n  .text-yellow-700 {\n    color: var(--color-yellow-700);\n  }\n  .text-yellow-800 {\n    color: var(--color-yellow-800);\n  }\n  .capitalize {\n    text-transform: capitalize;\n  }\n  .uppercase {\n    text-transform: uppercase;\n  }\n  .italic {\n    font-style: italic;\n  }\n  .tabular-nums {\n    --tw-numeric-spacing: tabular-nums;\n    font-variant-numeric: var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,);\n  }\n  .no-underline\\! {\n    text-decoration-line: none !important;\n  }\n  .underline {\n    text-decoration-line: underline;\n  }\n  .underline-offset-4 {\n    text-underline-offset: 4px;\n  }\n  .antialiased {\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n  }\n  .opacity-0 {\n    opacity: 0%;\n  }\n  .opacity-10 {\n    opacity: 10%;\n  }\n  .opacity-30 {\n    opacity: 30%;\n  }\n  .opacity-50 {\n    opacity: 50%;\n  }\n  .opacity-60 {\n    opacity: 60%;\n  }\n  .opacity-70 {\n    opacity: 70%;\n  }\n  .opacity-75 {\n    opacity: 75%;\n  }\n  .opacity-80 {\n    opacity: 80%;\n  }\n  .opacity-90 {\n    opacity: 90%;\n  }\n  .opacity-100 {\n    opacity: 100%;\n  }\n  .shadow {\n    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-2xl {\n    --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, rgb(0 0 0 / 0.25));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-\\[0_0_0_1px_hsl\\(var\\(--sidebar-border\\)\\)\\] {\n    --tw-shadow: 0 0 0 1px var(--tw-shadow-color, hsl(var(--sidebar-border)));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-lg {\n    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-md {\n    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-none {\n    --tw-shadow: 0 0 #0000;\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-sm {\n    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-xl {\n    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-xs {\n    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .ring {\n    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .ring-0 {\n    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .ring-1 {\n    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .ring-2 {\n    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .ring-blue-400 {\n    --tw-ring-color: var(--color-blue-400);\n  }\n  .ring-green-200 {\n    --tw-ring-color: var(--color-green-200);\n  }\n  .ring-primary {\n    --tw-ring-color: var(--primary);\n  }\n  .ring-red-200 {\n    --tw-ring-color: var(--color-red-200);\n  }\n  .ring-ring\\/50 {\n    --tw-ring-color: var(--ring);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);\n    }\n  }\n  .ring-sidebar-ring {\n    --tw-ring-color: var(--sidebar-ring);\n  }\n  .ring-sky-300 {\n    --tw-ring-color: var(--color-sky-300);\n  }\n  .ring-yellow-400 {\n    --tw-ring-color: var(--color-yellow-400);\n  }\n  .ring-offset-background {\n    --tw-ring-offset-color: var(--background);\n  }\n  .outline-hidden {\n    --tw-outline-style: none;\n    outline-style: none;\n    @media (forced-colors: active) {\n      outline: 2px solid transparent;\n      outline-offset: 2px;\n    }\n  }\n  .outline {\n    outline-style: var(--tw-outline-style);\n    outline-width: 1px;\n  }\n  .blur-md {\n    --tw-blur: blur(var(--blur-md));\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .drop-shadow-lg {\n    --tw-drop-shadow-size: drop-shadow(0 4px 4px var(--tw-drop-shadow-color, rgb(0 0 0 / 0.15)));\n    --tw-drop-shadow: drop-shadow(var(--drop-shadow-lg));\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .filter {\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .backdrop-blur-sm {\n    --tw-backdrop-blur: blur(var(--blur-sm));\n    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n  }\n  .transition {\n    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-\\[color\\,box-shadow\\] {\n    transition-property: color,box-shadow;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-\\[left\\,right\\,width\\] {\n    transition-property: left,right,width;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-\\[margin\\,opacity\\] {\n    transition-property: margin,opacity;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-\\[width\\,height\\,padding\\] {\n    transition-property: width,height,padding;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-\\[width\\,height\\] {\n    transition-property: width,height;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-\\[width\\] {\n    transition-property: width;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-all {\n    transition-property: all;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-colors {\n    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-opacity {\n    transition-property: opacity;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-shadow {\n    transition-property: box-shadow;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-transform {\n    transition-property: transform, translate, scale, rotate;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-none {\n    transition-property: none;\n  }\n  .duration-200 {\n    --tw-duration: 200ms;\n    transition-duration: 200ms;\n  }\n  .duration-300 {\n    --tw-duration: 300ms;\n    transition-duration: 300ms;\n  }\n  .duration-500 {\n    --tw-duration: 500ms;\n    transition-duration: 500ms;\n  }\n  .duration-1000 {\n    --tw-duration: 1000ms;\n    transition-duration: 1000ms;\n  }\n  .ease-in-out {\n    --tw-ease: var(--ease-in-out);\n    transition-timing-function: var(--ease-in-out);\n  }\n  .ease-linear {\n    --tw-ease: linear;\n    transition-timing-function: linear;\n  }\n  .ease-out {\n    --tw-ease: var(--ease-out);\n    transition-timing-function: var(--ease-out);\n  }\n  .fade-in-0 {\n    --tw-enter-opacity: calc(0/100);\n    --tw-enter-opacity: 0;\n  }\n  .outline-none {\n    --tw-outline-style: none;\n    outline-style: none;\n  }\n  .select-none {\n    -webkit-user-select: none;\n    user-select: none;\n  }\n  .zoom-in-95 {\n    --tw-enter-scale: calc(95*1%);\n    --tw-enter-scale: .95;\n  }\n  .fade-in {\n    --tw-enter-opacity: 0;\n  }\n  .ring-inset {\n    --tw-ring-inset: inset;\n  }\n  .slide-in-from-bottom-2 {\n    --tw-enter-translate-y: calc(2*var(--spacing));\n  }\n  .slide-in-from-top-2 {\n    --tw-enter-translate-y: calc(2*var(--spacing)*-1);\n  }\n  .group-focus-within\\/menu-item\\:opacity-100 {\n    &:is(:where(.group\\/menu-item):focus-within *) {\n      opacity: 100%;\n    }\n  }\n  .group-hover\\:bg-blue-200 {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        background-color: var(--color-blue-200);\n      }\n    }\n  }\n  .group-hover\\:text-blue-600 {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        color: var(--color-blue-600);\n      }\n    }\n  }\n  .group-hover\\/menu-item\\:opacity-100 {\n    &:is(:where(.group\\/menu-item):hover *) {\n      @media (hover: hover) {\n        opacity: 100%;\n      }\n    }\n  }\n  .group-has-data-\\[collapsible\\=icon\\]\\/sidebar-wrapper\\:h-12 {\n    &:is(:where(.group\\/sidebar-wrapper):has(*[data-collapsible=\"icon\"]) *) {\n      height: calc(var(--spacing) * 12);\n    }\n  }\n  .group-has-data-\\[sidebar\\=menu-action\\]\\/menu-item\\:pr-8 {\n    &:is(:where(.group\\/menu-item):has(*[data-sidebar=\"menu-action\"]) *) {\n      padding-right: calc(var(--spacing) * 8);\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:-mt-8 {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      margin-top: calc(var(--spacing) * -8);\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:hidden {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      display: none;\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:size-8\\! {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      width: calc(var(--spacing) * 8) !important;\n      height: calc(var(--spacing) * 8) !important;\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:w-\\(--sidebar-width-icon\\) {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      width: var(--sidebar-width-icon);\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:w-\\[calc\\(var\\(--sidebar-width-icon\\)\\+\\(--spacing\\(4\\)\\)\\)\\] {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      width: calc(var(--sidebar-width-icon) + (calc(var(--spacing) * 4)));\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:w-\\[calc\\(var\\(--sidebar-width-icon\\)\\+\\(--spacing\\(4\\)\\)\\+2px\\)\\] {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      width: calc(var(--sidebar-width-icon) + (calc(var(--spacing) * 4)) + 2px);\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:overflow-hidden {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      overflow: hidden;\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:p-0\\! {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      padding: calc(var(--spacing) * 0) !important;\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:p-2\\! {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      padding: calc(var(--spacing) * 2) !important;\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:opacity-0 {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      opacity: 0%;\n    }\n  }\n  .group-data-\\[collapsible\\=offcanvas\\]\\:right-\\[calc\\(var\\(--sidebar-width\\)\\*-1\\)\\] {\n    &:is(:where(.group)[data-collapsible=\"offcanvas\"] *) {\n      right: calc(var(--sidebar-width) * -1);\n    }\n  }\n  .group-data-\\[collapsible\\=offcanvas\\]\\:left-\\[calc\\(var\\(--sidebar-width\\)\\*-1\\)\\] {\n    &:is(:where(.group)[data-collapsible=\"offcanvas\"] *) {\n      left: calc(var(--sidebar-width) * -1);\n    }\n  }\n  .group-data-\\[collapsible\\=offcanvas\\]\\:w-0 {\n    &:is(:where(.group)[data-collapsible=\"offcanvas\"] *) {\n      width: calc(var(--spacing) * 0);\n    }\n  }\n  .group-data-\\[collapsible\\=offcanvas\\]\\:translate-x-0 {\n    &:is(:where(.group)[data-collapsible=\"offcanvas\"] *) {\n      --tw-translate-x: calc(var(--spacing) * 0);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .group-data-\\[disabled\\=true\\]\\:pointer-events-none {\n    &:is(:where(.group)[data-disabled=\"true\"] *) {\n      pointer-events: none;\n    }\n  }\n  .group-data-\\[disabled\\=true\\]\\:opacity-50 {\n    &:is(:where(.group)[data-disabled=\"true\"] *) {\n      opacity: 50%;\n    }\n  }\n  .group-data-\\[side\\=left\\]\\:-right-4 {\n    &:is(:where(.group)[data-side=\"left\"] *) {\n      right: calc(var(--spacing) * -4);\n    }\n  }\n  .group-data-\\[side\\=left\\]\\:border-r {\n    &:is(:where(.group)[data-side=\"left\"] *) {\n      border-right-style: var(--tw-border-style);\n      border-right-width: 1px;\n    }\n  }\n  .group-data-\\[side\\=right\\]\\:left-0 {\n    &:is(:where(.group)[data-side=\"right\"] *) {\n      left: calc(var(--spacing) * 0);\n    }\n  }\n  .group-data-\\[side\\=right\\]\\:rotate-180 {\n    &:is(:where(.group)[data-side=\"right\"] *) {\n      rotate: 180deg;\n    }\n  }\n  .group-data-\\[side\\=right\\]\\:border-l {\n    &:is(:where(.group)[data-side=\"right\"] *) {\n      border-left-style: var(--tw-border-style);\n      border-left-width: 1px;\n    }\n  }\n  .group-data-\\[state\\=open\\]\\:rotate-180 {\n    &:is(:where(.group)[data-state=\"open\"] *) {\n      rotate: 180deg;\n    }\n  }\n  .group-data-\\[state\\=open\\]\\/collapsible\\:rotate-90 {\n    &:is(:where(.group\\/collapsible)[data-state=\"open\"] *) {\n      rotate: 90deg;\n    }\n  }\n  .group-data-\\[variant\\=floating\\]\\:rounded-lg {\n    &:is(:where(.group)[data-variant=\"floating\"] *) {\n      border-radius: var(--radius);\n    }\n  }\n  .group-data-\\[variant\\=floating\\]\\:border {\n    &:is(:where(.group)[data-variant=\"floating\"] *) {\n      border-style: var(--tw-border-style);\n      border-width: 1px;\n    }\n  }\n  .group-data-\\[variant\\=floating\\]\\:border-sidebar-border {\n    &:is(:where(.group)[data-variant=\"floating\"] *) {\n      border-color: var(--sidebar-border);\n    }\n  }\n  .group-data-\\[variant\\=floating\\]\\:shadow-sm {\n    &:is(:where(.group)[data-variant=\"floating\"] *) {\n      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .group-data-\\[vaul-drawer-direction\\=bottom\\]\\/drawer-content\\:block {\n    &:is(:where(.group\\/drawer-content)[data-vaul-drawer-direction=\"bottom\"] *) {\n      display: block;\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:top-full {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      top: 100%;\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:mt-1\\.5 {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      margin-top: calc(var(--spacing) * 1.5);\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:overflow-hidden {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      overflow: hidden;\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:rounded-md {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      border-radius: calc(var(--radius) - 2px);\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:border {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      border-style: var(--tw-border-style);\n      border-width: 1px;\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:bg-popover {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      background-color: var(--popover);\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:text-popover-foreground {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      color: var(--popover-foreground);\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:shadow {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:duration-200 {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      --tw-duration: 200ms;\n      transition-duration: 200ms;\n    }\n  }\n  .peer-hover\\/menu-button\\:text-sidebar-accent-foreground {\n    &:is(:where(.peer\\/menu-button):hover ~ *) {\n      @media (hover: hover) {\n        color: var(--sidebar-accent-foreground);\n      }\n    }\n  }\n  .peer-disabled\\:cursor-not-allowed {\n    &:is(:where(.peer):disabled ~ *) {\n      cursor: not-allowed;\n    }\n  }\n  .peer-disabled\\:opacity-50 {\n    &:is(:where(.peer):disabled ~ *) {\n      opacity: 50%;\n    }\n  }\n  .peer-disabled\\:opacity-70 {\n    &:is(:where(.peer):disabled ~ *) {\n      opacity: 70%;\n    }\n  }\n  .peer-data-\\[active\\=true\\]\\/menu-button\\:text-sidebar-accent-foreground {\n    &:is(:where(.peer\\/menu-button)[data-active=\"true\"] ~ *) {\n      color: var(--sidebar-accent-foreground);\n    }\n  }\n  .peer-data-\\[size\\=default\\]\\/menu-button\\:top-1\\.5 {\n    &:is(:where(.peer\\/menu-button)[data-size=\"default\"] ~ *) {\n      top: calc(var(--spacing) * 1.5);\n    }\n  }\n  .peer-data-\\[size\\=lg\\]\\/menu-button\\:top-2\\.5 {\n    &:is(:where(.peer\\/menu-button)[data-size=\"lg\"] ~ *) {\n      top: calc(var(--spacing) * 2.5);\n    }\n  }\n  .peer-data-\\[size\\=sm\\]\\/menu-button\\:top-1 {\n    &:is(:where(.peer\\/menu-button)[data-size=\"sm\"] ~ *) {\n      top: calc(var(--spacing) * 1);\n    }\n  }\n  .selection\\:bg-primary {\n    & *::selection {\n      background-color: var(--primary);\n    }\n    &::selection {\n      background-color: var(--primary);\n    }\n  }\n  .selection\\:text-primary-foreground {\n    & *::selection {\n      color: var(--primary-foreground);\n    }\n    &::selection {\n      color: var(--primary-foreground);\n    }\n  }\n  .file\\:inline-flex {\n    &::file-selector-button {\n      display: inline-flex;\n    }\n  }\n  .file\\:h-7 {\n    &::file-selector-button {\n      height: calc(var(--spacing) * 7);\n    }\n  }\n  .file\\:border-0 {\n    &::file-selector-button {\n      border-style: var(--tw-border-style);\n      border-width: 0px;\n    }\n  }\n  .file\\:bg-transparent {\n    &::file-selector-button {\n      background-color: transparent;\n    }\n  }\n  .file\\:text-sm {\n    &::file-selector-button {\n      font-size: var(--text-sm);\n      line-height: var(--tw-leading, var(--text-sm--line-height));\n    }\n  }\n  .file\\:font-medium {\n    &::file-selector-button {\n      --tw-font-weight: var(--font-weight-medium);\n      font-weight: var(--font-weight-medium);\n    }\n  }\n  .file\\:text-foreground {\n    &::file-selector-button {\n      color: var(--foreground);\n    }\n  }\n  .placeholder\\:text-muted-foreground {\n    &::placeholder {\n      color: var(--muted-foreground);\n    }\n  }\n  .after\\:absolute {\n    &::after {\n      content: var(--tw-content);\n      position: absolute;\n    }\n  }\n  .after\\:-inset-2 {\n    &::after {\n      content: var(--tw-content);\n      inset: calc(var(--spacing) * -2);\n    }\n  }\n  .after\\:inset-y-0 {\n    &::after {\n      content: var(--tw-content);\n      inset-block: calc(var(--spacing) * 0);\n    }\n  }\n  .after\\:left-1\\/2 {\n    &::after {\n      content: var(--tw-content);\n      left: calc(1/2 * 100%);\n    }\n  }\n  .after\\:w-1 {\n    &::after {\n      content: var(--tw-content);\n      width: calc(var(--spacing) * 1);\n    }\n  }\n  .after\\:w-\\[2px\\] {\n    &::after {\n      content: var(--tw-content);\n      width: 2px;\n    }\n  }\n  .after\\:-translate-x-1\\/2 {\n    &::after {\n      content: var(--tw-content);\n      --tw-translate-x: calc(calc(1/2 * 100%) * -1);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .group-data-\\[collapsible\\=offcanvas\\]\\:after\\:left-full {\n    &:is(:where(.group)[data-collapsible=\"offcanvas\"] *) {\n      &::after {\n        content: var(--tw-content);\n        left: 100%;\n      }\n    }\n  }\n  .first\\:rounded-l-md {\n    &:first-child {\n      border-top-left-radius: calc(var(--radius) - 2px);\n      border-bottom-left-radius: calc(var(--radius) - 2px);\n    }\n  }\n  .first\\:border-l {\n    &:first-child {\n      border-left-style: var(--tw-border-style);\n      border-left-width: 1px;\n    }\n  }\n  .last\\:rounded-r-md {\n    &:last-child {\n      border-top-right-radius: calc(var(--radius) - 2px);\n      border-bottom-right-radius: calc(var(--radius) - 2px);\n    }\n  }\n  .last\\:border-b-0 {\n    &:last-child {\n      border-bottom-style: var(--tw-border-style);\n      border-bottom-width: 0px;\n    }\n  }\n  .even\\:border-l {\n    &:nth-child(even) {\n      border-left-style: var(--tw-border-style);\n      border-left-width: 1px;\n    }\n  }\n  .focus-within\\:relative {\n    &:focus-within {\n      position: relative;\n    }\n  }\n  .focus-within\\:z-20 {\n    &:focus-within {\n      z-index: 20;\n    }\n  }\n  .hover\\:scale-\\[1\\.02\\] {\n    &:hover {\n      @media (hover: hover) {\n        scale: 1.02;\n      }\n    }\n  }\n  .hover\\:rotate-0 {\n    &:hover {\n      @media (hover: hover) {\n        rotate: 0deg;\n      }\n    }\n  }\n  .hover\\:border-gray-300 {\n    &:hover {\n      @media (hover: hover) {\n        border-color: var(--color-gray-300);\n      }\n    }\n  }\n  .hover\\:border-gray-400 {\n    &:hover {\n      @media (hover: hover) {\n        border-color: var(--color-gray-400);\n      }\n    }\n  }\n  .hover\\:border-muted-foreground\\/50 {\n    &:hover {\n      @media (hover: hover) {\n        border-color: var(--muted-foreground);\n        @supports (color: color-mix(in lab, red, red)) {\n          border-color: color-mix(in oklab, var(--muted-foreground) 50%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-\\[var\\(--iai-primary\\)\\] {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--iai-primary);\n      }\n    }\n  }\n  .hover\\:bg-\\[var\\(--iai-secondary\\)\\] {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--iai-secondary);\n      }\n    }\n  }\n  .hover\\:bg-accent {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--accent);\n      }\n    }\n  }\n  .hover\\:bg-black {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-black);\n      }\n    }\n  }\n  .hover\\:bg-blue-50 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-blue-50);\n      }\n    }\n  }\n  .hover\\:bg-blue-100 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-blue-100);\n      }\n    }\n  }\n  .hover\\:bg-blue-700 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-blue-700);\n      }\n    }\n  }\n  .hover\\:bg-destructive\\/90 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--destructive);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--destructive) 90%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-gray-50 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-gray-50);\n      }\n    }\n  }\n  .hover\\:bg-gray-100 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-gray-100);\n      }\n    }\n  }\n  .hover\\:bg-gray-800 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-gray-800);\n      }\n    }\n  }\n  .hover\\:bg-green-50 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-green-50);\n      }\n    }\n  }\n  .hover\\:bg-green-100 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-green-100);\n      }\n    }\n  }\n  .hover\\:bg-green-200 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-green-200);\n      }\n    }\n  }\n  .hover\\:bg-green-600 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-green-600);\n      }\n    }\n  }\n  .hover\\:bg-green-700 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-green-700);\n      }\n    }\n  }\n  .hover\\:bg-muted {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--muted);\n      }\n    }\n  }\n  .hover\\:bg-muted\\/25 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--muted);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--muted) 25%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-muted\\/50 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--muted);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--muted) 50%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-muted\\/80 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--muted);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--muted) 80%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-primary {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--primary);\n      }\n    }\n  }\n  .hover\\:bg-primary\\/5 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--primary);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--primary) 5%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-primary\\/10 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--primary);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--primary) 10%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-primary\\/90 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--primary);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--primary) 90%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-red-100 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-red-100);\n      }\n    }\n  }\n  .hover\\:bg-red-200 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-red-200);\n      }\n    }\n  }\n  .hover\\:bg-secondary\\/80 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--secondary);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--secondary) 80%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-sidebar-accent {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--sidebar-accent);\n      }\n    }\n  }\n  .hover\\:bg-sky-50 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-sky-50);\n      }\n    }\n  }\n  .hover\\:bg-transparent {\n    &:hover {\n      @media (hover: hover) {\n        background-color: transparent;\n      }\n    }\n  }\n  .hover\\:bg-white {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-white);\n      }\n    }\n  }\n  .hover\\:bg-white\\/20 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, #fff 20%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-white) 20%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-yellow-500 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-yellow-500);\n      }\n    }\n  }\n  .hover\\:bg-yellow-700 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-yellow-700);\n      }\n    }\n  }\n  .hover\\:text-\\[var\\(--iai-secondary\\)\\] {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--iai-secondary);\n      }\n    }\n  }\n  .hover\\:text-accent-foreground {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--accent-foreground);\n      }\n    }\n  }\n  .hover\\:text-blue-600 {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-blue-600);\n      }\n    }\n  }\n  .hover\\:text-destructive {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--destructive);\n      }\n    }\n  }\n  .hover\\:text-foreground {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--foreground);\n      }\n    }\n  }\n  .hover\\:text-gray-600 {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-gray-600);\n      }\n    }\n  }\n  .hover\\:text-gray-700 {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-gray-700);\n      }\n    }\n  }\n  .hover\\:text-gray-900 {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-gray-900);\n      }\n    }\n  }\n  .hover\\:text-muted-foreground {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--muted-foreground);\n      }\n    }\n  }\n  .hover\\:text-primary-foreground {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--primary-foreground);\n      }\n    }\n  }\n  .hover\\:text-red-700 {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-red-700);\n      }\n    }\n  }\n  .hover\\:text-sidebar-accent-foreground {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--sidebar-accent-foreground);\n      }\n    }\n  }\n  .hover\\:text-white {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-white);\n      }\n    }\n  }\n  .hover\\:underline {\n    &:hover {\n      @media (hover: hover) {\n        text-decoration-line: underline;\n      }\n    }\n  }\n  .hover\\:opacity-80 {\n    &:hover {\n      @media (hover: hover) {\n        opacity: 80%;\n      }\n    }\n  }\n  .hover\\:opacity-100 {\n    &:hover {\n      @media (hover: hover) {\n        opacity: 100%;\n      }\n    }\n  }\n  .hover\\:shadow-2xl {\n    &:hover {\n      @media (hover: hover) {\n        --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, rgb(0 0 0 / 0.25));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .hover\\:shadow-\\[0_0_0_1px_hsl\\(var\\(--sidebar-accent\\)\\)\\] {\n    &:hover {\n      @media (hover: hover) {\n        --tw-shadow: 0 0 0 1px var(--tw-shadow-color, hsl(var(--sidebar-accent)));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .hover\\:shadow-lg {\n    &:hover {\n      @media (hover: hover) {\n        --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .hover\\:shadow-xl {\n    &:hover {\n      @media (hover: hover) {\n        --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .hover\\:ring-4 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .hover\\:group-data-\\[collapsible\\=offcanvas\\]\\:bg-sidebar {\n    &:hover {\n      @media (hover: hover) {\n        &:is(:where(.group)[data-collapsible=\"offcanvas\"] *) {\n          background-color: var(--sidebar);\n        }\n      }\n    }\n  }\n  .hover\\:after\\:bg-sidebar-border {\n    &:hover {\n      @media (hover: hover) {\n        &::after {\n          content: var(--tw-content);\n          background-color: var(--sidebar-border);\n        }\n      }\n    }\n  }\n  .focus\\:z-10 {\n    &:focus {\n      z-index: 10;\n    }\n  }\n  .focus\\:border-\\[var\\(--iai-primary\\)\\] {\n    &:focus {\n      border-color: var(--iai-primary);\n    }\n  }\n  .focus\\:border-red-500 {\n    &:focus {\n      border-color: var(--color-red-500);\n    }\n  }\n  .focus\\:border-transparent {\n    &:focus {\n      border-color: transparent;\n    }\n  }\n  .focus\\:bg-accent {\n    &:focus {\n      background-color: var(--accent);\n    }\n  }\n  .focus\\:bg-primary {\n    &:focus {\n      background-color: var(--primary);\n    }\n  }\n  .focus\\:text-accent-foreground {\n    &:focus {\n      color: var(--accent-foreground);\n    }\n  }\n  .focus\\:text-primary-foreground {\n    &:focus {\n      color: var(--primary-foreground);\n    }\n  }\n  .focus\\:ring-0 {\n    &:focus {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .focus\\:ring-1 {\n    &:focus {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .focus\\:ring-2 {\n    &:focus {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .focus\\:ring-\\[var\\(--iai-primary\\)\\] {\n    &:focus {\n      --tw-ring-color: var(--iai-primary);\n    }\n  }\n  .focus\\:ring-blue-500 {\n    &:focus {\n      --tw-ring-color: var(--color-blue-500);\n    }\n  }\n  .focus\\:ring-red-500 {\n    &:focus {\n      --tw-ring-color: var(--color-red-500);\n    }\n  }\n  .focus\\:ring-ring {\n    &:focus {\n      --tw-ring-color: var(--ring);\n    }\n  }\n  .focus\\:ring-offset-0 {\n    &:focus {\n      --tw-ring-offset-width: 0px;\n      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n    }\n  }\n  .focus\\:ring-offset-2 {\n    &:focus {\n      --tw-ring-offset-width: 2px;\n      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n    }\n  }\n  .focus\\:outline-hidden {\n    &:focus {\n      --tw-outline-style: none;\n      outline-style: none;\n      @media (forced-colors: active) {\n        outline: 2px solid transparent;\n        outline-offset: 2px;\n      }\n    }\n  }\n  .focus\\:outline-none {\n    &:focus {\n      --tw-outline-style: none;\n      outline-style: none;\n    }\n  }\n  .focus-visible\\:z-10 {\n    &:focus-visible {\n      z-index: 10;\n    }\n  }\n  .focus-visible\\:border-ring {\n    &:focus-visible {\n      border-color: var(--ring);\n    }\n  }\n  .focus-visible\\:ring-1 {\n    &:focus-visible {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .focus-visible\\:ring-2 {\n    &:focus-visible {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .focus-visible\\:ring-4 {\n    &:focus-visible {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .focus-visible\\:ring-\\[3px\\] {\n    &:focus-visible {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .focus-visible\\:ring-\\[var\\(--iai-primary\\)\\]\\/20 {\n    &:focus-visible {\n      --tw-ring-color: var(--iai-primary);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-ring-color: color-mix(in oklab, var(--iai-primary) 20%, transparent);\n      }\n    }\n  }\n  .focus-visible\\:ring-destructive\\/20 {\n    &:focus-visible {\n      --tw-ring-color: var(--destructive);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);\n      }\n    }\n  }\n  .focus-visible\\:ring-ring {\n    &:focus-visible {\n      --tw-ring-color: var(--ring);\n    }\n  }\n  .focus-visible\\:ring-ring\\/50 {\n    &:focus-visible {\n      --tw-ring-color: var(--ring);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);\n      }\n    }\n  }\n  .focus-visible\\:ring-offset-1 {\n    &:focus-visible {\n      --tw-ring-offset-width: 1px;\n      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n    }\n  }\n  .focus-visible\\:ring-offset-2 {\n    &:focus-visible {\n      --tw-ring-offset-width: 2px;\n      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n    }\n  }\n  .focus-visible\\:outline-hidden {\n    &:focus-visible {\n      --tw-outline-style: none;\n      outline-style: none;\n      @media (forced-colors: active) {\n        outline: 2px solid transparent;\n        outline-offset: 2px;\n      }\n    }\n  }\n  .focus-visible\\:outline-1 {\n    &:focus-visible {\n      outline-style: var(--tw-outline-style);\n      outline-width: 1px;\n    }\n  }\n  .focus-visible\\:outline-none {\n    &:focus-visible {\n      --tw-outline-style: none;\n      outline-style: none;\n    }\n  }\n  .active\\:bg-primary\\/90 {\n    &:active {\n      background-color: var(--primary);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--primary) 90%, transparent);\n      }\n    }\n  }\n  .active\\:bg-sidebar-accent {\n    &:active {\n      background-color: var(--sidebar-accent);\n    }\n  }\n  .active\\:text-primary-foreground {\n    &:active {\n      color: var(--primary-foreground);\n    }\n  }\n  .active\\:text-sidebar-accent-foreground {\n    &:active {\n      color: var(--sidebar-accent-foreground);\n    }\n  }\n  .disabled\\:pointer-events-none {\n    &:disabled {\n      pointer-events: none;\n    }\n  }\n  .disabled\\:cursor-not-allowed {\n    &:disabled {\n      cursor: not-allowed;\n    }\n  }\n  .disabled\\:cursor-pointer {\n    &:disabled {\n      cursor: pointer;\n    }\n  }\n  .disabled\\:border-none {\n    &:disabled {\n      --tw-border-style: none;\n      border-style: none;\n    }\n  }\n  .disabled\\:opacity-50 {\n    &:disabled {\n      opacity: 50%;\n    }\n  }\n  .disabled\\:opacity-100 {\n    &:disabled {\n      opacity: 100%;\n    }\n  }\n  .in-data-\\[side\\=left\\]\\:cursor-w-resize {\n    :where(*[data-side=\"left\"]) & {\n      cursor: w-resize;\n    }\n  }\n  .in-data-\\[side\\=right\\]\\:cursor-e-resize {\n    :where(*[data-side=\"right\"]) & {\n      cursor: e-resize;\n    }\n  }\n  .has-disabled\\:opacity-50 {\n    &:has(*:disabled) {\n      opacity: 50%;\n    }\n  }\n  .has-data-\\[slot\\=card-action\\]\\:grid-cols-\\[1fr_auto\\] {\n    &:has(*[data-slot=\"card-action\"]) {\n      grid-template-columns: 1fr auto;\n    }\n  }\n  .has-data-\\[variant\\=inset\\]\\:bg-sidebar {\n    &:has(*[data-variant=\"inset\"]) {\n      background-color: var(--sidebar);\n    }\n  }\n  .has-\\[\\>svg\\]\\:grid-cols-\\[calc\\(var\\(--spacing\\)\\*4\\)_1fr\\] {\n    &:has(>svg) {\n      grid-template-columns: calc(var(--spacing) * 4) 1fr;\n    }\n  }\n  .has-\\[\\>svg\\]\\:gap-x-3 {\n    &:has(>svg) {\n      column-gap: calc(var(--spacing) * 3);\n    }\n  }\n  .has-\\[\\>svg\\]\\:px-2\\.5 {\n    &:has(>svg) {\n      padding-inline: calc(var(--spacing) * 2.5);\n    }\n  }\n  .has-\\[\\>svg\\]\\:px-3 {\n    &:has(>svg) {\n      padding-inline: calc(var(--spacing) * 3);\n    }\n  }\n  .has-\\[\\>svg\\]\\:px-4 {\n    &:has(>svg) {\n      padding-inline: calc(var(--spacing) * 4);\n    }\n  }\n  .aria-disabled\\:pointer-events-none {\n    &[aria-disabled=\"true\"] {\n      pointer-events: none;\n    }\n  }\n  .aria-disabled\\:opacity-50 {\n    &[aria-disabled=\"true\"] {\n      opacity: 50%;\n    }\n  }\n  .aria-invalid\\:border-destructive {\n    &[aria-invalid=\"true\"] {\n      border-color: var(--destructive);\n    }\n  }\n  .aria-invalid\\:ring-destructive\\/20 {\n    &[aria-invalid=\"true\"] {\n      --tw-ring-color: var(--destructive);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);\n      }\n    }\n  }\n  .aria-selected\\:bg-accent {\n    &[aria-selected=\"true\"] {\n      background-color: var(--accent);\n    }\n  }\n  .aria-selected\\:bg-primary {\n    &[aria-selected=\"true\"] {\n      background-color: var(--primary);\n    }\n  }\n  .aria-selected\\:text-accent-foreground {\n    &[aria-selected=\"true\"] {\n      color: var(--accent-foreground);\n    }\n  }\n  .aria-selected\\:text-muted-foreground {\n    &[aria-selected=\"true\"] {\n      color: var(--muted-foreground);\n    }\n  }\n  .aria-selected\\:text-primary-foreground {\n    &[aria-selected=\"true\"] {\n      color: var(--primary-foreground);\n    }\n  }\n  .aria-selected\\:opacity-100 {\n    &[aria-selected=\"true\"] {\n      opacity: 100%;\n    }\n  }\n  .data-\\[active\\=true\\]\\:z-10 {\n    &[data-active=\"true\"] {\n      z-index: 10;\n    }\n  }\n  .data-\\[active\\=true\\]\\:border-ring {\n    &[data-active=\"true\"] {\n      border-color: var(--ring);\n    }\n  }\n  .data-\\[active\\=true\\]\\:bg-accent\\/50 {\n    &[data-active=\"true\"] {\n      background-color: var(--accent);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--accent) 50%, transparent);\n      }\n    }\n  }\n  .data-\\[active\\=true\\]\\:bg-primary\\/5 {\n    &[data-active=\"true\"] {\n      background-color: var(--primary);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--primary) 5%, transparent);\n      }\n    }\n  }\n  .data-\\[active\\=true\\]\\:bg-sidebar-accent {\n    &[data-active=\"true\"] {\n      background-color: var(--sidebar-accent);\n    }\n  }\n  .data-\\[active\\=true\\]\\:font-medium {\n    &[data-active=\"true\"] {\n      --tw-font-weight: var(--font-weight-medium);\n      font-weight: var(--font-weight-medium);\n    }\n  }\n  .data-\\[active\\=true\\]\\:text-accent-foreground {\n    &[data-active=\"true\"] {\n      color: var(--accent-foreground);\n    }\n  }\n  .data-\\[active\\=true\\]\\:text-sidebar-accent-foreground {\n    &[data-active=\"true\"] {\n      color: var(--sidebar-accent-foreground);\n    }\n  }\n  .data-\\[active\\=true\\]\\:ring-\\[3px\\] {\n    &[data-active=\"true\"] {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .data-\\[active\\=true\\]\\:ring-ring\\/50 {\n    &[data-active=\"true\"] {\n      --tw-ring-color: var(--ring);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);\n      }\n    }\n  }\n  .data-\\[active\\=true\\]\\:hover\\:bg-accent {\n    &[data-active=\"true\"] {\n      &:hover {\n        @media (hover: hover) {\n          background-color: var(--accent);\n        }\n      }\n    }\n  }\n  .data-\\[active\\=true\\]\\:focus\\:bg-accent {\n    &[data-active=\"true\"] {\n      &:focus {\n        background-color: var(--accent);\n      }\n    }\n  }\n  .data-\\[active\\=true\\]\\:aria-invalid\\:border-destructive {\n    &[data-active=\"true\"] {\n      &[aria-invalid=\"true\"] {\n        border-color: var(--destructive);\n      }\n    }\n  }\n  .data-\\[active\\=true\\]\\:aria-invalid\\:ring-destructive\\/20 {\n    &[data-active=\"true\"] {\n      &[aria-invalid=\"true\"] {\n        --tw-ring-color: var(--destructive);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);\n        }\n      }\n    }\n  }\n  .data-\\[disabled\\]\\:pointer-events-none {\n    &[data-disabled] {\n      pointer-events: none;\n    }\n  }\n  .data-\\[disabled\\]\\:opacity-50 {\n    &[data-disabled] {\n      opacity: 50%;\n    }\n  }\n  .data-\\[disabled\\=true\\]\\:pointer-events-none {\n    &[data-disabled=\"true\"] {\n      pointer-events: none;\n    }\n  }\n  .data-\\[disabled\\=true\\]\\:opacity-50 {\n    &[data-disabled=\"true\"] {\n      opacity: 50%;\n    }\n  }\n  .data-\\[error\\=true\\]\\:text-destructive {\n    &[data-error=\"true\"] {\n      color: var(--destructive);\n    }\n  }\n  .data-\\[inset\\]\\:pl-8 {\n    &[data-inset] {\n      padding-left: calc(var(--spacing) * 8);\n    }\n  }\n  .data-\\[motion\\=from-end\\]\\:slide-in-from-right-52 {\n    &[data-motion=\"from-end\"] {\n      --tw-enter-translate-x: calc(52*var(--spacing));\n    }\n  }\n  .data-\\[motion\\=from-start\\]\\:slide-in-from-left-52 {\n    &[data-motion=\"from-start\"] {\n      --tw-enter-translate-x: calc(52*var(--spacing)*-1);\n    }\n  }\n  .data-\\[motion\\=to-end\\]\\:slide-out-to-right-52 {\n    &[data-motion=\"to-end\"] {\n      --tw-exit-translate-x: calc(52*var(--spacing));\n    }\n  }\n  .data-\\[motion\\=to-start\\]\\:slide-out-to-left-52 {\n    &[data-motion=\"to-start\"] {\n      --tw-exit-translate-x: calc(52*var(--spacing)*-1);\n    }\n  }\n  .data-\\[motion\\^\\=from-\\]\\:animate-in {\n    &[data-motion^=\"from-\"] {\n      animation: enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none);\n    }\n  }\n  .data-\\[motion\\^\\=from-\\]\\:fade-in {\n    &[data-motion^=\"from-\"] {\n      --tw-enter-opacity: 0;\n    }\n  }\n  .data-\\[motion\\^\\=to-\\]\\:animate-out {\n    &[data-motion^=\"to-\"] {\n      animation: exit var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none);\n    }\n  }\n  .data-\\[motion\\^\\=to-\\]\\:fade-out {\n    &[data-motion^=\"to-\"] {\n      --tw-exit-opacity: 0;\n    }\n  }\n  .data-\\[orientation\\=horizontal\\]\\:h-1\\.5 {\n    &[data-orientation=\"horizontal\"] {\n      height: calc(var(--spacing) * 1.5);\n    }\n  }\n  .data-\\[orientation\\=horizontal\\]\\:h-full {\n    &[data-orientation=\"horizontal\"] {\n      height: 100%;\n    }\n  }\n  .data-\\[orientation\\=horizontal\\]\\:w-full {\n    &[data-orientation=\"horizontal\"] {\n      width: 100%;\n    }\n  }\n  .data-\\[orientation\\=vertical\\]\\:h-4 {\n    &[data-orientation=\"vertical\"] {\n      height: calc(var(--spacing) * 4);\n    }\n  }\n  .data-\\[orientation\\=vertical\\]\\:h-full {\n    &[data-orientation=\"vertical\"] {\n      height: 100%;\n    }\n  }\n  .data-\\[orientation\\=vertical\\]\\:min-h-44 {\n    &[data-orientation=\"vertical\"] {\n      min-height: calc(var(--spacing) * 44);\n    }\n  }\n  .data-\\[orientation\\=vertical\\]\\:w-1\\.5 {\n    &[data-orientation=\"vertical\"] {\n      width: calc(var(--spacing) * 1.5);\n    }\n  }\n  .data-\\[orientation\\=vertical\\]\\:w-auto {\n    &[data-orientation=\"vertical\"] {\n      width: auto;\n    }\n  }\n  .data-\\[orientation\\=vertical\\]\\:w-full {\n    &[data-orientation=\"vertical\"] {\n      width: 100%;\n    }\n  }\n  .data-\\[orientation\\=vertical\\]\\:flex-col {\n    &[data-orientation=\"vertical\"] {\n      flex-direction: column;\n    }\n  }\n  .data-\\[panel-group-direction\\=vertical\\]\\:h-px {\n    &[data-panel-group-direction=\"vertical\"] {\n      height: 1px;\n    }\n  }\n  .data-\\[panel-group-direction\\=vertical\\]\\:w-full {\n    &[data-panel-group-direction=\"vertical\"] {\n      width: 100%;\n    }\n  }\n  .data-\\[panel-group-direction\\=vertical\\]\\:flex-col {\n    &[data-panel-group-direction=\"vertical\"] {\n      flex-direction: column;\n    }\n  }\n  .data-\\[panel-group-direction\\=vertical\\]\\:after\\:left-0 {\n    &[data-panel-group-direction=\"vertical\"] {\n      &::after {\n        content: var(--tw-content);\n        left: calc(var(--spacing) * 0);\n      }\n    }\n  }\n  .data-\\[panel-group-direction\\=vertical\\]\\:after\\:h-1 {\n    &[data-panel-group-direction=\"vertical\"] {\n      &::after {\n        content: var(--tw-content);\n        height: calc(var(--spacing) * 1);\n      }\n    }\n  }\n  .data-\\[panel-group-direction\\=vertical\\]\\:after\\:w-full {\n    &[data-panel-group-direction=\"vertical\"] {\n      &::after {\n        content: var(--tw-content);\n        width: 100%;\n      }\n    }\n  }\n  .data-\\[panel-group-direction\\=vertical\\]\\:after\\:translate-x-0 {\n    &[data-panel-group-direction=\"vertical\"] {\n      &::after {\n        content: var(--tw-content);\n        --tw-translate-x: calc(var(--spacing) * 0);\n        translate: var(--tw-translate-x) var(--tw-translate-y);\n      }\n    }\n  }\n  .data-\\[panel-group-direction\\=vertical\\]\\:after\\:-translate-y-1\\/2 {\n    &[data-panel-group-direction=\"vertical\"] {\n      &::after {\n        content: var(--tw-content);\n        --tw-translate-y: calc(calc(1/2 * 100%) * -1);\n        translate: var(--tw-translate-x) var(--tw-translate-y);\n      }\n    }\n  }\n  .data-\\[placeholder\\]\\:text-muted-foreground {\n    &[data-placeholder] {\n      color: var(--muted-foreground);\n    }\n  }\n  .data-\\[selected\\=true\\]\\:bg-accent {\n    &[data-selected=\"true\"] {\n      background-color: var(--accent);\n    }\n  }\n  .data-\\[selected\\=true\\]\\:text-accent-foreground {\n    &[data-selected=\"true\"] {\n      color: var(--accent-foreground);\n    }\n  }\n  .data-\\[side\\=bottom\\]\\:translate-y-1 {\n    &[data-side=\"bottom\"] {\n      --tw-translate-y: calc(var(--spacing) * 1);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .data-\\[side\\=bottom\\]\\:slide-in-from-top-2 {\n    &[data-side=\"bottom\"] {\n      --tw-enter-translate-y: calc(2*var(--spacing)*-1);\n    }\n  }\n  .data-\\[side\\=left\\]\\:-translate-x-1 {\n    &[data-side=\"left\"] {\n      --tw-translate-x: calc(var(--spacing) * -1);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .data-\\[side\\=left\\]\\:slide-in-from-right-2 {\n    &[data-side=\"left\"] {\n      --tw-enter-translate-x: calc(2*var(--spacing));\n    }\n  }\n  .data-\\[side\\=right\\]\\:translate-x-1 {\n    &[data-side=\"right\"] {\n      --tw-translate-x: calc(var(--spacing) * 1);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .data-\\[side\\=right\\]\\:slide-in-from-left-2 {\n    &[data-side=\"right\"] {\n      --tw-enter-translate-x: calc(2*var(--spacing)*-1);\n    }\n  }\n  .data-\\[side\\=top\\]\\:-translate-y-1 {\n    &[data-side=\"top\"] {\n      --tw-translate-y: calc(var(--spacing) * -1);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .data-\\[side\\=top\\]\\:slide-in-from-bottom-2 {\n    &[data-side=\"top\"] {\n      --tw-enter-translate-y: calc(2*var(--spacing));\n    }\n  }\n  .data-\\[size\\=default\\]\\:h-9 {\n    &[data-size=\"default\"] {\n      height: calc(var(--spacing) * 9);\n    }\n  }\n  .data-\\[size\\=sm\\]\\:h-8 {\n    &[data-size=\"sm\"] {\n      height: calc(var(--spacing) * 8);\n    }\n  }\n  .\\*\\:data-\\[slot\\=alert-description\\]\\:text-destructive\\/90 {\n    :is(& > *) {\n      &[data-slot=\"alert-description\"] {\n        color: var(--destructive);\n        @supports (color: color-mix(in lab, red, red)) {\n          color: color-mix(in oklab, var(--destructive) 90%, transparent);\n        }\n      }\n    }\n  }\n  .\\*\\:data-\\[slot\\=card\\]\\:bg-gradient-to-t {\n    :is(& > *) {\n      &[data-slot=\"card\"] {\n        --tw-gradient-position: to top in oklab;\n        background-image: linear-gradient(var(--tw-gradient-stops));\n      }\n    }\n  }\n  .\\*\\:data-\\[slot\\=card\\]\\:from-primary\\/5 {\n    :is(& > *) {\n      &[data-slot=\"card\"] {\n        --tw-gradient-from: var(--primary);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-gradient-from: color-mix(in oklab, var(--primary) 5%, transparent);\n        }\n        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n      }\n    }\n  }\n  .\\*\\:data-\\[slot\\=card\\]\\:to-card {\n    :is(& > *) {\n      &[data-slot=\"card\"] {\n        --tw-gradient-to: var(--card);\n        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n      }\n    }\n  }\n  .\\*\\:data-\\[slot\\=card\\]\\:shadow-xs {\n    :is(& > *) {\n      &[data-slot=\"card\"] {\n        --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .\\*\\*\\:data-\\[slot\\=command-input-wrapper\\]\\:h-12 {\n    :is(& *) {\n      &[data-slot=\"command-input-wrapper\"] {\n        height: calc(var(--spacing) * 12);\n      }\n    }\n  }\n  .\\*\\*\\:data-\\[slot\\=navigation-menu-link\\]\\:focus\\:ring-0 {\n    :is(& *) {\n      &[data-slot=\"navigation-menu-link\"] {\n        &:focus {\n          --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n          box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n        }\n      }\n    }\n  }\n  .\\*\\*\\:data-\\[slot\\=navigation-menu-link\\]\\:focus\\:outline-none {\n    :is(& *) {\n      &[data-slot=\"navigation-menu-link\"] {\n        &:focus {\n          --tw-outline-style: none;\n          outline-style: none;\n        }\n      }\n    }\n  }\n  .\\*\\:data-\\[slot\\=select-value\\]\\:line-clamp-1 {\n    :is(& > *) {\n      &[data-slot=\"select-value\"] {\n        overflow: hidden;\n        display: -webkit-box;\n        -webkit-box-orient: vertical;\n        -webkit-line-clamp: 1;\n      }\n    }\n  }\n  .\\*\\:data-\\[slot\\=select-value\\]\\:flex {\n    :is(& > *) {\n      &[data-slot=\"select-value\"] {\n        display: flex;\n      }\n    }\n  }\n  .\\*\\:data-\\[slot\\=select-value\\]\\:w-12 {\n    :is(& > *) {\n      &[data-slot=\"select-value\"] {\n        width: calc(var(--spacing) * 12);\n      }\n    }\n  }\n  .\\*\\:data-\\[slot\\=select-value\\]\\:items-center {\n    :is(& > *) {\n      &[data-slot=\"select-value\"] {\n        align-items: center;\n      }\n    }\n  }\n  .\\*\\:data-\\[slot\\=select-value\\]\\:gap-2 {\n    :is(& > *) {\n      &[data-slot=\"select-value\"] {\n        gap: calc(var(--spacing) * 2);\n      }\n    }\n  }\n  .data-\\[state\\=active\\]\\:bg-background {\n    &[data-state=\"active\"] {\n      background-color: var(--background);\n    }\n  }\n  .data-\\[state\\=active\\]\\:text-foreground {\n    &[data-state=\"active\"] {\n      color: var(--foreground);\n    }\n  }\n  .data-\\[state\\=active\\]\\:shadow-sm {\n    &[data-state=\"active\"] {\n      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .data-\\[state\\=checked\\]\\:translate-x-\\[calc\\(100\\%-2px\\)\\] {\n    &[data-state=\"checked\"] {\n      --tw-translate-x: calc(100% - 2px);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .data-\\[state\\=checked\\]\\:border-\\[var\\(--iai-primary\\)\\] {\n    &[data-state=\"checked\"] {\n      border-color: var(--iai-primary);\n    }\n  }\n  .data-\\[state\\=checked\\]\\:border-primary {\n    &[data-state=\"checked\"] {\n      border-color: var(--primary);\n    }\n  }\n  .data-\\[state\\=checked\\]\\:bg-\\[var\\(--iai-primary\\)\\] {\n    &[data-state=\"checked\"] {\n      background-color: var(--iai-primary);\n    }\n  }\n  .data-\\[state\\=checked\\]\\:bg-primary {\n    &[data-state=\"checked\"] {\n      background-color: var(--primary);\n    }\n  }\n  .data-\\[state\\=checked\\]\\:text-primary-foreground {\n    &[data-state=\"checked\"] {\n      color: var(--primary-foreground);\n    }\n  }\n  .data-\\[state\\=closed\\]\\:animate-accordion-up {\n    &[data-state=\"closed\"] {\n      animation: accordion-up var(--tw-animation-duration,var(--tw-duration,.2s))var(--tw-ease,ease-out)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none);\n    }\n  }\n  .data-\\[state\\=closed\\]\\:animate-out {\n    &[data-state=\"closed\"] {\n      animation: exit var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none);\n    }\n  }\n  .data-\\[state\\=closed\\]\\:duration-300 {\n    &[data-state=\"closed\"] {\n      --tw-duration: 300ms;\n      transition-duration: 300ms;\n    }\n  }\n  .data-\\[state\\=closed\\]\\:fade-out-0 {\n    &[data-state=\"closed\"] {\n      --tw-exit-opacity: calc(0/100);\n      --tw-exit-opacity: 0;\n    }\n  }\n  .data-\\[state\\=closed\\]\\:zoom-out-95 {\n    &[data-state=\"closed\"] {\n      --tw-exit-scale: calc(95*1%);\n      --tw-exit-scale: .95;\n    }\n  }\n  .data-\\[state\\=closed\\]\\:slide-out-to-bottom {\n    &[data-state=\"closed\"] {\n      --tw-exit-translate-y: 100%;\n    }\n  }\n  .data-\\[state\\=closed\\]\\:slide-out-to-left {\n    &[data-state=\"closed\"] {\n      --tw-exit-translate-x: -100%;\n    }\n  }\n  .data-\\[state\\=closed\\]\\:slide-out-to-right {\n    &[data-state=\"closed\"] {\n      --tw-exit-translate-x: 100%;\n    }\n  }\n  .data-\\[state\\=closed\\]\\:slide-out-to-top {\n    &[data-state=\"closed\"] {\n      --tw-exit-translate-y: -100%;\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:data-\\[state\\=closed\\]\\:animate-out {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      &[data-state=\"closed\"] {\n        animation: exit var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none);\n      }\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:data-\\[state\\=closed\\]\\:fade-out-0 {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      &[data-state=\"closed\"] {\n        --tw-exit-opacity: calc(0/100);\n        --tw-exit-opacity: 0;\n      }\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:data-\\[state\\=closed\\]\\:zoom-out-95 {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      &[data-state=\"closed\"] {\n        --tw-exit-scale: calc(95*1%);\n        --tw-exit-scale: .95;\n      }\n    }\n  }\n  .data-\\[state\\=hidden\\]\\:animate-out {\n    &[data-state=\"hidden\"] {\n      animation: exit var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none);\n    }\n  }\n  .data-\\[state\\=hidden\\]\\:fade-out {\n    &[data-state=\"hidden\"] {\n      --tw-exit-opacity: 0;\n    }\n  }\n  .data-\\[state\\=on\\]\\:bg-accent {\n    &[data-state=\"on\"] {\n      background-color: var(--accent);\n    }\n  }\n  .data-\\[state\\=on\\]\\:text-accent-foreground {\n    &[data-state=\"on\"] {\n      color: var(--accent-foreground);\n    }\n  }\n  .data-\\[state\\=open\\]\\:animate-accordion-down {\n    &[data-state=\"open\"] {\n      animation: accordion-down var(--tw-animation-duration,var(--tw-duration,.2s))var(--tw-ease,ease-out)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none);\n    }\n  }\n  .data-\\[state\\=open\\]\\:animate-in {\n    &[data-state=\"open\"] {\n      animation: enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none);\n    }\n  }\n  .data-\\[state\\=open\\]\\:bg-accent {\n    &[data-state=\"open\"] {\n      background-color: var(--accent);\n    }\n  }\n  .data-\\[state\\=open\\]\\:bg-accent\\/50 {\n    &[data-state=\"open\"] {\n      background-color: var(--accent);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--accent) 50%, transparent);\n      }\n    }\n  }\n  .data-\\[state\\=open\\]\\:bg-secondary {\n    &[data-state=\"open\"] {\n      background-color: var(--secondary);\n    }\n  }\n  .data-\\[state\\=open\\]\\:bg-sidebar-accent {\n    &[data-state=\"open\"] {\n      background-color: var(--sidebar-accent);\n    }\n  }\n  .data-\\[state\\=open\\]\\:text-accent-foreground {\n    &[data-state=\"open\"] {\n      color: var(--accent-foreground);\n    }\n  }\n  .data-\\[state\\=open\\]\\:text-muted-foreground {\n    &[data-state=\"open\"] {\n      color: var(--muted-foreground);\n    }\n  }\n  .data-\\[state\\=open\\]\\:text-sidebar-accent-foreground {\n    &[data-state=\"open\"] {\n      color: var(--sidebar-accent-foreground);\n    }\n  }\n  .data-\\[state\\=open\\]\\:opacity-100 {\n    &[data-state=\"open\"] {\n      opacity: 100%;\n    }\n  }\n  .data-\\[state\\=open\\]\\:duration-500 {\n    &[data-state=\"open\"] {\n      --tw-duration: 500ms;\n      transition-duration: 500ms;\n    }\n  }\n  .data-\\[state\\=open\\]\\:fade-in-0 {\n    &[data-state=\"open\"] {\n      --tw-enter-opacity: calc(0/100);\n      --tw-enter-opacity: 0;\n    }\n  }\n  .data-\\[state\\=open\\]\\:zoom-in-90 {\n    &[data-state=\"open\"] {\n      --tw-enter-scale: calc(90*1%);\n      --tw-enter-scale: .9;\n    }\n  }\n  .data-\\[state\\=open\\]\\:zoom-in-95 {\n    &[data-state=\"open\"] {\n      --tw-enter-scale: calc(95*1%);\n      --tw-enter-scale: .95;\n    }\n  }\n  .data-\\[state\\=open\\]\\:slide-in-from-bottom {\n    &[data-state=\"open\"] {\n      --tw-enter-translate-y: 100%;\n    }\n  }\n  .data-\\[state\\=open\\]\\:slide-in-from-left {\n    &[data-state=\"open\"] {\n      --tw-enter-translate-x: -100%;\n    }\n  }\n  .data-\\[state\\=open\\]\\:slide-in-from-right {\n    &[data-state=\"open\"] {\n      --tw-enter-translate-x: 100%;\n    }\n  }\n  .data-\\[state\\=open\\]\\:slide-in-from-top {\n    &[data-state=\"open\"] {\n      --tw-enter-translate-y: -100%;\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:data-\\[state\\=open\\]\\:animate-in {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      &[data-state=\"open\"] {\n        animation: enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none);\n      }\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:data-\\[state\\=open\\]\\:fade-in-0 {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      &[data-state=\"open\"] {\n        --tw-enter-opacity: calc(0/100);\n        --tw-enter-opacity: 0;\n      }\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:data-\\[state\\=open\\]\\:zoom-in-95 {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      &[data-state=\"open\"] {\n        --tw-enter-scale: calc(95*1%);\n        --tw-enter-scale: .95;\n      }\n    }\n  }\n  .data-\\[state\\=open\\]\\:hover\\:bg-accent {\n    &[data-state=\"open\"] {\n      &:hover {\n        @media (hover: hover) {\n          background-color: var(--accent);\n        }\n      }\n    }\n  }\n  .data-\\[state\\=open\\]\\:hover\\:bg-sidebar-accent {\n    &[data-state=\"open\"] {\n      &:hover {\n        @media (hover: hover) {\n          background-color: var(--sidebar-accent);\n        }\n      }\n    }\n  }\n  .data-\\[state\\=open\\]\\:hover\\:text-sidebar-accent-foreground {\n    &[data-state=\"open\"] {\n      &:hover {\n        @media (hover: hover) {\n          color: var(--sidebar-accent-foreground);\n        }\n      }\n    }\n  }\n  .data-\\[state\\=open\\]\\:focus\\:bg-accent {\n    &[data-state=\"open\"] {\n      &:focus {\n        background-color: var(--accent);\n      }\n    }\n  }\n  .data-\\[state\\=selected\\]\\:bg-muted {\n    &[data-state=\"selected\"] {\n      background-color: var(--muted);\n    }\n  }\n  .data-\\[state\\=unchecked\\]\\:translate-x-0 {\n    &[data-state=\"unchecked\"] {\n      --tw-translate-x: calc(var(--spacing) * 0);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .data-\\[state\\=unchecked\\]\\:bg-input {\n    &[data-state=\"unchecked\"] {\n      background-color: var(--input);\n    }\n  }\n  .data-\\[state\\=visible\\]\\:animate-in {\n    &[data-state=\"visible\"] {\n      animation: enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none);\n    }\n  }\n  .data-\\[state\\=visible\\]\\:fade-in {\n    &[data-state=\"visible\"] {\n      --tw-enter-opacity: 0;\n    }\n  }\n  .data-\\[variant\\=destructive\\]\\:text-destructive {\n    &[data-variant=\"destructive\"] {\n      color: var(--destructive);\n    }\n  }\n  .data-\\[variant\\=destructive\\]\\:focus\\:bg-destructive\\/10 {\n    &[data-variant=\"destructive\"] {\n      &:focus {\n        background-color: var(--destructive);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--destructive) 10%, transparent);\n        }\n      }\n    }\n  }\n  .data-\\[variant\\=destructive\\]\\:focus\\:text-destructive {\n    &[data-variant=\"destructive\"] {\n      &:focus {\n        color: var(--destructive);\n      }\n    }\n  }\n  .data-\\[variant\\=outline\\]\\:border-l-0 {\n    &[data-variant=\"outline\"] {\n      border-left-style: var(--tw-border-style);\n      border-left-width: 0px;\n    }\n  }\n  .data-\\[variant\\=outline\\]\\:shadow-xs {\n    &[data-variant=\"outline\"] {\n      --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .data-\\[variant\\=outline\\]\\:first\\:border-l {\n    &[data-variant=\"outline\"] {\n      &:first-child {\n        border-left-style: var(--tw-border-style);\n        border-left-width: 1px;\n      }\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=bottom\\]\\:inset-x-0 {\n    &[data-vaul-drawer-direction=\"bottom\"] {\n      inset-inline: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=bottom\\]\\:bottom-0 {\n    &[data-vaul-drawer-direction=\"bottom\"] {\n      bottom: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=bottom\\]\\:mt-24 {\n    &[data-vaul-drawer-direction=\"bottom\"] {\n      margin-top: calc(var(--spacing) * 24);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=bottom\\]\\:max-h-\\[80vh\\] {\n    &[data-vaul-drawer-direction=\"bottom\"] {\n      max-height: 80vh;\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=bottom\\]\\:rounded-t-lg {\n    &[data-vaul-drawer-direction=\"bottom\"] {\n      border-top-left-radius: var(--radius);\n      border-top-right-radius: var(--radius);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=bottom\\]\\:border-t {\n    &[data-vaul-drawer-direction=\"bottom\"] {\n      border-top-style: var(--tw-border-style);\n      border-top-width: 1px;\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=left\\]\\:inset-y-0 {\n    &[data-vaul-drawer-direction=\"left\"] {\n      inset-block: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=left\\]\\:left-0 {\n    &[data-vaul-drawer-direction=\"left\"] {\n      left: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=left\\]\\:w-3\\/4 {\n    &[data-vaul-drawer-direction=\"left\"] {\n      width: calc(3/4 * 100%);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=left\\]\\:border-r {\n    &[data-vaul-drawer-direction=\"left\"] {\n      border-right-style: var(--tw-border-style);\n      border-right-width: 1px;\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=right\\]\\:inset-y-0 {\n    &[data-vaul-drawer-direction=\"right\"] {\n      inset-block: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=right\\]\\:right-0 {\n    &[data-vaul-drawer-direction=\"right\"] {\n      right: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=right\\]\\:w-3\\/4 {\n    &[data-vaul-drawer-direction=\"right\"] {\n      width: calc(3/4 * 100%);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=right\\]\\:border-l {\n    &[data-vaul-drawer-direction=\"right\"] {\n      border-left-style: var(--tw-border-style);\n      border-left-width: 1px;\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=top\\]\\:inset-x-0 {\n    &[data-vaul-drawer-direction=\"top\"] {\n      inset-inline: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=top\\]\\:top-0 {\n    &[data-vaul-drawer-direction=\"top\"] {\n      top: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=top\\]\\:mb-24 {\n    &[data-vaul-drawer-direction=\"top\"] {\n      margin-bottom: calc(var(--spacing) * 24);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=top\\]\\:max-h-\\[80vh\\] {\n    &[data-vaul-drawer-direction=\"top\"] {\n      max-height: 80vh;\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=top\\]\\:rounded-b-lg {\n    &[data-vaul-drawer-direction=\"top\"] {\n      border-bottom-right-radius: var(--radius);\n      border-bottom-left-radius: var(--radius);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=top\\]\\:border-b {\n    &[data-vaul-drawer-direction=\"top\"] {\n      border-bottom-style: var(--tw-border-style);\n      border-bottom-width: 1px;\n    }\n  }\n  .sm\\:mb-4 {\n    @media (width >= 40rem) {\n      margin-bottom: calc(var(--spacing) * 4);\n    }\n  }\n  .sm\\:mb-8 {\n    @media (width >= 40rem) {\n      margin-bottom: calc(var(--spacing) * 8);\n    }\n  }\n  .sm\\:block {\n    @media (width >= 40rem) {\n      display: block;\n    }\n  }\n  .sm\\:flex {\n    @media (width >= 40rem) {\n      display: flex;\n    }\n  }\n  .sm\\:hidden {\n    @media (width >= 40rem) {\n      display: none;\n    }\n  }\n  .sm\\:h-10 {\n    @media (width >= 40rem) {\n      height: calc(var(--spacing) * 10);\n    }\n  }\n  .sm\\:h-\\[200px\\] {\n    @media (width >= 40rem) {\n      height: 200px;\n    }\n  }\n  .sm\\:w-\\[200px\\] {\n    @media (width >= 40rem) {\n      width: 200px;\n    }\n  }\n  .sm\\:max-w-2xl {\n    @media (width >= 40rem) {\n      max-width: var(--container-2xl);\n    }\n  }\n  .sm\\:max-w-4xl {\n    @media (width >= 40rem) {\n      max-width: var(--container-4xl);\n    }\n  }\n  .sm\\:max-w-\\[425px\\] {\n    @media (width >= 40rem) {\n      max-width: 425px;\n    }\n  }\n  .sm\\:max-w-lg {\n    @media (width >= 40rem) {\n      max-width: var(--container-lg);\n    }\n  }\n  .sm\\:max-w-md {\n    @media (width >= 40rem) {\n      max-width: var(--container-md);\n    }\n  }\n  .sm\\:max-w-sm {\n    @media (width >= 40rem) {\n      max-width: var(--container-sm);\n    }\n  }\n  .sm\\:grid-cols-2 {\n    @media (width >= 40rem) {\n      grid-template-columns: repeat(2, minmax(0, 1fr));\n    }\n  }\n  .sm\\:flex-row {\n    @media (width >= 40rem) {\n      flex-direction: row;\n    }\n  }\n  .sm\\:justify-end {\n    @media (width >= 40rem) {\n      justify-content: flex-end;\n    }\n  }\n  .sm\\:gap-2\\.5 {\n    @media (width >= 40rem) {\n      gap: calc(var(--spacing) * 2.5);\n    }\n  }\n  .sm\\:gap-6 {\n    @media (width >= 40rem) {\n      gap: calc(var(--spacing) * 6);\n    }\n  }\n  .sm\\:gap-8 {\n    @media (width >= 40rem) {\n      gap: calc(var(--spacing) * 8);\n    }\n  }\n  .sm\\:space-y-6 {\n    @media (width >= 40rem) {\n      :where(& > :not(:last-child)) {\n        --tw-space-y-reverse: 0;\n        margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));\n        margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));\n      }\n    }\n  }\n  .sm\\:border-t-0 {\n    @media (width >= 40rem) {\n      border-top-style: var(--tw-border-style);\n      border-top-width: 0px;\n    }\n  }\n  .sm\\:border-l {\n    @media (width >= 40rem) {\n      border-left-style: var(--tw-border-style);\n      border-left-width: 1px;\n    }\n  }\n  .sm\\:p-6 {\n    @media (width >= 40rem) {\n      padding: calc(var(--spacing) * 6);\n    }\n  }\n  .sm\\:p-8 {\n    @media (width >= 40rem) {\n      padding: calc(var(--spacing) * 8);\n    }\n  }\n  .sm\\:px-5 {\n    @media (width >= 40rem) {\n      padding-inline: calc(var(--spacing) * 5);\n    }\n  }\n  .sm\\:px-6 {\n    @media (width >= 40rem) {\n      padding-inline: calc(var(--spacing) * 6);\n    }\n  }\n  .sm\\:px-8 {\n    @media (width >= 40rem) {\n      padding-inline: calc(var(--spacing) * 8);\n    }\n  }\n  .sm\\:py-6 {\n    @media (width >= 40rem) {\n      padding-block: calc(var(--spacing) * 6);\n    }\n  }\n  .sm\\:pt-6 {\n    @media (width >= 40rem) {\n      padding-top: calc(var(--spacing) * 6);\n    }\n  }\n  .sm\\:pr-2\\.5 {\n    @media (width >= 40rem) {\n      padding-right: calc(var(--spacing) * 2.5);\n    }\n  }\n  .sm\\:pr-12 {\n    @media (width >= 40rem) {\n      padding-right: calc(var(--spacing) * 12);\n    }\n  }\n  .sm\\:pb-6 {\n    @media (width >= 40rem) {\n      padding-bottom: calc(var(--spacing) * 6);\n    }\n  }\n  .sm\\:pl-2\\.5 {\n    @media (width >= 40rem) {\n      padding-left: calc(var(--spacing) * 2.5);\n    }\n  }\n  .sm\\:text-left {\n    @media (width >= 40rem) {\n      text-align: left;\n    }\n  }\n  .sm\\:text-3xl {\n    @media (width >= 40rem) {\n      font-size: var(--text-3xl);\n      line-height: var(--tw-leading, var(--text-3xl--line-height));\n    }\n  }\n  .sm\\:text-4xl {\n    @media (width >= 40rem) {\n      font-size: var(--text-4xl);\n      line-height: var(--tw-leading, var(--text-4xl--line-height));\n    }\n  }\n  .sm\\:text-lg {\n    @media (width >= 40rem) {\n      font-size: var(--text-lg);\n      line-height: var(--tw-leading, var(--text-lg--line-height));\n    }\n  }\n  .sm\\:text-xl {\n    @media (width >= 40rem) {\n      font-size: var(--text-xl);\n      line-height: var(--tw-leading, var(--text-xl--line-height));\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=left\\]\\:sm\\:max-w-sm {\n    &[data-vaul-drawer-direction=\"left\"] {\n      @media (width >= 40rem) {\n        max-width: var(--container-sm);\n      }\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=right\\]\\:sm\\:max-w-sm {\n    &[data-vaul-drawer-direction=\"right\"] {\n      @media (width >= 40rem) {\n        max-width: var(--container-sm);\n      }\n    }\n  }\n  .md\\:absolute {\n    @media (width >= 48rem) {\n      position: absolute;\n    }\n  }\n  .md\\:col-span-2 {\n    @media (width >= 48rem) {\n      grid-column: span 2 / span 2;\n    }\n  }\n  .md\\:col-span-3 {\n    @media (width >= 48rem) {\n      grid-column: span 3 / span 3;\n    }\n  }\n  .md\\:block {\n    @media (width >= 48rem) {\n      display: block;\n    }\n  }\n  .md\\:flex {\n    @media (width >= 48rem) {\n      display: flex;\n    }\n  }\n  .md\\:grid {\n    @media (width >= 48rem) {\n      display: grid;\n    }\n  }\n  .md\\:hidden {\n    @media (width >= 48rem) {\n      display: none;\n    }\n  }\n  .md\\:inline-block {\n    @media (width >= 48rem) {\n      display: inline-block;\n    }\n  }\n  .md\\:w-40 {\n    @media (width >= 48rem) {\n      width: calc(var(--spacing) * 40);\n    }\n  }\n  .md\\:w-96 {\n    @media (width >= 48rem) {\n      width: calc(var(--spacing) * 96);\n    }\n  }\n  .md\\:w-\\[var\\(--radix-navigation-menu-viewport-width\\)\\] {\n    @media (width >= 48rem) {\n      width: var(--radix-navigation-menu-viewport-width);\n    }\n  }\n  .md\\:w-auto {\n    @media (width >= 48rem) {\n      width: auto;\n    }\n  }\n  .md\\:max-w-lg {\n    @media (width >= 48rem) {\n      max-width: var(--container-lg);\n    }\n  }\n  .md\\:flex-1 {\n    @media (width >= 48rem) {\n      flex: 1;\n    }\n  }\n  .md\\:grid-cols-2 {\n    @media (width >= 48rem) {\n      grid-template-columns: repeat(2, minmax(0, 1fr));\n    }\n  }\n  .md\\:grid-cols-3 {\n    @media (width >= 48rem) {\n      grid-template-columns: repeat(3, minmax(0, 1fr));\n    }\n  }\n  .md\\:grid-cols-4 {\n    @media (width >= 48rem) {\n      grid-template-columns: repeat(4, minmax(0, 1fr));\n    }\n  }\n  .md\\:border-t-4 {\n    @media (width >= 48rem) {\n      border-top-style: var(--tw-border-style);\n      border-top-width: 4px;\n    }\n  }\n  .md\\:border-l-0 {\n    @media (width >= 48rem) {\n      border-left-style: var(--tw-border-style);\n      border-left-width: 0px;\n    }\n  }\n  .md\\:px-0 {\n    @media (width >= 48rem) {\n      padding-inline: calc(var(--spacing) * 0);\n    }\n  }\n  .md\\:px-6 {\n    @media (width >= 48rem) {\n      padding-inline: calc(var(--spacing) * 6);\n    }\n  }\n  .md\\:pt-4 {\n    @media (width >= 48rem) {\n      padding-top: calc(var(--spacing) * 4);\n    }\n  }\n  .md\\:pb-0 {\n    @media (width >= 48rem) {\n      padding-bottom: calc(var(--spacing) * 0);\n    }\n  }\n  .md\\:pl-0 {\n    @media (width >= 48rem) {\n      padding-left: calc(var(--spacing) * 0);\n    }\n  }\n  .md\\:text-2xl {\n    @media (width >= 48rem) {\n      font-size: var(--text-2xl);\n      line-height: var(--tw-leading, var(--text-2xl--line-height));\n    }\n  }\n  .md\\:text-5xl {\n    @media (width >= 48rem) {\n      font-size: var(--text-5xl);\n      line-height: var(--tw-leading, var(--text-5xl--line-height));\n    }\n  }\n  .md\\:text-sm {\n    @media (width >= 48rem) {\n      font-size: var(--text-sm);\n      line-height: var(--tw-leading, var(--text-sm--line-height));\n    }\n  }\n  .md\\:text-xl {\n    @media (width >= 48rem) {\n      font-size: var(--text-xl);\n      line-height: var(--tw-leading, var(--text-xl--line-height));\n    }\n  }\n  .md\\:opacity-0 {\n    @media (width >= 48rem) {\n      opacity: 0%;\n    }\n  }\n  .md\\:peer-data-\\[variant\\=inset\\]\\:m-2 {\n    @media (width >= 48rem) {\n      &:is(:where(.peer)[data-variant=\"inset\"] ~ *) {\n        margin: calc(var(--spacing) * 2);\n      }\n    }\n  }\n  .md\\:peer-data-\\[variant\\=inset\\]\\:ml-0 {\n    @media (width >= 48rem) {\n      &:is(:where(.peer)[data-variant=\"inset\"] ~ *) {\n        margin-left: calc(var(--spacing) * 0);\n      }\n    }\n  }\n  .md\\:peer-data-\\[variant\\=inset\\]\\:rounded-xl {\n    @media (width >= 48rem) {\n      &:is(:where(.peer)[data-variant=\"inset\"] ~ *) {\n        border-radius: calc(var(--radius) + 4px);\n      }\n    }\n  }\n  .md\\:peer-data-\\[variant\\=inset\\]\\:shadow-sm {\n    @media (width >= 48rem) {\n      &:is(:where(.peer)[data-variant=\"inset\"] ~ *) {\n        --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .md\\:peer-data-\\[variant\\=inset\\]\\:peer-data-\\[state\\=collapsed\\]\\:ml-2 {\n    @media (width >= 48rem) {\n      &:is(:where(.peer)[data-variant=\"inset\"] ~ *) {\n        &:is(:where(.peer)[data-state=\"collapsed\"] ~ *) {\n          margin-left: calc(var(--spacing) * 2);\n        }\n      }\n    }\n  }\n  .md\\:after\\:hidden {\n    @media (width >= 48rem) {\n      &::after {\n        content: var(--tw-content);\n        display: none;\n      }\n    }\n  }\n  .lg\\:col-span-1 {\n    @media (width >= 64rem) {\n      grid-column: span 1 / span 1;\n    }\n  }\n  .lg\\:col-span-2 {\n    @media (width >= 64rem) {\n      grid-column: span 2 / span 2;\n    }\n  }\n  .lg\\:col-span-3 {\n    @media (width >= 64rem) {\n      grid-column: span 3 / span 3;\n    }\n  }\n  .lg\\:mx-0 {\n    @media (width >= 64rem) {\n      margin-inline: calc(var(--spacing) * 0);\n    }\n  }\n  .lg\\:mb-16 {\n    @media (width >= 64rem) {\n      margin-bottom: calc(var(--spacing) * 16);\n    }\n  }\n  .lg\\:block {\n    @media (width >= 64rem) {\n      display: block;\n    }\n  }\n  .lg\\:flex {\n    @media (width >= 64rem) {\n      display: flex;\n    }\n  }\n  .lg\\:hidden {\n    @media (width >= 64rem) {\n      display: none;\n    }\n  }\n  .lg\\:h-full {\n    @media (width >= 64rem) {\n      height: 100%;\n    }\n  }\n  .lg\\:w-1\\/2 {\n    @media (width >= 64rem) {\n      width: calc(1/2 * 100%);\n    }\n  }\n  .lg\\:w-56 {\n    @media (width >= 64rem) {\n      width: calc(var(--spacing) * 56);\n    }\n  }\n  .lg\\:w-64 {\n    @media (width >= 64rem) {\n      width: calc(var(--spacing) * 64);\n    }\n  }\n  .lg\\:max-w-2xl {\n    @media (width >= 64rem) {\n      max-width: var(--container-2xl);\n    }\n  }\n  .lg\\:max-w-xl {\n    @media (width >= 64rem) {\n      max-width: var(--container-xl);\n    }\n  }\n  .lg\\:grid-cols-2 {\n    @media (width >= 64rem) {\n      grid-template-columns: repeat(2, minmax(0, 1fr));\n    }\n  }\n  .lg\\:grid-cols-3 {\n    @media (width >= 64rem) {\n      grid-template-columns: repeat(3, minmax(0, 1fr));\n    }\n  }\n  .lg\\:grid-cols-4 {\n    @media (width >= 64rem) {\n      grid-template-columns: repeat(4, minmax(0, 1fr));\n    }\n  }\n  .lg\\:grid-cols-6 {\n    @media (width >= 64rem) {\n      grid-template-columns: repeat(6, minmax(0, 1fr));\n    }\n  }\n  .lg\\:grid-cols-7 {\n    @media (width >= 64rem) {\n      grid-template-columns: repeat(7, minmax(0, 1fr));\n    }\n  }\n  .lg\\:justify-end {\n    @media (width >= 64rem) {\n      justify-content: flex-end;\n    }\n  }\n  .lg\\:justify-start {\n    @media (width >= 64rem) {\n      justify-content: flex-start;\n    }\n  }\n  .lg\\:gap-8 {\n    @media (width >= 64rem) {\n      gap: calc(var(--spacing) * 8);\n    }\n  }\n  .lg\\:p-8 {\n    @media (width >= 64rem) {\n      padding: calc(var(--spacing) * 8);\n    }\n  }\n  .lg\\:p-12 {\n    @media (width >= 64rem) {\n      padding: calc(var(--spacing) * 12);\n    }\n  }\n  .lg\\:px-6 {\n    @media (width >= 64rem) {\n      padding-inline: calc(var(--spacing) * 6);\n    }\n  }\n  .lg\\:px-8 {\n    @media (width >= 64rem) {\n      padding-inline: calc(var(--spacing) * 8);\n    }\n  }\n  .lg\\:py-24 {\n    @media (width >= 64rem) {\n      padding-block: calc(var(--spacing) * 24);\n    }\n  }\n  .lg\\:text-left {\n    @media (width >= 64rem) {\n      text-align: left;\n    }\n  }\n  .lg\\:text-2xl {\n    @media (width >= 64rem) {\n      font-size: var(--text-2xl);\n      line-height: var(--tw-leading, var(--text-2xl--line-height));\n    }\n  }\n  .lg\\:text-3xl {\n    @media (width >= 64rem) {\n      font-size: var(--text-3xl);\n      line-height: var(--tw-leading, var(--text-3xl--line-height));\n    }\n  }\n  .lg\\:text-6xl {\n    @media (width >= 64rem) {\n      font-size: var(--text-6xl);\n      line-height: var(--tw-leading, var(--text-6xl--line-height));\n    }\n  }\n  .lg\\:text-lg {\n    @media (width >= 64rem) {\n      font-size: var(--text-lg);\n      line-height: var(--tw-leading, var(--text-lg--line-height));\n    }\n  }\n  .lg\\:text-xl {\n    @media (width >= 64rem) {\n      font-size: var(--text-xl);\n      line-height: var(--tw-leading, var(--text-xl--line-height));\n    }\n  }\n  .xl\\:col-span-1 {\n    @media (width >= 80rem) {\n      grid-column: span 1 / span 1;\n    }\n  }\n  .xl\\:col-span-3 {\n    @media (width >= 80rem) {\n      grid-column: span 3 / span 3;\n    }\n  }\n  .xl\\:grid-cols-4 {\n    @media (width >= 80rem) {\n      grid-template-columns: repeat(4, minmax(0, 1fr));\n    }\n  }\n  .\\@\\[250px\\]\\/card\\:text-3xl {\n    @container card (width >= 250px) {\n      font-size: var(--text-3xl);\n      line-height: var(--tw-leading, var(--text-3xl--line-height));\n    }\n  }\n  .\\@\\[540px\\]\\/card\\:block {\n    @container card (width >= 540px) {\n      display: block;\n    }\n  }\n  .\\@\\[540px\\]\\/card\\:hidden {\n    @container card (width >= 540px) {\n      display: none;\n    }\n  }\n  .\\@xl\\/main\\:grid-cols-2 {\n    @container main (width >= 36rem) {\n      grid-template-columns: repeat(2, minmax(0, 1fr));\n    }\n  }\n  .\\@5xl\\/main\\:grid-cols-4 {\n    @container main (width >= 64rem) {\n      grid-template-columns: repeat(4, minmax(0, 1fr));\n    }\n  }\n  .dark\\:border-input {\n    &:is(.dark *) {\n      border-color: var(--input);\n    }\n  }\n  .dark\\:bg-destructive\\/60 {\n    &:is(.dark *) {\n      background-color: var(--destructive);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--destructive) 60%, transparent);\n      }\n    }\n  }\n  .dark\\:bg-input\\/30 {\n    &:is(.dark *) {\n      background-color: var(--input);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--input) 30%, transparent);\n      }\n    }\n  }\n  .dark\\:text-foreground {\n    &:is(.dark *) {\n      color: var(--foreground);\n    }\n  }\n  .dark\\:hover\\:bg-accent\\/50 {\n    &:is(.dark *) {\n      &:hover {\n        @media (hover: hover) {\n          background-color: var(--accent);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--accent) 50%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .dark\\:hover\\:bg-input\\/50 {\n    &:is(.dark *) {\n      &:hover {\n        @media (hover: hover) {\n          background-color: var(--input);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--input) 50%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .dark\\:focus-visible\\:ring-destructive\\/40 {\n    &:is(.dark *) {\n      &:focus-visible {\n        --tw-ring-color: var(--destructive);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);\n        }\n      }\n    }\n  }\n  .dark\\:aria-invalid\\:ring-destructive\\/40 {\n    &:is(.dark *) {\n      &[aria-invalid=\"true\"] {\n        --tw-ring-color: var(--destructive);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);\n        }\n      }\n    }\n  }\n  .dark\\:data-\\[active\\=true\\]\\:aria-invalid\\:ring-destructive\\/40 {\n    &:is(.dark *) {\n      &[data-active=\"true\"] {\n        &[aria-invalid=\"true\"] {\n          --tw-ring-color: var(--destructive);\n          @supports (color: color-mix(in lab, red, red)) {\n            --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .dark\\:\\*\\:data-\\[slot\\=card\\]\\:bg-card {\n    &:is(.dark *) {\n      :is(& > *) {\n        &[data-slot=\"card\"] {\n          background-color: var(--card);\n        }\n      }\n    }\n  }\n  .dark\\:data-\\[state\\=checked\\]\\:bg-primary {\n    &:is(.dark *) {\n      &[data-state=\"checked\"] {\n        background-color: var(--primary);\n      }\n    }\n  }\n  .dark\\:data-\\[state\\=checked\\]\\:bg-primary-foreground {\n    &:is(.dark *) {\n      &[data-state=\"checked\"] {\n        background-color: var(--primary-foreground);\n      }\n    }\n  }\n  .dark\\:data-\\[state\\=unchecked\\]\\:bg-foreground {\n    &:is(.dark *) {\n      &[data-state=\"unchecked\"] {\n        background-color: var(--foreground);\n      }\n    }\n  }\n  .dark\\:data-\\[state\\=unchecked\\]\\:bg-input\\/80 {\n    &:is(.dark *) {\n      &[data-state=\"unchecked\"] {\n        background-color: var(--input);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--input) 80%, transparent);\n        }\n      }\n    }\n  }\n  .dark\\:data-\\[variant\\=destructive\\]\\:focus\\:bg-destructive\\/20 {\n    &:is(.dark *) {\n      &[data-variant=\"destructive\"] {\n        &:focus {\n          background-color: var(--destructive);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--destructive) 20%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .\\[\\&_\\.recharts-cartesian-axis-tick_text\\]\\:fill-muted-foreground {\n    & .recharts-cartesian-axis-tick text {\n      fill: var(--muted-foreground);\n    }\n  }\n  .\\[\\&_\\.recharts-cartesian-grid_line\\[stroke\\=\\'\\#ccc\\'\\]\\]\\:stroke-border\\/50 {\n    & .recharts-cartesian-grid line[stroke='#ccc'] {\n      stroke: var(--border);\n      @supports (color: color-mix(in lab, red, red)) {\n        stroke: color-mix(in oklab, var(--border) 50%, transparent);\n      }\n    }\n  }\n  .\\[\\&_\\.recharts-curve\\.recharts-tooltip-cursor\\]\\:stroke-border {\n    & .recharts-curve.recharts-tooltip-cursor {\n      stroke: var(--border);\n    }\n  }\n  .\\[\\&_\\.recharts-dot\\[stroke\\=\\'\\#fff\\'\\]\\]\\:stroke-transparent {\n    & .recharts-dot[stroke='#fff'] {\n      stroke: transparent;\n    }\n  }\n  .\\[\\&_\\.recharts-layer\\]\\:outline-hidden {\n    & .recharts-layer {\n      --tw-outline-style: none;\n      outline-style: none;\n      @media (forced-colors: active) {\n        outline: 2px solid transparent;\n        outline-offset: 2px;\n      }\n    }\n  }\n  .\\[\\&_\\.recharts-polar-grid_\\[stroke\\=\\'\\#ccc\\'\\]\\]\\:stroke-border {\n    & .recharts-polar-grid [stroke='#ccc'] {\n      stroke: var(--border);\n    }\n  }\n  .\\[\\&_\\.recharts-radial-bar-background-sector\\]\\:fill-muted {\n    & .recharts-radial-bar-background-sector {\n      fill: var(--muted);\n    }\n  }\n  .\\[\\&_\\.recharts-rectangle\\.recharts-tooltip-cursor\\]\\:fill-muted {\n    & .recharts-rectangle.recharts-tooltip-cursor {\n      fill: var(--muted);\n    }\n  }\n  .\\[\\&_\\.recharts-reference-line_\\[stroke\\=\\'\\#ccc\\'\\]\\]\\:stroke-border {\n    & .recharts-reference-line [stroke='#ccc'] {\n      stroke: var(--border);\n    }\n  }\n  .\\[\\&_\\.recharts-sector\\]\\:outline-hidden {\n    & .recharts-sector {\n      --tw-outline-style: none;\n      outline-style: none;\n      @media (forced-colors: active) {\n        outline: 2px solid transparent;\n        outline-offset: 2px;\n      }\n    }\n  }\n  .\\[\\&_\\.recharts-sector\\[stroke\\=\\'\\#fff\\'\\]\\]\\:stroke-transparent {\n    & .recharts-sector[stroke='#fff'] {\n      stroke: transparent;\n    }\n  }\n  .\\[\\&_\\.recharts-surface\\]\\:outline-hidden {\n    & .recharts-surface {\n      --tw-outline-style: none;\n      outline-style: none;\n      @media (forced-colors: active) {\n        outline: 2px solid transparent;\n        outline-offset: 2px;\n      }\n    }\n  }\n  .\\[\\&_\\[cmdk-group-heading\\]\\]\\:px-2 {\n    & [cmdk-group-heading] {\n      padding-inline: calc(var(--spacing) * 2);\n    }\n  }\n  .\\[\\&_\\[cmdk-group-heading\\]\\]\\:py-1\\.5 {\n    & [cmdk-group-heading] {\n      padding-block: calc(var(--spacing) * 1.5);\n    }\n  }\n  .\\[\\&_\\[cmdk-group-heading\\]\\]\\:text-xs {\n    & [cmdk-group-heading] {\n      font-size: var(--text-xs);\n      line-height: var(--tw-leading, var(--text-xs--line-height));\n    }\n  }\n  .\\[\\&_\\[cmdk-group-heading\\]\\]\\:font-medium {\n    & [cmdk-group-heading] {\n      --tw-font-weight: var(--font-weight-medium);\n      font-weight: var(--font-weight-medium);\n    }\n  }\n  .\\[\\&_\\[cmdk-group-heading\\]\\]\\:text-muted-foreground {\n    & [cmdk-group-heading] {\n      color: var(--muted-foreground);\n    }\n  }\n  .\\[\\&_\\[cmdk-group\\]\\]\\:px-2 {\n    & [cmdk-group] {\n      padding-inline: calc(var(--spacing) * 2);\n    }\n  }\n  .\\[\\&_\\[cmdk-group\\]\\:not\\(\\[hidden\\]\\)_\\~\\[cmdk-group\\]\\]\\:pt-0 {\n    & [cmdk-group]:not([hidden]) ~[cmdk-group] {\n      padding-top: calc(var(--spacing) * 0);\n    }\n  }\n  .\\[\\&_\\[cmdk-input-wrapper\\]_svg\\]\\:h-5 {\n    & [cmdk-input-wrapper] svg {\n      height: calc(var(--spacing) * 5);\n    }\n  }\n  .\\[\\&_\\[cmdk-input-wrapper\\]_svg\\]\\:w-5 {\n    & [cmdk-input-wrapper] svg {\n      width: calc(var(--spacing) * 5);\n    }\n  }\n  .\\[\\&_\\[cmdk-input\\]\\]\\:h-12 {\n    & [cmdk-input] {\n      height: calc(var(--spacing) * 12);\n    }\n  }\n  .\\[\\&_\\[cmdk-item\\]\\]\\:px-2 {\n    & [cmdk-item] {\n      padding-inline: calc(var(--spacing) * 2);\n    }\n  }\n  .\\[\\&_\\[cmdk-item\\]\\]\\:py-3 {\n    & [cmdk-item] {\n      padding-block: calc(var(--spacing) * 3);\n    }\n  }\n  .\\[\\&_\\[cmdk-item\\]_svg\\]\\:h-5 {\n    & [cmdk-item] svg {\n      height: calc(var(--spacing) * 5);\n    }\n  }\n  .\\[\\&_\\[cmdk-item\\]_svg\\]\\:w-5 {\n    & [cmdk-item] svg {\n      width: calc(var(--spacing) * 5);\n    }\n  }\n  .\\[\\&_p\\]\\:leading-relaxed {\n    & p {\n      --tw-leading: var(--leading-relaxed);\n      line-height: var(--leading-relaxed);\n    }\n  }\n  .\\[\\&_svg\\]\\:pointer-events-none {\n    & svg {\n      pointer-events: none;\n    }\n  }\n  .\\[\\&_svg\\]\\:invisible {\n    & svg {\n      visibility: hidden;\n    }\n  }\n  .\\[\\&_svg\\]\\:size-4 {\n    & svg {\n      width: calc(var(--spacing) * 4);\n      height: calc(var(--spacing) * 4);\n    }\n  }\n  .\\[\\&_svg\\]\\:shrink-0 {\n    & svg {\n      flex-shrink: 0;\n    }\n  }\n  .\\[\\&_svg\\]\\:text-muted-foreground {\n    & svg {\n      color: var(--muted-foreground);\n    }\n  }\n  .\\[\\&_svg\\:not\\(\\[class\\*\\=\\'size-\\'\\]\\)\\]\\:size-4 {\n    & svg:not([class*='size-']) {\n      width: calc(var(--spacing) * 4);\n      height: calc(var(--spacing) * 4);\n    }\n  }\n  .\\[\\&_svg\\:not\\(\\[class\\*\\=\\'text-\\'\\]\\)\\]\\:text-muted-foreground {\n    & svg:not([class*='text-']) {\n      color: var(--muted-foreground);\n    }\n  }\n  .focus\\:\\[\\&_svg\\:not\\(\\[class\\*\\=\\'text-\\'\\]\\)\\]\\:text-accent-foreground {\n    &:focus {\n      & svg:not([class*='text-']) {\n        color: var(--accent-foreground);\n      }\n    }\n  }\n  .\\[\\&_tr\\]\\:border-b {\n    & tr {\n      border-bottom-style: var(--tw-border-style);\n      border-bottom-width: 1px;\n    }\n  }\n  .\\[\\&_tr\\:last-child\\]\\:border-0 {\n    & tr:last-child {\n      border-style: var(--tw-border-style);\n      border-width: 0px;\n    }\n  }\n  .\\[\\&\\:has\\(\\>\\.day-range-end\\)\\]\\:rounded-r-md {\n    &:has(>.day-range-end) {\n      border-top-right-radius: calc(var(--radius) - 2px);\n      border-bottom-right-radius: calc(var(--radius) - 2px);\n    }\n  }\n  .\\[\\&\\:has\\(\\>\\.day-range-start\\)\\]\\:rounded-l-md {\n    &:has(>.day-range-start) {\n      border-top-left-radius: calc(var(--radius) - 2px);\n      border-bottom-left-radius: calc(var(--radius) - 2px);\n    }\n  }\n  .\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:rounded-md {\n    &:has([aria-selected]) {\n      border-radius: calc(var(--radius) - 2px);\n    }\n  }\n  .\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:bg-accent {\n    &:has([aria-selected]) {\n      background-color: var(--accent);\n    }\n  }\n  .first\\:\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:rounded-l-md {\n    &:first-child {\n      &:has([aria-selected]) {\n        border-top-left-radius: calc(var(--radius) - 2px);\n        border-bottom-left-radius: calc(var(--radius) - 2px);\n      }\n    }\n  }\n  .last\\:\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:rounded-r-md {\n    &:last-child {\n      &:has([aria-selected]) {\n        border-top-right-radius: calc(var(--radius) - 2px);\n        border-bottom-right-radius: calc(var(--radius) - 2px);\n      }\n    }\n  }\n  .\\[\\&\\:has\\(\\[aria-selected\\]\\.day-range-end\\)\\]\\:rounded-r-md {\n    &:has([aria-selected].day-range-end) {\n      border-top-right-radius: calc(var(--radius) - 2px);\n      border-bottom-right-radius: calc(var(--radius) - 2px);\n    }\n  }\n  .\\[\\&\\:has\\(\\[role\\=checkbox\\]\\)\\]\\:pr-0 {\n    &:has([role=checkbox]) {\n      padding-right: calc(var(--spacing) * 0);\n    }\n  }\n  .\\[\\.border-b\\]\\:pb-6 {\n    &:is(.border-b) {\n      padding-bottom: calc(var(--spacing) * 6);\n    }\n  }\n  .\\[\\.border-t\\]\\:pt-6 {\n    &:is(.border-t) {\n      padding-top: calc(var(--spacing) * 6);\n    }\n  }\n  .\\*\\:\\[span\\]\\:last\\:flex {\n    :is(& > *) {\n      &:is(span) {\n        &:last-child {\n          display: flex;\n        }\n      }\n    }\n  }\n  .\\*\\:\\[span\\]\\:last\\:items-center {\n    :is(& > *) {\n      &:is(span) {\n        &:last-child {\n          align-items: center;\n        }\n      }\n    }\n  }\n  .\\*\\:\\[span\\]\\:last\\:gap-2 {\n    :is(& > *) {\n      &:is(span) {\n        &:last-child {\n          gap: calc(var(--spacing) * 2);\n        }\n      }\n    }\n  }\n  .data-\\[variant\\=destructive\\]\\:\\*\\:\\[svg\\]\\:\\!text-destructive {\n    &[data-variant=\"destructive\"] {\n      :is(& > *) {\n        &:is(svg) {\n          color: var(--destructive) !important;\n        }\n      }\n    }\n  }\n  .\\[\\&\\>\\[role\\=checkbox\\]\\]\\:translate-y-\\[2px\\] {\n    &>[role=checkbox] {\n      --tw-translate-y: 2px;\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .\\[\\&\\>button\\]\\:hidden {\n    &>button {\n      display: none;\n    }\n  }\n  .\\[\\&\\>span\\:first-child\\]\\:right-2 {\n    &>span:first-child {\n      right: calc(var(--spacing) * 2);\n    }\n  }\n  .\\[\\&\\>span\\:first-child\\]\\:left-auto {\n    &>span:first-child {\n      left: auto;\n    }\n  }\n  .\\[\\&\\>span\\:last-child\\]\\:truncate {\n    &>span:last-child {\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n    }\n  }\n  .\\[\\&\\>svg\\]\\:pointer-events-none {\n    &>svg {\n      pointer-events: none;\n    }\n  }\n  .\\[\\&\\>svg\\]\\:size-3 {\n    &>svg {\n      width: calc(var(--spacing) * 3);\n      height: calc(var(--spacing) * 3);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:size-3\\.5 {\n    &>svg {\n      width: calc(var(--spacing) * 3.5);\n      height: calc(var(--spacing) * 3.5);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:size-4 {\n    &>svg {\n      width: calc(var(--spacing) * 4);\n      height: calc(var(--spacing) * 4);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:h-2\\.5 {\n    &>svg {\n      height: calc(var(--spacing) * 2.5);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:h-3 {\n    &>svg {\n      height: calc(var(--spacing) * 3);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:w-2\\.5 {\n    &>svg {\n      width: calc(var(--spacing) * 2.5);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:w-3 {\n    &>svg {\n      width: calc(var(--spacing) * 3);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:shrink-0 {\n    &>svg {\n      flex-shrink: 0;\n    }\n  }\n  .\\[\\&\\>svg\\]\\:translate-y-0\\.5 {\n    &>svg {\n      --tw-translate-y: calc(var(--spacing) * 0.5);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:text-current {\n    &>svg {\n      color: currentcolor;\n    }\n  }\n  .\\[\\&\\>svg\\]\\:text-muted-foreground {\n    &>svg {\n      color: var(--muted-foreground);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:text-sidebar-accent-foreground {\n    &>svg {\n      color: var(--sidebar-accent-foreground);\n    }\n  }\n  .\\[\\&\\>tr\\]\\:last\\:border-b-0 {\n    &>tr {\n      &:last-child {\n        border-bottom-style: var(--tw-border-style);\n        border-bottom-width: 0px;\n      }\n    }\n  }\n  .\\[\\&\\[data-panel-group-direction\\=vertical\\]\\>div\\]\\:rotate-90 {\n    &[data-panel-group-direction=vertical]>div {\n      rotate: 90deg;\n    }\n  }\n  .\\[\\&\\[data-size\\]\\]\\:h-8 {\n    &[data-size] {\n      height: calc(var(--spacing) * 8);\n    }\n  }\n  .\\[\\&\\[data-state\\=closed\\]\\>button\\]\\:hidden {\n    &[data-state=closed]>button {\n      display: none;\n    }\n  }\n  .\\[\\&\\[data-state\\=open\\]\\>\\.alert\\]\\:hidden {\n    &[data-state=open]>.alert {\n      display: none;\n    }\n  }\n  .\\[\\&\\[data-state\\=open\\]\\>svg\\]\\:rotate-180 {\n    &[data-state=open]>svg {\n      rotate: 180deg;\n    }\n  }\n  .\\[\\[data-side\\=left\\]\\[data-collapsible\\=offcanvas\\]_\\&\\]\\:-right-2 {\n    [data-side=left][data-collapsible=offcanvas] & {\n      right: calc(var(--spacing) * -2);\n    }\n  }\n  .\\[\\[data-side\\=left\\]\\[data-state\\=collapsed\\]_\\&\\]\\:cursor-e-resize {\n    [data-side=left][data-state=collapsed] & {\n      cursor: e-resize;\n    }\n  }\n  .\\[\\[data-side\\=right\\]\\[data-collapsible\\=offcanvas\\]_\\&\\]\\:-left-2 {\n    [data-side=right][data-collapsible=offcanvas] & {\n      left: calc(var(--spacing) * -2);\n    }\n  }\n  .\\[\\[data-side\\=right\\]\\[data-state\\=collapsed\\]_\\&\\]\\:cursor-w-resize {\n    [data-side=right][data-state=collapsed] & {\n      cursor: w-resize;\n    }\n  }\n  .\\[a\\&\\]\\:hover\\:bg-accent {\n    a& {\n      &:hover {\n        @media (hover: hover) {\n          background-color: var(--accent);\n        }\n      }\n    }\n  }\n  .\\[a\\&\\]\\:hover\\:bg-destructive\\/90 {\n    a& {\n      &:hover {\n        @media (hover: hover) {\n          background-color: var(--destructive);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--destructive) 90%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .\\[a\\&\\]\\:hover\\:bg-primary\\/90 {\n    a& {\n      &:hover {\n        @media (hover: hover) {\n          background-color: var(--primary);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--primary) 90%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .\\[a\\&\\]\\:hover\\:bg-secondary\\/90 {\n    a& {\n      &:hover {\n        @media (hover: hover) {\n          background-color: var(--secondary);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--secondary) 90%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .\\[a\\&\\]\\:hover\\:text-accent-foreground {\n    a& {\n      &:hover {\n        @media (hover: hover) {\n          color: var(--accent-foreground);\n        }\n      }\n    }\n  }\n}\n@property --tw-animation-delay {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0s;\n}\n@property --tw-animation-direction {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: normal;\n}\n@property --tw-animation-duration {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-animation-fill-mode {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: none;\n}\n@property --tw-animation-iteration-count {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-enter-opacity {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-enter-rotate {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-enter-scale {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-enter-translate-x {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-enter-translate-y {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-exit-opacity {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-exit-rotate {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-exit-scale {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-exit-translate-x {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-exit-translate-y {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\nbody {\n  overscroll-behavior: none;\n  background-color: transparent;\n}\n:root {\n  --font-sans: var(--font-inter);\n  --header-height: calc(var(--spacing) * 12 + 1px);\n}\n.theme-scaled {\n  @media (min-width: 1024px) {\n    --radius: 0.6rem;\n    --text-lg: 1.05rem;\n    --text-base: 0.85rem;\n    --text-sm: 0.8rem;\n    --spacing: 0.222222rem;\n  }\n  [data-slot='card'] {\n    --spacing: 0.16rem;\n  }\n  [data-slot='select-trigger'], [data-slot='toggle-group-item'] {\n    --spacing: 0.222222rem;\n  }\n}\n.theme-default, .theme-default-scaled {\n  --primary: var(--color-neutral-600);\n  --primary-foreground: var(--color-neutral-50);\n  &:is(.dark *) {\n    --primary: var(--color-neutral-500);\n    --primary-foreground: var(--color-neutral-50);\n  }\n}\n.theme-blue, .theme-blue-scaled {\n  --primary: var(--color-blue-600);\n  --primary-foreground: var(--color-blue-50);\n  &:is(.dark *) {\n    --primary: var(--color-blue-500);\n    --primary-foreground: var(--color-blue-50);\n  }\n}\n.theme-green, .theme-green-scaled {\n  --primary: var(--color-lime-600);\n  --primary-foreground: var(--color-lime-50);\n  &:is(.dark *) {\n    --primary: var(--color-lime-600);\n    --primary-foreground: var(--color-lime-50);\n  }\n}\n.theme-amber, .theme-amber-scaled {\n  --primary: var(--color-amber-600);\n  --primary-foreground: var(--color-amber-50);\n  &:is(.dark *) {\n    --primary: var(--color-amber-500);\n    --primary-foreground: var(--color-amber-50);\n  }\n}\n.theme-mono, .theme-mono-scaled {\n  --font-sans: var(--font-mono);\n  --primary: var(--color-neutral-600);\n  --primary-foreground: var(--color-neutral-50);\n  &:is(.dark *) {\n    --primary: var(--color-neutral-500);\n    --primary-foreground: var(--color-neutral-50);\n  }\n  .rounded-xs, .rounded-sm, .rounded-md, .rounded-lg, .rounded-xl {\n    border-radius: 0 !important;\n    border-radius: 0;\n  }\n  .shadow-xs, .shadow-sm, .shadow-md, .shadow-lg, .shadow-xl {\n    --tw-shadow: 0 0 #0000 !important;\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow) !important;\n  }\n  [data-slot='toggle-group'], [data-slot='toggle-group-item'] {\n    border-radius: 0 !important;\n    --tw-shadow: 0 0 #0000 !important;\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow) !important;\n  }\n}\n:root, .light, .light-theme {\n  --blue-1: #fbfdff;\n  --blue-2: #f4faff;\n  --blue-3: #e6f4fe;\n  --blue-4: #d5efff;\n  --blue-5: #c2e5ff;\n  --blue-6: #acd8fc;\n  --blue-7: #8ec8f6;\n  --blue-8: #5eb1ef;\n  --blue-9: #0090ff;\n  --blue-10: #0588f0;\n  --blue-11: #0d74ce;\n  --blue-12: #113264;\n}\n@supports (color: color(display-p3 1 1 1)) {\n  @media (color-gamut: p3) {\n    :root, .light, .light-theme {\n      --blue-1: color(display-p3 0.986 0.992 0.999);\n      --blue-2: color(display-p3 0.96 0.979 0.998);\n      --blue-3: color(display-p3 0.912 0.956 0.991);\n      --blue-4: color(display-p3 0.853 0.932 1);\n      --blue-5: color(display-p3 0.788 0.894 0.998);\n      --blue-6: color(display-p3 0.709 0.843 0.976);\n      --blue-7: color(display-p3 0.606 0.777 0.947);\n      --blue-8: color(display-p3 0.451 0.688 0.917);\n      --blue-9: color(display-p3 0.247 0.556 0.969);\n      --blue-10: color(display-p3 0.234 0.523 0.912);\n      --blue-11: color(display-p3 0.15 0.44 0.84);\n      --blue-12: color(display-p3 0.102 0.193 0.379);\n    }\n  }\n}\n.dark, .dark-theme {\n  --blue-1: #0d1520;\n  --blue-2: #111927;\n  --blue-3: #0d2847;\n  --blue-4: #003362;\n  --blue-5: #004074;\n  --blue-6: #104d87;\n  --blue-7: #205d9e;\n  --blue-8: #2870bd;\n  --blue-9: #0090ff;\n  --blue-10: #3b9eff;\n  --blue-11: #70b8ff;\n  --blue-12: #c2e6ff;\n}\n@supports (color: color(display-p3 1 1 1)) {\n  @media (color-gamut: p3) {\n    .dark, .dark-theme {\n      --blue-1: color(display-p3 0.057 0.081 0.122);\n      --blue-2: color(display-p3 0.072 0.098 0.147);\n      --blue-3: color(display-p3 0.078 0.154 0.27);\n      --blue-4: color(display-p3 0.033 0.197 0.37);\n      --blue-5: color(display-p3 0.08 0.245 0.441);\n      --blue-6: color(display-p3 0.14 0.298 0.511);\n      --blue-7: color(display-p3 0.195 0.361 0.6);\n      --blue-8: color(display-p3 0.239 0.434 0.72);\n      --blue-9: color(display-p3 0.247 0.556 0.969);\n      --blue-10: color(display-p3 0.344 0.612 0.973);\n      --blue-11: color(display-p3 0.49 0.72 1);\n      --blue-12: color(display-p3 0.788 0.898 0.99);\n    }\n  }\n}\n:root, .light, .light-theme {\n  --slate-1: #fcfcfd;\n  --slate-2: #f9f9fb;\n  --slate-3: #f0f0f3;\n  --slate-4: #e8e8ec;\n  --slate-5: #e0e1e6;\n  --slate-6: #d9d9e0;\n  --slate-7: #cdced6;\n  --slate-8: #b9bbc6;\n  --slate-9: #8b8d98;\n  --slate-10: #80838d;\n  --slate-11: #60646c;\n  --slate-12: #1c2024;\n}\n@supports (color: color(display-p3 1 1 1)) {\n  @media (color-gamut: p3) {\n    :root, .light, .light-theme {\n      --slate-1: color(display-p3 0.988 0.988 0.992);\n      --slate-2: color(display-p3 0.976 0.976 0.984);\n      --slate-3: color(display-p3 0.94 0.941 0.953);\n      --slate-4: color(display-p3 0.908 0.909 0.925);\n      --slate-5: color(display-p3 0.88 0.881 0.901);\n      --slate-6: color(display-p3 0.85 0.852 0.876);\n      --slate-7: color(display-p3 0.805 0.808 0.838);\n      --slate-8: color(display-p3 0.727 0.733 0.773);\n      --slate-9: color(display-p3 0.547 0.553 0.592);\n      --slate-10: color(display-p3 0.503 0.512 0.549);\n      --slate-11: color(display-p3 0.379 0.392 0.421);\n      --slate-12: color(display-p3 0.113 0.125 0.14);\n    }\n  }\n}\n.dark, .dark-theme {\n  --slate-1: #111113;\n  --slate-2: #18191b;\n  --slate-3: #212225;\n  --slate-4: #272a2d;\n  --slate-5: #2e3135;\n  --slate-6: #363a3f;\n  --slate-7: #43484e;\n  --slate-8: #5a6169;\n  --slate-9: #696e77;\n  --slate-10: #777b84;\n  --slate-11: #b0b4ba;\n  --slate-12: #edeef0;\n}\n@supports (color: color(display-p3 1 1 1)) {\n  @media (color-gamut: p3) {\n    .dark, .dark-theme {\n      --slate-1: color(display-p3 0.067 0.067 0.074);\n      --slate-2: color(display-p3 0.095 0.098 0.105);\n      --slate-3: color(display-p3 0.13 0.135 0.145);\n      --slate-4: color(display-p3 0.156 0.163 0.176);\n      --slate-5: color(display-p3 0.183 0.191 0.206);\n      --slate-6: color(display-p3 0.215 0.226 0.244);\n      --slate-7: color(display-p3 0.265 0.28 0.302);\n      --slate-8: color(display-p3 0.357 0.381 0.409);\n      --slate-9: color(display-p3 0.415 0.431 0.463);\n      --slate-10: color(display-p3 0.469 0.483 0.514);\n      --slate-11: color(display-p3 0.692 0.704 0.728);\n      --slate-12: color(display-p3 0.93 0.933 0.94);\n    }\n  }\n}\n:root, .light, .light-theme {\n  --grass-1: #fbfefb;\n  --grass-2: #f5fbf5;\n  --grass-3: #e9f6e9;\n  --grass-4: #daf1db;\n  --grass-5: #c9e8ca;\n  --grass-6: #b2ddb5;\n  --grass-7: #94ce9a;\n  --grass-8: #65ba74;\n  --grass-9: #46a758;\n  --grass-10: #3e9b4f;\n  --grass-11: #2a7e3b;\n  --grass-12: #203c25;\n}\n@supports (color: color(display-p3 1 1 1)) {\n  @media (color-gamut: p3) {\n    :root, .light, .light-theme {\n      --grass-1: color(display-p3 0.986 0.996 0.985);\n      --grass-2: color(display-p3 0.966 0.983 0.964);\n      --grass-3: color(display-p3 0.923 0.965 0.917);\n      --grass-4: color(display-p3 0.872 0.94 0.865);\n      --grass-5: color(display-p3 0.811 0.908 0.802);\n      --grass-6: color(display-p3 0.733 0.864 0.724);\n      --grass-7: color(display-p3 0.628 0.803 0.622);\n      --grass-8: color(display-p3 0.477 0.72 0.482);\n      --grass-9: color(display-p3 0.38 0.647 0.378);\n      --grass-10: color(display-p3 0.344 0.598 0.342);\n      --grass-11: color(display-p3 0.263 0.488 0.261);\n      --grass-12: color(display-p3 0.151 0.233 0.153);\n    }\n  }\n}\n:root, .light, .light-theme {\n  --cyan-1: #fafdfe;\n  --cyan-2: #f2fafb;\n  --cyan-3: #def7f9;\n  --cyan-4: #caf1f6;\n  --cyan-5: #b5e9f0;\n  --cyan-6: #9ddde7;\n  --cyan-7: #7dcedc;\n  --cyan-8: #3db9cf;\n  --cyan-9: #00a2c7;\n  --cyan-10: #0797b9;\n  --cyan-11: #107d98;\n  --cyan-12: #0d3c48;\n}\n@supports (color: color(display-p3 1 1 1)) {\n  @media (color-gamut: p3) {\n    :root, .light, .light-theme {\n      --cyan-1: color(display-p3 0.982 0.992 0.996);\n      --cyan-2: color(display-p3 0.955 0.981 0.984);\n      --cyan-3: color(display-p3 0.888 0.965 0.975);\n      --cyan-4: color(display-p3 0.821 0.941 0.959);\n      --cyan-5: color(display-p3 0.751 0.907 0.935);\n      --cyan-6: color(display-p3 0.671 0.862 0.9);\n      --cyan-7: color(display-p3 0.564 0.8 0.854);\n      --cyan-8: color(display-p3 0.388 0.715 0.798);\n      --cyan-9: color(display-p3 0.282 0.627 0.765);\n      --cyan-10: color(display-p3 0.264 0.583 0.71);\n      --cyan-11: color(display-p3 0.08 0.48 0.63);\n      --cyan-12: color(display-p3 0.108 0.232 0.277);\n    }\n  }\n}\n:root, .light, .light-theme {\n  --amber-1: #fefdfb;\n  --amber-2: #fefbe9;\n  --amber-3: #fff7c2;\n  --amber-4: #ffee9c;\n  --amber-5: #fbe577;\n  --amber-6: #f3d673;\n  --amber-7: #e9c162;\n  --amber-8: #e2a336;\n  --amber-9: #ffc53d;\n  --amber-10: #ffba18;\n  --amber-11: #ab6400;\n  --amber-12: #4f3422;\n}\n@supports (color: color(display-p3 1 1 1)) {\n  @media (color-gamut: p3) {\n    :root, .light, .light-theme {\n      --amber-1: color(display-p3 0.995 0.992 0.985);\n      --amber-2: color(display-p3 0.994 0.986 0.921);\n      --amber-3: color(display-p3 0.994 0.969 0.782);\n      --amber-4: color(display-p3 0.989 0.937 0.65);\n      --amber-5: color(display-p3 0.97 0.902 0.527);\n      --amber-6: color(display-p3 0.936 0.844 0.506);\n      --amber-7: color(display-p3 0.89 0.762 0.443);\n      --amber-8: color(display-p3 0.85 0.65 0.3);\n      --amber-9: color(display-p3 1 0.77 0.26);\n      --amber-10: color(display-p3 0.959 0.741 0.274);\n      --amber-11: color(display-p3 0.64 0.4 0);\n      --amber-12: color(display-p3 0.294 0.208 0.145);\n    }\n  }\n}\n:root, .light, .light-theme {\n  --red-1: #fffcfc;\n  --red-2: #fff7f7;\n  --red-3: #feebec;\n  --red-4: #ffdbdc;\n  --red-5: #ffcdce;\n  --red-6: #fdbdbe;\n  --red-7: #f4a9aa;\n  --red-8: #eb8e90;\n  --red-9: #e5484d;\n  --red-10: #dc3e42;\n  --red-11: #ce2c31;\n  --red-12: #641723;\n}\n@supports (color: color(display-p3 1 1 1)) {\n  @media (color-gamut: p3) {\n    :root, .light, .light-theme {\n      --red-1: color(display-p3 0.998 0.989 0.988);\n      --red-2: color(display-p3 0.995 0.971 0.971);\n      --red-3: color(display-p3 0.985 0.925 0.925);\n      --red-4: color(display-p3 0.999 0.866 0.866);\n      --red-5: color(display-p3 0.984 0.812 0.811);\n      --red-6: color(display-p3 0.955 0.751 0.749);\n      --red-7: color(display-p3 0.915 0.675 0.672);\n      --red-8: color(display-p3 0.872 0.575 0.572);\n      --red-9: color(display-p3 0.83 0.329 0.324);\n      --red-10: color(display-p3 0.798 0.294 0.285);\n      --red-11: color(display-p3 0.744 0.234 0.222);\n      --red-12: color(display-p3 0.36 0.115 0.143);\n    }\n  }\n}\n.mdxeditor .cm-editor {\n  --sp-font-mono: var(--font-mono);\n  --sp-font-body: var(--font-body);\n  padding: var(--sp-space-4) 0;\n}\n.mdxeditor .sp-editor .cm-editor {\n  padding-bottom: 0;\n}\n.mdxeditor .cm-scroller {\n  padding: 0 !important;\n}\n.mdxeditor .cm-focused {\n  outline: none;\n}\n.mdxeditor .sp-wrapper {\n  overflow: hidden;\n}\n.mdxeditor .sp-layout {\n  border: none;\n}\n.mdxeditor .sp-cm pre {\n  white-space: break-spaces;\n  word-break: break-word;\n  overflow-wrap: anywhere;\n  flex-shrink: 1;\n}\n.mdxeditor .cm-mergeView .cm-scroller {\n  font-family: var(--font-mono);\n  line-height: 1.3rem;\n  font-size: var(--text-xs);\n}\n.mdxeditor .cm-sourceView .cm-scroller {\n  font-family: var(--font-mono);\n  line-height: 1.3rem;\n  font-size: var(--text-xs);\n}\n.mdxeditor .cm-gutters {\n  background: transparent;\n  font-size: var(--text-xxs);\n}\n.mdxeditor .cm-activeLine {\n  background: transparent;\n}\n.mdxeditor .cm-tooltip-autocomplete {\n  background: var(--baseBgSubtle);\n}\n.mdxeditor hr.selected[data-lexical-decorator=true] {\n  outline: 2px solid highlight;\n}\n._editorRoot_1e2ox_53 {\n  --accentBase: var(--blue-1);\n  --accentBgSubtle: var(--blue-2);\n  --accentBg: var(--blue-3);\n  --accentBgHover: var(--blue-4);\n  --accentBgActive: var(--blue-5);\n  --accentLine: var(--blue-6);\n  --accentBorder: var(--blue-7);\n  --accentBorderHover: var(--blue-8);\n  --accentSolid: var(--blue-9);\n  --accentSolidHover: var(--blue-10);\n  --accentText: var(--blue-11);\n  --accentTextContrast: var(--blue-12);\n  --basePageBg: white;\n  --baseBase: var(--slate-1);\n  --baseBgSubtle: var(--slate-2);\n  --baseBg: var(--slate-3);\n  --baseBgHover: var(--slate-4);\n  --baseBgActive: var(--slate-5);\n  --baseLine: var(--slate-6);\n  --baseBorder: var(--slate-7);\n  --baseBorderHover: var(--slate-8);\n  --baseSolid: var(--slate-9);\n  --baseSolidHover: var(--slate-10);\n  --baseText: var(--slate-11);\n  --baseTextContrast: var(--slate-12);\n  --admonitionTipBg: var(--cyan-4);\n  --admonitionTipBorder: var(--cyan-8);\n  --admonitionInfoBg: var(--grass-4);\n  --admonitionInfoBorder: var(--grass-8);\n  --admonitionCautionBg: var(--amber-4);\n  --admonitionCautionBorder: var(--amber-8);\n  --admonitionDangerBg: var(--red-4);\n  --admonitionDangerBorder: var(--red-8);\n  --admonitionNoteBg: var(--slate-4);\n  --admonitionNoteBorder: var(--slate-8);\n  --error-color: var(--red-10);\n  --spacing-0: 0px;\n  --spacing-px: 1px;\n  --spacing-0_5: 0.125rem;\n  --spacing-1: 0.25rem;\n  --spacing-1_5: 0.375rem;\n  --spacing-2: 0.5rem;\n  --spacing-2_5: 0.625rem;\n  --spacing-3: 0.75rem;\n  --spacing-3_5: 0.875rem;\n  --spacing-4: 1rem;\n  --spacing-5: 1.25rem;\n  --spacing-6: 1.5rem;\n  --spacing-7: 1.75rem;\n  --spacing-8: 2rem;\n  --spacing-9: 2.25rem;\n  --spacing-10: 2.5rem;\n  --spacing-11: 2.75rem;\n  --spacing-12: 3rem;\n  --spacing-14: 3.5rem;\n  --spacing-16: 4rem;\n  --spacing-20: 5rem;\n  --spacing-24: 6rem;\n  --spacing-28: 7rem;\n  --spacing-32: 8rem;\n  --spacing-36: 9rem;\n  --spacing-40: 10rem;\n  --spacing-44: 11rem;\n  --spacing-48: 12rem;\n  --spacing-52: 13rem;\n  --spacing-56: 14rem;\n  --spacing-60: 15rem;\n  --spacing-64: 16rem;\n  --spacing-72: 18rem;\n  --spacing-80: 20rem;\n  --spacing-96: 24rem;\n  --radius-none: 0px;\n  --radius-small: var(--spacing-0_5);\n  --radius-base: var(--spacing-1);\n  --radius-medium: var(--spacing-1_5);\n  --radius-large: var(--spacing-2);\n  --radius-extra-large: var(--spacing-3);\n  --radius-full: 9999px;\n  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;\n  --font-body: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue',\r\n    sans-serif;\n  --text-base: 1rem;\n  --text-sm: 0.875rem;\n  --text-xs: 0.75rem;\n  --text-xxs: 0.6rem;\n  font-family: var(--font-body);\n  color: var(--baseText);\n}\n._nestedListItem_1e2ox_158 {\n  list-style: none;\n}\n._toolbarRoot_1e2ox_162 {\n  z-index: 2;\n  display: flex;\n  flex-direction: row;\n  gap: var(--spacing-1);\n  border-radius: var(--radius-medium);\n  padding: var(--spacing-1_5);\n  align-items: center;\n  overflow-x: auto;\n  position: sticky;\n  top: 0;\n  background-color: var(--baseBg);\n  width: inherit;\n}\n._toolbarRoot_1e2ox_162 div[role='separator'] {\n  margin: var(--spacing-2) var(--spacing-1);\n  border-left: 1px solid var(--baseBorder);\n  border-right: 1px solid var(--baseBase);\n  height: var(--spacing-4);\n}\n._toolbarRoot_1e2ox_162 svg {\n  color: var(--baseTextContrast);\n  display: block;\n}\n._readOnlyToolbarRoot_1e2ox_189 {\n  pointer-events: none;\n  background: var(--baseBase);\n}\n._readOnlyToolbarRoot_1e2ox_189 > div {\n  opacity: 0.5;\n}\n._toolbarModeSwitch_1e2ox_198 {\n  opacity: 1 !important;\n  margin-left: auto;\n  align-self: stretch;\n  align-items: stretch;\n  display: flex;\n  border: 1px solid var(--baseBg);\n  border-radius: var(--radius-medium);\n  font-size: var(--text-xs);\n}\n._toolbarModeSwitch_1e2ox_198 ._toolbarToggleItem_1e2ox_208 {\n  padding-inline-end: var(--spacing-4);\n  padding-inline-start: var(--spacing-4);\n}\n._toolbarModeSwitch_1e2ox_198 ._toolbarToggleItem_1e2ox_208:active, ._toolbarModeSwitch_1e2ox_198 ._toolbarToggleItem_1e2ox_208[data-state='on'] {\n  background-color: var(--baseBorder);\n}\n._toolbarGroupOfGroups_1e2ox_219 {\n  display: flex;\n  margin: 0 var(--spacing-1);\n}\n._toolbarToggleSingleGroup_1e2ox_224:first-of-type ._toolbarToggleItem_1e2ox_208:only-child, ._toolbarToggleSingleGroup_1e2ox_224:only-child ._toolbarToggleItem_1e2ox_208:first-child, ._toolbarModeSwitch_1e2ox_198 ._toolbarToggleItem_1e2ox_208:first-child {\n  border-top-left-radius: var(--radius-base);\n  border-bottom-left-radius: var(--radius-base);\n}\n._toolbarToggleSingleGroup_1e2ox_224:last-of-type ._toolbarToggleItem_1e2ox_208:only-child, ._toolbarToggleSingleGroup_1e2ox_224:only-child ._toolbarToggleItem_1e2ox_208:last-child, ._toolbarModeSwitch_1e2ox_198 ._toolbarToggleItem_1e2ox_208:last-child {\n  border-top-right-radius: var(--radius-base);\n  border-bottom-right-radius: var(--radius-base);\n}\n._toolbarToggleItem_1e2ox_208, ._toolbarButton_1e2ox_239 {\n  border: 0;\n  background-color: transparent;\n  font-size: inherit;\n  -webkit-appearance: none;\n  -moz-appearance: none;\n  appearance: none;\n  all: unset;\n  box-sizing: border-box;\n  cursor: default;\n  padding: var(--spacing-0_5);\n}\n@media (hover: hover) {\n  ._toolbarToggleItem_1e2ox_208:hover, ._toolbarButton_1e2ox_239:hover {\n    background-color: var(--baseBgActive);\n  }\n}\n._toolbarToggleItem_1e2ox_208:active svg, ._toolbarButton_1e2ox_239:active svg {\n  transform: translate(1px, 1px);\n}\n._toolbarToggleItem_1e2ox_208[data-state='on'], ._toolbarButton_1e2ox_239[data-state='on'], ._toolbarToggleItem_1e2ox_208:active, ._toolbarButton_1e2ox_239:active {\n  color: var(--baseTextContrast);\n  background-color: var(--baseBgActive);\n}\n._toolbarToggleItem_1e2ox_208[data-disabled], ._toolbarButton_1e2ox_239[data-disabled] {\n  pointer-events: none;\n}\n._toolbarToggleItem_1e2ox_208[data-disabled] svg, ._toolbarButton_1e2ox_239[data-disabled] svg {\n  color: var(--baseBorderHover);\n}\n._toolbarButton_1e2ox_239 {\n  border-radius: var(--radius-base);\n}\n._toolbarButton_1e2ox_239 + ._toolbarButton_1e2ox_239 {\n  margin-left: var(--spacing-1);\n}\n._activeToolbarButton_1e2ox_275 {\n  color: var(--accentText);\n}\n._toolbarToggleSingleGroup_1e2ox_224 {\n  display: flex;\n  align-items: center;\n  white-space: nowrap;\n}\n._toolbarNodeKindSelectContainer_1e2ox_285, ._toolbarButtonDropdownContainer_1e2ox_286, ._toolbarCodeBlockLanguageSelectContent_1e2ox_287, ._selectContainer_1e2ox_288 {\n  filter: drop-shadow(0 2px 2px rgb(0 0 0 / 0.2));\n  z-index: 3;\n  width: var(--spacing-36);\n  border-bottom-left-radius: var(--radius-base);\n  border-bottom-right-radius: var(--radius-base);\n  background-color: var(--basePageBg);\n  font-size: var(--text-sm);\n}\n._toolbarButtonDropdownContainer_1e2ox_286 {\n  border-top-right-radius: var(--radius-base);\n}\n._toolbarButtonDropdownContainer_1e2ox_286 ._selectItem_1e2ox_301:first-child {\n  border-top-right-radius: var(--radius-base);\n}\n._toolbarNodeKindSelectTrigger_1e2ox_306, ._toolbarButtonSelectTrigger_1e2ox_307, ._selectTrigger_1e2ox_308 {\n  border: 0;\n  background-color: transparent;\n  display: flex;\n  color: inherit;\n  align-items: center;\n  width: var(--spacing-36);\n  padding: var(--spacing-0_5) var(--spacing-1);\n  padding-inline-start: var(--spacing-2);\n  border-radius: var(--radius-medium);\n  white-space: nowrap;\n  flex-wrap: nowrap;\n  font-size: var(--text-sm);\n  background-color: var(--basePageBg);\n  margin: 0 var(--spacing-1);\n}\n._toolbarNodeKindSelectTrigger_1e2ox_306[data-state='open'], ._toolbarButtonSelectTrigger_1e2ox_307[data-state='open'], ._selectTrigger_1e2ox_308[data-state='open'] {\n  filter: drop-shadow(0 2px 2px rgb(0 0 0 / 0.2));\n  border-bottom-right-radius: var(--radius-none);\n  border-bottom-left-radius: var(--radius-none);\n}\n._selectTrigger_1e2ox_308[data-placeholder] > span:first-child {\n  color: var(--baseBorderHover);\n}\n._toolbarButtonSelectTrigger_1e2ox_307 {\n  width: auto;\n  padding-inline-start: var(--spacing-2);\n  padding-inline-end: var(--spacing-1);\n  padding-block: var(--spacing-0_5);\n}\n._toolbarCodeBlockLanguageSelectTrigger_1e2ox_343, ._toolbarCodeBlockLanguageSelectContent_1e2ox_287 {\n  width: var(--spacing-48);\n}\n._toolbarNodeKindSelectItem_1e2ox_348, ._selectItem_1e2ox_301 {\n  cursor: default;\n  display: flex;\n  padding: var(--spacing-2);\n}\n._toolbarNodeKindSelectItem_1e2ox_348[data-highlighted], ._selectItem_1e2ox_301[data-highlighted] {\n  background-color: var(--baseBg);\n}\n._toolbarNodeKindSelectItem_1e2ox_348[data-state='checked'], ._selectItem_1e2ox_301[data-state='checked'] {\n  color: var(--baseTextContrast);\n  background-color: var(--baseBg);\n}\n._toolbarNodeKindSelectItem_1e2ox_348[data-highlighted], ._selectItem_1e2ox_301[data-highlighted] {\n  outline: none;\n}\n._toolbarNodeKindSelectItem_1e2ox_348:last-child, ._selectItem_1e2ox_301:last-child {\n  border-bottom-left-radius: var(--radius-base);\n  border-bottom-right-radius: var(--radius-base);\n}\n._toolbarNodeKindSelectDropdownArrow_1e2ox_373, ._selectDropdownArrow_1e2ox_374 {\n  margin-left: auto;\n  display: flex;\n  align-items: center;\n}\n._contentEditable_1e2ox_380 {\n  box-sizing: border-box;\n  width: 100%;\n  color: var(--baseTextContrast);\n  padding: var(--spacing-3);\n}\n._contentEditable_1e2ox_380:focus {\n  outline: none;\n}\n._codeMirrorWrapper_1e2ox_392 {\n  margin-bottom: var(--spacing-5);\n  border: 1px solid var(--baseLine);\n  border-radius: var(--radius-medium);\n  overflow: hidden;\n  padding: 0.8rem;\n  position: relative;\n}\n._sandPackWrapper_1e2ox_401 {\n  margin-bottom: var(--spacing-5);\n  border: 1px solid var(--baseLine);\n  border-radius: var(--radius-medium);\n  overflow: hidden;\n  position: relative;\n}\n._codeMirrorToolbar_1e2ox_409 {\n  position: absolute;\n  right: 0;\n  top: 0;\n  display: flex;\n  gap: var(--spacing-1);\n  padding: var(--spacing-1);\n  z-index: 1;\n  background-color: var(--baseBase);\n  border-bottom-left-radius: var(--radius-base);\n}\n._frontmatterWrapper_1e2ox_413 {\n  border-radius: var(--radius-medium);\n  padding: var(--spacing-3);\n  background-color: var(--baseBgSubtle);\n}\n._frontmatterWrapper_1e2ox_413[data-expanded='true'] {\n  margin-bottom: var(--spacing-10);\n}\n._frontmatterToggleButton_1e2ox_423 {\n  border: 0;\n  background-color: transparent;\n  font-size: inherit;\n  -webkit-appearance: none;\n  -moz-appearance: none;\n  appearance: none;\n  all: unset;\n  box-sizing: border-box;\n  cursor: default;\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-1);\n  font-size: var(--text-sm);\n}\n._propertyPanelTitle_1e2ox_431 {\n  font-size: var(--text-xs);\n  font-weight: 400;\n  margin: 0;\n  padding-top: var(--spacing-2);\n  padding-left: var(--spacing-2);\n}\n._propertyEditorTable_1e2ox_439 {\n  table-layout: fixed;\n  border-spacing: var(--spacing-2);\n}\n._propertyEditorTable_1e2ox_439 th {\n  text-align: left;\n  font-size: var(--text-sm);\n  padding: var(--spacing-2) var(--spacing-3);\n}\n._propertyEditorTable_1e2ox_439 col:nth-child(1) {\n  width: 30%;\n}\n._propertyEditorTable_1e2ox_439 col:nth-child(2) {\n  width: 70%;\n}\n._propertyEditorTable_1e2ox_439 td:last-child ._iconButton_1e2ox_457 {\n  margin-left: var(--spacing-4);\n  margin-right: var(--spacing-4);\n}\n._propertyEditorTable_1e2ox_439 ._readOnlyColumnCell_1e2ox_462 {\n  padding-left: 0;\n}\n._propertyEditorLabelCell_1e2ox_467 {\n  font-weight: 400;\n}\n._readOnlyColumnCell_1e2ox_462 {\n  padding-left: 0;\n}\n._buttonsFooter_1e2ox_475 {\n  display: flex;\n  justify-content: flex-end;\n  gap: var(--spacing-2);\n}\n._propertyEditorInput_1e2ox_481 {\n  border: 0;\n  background-color: transparent;\n  font-size: inherit;\n  -webkit-appearance: none;\n  -moz-appearance: none;\n  appearance: none;\n  all: unset;\n  box-sizing: border-box;\n  cursor: default;\n  width: 100%;\n  padding: var(--spacing-2) var(--spacing-3);\n  border-radius: var(--radius-base);\n  border: 1px solid var(--baseBorder);\n  background-color: var(--baseBase);\n  font-size: var(--text-sm);\n}\n._iconButton_1e2ox_457 {\n  border: 0;\n  background-color: transparent;\n  font-size: inherit;\n  -webkit-appearance: none;\n  -moz-appearance: none;\n  appearance: none;\n  all: unset;\n  box-sizing: border-box;\n  cursor: default;\n  color: var(--baseText);\n}\n@media (hover: hover) {\n  ._iconButton_1e2ox_457:hover {\n    color: var(--baseTextContrast);\n  }\n}\n._iconButton_1e2ox_457:disabled, ._iconButton_1e2ox_457:disabled:hover {\n  color: var(--baseLine);\n}\n._primaryButton_1e2ox_507, ._secondaryButton_1e2ox_508 {\n  border: 0;\n  background-color: transparent;\n  font-size: inherit;\n  -webkit-appearance: none;\n  -moz-appearance: none;\n  appearance: none;\n  all: unset;\n  box-sizing: border-box;\n  cursor: default;\n  padding: var(--spacing-2) var(--spacing-3);\n  border: 1px solid var(--accentBorder);\n  background-color: var(--accentSolidHover);\n  color: var(--baseBase);\n  font-size: var(--text-xs);\n  border-radius: var(--radius-medium);\n}\n._primaryButton_1e2ox_507:disabled, ._secondaryButton_1e2ox_508:disabled {\n  background: var(--accentLine);\n  border-color: var(--accentBg);\n}\n._smallButton_1e2ox_523 {\n  font-size: var(--text-xs);\n  padding: var(--spacing-1) var(--spacing-2);\n  border-radius: var(--radius-base);\n}\n._secondaryButton_1e2ox_508 {\n  border: 1px solid var(--baseBorder);\n  background-color: var(--baseSolidHover);\n  color: var(--baseBase);\n}\n._dialogForm_1e2ox_535 {\n  display: flex;\n  flex-direction: row;\n  gap: var(--spacing-2);\n}\n._linkDialogEditForm_1e2ox_541 {\n  display: flex;\n  flex-direction: column;\n  align-items: stretch;\n  gap: var(--spacing-2);\n  padding: 0;\n}\n._linkDialogInputContainer_1e2ox_549 {\n  display: flex;\n  flex-direction: column;\n  align-items: stretch;\n}\n._linkDialogInputWrapper_1e2ox_555 {\n  display: flex;\n  align-items: center;\n  background-color: var(--baseBase);\n  border-radius: var(--radius-base);\n  border: 1px solid var(--baseBorder);\n}\n._linkDialogInputWrapper_1e2ox_555[data-visible-dropdown='true'] {\n  border-bottom-left-radius: var(--radius-none);\n  border-bottom-right-radius: var(--radius-none);\n  border-bottom-width: 0;\n}\n._linkDialogInputWrapper_1e2ox_555 > button {\n  border: 0;\n  background-color: transparent;\n  font-size: inherit;\n  -webkit-appearance: none;\n  -moz-appearance: none;\n  appearance: none;\n  all: unset;\n  box-sizing: border-box;\n  cursor: default;\n  padding-right: var(--spacing-2);\n}\n._linkDialogInput_1e2ox_549, ._dialogInput_1e2ox_576 {\n  border: 0;\n  background-color: transparent;\n  font-size: inherit;\n  -webkit-appearance: none;\n  -moz-appearance: none;\n  appearance: none;\n  all: unset;\n  box-sizing: border-box;\n  cursor: default;\n  width: 20rem;\n  padding: var(--spacing-2) var(--spacing-3);\n  font-size: var(--text-sm);\n}\n._linkDialogInput_1e2ox_549::-moz-placeholder, ._dialogInput_1e2ox_576::-moz-placeholder {\n  color: var(--baseBorder);\n}\n._linkDialogInput_1e2ox_549::placeholder, ._dialogInput_1e2ox_576::placeholder {\n  color: var(--baseBorder);\n}\n._linkDialogAnchor_1e2ox_587 {\n  position: fixed;\n  background-color: highlight;\n  z-index: -1;\n}\n._linkDialogAnchor_1e2ox_587[data-visible='true'] {\n  visibility: visible;\n}\n._linkDialogAnchor_1e2ox_587[data-visible='false'] {\n  visibility: hidden;\n}\n._linkDialogPopoverContent_1e2ox_601, ._tableColumnEditorPopoverContent_1e2ox_602, ._dialogContent_1e2ox_603 {\n  filter: drop-shadow(0 2px 2px rgb(0 0 0 / 0.2));\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-0_5);\n  border-radius: var(--radius-medium);\n  border: 1px solid var(--baseBg);\n  background-color: var(--basePageBg);\n  padding: var(--spacing-1) var(--spacing-1);\n  font-size: var(--text-sm);\n}\n._largeDialogContent_1e2ox_615 {\n  filter: drop-shadow(0 2px 2px rgb(0 0 0 / 0.2));\n  gap: var(--spacing-0_5);\n  border-radius: var(--radius-medium);\n  border: 1px solid var(--baseBorder);\n  background-color: var(--baseBgSubtle);\n  padding: var(--spacing-4);\n  font-size: var(--text-sm);\n}\n._dialogTitle_1e2ox_625 {\n  font-size: var(--text-base);\n  font-weight: 600;\n  padding-left: var(--spacing-2);\n}\n._dialogCloseButton_1e2ox_631 {\n  border: 0;\n  background-color: transparent;\n  font-size: inherit;\n  -webkit-appearance: none;\n  -moz-appearance: none;\n  appearance: none;\n  all: unset;\n  box-sizing: border-box;\n  cursor: default;\n  position: absolute;\n  top: 10px;\n  right: 10px;\n}\n._popoverContent_1e2ox_638 {\n  filter: drop-shadow(0 2px 2px rgb(0 0 0 / 0.2));\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-0_5);\n  border-radius: var(--radius-medium);\n  background-color: var(--baseBgSubtle);\n  padding: var(--spacing-2) var(--spacing-2);\n  font-size: var(--text-sm);\n  z-index: 1;\n}\n._popoverArrow_1e2ox_650 {\n  fill: var(--basePageBg);\n}\n._linkDialogPreviewAnchor_1e2ox_654 {\n  margin-right: var(--spacing-1);\n  display: flex;\n  align-items: center;\n  color: var(--accentText);\n  text-decoration: none;\n  border: 1px solid transparent;\n}\n@media (hover: hover) {\n  ._linkDialogPreviewAnchor_1e2ox_654:hover {\n    color: var(--accentSolidHover);\n  }\n}\n._linkDialogPreviewAnchor_1e2ox_654 span {\n  max-width: 14rem;\n  overflow-x: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n._tooltipTrigger_1e2ox_677 {\n  align-self: center;\n}\n._tooltipContent_1e2ox_681 {\n  z-index: 2;\n  position: relative;\n  border-radius: var(--radius-medium);\n  padding: var(--spacing-1) var(--spacing-2);\n  font-size: var(--text-xs);\n  background-color: var(--baseText);\n  color: var(--baseBase);\n}\n._tooltipContent_1e2ox_681 svg {\n  fill: var(--baseText);\n}\n._actionButton_1e2ox_695 {\n  border: 0;\n  background-color: transparent;\n  font-size: inherit;\n  -webkit-appearance: none;\n  -moz-appearance: none;\n  appearance: none;\n  all: unset;\n  box-sizing: border-box;\n  cursor: default;\n  padding: var(--spacing-1);\n  color: var(--baseTextContrast);\n  padding: var(--spacing-1) var(--spacing-1);\n  border-radius: var(--radius-medium);\n  color: var(--baseTextContrast);\n}\n._actionButton_1e2ox_695 svg {\n  display: block;\n}\n@media (hover: hover) {\n  ._actionButton_1e2ox_695:hover {\n    background-color: var(--baseBg);\n  }\n}\n._actionButton_1e2ox_695:active svg {\n  transform: translate(1px, 1px);\n}\n._actionButton_1e2ox_695[data-state='on'], ._actionButton_1e2ox_695:active {\n  background-color: var(--baseBg);\n  color: var(--baseTextContrast);\n}\n._primaryActionButton_1e2ox_702 {\n  background-color: var(--accentSolid);\n  color: var(--baseBase);\n}\n@media (hover: hover) {\n  ._primaryActionButton_1e2ox_702:hover {\n    background-color: var(--accentSolidHover);\n    color: var(--baseBase);\n  }\n}\n._tableEditor_1e2ox_714 {\n  table-layout: fixed;\n  width: 100%;\n  height: 100%;\n  border-spacing: 0;\n  border-collapse: collapse;\n}\n._tableEditor_1e2ox_714 thead > tr > th {\n  text-align: right;\n}\n._tableEditor_1e2ox_714 > tbody > tr > td:not(._toolCell_1e2ox_725), ._tableEditor_1e2ox_714 > tbody > tr > th:not(._toolCell_1e2ox_725):not([data-tool-cell='true']) {\n  border: 1px solid var(--baseBgActive);\n  padding: var(--spacing-1) var(--spacing-2);\n  white-space: normal;\n}\n:is(._tableEditor_1e2ox_714 > tbody > tr > td:not(._toolCell_1e2ox_725),._tableEditor_1e2ox_714 > tbody > tr > th:not(._toolCell_1e2ox_725):not([data-tool-cell='true'])) > div {\n  outline: none;\n}\n:is(._tableEditor_1e2ox_714 > tbody > tr > td:not(._toolCell_1e2ox_725),._tableEditor_1e2ox_714 > tbody > tr > th:not(._toolCell_1e2ox_725):not([data-tool-cell='true'])) > div > p {\n  margin: 0;\n}\n[data-active='true']:is(._tableEditor_1e2ox_714 > tbody > tr > td:not(._toolCell_1e2ox_725),._tableEditor_1e2ox_714 > tbody > tr > th:not(._toolCell_1e2ox_725):not([data-tool-cell='true'])) {\n  outline: solid 1px var(--baseSolid);\n}\n._tableEditor_1e2ox_714 ._tableColumnEditorTrigger_1e2ox_744, ._tableEditor_1e2ox_714 ._tableRowEditorTrigger_1e2ox_745, ._tableEditor_1e2ox_714 ._addRowButton_1e2ox_746, ._tableEditor_1e2ox_714 ._addColumnButton_1e2ox_747, ._tableEditor_1e2ox_714 ._iconButton_1e2ox_457 {\n  opacity: 0.15;\n}\n@media (hover: hover) {\n  ._tableEditor_1e2ox_714:hover ._tableColumnEditorTrigger_1e2ox_744, ._tableEditor_1e2ox_714:hover ._tableRowEditorTrigger_1e2ox_745, ._tableEditor_1e2ox_714:hover ._addRowButton_1e2ox_746, ._tableEditor_1e2ox_714:hover ._addColumnButton_1e2ox_747, ._tableEditor_1e2ox_714:hover ._iconButton_1e2ox_457 {\n    opacity: 0.3;\n  }\n  ._tableEditor_1e2ox_714:hover ._tableColumnEditorTrigger_1e2ox_744:hover, ._tableEditor_1e2ox_714:hover ._tableRowEditorTrigger_1e2ox_745:hover, ._tableEditor_1e2ox_714:hover ._addRowButton_1e2ox_746:hover, ._tableEditor_1e2ox_714:hover ._addColumnButton_1e2ox_747:hover, ._tableEditor_1e2ox_714:hover ._iconButton_1e2ox_457:hover {\n    opacity: 1;\n  }\n}\n._toolCell_1e2ox_725 {\n  text-align: right;\n}\n._toolCell_1e2ox_725 button {\n  margin: auto;\n  display: block;\n}\n._tableColumnEditorTrigger_1e2ox_744 {\n  border: 0;\n  background-color: transparent;\n  font-size: inherit;\n  -webkit-appearance: none;\n  -moz-appearance: none;\n  appearance: none;\n  all: unset;\n  box-sizing: border-box;\n  cursor: default;\n  padding: var(--spacing-1);\n  color: var(--baseTextContrast);\n  padding: var(--spacing-1);\n  border-radius: var(--radius-full);\n  opacity: 0.2;\n}\n._tableColumnEditorTrigger_1e2ox_744 svg {\n  display: block;\n}\n@media (hover: hover) {\n  ._tableColumnEditorTrigger_1e2ox_744:hover {\n    background-color: var(--baseBg);\n  }\n}\n._tableColumnEditorTrigger_1e2ox_744:active svg {\n  transform: translate(1px, 1px);\n}\n._tableColumnEditorTrigger_1e2ox_744[data-state='on'], ._tableColumnEditorTrigger_1e2ox_744:active {\n  background-color: var(--baseBg);\n  color: var(--baseTextContrast);\n}\n._tableColumnEditorTrigger_1e2ox_744[data-active='true'] {\n  opacity: 1 !important;\n}\n._tableColumnEditorToolbar_1e2ox_789 {\n  display: flex;\n}\n._tableColumnEditorToolbar_1e2ox_789 > button {\n  border: 0;\n  background-color: transparent;\n  font-size: inherit;\n  -webkit-appearance: none;\n  -moz-appearance: none;\n  appearance: none;\n  all: unset;\n  box-sizing: border-box;\n  cursor: default;\n  padding: var(--spacing-1);\n  color: var(--baseTextContrast);\n}\n._tableColumnEditorToolbar_1e2ox_789 > button svg {\n  display: block;\n}\n@media (hover: hover) {\n  ._tableColumnEditorToolbar_1e2ox_789 > button:hover {\n    background-color: var(--baseBg);\n  }\n}\n._tableColumnEditorToolbar_1e2ox_789 > button:active svg {\n  transform: translate(1px, 1px);\n}\n._tableColumnEditorToolbar_1e2ox_789 > button[data-state='on'], ._tableColumnEditorToolbar_1e2ox_789 > button:active {\n  background-color: var(--baseBg);\n  color: var(--baseTextContrast);\n}\n._tableColumnEditorToolbar_1e2ox_789 [role='separator'] {\n  margin-left: var(--spacing-1);\n  margin-right: var(--spacing-1);\n}\n._toggleGroupRoot_1e2ox_802 {\n  display: inline-flex;\n}\n._toggleGroupRoot_1e2ox_802 button {\n  border: 0;\n  background-color: transparent;\n  font-size: inherit;\n  -webkit-appearance: none;\n  -moz-appearance: none;\n  appearance: none;\n  all: unset;\n  box-sizing: border-box;\n  cursor: default;\n  padding: var(--spacing-1);\n  color: var(--baseTextContrast);\n}\n._toggleGroupRoot_1e2ox_802 button svg {\n  display: block;\n}\n@media (hover: hover) {\n  ._toggleGroupRoot_1e2ox_802 button:hover {\n    background-color: var(--baseBg);\n  }\n}\n._toggleGroupRoot_1e2ox_802 button:active svg {\n  transform: translate(1px, 1px);\n}\n._toggleGroupRoot_1e2ox_802 button[data-state='on'], ._toggleGroupRoot_1e2ox_802 button:active {\n  background-color: var(--baseBg);\n  color: var(--baseTextContrast);\n}\n._toggleGroupRoot_1e2ox_802 button:first-child {\n  border-top-left-radius: var(--radius-base);\n  border-bottom-left-radius: var(--radius-base);\n}\n._toggleGroupRoot_1e2ox_802 button:last-child {\n  border-top-right-radius: var(--radius-base);\n  border-bottom-right-radius: var(--radius-base);\n}\n._tableToolsColumn_1e2ox_820 {\n  width: 2rem;\n}\n._tableToolsColumn_1e2ox_820 button {\n  margin: auto;\n  display: block;\n}\n._leftAlignedCell_1e2ox_829 {\n  text-align: left;\n}\n._rightAlignedCell_1e2ox_833 {\n  text-align: right;\n}\n._centeredCell_1e2ox_837 {\n  text-align: center;\n}\n._addColumnButton_1e2ox_747, ._addRowButton_1e2ox_746 {\n  border: 0;\n  background-color: transparent;\n  font-size: inherit;\n  -webkit-appearance: none;\n  -moz-appearance: none;\n  appearance: none;\n  all: unset;\n  box-sizing: border-box;\n  cursor: default;\n  padding: var(--spacing-1);\n  color: var(--baseTextContrast);\n  background-color: var(--baseBase);\n  display: flex;\n  align-items: center;\n}\n._addColumnButton_1e2ox_747 svg, ._addRowButton_1e2ox_746 svg {\n  display: block;\n}\n@media (hover: hover) {\n  ._addColumnButton_1e2ox_747:hover, ._addRowButton_1e2ox_746:hover {\n    background-color: var(--baseBg);\n  }\n}\n._addColumnButton_1e2ox_747:active svg, ._addRowButton_1e2ox_746:active svg {\n  transform: translate(1px, 1px);\n}\n._addColumnButton_1e2ox_747[data-state='on'], ._addRowButton_1e2ox_746[data-state='on'], ._addColumnButton_1e2ox_747:active, ._addRowButton_1e2ox_746:active {\n  background-color: var(--baseBg);\n  color: var(--baseTextContrast);\n}\n._addColumnButton_1e2ox_747 svg, ._addRowButton_1e2ox_746 svg {\n  margin: auto;\n}\n._addRowButton_1e2ox_746 {\n  width: 100%;\n  margin-top: var(--spacing-px);\n  box-sizing: border-box;\n  border-bottom-right-radius: var(--radius-medium);\n  border-bottom-left-radius: var(--radius-medium);\n}\n._addColumnButton_1e2ox_747 {\n  margin-left: var(--spacing-px);\n  height: 100%;\n  border-top-right-radius: var(--radius-medium);\n  border-bottom-right-radius: var(--radius-medium);\n}\n._dialogOverlay_1e2ox_870 {\n  position: fixed;\n  inset: 0;\n  animation: _overlayShow_1e2ox_1 150ms cubic-bezier(0.16, 1, 0.3, 1);\n  background-color: var(--baseBase);\n  z-index: 51;\n  opacity: 0.5;\n}\n._dialogContent_1e2ox_603, ._largeDialogContent_1e2ox_615 {\n  position: fixed;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  animation: _contentShow_1e2ox_1 150ms cubic-bezier(0.16, 1, 0.3, 1);\n  z-index: 52;\n}\n._dialogContent_1e2ox_603:focus, ._largeDialogContent_1e2ox_615:focus {\n  outline: none;\n}\n@keyframes _overlayShow_1e2ox_1 {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 0.5;\n  }\n}\n@keyframes _contentShow_1e2ox_1 {\n  from {\n    opacity: 0;\n    transform: translate(-50%, -48%) scale(0.96);\n  }\n  to {\n    opacity: 1;\n    transform: translate(-50%, -50%) scale(1);\n  }\n}\n._focusedImage_1e2ox_916 {\n  outline: highlight solid 2px;\n}\n._imageWrapper_1e2ox_920 {\n  display: inline-block;\n  position: relative;\n}\n._imageWrapper_1e2ox_920[draggable='true'] {\n  cursor: move;\n  cursor: grab;\n  cursor: -webkit-grab;\n}\n._editImageToolbar_1e2ox_933 {\n  position: absolute;\n  right: 0;\n  top: 0;\n  display: flex;\n  gap: var(--spacing-1);\n  padding: var(--spacing-1);\n  z-index: 1;\n  background-color: var(--baseBase);\n  border-bottom-left-radius: var(--radius-base);\n}\n._editImageButton_1e2ox_937 svg {\n  display: block;\n}\n._inlineEditor_1e2ox_943 {\n  display: inline-flex;\n  border-radius: var(--radius-medium);\n  padding: var(--spacing-1);\n  gap: var(--spacing-2);\n  align-items: center;\n  background: var(--baseBg);\n}\n._blockEditor_1e2ox_952 {\n  display: flex;\n  justify-content: stretch;\n  border-radius: var(--radius-medium);\n  padding: var(--spacing-2);\n  gap: var(--spacing-2);\n  align-items: center;\n  background: var(--baseBg);\n}\n._blockEditor_1e2ox_952 ._nestedEditor_1e2ox_961 {\n  flex-grow: 1;\n}\n._nestedEditor_1e2ox_961 {\n  background: var(--basePageBg);\n  padding: var(--spacing-1) var(--spacing-2);\n  border-radius: var(--radius-medium);\n}\n._nestedEditor_1e2ox_961 > p {\n  margin: 0;\n}\n._nestedEditor_1e2ox_961:focus {\n  outline: none;\n}\n._genericComponentName_1e2ox_980 {\n  font-size: var(--text-sm);\n  color: var(--baseText);\n  padding-right: var(--spacing-2);\n}\n._diffSourceToggleWrapper_1e2ox_986 {\n  margin-left: auto;\n  pointer-events: auto;\n  opacity: 1;\n  position: sticky;\n  right: 0;\n}\n._diffSourceToggle_1e2ox_986 {\n  border-radius: var(--radius-medium);\n  background-color: var(--baseBase);\n  display: flex;\n}\n._diffSourceToggle_1e2ox_986 ._toolbarToggleItem_1e2ox_208 {\n  padding: 0;\n}\n._diffSourceToggle_1e2ox_986 ._toolbarToggleItem_1e2ox_208 > span {\n  display: block;\n  padding: var(--spacing-1) var(--spacing-1);\n}\n._selectWithLabel_1e2ox_1012 {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-2);\n  margin-left: var(--spacing-2);\n}\n._selectWithLabel_1e2ox_1012 > label {\n  font-size: var(--text-sm);\n}\n._selectWithLabel_1e2ox_1012 ._selectTrigger_1e2ox_308 {\n  border: 1px solid var(--baseBorder);\n}\n._toolbarTitleMode_1e2ox_1027 {\n  font-size: var(--text-sm);\n  margin-left: var(--spacing-2);\n}\n._imageControlWrapperResizing_1e2ox_1032 {\n  touch-action: none;\n}\n._imageResizer_1e2ox_1036 {\n  display: block;\n  width: 7px;\n  height: 7px;\n  position: absolute;\n  background-color: var(--accentText);\n  border: 1px solid var(--baseBg);\n}\n._imageResizer_1e2ox_1036._imageResizerN_1e2ox_1045 {\n  top: -6px;\n  left: 48%;\n  cursor: n-resize;\n}\n._imageResizer_1e2ox_1036._imageResizerNe_1e2ox_1051 {\n  top: -6px;\n  right: -6px;\n  cursor: ne-resize;\n}\n._imageResizer_1e2ox_1036._imageResizerE_1e2ox_1057 {\n  bottom: 48%;\n  right: -6px;\n  cursor: e-resize;\n}\n._imageResizer_1e2ox_1036._imageResizerSe_1e2ox_1063 {\n  bottom: -2px;\n  right: -6px;\n  cursor: nwse-resize;\n}\n._imageResizer_1e2ox_1036._imageResizerS_1e2ox_1063 {\n  bottom: -2px;\n  left: 48%;\n  cursor: s-resize;\n}\n._imageResizer_1e2ox_1036._imageResizerSw_1e2ox_1075 {\n  bottom: -2px;\n  left: -6px;\n  cursor: sw-resize;\n}\n._imageResizer_1e2ox_1036._imageResizerW_1e2ox_1081 {\n  bottom: 48%;\n  left: -6px;\n  cursor: w-resize;\n}\n._imageResizer_1e2ox_1036._imageResizerNw_1e2ox_1087 {\n  top: -6px;\n  left: -6px;\n  cursor: nw-resize;\n}\n._imagePlaceholder_1e2ox_1093 {\n  border: 2px dashed;\n  padding: 48px;\n  margin: 12px;\n  width: -moz-fit-content;\n  width: fit-content;\n  height: -moz-fit-content;\n  height: fit-content;\n}\n._imageDimensionsContainer_1e2ox_1101 {\n  display: flex;\n  gap: var(--spacing-4);\n}\n._placeholder_1e2ox_1106 {\n  color: var(--baseSolid);\n  overflow: hidden;\n  position: absolute;\n  top: 0;\n  padding: var(--spacing-3);\n  text-overflow: ellipsis;\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  user-select: none;\n  white-space: nowrap;\n  display: inline-block;\n  pointer-events: none;\n}\n._rootContentEditableWrapper_1e2ox_1119 {\n  position: relative;\n}\n._downshiftContainer_1e2ox_1123 {\n  display: flex;\n  flex-direction: column;\n  align-items: stretch;\n}\n._downshiftInputWrapper_1e2ox_1129 {\n  display: flex;\n  align-items: center;\n  background-color: var(--baseBase);\n  border-radius: var(--radius-base);\n  border: 1px solid var(--baseBorder);\n}\n._downshiftInputWrapper_1e2ox_1129[data-visible-dropdown='true'] {\n  border-bottom-left-radius: var(--radius-none);\n  border-bottom-right-radius: var(--radius-none);\n  border-bottom-width: 0;\n}\n._downshiftInputWrapper_1e2ox_1129 > button {\n  border: 0;\n  background-color: transparent;\n  font-size: inherit;\n  -webkit-appearance: none;\n  -moz-appearance: none;\n  appearance: none;\n  all: unset;\n  box-sizing: border-box;\n  cursor: default;\n  padding-right: var(--spacing-2);\n}\n._downshiftInput_1e2ox_1129 {\n  border: 0;\n  background-color: transparent;\n  font-size: inherit;\n  -webkit-appearance: none;\n  -moz-appearance: none;\n  appearance: none;\n  all: unset;\n  box-sizing: border-box;\n  cursor: default;\n  width: 20rem;\n  padding: var(--spacing-2) var(--spacing-3);\n  font-size: var(--text-sm);\n}\n._downshiftInput_1e2ox_1129::-moz-placeholder {\n  color: var(--baseBorder);\n}\n._downshiftInput_1e2ox_1129::placeholder {\n  color: var(--baseBorder);\n}\n._downshiftAutocompleteContainer_1e2ox_1160 {\n  position: relative;\n}\n._downshiftAutocompleteContainer_1e2ox_1160 ul {\n  all: unset;\n  box-sizing: border-box;\n  position: absolute;\n  font-size: var(--text-sm);\n  width: 100%;\n  display: none;\n  border-bottom-left-radius: var(--radius-medium);\n  border-bottom-right-radius: var(--radius-medium);\n  max-height: var(--spacing-48);\n  overflow-x: hidden;\n  overflow-y: auto;\n  border: 1px solid var(--baseBorder);\n  border-top-width: 0;\n  background-color: var(--baseBase);\n}\n._downshiftAutocompleteContainer_1e2ox_1160 ul[data-visible='true'] {\n  display: block;\n}\n._downshiftAutocompleteContainer_1e2ox_1160 ul li {\n  padding: var(--spacing-2) var(--spacing-3);\n  white-space: nowrap;\n  margin-bottom: var(--spacing-1);\n  overflow-x: hidden;\n  text-overflow: ellipsis;\n}\n._downshiftAutocompleteContainer_1e2ox_1160 ul li[data-selected='true'] {\n  background-color: var(--baseBgSubtle);\n}\n._downshiftAutocompleteContainer_1e2ox_1160 ul li[data-highlighted='true'] {\n  background-color: var(--baseBgHover);\n}\n._downshiftAutocompleteContainer_1e2ox_1160 ul li:last-of-type {\n  border-bottom-left-radius: var(--radius-medium);\n  border-bottom-right-radius: var(--radius-medium);\n}\n._textInput_1e2ox_1206 {\n  all: unset;\n  border-radius: var(--radius-base);\n  border: 1px solid var(--baseBorder);\n  background-color: var(--baseBase);\n  padding: var(--spacing-2) var(--spacing-3);\n}\nform._multiFieldForm_1e2ox_1214 {\n  display: flex;\n  flex-direction: column;\n  padding: var(--spacing-2);\n  gap: var(--spacing-2);\n}\nform._multiFieldForm_1e2ox_1214 ._formField_1e2ox_1220 {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-2);\n}\nform._multiFieldForm_1e2ox_1214 ._formField_1e2ox_1220 label {\n  font-size: var(--text-xs);\n}\n._markdownParseError_1e2ox_1231 {\n  border-radius: var(--radius-base);\n  border: 1px solid var(--error-color);\n  padding: var(--spacing-2);\n  margin-block: var(--spacing-2);\n  color: var(--error-color);\n  font-size: var(--text-xs);\n}\n._popupContainer_1e2ox_1240 {\n  position: relative;\n  z-index: 2;\n}\n._inputSizer_1e2ox_1245 {\n  display: inline-grid;\n  vertical-align: baseline;\n  align-items: center;\n  position: relative;\n}\n._inputSizer_1e2ox_1245::after, ._inputSizer_1e2ox_1245 input {\n  width: auto;\n  min-width: 1rem;\n  grid-area: 1 / 2;\n  font: inherit;\n  margin: 0;\n  padding: 0 2px;\n  resize: none;\n  background: none;\n  -webkit-appearance: none;\n  -moz-appearance: none;\n  appearance: none;\n  border: none;\n  color: inherit;\n}\n._inputSizer_1e2ox_1245 span {\n  padding: 0.25em;\n}\n._inputSizer_1e2ox_1245::after {\n  content: attr(data-value);\n  white-space: pre-wrap;\n}\n:root, ._light_1tncs_1, ._light-theme_1tncs_1 {\n  --blue-1: #fbfdff;\n  --blue-2: #f4faff;\n  --blue-3: #e6f4fe;\n  --blue-4: #d5efff;\n  --blue-5: #c2e5ff;\n  --blue-6: #acd8fc;\n  --blue-7: #8ec8f6;\n  --blue-8: #5eb1ef;\n  --blue-9: #0090ff;\n  --blue-10: #0588f0;\n  --blue-11: #0d74ce;\n  --blue-12: #113264;\n}\n@supports (color: color(display-p3 1 1 1)) {\n  @media (color-gamut: p3) {\n    :root, ._light_1tncs_1, ._light-theme_1tncs_1 {\n      --blue-1: color(display-p3 0.986 0.992 0.999);\n      --blue-2: color(display-p3 0.96 0.979 0.998);\n      --blue-3: color(display-p3 0.912 0.956 0.991);\n      --blue-4: color(display-p3 0.853 0.932 1);\n      --blue-5: color(display-p3 0.788 0.894 0.998);\n      --blue-6: color(display-p3 0.709 0.843 0.976);\n      --blue-7: color(display-p3 0.606 0.777 0.947);\n      --blue-8: color(display-p3 0.451 0.688 0.917);\n      --blue-9: color(display-p3 0.247 0.556 0.969);\n      --blue-10: color(display-p3 0.234 0.523 0.912);\n      --blue-11: color(display-p3 0.15 0.44 0.84);\n      --blue-12: color(display-p3 0.102 0.193 0.379);\n    }\n  }\n}\n._dark_1tncs_1, ._dark-theme_1tncs_1 {\n  --blue-1: #0d1520;\n  --blue-2: #111927;\n  --blue-3: #0d2847;\n  --blue-4: #003362;\n  --blue-5: #004074;\n  --blue-6: #104d87;\n  --blue-7: #205d9e;\n  --blue-8: #2870bd;\n  --blue-9: #0090ff;\n  --blue-10: #3b9eff;\n  --blue-11: #70b8ff;\n  --blue-12: #c2e6ff;\n}\n@supports (color: color(display-p3 1 1 1)) {\n  @media (color-gamut: p3) {\n    ._dark_1tncs_1, ._dark-theme_1tncs_1 {\n      --blue-1: color(display-p3 0.057 0.081 0.122);\n      --blue-2: color(display-p3 0.072 0.098 0.147);\n      --blue-3: color(display-p3 0.078 0.154 0.27);\n      --blue-4: color(display-p3 0.033 0.197 0.37);\n      --blue-5: color(display-p3 0.08 0.245 0.441);\n      --blue-6: color(display-p3 0.14 0.298 0.511);\n      --blue-7: color(display-p3 0.195 0.361 0.6);\n      --blue-8: color(display-p3 0.239 0.434 0.72);\n      --blue-9: color(display-p3 0.247 0.556 0.969);\n      --blue-10: color(display-p3 0.344 0.612 0.973);\n      --blue-11: color(display-p3 0.49 0.72 1);\n      --blue-12: color(display-p3 0.788 0.898 0.99);\n    }\n  }\n}\n:root, ._light_1tncs_1, ._light-theme_1tncs_1 {\n  --slate-1: #fcfcfd;\n  --slate-2: #f9f9fb;\n  --slate-3: #f0f0f3;\n  --slate-4: #e8e8ec;\n  --slate-5: #e0e1e6;\n  --slate-6: #d9d9e0;\n  --slate-7: #cdced6;\n  --slate-8: #b9bbc6;\n  --slate-9: #8b8d98;\n  --slate-10: #80838d;\n  --slate-11: #60646c;\n  --slate-12: #1c2024;\n}\n@supports (color: color(display-p3 1 1 1)) {\n  @media (color-gamut: p3) {\n    :root, ._light_1tncs_1, ._light-theme_1tncs_1 {\n      --slate-1: color(display-p3 0.988 0.988 0.992);\n      --slate-2: color(display-p3 0.976 0.976 0.984);\n      --slate-3: color(display-p3 0.94 0.941 0.953);\n      --slate-4: color(display-p3 0.908 0.909 0.925);\n      --slate-5: color(display-p3 0.88 0.881 0.901);\n      --slate-6: color(display-p3 0.85 0.852 0.876);\n      --slate-7: color(display-p3 0.805 0.808 0.838);\n      --slate-8: color(display-p3 0.727 0.733 0.773);\n      --slate-9: color(display-p3 0.547 0.553 0.592);\n      --slate-10: color(display-p3 0.503 0.512 0.549);\n      --slate-11: color(display-p3 0.379 0.392 0.421);\n      --slate-12: color(display-p3 0.113 0.125 0.14);\n    }\n  }\n}\n._dark_1tncs_1, ._dark-theme_1tncs_1 {\n  --slate-1: #111113;\n  --slate-2: #18191b;\n  --slate-3: #212225;\n  --slate-4: #272a2d;\n  --slate-5: #2e3135;\n  --slate-6: #363a3f;\n  --slate-7: #43484e;\n  --slate-8: #5a6169;\n  --slate-9: #696e77;\n  --slate-10: #777b84;\n  --slate-11: #b0b4ba;\n  --slate-12: #edeef0;\n}\n@supports (color: color(display-p3 1 1 1)) {\n  @media (color-gamut: p3) {\n    ._dark_1tncs_1, ._dark-theme_1tncs_1 {\n      --slate-1: color(display-p3 0.067 0.067 0.074);\n      --slate-2: color(display-p3 0.095 0.098 0.105);\n      --slate-3: color(display-p3 0.13 0.135 0.145);\n      --slate-4: color(display-p3 0.156 0.163 0.176);\n      --slate-5: color(display-p3 0.183 0.191 0.206);\n      --slate-6: color(display-p3 0.215 0.226 0.244);\n      --slate-7: color(display-p3 0.265 0.28 0.302);\n      --slate-8: color(display-p3 0.357 0.381 0.409);\n      --slate-9: color(display-p3 0.415 0.431 0.463);\n      --slate-10: color(display-p3 0.469 0.483 0.514);\n      --slate-11: color(display-p3 0.692 0.704 0.728);\n      --slate-12: color(display-p3 0.93 0.933 0.94);\n    }\n  }\n}\n:root, ._light_1tncs_1, ._light-theme_1tncs_1 {\n  --grass-1: #fbfefb;\n  --grass-2: #f5fbf5;\n  --grass-3: #e9f6e9;\n  --grass-4: #daf1db;\n  --grass-5: #c9e8ca;\n  --grass-6: #b2ddb5;\n  --grass-7: #94ce9a;\n  --grass-8: #65ba74;\n  --grass-9: #46a758;\n  --grass-10: #3e9b4f;\n  --grass-11: #2a7e3b;\n  --grass-12: #203c25;\n}\n@supports (color: color(display-p3 1 1 1)) {\n  @media (color-gamut: p3) {\n    :root, ._light_1tncs_1, ._light-theme_1tncs_1 {\n      --grass-1: color(display-p3 0.986 0.996 0.985);\n      --grass-2: color(display-p3 0.966 0.983 0.964);\n      --grass-3: color(display-p3 0.923 0.965 0.917);\n      --grass-4: color(display-p3 0.872 0.94 0.865);\n      --grass-5: color(display-p3 0.811 0.908 0.802);\n      --grass-6: color(display-p3 0.733 0.864 0.724);\n      --grass-7: color(display-p3 0.628 0.803 0.622);\n      --grass-8: color(display-p3 0.477 0.72 0.482);\n      --grass-9: color(display-p3 0.38 0.647 0.378);\n      --grass-10: color(display-p3 0.344 0.598 0.342);\n      --grass-11: color(display-p3 0.263 0.488 0.261);\n      --grass-12: color(display-p3 0.151 0.233 0.153);\n    }\n  }\n}\n:root, ._light_1tncs_1, ._light-theme_1tncs_1 {\n  --cyan-1: #fafdfe;\n  --cyan-2: #f2fafb;\n  --cyan-3: #def7f9;\n  --cyan-4: #caf1f6;\n  --cyan-5: #b5e9f0;\n  --cyan-6: #9ddde7;\n  --cyan-7: #7dcedc;\n  --cyan-8: #3db9cf;\n  --cyan-9: #00a2c7;\n  --cyan-10: #0797b9;\n  --cyan-11: #107d98;\n  --cyan-12: #0d3c48;\n}\n@supports (color: color(display-p3 1 1 1)) {\n  @media (color-gamut: p3) {\n    :root, ._light_1tncs_1, ._light-theme_1tncs_1 {\n      --cyan-1: color(display-p3 0.982 0.992 0.996);\n      --cyan-2: color(display-p3 0.955 0.981 0.984);\n      --cyan-3: color(display-p3 0.888 0.965 0.975);\n      --cyan-4: color(display-p3 0.821 0.941 0.959);\n      --cyan-5: color(display-p3 0.751 0.907 0.935);\n      --cyan-6: color(display-p3 0.671 0.862 0.9);\n      --cyan-7: color(display-p3 0.564 0.8 0.854);\n      --cyan-8: color(display-p3 0.388 0.715 0.798);\n      --cyan-9: color(display-p3 0.282 0.627 0.765);\n      --cyan-10: color(display-p3 0.264 0.583 0.71);\n      --cyan-11: color(display-p3 0.08 0.48 0.63);\n      --cyan-12: color(display-p3 0.108 0.232 0.277);\n    }\n  }\n}\n:root, ._light_1tncs_1, ._light-theme_1tncs_1 {\n  --amber-1: #fefdfb;\n  --amber-2: #fefbe9;\n  --amber-3: #fff7c2;\n  --amber-4: #ffee9c;\n  --amber-5: #fbe577;\n  --amber-6: #f3d673;\n  --amber-7: #e9c162;\n  --amber-8: #e2a336;\n  --amber-9: #ffc53d;\n  --amber-10: #ffba18;\n  --amber-11: #ab6400;\n  --amber-12: #4f3422;\n}\n@supports (color: color(display-p3 1 1 1)) {\n  @media (color-gamut: p3) {\n    :root, ._light_1tncs_1, ._light-theme_1tncs_1 {\n      --amber-1: color(display-p3 0.995 0.992 0.985);\n      --amber-2: color(display-p3 0.994 0.986 0.921);\n      --amber-3: color(display-p3 0.994 0.969 0.782);\n      --amber-4: color(display-p3 0.989 0.937 0.65);\n      --amber-5: color(display-p3 0.97 0.902 0.527);\n      --amber-6: color(display-p3 0.936 0.844 0.506);\n      --amber-7: color(display-p3 0.89 0.762 0.443);\n      --amber-8: color(display-p3 0.85 0.65 0.3);\n      --amber-9: color(display-p3 1 0.77 0.26);\n      --amber-10: color(display-p3 0.959 0.741 0.274);\n      --amber-11: color(display-p3 0.64 0.4 0);\n      --amber-12: color(display-p3 0.294 0.208 0.145);\n    }\n  }\n}\n:root, ._light_1tncs_1, ._light-theme_1tncs_1 {\n  --red-1: #fffcfc;\n  --red-2: #fff7f7;\n  --red-3: #feebec;\n  --red-4: #ffdbdc;\n  --red-5: #ffcdce;\n  --red-6: #fdbdbe;\n  --red-7: #f4a9aa;\n  --red-8: #eb8e90;\n  --red-9: #e5484d;\n  --red-10: #dc3e42;\n  --red-11: #ce2c31;\n  --red-12: #641723;\n}\n@supports (color: color(display-p3 1 1 1)) {\n  @media (color-gamut: p3) {\n    :root, ._light_1tncs_1, ._light-theme_1tncs_1 {\n      --red-1: color(display-p3 0.998 0.989 0.988);\n      --red-2: color(display-p3 0.995 0.971 0.971);\n      --red-3: color(display-p3 0.985 0.925 0.925);\n      --red-4: color(display-p3 0.999 0.866 0.866);\n      --red-5: color(display-p3 0.984 0.812 0.811);\n      --red-6: color(display-p3 0.955 0.751 0.749);\n      --red-7: color(display-p3 0.915 0.675 0.672);\n      --red-8: color(display-p3 0.872 0.575 0.572);\n      --red-9: color(display-p3 0.83 0.329 0.324);\n      --red-10: color(display-p3 0.798 0.294 0.285);\n      --red-11: color(display-p3 0.744 0.234 0.222);\n      --red-12: color(display-p3 0.36 0.115 0.143);\n    }\n  }\n}\n._bold_1tncs_10 {\n  font-weight: bold;\n}\n._italic_1tncs_14 {\n  font-style: italic;\n}\n._underline_1tncs_18 {\n  text-decoration: underline;\n}\n._bold_1tncs_10 {\n  font-weight: 700;\n}\n._italic_1tncs_14 {\n  font-style: italic;\n}\n._underline_1tncs_18 {\n  text-decoration: underline;\n}\n._strikethrough_1tncs_34 {\n  text-decoration: line-through;\n}\n._underlineStrikethrough_1tncs_38 {\n  text-decoration: underline line-through;\n}\n._subscript_1tncs_42 {\n  font-size: .8em;\n  vertical-align: sub !important;\n}\n._superscript_1tncs_47 {\n  font-size: .8em;\n  vertical-align: super;\n}\n._code_1tncs_52 {\n  background-color: var(--baseBg);\n  padding: 1px .25rem;\n  font-family: var(--font-mono);\n  font-size: 94%;\n}\n._nestedListItem_1tncs_59 {\n  list-style: none;\n  list-style-type: none;\n}\n._nestedListItem_1tncs_59:before, ._nestedListItem_1tncs_59:after {\n  display: none;\n}\n._listitem_1tncs_69 {\n  margin: var(--spacing-2) 0;\n}\n._listItemChecked_1tncs_73, ._listItemUnchecked_1tncs_74 {\n  position: relative;\n  margin-left: 0;\n  margin-right: 0;\n  margin-inline-start: -1rem;\n  padding-left: var(--spacing-6);\n  padding-right: var(--spacing-6);\n  list-style-type: none;\n  outline: none;\n}\n._listItemChecked_1tncs_73 {\n  text-decoration: line-through;\n}\n._listItemUnchecked_1tncs_74:before, ._listItemChecked_1tncs_73:before {\n  content: '';\n  width: var(--spacing-4);\n  height: var(--spacing-4);\n  top: 0;\n  left: 0;\n  cursor: pointer;\n  display: block;\n  background-size: cover;\n  position: absolute;\n}\n._listItemUnchecked_1tncs_74[dir='rtl']:before, ._listItemChecked_1tncs_73[dir='rtl']:before {\n  left: auto;\n  right: 0;\n}\n._listItemUnchecked_1tncs_74:focus:before, ._listItemChecked_1tncs_73:focus:before {\n  box-shadow: 0 0 0 2px var(--accentBgActive);\n  border-radius: var(--radius-small);\n}\n._listItemUnchecked_1tncs_74:before {\n  border: 1px solid var(--baseBorder);\n  border-radius: var(--radius-small);\n}\n._listItemChecked_1tncs_73:before {\n  border: 1px solid var(--accentBorder);\n  border-radius: var(--radius-small);\n  background-color: var(--accentSolid);\n  background-repeat: no-repeat;\n}\n._listItemChecked_1tncs_73:after {\n  content: '';\n  cursor: pointer;\n  border-color: var(--baseBase);\n  border-style: solid;\n  position: absolute;\n  display: block;\n  top: var(--spacing-0_5);\n  width: var(--spacing-1);\n  left: var(--spacing-1_5);\n  right: var(--spacing-1_5);\n  height: var(--spacing-2);\n  transform: rotate(45deg);\n  border-width: 0 var(--spacing-0_5) var(--spacing-0_5) 0;\n}\n._nestedListItem_1tncs_59 {\n  list-style-type: none;\n}\n._nestedListItem_1tncs_59:before, ._nestedListItem_1tncs_59:after {\n  display: none;\n}\n._admonitionDanger_1tncs_151, ._admonitionInfo_1tncs_152, ._admonitionNote_1tncs_153, ._admonitionTip_1tncs_154, ._admonitionCaution_1tncs_155 {\n  padding: var(--spacing-2);\n  margin-top: var(--spacing-2);\n  margin-bottom: var(--spacing-2);\n  border-left: 3px solid var(--admonitionBorder);\n  background-color: var(--admonitionBg);\n}\n._admonitionInfo_1tncs_152 {\n  --admonitionBorder: var(--admonitionInfoBorder);\n  --admonitionBg: var(--admonitionInfoBg);\n}\n._admonitionTip_1tncs_154 {\n  --admonitionBorder: var(--admonitionTipBorder);\n  --admonitionBg: var(--admonitionTipBg);\n}\n._admonitionCaution_1tncs_155 {\n  --admonitionBorder: var(--admonitionCautionBorder);\n  --admonitionBg: var(--admonitionCautionBg);\n}\n._admonitionDanger_1tncs_151 {\n  --admonitionBorder: var(--admonitionDangerBorder);\n  --admonitionBg: var(--admonitionDangerBg);\n}\n._admonitionNote_1tncs_153 {\n  --admonitionBorder: var(--admonitionNoteBorder);\n  --admonitionBg: var(--admonitionNoteBg);\n}\n._mdxExpression_1tncs_188 {\n  font-family: var(--font-mono);\n  font-size: 84%;\n  color: var(--accentText);\n}\n._mdxExpression_1tncs_188 input:focus-visible {\n  outline: none;\n}\n:root {\n  --radius: 0.625rem;\n  --background: oklch(1 0 0);\n  --foreground: oklch(0.145 0 0);\n  --card: oklch(1 0 0);\n  --card-foreground: oklch(0.145 0 0);\n  --popover: oklch(1 0 0);\n  --popover-foreground: oklch(0.145 0 0);\n  --primary: oklch(0.35 0.15 350);\n  --primary-foreground: oklch(0.985 0 0);\n  --secondary: oklch(0.97 0 0);\n  --secondary-foreground: oklch(0.205 0 0);\n  --muted: oklch(0.97 0 0);\n  --muted-foreground: oklch(0.556 0 0);\n  --accent: oklch(0.32 0.15 350);\n  --accent-foreground: oklch(0.985 0 0);\n  --destructive: oklch(0.577 0.245 27.325);\n  --border: oklch(0.922 0 0);\n  --input: oklch(0.922 0 0);\n  --ring: oklch(0.35 0.15 350);\n  --chart-1: oklch(0.646 0.222 41.116);\n  --chart-2: oklch(0.6 0.118 184.704);\n  --chart-3: oklch(0.398 0.07 227.392);\n  --chart-4: oklch(0.828 0.189 84.429);\n  --chart-5: oklch(0.769 0.188 70.08);\n  --sidebar: oklch(0.985 0 0);\n  --sidebar-foreground: oklch(0.145 0 0);\n  --sidebar-primary: oklch(0.35 0.15 350);\n  --sidebar-primary-foreground: oklch(0.985 0 0);\n  --sidebar-accent: oklch(0.97 0 0);\n  --sidebar-accent-foreground: oklch(0.205 0 0);\n  --sidebar-border: oklch(0.922 0 0);\n  --sidebar-ring: oklch(0.35 0.15 350);\n  --iai-primary: #8B1538;\n  --iai-secondary: #A91B3C;\n  --iai-accent: #6B1227;\n}\n.dark {\n  --background: oklch(0.145 0 0);\n  --foreground: oklch(0.985 0 0);\n  --card: oklch(0.205 0 0);\n  --card-foreground: oklch(0.985 0 0);\n  --popover: oklch(0.269 0 0);\n  --popover-foreground: oklch(0.985 0 0);\n  --primary: oklch(0.45 0.18 350);\n  --primary-foreground: oklch(0.985 0 0);\n  --secondary: oklch(0.269 0 0);\n  --secondary-foreground: oklch(0.985 0 0);\n  --muted: oklch(0.269 0 0);\n  --muted-foreground: oklch(0.708 0 0);\n  --accent: oklch(0.42 0.18 350);\n  --accent-foreground: oklch(0.985 0 0);\n  --destructive: oklch(0.704 0.191 22.216);\n  --border: oklch(1 0 0 / 10%);\n  --input: oklch(1 0 0 / 15%);\n  --ring: oklch(0.45 0.18 350);\n  --chart-1: oklch(0.488 0.243 264.376);\n  --chart-2: oklch(0.696 0.17 162.48);\n  --chart-3: oklch(0.769 0.188 70.08);\n  --chart-4: oklch(0.627 0.265 303.9);\n  --chart-5: oklch(0.645 0.246 16.439);\n  --sidebar: oklch(0.205 0 0);\n  --sidebar-foreground: oklch(0.985 0 0);\n  --sidebar-primary: oklch(0.45 0.18 350);\n  --sidebar-primary-foreground: oklch(0.985 0 0);\n  --sidebar-accent: oklch(0.269 0 0);\n  --sidebar-accent-foreground: oklch(0.985 0 0);\n  --sidebar-border: oklch(1 0 0 / 10%);\n  --sidebar-ring: oklch(0.45 0.18 350);\n}\n@layer base {\n  * {\n    border-color: var(--border);\n    outline-color: var(--ring);\n    @supports (color: color-mix(in lab, red, red)) {\n      outline-color: color-mix(in oklab, var(--ring) 50%, transparent);\n    }\n  }\n  body {\n    background-color: var(--background);\n    color: var(--foreground);\n  }\n}\n::view-transition-old(root),\r\n::view-transition-new(root) {\n  animation: none;\n  mix-blend-mode: normal;\n}\n::view-transition-old(root) {\n  z-index: 0;\n}\n::view-transition-new(root) {\n  z-index: 1;\n}\n@keyframes reveal {\n  from {\n    clip-path: circle(0% at var(--x, 50%) var(--y, 50%));\n    opacity: 0.7;\n  }\n  to {\n    clip-path: circle(150% at var(--x, 50%) var(--y, 50%));\n    opacity: 1;\n  }\n}\n::view-transition-new(root) {\n  animation: reveal 0.4s ease-in-out forwards;\n}\n@property --tw-translate-x {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-translate-y {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-translate-z {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-scale-x {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-scale-y {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-scale-z {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-rotate-x {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-rotate-y {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-rotate-z {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-skew-x {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-skew-y {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-space-y-reverse {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-space-x-reverse {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-border-style {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: solid;\n}\n@property --tw-gradient-position {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-gradient-from {\n  syntax: \"<color>\";\n  inherits: false;\n  initial-value: #0000;\n}\n@property --tw-gradient-via {\n  syntax: \"<color>\";\n  inherits: false;\n  initial-value: #0000;\n}\n@property --tw-gradient-to {\n  syntax: \"<color>\";\n  inherits: false;\n  initial-value: #0000;\n}\n@property --tw-gradient-stops {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-gradient-via-stops {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-gradient-from-position {\n  syntax: \"<length-percentage>\";\n  inherits: false;\n  initial-value: 0%;\n}\n@property --tw-gradient-via-position {\n  syntax: \"<length-percentage>\";\n  inherits: false;\n  initial-value: 50%;\n}\n@property --tw-gradient-to-position {\n  syntax: \"<length-percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-leading {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-font-weight {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-tracking {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ordinal {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-slashed-zero {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-numeric-figure {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-numeric-spacing {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-numeric-fraction {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-inset-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-inset-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-inset-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-ring-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ring-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-inset-ring-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-inset-ring-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-ring-inset {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ring-offset-width {\n  syntax: \"<length>\";\n  inherits: false;\n  initial-value: 0px;\n}\n@property --tw-ring-offset-color {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: #fff;\n}\n@property --tw-ring-offset-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-outline-style {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: solid;\n}\n@property --tw-blur {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-brightness {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-contrast {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-grayscale {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-hue-rotate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-invert {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-opacity {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-saturate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-sepia {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-drop-shadow-size {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-blur {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-brightness {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-contrast {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-grayscale {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-hue-rotate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-invert {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-opacity {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-saturate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-sepia {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-duration {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ease {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-content {\n  syntax: \"*\";\n  initial-value: \"\";\n  inherits: false;\n}\n@keyframes spin {\n  to {\n    transform: rotate(360deg);\n  }\n}\n@keyframes ping {\n  75%, 100% {\n    transform: scale(2);\n    opacity: 0;\n  }\n}\n@keyframes pulse {\n  50% {\n    opacity: 0.5;\n  }\n}\n@keyframes bounce {\n  0%, 100% {\n    transform: translateY(-25%);\n    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);\n  }\n  50% {\n    transform: none;\n    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\n  }\n}\n@keyframes enter {\n  from {\n    opacity: var(--tw-enter-opacity,1);\n    transform: translate3d(var(--tw-enter-translate-x,0),var(--tw-enter-translate-y,0),0)scale3d(var(--tw-enter-scale,1),var(--tw-enter-scale,1),var(--tw-enter-scale,1))rotate(var(--tw-enter-rotate,0));\n  }\n}\n@keyframes exit {\n  to {\n    opacity: var(--tw-exit-opacity,1);\n    transform: translate3d(var(--tw-exit-translate-x,0),var(--tw-exit-translate-y,0),0)scale3d(var(--tw-exit-scale,1),var(--tw-exit-scale,1),var(--tw-exit-scale,1))rotate(var(--tw-exit-rotate,0));\n  }\n}\n@keyframes accordion-down {\n  from {\n    height: 0;\n  }\n  to {\n    height: var(--radix-accordion-content-height,var(--bits-accordion-content-height,var(--reka-accordion-content-height,var(--kb-accordion-content-height,auto))));\n  }\n}\n@keyframes accordion-up {\n  from {\n    height: var(--radix-accordion-content-height,var(--bits-accordion-content-height,var(--reka-accordion-content-height,var(--kb-accordion-content-height,auto))));\n  }\n  to {\n    height: 0;\n  }\n}\n@keyframes caret-blink {\n  0%,70%,100% {\n    opacity: 1;\n  }\n  20%,50% {\n    opacity: 0;\n  }\n}\n@layer properties {\n  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {\n    *, ::before, ::after, ::backdrop {\n      --tw-translate-x: 0;\n      --tw-translate-y: 0;\n      --tw-translate-z: 0;\n      --tw-scale-x: 1;\n      --tw-scale-y: 1;\n      --tw-scale-z: 1;\n      --tw-rotate-x: initial;\n      --tw-rotate-y: initial;\n      --tw-rotate-z: initial;\n      --tw-skew-x: initial;\n      --tw-skew-y: initial;\n      --tw-space-y-reverse: 0;\n      --tw-space-x-reverse: 0;\n      --tw-border-style: solid;\n      --tw-gradient-position: initial;\n      --tw-gradient-from: #0000;\n      --tw-gradient-via: #0000;\n      --tw-gradient-to: #0000;\n      --tw-gradient-stops: initial;\n      --tw-gradient-via-stops: initial;\n      --tw-gradient-from-position: 0%;\n      --tw-gradient-via-position: 50%;\n      --tw-gradient-to-position: 100%;\n      --tw-leading: initial;\n      --tw-font-weight: initial;\n      --tw-tracking: initial;\n      --tw-ordinal: initial;\n      --tw-slashed-zero: initial;\n      --tw-numeric-figure: initial;\n      --tw-numeric-spacing: initial;\n      --tw-numeric-fraction: initial;\n      --tw-shadow: 0 0 #0000;\n      --tw-shadow-color: initial;\n      --tw-shadow-alpha: 100%;\n      --tw-inset-shadow: 0 0 #0000;\n      --tw-inset-shadow-color: initial;\n      --tw-inset-shadow-alpha: 100%;\n      --tw-ring-color: initial;\n      --tw-ring-shadow: 0 0 #0000;\n      --tw-inset-ring-color: initial;\n      --tw-inset-ring-shadow: 0 0 #0000;\n      --tw-ring-inset: initial;\n      --tw-ring-offset-width: 0px;\n      --tw-ring-offset-color: #fff;\n      --tw-ring-offset-shadow: 0 0 #0000;\n      --tw-outline-style: solid;\n      --tw-blur: initial;\n      --tw-brightness: initial;\n      --tw-contrast: initial;\n      --tw-grayscale: initial;\n      --tw-hue-rotate: initial;\n      --tw-invert: initial;\n      --tw-opacity: initial;\n      --tw-saturate: initial;\n      --tw-sepia: initial;\n      --tw-drop-shadow: initial;\n      --tw-drop-shadow-color: initial;\n      --tw-drop-shadow-alpha: 100%;\n      --tw-drop-shadow-size: initial;\n      --tw-backdrop-blur: initial;\n      --tw-backdrop-brightness: initial;\n      --tw-backdrop-contrast: initial;\n      --tw-backdrop-grayscale: initial;\n      --tw-backdrop-hue-rotate: initial;\n      --tw-backdrop-invert: initial;\n      --tw-backdrop-opacity: initial;\n      --tw-backdrop-saturate: initial;\n      --tw-backdrop-sepia: initial;\n      --tw-duration: initial;\n      --tw-ease: initial;\n      --tw-content: \"\";\n      --tw-animation-delay: 0s;\n      --tw-animation-direction: normal;\n      --tw-animation-duration: initial;\n      --tw-animation-fill-mode: none;\n      --tw-animation-iteration-count: 1;\n      --tw-enter-opacity: 1;\n      --tw-enter-rotate: 0;\n      --tw-enter-scale: 1;\n      --tw-enter-translate-x: 0;\n      --tw-enter-translate-y: 0;\n      --tw-exit-opacity: 1;\n      --tw-exit-rotate: 0;\n      --tw-exit-scale: 1;\n      --tw-exit-translate-x: 0;\n      --tw-exit-translate-y: 0;\n    }\n  }\n}\r\n"], "names": [], "mappings": "AACA;EA2hTE;IACE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA3hTJ;EAEE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAFF;EA0JE;;;;;;;EAMA;;;;;;;;;;EASA;;;;;;EAKA;;;;EAIA;;;;;EAIA;;;;;;EAKA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;;;;;;;EAUA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;IACE;;;;IAEE;MAAgD;;;;;;EAKpD;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EA01RA;;;;;EAGE;IAAgD;;;;;EAIlD;;;;;;AAvoSF;;AAAA;EA2SE;;;;EAIA;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;;;;;;;EAWA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAyB;;;;;EAGzB;IAAyB;;;;;EAGzB;IAAyB;;;;;EAGzB;IAAyB;;;;;EAGzB;IAAyB;;;;;EAI3B;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAGE;;;;;EAIA;;;;;;;;EAOA;;;;;;EAKA;;;;;EAIA;;;;EASA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;;EAMA;;;;;EAIA;;;;EAGA;;;;;;EAKA;;;;;;;EAMA;;;;;;;;;;;;EAWA;;;;EAGA;;;;EAGA;;;;;;;;;EAQA;;;;;EAIA;;;;;;;;;EAQA;;;;;EAIA;;;;;;;;;EAQA;;;;;EAIA;;;;;;;;EAOA;;;;;EAIA;;;;;EAIA;;;;;;EAKA;;;;;EAIA;;;;;;;;;;;;;EAYA;;;;;;EAKA;;;;EAMA;;;;EAMA;;;;;EAIA;;;;;EAIA;;;;EASA;;;;;;;;;;;;;;;;EAeA;;;;;;;;;;;;EAWA;;;;EAMA;;;;;;;;;EAQA;;;;;EAIA;;;;;;;;;EAQA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;;;EA4CA;;;;;EAIA;;;;;EAIA;;;;EAMA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAYA;;;;EAGA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAIF;;;;;EAGE;;;;;EAIA;;;;;;;EAMA;;;;;;EAKA;;;;;;;EAMA;;;;;;;EAMA;;;;;;;EAMA;;;;;;EAKA;;;;;EAQA;;;;;EAIA;;;;;EAIA;;;;;;;;;EAQA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;;;;;;;EAWA;;;;;;EAUA;;;;;EAIA;;;;EAMA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;EAYA;;;;;EAIA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;;EAKA;;;;EAGA;;;;EAIF;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;;EAMA;;;;;;;EAMA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAMA;;;;EAMA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;;;EAMA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAIE;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAMF;;;;EAIE;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAMF;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAEE;IAAiE;;;;;EAKnE;;;;;EAEE;IAAiE;;;;;EAKnE;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;;EAKA;;;;;;EAKA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAGE;IAAgC;;;;;;EAKlC;;;;;EAIA;;;;;EAIA;;;;;;EAKA;;;;EAGA;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAIE;;;;EAME;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAMzB;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAOE;IAAuB;;;;;EAMzB;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAQA;;;;EAQA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;;EAQE;;;;;EAOF;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAME;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IACE;;;;;EAQF;IACE;;;;;;EAQJ;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;;EAGE;IAAgC;;;;;;EAOlC;;;;;EAMA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;;EAMA;;;;;EAMA;;;;;EAGE;IAAgC;;;;;;EAOlC;;;;;EAMA;;;;;EAMA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAEE;IAAgD;;;;;EAQ9C;IAAuB;;;;;EAQzB;;;;EAOA;;;;EAOA;;;;EAEE;IAAgD;;;;;EAOpD;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAME;;;;;EAQA;;;;;EAQA;;;;;EAQA;;;;;;EASA;;;;;;EAQF;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAME;;;;EAEE;IAAgD;;;;;EAQlD;;;;;EAQA;;;;;EAEE;IAAgD;;;;;EASlD;;;;;EAQA;;;;;EAQA;;;;EAQE;;;;;EAUA;;;;;EASF;;;;;;;EAUA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAMF;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAMA;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAME;;;;EAOA;;;;EAQA;;;;EAOF;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAMA;;;;EAMA;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAME;;;;EAOA;;;;EAQA;;;;EASE;IAAuB;;;;;EASvB;IAAuB;;;;;EASvB;IAAuB;;;;;EAQzB;;;;EAMF;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAME;;;;EAEE;IAAgD;;;;;EAQlD;;;;EAMF;;;;;EAMA;;;;;EAOE;;;;;EAOF;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IACE;;;;;;;EAQF;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAOvB;IAAyB;;;;;EAOzB;IAAyB;;;;;EAM3B;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;EAKzB;IACE;;;;;EAMF;IACE;;;;;EAMF;IACE;;;;;EAMF;IACE;;;;;;EAOF;IAEI;;;;;EAOJ;IACE;;;;;;EAOF;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAkC;;;;;;EAMlC;IAAkC;;;;;EAKlC;IAAkC;;;;;EAKlC;IAAkC;;;;;EAKlC;IAAkC;;;;;EAKlC;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAOI;IAAuB;;;;IAErB;MAAgD;;;;;;EAUlD;IAAuB;;;;IAErB;MAAgD;;;;;;EASpD;;;;EAEE;IAAgD;;;;;EAQlD;;;;EAEE;IAAgD;;;;;EAShD;;;;EAEE;IAAgD;;;;;EAUlD;;;;EAQF;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAEE;IAAgD;;;;;EAShD;;;;EAEE;IAAgD;;;;;EAQtD;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAKA;;;;;EAGE;IAAgC;;;;;;EAOlC;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAGE;IAAgC;;;;;;EAOlC;;;;EAKA;;;;;EAGE;IAAgC;;;;;;EAOlC;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAME;;;;EAMF;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAME;;;;;EAQA;;;;;EAOF;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAOI;;;;EASA;;;;EASA;;;;EASA;;;;EAOJ;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;;EAOA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAME;;;;;EAOF;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAOI;IAAuB;;;;;EASvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAUlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAUlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAUlD;IAAuB;;;;;;AAO/B;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAKE;EAA4B;;;;;;;;;AAO5B;;;;AAGA;;;;AAIF;;;;;AAGE;;;;;AAKF;;;;;AAGE;;;;;AAKF;;;;;AAGE;;;;;AAKF;;;;;AAGE;;;;;AAKF;;;;;;AAIE;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAMF;;;;;;;;;;;;;;;AAcA;EACE;IACE;;;;;;;;;;;;;;;;;AAgBJ;;;;;;;;;;;;;;;AAcA;EACE;IACE;;;;;;;;;;;;;;;;;AAgBJ;;;;;;;;;;;;;;;AAcA;EACE;IACE;;;;;;;;;;;;;;;;;AAgBJ;;;;;;;;;;;;;;;AAcA;EACE;IACE;;;;;;;;;;;;;;;;;AAgBJ;;;;;;;;;;;;;;;AAcA;EACE;IACE;;;;;;;;;;;;;;;;;AAgBJ;;;;;;;;;;;;;;;AAcA;EACE;IACE;;;;;;;;;;;;;;;;;AAgBJ;;;;;;;;;;;;;;;AAcA;EACE;IACE;;;;;;;;;;;;;;;;;AAgBJ;;;;;;;;;;;;;;;AAcA;EACE;IACE;;;;;;;;;;;;;;;;;AAgBJ;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;AAMA;;;;;;AAUA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyFA;;;;AAGA;;;;;;;;;;;;;;;AAcA;;;;;;;AAMA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;;;;;;;AAUA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;;AAYA;EACE;;;;;AAIF;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;;;;;AASA;;;;AAMA;;;;;;;;;;;;;;;;;AAgBA;;;;;;AAKA;;;;AAGA;;;;;;;AAMA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;;AAKA;;;;;;;AAMA;;;;AAGA;;;;;;;;;AAQA;;;;;;;;AAOA;;;;;;;;;;;;AAWA;;;;;;AAKA;;;;AAGA;;;;;;;;;;AAeA;;;;;;;;AAOA;;;;;AAIA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;;;;;;;AAiBA;;;;;;;AAYA;EACE;;;;;AAIF;;;;AAGA;;;;;;;;;;;;AAiBA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;;;AAOA;;;;;;AAKA;;;;;;;;AAOA;;;;;;AAKA;;;;;;;AAYA;;;;;;;;;AAcA;;;;AAAA;;;;AAMA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;;;;;;;;;AAWA;;;;;;;;;;AASA;;;;;;AAKA;;;;;;;;;AAcA;;;;;;;;;;;;AAWA;;;;AAGA;;;;;;;;;AAQA;EACE;;;;;AAIF;;;;;;;AAMA;;;;AAGA;;;;;;;;;;AASA;;;;AAGA;;;;;;;;;;AAgBA;;;;AAGA;EACE;;;;;AAIF;;;;AAGA;;;;;AAIA;;;;;AAIA;EACE;;;;;;AAKF;;;;;;;;AAOA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;EACE;;;;EAGA;;;;;AAIF;;;;AAGA;;;;;AAIA;;;;;;;;;;;AAgBA;;;;AAGA;EACE;;;;;AAIF;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;;;;AAaA;;;;AAGA;EACE;;;;;AAIF;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;;;;AAaA;;;;AAGA;EACE;;;;;AAIF;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;;;;;AAgBA;;;;AAGA;EACE;;;;;AAIF;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;;;;AAOA;;;;;;;AAMA;;;;;;;;;AAQA;;;;;;;;;AAQA;;;;AAGA;;;;;;;;;;AAQA;;;;;;;;;;;;AAUA;;;;AAGA;;;;;AAIA;;;;;;AAKA;;;;;;;;;;;;AAWA;;;;AAGA;;;;;;;;;AAQA;;;;;;;;;;AASA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;;;AAOA;;;;;;AAKA;;;;AAGA;;;;;AAIA;;;;;;;AAMA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;;;;;AAQA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;;;AASA;;;;;AAIA;;;;;;;;;;;;;;;AAcA;;;;AAGA;;;;;;AAKA;;;;;;;;AAOA;;;;;;AAKA;;;;;;;AAYA;;;;;;;;;AAcA;;;;AAAA;;;;AAMA;;;;AAGA;;;;;;;;;;;;;;;;AAgBA;;;;AAGA;;;;;;;;AAOA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;;;;AAOA;;;;;;;AAMA;;;;;;AAKA;;;;AAGA;;;;;;;;;AAQA;;;;;AAIA;;;;;;;AAMA;;;;;;;;;;;;;;;;AAeA;;;;AAGA;;;;;AAIA;;;;;;;;;;;;;;;AAcA;EACE;IACE;;;;;;;;;;;;;;;;;AAgBJ;;;;;;;;;;;;;;;AAcA;EACE;IACE;;;;;;;;;;;;;;;;;AAgBJ;;;;;;;;;;;;;;;AAcA;EACE;IACE;;;;;;;;;;;;;;;;;AAgBJ;;;;;;;;;;;;;;;AAcA;EACE;IACE;;;;;;;;;;;;;;;;;AAgBJ;;;;;;;;;;;;;;;AAcA;EACE;IACE;;;;;;;;;;;;;;;;;AAgBJ;;;;;;;;;;;;;;;AAcA;EACE;IACE;;;;;;;;;;;;;;;;;AAgBJ;;;;;;;;;;;;;;;AAcA;EACE;IACE;;;;;;;;;;;;;;;;;AAgBJ;;;;;;;;;;;;;;;AAcA;EACE;IACE;;;;;;;;;;;;;;;;;AAyBJ;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;;;AAMA;;;;AAOA;;;;AAGA;;;;;;;;;;;AAUA;;;;AAGA;;;;;;;;;;;;AAWA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;;AAMA;;;;;;;;;;;;;;;;AAeA;;;;AAGA;;;;AAGA;;;;;;;;AAOA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8CA;;;;;AAKA;;;;AAGA;;;;AAGA;;;;;;;;;;;;AAUA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;;AAMA;;;;;;AAKA;;;;;;;;;;;;AAUA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;;;;AAQA;;;;;;;;;;AAQA"}}]}