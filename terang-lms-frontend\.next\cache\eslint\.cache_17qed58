[{"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\(course-view)\\layout.tsx": "1", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\(course-view)\\my-courses\\[courseId]\\exam\\page.tsx": "2", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\(course-view)\\my-courses\\[courseId]\\exam\\results\\page.tsx": "3", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\(course-view)\\my-courses\\[courseId]\\page.tsx": "4", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\(students-page)\\courses\\material-section.tsx": "5", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\(students-page)\\courses\\page.tsx": "6", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\(students-page)\\layout.tsx": "7", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\(students-page)\\my-courses\\page.tsx": "8", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\auth\\login\\route.ts": "9", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\auth\\register\\route.ts": "10", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\auth\\signin\\route.ts": "11", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\auth\\signup\\route.ts": "12", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\certificates\\route.ts": "13", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\chapters\\route.ts": "14", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\chapters\\[id]\\route.ts": "15", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\class-enrollments\\route.ts": "16", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\class-enrollments\\[id]\\route.ts": "17", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\classes\\route.ts": "18", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\classes\\[id]\\route.ts": "19", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\courses\\route.ts": "20", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\courses\\[id]\\route.ts": "21", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\enrollments\\route.ts": "22", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\enrollments\\[id]\\route.ts": "23", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\institutions\\route.ts": "24", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\institutions\\[id]\\route.ts": "25", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\modules\\route.ts": "26", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\modules\\[id]\\route.ts": "27", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\questions\\route.ts": "28", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\questions\\[id]\\route.ts": "29", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\quizzes\\route.ts": "30", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\quizzes\\[id]\\route.ts": "31", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\reports\\route.ts": "32", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\subscriptions\\route.ts": "33", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\upload\\route.ts": "34", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\users\\route.ts": "35", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\users\\[id]\\route.ts": "36", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\auth\\sign-in\\[[...sign-in]]\\page.tsx": "37", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\auth\\sign-up\\[[...sign-up]]\\page.tsx": "38", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\institutions\\new\\page.tsx": "39", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\institutions\\page.tsx": "40", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\institutions\\[id]\\page.tsx": "41", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\layout.tsx": "42", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\page.tsx": "43", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\subscriptions\\page.tsx": "44", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\users\\new\\page.tsx": "45", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\users\\page.tsx": "46", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\users\\[id]\\page.tsx": "47", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\kanban\\page.tsx": "48", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\layout.tsx": "49", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\overview\\@area_stats\\error.tsx": "50", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\overview\\@area_stats\\loading.tsx": "51", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\overview\\@area_stats\\page.tsx": "52", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\overview\\@bar_stats\\error.tsx": "53", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\overview\\@bar_stats\\loading.tsx": "54", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\overview\\@bar_stats\\page.tsx": "55", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\overview\\@pie_stats\\error.tsx": "56", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\overview\\@pie_stats\\loading.tsx": "57", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\overview\\@pie_stats\\page.tsx": "58", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\overview\\@sales\\error.tsx": "59", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\overview\\@sales\\loading.tsx": "60", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\overview\\@sales\\page.tsx": "61", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\overview\\error.tsx": "62", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\overview\\layout.tsx": "63", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\page.tsx": "64", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\product\\page.tsx": "65", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\product\\[productId]\\page.tsx": "66", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\profile\\[[...profile]]\\page.tsx": "67", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\student\\certificates\\page.tsx": "68", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\student\\courses\\page.tsx": "69", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\student\\courses\\[id]\\page.tsx": "70", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\student\\layout.tsx": "71", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\student\\page.tsx": "72", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\student\\progress\\page.tsx": "73", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\classes\\new\\page.tsx": "74", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\classes\\page.tsx": "75", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\classes\\[id]\\courses\\page.tsx": "76", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\classes\\[id]\\page.tsx": "77", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\classes\\[id]\\students\\page.tsx": "78", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\courses\\generate\\page.tsx": "79", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\courses\\new\\page.tsx": "80", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\courses\\page.tsx": "81", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\courses\\[id]\\page.tsx": "82", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\layout.tsx": "83", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\page.tsx": "84", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\reports\\page.tsx": "85", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\global-error.tsx": "86", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\layout.tsx": "87", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\not-found.tsx": "88", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\page.tsx": "89", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\active-theme.tsx": "90", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\breadcrumbs.tsx": "91", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\course\\course-creation-wizard.tsx": "92", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\course\\steps\\academics-step.tsx": "93", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\course\\steps\\admissions-step.tsx": "94", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\course\\steps\\basic-info-step.tsx": "95", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\course\\steps\\careers-step.tsx": "96", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\course\\steps\\content-creation-step.tsx": "97", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\course\\steps\\course-details-step.tsx": "98", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\course\\steps\\module-structure-step.tsx": "99", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\course\\steps\\publishing-step.tsx": "100", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\course\\steps\\student-experience-step.tsx": "101", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\course\\steps\\tuition-financing-step.tsx": "102", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\dynamic-content-editor.tsx": "103", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\file-uploader.tsx": "104", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\form-card-skeleton.tsx": "105", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\icons.tsx": "106", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\kbar\\index.tsx": "107", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\kbar\\render-result.tsx": "108", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\kbar\\result-item.tsx": "109", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\kbar\\use-theme-switching.tsx": "110", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\landingpage\\CallToActionSection.tsx": "111", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\landingpage\\CourseCard.tsx": "112", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\landingpage\\CourseGrid.tsx": "113", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\landingpage\\CourseHeroSection.tsx": "114", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\landingpage\\CourseLandingPage.tsx": "115", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\landingpage\\FeaturesSection.tsx": "116", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\landingpage\\Footer.tsx": "117", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\landingpage\\LandingNavbar.tsx": "118", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\layout\\app-sidebar.tsx": "119", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\layout\\cta-github.tsx": "120", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\layout\\header.tsx": "121", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\layout\\page-container.tsx": "122", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\layout\\providers.tsx": "123", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\layout\\ThemeToggle\\theme-provider.tsx": "124", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\layout\\ThemeToggle\\theme-toggle.tsx": "125", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\layout\\user-nav.tsx": "126", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\live-preview.tsx": "127", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\certificate-template.tsx": "128", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\chapter-section.tsx": "129", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\content-item.tsx": "130", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\course-detail-tabs.tsx": "131", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\course-preview-card.tsx": "132", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\course-success-modal.tsx": "133", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\final-exam\\index.ts": "134", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\final-exam\\option.tsx": "135", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\final-exam\\question-bank.tsx": "136", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\final-exam\\question.tsx": "137", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\final-exam\\review-question-bank.tsx": "138", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\final-exam\\review-question.tsx": "139", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\index.ts": "140", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\module-section.tsx": "141", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\payment-modal.tsx": "142", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\quiz-card.tsx": "143", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\quiz-modal.tsx": "144", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\table-of-contents.tsx": "145", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\tabs\\certificate-tab.tsx": "146", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\tabs\\course-tab.tsx": "147", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\tabs\\exam-tab.tsx": "148", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\tabs\\progress-tab.tsx": "149", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\tree-node.tsx": "150", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\modal\\alert-modal.tsx": "151", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\nav-main.tsx": "152", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\nav-projects.tsx": "153", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\nav-user.tsx": "154", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\not-assigned-to-institution.tsx": "155", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\org-switcher.tsx": "156", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\quiz\\QuizTaker.tsx": "157", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\role-indicator.tsx": "158", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\search-input.tsx": "159", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\theme-selector.tsx": "160", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\accordion.tsx": "161", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\alert-dialog.tsx": "162", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\alert.tsx": "163", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\aspect-ratio.tsx": "164", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\avatar.tsx": "165", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\badge.tsx": "166", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\breadcrumb.tsx": "167", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\button.tsx": "168", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\calendar.tsx": "169", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\card.tsx": "170", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\chart.tsx": "171", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\checkbox.tsx": "172", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\collapsible.tsx": "173", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\command.tsx": "174", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\context-menu.tsx": "175", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\dialog.tsx": "176", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\drawer.tsx": "177", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\dropdown-menu.tsx": "178", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\form.tsx": "179", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\heading.tsx": "180", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\hover-card.tsx": "181", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\input-otp.tsx": "182", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\input.tsx": "183", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\label.tsx": "184", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\menubar.tsx": "185", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\modal.tsx": "186", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\navigation-menu.tsx": "187", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\pagination.tsx": "188", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\popover.tsx": "189", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\progress.tsx": "190", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\radio-group.tsx": "191", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\resizable.tsx": "192", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\scroll-area.tsx": "193", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\select.tsx": "194", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\separator.tsx": "195", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\sheet.tsx": "196", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\sidebar.tsx": "197", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\skeleton.tsx": "198", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\slider.tsx": "199", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\sonner.tsx": "200", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\switch.tsx": "201", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\table\\data-table-column-header.tsx": "202", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\table\\data-table-date-filter.tsx": "203", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\table\\data-table-faceted-filter.tsx": "204", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\table\\data-table-pagination.tsx": "205", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\table\\data-table-skeleton.tsx": "206", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\table\\data-table-slider-filter.tsx": "207", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\table\\data-table-toolbar.tsx": "208", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\table\\data-table-view-options.tsx": "209", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\table\\data-table.tsx": "210", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\table.tsx": "211", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\tabs.tsx": "212", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\textarea.tsx": "213", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\toggle-group.tsx": "214", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\toggle.tsx": "215", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\tooltip.tsx": "216", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\user-avatar-profile.tsx": "217", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\config\\data-table.ts": "218", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\config\\navigation.ts": "219", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\config\\subscriptions.ts": "220", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\constants\\data.ts": "221", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\constants\\mock-api.ts": "222", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\constants\\mock-lms-data.ts": "223", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\constants\\shared-course-data.ts": "224", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\contexts\\enrollment-context.tsx": "225", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\auth\\components\\github-auth-button.tsx": "226", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\auth\\components\\main-page.tsx": "227", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\auth\\components\\sign-in-view.tsx": "228", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\auth\\components\\sign-up-view.tsx": "229", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\kanban\\components\\board-column.tsx": "230", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\kanban\\components\\column-action.tsx": "231", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\kanban\\components\\kanban-board.tsx": "232", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\kanban\\components\\kanban-view-page.tsx": "233", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\kanban\\components\\new-section-dialog.tsx": "234", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\kanban\\components\\new-task-dialog.tsx": "235", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\kanban\\components\\task-card.tsx": "236", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\kanban\\utils\\index.ts": "237", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\kanban\\utils\\store.ts": "238", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\overview\\components\\area-graph-skeleton.tsx": "239", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\overview\\components\\area-graph.tsx": "240", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\overview\\components\\bar-graph-skeleton.tsx": "241", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\overview\\components\\bar-graph.tsx": "242", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\overview\\components\\overview.tsx": "243", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\overview\\components\\pie-graph-skeleton.tsx": "244", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\overview\\components\\pie-graph.tsx": "245", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\overview\\components\\recent-sales-skeleton.tsx": "246", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\overview\\components\\recent-sales.tsx": "247", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\products\\components\\product-form.tsx": "248", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\products\\components\\product-listing.tsx": "249", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\products\\components\\product-tables\\cell-action.tsx": "250", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\products\\components\\product-tables\\columns.tsx": "251", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\products\\components\\product-tables\\index.tsx": "252", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\products\\components\\product-tables\\options.tsx": "253", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\products\\components\\product-view-page.tsx": "254", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\profile\\components\\profile-create-form.tsx": "255", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\profile\\components\\profile-view-page.tsx": "256", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\profile\\utils\\form-schema.ts": "257", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\hooks\\use-auth.ts": "258", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\hooks\\use-breadcrumbs.tsx": "259", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\hooks\\use-callback-ref.ts": "260", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\hooks\\use-callback-ref.tsx": "261", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\hooks\\use-controllable-state.tsx": "262", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\hooks\\use-data-table.ts": "263", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\hooks\\use-debounce.tsx": "264", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\hooks\\use-debounced-callback.ts": "265", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\hooks\\use-media-query.ts": "266", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\hooks\\use-mobile.tsx": "267", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\hooks\\use-multistep-form.tsx": "268", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\hooks\\use-toast.ts": "269", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\instrumentation-client.ts": "270", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\instrumentation.ts": "271", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\lib\\auth.ts": "272", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\lib\\certificate.ts": "273", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\lib\\data-table.ts": "274", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\lib\\db\\index.ts": "275", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\lib\\db\\raw.ts": "276", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\lib\\db\\schema.ts": "277", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\lib\\feature-flags.ts": "278", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\lib\\font.ts": "279", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\lib\\format.ts": "280", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\lib\\gemini\\batch-generator.ts": "281", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\lib\\gemini\\client.ts": "282", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\lib\\gemini\\content-generator.ts": "283", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\lib\\gemini\\course-outline.ts": "284", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\lib\\gemini\\index.ts": "285", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\lib\\gemini\\quiz-generator.ts": "286", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\lib\\gemini\\types.ts": "287", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\lib\\parsers.ts": "288", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\lib\\searchparams.ts": "289", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\lib\\utils.ts": "290", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\middleware.ts": "291", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\types\\data-table.ts": "292", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\types\\database.ts": "293", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\types\\index.ts": "294", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\types\\lms.ts": "295", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\types\\tabler-icons.d.ts": "296", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\utils\\course-data-transformer.ts": "297", "C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\mdx-editor-wrapper.tsx": "298"}, {"size": 480, "mtime": 1755758661238, "results": "299", "hashOfConfig": "300"}, {"size": 28548, "mtime": 1755758661238, "results": "301", "hashOfConfig": "300"}, {"size": 9597, "mtime": 1756274124842, "results": "302", "hashOfConfig": "300"}, {"size": 21913, "mtime": 1756365584356, "results": "303", "hashOfConfig": "300"}, {"size": 15561, "mtime": 1755758661240, "results": "304", "hashOfConfig": "300"}, {"size": 24947, "mtime": 1757667242756, "results": "305", "hashOfConfig": "300"}, {"size": 1173, "mtime": 1755758661240, "results": "306", "hashOfConfig": "300"}, {"size": 8953, "mtime": 1757667242758, "results": "307", "hashOfConfig": "300"}, {"size": 1670, "mtime": 1755758661241, "results": "308", "hashOfConfig": "300"}, {"size": 1771, "mtime": 1755758661242, "results": "309", "hashOfConfig": "300"}, {"size": 2064, "mtime": 1755758661242, "results": "310", "hashOfConfig": "300"}, {"size": 2507, "mtime": 1755758661242, "results": "311", "hashOfConfig": "300"}, {"size": 1809, "mtime": 1755758661243, "results": "312", "hashOfConfig": "300"}, {"size": 4573, "mtime": 1755758661243, "results": "313", "hashOfConfig": "300"}, {"size": 5980, "mtime": 1755758661243, "results": "314", "hashOfConfig": "300"}, {"size": 5311, "mtime": 1755758661245, "results": "315", "hashOfConfig": "300"}, {"size": 5085, "mtime": 1755758661243, "results": "316", "hashOfConfig": "300"}, {"size": 3255, "mtime": 1755758661246, "results": "317", "hashOfConfig": "300"}, {"size": 5935, "mtime": 1755758661245, "results": "318", "hashOfConfig": "300"}, {"size": 24473, "mtime": 1755758661246, "results": "319", "hashOfConfig": "300"}, {"size": 16010, "mtime": 1755760789990, "results": "320", "hashOfConfig": "300"}, {"size": 12015, "mtime": 1757667242759, "results": "321", "hashOfConfig": "300"}, {"size": 6283, "mtime": 1755758661247, "results": "322", "hashOfConfig": "300"}, {"size": 4310, "mtime": 1755758661248, "results": "323", "hashOfConfig": "300"}, {"size": 5613, "mtime": 1755758661248, "results": "324", "hashOfConfig": "300"}, {"size": 3899, "mtime": 1755758661249, "results": "325", "hashOfConfig": "300"}, {"size": 5863, "mtime": 1755758661248, "results": "326", "hashOfConfig": "300"}, {"size": 5173, "mtime": 1755758661249, "results": "327", "hashOfConfig": "300"}, {"size": 6769, "mtime": 1755758661249, "results": "328", "hashOfConfig": "300"}, {"size": 15033, "mtime": 1755758661250, "results": "329", "hashOfConfig": "300"}, {"size": 10104, "mtime": 1755758661250, "results": "330", "hashOfConfig": "300"}, {"size": 12572, "mtime": 1755758661251, "results": "331", "hashOfConfig": "300"}, {"size": 16987, "mtime": 1755758661251, "results": "332", "hashOfConfig": "300"}, {"size": 992, "mtime": 1755758661251, "results": "333", "hashOfConfig": "300"}, {"size": 13892, "mtime": 1755758661252, "results": "334", "hashOfConfig": "300"}, {"size": 5251, "mtime": 1755758661252, "results": "335", "hashOfConfig": "300"}, {"size": 341, "mtime": 1755758661253, "results": "336", "hashOfConfig": "300"}, {"size": 340, "mtime": 1755758661253, "results": "337", "hashOfConfig": "300"}, {"size": 11613, "mtime": 1755758661254, "results": "338", "hashOfConfig": "300"}, {"size": 12150, "mtime": 1755758661254, "results": "339", "hashOfConfig": "300"}, {"size": 12449, "mtime": 1755758661253, "results": "340", "hashOfConfig": "300"}, {"size": 331, "mtime": 1755758661255, "results": "341", "hashOfConfig": "300"}, {"size": 7252, "mtime": 1755758661255, "results": "342", "hashOfConfig": "300"}, {"size": 18600, "mtime": 1755758661255, "results": "343", "hashOfConfig": "300"}, {"size": 11038, "mtime": 1755758661256, "results": "344", "hashOfConfig": "300"}, {"size": 13256, "mtime": 1755758661257, "results": "345", "hashOfConfig": "300"}, {"size": 13560, "mtime": 1755758661256, "results": "346", "hashOfConfig": "300"}, {"size": 215, "mtime": 1754216337007, "results": "347", "hashOfConfig": "300"}, {"size": 1114, "mtime": 1755758661257, "results": "348", "hashOfConfig": "300"}, {"size": 474, "mtime": 1755758661258, "results": "349", "hashOfConfig": "300"}, {"size": 164, "mtime": 1754216337008, "results": "350", "hashOfConfig": "300"}, {"size": 222, "mtime": 1754216337008, "results": "351", "hashOfConfig": "300"}, {"size": 2127, "mtime": 1755758661258, "results": "352", "hashOfConfig": "300"}, {"size": 161, "mtime": 1754216337009, "results": "353", "hashOfConfig": "300"}, {"size": 220, "mtime": 1754216337009, "results": "354", "hashOfConfig": "300"}, {"size": 472, "mtime": 1755758661258, "results": "355", "hashOfConfig": "300"}, {"size": 161, "mtime": 1754216337009, "results": "356", "hashOfConfig": "300"}, {"size": 209, "mtime": 1754216337009, "results": "357", "hashOfConfig": "300"}, {"size": 465, "mtime": 1755758661258, "results": "358", "hashOfConfig": "300"}, {"size": 198, "mtime": 1754216337011, "results": "359", "hashOfConfig": "300"}, {"size": 218, "mtime": 1754216337011, "results": "360", "hashOfConfig": "300"}, {"size": 468, "mtime": 1755758661258, "results": "361", "hashOfConfig": "300"}, {"size": 5292, "mtime": 1755758661258, "results": "362", "hashOfConfig": "300"}, {"size": 755, "mtime": 1755758661260, "results": "363", "hashOfConfig": "300"}, {"size": 1987, "mtime": 1755758661260, "results": "364", "hashOfConfig": "300"}, {"size": 740, "mtime": 1754216337012, "results": "365", "hashOfConfig": "300"}, {"size": 221, "mtime": 1754216337012, "results": "366", "hashOfConfig": "300"}, {"size": 12186, "mtime": 1755758661260, "results": "367", "hashOfConfig": "300"}, {"size": 15387, "mtime": 1757667242763, "results": "368", "hashOfConfig": "300"}, {"size": 18506, "mtime": 1755758661261, "results": "369", "hashOfConfig": "300"}, {"size": 325, "mtime": 1755758661261, "results": "370", "hashOfConfig": "300"}, {"size": 7247, "mtime": 1755758661262, "results": "371", "hashOfConfig": "300"}, {"size": 14632, "mtime": 1757667242764, "results": "372", "hashOfConfig": "300"}, {"size": 10657, "mtime": 1755758661264, "results": "373", "hashOfConfig": "300"}, {"size": 10797, "mtime": 1755758661264, "results": "374", "hashOfConfig": "300"}, {"size": 18582, "mtime": 1755758661263, "results": "375", "hashOfConfig": "300"}, {"size": 9420, "mtime": 1755758661263, "results": "376", "hashOfConfig": "300"}, {"size": 14804, "mtime": 1755758661263, "results": "377", "hashOfConfig": "300"}, {"size": 31856, "mtime": 1756365584495, "results": "378", "hashOfConfig": "300"}, {"size": 11899, "mtime": 1755758661265, "results": "379", "hashOfConfig": "300"}, {"size": 13216, "mtime": 1755758661266, "results": "380", "hashOfConfig": "300"}, {"size": 19123, "mtime": 1755844174433, "results": "381", "hashOfConfig": "300"}, {"size": 325, "mtime": 1755758661266, "results": "382", "hashOfConfig": "300"}, {"size": 7149, "mtime": 1755758661266, "results": "383", "hashOfConfig": "300"}, {"size": 17518, "mtime": 1757667242765, "results": "384", "hashOfConfig": "300"}, {"size": 697, "mtime": 1754216337014, "results": "385", "hashOfConfig": "300"}, {"size": 1866, "mtime": 1755758661268, "results": "386", "hashOfConfig": "300"}, {"size": 1100, "mtime": 1754216337014, "results": "387", "hashOfConfig": "300"}, {"size": 125, "mtime": 1755758661269, "results": "388", "hashOfConfig": "300"}, {"size": 1671, "mtime": 1754216337014, "results": "389", "hashOfConfig": "300"}, {"size": 1191, "mtime": 1755758661269, "results": "390", "hashOfConfig": "300"}, {"size": 14528, "mtime": 1755758661269, "results": "391", "hashOfConfig": "300"}, {"size": 4003, "mtime": 1755758661269, "results": "392", "hashOfConfig": "300"}, {"size": 5729, "mtime": 1755758661269, "results": "393", "hashOfConfig": "300"}, {"size": 15454, "mtime": 1755844542207, "results": "394", "hashOfConfig": "300"}, {"size": 5450, "mtime": 1755758661271, "results": "395", "hashOfConfig": "300"}, {"size": 55040, "mtime": 1757670094524, "results": "396", "hashOfConfig": "300"}, {"size": 2424, "mtime": 1755758661271, "results": "397", "hashOfConfig": "300"}, {"size": 22849, "mtime": 1755758661272, "results": "398", "hashOfConfig": "300"}, {"size": 18525, "mtime": 1755758661272, "results": "399", "hashOfConfig": "300"}, {"size": 7158, "mtime": 1755758661272, "results": "400", "hashOfConfig": "300"}, {"size": 5831, "mtime": 1755758661273, "results": "401", "hashOfConfig": "300"}, {"size": 15779, "mtime": 1757675323835, "results": "402", "hashOfConfig": "300"}, {"size": 9590, "mtime": 1755758661273, "results": "403", "hashOfConfig": "300"}, {"size": 1814, "mtime": 1754216337015, "results": "404", "hashOfConfig": "300"}, {"size": 1886, "mtime": 1755758661274, "results": "405", "hashOfConfig": "300"}, {"size": 2918, "mtime": 1754216337015, "results": "406", "hashOfConfig": "300"}, {"size": 647, "mtime": 1754216337016, "results": "407", "hashOfConfig": "300"}, {"size": 2381, "mtime": 1754216337016, "results": "408", "hashOfConfig": "300"}, {"size": 806, "mtime": 1754216337016, "results": "409", "hashOfConfig": "300"}, {"size": 6199, "mtime": 1755758661274, "results": "410", "hashOfConfig": "300"}, {"size": 8725, "mtime": 1755758661274, "results": "411", "hashOfConfig": "300"}, {"size": 8573, "mtime": 1755758661275, "results": "412", "hashOfConfig": "300"}, {"size": 20375, "mtime": 1755758661275, "results": "413", "hashOfConfig": "300"}, {"size": 8644, "mtime": 1755758661275, "results": "414", "hashOfConfig": "300"}, {"size": 4068, "mtime": 1755758661275, "results": "415", "hashOfConfig": "300"}, {"size": 595, "mtime": 1755758661276, "results": "416", "hashOfConfig": "300"}, {"size": 6617, "mtime": 1755758661276, "results": "417", "hashOfConfig": "300"}, {"size": 8640, "mtime": 1755758661277, "results": "418", "hashOfConfig": "300"}, {"size": 509, "mtime": 1755758661277, "results": "419", "hashOfConfig": "300"}, {"size": 2670, "mtime": 1755758661278, "results": "420", "hashOfConfig": "300"}, {"size": 530, "mtime": 1754216337017, "results": "421", "hashOfConfig": "300"}, {"size": 412, "mtime": 1754222108920, "results": "422", "hashOfConfig": "300"}, {"size": 283, "mtime": 1754216337017, "results": "423", "hashOfConfig": "300"}, {"size": 1128, "mtime": 1755758661276, "results": "424", "hashOfConfig": "300"}, {"size": 1748, "mtime": 1755758661278, "results": "425", "hashOfConfig": "300"}, {"size": 6305, "mtime": 1755758661278, "results": "426", "hashOfConfig": "300"}, {"size": 3965, "mtime": 1755758661279, "results": "427", "hashOfConfig": "300"}, {"size": 3542, "mtime": 1755758661279, "results": "428", "hashOfConfig": "300"}, {"size": 25570, "mtime": 1756365584542, "results": "429", "hashOfConfig": "300"}, {"size": 20268, "mtime": 1755758661279, "results": "430", "hashOfConfig": "300"}, {"size": 6733, "mtime": 1755758661279, "results": "431", "hashOfConfig": "300"}, {"size": 8032, "mtime": 1755758661281, "results": "432", "hashOfConfig": "300"}, {"size": 123, "mtime": 1755758661281, "results": "433", "hashOfConfig": "300"}, {"size": 4623, "mtime": 1755758661281, "results": "434", "hashOfConfig": "300"}, {"size": 8510, "mtime": 1755758661281, "results": "435", "hashOfConfig": "300"}, {"size": 9106, "mtime": 1755758661282, "results": "436", "hashOfConfig": "300"}, {"size": 5499, "mtime": 1755758661282, "results": "437", "hashOfConfig": "300"}, {"size": 11279, "mtime": 1755758661282, "results": "438", "hashOfConfig": "300"}, {"size": 710, "mtime": 1755758661282, "results": "439", "hashOfConfig": "300"}, {"size": 4402, "mtime": 1755758661283, "results": "440", "hashOfConfig": "300"}, {"size": 12029, "mtime": 1755758661283, "results": "441", "hashOfConfig": "300"}, {"size": 3073, "mtime": 1755758661283, "results": "442", "hashOfConfig": "300"}, {"size": 10316, "mtime": 1757667242766, "results": "443", "hashOfConfig": "300"}, {"size": 5499, "mtime": 1756365584564, "results": "444", "hashOfConfig": "300"}, {"size": 8680, "mtime": 1755758661284, "results": "445", "hashOfConfig": "300"}, {"size": 7244, "mtime": 1756365584608, "results": "446", "hashOfConfig": "300"}, {"size": 4895, "mtime": 1755758661285, "results": "447", "hashOfConfig": "300"}, {"size": 8410, "mtime": 1755758661285, "results": "448", "hashOfConfig": "300"}, {"size": 2755, "mtime": 1756365584661, "results": "449", "hashOfConfig": "300"}, {"size": 1063, "mtime": 1754216337018, "results": "450", "hashOfConfig": "300"}, {"size": 2498, "mtime": 1755758661285, "results": "451", "hashOfConfig": "300"}, {"size": 2647, "mtime": 1755758661286, "results": "452", "hashOfConfig": "300"}, {"size": 3642, "mtime": 1755758661286, "results": "453", "hashOfConfig": "300"}, {"size": 3714, "mtime": 1755758661286, "results": "454", "hashOfConfig": "300"}, {"size": 2490, "mtime": 1755758661286, "results": "455", "hashOfConfig": "300"}, {"size": 10618, "mtime": 1757667242767, "results": "456", "hashOfConfig": "300"}, {"size": 1906, "mtime": 1755758661287, "results": "457", "hashOfConfig": "300"}, {"size": 880, "mtime": 1755758661287, "results": "458", "hashOfConfig": "300"}, {"size": 2486, "mtime": 1754216337020, "results": "459", "hashOfConfig": "300"}, {"size": 2129, "mtime": 1754216337020, "results": "460", "hashOfConfig": "300"}, {"size": 4037, "mtime": 1754216337020, "results": "461", "hashOfConfig": "300"}, {"size": 1684, "mtime": 1754216337021, "results": "462", "hashOfConfig": "300"}, {"size": 295, "mtime": 1754216337021, "results": "463", "hashOfConfig": "300"}, {"size": 1158, "mtime": 1754216337021, "results": "464", "hashOfConfig": "300"}, {"size": 1681, "mtime": 1754216337021, "results": "465", "hashOfConfig": "300"}, {"size": 2479, "mtime": 1754216337022, "results": "466", "hashOfConfig": "300"}, {"size": 2497, "mtime": 1755758661288, "results": "467", "hashOfConfig": "300"}, {"size": 3030, "mtime": 1754216337022, "results": "468", "hashOfConfig": "300"}, {"size": 2090, "mtime": 1754216337023, "results": "469", "hashOfConfig": "300"}, {"size": 10300, "mtime": 1754216337023, "results": "470", "hashOfConfig": "300"}, {"size": 1265, "mtime": 1754216337024, "results": "471", "hashOfConfig": "300"}, {"size": 839, "mtime": 1754216337024, "results": "472", "hashOfConfig": "300"}, {"size": 4849, "mtime": 1754216337024, "results": "473", "hashOfConfig": "300"}, {"size": 8498, "mtime": 1754216337024, "results": "474", "hashOfConfig": "300"}, {"size": 3963, "mtime": 1754216337025, "results": "475", "hashOfConfig": "300"}, {"size": 4218, "mtime": 1754216337025, "results": "476", "hashOfConfig": "300"}, {"size": 8624, "mtime": 1755758661288, "results": "477", "hashOfConfig": "300"}, {"size": 3964, "mtime": 1754216337025, "results": "478", "hashOfConfig": "300"}, {"size": 336, "mtime": 1754216337026, "results": "479", "hashOfConfig": "300"}, {"size": 1584, "mtime": 1754216337026, "results": "480", "hashOfConfig": "300"}, {"size": 2345, "mtime": 1754216337027, "results": "481", "hashOfConfig": "300"}, {"size": 992, "mtime": 1754216337027, "results": "482", "hashOfConfig": "300"}, {"size": 641, "mtime": 1754216337027, "results": "483", "hashOfConfig": "300"}, {"size": 8695, "mtime": 1754216337027, "results": "484", "hashOfConfig": "300"}, {"size": 825, "mtime": 1754216337027, "results": "485", "hashOfConfig": "300"}, {"size": 6847, "mtime": 1754216337028, "results": "486", "hashOfConfig": "300"}, {"size": 2850, "mtime": 1754216337029, "results": "487", "hashOfConfig": "300"}, {"size": 1692, "mtime": 1754216337029, "results": "488", "hashOfConfig": "300"}, {"size": 817, "mtime": 1755758661289, "results": "489", "hashOfConfig": "300"}, {"size": 1519, "mtime": 1754216337029, "results": "490", "hashOfConfig": "300"}, {"size": 2094, "mtime": 1754216337030, "results": "491", "hashOfConfig": "300"}, {"size": 1710, "mtime": 1754216337030, "results": "492", "hashOfConfig": "300"}, {"size": 6454, "mtime": 1754216337030, "results": "493", "hashOfConfig": "300"}, {"size": 763, "mtime": 1755758661289, "results": "494", "hashOfConfig": "300"}, {"size": 4245, "mtime": 1754216337031, "results": "495", "hashOfConfig": "300"}, {"size": 22467, "mtime": 1755758661289, "results": "496", "hashOfConfig": "300"}, {"size": 292, "mtime": 1754216337031, "results": "497", "hashOfConfig": "300"}, {"size": 2071, "mtime": 1754216337031, "results": "498", "hashOfConfig": "300"}, {"size": 595, "mtime": 1754216337032, "results": "499", "hashOfConfig": "300"}, {"size": 1214, "mtime": 1754216337032, "results": "500", "hashOfConfig": "300"}, {"size": 3280, "mtime": 1754216337033, "results": "501", "hashOfConfig": "300"}, {"size": 6331, "mtime": 1754216337033, "results": "502", "hashOfConfig": "300"}, {"size": 6155, "mtime": 1754216337034, "results": "503", "hashOfConfig": "300"}, {"size": 3896, "mtime": 1754216337034, "results": "504", "hashOfConfig": "300"}, {"size": 3836, "mtime": 1754216337035, "results": "505", "hashOfConfig": "300"}, {"size": 7498, "mtime": 1754216337035, "results": "506", "hashOfConfig": "300"}, {"size": 4497, "mtime": 1754216337035, "results": "507", "hashOfConfig": "300"}, {"size": 2391, "mtime": 1754216337035, "results": "508", "hashOfConfig": "300"}, {"size": 3579, "mtime": 1754216337036, "results": "509", "hashOfConfig": "300"}, {"size": 2575, "mtime": 1754216337032, "results": "510", "hashOfConfig": "300"}, {"size": 1965, "mtime": 1755758661291, "results": "511", "hashOfConfig": "300"}, {"size": 781, "mtime": 1754216337036, "results": "512", "hashOfConfig": "300"}, {"size": 2007, "mtime": 1754216337036, "results": "513", "hashOfConfig": "300"}, {"size": 1620, "mtime": 1754216337037, "results": "514", "hashOfConfig": "300"}, {"size": 1961, "mtime": 1754216337037, "results": "515", "hashOfConfig": "300"}, {"size": 1162, "mtime": 1755758661291, "results": "516", "hashOfConfig": "300"}, {"size": 2758, "mtime": 1754216337038, "results": "517", "hashOfConfig": "300"}, {"size": 5606, "mtime": 1757667242768, "results": "518", "hashOfConfig": "300"}, {"size": 3035, "mtime": 1755758661291, "results": "519", "hashOfConfig": "300"}, {"size": 2128, "mtime": 1755758661292, "results": "520", "hashOfConfig": "300"}, {"size": 4212, "mtime": 1754983009449, "results": "521", "hashOfConfig": "300"}, {"size": 16712, "mtime": 1755758661292, "results": "522", "hashOfConfig": "300"}, {"size": 17835, "mtime": 1755758661292, "results": "523", "hashOfConfig": "300"}, {"size": 7250, "mtime": 1755758661293, "results": "524", "hashOfConfig": "300"}, {"size": 587, "mtime": 1754216337040, "results": "525", "hashOfConfig": "300"}, {"size": 183, "mtime": 1755758661293, "results": "526", "hashOfConfig": "300"}, {"size": 17400, "mtime": 1755758661294, "results": "527", "hashOfConfig": "300"}, {"size": 23140, "mtime": 1755758661294, "results": "528", "hashOfConfig": "300"}, {"size": 3823, "mtime": 1755758661294, "results": "529", "hashOfConfig": "300"}, {"size": 3726, "mtime": 1754216337042, "results": "530", "hashOfConfig": "300"}, {"size": 10137, "mtime": 1754216337042, "results": "531", "hashOfConfig": "300"}, {"size": 575, "mtime": 1754216337042, "results": "532", "hashOfConfig": "300"}, {"size": 1868, "mtime": 1754216337043, "results": "533", "hashOfConfig": "300"}, {"size": 2224, "mtime": 1754216337043, "results": "534", "hashOfConfig": "300"}, {"size": 2169, "mtime": 1755758661295, "results": "535", "hashOfConfig": "300"}, {"size": 565, "mtime": 1754216337043, "results": "536", "hashOfConfig": "300"}, {"size": 2675, "mtime": 1754216337044, "results": "537", "hashOfConfig": "300"}, {"size": 1029, "mtime": 1754216337044, "results": "538", "hashOfConfig": "300"}, {"size": 3951, "mtime": 1755758661296, "results": "539", "hashOfConfig": "300"}, {"size": 1465, "mtime": 1754216337044, "results": "540", "hashOfConfig": "300"}, {"size": 10034, "mtime": 1754216337045, "results": "541", "hashOfConfig": "300"}, {"size": 6311, "mtime": 1755758661296, "results": "542", "hashOfConfig": "300"}, {"size": 783, "mtime": 1754216337045, "results": "543", "hashOfConfig": "300"}, {"size": 5025, "mtime": 1755758661296, "results": "544", "hashOfConfig": "300"}, {"size": 1015, "mtime": 1754216337046, "results": "545", "hashOfConfig": "300"}, {"size": 2173, "mtime": 1754216337046, "results": "546", "hashOfConfig": "300"}, {"size": 6627, "mtime": 1754216337046, "results": "547", "hashOfConfig": "300"}, {"size": 1070, "mtime": 1754233447164, "results": "548", "hashOfConfig": "300"}, {"size": 1740, "mtime": 1755758661297, "results": "549", "hashOfConfig": "300"}, {"size": 2153, "mtime": 1754216337047, "results": "550", "hashOfConfig": "300"}, {"size": 1080, "mtime": 1754216337047, "results": "551", "hashOfConfig": "300"}, {"size": 406, "mtime": 1754216337048, "results": "552", "hashOfConfig": "300"}, {"size": 673, "mtime": 1754216337048, "results": "553", "hashOfConfig": "300"}, {"size": 22973, "mtime": 1755758661297, "results": "554", "hashOfConfig": "300"}, {"size": 2677, "mtime": 1755758661297, "results": "555", "hashOfConfig": "300"}, {"size": 1437, "mtime": 1754216337049, "results": "556", "hashOfConfig": "300"}, {"size": 696, "mtime": 1755758661298, "results": "557", "hashOfConfig": "300"}, {"size": 1551, "mtime": 1755758661298, "results": "558", "hashOfConfig": "300"}, {"size": 749, "mtime": 1754216337049, "results": "559", "hashOfConfig": "300"}, {"size": 749, "mtime": 1754216337050, "results": "560", "hashOfConfig": "300"}, {"size": 1945, "mtime": 1754216337050, "results": "561", "hashOfConfig": "300"}, {"size": 8379, "mtime": 1754216337050, "results": "562", "hashOfConfig": "300"}, {"size": 420, "mtime": 1754216337050, "results": "563", "hashOfConfig": "300"}, {"size": 726, "mtime": 1754216337050, "results": "564", "hashOfConfig": "300"}, {"size": 507, "mtime": 1754216337050, "results": "565", "hashOfConfig": "300"}, {"size": 605, "mtime": 1754216337050, "results": "566", "hashOfConfig": "300"}, {"size": 750, "mtime": 1754216337051, "results": "567", "hashOfConfig": "300"}, {"size": 514, "mtime": 1755758661298, "results": "568", "hashOfConfig": "300"}, {"size": 1299, "mtime": 1754216337051, "results": "569", "hashOfConfig": "300"}, {"size": 1014, "mtime": 1754216337051, "results": "570", "hashOfConfig": "300"}, {"size": 2530, "mtime": 1755758661298, "results": "571", "hashOfConfig": "300"}, {"size": 17250, "mtime": 1756274473401, "results": "572", "hashOfConfig": "300"}, {"size": 2556, "mtime": 1754216337051, "results": "573", "hashOfConfig": "300"}, {"size": 373, "mtime": 1755758661299, "results": "574", "hashOfConfig": "300"}, {"size": 351, "mtime": 1755758661299, "results": "575", "hashOfConfig": "300"}, {"size": 18060, "mtime": 1757667242769, "results": "576", "hashOfConfig": "300"}, {"size": 1556, "mtime": 1755758661299, "results": "577", "hashOfConfig": "300"}, {"size": 904, "mtime": 1755758661301, "results": "578", "hashOfConfig": "300"}, {"size": 402, "mtime": 1754216337051, "results": "579", "hashOfConfig": "300"}, {"size": 10522, "mtime": 1755758661301, "results": "580", "hashOfConfig": "300"}, {"size": 12304, "mtime": 1755758661301, "results": "581", "hashOfConfig": "300"}, {"size": 12121, "mtime": 1755758661301, "results": "582", "hashOfConfig": "300"}, {"size": 9701, "mtime": 1755758661302, "results": "583", "hashOfConfig": "300"}, {"size": 1181, "mtime": 1755758661302, "results": "584", "hashOfConfig": "300"}, {"size": 19805, "mtime": 1757667242770, "results": "585", "hashOfConfig": "300"}, {"size": 3134, "mtime": 1755758661303, "results": "586", "hashOfConfig": "300"}, {"size": 2557, "mtime": 1754216337052, "results": "587", "hashOfConfig": "300"}, {"size": 599, "mtime": 1754216337052, "results": "588", "hashOfConfig": "300"}, {"size": 756, "mtime": 1754659090871, "results": "589", "hashOfConfig": "300"}, {"size": 888, "mtime": 1755758661303, "results": "590", "hashOfConfig": "300"}, {"size": 1308, "mtime": 1754216337052, "results": "591", "hashOfConfig": "300"}, {"size": 4492, "mtime": 1755758661303, "results": "592", "hashOfConfig": "300"}, {"size": 755, "mtime": 1754216337053, "results": "593", "hashOfConfig": "300"}, {"size": 5700, "mtime": 1756365584701, "results": "594", "hashOfConfig": "300"}, {"size": 1832, "mtime": 1755758661304, "results": "595", "hashOfConfig": "300"}, {"size": 13249, "mtime": 1755760790052, "results": "596", "hashOfConfig": "300"}, {"size": 3015, "mtime": 1757675249465, "results": "597", "hashOfConfig": "300"}, {"filePath": "598", "messages": "599", "suppressedMessages": "600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1m9syvw", {"filePath": "601", "messages": "602", "suppressedMessages": "603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "604", "messages": "605", "suppressedMessages": "606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "607", "messages": "608", "suppressedMessages": "609", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "610", "messages": "611", "suppressedMessages": "612", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "613", "messages": "614", "suppressedMessages": "615", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "616", "messages": "617", "suppressedMessages": "618", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "619", "messages": "620", "suppressedMessages": "621", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "622", "messages": "623", "suppressedMessages": "624", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "625", "messages": "626", "suppressedMessages": "627", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "628", "messages": "629", "suppressedMessages": "630", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "631", "messages": "632", "suppressedMessages": "633", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "634", "messages": "635", "suppressedMessages": "636", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "637", "messages": "638", "suppressedMessages": "639", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "640", "messages": "641", "suppressedMessages": "642", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "643", "messages": "644", "suppressedMessages": "645", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "646", "messages": "647", "suppressedMessages": "648", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "649", "messages": "650", "suppressedMessages": "651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "652", "messages": "653", "suppressedMessages": "654", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "655", "messages": "656", "suppressedMessages": "657", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "658", "messages": "659", "suppressedMessages": "660", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "661", "messages": "662", "suppressedMessages": "663", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "664", "messages": "665", "suppressedMessages": "666", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "667", "messages": "668", "suppressedMessages": "669", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "670", "messages": "671", "suppressedMessages": "672", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "673", "messages": "674", "suppressedMessages": "675", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "676", "messages": "677", "suppressedMessages": "678", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "679", "messages": "680", "suppressedMessages": "681", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "682", "messages": "683", "suppressedMessages": "684", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "685", "messages": "686", "suppressedMessages": "687", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "688", "messages": "689", "suppressedMessages": "690", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "691", "messages": "692", "suppressedMessages": "693", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "694", "messages": "695", "suppressedMessages": "696", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "697", "messages": "698", "suppressedMessages": "699", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "700", "messages": "701", "suppressedMessages": "702", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "703", "messages": "704", "suppressedMessages": "705", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "706", "messages": "707", "suppressedMessages": "708", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "709", "messages": "710", "suppressedMessages": "711", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "712", "messages": "713", "suppressedMessages": "714", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "715", "messages": "716", "suppressedMessages": "717", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "718", "messages": "719", "suppressedMessages": "720", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "721", "messages": "722", "suppressedMessages": "723", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "724", "messages": "725", "suppressedMessages": "726", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "727", "messages": "728", "suppressedMessages": "729", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "730", "messages": "731", "suppressedMessages": "732", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "733", "messages": "734", "suppressedMessages": "735", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "736", "messages": "737", "suppressedMessages": "738", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "739", "messages": "740", "suppressedMessages": "741", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "742", "messages": "743", "suppressedMessages": "744", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "745", "messages": "746", "suppressedMessages": "747", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "748", "messages": "749", "suppressedMessages": "750", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "751", "messages": "752", "suppressedMessages": "753", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "754", "messages": "755", "suppressedMessages": "756", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "757", "messages": "758", "suppressedMessages": "759", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "760", "messages": "761", "suppressedMessages": "762", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "763", "messages": "764", "suppressedMessages": "765", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "766", "messages": "767", "suppressedMessages": "768", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "769", "messages": "770", "suppressedMessages": "771", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "772", "messages": "773", "suppressedMessages": "774", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "775", "messages": "776", "suppressedMessages": "777", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "778", "messages": "779", "suppressedMessages": "780", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "781", "messages": "782", "suppressedMessages": "783", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "784", "messages": "785", "suppressedMessages": "786", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "787", "messages": "788", "suppressedMessages": "789", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "790", "messages": "791", "suppressedMessages": "792", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "793", "messages": "794", "suppressedMessages": "795", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "796", "messages": "797", "suppressedMessages": "798", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "799", "messages": "800", "suppressedMessages": "801", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "802", "messages": "803", "suppressedMessages": "804", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "805", "messages": "806", "suppressedMessages": "807", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "808", "messages": "809", "suppressedMessages": "810", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "811", "messages": "812", "suppressedMessages": "813", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "814", "messages": "815", "suppressedMessages": "816", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "817", "messages": "818", "suppressedMessages": "819", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "820", "messages": "821", "suppressedMessages": "822", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "823"}, {"filePath": "824", "messages": "825", "suppressedMessages": "826", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "827", "messages": "828", "suppressedMessages": "829", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "830", "messages": "831", "suppressedMessages": "832", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "833", "messages": "834", "suppressedMessages": "835", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "836", "messages": "837", "suppressedMessages": "838", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "839", "messages": "840", "suppressedMessages": "841", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "842"}, {"filePath": "843", "messages": "844", "suppressedMessages": "845", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "846", "messages": "847", "suppressedMessages": "848", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "849", "messages": "850", "suppressedMessages": "851", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "852", "messages": "853", "suppressedMessages": "854", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "855", "messages": "856", "suppressedMessages": "857", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "858", "messages": "859", "suppressedMessages": "860", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "861", "messages": "862", "suppressedMessages": "863", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "864", "messages": "865", "suppressedMessages": "866", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "867", "messages": "868", "suppressedMessages": "869", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "870", "messages": "871", "suppressedMessages": "872", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "873", "messages": "874", "suppressedMessages": "875", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "876", "messages": "877", "suppressedMessages": "878", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "879", "messages": "880", "suppressedMessages": "881", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "882", "messages": "883", "suppressedMessages": "884", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "885"}, {"filePath": "886", "messages": "887", "suppressedMessages": "888", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "889", "messages": "890", "suppressedMessages": "891", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "892"}, {"filePath": "893", "messages": "894", "suppressedMessages": "895", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "896", "messages": "897", "suppressedMessages": "898", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "899", "messages": "900", "suppressedMessages": "901", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "902"}, {"filePath": "903", "messages": "904", "suppressedMessages": "905", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "906", "messages": "907", "suppressedMessages": "908", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "909", "messages": "910", "suppressedMessages": "911", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "912", "messages": "913", "suppressedMessages": "914", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "915", "messages": "916", "suppressedMessages": "917", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "918", "messages": "919", "suppressedMessages": "920", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "921", "messages": "922", "suppressedMessages": "923", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "924", "messages": "925", "suppressedMessages": "926", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "927", "messages": "928", "suppressedMessages": "929", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "930", "messages": "931", "suppressedMessages": "932", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "933", "messages": "934", "suppressedMessages": "935", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "936", "messages": "937", "suppressedMessages": "938", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "939", "messages": "940", "suppressedMessages": "941", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "942", "messages": "943", "suppressedMessages": "944", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "945", "messages": "946", "suppressedMessages": "947", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "948", "messages": "949", "suppressedMessages": "950", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "951", "messages": "952", "suppressedMessages": "953", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "954"}, {"filePath": "955", "messages": "956", "suppressedMessages": "957", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "958", "messages": "959", "suppressedMessages": "960", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "961", "messages": "962", "suppressedMessages": "963", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "964", "messages": "965", "suppressedMessages": "966", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "967", "messages": "968", "suppressedMessages": "969", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "970", "messages": "971", "suppressedMessages": "972", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "973", "messages": "974", "suppressedMessages": "975", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "976", "messages": "977", "suppressedMessages": "978", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "979", "messages": "980", "suppressedMessages": "981", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "982", "messages": "983", "suppressedMessages": "984", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "985", "messages": "986", "suppressedMessages": "987", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "988", "messages": "989", "suppressedMessages": "990", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "991", "messages": "992", "suppressedMessages": "993", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "994"}, {"filePath": "995", "messages": "996", "suppressedMessages": "997", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "998", "messages": "999", "suppressedMessages": "1000", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1001", "messages": "1002", "suppressedMessages": "1003", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1004", "messages": "1005", "suppressedMessages": "1006", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1007", "messages": "1008", "suppressedMessages": "1009", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "1010"}, {"filePath": "1011", "messages": "1012", "suppressedMessages": "1013", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1014", "messages": "1015", "suppressedMessages": "1016", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "1017"}, {"filePath": "1018", "messages": "1019", "suppressedMessages": "1020", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1021", "messages": "1022", "suppressedMessages": "1023", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "1024"}, {"filePath": "1025", "messages": "1026", "suppressedMessages": "1027", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1028", "messages": "1029", "suppressedMessages": "1030", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1031", "messages": "1032", "suppressedMessages": "1033", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1034", "messages": "1035", "suppressedMessages": "1036", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1037", "messages": "1038", "suppressedMessages": "1039", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "1040"}, {"filePath": "1041", "messages": "1042", "suppressedMessages": "1043", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1044", "messages": "1045", "suppressedMessages": "1046", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1047", "messages": "1048", "suppressedMessages": "1049", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1050", "messages": "1051", "suppressedMessages": "1052", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1053", "messages": "1054", "suppressedMessages": "1055", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1056", "messages": "1057", "suppressedMessages": "1058", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1059", "messages": "1060", "suppressedMessages": "1061", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1062", "messages": "1063", "suppressedMessages": "1064", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1065", "messages": "1066", "suppressedMessages": "1067", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1068", "messages": "1069", "suppressedMessages": "1070", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1071", "messages": "1072", "suppressedMessages": "1073", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1074", "messages": "1075", "suppressedMessages": "1076", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1077", "messages": "1078", "suppressedMessages": "1079", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1080", "messages": "1081", "suppressedMessages": "1082", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1083", "messages": "1084", "suppressedMessages": "1085", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1086", "messages": "1087", "suppressedMessages": "1088", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1089", "messages": "1090", "suppressedMessages": "1091", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1092", "messages": "1093", "suppressedMessages": "1094", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1095", "messages": "1096", "suppressedMessages": "1097", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1098", "messages": "1099", "suppressedMessages": "1100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1101", "messages": "1102", "suppressedMessages": "1103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1104", "messages": "1105", "suppressedMessages": "1106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1107", "messages": "1108", "suppressedMessages": "1109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1110", "messages": "1111", "suppressedMessages": "1112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1113", "messages": "1114", "suppressedMessages": "1115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1116", "messages": "1117", "suppressedMessages": "1118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1119", "messages": "1120", "suppressedMessages": "1121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1122", "messages": "1123", "suppressedMessages": "1124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1125", "messages": "1126", "suppressedMessages": "1127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1128", "messages": "1129", "suppressedMessages": "1130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1131", "messages": "1132", "suppressedMessages": "1133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1134", "messages": "1135", "suppressedMessages": "1136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1137", "messages": "1138", "suppressedMessages": "1139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1140", "messages": "1141", "suppressedMessages": "1142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1143", "messages": "1144", "suppressedMessages": "1145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1146", "messages": "1147", "suppressedMessages": "1148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1149", "messages": "1150", "suppressedMessages": "1151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1152", "messages": "1153", "suppressedMessages": "1154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1155", "messages": "1156", "suppressedMessages": "1157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1158", "messages": "1159", "suppressedMessages": "1160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1161", "messages": "1162", "suppressedMessages": "1163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1164", "messages": "1165", "suppressedMessages": "1166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1167", "messages": "1168", "suppressedMessages": "1169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1170", "messages": "1171", "suppressedMessages": "1172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1173", "messages": "1174", "suppressedMessages": "1175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1176", "messages": "1177", "suppressedMessages": "1178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1179", "messages": "1180", "suppressedMessages": "1181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1182", "messages": "1183", "suppressedMessages": "1184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1185", "messages": "1186", "suppressedMessages": "1187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1188", "messages": "1189", "suppressedMessages": "1190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1191", "messages": "1192", "suppressedMessages": "1193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1194", "messages": "1195", "suppressedMessages": "1196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1197", "messages": "1198", "suppressedMessages": "1199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1200", "messages": "1201", "suppressedMessages": "1202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1203", "messages": "1204", "suppressedMessages": "1205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1206", "messages": "1207", "suppressedMessages": "1208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1209", "messages": "1210", "suppressedMessages": "1211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1212", "messages": "1213", "suppressedMessages": "1214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1215", "messages": "1216", "suppressedMessages": "1217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1218", "messages": "1219", "suppressedMessages": "1220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1221", "messages": "1222", "suppressedMessages": "1223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1224", "messages": "1225", "suppressedMessages": "1226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1227", "messages": "1228", "suppressedMessages": "1229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1230", "messages": "1231", "suppressedMessages": "1232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1233", "messages": "1234", "suppressedMessages": "1235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1236", "messages": "1237", "suppressedMessages": "1238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1239", "messages": "1240", "suppressedMessages": "1241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1242", "messages": "1243", "suppressedMessages": "1244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1245", "messages": "1246", "suppressedMessages": "1247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1248", "messages": "1249", "suppressedMessages": "1250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1251", "messages": "1252", "suppressedMessages": "1253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1254", "messages": "1255", "suppressedMessages": "1256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1257", "messages": "1258", "suppressedMessages": "1259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1260", "messages": "1261", "suppressedMessages": "1262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1263", "messages": "1264", "suppressedMessages": "1265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1266", "messages": "1267", "suppressedMessages": "1268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1269", "messages": "1270", "suppressedMessages": "1271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1272", "messages": "1273", "suppressedMessages": "1274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1275", "messages": "1276", "suppressedMessages": "1277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1278", "messages": "1279", "suppressedMessages": "1280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1281", "messages": "1282", "suppressedMessages": "1283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1284", "messages": "1285", "suppressedMessages": "1286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1287", "messages": "1288", "suppressedMessages": "1289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1290", "messages": "1291", "suppressedMessages": "1292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "1293"}, {"filePath": "1294", "messages": "1295", "suppressedMessages": "1296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "1297"}, {"filePath": "1298", "messages": "1299", "suppressedMessages": "1300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1301", "messages": "1302", "suppressedMessages": "1303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1304", "messages": "1305", "suppressedMessages": "1306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1307", "messages": "1308", "suppressedMessages": "1309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1310", "messages": "1311", "suppressedMessages": "1312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1313", "messages": "1314", "suppressedMessages": "1315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1316", "messages": "1317", "suppressedMessages": "1318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1319", "messages": "1320", "suppressedMessages": "1321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1322", "messages": "1323", "suppressedMessages": "1324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1325", "messages": "1326", "suppressedMessages": "1327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1328", "messages": "1329", "suppressedMessages": "1330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1331", "messages": "1332", "suppressedMessages": "1333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1334", "messages": "1335", "suppressedMessages": "1336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1337", "messages": "1338", "suppressedMessages": "1339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1340", "messages": "1341", "suppressedMessages": "1342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1343", "messages": "1344", "suppressedMessages": "1345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1346", "messages": "1347", "suppressedMessages": "1348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1349", "messages": "1350", "suppressedMessages": "1351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1352", "messages": "1353", "suppressedMessages": "1354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1355", "messages": "1356", "suppressedMessages": "1357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1358", "messages": "1359", "suppressedMessages": "1360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1361", "messages": "1362", "suppressedMessages": "1363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1364", "messages": "1365", "suppressedMessages": "1366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1367", "messages": "1368", "suppressedMessages": "1369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1370", "messages": "1371", "suppressedMessages": "1372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1373", "messages": "1374", "suppressedMessages": "1375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1376", "messages": "1377", "suppressedMessages": "1378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1379", "messages": "1380", "suppressedMessages": "1381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1382", "messages": "1383", "suppressedMessages": "1384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1385", "messages": "1386", "suppressedMessages": "1387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1388", "messages": "1389", "suppressedMessages": "1390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1391", "messages": "1392", "suppressedMessages": "1393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1394", "messages": "1395", "suppressedMessages": "1396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1397", "messages": "1398", "suppressedMessages": "1399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1400", "messages": "1401", "suppressedMessages": "1402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1403", "messages": "1404", "suppressedMessages": "1405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1406", "messages": "1407", "suppressedMessages": "1408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1409", "messages": "1410", "suppressedMessages": "1411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1412", "messages": "1413", "suppressedMessages": "1414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1415", "messages": "1416", "suppressedMessages": "1417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1418", "messages": "1419", "suppressedMessages": "1420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1421", "messages": "1422", "suppressedMessages": "1423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1424", "messages": "1425", "suppressedMessages": "1426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1427", "messages": "1428", "suppressedMessages": "1429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1430", "messages": "1431", "suppressedMessages": "1432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1433", "messages": "1434", "suppressedMessages": "1435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1436", "messages": "1437", "suppressedMessages": "1438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1439", "messages": "1440", "suppressedMessages": "1441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1442", "messages": "1443", "suppressedMessages": "1444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1445", "messages": "1446", "suppressedMessages": "1447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1448", "messages": "1449", "suppressedMessages": "1450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1451", "messages": "1452", "suppressedMessages": "1453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1454", "messages": "1455", "suppressedMessages": "1456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1457", "messages": "1458", "suppressedMessages": "1459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1460", "messages": "1461", "suppressedMessages": "1462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1463", "messages": "1464", "suppressedMessages": "1465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1466", "messages": "1467", "suppressedMessages": "1468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1469", "messages": "1470", "suppressedMessages": "1471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1472", "messages": "1473", "suppressedMessages": "1474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1475", "messages": "1476", "suppressedMessages": "1477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1478", "messages": "1479", "suppressedMessages": "1480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1481", "messages": "1482", "suppressedMessages": "1483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1484", "messages": "1485", "suppressedMessages": "1486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1487", "messages": "1488", "suppressedMessages": "1489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1490", "messages": "1491", "suppressedMessages": "1492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1493", "messages": "1494", "suppressedMessages": "1495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1496", "messages": "1497", "suppressedMessages": "1498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1499", "messages": "1500", "suppressedMessages": "1501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1502", "messages": "1503", "suppressedMessages": "1504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\(course-view)\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\(course-view)\\my-courses\\[courseId]\\exam\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\(course-view)\\my-courses\\[courseId]\\exam\\results\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\(course-view)\\my-courses\\[courseId]\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\(students-page)\\courses\\material-section.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\(students-page)\\courses\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\(students-page)\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\(students-page)\\my-courses\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\auth\\login\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\auth\\register\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\auth\\signin\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\auth\\signup\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\certificates\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\chapters\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\chapters\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\class-enrollments\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\class-enrollments\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\classes\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\classes\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\courses\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\courses\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\enrollments\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\enrollments\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\institutions\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\institutions\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\modules\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\modules\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\questions\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\questions\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\quizzes\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\quizzes\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\reports\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\subscriptions\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\upload\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\users\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\users\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\auth\\sign-in\\[[...sign-in]]\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\auth\\sign-up\\[[...sign-up]]\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\institutions\\new\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\institutions\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\institutions\\[id]\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\subscriptions\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\users\\new\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\users\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\users\\[id]\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\kanban\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\overview\\@area_stats\\error.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\overview\\@area_stats\\loading.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\overview\\@area_stats\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\overview\\@bar_stats\\error.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\overview\\@bar_stats\\loading.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\overview\\@bar_stats\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\overview\\@pie_stats\\error.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\overview\\@pie_stats\\loading.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\overview\\@pie_stats\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\overview\\@sales\\error.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\overview\\@sales\\loading.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\overview\\@sales\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\overview\\error.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\overview\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\product\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\product\\[productId]\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\profile\\[[...profile]]\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\student\\certificates\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\student\\courses\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\student\\courses\\[id]\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\student\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\student\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\student\\progress\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\classes\\new\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\classes\\page.tsx", ["1505"], [], "'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardDescription,\r\n  CardFooter,\r\n  CardHeader,\r\n  CardTitle\r\n} from '@/components/ui/card';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger\r\n} from '@/components/ui/dropdown-menu';\r\nimport {\r\n  Users,\r\n  Plus,\r\n  Search,\r\n  MoreHorizontal,\r\n  Edit,\r\n  Trash2,\r\n  BookOpen,\r\n  Loader2,\r\n  ImageIcon\r\n} from 'lucide-react';\r\nimport Link from 'next/link';\r\nimport { authStorage } from '@/lib/auth';\r\nimport { toast } from 'sonner';\r\nimport { Skeleton } from '@/components/ui/skeleton';\r\n\r\ninterface ClassData {\r\n  id: number;\r\n  name: string;\r\n  description: string;\r\n  studentCount: number;\r\n  courseCount: number;\r\n  createdAt: string;\r\n  status: string;\r\n  coverPicture?: string;\r\n}\r\n\r\n// Skeleton loading component\r\nconst ClassSkeleton = () => (\r\n  <Card>\r\n    <CardHeader>\r\n      <Skeleton className='h-48 w-full rounded-lg mb-4' />\r\n      <Skeleton className='h-6 w-3/4 mb-2' />\r\n      <Skeleton className='h-4 w-full mb-1' />\r\n      <Skeleton className='h-4 w-2/3' />\r\n    </CardHeader>\r\n    <CardContent>\r\n      <div className='flex justify-between mb-4'>\r\n        <div className='space-y-2'>\r\n          <Skeleton className='h-4 w-20' />\r\n          <Skeleton className='h-4 w-24' />\r\n        </div>\r\n        <div className='space-y-2'>\r\n          <Skeleton className='h-4 w-20' />\r\n          <Skeleton className='h-4 w-24' />\r\n        </div>\r\n      </div>\r\n      <div className='flex justify-between items-center'>\r\n        <Skeleton className='h-6 w-20' />\r\n        <Skeleton className='h-4 w-32' />\r\n      </div>\r\n    </CardContent>\r\n    <CardFooter className='flex justify-end gap-3'>\r\n      <Skeleton className='h-9 w-16' />\r\n      <Skeleton className='h-9 w-16' />\r\n    </CardFooter>\r\n  </Card>\r\n);\r\n\r\nexport default function ClassesPage() {\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [classes, setClasses] = useState<ClassData[]>([]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [isDeleting, setIsDeleting] = useState<number | null>(null);\r\n\r\n  useEffect(() => {\r\n    fetchClasses();\r\n  }, []);\r\n\r\n  const fetchClasses = async () => {\r\n    try {\r\n      const user = authStorage.getUser();\r\n      if (!user) {\r\n        toast.error('Please log in to view classes');\r\n        return;\r\n      }\r\n\r\n      const response = await fetch(`/api/classes?teacherId=${user.id}`);\r\n      const data = await response.json();\r\n\r\n      if (data.success) {\r\n        setClasses(data.classes || []);\r\n      } else {\r\n        toast.error(data.error || 'Failed to fetch classes');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching classes:', error);\r\n      toast.error('Failed to fetch classes');\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleDeleteClass = async (classId: number) => {\r\n    if (!confirm('Are you sure you want to delete this class? This action cannot be undone.')) {\r\n      return;\r\n    }\r\n\r\n    setIsDeleting(classId);\r\n    try {\r\n      const user = authStorage.getUser();\r\n      if (!user) {\r\n        toast.error('Please log in to delete classes');\r\n        return;\r\n      }\r\n\r\n      const response = await fetch(`/api/classes/${classId}?teacherId=${user.id}`, {\r\n        method: 'DELETE'\r\n      });\r\n      const data = await response.json();\r\n\r\n      if (data.success) {\r\n        toast.success('Class deleted successfully');\r\n        fetchClasses(); // Refresh the list\r\n      } else {\r\n        toast.error(data.error || 'Failed to delete class');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error deleting class:', error);\r\n      toast.error('Failed to delete class');\r\n    } finally {\r\n      setIsDeleting(null);\r\n    }\r\n  };\r\n\r\n  const filteredClasses = classes.filter(\r\n    (classItem) =>\r\n      classItem.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n      classItem.description.toLowerCase().includes(searchTerm.toLowerCase())\r\n  );\r\n\r\n  return (\r\n    <div className='space-y-6'>\r\n      <div className='flex items-center justify-between'>\r\n        <div>\r\n          <h1 className='text-3xl font-bold tracking-tight'>My Classes</h1>\r\n          <p className='text-muted-foreground'>\r\n            Manage your classes and student groups\r\n          </p>\r\n        </div>\r\n        <Link href='/dashboard/teacher/classes/new'>\r\n          <Button>\r\n            <Plus className='mr-2 h-4 w-4' />\r\n            Create Class\r\n          </Button>\r\n        </Link>\r\n      </div>\r\n      <div className='flex items-center space-x-2'>\r\n        <div className='relative flex-1'>\r\n          <Search className='text-muted-foreground absolute top-2.5 left-2 h-4 w-4' />\r\n          <Input\r\n            placeholder='Search classes...'\r\n            value={searchTerm}\r\n            onChange={(e) => setSearchTerm(e.target.value)}\r\n            className='pl-8'\r\n          />\r\n        </div>\r\n      </div>\r\n      <div className='grid gap-6 md:grid-cols-2 lg:grid-cols-3'>\r\n        {isLoading ? (\r\n          // Show loading skeletons\r\n          Array.from({ length: 6 }).map((_, index) => (\r\n            <ClassSkeleton key={index} />\r\n          ))\r\n        ) : filteredClasses.length === 0 ? (\r\n          <div className='col-span-full flex flex-col items-center justify-center py-12'>\r\n            <div className='text-6xl mb-4'>👩‍🏫</div>\r\n            <h3 className='mt-2 text-xl font-semibold'>No classes found</h3>\r\n            <p className='text-muted-foreground mt-1 text-sm'>\r\n              {searchTerm\r\n                ? 'Try adjusting your search terms.'\r\n                : 'Get started by creating a new class.'}\r\n            </p>\r\n            {!searchTerm && (\r\n              <div className='mt-6'>\r\n                <Link href='/dashboard/teacher/classes/new'>\r\n                  <Button>\r\n                    <Plus className='mr-2 h-4 w-4' />\r\n                    Create Class\r\n                  </Button>\r\n                </Link>\r\n              </div>\r\n            )}\r\n          </div>\r\n        ) : (\r\n          filteredClasses.map((classItem) => (\r\n            <Card key={classItem.id} className='relative overflow-hidden animate-fade-in w-full'>\r\n              <CardHeader className='p-0'>\r\n                {/* Cover Image Section */}\r\n                <div className=\"p-6 pb-0\">\r\n                  <div className=\"h-48 w-full overflow-hidden rounded-lg\">\r\n                    {classItem.coverPicture ? (\r\n                      <img\r\n                        src={classItem.coverPicture}\r\n                        alt={`Cover for ${classItem.name}`}\r\n                        loading='lazy'\r\n                        className='h-full w-full object-cover'\r\n                      />\r\n                    ) : (\r\n                      <div className='h-full w-full bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center'>\r\n                        <ImageIcon className='h-16 w-16 text-gray-400' />\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n                {/* Text and Dropdown Menu */}\r\n                <div className='p-6'>\r\n                  <div className='flex items-start justify-between'>\r\n                    <div className='space-y-1 flex-1'>\r\n                      <CardTitle className='text-xl'>{classItem.name}</CardTitle>\r\n                      <CardDescription className='text-sm'>\r\n                        {classItem.description.substring(0, 100)}{classItem.description.length > 100 ? '...' : ''}\r\n                      </CardDescription>\r\n                    </div>\r\n                    <DropdownMenu>\r\n                      <DropdownMenuTrigger asChild>\r\n                        <Button variant='ghost' className='h-8 w-8 p-0'>\r\n                          <MoreHorizontal className='h-4 w-4' />\r\n                        </Button>\r\n                      </DropdownMenuTrigger>\r\n                      <DropdownMenuContent align='end'>\r\n                        <DropdownMenuItem asChild>\r\n                          <Link href={`/dashboard/teacher/classes/${classItem.id}`}>\r\n                            <Edit className='mr-2 h-4 w-4' />\r\n                            Edit\r\n                          </Link>\r\n                        </DropdownMenuItem>\r\n                        <DropdownMenuItem asChild>\r\n                          <Link href={`/dashboard/teacher/classes/${classItem.id}/students`}>\r\n                            <Users className='mr-2 h-4 w-4' />\r\n                            Manage Students\r\n                          </Link>\r\n                        </DropdownMenuItem>\r\n                        <DropdownMenuItem\r\n                          className='text-red-600'\r\n                          onClick={() => handleDeleteClass(classItem.id)}\r\n                          disabled={isDeleting === classItem.id}\r\n                        >\r\n                          {isDeleting === classItem.id ? (\r\n                            <Loader2 className='mr-2 h-4 w-4 animate-spin' />\r\n                          ) : (\r\n                            <Trash2 className='mr-2 h-4 w-4' />\r\n                          )}\r\n                          {isDeleting === classItem.id ? 'Deleting...' : 'Delete'}\r\n                        </DropdownMenuItem>\r\n                      </DropdownMenuContent>\r\n                    </DropdownMenu>\r\n                  </div>\r\n                </div>\r\n              </CardHeader>\r\n              <CardContent className='px-6 pt-5 pb-4 space-y-2 border-t'>\r\n                <div className='flex items-center justify-between'>\r\n                  <div className='flex items-center space-x-2'>\r\n                    <Users className='text-muted-foreground h-4 w-4' />\r\n                    <span className='text-sm font-medium'>{classItem.studentCount}</span>\r\n                    <span className='text-muted-foreground text-sm'>Students</span>\r\n                  </div>\r\n                  <div className='flex items-center space-x-2'>\r\n                    <BookOpen className='text-muted-foreground h-4 w-4' />\r\n                    <span className='text-sm font-medium'>{classItem.courseCount}</span>\r\n                    <span className='text-muted-foreground text-sm'>Courses</span>\r\n                  </div>\r\n                </div>\r\n              </CardContent>\r\n              <CardFooter className='flex justify-end'>\r\n                <Link href={`/dashboard/teacher/classes/${classItem.id}`}>\r\n                  <Button>View Class</Button>\r\n                </Link>\r\n              </CardFooter>\r\n            </Card>\r\n          ))\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}", "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\classes\\[id]\\courses\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\classes\\[id]\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\classes\\[id]\\students\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\courses\\generate\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\courses\\new\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\courses\\page.tsx", ["1506"], [], "'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardDescription,\r\n  CardHeader,\r\n  CardTitle\r\n} from '@/components/ui/card';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Skeleton } from '@/components/ui/skeleton';\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger\r\n} from '@/components/ui/dropdown-menu';\r\nimport {\r\n  BookOpen,\r\n  Plus,\r\n  Search,\r\n  MoreHorizontal,\r\n  Edit,\r\n  Trash2,\r\n  Users,\r\n  Bot,\r\n  Copy\r\n} from 'lucide-react';\r\nimport Link from 'next/link';\r\nimport { authStorage } from '@/lib/auth';\r\nimport { toast } from 'sonner';\r\n\r\ninterface Course {\r\n  id: number;\r\n  name: string;\r\n  description: string;\r\n  type: string;\r\n  courseCode: string;\r\n  moduleCount: number;\r\n  studentCount: number;\r\n  status: string;\r\n  createdAt: string;\r\n  startDate: string;\r\n  endDate: string;\r\n  coverPicture?: string;\r\n}\r\n\r\nexport default function CoursesPage() {\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [courses, setCourses] = useState<Course[]>([]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [isDeletingCourse, setIsDeletingCourse] = useState<number | null>(null);\r\n\r\n  useEffect(() => {\r\n    fetchCourses();\r\n  }, []);\r\n\r\n  const fetchCourses = async () => {\r\n    try {\r\n      const user = authStorage.getUser();\r\n      if (!user) {\r\n        toast.error('Please log in to view courses');\r\n        return;\r\n      }\r\n\r\n      const response = await fetch(`/api/courses?teacherId=${user.id}`);\r\n      const data = await response.json();\r\n\r\n      if (data.success) {\r\n        setCourses(data.courses || []);\r\n      } else {\r\n        toast.error(data.error || 'Failed to fetch courses');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching courses:', error);\r\n      toast.error('Failed to fetch courses');\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleDeleteCourse = async (courseId: number) => {\r\n    if (!confirm('Are you sure you want to delete this course? This action cannot be undone.')) {\r\n      return;\r\n    }\r\n\r\n    setIsDeletingCourse(courseId);\r\n    try {\r\n      const user = authStorage.getUser();\r\n      if (!user) {\r\n        toast.error('Please log in to delete courses');\r\n        return;\r\n      }\r\n\r\n      const response = await fetch(`/api/courses/${courseId}?teacherId=${user.id}`, {\r\n        method: 'DELETE'\r\n      });\r\n      const data = await response.json();\r\n\r\n      if (data.success) {\r\n        toast.success('Course deleted successfully!');\r\n        fetchCourses();\r\n      } else {\r\n        toast.error(data.error || 'Failed to delete course');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error deleting course:', error);\r\n      toast.error('Failed to delete course');\r\n    } finally {\r\n      setIsDeletingCourse(null);\r\n    }\r\n  };\r\n\r\n  const filteredCourses = courses.filter(\r\n    (course) =>\r\n      course.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n      course.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n      course.courseCode.toLowerCase().includes(searchTerm.toLowerCase())\r\n  );\r\n\r\n  const copyToClipboard = (text: string) => {\r\n    navigator.clipboard.writeText(text);\r\n    toast.success('Course code copied to clipboard!');\r\n  };\r\n\r\n  const LoadingSkeleton = () => (\r\n    <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>\r\n      {[...Array(6)].map((_, i) => (\r\n        <Card key={i} className='overflow-hidden'>\r\n          <div className='aspect-video bg-muted'>\r\n            <Skeleton className='w-full h-full' />\r\n          </div>\r\n          <CardContent className='p-4'>\r\n            <Skeleton className='h-6 w-3/4 mb-2' />\r\n            <Skeleton className='h-4 w-full mb-2' />\r\n            <Skeleton className='h-4 w-2/3 mb-4' />\r\n            <div className='flex items-center justify-between'>\r\n              <div className='flex space-x-4'>\r\n                <Skeleton className='h-4 w-16' />\r\n                <Skeleton className='h-4 w-16' />\r\n              </div>\r\n              <Skeleton className='h-8 w-8 rounded-full' />\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n      ))}\r\n    </div>\r\n  );\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <div className='space-y-6'>\r\n        <div className='flex items-center justify-between'>\r\n          <div>\r\n            <h1 className='text-3xl font-bold tracking-tight'>My Courses</h1>\r\n            <p className='text-muted-foreground'>\r\n              Create and manage your educational courses\r\n            </p>\r\n          </div>\r\n          <div className='flex space-x-2'>\r\n            <Link href='/dashboard/teacher/courses/new'>\r\n              <Button>\r\n                <Plus className='mr-2 h-4 w-4' />\r\n                Create Course\r\n              </Button>\r\n            </Link>\r\n          </div>\r\n        </div>\r\n        <LoadingSkeleton />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className='space-y-6'>\r\n      <div className='flex items-center justify-between'>\r\n        <div>\r\n          <h1 className='text-3xl font-bold tracking-tight'>My Courses</h1>\r\n          <p className='text-muted-foreground'>\r\n            Create and manage your educational courses\r\n          </p>\r\n        </div>\r\n        <div className='flex space-x-2'>\r\n          <Link href='/dashboard/teacher/courses/generate'>\r\n            <Button variant='outline'>\r\n              <Bot className='mr-2 h-4 w-4' />\r\n              AI Generator\r\n            </Button>\r\n          </Link>\r\n          <Link href='/dashboard/teacher/courses/new'>\r\n            <Button>\r\n              <Plus className='mr-2 h-4 w-4' />\r\n              Create Course\r\n            </Button>\r\n          </Link>\r\n        </div>\r\n      </div>\r\n\r\n      <div className='space-y-6'>\r\n        <div className='flex items-center space-x-2'>\r\n          <div className='relative flex-1'>\r\n            <Search className='text-muted-foreground absolute top-2.5 left-2 h-4 w-4' />\r\n            <Input\r\n              placeholder='Search courses...'\r\n              value={searchTerm}\r\n              onChange={(e) => setSearchTerm(e.target.value)}\r\n              className='pl-8'\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>\r\n          {filteredCourses.map((course) => (\r\n            <Card key={course.id} className='overflow-hidden hover:shadow-lg transition-shadow'>\r\n              <CardHeader className='p-0'>\r\n                {/* Cover Image Section */}\r\n                <div className=\"p-6 pb-0\">\r\n                  <div className=\"h-48 w-full overflow-hidden rounded-lg relative\">\r\n                    {course.coverPicture ? (\r\n                      <img\r\n                        src={course.coverPicture}\r\n                        alt={course.name}\r\n                        loading='lazy'\r\n                        className='h-full w-full object-cover'\r\n                      />\r\n                    ) : (\r\n                      <div className='h-full w-full bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center'>\r\n                        <BookOpen className='h-12 w-12 text-gray-400' />\r\n                      </div>\r\n                    )}\r\n                    <div className='absolute top-2 right-2'>\r\n                      <Badge\r\n                        variant={\r\n                          course.status === 'published' ? 'default' : 'outline'\r\n                        }\r\n                      >\r\n                        {course.status}\r\n                      </Badge>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </CardHeader>\r\n              <CardContent className='p-6 pt-4'>\r\n                <div className='space-y-3'>\r\n                  <div>\r\n                    <h3 className='font-semibold text-lg line-clamp-1'>{course.name}</h3>\r\n                    <p className='text-muted-foreground text-sm line-clamp-2 mt-1'>\r\n                      {course.description}\r\n                    </p>\r\n                  </div>\r\n                  \r\n                  <div className='flex items-center justify-between text-sm'>\r\n                    <div className='flex items-center space-x-2'>\r\n                      <code className='bg-muted rounded px-2 py-1 text-xs'>\r\n                        {course.courseCode}\r\n                      </code>\r\n                      <Button\r\n                        variant='ghost'\r\n                        size='sm'\r\n                        onClick={() => copyToClipboard(course.courseCode)}\r\n                        className='h-6 w-6 p-0'\r\n                      >\r\n                        <Copy className='h-3 w-3' />\r\n                      </Button>\r\n                    </div>\r\n                    <Badge\r\n                      variant={\r\n                        course.type === 'verified' ? 'default' : 'secondary'\r\n                      }\r\n                    >\r\n                      {course.type}\r\n                    </Badge>\r\n                  </div>\r\n                  \r\n                  <div className='flex items-center justify-between text-sm text-muted-foreground'>\r\n                    <div className='flex items-center space-x-1'>\r\n                      <BookOpen className='h-4 w-4' />\r\n                      <span>{course.moduleCount} modules</span>\r\n                    </div>\r\n                    <div className='flex items-center space-x-1'>\r\n                      <Users className='h-4 w-4' />\r\n                      <span>{course.studentCount} students</span>\r\n                    </div>\r\n                  </div>\r\n                  \r\n                  <div className='flex items-center justify-between pt-2'>\r\n                    <div className='flex space-x-1'>\r\n                      <Link href={`/dashboard/teacher/courses/${course.id}`}>\r\n                        <Button variant='outline' size='sm'>\r\n                          <Edit className='h-3 w-3 mr-1' />\r\n                          Edit\r\n                        </Button>\r\n                      </Link>\r\n                    </div>\r\n                    <DropdownMenu>\r\n                      <DropdownMenuTrigger asChild>\r\n                        <Button variant='ghost' size='sm' className='h-8 w-8 p-0'>\r\n                          <MoreHorizontal className='h-4 w-4' />\r\n                        </Button>\r\n                      </DropdownMenuTrigger>\r\n                      <DropdownMenuContent align='end'>\r\n                        <DropdownMenuItem asChild>\r\n                          <Link\r\n                            href={`/dashboard/teacher/courses/${course.id}/students`}\r\n                          >\r\n                            <Users className='mr-2 h-4 w-4' />\r\n                            View Students\r\n                          </Link>\r\n                        </DropdownMenuItem>\r\n                        <DropdownMenuItem \r\n                          className='text-red-600'\r\n                          onClick={() => handleDeleteCourse(course.id)}\r\n                          disabled={isDeletingCourse === course.id}\r\n                        >\r\n                          {isDeletingCourse === course.id ? (\r\n                            <div className='mr-2 h-4 w-4 animate-spin rounded-full border-2 border-red-600 border-t-transparent' />\r\n                          ) : (\r\n                            <Trash2 className='mr-2 h-4 w-4' />\r\n                          )}\r\n                          {isDeletingCourse === course.id ? 'Deleting...' : 'Delete'}\r\n                        </DropdownMenuItem>\r\n                      </DropdownMenuContent>\r\n                    </DropdownMenu>\r\n                  </div>\r\n                </div>\r\n              </CardContent>\r\n            </Card>\r\n          ))}\r\n        </div>\r\n\r\n        {filteredCourses.length === 0 && (\r\n          <div className='py-16 text-center'>\r\n            <BookOpen className='text-muted-foreground mx-auto h-16 w-16' />\r\n            <h3 className='mt-4 text-lg font-semibold'>No courses found</h3>\r\n            <p className='text-muted-foreground mt-2 text-sm max-w-sm mx-auto'>\r\n              {searchTerm\r\n                ? 'Try adjusting your search terms to find the courses you\\'re looking for.'\r\n                : 'Get started by creating your first course using our intuitive wizard or AI generator.'}\r\n            </p>\r\n            {!searchTerm && (\r\n              <div className='mt-8 flex justify-center space-x-3'>\r\n                <Link href='/dashboard/teacher/courses/generate'>\r\n                  <Button variant='outline' size='lg'>\r\n                    <Bot className='mr-2 h-4 w-4' />\r\n                    AI Generator\r\n                  </Button>\r\n                </Link>\r\n                <Link href='/dashboard/teacher/courses/new'>\r\n                  <Button size='lg'>\r\n                    <Plus className='mr-2 h-4 w-4' />\r\n                    Create Course\r\n                  </Button>\r\n                </Link>\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n", "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\courses\\[id]\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\reports\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\global-error.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\not-found.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\active-theme.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\breadcrumbs.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\course\\course-creation-wizard.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\course\\steps\\academics-step.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\course\\steps\\admissions-step.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\course\\steps\\basic-info-step.tsx", ["1507"], [], "'use client';\r\n\r\nimport React, { useState, useRef } from 'react';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Textarea } from '@/components/ui/textarea';\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Calendar } from '@/components/ui/calendar';\r\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\r\nimport { Checkbox } from '@/components/ui/checkbox';\r\nimport { format } from 'date-fns';\r\nimport { id } from 'date-fns/locale';\r\nimport { CalendarIcon, Upload, X, Shuffle, Info } from 'lucide-react';\r\nimport { cn } from '@/lib/utils';\r\nimport { CourseData } from '../course-creation-wizard';\r\nimport { toast } from 'sonner';\r\n\r\ninterface BasicInfoStepProps {\r\n  data: CourseData;\r\n  onUpdate: (updates: Partial<CourseData>) => void;\r\n}\r\n\r\nexport function BasicInfoStep({ data, onUpdate }: BasicInfoStepProps) {\r\n  const [isGeneratingCode, setIsGeneratingCode] = useState(false);\r\n  const [dateRangeEnabled, setDateRangeEnabled] = useState(Boolean(data.startDate || data.endDate));\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n\r\n  const generateCourseCode = () => {\r\n    setIsGeneratingCode(true);\r\n    // Simulate API call\r\n    setTimeout(() => {\r\n      const code = Math.random().toString(36).substring(2, 8).toUpperCase();\r\n      onUpdate({ courseCode: code });\r\n      setIsGeneratingCode(false);\r\n      toast.success('Kode course berhasil dibuat');\r\n    }, 1000);\r\n  };\r\n\r\n  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n    const file = event.target.files?.[0];\r\n    if (!file) return;\r\n\r\n    // Validate file type\r\n    if (!file.type.startsWith('image/')) {\r\n      toast.error('File harus berupa gambar');\r\n      return;\r\n    }\r\n\r\n    // Validate file size (max 5MB)\r\n    if (file.size > 5 * 1024 * 1024) {\r\n      toast.error('Ukuran file maksimal 5MB');\r\n      return;\r\n    }\r\n\r\n    // Create preview URL\r\n    const previewUrl = URL.createObjectURL(file);\r\n    onUpdate({ \r\n      coverImage: file, \r\n      coverImagePreview: previewUrl \r\n    });\r\n    toast.success('Gambar berhasil diunggah');\r\n  };\r\n\r\n  const removeCoverImage = () => {\r\n    if (data.coverImagePreview) {\r\n      URL.revokeObjectURL(data.coverImagePreview);\r\n    }\r\n    onUpdate({ \r\n      coverImage: undefined, \r\n      coverImagePreview: undefined \r\n    });\r\n  };\r\n\r\n  const handleEnrollmentTypeChange = (value: 'code' | 'invitation' | 'both' | 'purchase') => {\r\n    const updates: Partial<CourseData> = { enrollmentType: value };\r\n    \r\n    // Auto-set default currency when switching to purchase/both\r\n    if ((value === 'purchase' || value === 'both') && !data.currency) {\r\n      updates.currency = 'IDR';\r\n    }\r\n    \r\n    onUpdate(updates);\r\n  };\r\n\r\n  const handleDateRangeToggle = (checked: boolean) => {\r\n    setDateRangeEnabled(checked);\r\n    if (!checked) {\r\n      onUpdate({ startDate: null, endDate: null });\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Course Name */}\r\n      <div className=\"space-y-2\">\r\n        <Label htmlFor=\"courseName\">Nama Course *</Label>\r\n        <Input\r\n          id=\"courseName\"\r\n          placeholder=\"Masukkan nama course\"\r\n          value={data.name}\r\n          onChange={(e) => onUpdate({ name: e.target.value })}\r\n        />\r\n      </div>\r\n\r\n      {/* Instructor */}\r\n      <div className=\"space-y-2\">\r\n        <Label htmlFor=\"instructor\">Nama Instruktur *</Label>\r\n        <Input\r\n          id=\"instructor\"\r\n          placeholder=\"Masukkan nama instruktur\"\r\n          value={data.instructor}\r\n          onChange={(e) => onUpdate({ instructor: e.target.value })}\r\n        />\r\n      </div>\r\n\r\n      {/* Course Code */}\r\n      <div className=\"space-y-2\">\r\n        <Label htmlFor=\"courseCode\">Kode Course *</Label>\r\n        <div className=\"flex space-x-2\">\r\n          <Input\r\n            id=\"courseCode\"\r\n            placeholder=\"Kode unik untuk course\"\r\n            value={data.courseCode}\r\n            onChange={(e) => onUpdate({ courseCode: e.target.value.toUpperCase() })}\r\n            className=\"flex-1\"\r\n          />\r\n          <Button \r\n            type=\"button\" \r\n            variant=\"outline\" \r\n            onClick={generateCourseCode}\r\n            disabled={isGeneratingCode}\r\n          >\r\n            <Shuffle className=\"w-4 h-4 mr-2\" />\r\n            {isGeneratingCode ? 'Membuat...' : 'Generate'}\r\n          </Button>\r\n        </div>\r\n        <p className=\"text-sm text-muted-foreground\">\r\n          Kode ini akan digunakan siswa untuk mendaftar ke course\r\n        </p>\r\n      </div>\r\n\r\n      {/* Description */}\r\n      <div className=\"space-y-2\">\r\n        <Label htmlFor=\"description\">Deskripsi Course *</Label>\r\n        <Textarea\r\n          id=\"description\"\r\n          placeholder=\"Jelaskan tentang course ini...\"\r\n          value={data.description}\r\n          onChange={(e) => onUpdate({ description: e.target.value })}\r\n          rows={4}\r\n        />\r\n      </div>\r\n\r\n      {/* Cover Image */}\r\n      <div className=\"space-y-2\">\r\n        <Label>Cover Image</Label>\r\n        {data.coverImagePreview ? (\r\n          <div className=\"relative\">\r\n            <img\r\n              src={data.coverImagePreview}\r\n              alt=\"Course cover\"\r\n              className=\"w-full h-auto object-cover rounded-md aspect-video\"\r\n            />\r\n            <Button\r\n              type=\"button\"\r\n              variant=\"destructive\"\r\n              size=\"sm\"\r\n              className=\"absolute top-2 right-2\"\r\n              onClick={removeCoverImage}\r\n            >\r\n              <X className=\"w-4 h-4\" />\r\n            </Button>\r\n          </div>\r\n        ) : (\r\n          <div\r\n            className=\"border-2 border-dashed border-muted-foreground/25 rounded-md p-6 text-center cursor-pointer hover:border-muted-foreground/50 transition-colors aspect-video flex flex-col items-center justify-center\"\r\n            onClick={() => fileInputRef.current?.click()}\r\n          >\r\n            <Upload className=\"w-8 h-8 mx-auto mb-2 text-muted-foreground\" />\r\n            <p className=\"text-sm text-muted-foreground\">\r\n              Klik untuk upload cover image\r\n            </p>\r\n            <p className=\"text-xs text-muted-foreground mt-1\">\r\n              PNG, JPG hingga 5MB\r\n            </p>\r\n          </div>\r\n        )}\r\n        <input\r\n          ref={fileInputRef}\r\n          type=\"file\"\r\n          accept=\"image/*\"\r\n          onChange={handleImageUpload}\r\n          className=\"hidden\"\r\n        />\r\n      </div>\r\n\r\n      {/* Course Type */}\r\n      <div className=\"space-y-2\">\r\n        <div className=\"flex items-center space-x-2\">\r\n          <Label>Tipe Course *</Label>\r\n          <Popover>\r\n            <PopoverTrigger asChild>\r\n              <Button variant=\"ghost\" size=\"sm\" className=\"h-auto p-1\">\r\n                <Info className=\"h-4 w-4 text-muted-foreground\" />\r\n              </Button>\r\n            </PopoverTrigger>\r\n            <PopoverContent className=\"w-96\" align=\"start\">\r\n              <div className=\"space-y-4\">\r\n                <h4 className=\"font-medium text-sm\">Informasi Tipe Course</h4>\r\n                <div className=\"grid grid-cols-1 gap-4\">\r\n                  <div className=\"space-y-2\">\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <Badge variant=\"secondary\">Self-paced</Badge>\r\n                    </div>\r\n                    <ul className=\"text-xs text-muted-foreground space-y-1\">\r\n                      <li>• Siswa belajar dengan kecepatan sendiri</li>\r\n                      <li>• Tidak ada deadline ketat</li>\r\n                      <li>• Akses selamanya setelah enrollment</li>\r\n                      <li>• Cocok untuk pembelajaran mandiri</li>\r\n                    </ul>\r\n                  </div>\r\n                  <div className=\"space-y-2\">\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <Badge variant=\"default\">Verified</Badge>\r\n                    </div>\r\n                    <ul className=\"text-xs text-muted-foreground space-y-1\">\r\n                      <li>• Course dengan jadwal dan deadline</li>\r\n                      <li>• Sertifikat resmi setelah selesai</li>\r\n                      <li>• Monitoring progress lebih ketat</li>\r\n                      <li>• Cocok untuk pembelajaran formal</li>\r\n                    </ul>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </PopoverContent>\r\n          </Popover>\r\n        </div>\r\n        <Select\r\n          value={data.type}\r\n          onValueChange={(value: 'self_paced' | 'verified') => onUpdate({ type: value })}\r\n        >\r\n          <SelectTrigger>\r\n            <SelectValue />\r\n          </SelectTrigger>\r\n          <SelectContent>\r\n            <SelectItem value=\"self_paced\">\r\n              <div className=\"flex items-center space-x-2\">\r\n                <Badge variant=\"secondary\">Self-paced</Badge>\r\n                <span>Siswa belajar dengan kecepatan sendiri</span>\r\n              </div>\r\n            </SelectItem>\r\n            <SelectItem value=\"verified\">\r\n              <div className=\"flex items-center space-x-2\">\r\n                <Badge variant=\"default\">Verified</Badge>\r\n                <span>Course dengan jadwal dan deadline</span>\r\n              </div>\r\n            </SelectItem>\r\n          </SelectContent>\r\n        </Select>\r\n      </div>\r\n\r\n      {/* Enrollment Type */}\r\n      <div className=\"space-y-2\">\r\n        <Label>Tipe Pendaftaran *</Label>\r\n        <Select\r\n          value={data.enrollmentType}\r\n          onValueChange={handleEnrollmentTypeChange}\r\n        >\r\n          <SelectTrigger>\r\n            <SelectValue />\r\n          </SelectTrigger>\r\n          <SelectContent>\r\n            <SelectItem value=\"code\">\r\n              <div className=\"flex items-center space-x-2\">\r\n                <Badge variant=\"outline\">Kode</Badge>\r\n                <span>Siswa mendaftar dengan kode</span>\r\n              </div>\r\n            </SelectItem>\r\n            <SelectItem value=\"invitation\">\r\n              <div className=\"flex items-center space-x-2\">\r\n                <Badge variant=\"outline\">Undangan</Badge>\r\n                <span>Hanya dengan undangan</span>\r\n              </div>\r\n            </SelectItem>\r\n            <SelectItem value=\"both\">\r\n              <div className=\"flex items-center space-x-2\">\r\n                <Badge variant=\"outline\">Keduanya</Badge>\r\n                <span>Kode atau undangan</span>\r\n              </div>\r\n            </SelectItem>\r\n            <SelectItem value=\"purchase\">\r\n              <div className=\"flex items-center space-x-2\">\r\n                <Badge variant=\"default\">Berbayar</Badge>\r\n                <span>Siswa harus membeli</span>\r\n              </div>\r\n            </SelectItem>\r\n          </SelectContent>\r\n        </Select>\r\n      </div>\r\n\r\n      {/* Price and Currency (only for purchase/both enrollment type) */}\r\n      {(data.enrollmentType === 'purchase' || data.enrollmentType === 'both') && (\r\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n          <div className=\"space-y-2 md:col-span-2\">\r\n            <Label htmlFor=\"price\">Harga *</Label>\r\n            <Input\r\n              id=\"price\"\r\n              type=\"number\"\r\n              placeholder=\"0\"\r\n              value={data.price || ''}\r\n              onChange={(e) => onUpdate({ price: parseFloat(e.target.value) || 0 })}\r\n              min=\"0\"\r\n              step=\"1000\"\r\n            />\r\n          </div>\r\n          <div className=\"space-y-2\">\r\n            <Label>Mata Uang *</Label>\r\n            <Select\r\n              value={data.currency || 'IDR'}\r\n              onValueChange={(value) => onUpdate({ currency: value })}\r\n            >\r\n              <SelectTrigger>\r\n                <SelectValue />\r\n              </SelectTrigger>\r\n              <SelectContent>\r\n                <SelectItem value=\"IDR\">IDR (Rupiah)</SelectItem>\r\n                <SelectItem value=\"USD\">USD (Dollar)</SelectItem>\r\n                <SelectItem value=\"EUR\">EUR (Euro)</SelectItem>\r\n              </SelectContent>\r\n            </Select>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Date Range */}\r\n      <div className=\"space-y-2\">\r\n        <div className=\"flex items-center space-x-2\">\r\n          <Checkbox\r\n            id=\"enableDateRange\"\r\n            checked={dateRangeEnabled}\r\n            onCheckedChange={handleDateRangeToggle}\r\n          />\r\n          <Label htmlFor=\"enableDateRange\">Atur Tanggal Mulai & Selesai</Label>\r\n        </div>\r\n        {dateRangeEnabled && (\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n            <div className=\"space-y-2\">\r\n              <Label>Tanggal Mulai</Label>\r\n              <Popover>\r\n                <PopoverTrigger asChild>\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    className={cn(\r\n                      \"w-full justify-start text-left font-normal\",\r\n                      !data.startDate && \"text-muted-foreground\"\r\n                    )}\r\n                  >\r\n                    <CalendarIcon className=\"mr-2 h-4 w-4\" />\r\n                    {data.startDate ? (\r\n                      format(data.startDate, \"PPP\", { locale: id })\r\n                    ) : (\r\n                      \"Pilih tanggal mulai\"\r\n                    )}\r\n                  </Button>\r\n                </PopoverTrigger>\r\n                <PopoverContent className=\"w-auto p-0\" align=\"start\">\r\n                  <Calendar\r\n                    mode=\"single\"\r\n                    selected={data.startDate || undefined}\r\n                    onSelect={(date) => onUpdate({ startDate: date })}\r\n                    disabled={(date) => date < new Date()}\r\n                    initialFocus\r\n                  />\r\n                </PopoverContent>\r\n              </Popover>\r\n            </div>\r\n\r\n            <div className=\"space-y-2\">\r\n              <Label>Tanggal Selesai</Label>\r\n              <Popover>\r\n                <PopoverTrigger asChild>\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    className={cn(\r\n                      \"w-full justify-start text-left font-normal\",\r\n                      !data.endDate && \"text-muted-foreground\"\r\n                    )}\r\n                  >\r\n                    <CalendarIcon className=\"mr-2 h-4 w-4\" />\r\n                    {data.endDate ? (\r\n                      format(data.endDate, \"PPP\", { locale: id })\r\n                    ) : (\r\n                      \"Pilih tanggal selesai\"\r\n                    )}\r\n                  </Button>\r\n                </PopoverTrigger>\r\n                <PopoverContent className=\"w-auto p-0\" align=\"start\">\r\n                  <Calendar\r\n                    mode=\"single\"\r\n                    selected={data.endDate || undefined}\r\n                    onSelect={(date) => onUpdate({ endDate: date })}\r\n                    disabled={(date) => Boolean(date < new Date() || (data.startDate && date <= data.startDate))}\r\n                    initialFocus\r\n                  />\r\n                </PopoverContent>\r\n              </Popover>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n\r\n    </div>\r\n  );\r\n}", "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\course\\steps\\careers-step.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\course\\steps\\content-creation-step.tsx", ["1508", "1509", "1510", "1511"], [], "'use client';\r\n\r\nimport React, { useState, useRef, useEffect } from 'react';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Textarea } from '@/components/ui/textarea'; // Keep for other uses\r\nimport { DynamicContentEditor, ContentBlock } from '@/components/dynamic-content-editor';\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Checkbox } from '@/components/ui/checkbox';\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\r\nimport { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';\r\nimport { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';\r\nimport { \r\n  FileText, \r\n  HelpCircle, \r\n  Plus, \r\n  Edit, \r\n  Trash2, \r\n  Eye, \r\n  Save,\r\n  BookOpen,\r\n  CheckCircle,\r\n  Clock,\r\n  Type,\r\n  Image,\r\n  Video,\r\n  FileIcon,\r\n  Navigation\r\n} from 'lucide-react';\r\nimport { cn } from '@/lib/utils';\r\nimport { CourseData, ModuleData, ChapterData, QuizData, QuestionData } from '../course-creation-wizard';\r\nimport { toast } from 'sonner';\r\nimport ReactMarkdown from 'react-markdown';\r\nimport remarkGfm from 'remark-gfm';\r\n\r\ninterface ContentCreationStepProps {\r\n  data: CourseData;\r\n  onUpdate: (updates: Partial<CourseData>) => void;\r\n}\r\n\r\nexport function ContentCreationStep({ data, onUpdate }: ContentCreationStepProps) {\r\n  const [selectedModule, setSelectedModule] = useState<string>(data.modules[0]?.id || '');\r\n  const [selectedChapter, setSelectedChapter] = useState<string>('');\r\n  const [editingQuiz, setEditingQuiz] = useState<{ type: 'chapter' | 'module' | 'final'; quiz: QuizData | null }>({\r\n    type: 'chapter',\r\n    quiz: null\r\n  });\r\n  const [isQuizDialogOpen, setIsQuizDialogOpen] = useState(false);\r\n  const [editingQuestion, setEditingQuestion] = useState<QuestionData | null>(null);\r\n  const [isQuestionDialogOpen, setIsQuestionDialogOpen] = useState(false);\r\n  const [previewMode, setPreviewMode] = useState(false);\r\n  \r\n  // Add ref for content scrolling\r\n  const contentRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});\r\n\r\n  const currentModule = data.modules.find(m => m.id === selectedModule);\r\n  const currentChapter = currentModule?.chapters.find(c => c.id === selectedChapter);\r\n\r\n  // Function to scroll to specific content block\r\n  const scrollToContent = (blockId: string) => {\r\n    const element = contentRefs.current[blockId];\r\n    if (element) {\r\n      element.scrollIntoView({ \r\n        behavior: 'smooth', \r\n        block: 'start',\r\n        inline: 'nearest'\r\n      });\r\n    }\r\n  };\r\n\r\n  // Function to get content type icon\r\n  const getContentTypeIcon = (type: string) => {\r\n    switch (type) {\r\n      case 'text':\r\n        return <Type className=\"w-3 h-3\" />;\r\n      case 'image':\r\n        return <Image className=\"w-3 h-3\" />;\r\n      case 'video':\r\n        return <Video className=\"w-3 h-3\" />;\r\n      case 'pdf':\r\n        return <FileText className=\"w-3 h-3\" />;\r\n      case 'zoom-recording':\r\n        return <Video className=\"w-3 h-3\" />;\r\n      default:\r\n        return <FileIcon className=\"w-3 h-3\" />;\r\n    }\r\n  };\r\n\r\n  // Function to get short content preview\r\n  const getContentPreview = (block: ContentBlock) => {\r\n    if (block.type === 'text') {\r\n      return block.value?.slice(0, 30) + (block.value && block.value.length > 30 ? '...' : '') || 'Empty text';\r\n    }\r\n    return block.type.charAt(0).toUpperCase() + block.type.slice(1);\r\n  };\r\n\r\n  const updateChapterContent = (content: ContentBlock[]) => {\r\n    if (!currentModule || !currentChapter) return;\r\n\r\n    const updatedModules = data.modules.map(module => {\r\n      if (module.id === selectedModule) {\r\n        const updatedChapters = module.chapters.map(chapter => {\r\n          if (chapter.id === selectedChapter) {\r\n            return { ...chapter, content };\r\n          }\r\n          return chapter;\r\n        });\r\n        return { ...module, chapters: updatedChapters };\r\n      }\r\n      return module;\r\n    });\r\n\r\n    onUpdate({ modules: updatedModules });\r\n  };\r\n\r\n  const createQuiz = (type: 'chapter' | 'module' | 'final') => {\r\n    const newQuiz: QuizData = {\r\n      id: `quiz-${Date.now()}`,\r\n      name: type === 'chapter' ? `Quiz ${currentChapter?.name}` : \r\n            type === 'module' ? `Quiz ${currentModule?.name}` : \r\n            `Final Exam - ${data.name}`,\r\n      description: '',\r\n      questions: [],\r\n      minimumScore: 70,\r\n      timeLimit: type === 'final' ? 120 : undefined // Default 2 hours for final exam\r\n    };\r\n    \r\n    setEditingQuiz({ type, quiz: newQuiz });\r\n    setIsQuizDialogOpen(true);\r\n  };\r\n\r\n  const editQuiz = (type: 'chapter' | 'module' | 'final', quiz: QuizData) => {\r\n    setEditingQuiz({ type, quiz: { ...quiz } });\r\n    setIsQuizDialogOpen(true);\r\n  };\r\n\r\n  const saveQuiz = () => {\r\n    if (!editingQuiz.quiz || !editingQuiz.quiz.name.trim()) {\r\n      toast.error('Nama quiz harus diisi');\r\n      return;\r\n    }\r\n\r\n    if (editingQuiz.type === 'final') {\r\n      onUpdate({ finalExam: editingQuiz.quiz! });\r\n    } else {\r\n      const updatedModules = data.modules.map(module => {\r\n        if (module.id === selectedModule) {\r\n          if (editingQuiz.type === 'module') {\r\n            return { ...module, moduleQuiz: editingQuiz.quiz! };\r\n          } else {\r\n            const updatedChapters = module.chapters.map(chapter => {\r\n              if (chapter.id === selectedChapter) {\r\n                return { ...chapter, chapterQuiz: editingQuiz.quiz! };\r\n              }\r\n              return chapter;\r\n            });\r\n            return { ...module, chapters: updatedChapters };\r\n          }\r\n        }\r\n        return module;\r\n      });\r\n      onUpdate({ modules: updatedModules });\r\n    }\r\n\r\n    setIsQuizDialogOpen(false);\r\n    setEditingQuiz({ type: 'chapter', quiz: null });\r\n    toast.success('Quiz berhasil disimpan');\r\n  };\r\n\r\n  const createQuestion = () => {\r\n    const newQuestion: QuestionData = {\r\n      id: editingQuestion?.id || `question-${Date.now()}`,\r\n      type: 'multiple_choice',\r\n      question: [{ type: 'text', value: '' }],\r\n      options: editingQuestion?.type === 'true_false'\r\n        ? [\r\n            { content: [{ type: 'text', value: 'True' }], isCorrect: false },\r\n            { content: [{ type: 'text', value: 'False' }], isCorrect: false }\r\n          ]\r\n        : [\r\n            { content: [{ type: 'text', value: '' }], isCorrect: false },\r\n            { content: [{ type: 'text', value: '' }], isCorrect: false },\r\n            { content: [{ type: 'text', value: '' }], isCorrect: false },\r\n            { content: [{ type: 'text', value: '' }], isCorrect: false }\r\n          ],\r\n      essayAnswer: '',\r\n      explanation: [],\r\n      points: 1,\r\n      orderIndex: editingQuiz.quiz?.questions.length || 0\r\n    };\r\n    \r\n    setEditingQuestion(newQuestion);\r\n    setIsQuestionDialogOpen(true);\r\n  };\r\n\r\n  const editQuestion = (question: QuestionData) => {\r\n    setEditingQuestion({ ...question });\r\n    setIsQuestionDialogOpen(true);\r\n  };\r\n\r\n  const saveQuestion = () => {\r\n    if (!editingQuestion || editingQuestion.question.length === 0 || (editingQuestion.question[0].type === 'text' && !editingQuestion.question[0].value.trim())) {\r\n      toast.error('Pertanyaan harus diisi');\r\n      return;\r\n    }\r\n\r\n    if (!editingQuiz.quiz) return;\r\n\r\n    const updatedQuestions = [...editingQuiz.quiz.questions];\r\n    const existingIndex = updatedQuestions.findIndex(q => q.id === editingQuestion.id);\r\n    \r\n    if (existingIndex >= 0) {\r\n      updatedQuestions[existingIndex] = editingQuestion;\r\n    } else {\r\n      updatedQuestions.push(editingQuestion);\r\n    }\r\n\r\n    setEditingQuiz(prev => ({\r\n      ...prev,\r\n      quiz: prev.quiz ? { ...prev.quiz, questions: updatedQuestions } : null\r\n    }));\r\n    \r\n    setIsQuestionDialogOpen(false);\r\n    setEditingQuestion(null);\r\n    toast.success('Pertanyaan berhasil disimpan');\r\n  };\r\n\r\n  const deleteQuestion = (questionId: string) => {\r\n    if (!editingQuiz.quiz) return;\r\n\r\n    const updatedQuestions = editingQuiz.quiz.questions\r\n      .filter(q => q.id !== questionId)\r\n      .map((q, index) => ({ ...q, orderIndex: index }));\r\n\r\n    setEditingQuiz(prev => ({\r\n      ...prev,\r\n      quiz: prev.quiz ? { ...prev.quiz, questions: updatedQuestions } : null\r\n    }));\r\n    \r\n    toast.success('Pertanyaan berhasil dihapus');\r\n  };\r\n\r\n  const getCompletionStatus = () => {\r\n    const totalChapters = data.modules.reduce((acc, module) => acc + module.chapters.length, 0);\r\n    const completedChapters = data.modules.reduce((acc, module) => \r\n      acc + module.chapters.filter(chapter => chapter.content && chapter.content.length > 0).length, 0\r\n    );\r\n    \r\n    return {\r\n      total: totalChapters,\r\n      completed: completedChapters,\r\n      percentage: totalChapters > 0 ? Math.round((completedChapters / totalChapters) * 100) : 0\r\n    };\r\n  };\r\n\r\n  const completionStatus = getCompletionStatus();\r\n\r\n  if (data.modules.length === 0) {\r\n    return (\r\n      <div className=\"text-center py-12\">\r\n        <BookOpen className=\"w-12 h-12 text-muted-foreground mx-auto mb-4\" />\r\n        <h3 className=\"text-lg font-semibold mb-2\">Belum ada modul</h3>\r\n        <p className=\"text-muted-foreground\">\r\n          Kembali ke langkah sebelumnya untuk membuat struktur modul terlebih dahulu\r\n        </p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header with Progress */}\r\n      <div className=\"flex items-center justify-between\">\r\n        <div>\r\n          <h3 className=\"text-lg font-semibold\">Pembuatan Konten</h3>\r\n          <p className=\"text-sm text-muted-foreground\">\r\n            Tambahkan konten dan quiz untuk setiap chapter\r\n          </p>\r\n        </div>\r\n        <div className=\"flex items-center space-x-4\">\r\n          <div className=\"text-right\">\r\n            <div className=\"text-sm font-medium\">\r\n              {completionStatus.completed} / {completionStatus.total} Chapter\r\n            </div>\r\n            <div className=\"text-xs text-muted-foreground\">\r\n              {completionStatus.percentage}% selesai\r\n            </div>\r\n          </div>\r\n          <div className={cn(\r\n            \"w-12 h-12 rounded-full flex items-center justify-center\",\r\n            completionStatus.percentage === 100 \r\n              ? \"bg-green-100 text-green-600\" \r\n              : \"bg-muted text-muted-foreground\"\r\n          )}>\r\n            {completionStatus.percentage === 100 ? (\r\n              <CheckCircle className=\"w-6 h-6\" />\r\n            ) : (\r\n              <Clock className=\"w-6 h-6\" />\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-6\">\r\n        {/* Module/Chapter Navigation */}\r\n        <div className=\"lg:col-span-1\">\r\n          <Card>\r\n            <CardHeader>\r\n              <CardTitle className=\"text-base\">Navigasi Konten</CardTitle>\r\n            </CardHeader>\r\n            <CardContent className=\"space-y-4 max-h-[70vh] overflow-y-auto\">\r\n              {/* Final Exam Section */}\r\n              <div className=\"space-y-2\">\r\n                <div className=\"p-3 rounded-lg border-2 border-dashed border-primary/30 bg-primary/5\">\r\n                  <div className=\"flex items-center justify-between mb-2\">\r\n                    <div>\r\n                      <div className=\"font-medium text-sm text-primary\">Final Exam</div>\r\n                      <div className=\"text-xs text-muted-foreground\">\r\n                        Ujian akhir untuk seluruh course\r\n                      </div>\r\n                    </div>\r\n                    {data.finalExam && (\r\n                      <CheckCircle className=\"w-4 h-4 text-green-600\" />\r\n                    )}\r\n                  </div>\r\n                  <Button\r\n                    variant={data.finalExam ? \"outline\" : \"default\"}\r\n                    size=\"sm\"\r\n                    className=\"w-full\"\r\n                    onClick={() => {\r\n                      if (data.finalExam) {\r\n                        editQuiz('final', data.finalExam);\r\n                      } else {\r\n                        createQuiz('final');\r\n                      }\r\n                    }}\r\n                  >\r\n                    <HelpCircle className=\"w-4 h-4 mr-2\" />\r\n                    {data.finalExam ? 'Edit Final Exam' : 'Buat Final Exam'}\r\n                  </Button>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Modules */}\r\n              {data.modules.map(module => (\r\n                <div key={module.id} className=\"space-y-2\">\r\n                  <div \r\n                    className={cn(\r\n                      \"p-2 rounded-lg cursor-pointer transition-colors\",\r\n                      selectedModule === module.id \r\n                        ? \"bg-primary text-primary-foreground\" \r\n                        : \"bg-muted hover:bg-muted/80\"\r\n                    )}\r\n                    onClick={() => {\r\n                      setSelectedModule(module.id);\r\n                      setSelectedChapter('');\r\n                    }}\r\n                  >\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <div>\r\n                        <div className=\"font-medium text-sm\">{module.name}</div>\r\n                        <div className=\"text-xs opacity-75\">\r\n                          {module.chapters.length} chapters\r\n                        </div>\r\n                      </div>\r\n                      {module.moduleQuiz && (\r\n                        <Badge variant=\"secondary\" className=\"text-xs\">\r\n                          <HelpCircle className=\"w-3 h-3 mr-1\" />\r\n                          Quiz\r\n                        </Badge>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                  \r\n                  {selectedModule === module.id && (\r\n                    <div className=\"ml-4 space-y-2\">\r\n                      {/* Module Quiz Button */}\r\n                      <div className=\"p-2 rounded bg-secondary/50\">\r\n                        <Button\r\n                          variant={module.moduleQuiz ? \"outline\" : \"secondary\"}\r\n                          size=\"sm\"\r\n                          className=\"w-full text-xs\"\r\n                          onClick={() => {\r\n                            if (module.moduleQuiz) {\r\n                              editQuiz('module', module.moduleQuiz);\r\n                            } else {\r\n                              createQuiz('module');\r\n                            }\r\n                          }}\r\n                        >\r\n                          <HelpCircle className=\"w-3 h-3 mr-1\" />\r\n                          {module.moduleQuiz ? 'Edit Module Quiz' : 'Buat Module Quiz'}\r\n                        </Button>\r\n                      </div>\r\n                      \r\n                      {/* Chapters */}\r\n                      {module.chapters.map(chapter => {\r\n                        const hasContent = chapter.content && chapter.content.length > 0;\r\n                        return (\r\n                          <div key={chapter.id} className=\"space-y-1\">\r\n                            <div\r\n                              className={cn(\r\n                                \"p-2 rounded text-xs cursor-pointer transition-colors flex items-center justify-between\",\r\n                                selectedChapter === chapter.id\r\n                                  ? \"bg-primary/20 text-primary\"\r\n                                  : \"hover:bg-muted/50\"\r\n                              )}\r\n                              onClick={() => setSelectedChapter(chapter.id)}\r\n                            >\r\n                              <span>{chapter.name}</span>\r\n                              {hasContent && (\r\n                                <CheckCircle className=\"w-3 h-3 text-green-600\" />\r\n                              )}\r\n                            </div>\r\n                            \r\n                            {/* Content Block Navigation - show when chapter is selected and has content */}\r\n                            {selectedChapter === chapter.id && hasContent && chapter.content && (\r\n                              <div className=\"ml-4 space-y-1\">\r\n                                <div className=\"flex items-center space-x-1 text-xs text-muted-foreground mb-1\">\r\n                                  <Navigation className=\"w-3 h-3\" />\r\n                                  <span>Content Blocks</span>\r\n                                </div>\r\n                                {chapter.content.map((block, index) => (\r\n                                  <button\r\n                                    key={block.id || index}\r\n                                    onClick={() => scrollToContent(block.id || `block-${index}`)}\r\n                                    className=\"w-full text-left p-1.5 rounded text-xs hover:bg-primary/10 transition-colors flex items-center space-x-2\"\r\n                                  >\r\n                                    {getContentTypeIcon(block.type)}\r\n                                    <span className=\"truncate flex-1\">\r\n                                      {index + 1}. {getContentPreview(block)}\r\n                                    </span>\r\n                                  </button>\r\n                                ))}\r\n                              </div>\r\n                            )}\r\n                          </div>\r\n                        );\r\n                      })}\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              ))}\r\n            </CardContent>\r\n          </Card>\r\n        </div>\r\n\r\n        {/* Content Editor */}\r\n        <div className=\"lg:col-span-3\">\r\n          {!selectedChapter ? (\r\n            <div className=\"space-y-6\">\r\n              {/* Final Exam Info */}\r\n              <Card>\r\n                <CardHeader>\r\n                  <CardTitle className=\"flex items-center space-x-2\">\r\n                    <HelpCircle className=\"w-5 h-5 text-primary\" />\r\n                    <span>Final Exam</span>\r\n                    {data.finalExam && (\r\n                      <Badge variant=\"secondary\">Sudah dibuat</Badge>\r\n                    )}\r\n                  </CardTitle>\r\n                  <CardDescription>\r\n                    Ujian akhir untuk menguji pemahaman siswa terhadap seluruh materi course\r\n                  </CardDescription>\r\n                </CardHeader>\r\n                <CardContent>\r\n                  {data.finalExam ? (\r\n                    <div className=\"space-y-4\">\r\n                      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\r\n                        <div className=\"text-center p-4 bg-muted rounded-lg\">\r\n                          <div className=\"text-2xl font-bold text-primary\">\r\n                            {data.finalExam.questions.length}\r\n                          </div>\r\n                          <div className=\"text-sm text-muted-foreground\">Pertanyaan</div>\r\n                        </div>\r\n                        <div className=\"text-center p-4 bg-muted rounded-lg\">\r\n                          <div className=\"text-2xl font-bold text-primary\">\r\n                            {data.finalExam.minimumScore}%\r\n                          </div>\r\n                          <div className=\"text-sm text-muted-foreground\">Nilai Minimum</div>\r\n                        </div>\r\n                        <div className=\"text-center p-4 bg-muted rounded-lg\">\r\n                          <div className=\"text-2xl font-bold text-primary\">\r\n                            {data.finalExam.questions.reduce((sum, q) => sum + q.points, 0)}\r\n                          </div>\r\n                          <div className=\"text-sm text-muted-foreground\">Total Poin</div>\r\n                        </div>\r\n                        {data.finalExam.timeLimit && (\r\n                          <div className=\"text-center p-4 bg-muted rounded-lg\">\r\n                            <div className=\"text-2xl font-bold text-primary\">\r\n                              {data.finalExam.timeLimit}\r\n                            </div>\r\n                            <div className=\"text-sm text-muted-foreground\">Menit</div>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                      <div className=\"flex space-x-2\">\r\n                        <Button\r\n                          onClick={() => editQuiz('final', data.finalExam!)}\r\n                          className=\"flex-1\"\r\n                        >\r\n                          <Edit className=\"w-4 h-4 mr-2\" />\r\n                          Edit Final Exam\r\n                        </Button>\r\n                      </div>\r\n                    </div>\r\n                  ) : (\r\n                    <div className=\"text-center py-8\">\r\n                      <HelpCircle className=\"w-12 h-12 text-muted-foreground mx-auto mb-4\" />\r\n                      <h3 className=\"text-lg font-semibold mb-2\">Belum ada Final Exam</h3>\r\n                      <p className=\"text-muted-foreground mb-4\">\r\n                        Final Exam adalah ujian akhir yang menguji pemahaman siswa terhadap seluruh materi course\r\n                      </p>\r\n                      <Button onClick={() => createQuiz('final')}>\r\n                        <Plus className=\"w-4 h-4 mr-2\" />\r\n                        Buat Final Exam\r\n                      </Button>\r\n                    </div>\r\n                  )}\r\n                </CardContent>\r\n              </Card>\r\n\r\n              {/* Module Overview */}\r\n              <Card>\r\n                <CardHeader>\r\n                  <CardTitle>Overview Modul</CardTitle>\r\n                  <CardDescription>\r\n                    Pilih chapter dari navigasi di sebelah kiri untuk mulai menambahkan konten\r\n                  </CardDescription>\r\n                </CardHeader>\r\n                <CardContent>\r\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                    {data.modules.map(module => (\r\n                      <div key={module.id} className=\"p-4 border rounded-lg\">\r\n                        <div className=\"flex items-center justify-between mb-2\">\r\n                          <h4 className=\"font-medium\">{module.name}</h4>\r\n                          {module.moduleQuiz && (\r\n                            <Badge variant=\"secondary\">\r\n                              <HelpCircle className=\"w-3 h-3 mr-1\" />\r\n                              Quiz\r\n                            </Badge>\r\n                          )}\r\n                        </div>\r\n                        <div className=\"text-sm text-muted-foreground mb-3\">\r\n                          {module.chapters.length} chapters\r\n                        </div>\r\n                        <div className=\"space-y-1\">\r\n                          {module.chapters.map(chapter => {\r\n                            const hasContent = chapter.content && chapter.content.length > 0;\r\n                            return (\r\n                              <div key={chapter.id} className=\"flex items-center justify-between text-xs\">\r\n                                <span>{chapter.name}</span>\r\n                                {hasContent ? (\r\n                                  <CheckCircle className=\"w-3 h-3 text-green-600\" />\r\n                                ) : (\r\n                                  <Clock className=\"w-3 h-3 text-muted-foreground\" />\r\n                                )}\r\n                              </div>\r\n                            );\r\n                          })}\r\n                        </div>\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                </CardContent>\r\n              </Card>\r\n            </div>\r\n          ) : (\r\n            <div className=\"space-y-6\">\r\n              {/* Chapter Header */}\r\n              <Card>\r\n                <CardHeader>\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <div>\r\n                      <CardTitle className=\"flex items-center space-x-2\">\r\n                        <span>{currentChapter?.name}</span>\r\n                        {currentChapter?.hasChapterQuiz && (\r\n                          <Badge variant=\"secondary\">\r\n                            <HelpCircle className=\"w-3 h-3 mr-1\" />\r\n                            Chapter Quiz\r\n                          </Badge>\r\n                        )}\r\n                        {currentModule?.moduleQuiz && (\r\n                          <Badge variant=\"outline\">\r\n                            <HelpCircle className=\"w-3 h-3 mr-1\" />\r\n                            Module Quiz\r\n                          </Badge>\r\n                        )}\r\n                      </CardTitle>\r\n                      <CardDescription>\r\n                        Modul: {currentModule?.name}\r\n                        {currentModule?.moduleQuiz && (\r\n                          <span className=\"ml-2 text-xs text-primary\">\r\n                            • Module ini memiliki quiz\r\n                          </span>\r\n                        )}\r\n                      </CardDescription>\r\n                    </div>\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <Button\r\n                        variant=\"outline\"\r\n                        size=\"sm\"\r\n                        onClick={() => setPreviewMode(!previewMode)}\r\n                      >\r\n                        <Eye className=\"w-4 h-4 mr-2\" />\r\n                        {previewMode ? 'Edit' : 'Preview'}\r\n                      </Button>\r\n                      {currentModule?.moduleQuiz && (\r\n                        <Button\r\n                          variant=\"outline\"\r\n                          size=\"sm\"\r\n                          onClick={() => editQuiz('module', currentModule.moduleQuiz!)}\r\n                        >\r\n                          <HelpCircle className=\"w-4 h-4 mr-2\" />\r\n                          Edit Module Quiz\r\n                        </Button>\r\n                      )}\r\n                      {currentChapter?.hasChapterQuiz && (\r\n                        <Button\r\n                          variant=\"outline\"\r\n                          size=\"sm\"\r\n                          onClick={() => {\r\n                            if (currentChapter.chapterQuiz) {\r\n                              editQuiz('chapter', currentChapter.chapterQuiz);\r\n                            } else {\r\n                              createQuiz('chapter');\r\n                            }\r\n                          }}\r\n                        >\r\n                          <HelpCircle className=\"w-4 h-4 mr-2\" />\r\n                          {currentChapter.chapterQuiz ? 'Edit Chapter Quiz' : 'Buat Chapter Quiz'}\r\n                        </Button>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                </CardHeader>\r\n              </Card>\r\n\r\n              {/* Content Editor/Preview with Scrollable Container */}\r\n              <Card>\r\n                <CardHeader>\r\n                  <CardTitle className=\"text-base\">Konten Chapter</CardTitle>\r\n                  <CardDescription>\r\n                    {previewMode \r\n                      ? 'Preview konten seperti yang akan dilihat siswa'\r\n                      : 'Gunakan Markdown untuk memformat konten'}\r\n                  </CardDescription>\r\n                </CardHeader>\r\n                <CardContent>\r\n                  {previewMode ? (\r\n                    <div className=\"max-h-[60vh] overflow-y-auto prose max-w-none pr-4\">\r\n                      {currentChapter?.content && currentChapter.content.length > 0 ? (\r\n                        <>\r\n                          {currentChapter.content.map((block: any, index: number) => (\r\n                            <div \r\n                              key={block.id || index} \r\n                              ref={(el) => {\r\n                                contentRefs.current[block.id || `block-${index}`] = el;\r\n                              }}\r\n                              className=\"mb-6 scroll-mt-4\"\r\n                              id={block.id || `block-${index}`}\r\n                            >\r\n                              {/* Content Block Header for easier identification */}\r\n                              <div className=\"flex items-center space-x-2 mb-2 py-1 px-2 bg-muted/30 rounded text-xs text-muted-foreground\">\r\n                                {getContentTypeIcon(block.type)}\r\n                                <span>Content-{index + 1}</span>\r\n                                <span>({block.type})</span>\r\n                              </div>\r\n                              \r\n                              {block.type === 'text' ? (\r\n                                <ReactMarkdown\r\n                                  remarkPlugins={[remarkGfm]}\r\n                                  components={{\r\n                                    h1: ({ node, ...props }) => (\r\n                                      <h1 className=\"mb-4 text-2xl font-bold text-gray-900\" {...props} />\r\n                                    ),\r\n                                    h2: ({ node, ...props }) => (\r\n                                      <h2 className=\"mb-3 text-xl font-semibold text-gray-800\" {...props} />\r\n                                    ),\r\n                                    h3: ({ node, ...props }) => (\r\n                                      <h3 className=\"mb-2 text-lg font-semibold text-gray-800\" {...props} />\r\n                                    ),\r\n                                    h4: ({ node, ...props }) => (\r\n                                      <h4 className=\"mb-2 text-base font-semibold text-gray-700\" {...props} />\r\n                                    ),\r\n                                    p: ({ node, ...props }) => (\r\n                                      <p className=\"mb-3 leading-relaxed\" {...props} />\r\n                                    ),\r\n                                    ul: ({ node, ...props }) => (\r\n                                      <ul className=\"mb-3 ml-4 list-disc\" {...props} />\r\n                                    ),\r\n                                    ol: ({ node, ...props }) => (\r\n                                      <ol className=\"mb-3 ml-4 list-decimal\" {...props} />\r\n                                    ),\r\n                                    li: ({ node, ...props }) => (\r\n                                      <li className=\"mb-1\" {...props} />\r\n                                    ),\r\n                                    blockquote: ({ node, ...props }) => (\r\n                                      <blockquote\r\n                                        className=\"mb-4 rounded-r border-l-4 border-blue-200 bg-blue-50 py-2 pl-4 italic\"\r\n                                        {...props}\r\n                                      />\r\n                                    ),\r\n                                    code: ({ node, className, children, ...props }) => {\r\n                                      const match = /language-(\\w+)/.exec(className || '');\r\n                                      const isInline = !match;\r\n                                      return isInline ? (\r\n                                        <code\r\n                                          className=\"rounded bg-gray-100 px-1 py-0.5 font-mono text-sm\"\r\n                                          {...props}\r\n                                        >\r\n                                          {children}\r\n                                        </code>\r\n                                      ) : (\r\n                                        <code\r\n                                          className=\"block overflow-x-auto rounded bg-gray-900 p-4 font-mono text-sm text-gray-100\"\r\n                                          {...props}\r\n                                        >\r\n                                          {children}\r\n                                        </code>\r\n                                      );\r\n                                    },\r\n                                    pre: ({ node, ...props }) => (\r\n                                      <pre className=\"mb-4\" {...props} />\r\n                                    ),\r\n                                    table: ({ node, ...props }) => (\r\n                                      <div className=\"mb-4 overflow-x-auto\">\r\n                                        <table\r\n                                          className=\"min-w-full rounded border border-gray-200\"\r\n                                          {...props}\r\n                                        />\r\n                                      </div>\r\n                                    ),\r\n                                    thead: ({ node, ...props }) => (\r\n                                      <thead className=\"bg-gray-50\" {...props} />\r\n                                    ),\r\n                                    th: ({ node, ...props }) => (\r\n                                      <th\r\n                                        className=\"border border-gray-200 px-3 py-2 text-left font-semibold\"\r\n                                        {...props}\r\n                                      />\r\n                                    ),\r\n                                    td: ({ node, ...props }) => (\r\n                                      <td\r\n                                        className=\"border border-gray-200 px-3 py-2\"\r\n                                        {...props}\r\n                                      />\r\n                                    ),\r\n                                    hr: ({ node, ...props }) => (\r\n                                      <hr className=\"my-6 border-gray-300\" {...props} />\r\n                                    ),\r\n                                    strong: ({ node, ...props }) => (\r\n                                      <strong\r\n                                        className=\"font-semibold text-gray-900\"\r\n                                        {...props}\r\n                                      />\r\n                                    ),\r\n                                    em: ({ node, ...props }) => (\r\n                                      <em className=\"italic\" {...props} />\r\n                                    )\r\n                                  }}\r\n                                >\r\n                                  {block.value}\r\n                                </ReactMarkdown>\r\n                              ) : block.type === 'image' ? (\r\n                                <div className=\"my-4\">\r\n                                  <img \r\n                                    src={block.value} \r\n                                    alt=\"Content\" \r\n                                    className=\"max-w-full h-auto rounded-md\"\r\n                                  />\r\n                                </div>\r\n                              ) : block.type === 'video' ? (\r\n                                <div className=\"my-4\">\r\n                                  <video \r\n                                    src={block.value} \r\n                                    controls \r\n                                    className=\"max-w-full rounded-md\"\r\n                                  />\r\n                                </div>\r\n                              ) : block.type === 'pdf' ? (\r\n                                <div className=\"my-4\">\r\n                                  <iframe \r\n                                    src={block.value} \r\n                                    className=\"w-full h-96 rounded-md\"\r\n                                    title=\"PDF Content\"\r\n                                  />\r\n                                </div>\r\n                              ) : (\r\n                                <div className=\"my-4 p-4 bg-muted rounded-md\">\r\n                                  <p className=\"text-sm text-muted-foreground\">\r\n                                    {block.type === 'zoom-recording' \r\n                                      ? 'Zoom Recording: ' \r\n                                      : 'File: '}\r\n                                    <a \r\n                                      href={block.value} \r\n                                      target=\"_blank\" \r\n                                      rel=\"noopener noreferrer\"\r\n                                      className=\"text-primary hover:underline\"\r\n                                    >\r\n                                      {block.value}\r\n                                    </a>\r\n                                  </p>\r\n                                </div>\r\n                              )}\r\n                            </div>\r\n                          ))}\r\n                        </>\r\n                      ) : (\r\n                        <p className=\"text-muted-foreground italic\">\r\n                          Belum ada konten untuk chapter ini\r\n                        </p>\r\n                      )}\r\n                    </div>\r\n                  ) : (\r\n                    <div className=\"space-y-4\">\r\n                      <div className=\"max-h-[60vh] overflow-y-auto pr-4\">\r\n                        <DynamicContentEditor\r\n                          initialContent={currentChapter?.content || []}\r\n                          onContentChange={updateChapterContent}\r\n                          contentRefs={contentRefs}\r\n                        />\r\n                      </div>\r\n                      <div className=\"flex justify-between items-center text-sm text-muted-foreground\">\r\n                        <span>Mendukung Markdown formatting</span>\r\n                        <span>\r\n                          {currentChapter?.content?.length || 0} blok konten\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n                </CardContent>\r\n              </Card>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Quiz Dialog */}\r\n      <Dialog open={isQuizDialogOpen} onOpenChange={setIsQuizDialogOpen}>\r\n        <DialogContent className=\"sm:max-w-4xl max-h-[80vh] overflow-y-auto p-6\">\r\n          <DialogHeader>\r\n            <DialogTitle>\r\n              {editingQuiz.quiz?.questions.length ? 'Edit Quiz' : 'Buat Quiz Baru'}\r\n            </DialogTitle>\r\n            <DialogDescription>\r\n              Buat pertanyaan untuk menguji pemahaman siswa\r\n            </DialogDescription>\r\n          </DialogHeader>\r\n          \r\n          <div className=\"space-y-6\">\r\n            {/* Quiz Info */}\r\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"quizName\">Nama Quiz *</Label>\r\n                <Input\r\n                  id=\"quizName\"\r\n                  placeholder=\"Masukkan nama quiz\"\r\n                  value={editingQuiz.quiz?.name || ''}\r\n                  onChange={(e) => setEditingQuiz(prev => ({\r\n                    ...prev,\r\n                    quiz: prev.quiz ? { ...prev.quiz, name: e.target.value } : null\r\n                  }))}\r\n                />\r\n              </div>\r\n              \r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"minimumScore\">Nilai Minimum (%)</Label>\r\n                <Input\r\n                  id=\"minimumScore\"\r\n                  type=\"number\"\r\n                  min=\"0\"\r\n                  max=\"100\"\r\n                  value={editingQuiz.quiz?.minimumScore || 70}\r\n                  onChange={(e) => setEditingQuiz(prev => ({\r\n                    ...prev,\r\n                    quiz: prev.quiz ? { ...prev.quiz, minimumScore: parseInt(e.target.value) } : null\r\n                  }))}\r\n                />\r\n              </div>\r\n              \r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"timeLimit\">Batas Waktu (menit)</Label>\r\n                <Input\r\n                  id=\"timeLimit\"\r\n                  type=\"number\"\r\n                  min=\"1\"\r\n                  value={editingQuiz.quiz?.timeLimit || ''}\r\n                  onChange={(e) => setEditingQuiz(prev => ({\r\n                    ...prev,\r\n                    quiz: prev.quiz ? { ...prev.quiz, timeLimit: e.target.value ? parseInt(e.target.value) : undefined } : null\r\n                  }))}\r\n                  placeholder=\"Tanpa batas waktu\"\r\n                />\r\n              </div>\r\n            </div>\r\n            \r\n            <div className=\"space-y-2\">\r\n              <Label htmlFor=\"quizDescription\">Deskripsi</Label>\r\n              <Textarea\r\n                id=\"quizDescription\"\r\n                placeholder=\"Jelaskan tentang quiz ini...\"\r\n                value={editingQuiz.quiz?.description || ''}\r\n                onChange={(e) => setEditingQuiz(prev => ({\r\n                  ...prev,\r\n                  quiz: prev.quiz ? { ...prev.quiz, description: e.target.value } : null\r\n                }))}\r\n                rows={2}\r\n              />\r\n            </div>\r\n\r\n            {/* Questions */}\r\n            <div className=\"space-y-4\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <h4 className=\"text-lg font-semibold\">Pertanyaan</h4>\r\n                <Button onClick={createQuestion}>\r\n                  <Plus className=\"w-4 h-4 mr-2\" />\r\n                  Tambah Pertanyaan\r\n                </Button>\r\n              </div>\r\n              \r\n              {editingQuiz.quiz?.questions.length === 0 ? (\r\n                <div className=\"text-center py-8 text-muted-foreground\">\r\n                  <HelpCircle className=\"w-8 h-8 mx-auto mb-2\" />\r\n                  <p className=\"text-sm\">Belum ada pertanyaan</p>\r\n                </div>\r\n              ) : (\r\n                <div className=\"space-y-3\">\r\n                  {editingQuiz.quiz?.questions.map((question, index) => (\r\n                    <Card key={question.id}>\r\n                      <CardContent className=\"p-4\">\r\n                        <div className=\"flex items-start justify-between\">\r\n                          <div className=\"flex-1\">\r\n                            <div className=\"flex items-center space-x-2 mb-2\">\r\n                              <Badge variant=\"outline\">{index + 1}</Badge>\r\n                              <Badge variant=\"secondary\">\r\n                                {question.type === 'multiple_choice' ? 'Pilihan Ganda' :\r\n                                 question.type === 'true_false' ? 'Benar/Salah' : 'Essay'}\r\n                              </Badge>\r\n                              <span className=\"text-sm text-muted-foreground\">\r\n                                {question.points} poin\r\n                              </span>\r\n                            </div>\r\n                            <div className=\"text-sm\">\r\n                              {question.question.map((block, blockIndex) => (\r\n                                <React.Fragment key={blockIndex}>\r\n                                  {block.type === 'text' && <p>{block.value}</p>}\r\n                                  {block.type === 'image' && block.value && (\r\n                                    <img src={block.value} alt={`Question image ${blockIndex}`} className=\"max-w-xs max-h-32 object-contain mt-2\" />\r\n                                  )}\r\n                                </React.Fragment>\r\n                              ))}\r\n                            </div>\r\n                            {question.type === 'multiple_choice' && question.options && (\r\n                              <div className=\"mt-2 space-y-1\">\r\n                                {question.options.map((option, optIndex) => (\r\n                                  <div key={optIndex} className=\"text-xs text-muted-foreground\">\r\n                                    {String.fromCharCode(65 + optIndex)}.\r\n                                    {option.content.map((block, optionBlockIndex) => (\r\n                                      <React.Fragment key={optionBlockIndex}>\r\n                                        {block.type === 'text' && <span>{block.value}</span>}\r\n                                        {block.type === 'image' && block.value && (\r\n                                          <img src={block.value} alt={`Option image ${optionBlockIndex}`} className=\"inline-block max-h-8 object-contain ml-1\" />\r\n                                        )}\r\n                                      </React.Fragment>\r\n                                    ))}\r\n                                  </div>\r\n                                ))}\r\n                              </div>\r\n                            )}\r\n                          </div>\r\n                          <div className=\"flex items-center space-x-1\">\r\n                            <Button\r\n                              variant=\"ghost\"\r\n                              size=\"sm\"\r\n                              onClick={() => editQuestion(question)}\r\n                            >\r\n                              <Edit className=\"w-4 h-4\" />\r\n                            </Button>\r\n                            <AlertDialog>\r\n                              <AlertDialogTrigger asChild>\r\n                                <Button variant=\"ghost\" size=\"sm\">\r\n                                  <Trash2 className=\"w-4 h-4\" />\r\n                                </Button>\r\n                              </AlertDialogTrigger>\r\n                              <AlertDialogContent>\r\n                                <AlertDialogHeader>\r\n                                  <AlertDialogTitle>Hapus Pertanyaan</AlertDialogTitle>\r\n                                  <AlertDialogDescription>\r\n                                    Apakah Anda yakin ingin menghapus pertanyaan ini?\r\n                                  </AlertDialogDescription>\r\n                                </AlertDialogHeader>\r\n                                <AlertDialogFooter>\r\n                                  <AlertDialogCancel>Batal</AlertDialogCancel>\r\n                                  <AlertDialogAction onClick={() => deleteQuestion(question.id)}>\r\n                                    Hapus\r\n                                  </AlertDialogAction>\r\n                                </AlertDialogFooter>\r\n                              </AlertDialogContent>\r\n                            </AlertDialog>\r\n                          </div>\r\n                        </div>\r\n                      </CardContent>\r\n                    </Card>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n          \r\n          <DialogFooter>\r\n            <Button variant=\"outline\" onClick={() => setIsQuizDialogOpen(false)}>\r\n              Batal\r\n            </Button>\r\n            <Button onClick={saveQuiz}>\r\n              <Save className=\"w-4 h-4 mr-2\" />\r\n              Simpan Quiz\r\n            </Button>\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n\r\n      {/* Question Dialog */}\r\n      <Dialog open={isQuestionDialogOpen} onOpenChange={setIsQuestionDialogOpen}>\r\n        <DialogContent className=\"sm:max-w-2xl max-h-[80vh] overflow-y-auto p-6\">\r\n          <DialogHeader>\r\n            <DialogTitle>\r\n              {editingQuestion?.question ? 'Edit Pertanyaan' : 'Tambah Pertanyaan Baru'}\r\n            </DialogTitle>\r\n          </DialogHeader>\r\n          \r\n          <div className=\"space-y-4\">\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"questionType\">Tipe Pertanyaan</Label>\r\n                <Select \r\n                  value={editingQuestion?.type || 'multiple_choice'}\r\n                  onValueChange={(value: 'multiple_choice' | 'true_false' | 'essay') => {\r\n                    setEditingQuestion(prev => {\r\n                      if (!prev) return null;\r\n                      const newQuestion = { ...prev, type: value };\r\n                      if (value === 'true_false') {\r\n                        newQuestion.options = [\r\n                          { content: [{ type: 'text', value: 'True' }], isCorrect: false },\r\n                          { content: [{ type: 'text', value: 'False' }], isCorrect: false }\r\n                        ];\r\n                      } else if (value === 'multiple_choice') {\r\n                        newQuestion.options = [\r\n                          { content: [{ type: 'text', value: '' }], isCorrect: false },\r\n                          { content: [{ type: 'text', value: '' }], isCorrect: false },\r\n                          { content: [{ type: 'text', value: '' }], isCorrect: false },\r\n                          { content: [{ type: 'text', value: '' }], isCorrect: false }\r\n                        ];\r\n                      } else {\r\n                        newQuestion.options = undefined; // Clear options for essay\r\n                      }\r\n                      return newQuestion;\r\n                    });\r\n                  }}\r\n                >\r\n                  <SelectTrigger>\r\n                    <SelectValue />\r\n                  </SelectTrigger>\r\n                  <SelectContent>\r\n                    <SelectItem value=\"multiple_choice\">Pilihan Ganda</SelectItem>\r\n                    <SelectItem value=\"true_false\">Benar/Salah</SelectItem>\r\n                    <SelectItem value=\"essay\">Essay</SelectItem>\r\n                  </SelectContent>\r\n                </Select>\r\n              </div>\r\n              \r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"questionPoints\">Poin</Label>\r\n                <Input\r\n                  id=\"questionPoints\"\r\n                  type=\"number\"\r\n                  min=\"1\"\r\n                  value={editingQuestion?.points || 1}\r\n                  onChange={(e) => setEditingQuestion(prev => \r\n                    prev ? { ...prev, points: parseInt(e.target.value) } : null\r\n                  )}\r\n                />\r\n              </div>\r\n            </div>\r\n            \r\n            <div className=\"space-y-2\">\r\n              <Label htmlFor=\"questionText\">Pertanyaan *</Label>\r\n              <DynamicContentEditor\r\n                initialContent={editingQuestion?.question || []}\r\n                onContentChange={(content) => setEditingQuestion(prev =>\r\n                  prev ? { ...prev, question: content } : null\r\n                )}\r\n                allowImages={true} // Allow images in questions\r\n              />\r\n            </div>\r\n            \r\n            {(editingQuestion?.type === 'multiple_choice' || editingQuestion?.type === 'true_false') && (\r\n              <div className=\"space-y-4\">\r\n                <Label>Pilihan Jawaban</Label>\r\n                {editingQuestion.options?.map((option, index) => (\r\n                  <div key={index} className=\"flex flex-col space-y-2 border p-3 rounded-md\">\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      {editingQuestion.type === 'multiple_choice' && (\r\n                        <span className=\"text-sm font-medium w-6\">\r\n                          {String.fromCharCode(65 + index)}.\r\n                        </span>\r\n                      )}\r\n                      {editingQuestion.type === 'multiple_choice' ? (\r\n                        <DynamicContentEditor\r\n                          initialContent={option.content || []}\r\n                          onContentChange={(content) => {\r\n                            const newOptions = [...(editingQuestion.options || [])];\r\n                            newOptions[index] = { ...newOptions[index], content: content };\r\n                            setEditingQuestion(prev =>\r\n                              prev ? { ...prev, options: newOptions } : null\r\n                            );\r\n                          }}\r\n                          allowImages={true} // Allow images in options\r\n                        />\r\n                      ) : (\r\n                        <span className=\"text-base font-medium\">{option.content[0].value}</span>\r\n                      )}\r\n                    </div>\r\n                    <div className=\"flex items-center space-x-2 mt-2\">\r\n                      <Checkbox\r\n                        id={`option-correct-${index}`}\r\n                        checked={option.isCorrect}\r\n                        onCheckedChange={(checked: boolean) => {\r\n                          const newOptions = [...(editingQuestion.options || [])];\r\n                          newOptions[index] = { ...newOptions[index], isCorrect: checked as boolean };\r\n                          setEditingQuestion(prev =>\r\n                            prev ? { ...prev, options: newOptions } : null\r\n                          );\r\n                        }}\r\n                      />\r\n                      <Label htmlFor={`option-correct-${index}`}>Jawaban Benar</Label>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            )}\r\n            \r\n            \r\n            {editingQuestion && editingQuestion.type === 'essay' && (\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"essay-answer\">Jawaban Esai</Label>\r\n                <Textarea\r\n                  id=\"essay-answer\"\r\n                  placeholder=\"Masukkan jawaban esai untuk pertanyaan ini\"\r\n                  value={editingQuestion.essayAnswer || ''}\r\n                  onChange={(e) => setEditingQuestion(prev =>\r\n                    prev ? { ...prev, essayAnswer: e.target.value } : null\r\n                  )}\r\n                  rows={4}\r\n                />\r\n              </div>\r\n            )}\r\n\r\n            {editingQuestion && (\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"explanation\">Penjelasan Jawaban (Opsional)</Label>\r\n                <DynamicContentEditor\r\n                  initialContent={editingQuestion?.explanation || []}\r\n                  onContentChange={(content) => {\r\n                    setEditingQuestion(prev =>\r\n                      prev ? { ...prev, explanation: content } : null\r\n                    );\r\n                  }}\r\n                  placeholder=\"Jelaskan jawaban yang benar atau berikan informasi tambahan\"\r\n                  allowImages={true}\r\n                />\r\n              </div>\r\n            )}\r\n          </div>\r\n          \r\n          <DialogFooter>\r\n            <Button variant=\"outline\" onClick={() => setIsQuestionDialogOpen(false)}>\r\n              Batal\r\n            </Button>\r\n            <Button onClick={saveQuestion}>\r\n              {editingQuestion?.question ? 'Perbarui' : 'Tambah'} Pertanyaan\r\n            </Button>\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n    </div>\r\n  );\r\n}", "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\course\\steps\\course-details-step.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\course\\steps\\module-structure-step.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\course\\steps\\publishing-step.tsx", ["1512", "1513"], [], "'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport { But<PERSON> } from '@/components/ui/button';\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Progress } from '@/components/ui/progress';\r\nimport { Separator } from '@/components/ui/separator';\r\nimport { Alert, AlertDescription } from '@/components/ui/alert';\r\nimport { \r\n  CheckCircle, \r\n  AlertCircle, \r\n  BookOpen, \r\n  Users, \r\n  HelpCircle,\r\n  Calendar,\r\n  Code,\r\n  Image,\r\n  FileText,\r\n  Clock,\r\n  Target,\r\n  Rocket,\r\n  Eye,\r\n  Share2\r\n} from 'lucide-react';\r\nimport { cn } from '@/lib/utils';\r\nimport { CourseData } from '../course-creation-wizard';\r\nimport { toast } from 'sonner';\r\n\r\ninterface PublishingStepProps {\r\n  data: CourseData;\r\n  onPublish: () => Promise<void>;\r\n  isPublishing: boolean;\r\n}\r\n\r\ninterface ValidationItem {\r\n  id: string;\r\n  label: string;\r\n  status: 'complete' | 'incomplete' | 'warning';\r\n  description: string;\r\n  required: boolean;\r\n}\r\n\r\nexport function PublishingStep({ data, onPublish, isPublishing }: PublishingStepProps) {\r\n  const [showDetails, setShowDetails] = useState(false);\r\n\r\n  const getValidationItems = (): ValidationItem[] => {\r\n    const items: ValidationItem[] = [];\r\n\r\n    // Basic course info validation\r\n    items.push({\r\n      id: 'course-name',\r\n      label: 'Nama Course',\r\n      status: data.name.trim() ? 'complete' : 'incomplete',\r\n      description: data.name.trim() ? `\"${data.name}\"` : 'Nama course harus diisi',\r\n      required: true\r\n    });\r\n\r\n    items.push({\r\n      id: 'course-description',\r\n      label: 'Deskripsi Course',\r\n      status: data.description.trim() ? 'complete' : 'incomplete',\r\n      description: data.description.trim() \r\n        ? `${data.description.length} karakter` \r\n        : 'Deskripsi course harus diisi',\r\n      required: true\r\n    });\r\n\r\n    items.push({\r\n      id: 'course-code',\r\n      label: 'Kode Course',\r\n      status: data.courseCode.trim() ? 'complete' : 'incomplete',\r\n      description: data.courseCode.trim() ? data.courseCode : 'Kode course harus diisi',\r\n      required: true\r\n    });\r\n\r\n    items.push({\r\n      id: 'cover-image',\r\n      label: 'Cover Image',\r\n      status: data.coverImage ? 'complete' : 'warning',\r\n      description: data.coverImage ? 'Cover image telah diupload' : 'Disarankan menambahkan cover image',\r\n      required: false\r\n    });\r\n\r\n    items.push({\r\n      id: 'course-dates',\r\n      label: 'Tanggal Course',\r\n      status: data.startDate && data.endDate ? 'complete' : 'warning',\r\n      description: data.startDate && data.endDate \r\n        ? `${new Date(data.startDate).toLocaleDateString()} - ${new Date(data.endDate).toLocaleDateString()}`\r\n        : 'Tanggal mulai dan selesai belum diatur',\r\n      required: false\r\n    });\r\n\r\n    // Module structure validation\r\n    const moduleCount = data.modules.length;\r\n    items.push({\r\n      id: 'modules',\r\n      label: 'Struktur Modul',\r\n      status: moduleCount > 0 ? 'complete' : 'incomplete',\r\n      description: moduleCount > 0 \r\n        ? `${moduleCount} modul telah dibuat` \r\n        : 'Minimal 1 modul harus dibuat',\r\n      required: true\r\n    });\r\n\r\n    const totalChapters = data.modules.reduce((acc, module) => acc + module.chapters.length, 0);\r\n    items.push({\r\n      id: 'chapters',\r\n      label: 'Chapter',\r\n      status: totalChapters > 0 ? 'complete' : 'incomplete',\r\n      description: totalChapters > 0 \r\n        ? `${totalChapters} chapter telah dibuat` \r\n        : 'Minimal 1 chapter harus dibuat',\r\n      required: true\r\n    });\r\n\r\n    // Content validation\r\n    const chaptersWithContent = data.modules.reduce((acc, module) => \r\n      acc + module.chapters.filter(chapter => chapter.content && chapter.content.length > 0).length, 0\r\n    );\r\n    \r\n    items.push({\r\n      id: 'content',\r\n      label: 'Konten Chapter',\r\n      status: chaptersWithContent === totalChapters ? 'complete' : \r\n               chaptersWithContent > 0 ? 'warning' : 'incomplete',\r\n      description: `${chaptersWithContent} dari ${totalChapters} chapter memiliki konten`,\r\n      required: true\r\n    });\r\n\r\n    // Quiz validation\r\n    const chaptersWithQuiz = data.modules.reduce((acc, module) => \r\n      acc + module.chapters.filter(chapter => chapter.hasChapterQuiz && chapter.chapterQuiz).length, 0\r\n    );\r\n    \r\n    const modulesWithQuiz = data.modules.filter(module => module.hasModuleQuiz && module.moduleQuiz).length;\r\n    \r\n    items.push({\r\n      id: 'quizzes',\r\n      label: 'Quiz',\r\n      status: (chaptersWithQuiz > 0 || modulesWithQuiz > 0) ? 'complete' : 'warning',\r\n      description: `${chaptersWithQuiz} chapter quiz, ${modulesWithQuiz} module quiz`,\r\n      required: false\r\n    });\r\n\r\n    // Final exam validation\r\n    items.push({\r\n      id: 'final-exam',\r\n      label: 'Final Exam',\r\n      status: data.finalExam ? 'complete' : 'warning',\r\n      description: data.finalExam \r\n        ? `${data.finalExam.questions.length} pertanyaan` \r\n        : 'Final exam belum dibuat',\r\n      required: false\r\n    });\r\n\r\n    return items;\r\n  };\r\n\r\n  const validationItems = getValidationItems();\r\n  const requiredItems = validationItems.filter(item => item.required);\r\n  const completedRequired = requiredItems.filter(item => item.status === 'complete').length;\r\n  const canPublish = completedRequired === requiredItems.length;\r\n  \r\n  const allCompleted = validationItems.filter(item => item.status === 'complete').length;\r\n  const completionPercentage = Math.round((allCompleted / validationItems.length) * 100);\r\n\r\n  const getCourseStats = () => {\r\n    const totalChapters = data.modules.reduce((acc, module) => acc + module.chapters.length, 0);\r\n    const totalQuizzes = data.modules.reduce((acc, module) => {\r\n      const chapterQuizzes = module.chapters.filter(c => c.hasChapterQuiz).length;\r\n      const moduleQuiz = module.hasModuleQuiz ? 1 : 0;\r\n      return acc + chapterQuizzes + moduleQuiz;\r\n    }, 0) + (data.finalExam ? 1 : 0);\r\n    \r\n    const estimatedDuration = data.modules.reduce((acc, module) => \r\n      acc + module.chapters.reduce((chapterAcc, chapter) =>\r\n        chapterAcc + Math.ceil((chapter.content as any[]).filter(block => block.type === 'text').reduce((textAcc, block) => textAcc + block.value.length, 0) / 1000) * 5, 0\r\n      ), 0\r\n    );\r\n\r\n    return {\r\n      modules: data.modules.length,\r\n      chapters: totalChapters,\r\n      quizzes: totalQuizzes,\r\n      estimatedDuration: Math.max(estimatedDuration, 30) // minimum 30 minutes\r\n    };\r\n  };\r\n\r\n  const stats = getCourseStats();\r\n\r\n  const handlePublish = async () => {\r\n    if (!canPublish) {\r\n      toast.error('Lengkapi semua item yang wajib diisi terlebih dahulu');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      await onPublish();\r\n      toast.success('Course berhasil dipublikasi!');\r\n    } catch (error) {\r\n      toast.error('Gagal mempublikasi course');\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header */}\r\n      <div className=\"text-center space-y-2\">\r\n        <div className={cn(\r\n          \"w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\",\r\n          canPublish ? \"bg-green-100 text-green-600\" : \"bg-orange-100 text-orange-600\"\r\n        )}>\r\n          {canPublish ? (\r\n            <Rocket className=\"w-8 h-8\" />\r\n          ) : (\r\n            <AlertCircle className=\"w-8 h-8\" />\r\n          )}\r\n        </div>\r\n        <h3 className=\"text-2xl font-bold\">\r\n          {canPublish ? 'Siap untuk Dipublikasi!' : 'Hampir Selesai'}\r\n        </h3>\r\n        <p className=\"text-muted-foreground\">\r\n          {canPublish \r\n            ? 'Course Anda sudah siap untuk dipublikasi dan dapat diakses oleh siswa'\r\n            : 'Lengkapi beberapa item berikut untuk mempublikasi course'\r\n          }\r\n        </p>\r\n      </div>\r\n\r\n      {/* Progress Overview */}\r\n      <Card>\r\n        <CardHeader>\r\n          <div className=\"flex items-center justify-between\">\r\n            <div>\r\n              <CardTitle className=\"flex items-center space-x-2\">\r\n                <Target className=\"w-5 h-5\" />\r\n                <span>Progress Kelengkapan</span>\r\n              </CardTitle>\r\n              <CardDescription>\r\n                {allCompleted} dari {validationItems.length} item selesai\r\n              </CardDescription>\r\n            </div>\r\n            <div className=\"text-right\">\r\n              <div className=\"text-2xl font-bold\">{completionPercentage}%</div>\r\n              <div className=\"text-sm text-muted-foreground\">Selesai</div>\r\n            </div>\r\n          </div>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <Progress value={completionPercentage} className=\"mb-4\" />\r\n          \r\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\r\n            <div className=\"text-center\">\r\n              <div className=\"flex items-center justify-center w-10 h-10 bg-blue-100 text-blue-600 rounded-full mx-auto mb-2\">\r\n                <BookOpen className=\"w-5 h-5\" />\r\n              </div>\r\n              <div className=\"text-sm font-medium\">{stats.modules}</div>\r\n              <div className=\"text-xs text-muted-foreground\">Modul</div>\r\n            </div>\r\n            \r\n            <div className=\"text-center\">\r\n              <div className=\"flex items-center justify-center w-10 h-10 bg-green-100 text-green-600 rounded-full mx-auto mb-2\">\r\n                <FileText className=\"w-5 h-5\" />\r\n              </div>\r\n              <div className=\"text-sm font-medium\">{stats.chapters}</div>\r\n              <div className=\"text-xs text-muted-foreground\">Chapter</div>\r\n            </div>\r\n            \r\n            <div className=\"text-center\">\r\n              <div className=\"flex items-center justify-center w-10 h-10 bg-purple-100 text-purple-600 rounded-full mx-auto mb-2\">\r\n                <HelpCircle className=\"w-5 h-5\" />\r\n              </div>\r\n              <div className=\"text-sm font-medium\">{stats.quizzes}</div>\r\n              <div className=\"text-xs text-muted-foreground\">Quiz</div>\r\n            </div>\r\n            \r\n            <div className=\"text-center\">\r\n              <div className=\"flex items-center justify-center w-10 h-10 bg-orange-100 text-orange-600 rounded-full mx-auto mb-2\">\r\n                <Clock className=\"w-5 h-5\" />\r\n              </div>\r\n              <div className=\"text-sm font-medium\">{stats.estimatedDuration}</div>\r\n              <div className=\"text-xs text-muted-foreground\">Menit</div>\r\n            </div>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Validation Checklist */}\r\n      <Card>\r\n        <CardHeader>\r\n          <div className=\"flex items-center justify-between\">\r\n            <CardTitle className=\"flex items-center space-x-2\">\r\n              <CheckCircle className=\"w-5 h-5\" />\r\n              <span>Checklist Publikasi</span>\r\n            </CardTitle>\r\n            <Button \r\n              variant=\"ghost\" \r\n              size=\"sm\"\r\n              onClick={() => setShowDetails(!showDetails)}\r\n            >\r\n              {showDetails ? 'Sembunyikan' : 'Lihat'} Detail\r\n            </Button>\r\n          </div>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <div className=\"space-y-3\">\r\n            {validationItems.map((item) => (\r\n              <div key={item.id} className=\"flex items-start space-x-3\">\r\n                <div className={cn(\r\n                  \"w-5 h-5 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5\",\r\n                  item.status === 'complete' ? \"bg-green-100 text-green-600\" :\r\n                  item.status === 'warning' ? \"bg-orange-100 text-orange-600\" :\r\n                  \"bg-gray-100 text-gray-400\"\r\n                )}>\r\n                  {item.status === 'complete' ? (\r\n                    <CheckCircle className=\"w-3 h-3\" />\r\n                  ) : item.status === 'warning' ? (\r\n                    <AlertCircle className=\"w-3 h-3\" />\r\n                  ) : (\r\n                    <div className=\"w-2 h-2 bg-current rounded-full\" />\r\n                  )}\r\n                </div>\r\n                \r\n                <div className=\"flex-1 min-w-0\">\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <span className={cn(\r\n                      \"text-sm font-medium\",\r\n                      item.status === 'complete' ? \"text-green-700\" :\r\n                      item.status === 'warning' ? \"text-orange-700\" :\r\n                      \"text-gray-500\"\r\n                    )}>\r\n                      {item.label}\r\n                    </span>\r\n                    {item.required && (\r\n                      <Badge variant=\"destructive\" className=\"text-xs px-1 py-0\">\r\n                        Wajib\r\n                      </Badge>\r\n                    )}\r\n                  </div>\r\n                  \r\n                  {showDetails && (\r\n                    <p className=\"text-xs text-muted-foreground mt-1\">\r\n                      {item.description}\r\n                    </p>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Course Preview */}\r\n      <Card>\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center space-x-2\">\r\n            <Eye className=\"w-5 h-5\" />\r\n            <span>Preview Course</span>\r\n          </CardTitle>\r\n          <CardDescription>\r\n            Begini tampilan course Anda untuk siswa\r\n          </CardDescription>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <div className=\"border rounded-lg p-4 space-y-4\">\r\n            {/* Course Header */}\r\n            <div className=\"flex items-start space-x-4\">\r\n              {data.coverImage ? (\r\n                <img \r\n                  src={typeof data.coverImage === 'string' \r\n                  ? data.coverImage \r\n                  : URL.createObjectURL(data.coverImage)}\r\n                  alt={data.name}\r\n                  className=\"w-20 h-20 object-cover rounded-lg\"\r\n                />\r\n              ) : (\r\n                <div className=\"w-20 h-20 bg-muted rounded-lg flex items-center justify-center\">\r\n                  <Image className=\"w-8 h-8 text-muted-foreground\" />\r\n                </div>\r\n              )}\r\n              \r\n              <div className=\"flex-1\">\r\n                <h4 className=\"font-semibold text-lg\">{data.name || 'Nama Course'}</h4>\r\n                <p className=\"text-sm text-muted-foreground mb-2\">\r\n                  {data.description || 'Deskripsi course'}\r\n                </p>\r\n                \r\n                <div className=\"flex items-center space-x-4 text-xs text-muted-foreground\">\r\n                  <div className=\"flex items-center space-x-1\">\r\n                    <Code className=\"w-3 h-3\" />\r\n                    <span>{data.courseCode || 'COURSE-CODE'}</span>\r\n                  </div>\r\n                  <div className=\"flex items-center space-x-1\">\r\n                    <BookOpen className=\"w-3 h-3\" />\r\n                    <span>{stats.modules} Modul</span>\r\n                  </div>\r\n                  <div className=\"flex items-center space-x-1\">\r\n                    <Clock className=\"w-3 h-3\" />\r\n                    <span>~{stats.estimatedDuration} Menit</span>\r\n                  </div>\r\n                  {data.startDate && (\r\n                    <div className=\"flex items-center space-x-1\">\r\n                      <Calendar className=\"w-3 h-3\" />\r\n                      <span>{new Date(data.startDate).toLocaleDateString()}</span>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </div>\r\n            \r\n            <Separator />\r\n            \r\n            {/* Module Structure Preview */}\r\n            <div className=\"space-y-2\">\r\n              <h5 className=\"font-medium text-sm\">Struktur Course:</h5>\r\n              {data.modules.length > 0 ? (\r\n                <div className=\"space-y-2\">\r\n                  {data.modules.slice(0, 3).map((module, index) => (\r\n                    <div key={module.id} className=\"text-sm\">\r\n                      <div className=\"font-medium\">\r\n                        {index + 1}. {module.name}\r\n                      </div>\r\n                      <div className=\"ml-4 text-xs text-muted-foreground\">\r\n                        {module.chapters.length} chapter\r\n                        {module.hasModuleQuiz && ' • Quiz modul'}\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                  {data.modules.length > 3 && (\r\n                    <div className=\"text-xs text-muted-foreground\">\r\n                      ... dan {data.modules.length - 3} modul lainnya\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              ) : (\r\n                <p className=\"text-sm text-muted-foreground italic\">\r\n                  Belum ada modul\r\n                </p>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Warnings */}\r\n      {!canPublish && (\r\n        <Alert>\r\n          <AlertCircle className=\"h-4 w-4\" />\r\n          <AlertDescription>\r\n            <strong>Perhatian:</strong> Beberapa item wajib belum lengkap. \r\n            Course tidak dapat dipublikasi sampai semua item wajib diselesaikan.\r\n          </AlertDescription>\r\n        </Alert>\r\n      )}\r\n\r\n      {/* Action Buttons */}\r\n      <div className=\"flex items-center justify-between pt-6\">\r\n        <div className=\"text-sm text-muted-foreground\">\r\n          {canPublish \r\n            ? 'Course siap dipublikasi dan dapat diakses siswa'\r\n            : `${completedRequired}/${requiredItems.length} item wajib selesai`\r\n          }\r\n        </div>\r\n        \r\n        <div className=\"flex items-center space-x-3\">\r\n          <Button variant=\"outline\" disabled={isPublishing}>\r\n            <Eye className=\"w-4 h-4 mr-2\" />\r\n            Preview\r\n          </Button>\r\n          \r\n          <Button \r\n            onClick={handlePublish}\r\n            disabled={!canPublish || isPublishing}\r\n            className=\"min-w-[120px]\"\r\n          >\r\n            {isPublishing ? (\r\n              <>\r\n                <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\" />\r\n                Publishing...\r\n              </>\r\n            ) : (\r\n              <>\r\n                <Rocket className=\"w-4 h-4 mr-2\" />\r\n                Publikasi Course\r\n              </>\r\n            )}\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}", "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\course\\steps\\student-experience-step.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\course\\steps\\tuition-financing-step.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\dynamic-content-editor.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\file-uploader.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\form-card-skeleton.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\icons.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\kbar\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\kbar\\render-result.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\kbar\\result-item.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\kbar\\use-theme-switching.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\landingpage\\CallToActionSection.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\landingpage\\CourseCard.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\landingpage\\CourseGrid.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\landingpage\\CourseHeroSection.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\landingpage\\CourseLandingPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\landingpage\\FeaturesSection.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\landingpage\\Footer.tsx", ["1514"], [], "'use client';\r\n\r\nimport Image from 'next/image';\r\n\r\nexport default function Footer() {\r\n  return (\r\n    <footer className=\"bg-white border-t border-gray-200 py-6\">\r\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\r\n        <div className=\"flex items-center justify-center space-x-2 text-gray-600\">\r\n          <span className=\"text-sm\">Powered by</span>\r\n          <img\r\n            src=\"https://cdn.terang.ai/images/logo/logo-terang-ai.svg\"\r\n            alt=\"Terang AI\"\r\n            className=\"h-5 object-contain\"\r\n          />\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  );\r\n}", "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\landingpage\\LandingNavbar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\layout\\app-sidebar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\layout\\cta-github.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\layout\\header.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\layout\\page-container.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\layout\\providers.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\layout\\ThemeToggle\\theme-provider.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\layout\\ThemeToggle\\theme-toggle.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\layout\\user-nav.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\live-preview.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\certificate-template.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\chapter-section.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\content-item.tsx", ["1515", "1516"], [], "import React from 'react';\r\nimport ReactMarkdown from 'react-markdown';\r\nimport remarkGfm from 'remark-gfm';\r\nimport { Card, CardContent } from '@/components/ui/card';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport {\r\n  Play,\r\n  FileText,\r\n  BookMarked,\r\n  CheckCircle2,\r\n  ChevronUp,\r\n  ChevronDown,\r\n  Timer,\r\n  Download,\r\n  Eye,\r\n  ExternalLink,\r\n  Image\r\n} from 'lucide-react';\r\nimport { ContentItemProps } from '@/types/lms';\r\n\r\nexport const ContentItem: React.FC<ContentItemProps> = ({\r\n  content,\r\n  onToggleComplete,\r\n  isExpanded,\r\n  onToggleExpand\r\n}) => {\r\n  // Console log for debugging content object\r\n  console.log('ContentItem received content:', content);\r\n\r\n  const extractFirstMarkdownHeader = (markdown: string): string | null => {\r\n    const lines = markdown.split('\\n');\r\n    for (const line of lines) {\r\n      const match = line.match(/^#{1,4}\\s+(.*)$/);\r\n      if (match && match[1]) {\r\n        return match[1].trim();\r\n      }\r\n    }\r\n    return null;\r\n  };\r\n\r\n  const displayTitle =\r\n    content.title ||\r\n    (typeof content.content === 'object' &&\r\n    content.content !== null &&\r\n    'value' in content.content &&\r\n    typeof content.content.value === 'string'\r\n      ? extractFirstMarkdownHeader(content.content.value)\r\n      : null) ||\r\n    'No Title';\r\n\r\n  const handleDownloadMarkdownAsPDF = () => {\r\n    // Create a new window for printing\r\n    const printWindow = window.open('', '_blank');\r\n    if (!printWindow) return;\r\n\r\n    // Create HTML content for the markdown\r\n    const htmlContent = `\r\n      <!DOCTYPE html>\r\n      <html>\r\n        <head>\r\n          <title>${content.title}</title>\r\n          <style>\r\n            @media print {\r\n              @page {\r\n                size: A4;\r\n                margin: 2cm;\r\n              }\r\n              body {\r\n                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\r\n                line-height: 1.6;\r\n                color: #333;\r\n              }\r\n            }\r\n            body {\r\n              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\r\n              line-height: 1.6;\r\n              color: #333;\r\n              max-width: 800px;\r\n              margin: 0 auto;\r\n              padding: 20px;\r\n            }\r\n            h1 { color: #2d3748; font-size: 2em; margin-bottom: 1em; }\r\n            h2 { color: #4a5568; font-size: 1.5em; margin: 1.5em 0 0.5em; }\r\n            h3 { color: #4a5568; font-size: 1.25em; margin: 1.2em 0 0.5em; }\r\n            h4 { color: #718096; font-size: 1.1em; margin: 1em 0 0.5em; }\r\n            p { margin-bottom: 1em; }\r\n            ul, ol { margin-bottom: 1em; padding-left: 2em; }\r\n            li { margin-bottom: 0.25em; }\r\n            blockquote {\r\n              border-left: 4px solid #3182ce;\r\n              background: #ebf8ff;\r\n              padding: 1em;\r\n              margin: 1em 0;\r\n              font-style: italic;\r\n            }\r\n            code {\r\n              background: #f7fafc;\r\n              padding: 0.2em 0.4em;\r\n              border-radius: 3px;\r\n              font-family: 'Courier New', monospace;\r\n              font-size: 0.9em;\r\n            }\r\n            pre {\r\n              background: #2d3748;\r\n              color: #f7fafc;\r\n              padding: 1em;\r\n              border-radius: 5px;\r\n              overflow-x: auto;\r\n              margin: 1em 0;\r\n            }\r\n            pre code {\r\n              background: none;\r\n              padding: 0;\r\n              color: inherit;\r\n            }\r\n            table {\r\n              border-collapse: collapse;\r\n              width: 100%;\r\n              margin: 1em 0;\r\n            }\r\n            th, td {\r\n              border: 1px solid #e2e8f0;\r\n              padding: 0.5em;\r\n              text-align: left;\r\n            }\r\n            th {\r\n              background: #f7fafc;\r\n              font-weight: 600;\r\n            }\r\n            hr {\r\n              border: none;\r\n              height: 1px;\r\n              background: #e2e8f0;\r\n              margin: 2em 0;\r\n            }\r\n            strong { font-weight: 600; }\r\n            em { font-style: italic; }\r\n          </style>\r\n        </head>\r\n        <body>\r\n          <h1>${content.title}</h1>\r\n          <div id=\"markdown-content\"></div>\r\n        </body>\r\n      </html>\r\n    `;\r\n\r\n    printWindow.document.write(htmlContent);\r\n    printWindow.document.close();\r\n\r\n    // Convert markdown to HTML and insert it\r\n    const markdownDiv = printWindow.document.getElementById('markdown-content');\r\n    if (markdownDiv) {\r\n      // Simple markdown to HTML conversion for basic formatting\r\n      let htmlText = '';\r\n      \r\n      // Check if content.content is a string, ContentBlock object, or ContentBlock array\r\n      if (typeof content.content === 'string') {\r\n        htmlText = content.content;\r\n      } else if (\r\n        typeof content.content === 'object' &&\r\n        content.content !== null &&\r\n        'value' in content.content &&\r\n        typeof content.content.value === 'string'\r\n      ) {\r\n        htmlText = content.content.value;\r\n      } else if (Array.isArray(content.content)) {\r\n        // Convert ContentBlock array to string\r\n        htmlText = content.content\r\n          .map((block) => (block.type === 'text' ? block.value : ''))\r\n          .join('');\r\n      }\r\n      \r\n      // Headers\r\n      htmlText = htmlText.replace(/^### (.*$)/gim, '<h3>$1</h3>');\r\n      htmlText = htmlText.replace(/^## (.*$)/gim, '<h2>$1</h2>');\r\n      htmlText = htmlText.replace(/^# (.*$)/gim, '<h1>$1</h1>');\r\n      \r\n      // Bold and italic\r\n      htmlText = htmlText.replace(/\\*\\*(.*)\\*\\*/gim, '<strong>$1</strong>');\r\n      htmlText = htmlText.replace(/\\*(.*)\\*/gim, '<em>$1</em>');\r\n      \r\n      // Lists\r\n      htmlText = htmlText.replace(/^\\* (.*$)/gim, '<li>$1</li>');\r\n      htmlText = htmlText.replace(/(<li>.*<\\/li>)/gim, '<ul>$1</ul>');\r\n      htmlText = htmlText.replace(/^\\d+\\. (.*$)/gim, '<li>$1</li>');\r\n      \r\n      // Line breaks to paragraphs\r\n      htmlText = htmlText.replace(/\\n\\n/g, '</p><p>');\r\n      htmlText = '<p>' + htmlText + '</p>';\r\n      \r\n      // Clean up empty paragraphs\r\n      htmlText = htmlText.replace(/<p><\\/p>/g, '');\r\n      \r\n      markdownDiv.innerHTML = htmlText;\r\n    }\r\n\r\n    // Wait for content to load then print\r\n    setTimeout(() => {\r\n      printWindow.focus();\r\n      printWindow.print();\r\n      printWindow.close();\r\n    }, 250);\r\n  };\r\n  const getContentIcon = () => {\r\n    switch (content.type) {\r\n      case 'video':\r\n        return <Play className='h-4 w-4 text-red-500' />;\r\n      case 'pdf':\r\n        return <FileText className='h-4 w-4 text-red-600' />;\r\n      case 'zoom-recording':\r\n        return <Play className='h-4 w-4 text-blue-500' />;\r\n      case 'image':\r\n        return <Image className='h-4 w-4 text-green-500' />;\r\n      default:\r\n        return <BookMarked className='h-4 w-4 text-blue-500' />;\r\n    }\r\n  };\r\n\r\n  const getContentTypeLabel = () => {\r\n    switch (content.type) {\r\n      case 'video':\r\n        return 'Video';\r\n      case 'pdf':\r\n        return 'PDF Document';\r\n      case 'zoom-recording':\r\n        return 'Zoom Recording';\r\n      case 'image':\r\n        return 'Image';\r\n      default:\r\n        return 'Reading Material';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Card id={`content-${content.id}`} className='my-2 ml-6 border-l-4 border-l-blue-200 scroll-mt-20'>\r\n      <CardContent className='py-3'>\r\n        <div className='flex flex-col'>\r\n          <div\r\n            className='flex cursor-pointer items-center justify-between'\r\n            onClick={onToggleExpand}\r\n          >\r\n            <div className='flex flex-1 items-center space-x-3'>\r\n              {getContentIcon()}\r\n              <div className='flex-1'>\r\n                <span className='text-sm font-medium'>{displayTitle}</span>\r\n                <div className='mt-1 flex items-center space-x-2'>\r\n                  <Badge variant='outline' className='text-xs'>\r\n                    {getContentTypeLabel()}\r\n                  </Badge>\r\n                  {content.duration && (\r\n                    <Badge variant='outline' className='text-xs'>\r\n                      <Timer className='mr-1 h-3 w-3' />\r\n                      {content.duration} min\r\n                    </Badge>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className='flex items-center space-x-2'>\r\n              {isExpanded ? (\r\n                <ChevronUp className='h-4 w-4 text-gray-400' />\r\n              ) : (\r\n                <ChevronDown className='h-4 w-4 text-gray-400' />\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          {isExpanded && (\r\n            <div className='mt-4 border-t pt-4 pl-7'>\r\n              {content.type === 'text' ? (\r\n                <div className='space-y-4'>\r\n                  <div className='prose prose-sm max-w-none text-gray-700'>\r\n                    <ReactMarkdown\r\n                      remarkPlugins={[remarkGfm]}\r\n                      components={{\r\n                        h1: ({ node, ...props }) => (\r\n                          <h1\r\n                            className='mb-4 text-2xl font-bold text-gray-900'\r\n                            {...props}\r\n                          />\r\n                        ),\r\n                        h2: ({ node, ...props }) => (\r\n                          <h2\r\n                            className='mb-3 text-xl font-semibold text-gray-800'\r\n                            {...props}\r\n                          />\r\n                        ),\r\n                        h3: ({ node, ...props }) => (\r\n                          <h3\r\n                            className='mb-2 text-lg font-semibold text-gray-800'\r\n                            {...props}\r\n                          />\r\n                        ),\r\n                        h4: ({ node, ...props }) => (\r\n                          <h4\r\n                            className='mb-2 text-base font-semibold text-gray-700'\r\n                            {...props}\r\n                          />\r\n                        ),\r\n                        p: ({ node, ...props }) => (\r\n                          <p className='mb-3 leading-relaxed' {...props} />\r\n                        ),\r\n                        ul: ({ node, ...props }) => (\r\n                          <ul className='mb-3 ml-4 list-disc' {...props} />\r\n                        ),\r\n                        ol: ({ node, ...props }) => (\r\n                          <ol className='mb-3 ml-4 list-decimal' {...props} />\r\n                        ),\r\n                        li: ({ node, ...props }) => (\r\n                          <li className='mb-1' {...props} />\r\n                        ),\r\n                        blockquote: ({ node, ...props }) => (\r\n                          <blockquote\r\n                            className='mb-4 rounded-r border-l-4 border-blue-200 bg-blue-50 py-2 pl-4 italic'\r\n                            {...props}\r\n                          />\r\n                        ),\r\n                        code: ({ node, className, children, ...props }) => {\r\n                          const match = /language-(\\w+)/.exec(className || '');\r\n                          const isInline = !match;\r\n                          return isInline ? (\r\n                            <code\r\n                              className='rounded bg-gray-100 px-1 py-0.5 font-mono text-sm'\r\n                              {...props}\r\n                            >\r\n                              {children}\r\n                            </code>\r\n                          ) : (\r\n                            <code\r\n                              className='block overflow-x-auto rounded bg-gray-900 p-4 font-mono text-sm text-gray-100'\r\n                              {...props}\r\n                            >\r\n                              {children}\r\n                            </code>\r\n                          );\r\n                        },\r\n                        pre: ({ node, ...props }) => (\r\n                          <pre className='mb-4' {...props} />\r\n                        ),\r\n                        table: ({ node, ...props }) => (\r\n                          <div className='mb-4 overflow-x-auto'>\r\n                            <table\r\n                              className='min-w-full rounded border border-gray-200'\r\n                              {...props}\r\n                            />\r\n                          </div>\r\n                        ),\r\n                        thead: ({ node, ...props }) => (\r\n                          <thead className='bg-gray-50' {...props} />\r\n                        ),\r\n                        th: ({ node, ...props }) => (\r\n                          <th\r\n                            className='border border-gray-200 px-3 py-2 text-left font-semibold'\r\n                            {...props}\r\n                          />\r\n                        ),\r\n                        td: ({ node, ...props }) => (\r\n                          <td\r\n                            className='border border-gray-200 px-3 py-2'\r\n                            {...props}\r\n                          />\r\n                        ),\r\n                        hr: ({ node, ...props }) => (\r\n                          <hr className='my-6 border-gray-300' {...props} />\r\n                        ),\r\n                        strong: ({ node, ...props }) => (\r\n                          <strong\r\n                            className='font-semibold text-gray-900'\r\n                            {...props}\r\n                          />\r\n                        ),\r\n                        em: ({ node, ...props }) => (\r\n                          <em className='italic' {...props} />\r\n                        )\r\n                      }}\r\n                    >\r\n                      {typeof content.content === 'string'\r\n                        ? content.content\r\n                        : typeof content.content === 'object' &&\r\n                            content.content !== null &&\r\n                            'value' in content.content &&\r\n                            typeof content.content.value === 'string'\r\n                          ? content.content.value\r\n                          : Array.isArray(content.content)\r\n                            ? content.content\r\n                                .map((block) =>\r\n                                  block.type === 'text' ? block.value : ''\r\n                                )\r\n                                .join('')\r\n                            : ''}\r\n                    </ReactMarkdown>\r\n                  </div>\r\n                  <Button\r\n                    size='sm'\r\n                    variant='outline'\r\n                    className='border-blue-200 text-blue-600 hover:bg-blue-50'\r\n                    onClick={handleDownloadMarkdownAsPDF}\r\n                  >\r\n                    <Download className='mr-2 h-4 w-4' />\r\n                    Download as PDF\r\n                  </Button>\r\n                </div>\r\n              ) : content.type === 'pdf' ? (\r\n                <div className='space-y-4'>\r\n                  <iframe\r\n                    src={`${typeof content.content === 'string' ? content.content : (typeof content.content === 'object' && content.content !== null && 'value' in content.content && typeof content.content.value === 'string') ? content.content.value : ''}#toolbar=0&navpanes=0`}\r\n                    className='w-full h-96 rounded border'\r\n                    title={content.title}\r\n                  />\r\n                  <div className='flex space-x-2'>\r\n                    <Button\r\n                      size='sm'\r\n                      variant='outline'\r\n                      className='border-blue-200 text-blue-600 hover:bg-blue-50'\r\n                      onClick={() => {\r\n                        const pdfUrl = typeof content.content === 'string' ? content.content :\r\n                          (typeof content.content === 'object' && content.content !== null && 'value' in content.content && typeof content.content.value === 'string') ? content.content.value : '';\r\n                        if (pdfUrl) window.open(pdfUrl, '_blank');\r\n                      }}\r\n                    >\r\n                      <Eye className='mr-2 h-4 w-4' />\r\n                      Open in New Tab\r\n                    </Button>\r\n                    <Button\r\n                      size='sm'\r\n                      variant='outline'\r\n                      className='text-gray-600 hover:bg-gray-50'\r\n                      onClick={() => {\r\n                        const pdfUrl = typeof content.content === 'string' ? content.content :\r\n                          (typeof content.content === 'object' && content.content !== null && 'value' in content.content && typeof content.content.value === 'string') ? content.content.value : '';\r\n                        if (pdfUrl) {\r\n                          const link = document.createElement('a');\r\n                          link.href = pdfUrl;\r\n                          link.download = content.title || 'document.pdf';\r\n                          document.body.appendChild(link);\r\n                          link.click();\r\n                          document.body.removeChild(link);\r\n                        }\r\n                      }}\r\n                    >\r\n                      <Download className='mr-2 h-4 w-4' />\r\n                      Download\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n              ) : content.type === 'image' ? (\r\n                <div className='space-y-4'>\r\n                  <img\r\n                    src={\r\n                      typeof content.content === 'string'\r\n                        ? content.content\r\n                        : typeof content.content === 'object' &&\r\n                          content.content !== null &&\r\n                          'value' in content.content &&\r\n                          typeof content.content.value === 'string'\r\n                        ? content.content.value\r\n                        : ''\r\n                    }\r\n                    alt={content.title || 'Image'}\r\n                    className='max-w-full h-auto rounded border'\r\n                  />\r\n                  <div className='flex space-x-2'>\r\n                    <Button\r\n                      size='sm'\r\n                      variant='outline'\r\n                      className='border-blue-200 text-blue-600 hover:bg-blue-50'\r\n                      onClick={() => {\r\n                        const imageUrl = typeof content.content === 'string' ? content.content :\r\n                          (typeof content.content === 'object' && content.content !== null && 'value' in content.content && typeof content.content.value === 'string') ? content.content.value : '';\r\n                        if (imageUrl) window.open(imageUrl, '_blank');\r\n                      }}\r\n                    >\r\n                      <ExternalLink className='mr-2 h-4 w-4' />\r\n                      Open in New Tab\r\n                    </Button>\r\n                    <Button\r\n                      size='sm'\r\n                      variant='outline'\r\n                      className='text-gray-600 hover:bg-gray-50'\r\n                      onClick={() => {\r\n                        const imageUrl = typeof content.content === 'string' ? content.content :\r\n                          (typeof content.content === 'object' && content.content !== null && 'value' in content.content && typeof content.content.value === 'string') ? content.content.value : '';\r\n                        if (imageUrl) {\r\n                          const link = document.createElement('a');\r\n                          link.href = imageUrl;\r\n                          link.download = content.title || 'image.jpg';\r\n                          document.body.appendChild(link);\r\n                          link.click();\r\n                          document.body.removeChild(link);\r\n                        }\r\n                      }}\r\n                    >\r\n                      <Download className='mr-2 h-4 w-4' />\r\n                      Download\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n              ) : (content.type === 'video' || content.type === 'zoom-recording') ? (\r\n                <div className='space-y-4'>\r\n                  <div className='aspect-video w-full overflow-hidden rounded-lg bg-gray-100'>\r\n                    {(() => {\r\n                      const videoUrl = typeof content.content === 'string' ? content.content :\r\n                                       (typeof content.content === 'object' && content.content !== null && 'value' in content.content && typeof content.content.value === 'string') ? content.content.value : '';\r\n\r\n                      if (!videoUrl) {\r\n                        return (\r\n                          <div className='flex h-full w-full items-center justify-center text-center text-gray-500'>\r\n                            No video URL provided.\r\n                          </div>\r\n                        );\r\n                      }\r\n\r\n                      if (videoUrl.includes('youtube.com/watch?v=') || videoUrl.includes('youtu.be/')) {\r\n                        const youtubeId = videoUrl.split('v=')[1]?.split('&')[0] || videoUrl.split('/').pop();\r\n                        return (\r\n                          <iframe\r\n                            className='h-full w-full'\r\n                            src={`https://www.youtube.com/embed/${youtubeId}`}\r\n                            frameBorder='0'\r\n                            allow='accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture'\r\n                            allowFullScreen\r\n                            title={content.title}\r\n                          ></iframe>\r\n                        );\r\n                      } else if (videoUrl.includes('vimeo.com/')) {\r\n                        const vimeoId = videoUrl.split('/').pop();\r\n                        return (\r\n                          <iframe\r\n                            className='h-full w-full'\r\n                            src={`https://player.vimeo.com/video/${vimeoId}`}\r\n                            frameBorder='0'\r\n                            allow='autoplay; fullscreen; picture-in-picture'\r\n                            allowFullScreen\r\n                            title={content.title}\r\n                          ></iframe>\r\n                        );\r\n                      } else {\r\n                        // Generic video tag for other direct video links\r\n                        return (\r\n                          <video\r\n                            controls\r\n                            className='h-full w-full'\r\n                            src={videoUrl}\r\n                            title={content.title}\r\n                          >\r\n                            Your browser does not support the video tag.\r\n                          </video>\r\n                        );\r\n                      }\r\n                    })()}\r\n                  </div>\r\n                  <div className='flex space-x-2'>\r\n                    <Button\r\n                      size='sm'\r\n                      variant='outline'\r\n                      className='border-blue-200 text-blue-600 hover:bg-blue-50'\r\n                      onClick={() => {\r\n                        const videoUrl = typeof content.content === 'string' ? content.content :\r\n                                         (typeof content.content === 'object' && content.content !== null && 'value' in content.content && typeof content.content.value === 'string') ? content.content.value : '';\r\n                        if (videoUrl) window.open(videoUrl, '_blank');\r\n                      }}\r\n                    >\r\n                      <ExternalLink className='mr-2 h-4 w-4' />\r\n                      Open in New Tab\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n              ) : (\r\n                <div className='space-y-4'>\r\n                  <div className='flex aspect-video items-center justify-center rounded-lg bg-gray-100'>\r\n                    <div className='text-center'>\r\n                      <Play className='mx-auto mb-2 h-12 w-12 text-gray-400' />\r\n                      <p className='text-sm text-gray-500'>\r\n                        Unsupported Media Type or Invalid Content.\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              )}\r\n              \r\n              {/* Mark as Complete Button - Moved to bottom */}\r\n              <div className='mt-4 pt-4 border-t'>\r\n                <Button\r\n                  size='sm'\r\n                  variant={content.isCompleted ? 'default' : 'outline'}\r\n                  className={`min-w-[120px] ${\r\n                    content.isCompleted\r\n                      ? 'bg-green-600 text-white hover:bg-green-700'\r\n                      : 'text-gray-600 hover:bg-gray-50'\r\n                  }`}\r\n                  onClick={(e) => {\r\n                    e.stopPropagation();\r\n                    onToggleComplete();\r\n                  }}\r\n                >\r\n                  <CheckCircle2 className='mr-2 h-4 w-4' />\r\n                  {content.isCompleted ? 'Completed' : 'Mark Complete'}\r\n                </Button>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};", "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\course-detail-tabs.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\course-preview-card.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\course-success-modal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\final-exam\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\final-exam\\option.tsx", ["1517"], [], "import React from 'react';\r\nimport { ContentBlock } from '@/components/dynamic-content-editor';\r\n\r\ninterface OptionProps {\r\n  option: string | { content: ContentBlock[]; isCorrect: boolean };\r\n  index: number;\r\n  questionId: string;\r\n  selectedAnswer: number | string | number[] | undefined;\r\n  onAnswerChange: (questionId: string, answer: number | string | number[]) => void;\r\n  type: 'radio' | 'checkbox';\r\n  disabled?: boolean;\r\n  showResults?: boolean;\r\n  correctAnswer?: number | string | number[];\r\n  isCorrect?: boolean;\r\n}\r\n\r\nexport const Option: React.FC<OptionProps> = ({\r\n  option,\r\n  index,\r\n  questionId,\r\n  selectedAnswer,\r\n  onAnswerChange,\r\n  type = 'radio',\r\n  disabled = false,\r\n  showResults = false,\r\n  correctAnswer,\r\n  isCorrect\r\n}) => {\r\n  const isSelected = type === 'radio' \r\n    ? selectedAnswer === index \r\n    : Array.isArray(selectedAnswer) && selectedAnswer.includes(index);\r\n\r\n  const isCorrectOption = type === 'radio' \r\n    ? correctAnswer === index \r\n    : Array.isArray(correctAnswer) && correctAnswer.includes(index);\r\n\r\n  const handleChange = () => {\r\n    if (disabled) return;\r\n    \r\n    if (type === 'radio') {\r\n      onAnswerChange(questionId, index);\r\n    } else {\r\n      // Handle checkbox logic for multiple selection\r\n      const currentAnswers = Array.isArray(selectedAnswer) ? selectedAnswer : [];\r\n      const newAnswers = isSelected \r\n        ? currentAnswers.filter((ans) => ans !== index)\r\n        : [...currentAnswers, index];\r\n      onAnswerChange(questionId, newAnswers);\r\n    }\r\n  };\r\n\r\n  const getOptionStyles = () => {\r\n    if (!showResults) {\r\n      // Normal mode - just show selected state\r\n      return isSelected \r\n        ? 'border-blue-500 bg-blue-50' \r\n        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50';\r\n    }\r\n\r\n    // Results mode - show correct/incorrect highlighting\r\n    if (isCorrectOption) {\r\n      // This is the correct answer - always highlight green\r\n      return 'border-green-500 bg-green-50';\r\n    } else if (isSelected) {\r\n      // User selected this wrong answer - highlight red\r\n      return 'border-red-500 bg-red-50';\r\n    } else {\r\n      // Not selected, not correct - neutral\r\n      return 'border-gray-200 bg-gray-50';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <label \r\n      className={`\r\n        flex cursor-pointer items-start space-x-3 p-3 rounded-lg border-2 transition-all\r\n        ${getOptionStyles()}\r\n        ${disabled ? 'opacity-50 cursor-not-allowed' : ''}\r\n        ${showResults && isCorrectOption ? 'ring-2 ring-green-200' : ''}\r\n        ${showResults && isSelected && !isCorrectOption ? 'ring-2 ring-red-200' : ''}\r\n      `}\r\n    >\r\n      <input\r\n        type={type}\r\n        name={questionId}\r\n        value={index}\r\n        checked={isSelected}\r\n        onChange={handleChange}\r\n        disabled={disabled}\r\n        className={`\r\n          mt-0.5 h-4 w-4 shrink-0\r\n          ${type === 'radio' ? 'text-blue-600' : 'text-blue-600 rounded'}\r\n          ${disabled ? 'cursor-not-allowed' : 'cursor-pointer'}\r\n        `}\r\n      />\r\n      <span className={`text-sm leading-relaxed flex-1 ${disabled ? 'text-gray-500' : 'text-gray-900'}`}>\r\n        <span className=\"font-medium mr-2\">\r\n          {String.fromCharCode(65 + index)}.\r\n        </span>\r\n        {typeof option === 'string' ? (\r\n          option\r\n        ) : (\r\n          option.content.map((block, blockIndex) => (\r\n            <React.Fragment key={blockIndex}>\r\n              {block.type === 'text' && <span>{block.value}</span>}\r\n              {block.type === 'image' && block.value && (\r\n                <img src={block.value} alt={`Option image ${blockIndex}`} className=\"inline-block max-h-8 object-contain ml-1\" />\r\n              )}\r\n              {block.type === 'video' && <span>[Video: {block.value}]</span>}\r\n              {block.type === 'pdf' && <span>[PDF: {block.value}]</span>}\r\n              {block.type === 'zoom-recording' && <span>[Recording: {block.value}]</span>}\r\n            </React.Fragment>\r\n          ))\r\n        )}\r\n      </span>\r\n      \r\n      {/* Show correct/incorrect indicators in results mode */}\r\n      {showResults && (\r\n        <div className=\"flex items-center ml-2\">\r\n          {isCorrectOption && (\r\n            <span className=\"text-green-600 font-medium text-xs bg-green-100 px-2 py-1 rounded-full\">\r\n              ✓ Benar\r\n            </span>\r\n          )}\r\n          {isSelected && !isCorrectOption && (\r\n            <span className=\"text-red-600 font-medium text-xs bg-red-100 px-2 py-1 rounded-full\">\r\n              ✗ Salah\r\n            </span>\r\n          )}\r\n        </div>\r\n      )}\r\n    </label>\r\n  );\r\n};", "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\final-exam\\question-bank.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\final-exam\\question.tsx", ["1518", "1519"], [], "import React from 'react';\r\nimport { Card, CardContent } from '@/components/ui/card';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Option } from './option';\r\nimport { Question as QuestionType } from '@/types/lms';\r\n\r\ninterface QuestionProps {\r\n  question: QuestionType;\r\n  questionNumber: number;\r\n  totalQuestions: number;\r\n  selectedAnswer: number | string | number[] | undefined;\r\n  onAnswerChange: (questionId: string, answer: number | string | number[]) => void;\r\n  showResults?: boolean;\r\n  isCorrect?: boolean;\r\n  disabled?: boolean;\r\n}\r\n\r\nexport const Question: React.FC<QuestionProps> = ({\r\n  question,\r\n  questionNumber,\r\n  totalQuestions,\r\n  selectedAnswer,\r\n  onAnswerChange,\r\n  showResults = false,\r\n  isCorrect,\r\n  disabled = false\r\n}) => {\r\n  const getQuestionTypeLabel = (type: string) => {\r\n    switch (type) {\r\n      case 'multiple-choice':\r\n      case 'multiple_choice':\r\n        return 'Pilihan Ganda';\r\n      case 'true-false':\r\n      case 'true_false':\r\n        return 'Benar/Salah';\r\n      case 'essay':\r\n        return 'Esai';\r\n      default:\r\n        return type;\r\n    }\r\n  };\r\n\r\n  const handleEssayChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {\r\n    if (!disabled) {\r\n      onAnswerChange(question.id, e.target.value);\r\n    }\r\n  };\r\n\r\n  const handleTrueFalseChange = (value: string) => {\r\n    if (!disabled) {\r\n      onAnswerChange(question.id, value);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Card className={`\r\n      border-2 transition-all\r\n      ${showResults \r\n        ? (isCorrect ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50')\r\n        : 'border-gray-200'\r\n      }\r\n    `}>\r\n      <CardContent className=\"p-6\">\r\n        <div className=\"mb-4 flex items-center justify-between\">\r\n          <div className=\"flex items-center space-x-3\">\r\n            <Badge variant=\"outline\" className=\"bg-blue-50 text-blue-700 border-blue-200\">\r\n              Soal {questionNumber} dari {totalQuestions}\r\n            </Badge>\r\n            <Badge variant=\"secondary\" className=\"text-xs\">\r\n              {getQuestionTypeLabel(question.type)}\r\n            </Badge>\r\n          </div>\r\n          {showResults && (\r\n            <Badge \r\n              variant={isCorrect ? 'default' : 'destructive'}\r\n              className={isCorrect ? 'bg-green-600 hover:bg-green-700' : ''}\r\n            >\r\n              {isCorrect ? 'Benar' : 'Salah'}\r\n            </Badge>\r\n          )}\r\n        </div>\r\n\r\n        <div className=\"mb-6\">\r\n          <p className=\"text-lg leading-relaxed text-gray-900 whitespace-pre-wrap\">\r\n            {typeof question.question === 'string' ? (\r\n              question.question\r\n            ) : Array.isArray(question.question) ? (\r\n              question.question.map((block, index) => (\r\n                <React.Fragment key={index}>\r\n                  {block.type === 'text' && <span>{block.value}</span>}\r\n                  {block.type === 'image' && block.value && (\r\n                    <img src={block.value} alt={`Question image ${index}`} className=\"inline-block max-h-16 object-contain ml-2\" />\r\n                  )}\r\n                </React.Fragment>\r\n              ))\r\n            ) : (\r\n              <span>{String(question.question)}</span>\r\n            )}\r\n          </p>\r\n        </div>\r\n\r\n        {/* Multiple Choice Options */}\r\n        {(question.type === 'multiple-choice' || question.type === 'multiple_choice') && question.options && (\r\n          <div className=\"space-y-3\">\r\n            {question.options.map((option, index) => (\r\n              <Option\r\n                key={index}\r\n                option={option}\r\n                index={index}\r\n                questionId={question.id}\r\n                selectedAnswer={selectedAnswer}\r\n                onAnswerChange={onAnswerChange}\r\n                type=\"radio\"\r\n                disabled={disabled}\r\n                showResults={showResults}\r\n                correctAnswer={question.correctAnswer}\r\n                isCorrect={isCorrect}\r\n              />\r\n            ))}\r\n          </div>\r\n        )}\r\n\r\n        {/* True/False Options */}\r\n        {(question.type === 'true-false' || question.type === 'true_false') && (\r\n          <div className=\"space-y-3\">\r\n            {['true', 'false'].map((value, index) => {\r\n              const isSelected = selectedAnswer === value;\r\n              const isCorrectOption = question.correctAnswer === value;\r\n              \r\n              const getOptionStyles = () => {\r\n                if (!showResults) {\r\n                  return isSelected \r\n                    ? 'border-blue-500 bg-blue-50' \r\n                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50';\r\n                }\r\n                \r\n                if (isCorrectOption) {\r\n                  return 'border-green-500 bg-green-50';\r\n                } else if (isSelected) {\r\n                  return 'border-red-500 bg-red-50';\r\n                } else {\r\n                  return 'border-gray-200 bg-gray-50';\r\n                }\r\n              };\r\n              \r\n              return (\r\n                <label \r\n                  key={value}\r\n                  className={`\r\n                    flex cursor-pointer items-center justify-between p-3 rounded-lg border-2 transition-all\r\n                    ${getOptionStyles()}\r\n                    ${disabled ? 'opacity-50 cursor-not-allowed' : ''}\r\n                    ${showResults && isCorrectOption ? 'ring-2 ring-green-200' : ''}\r\n                    ${showResults && isSelected && !isCorrectOption ? 'ring-2 ring-red-200' : ''}\r\n                  `}\r\n                >\r\n                  <div className=\"flex items-center space-x-3\">\r\n                    <input\r\n                      type=\"radio\"\r\n                      name={question.id}\r\n                      value={value}\r\n                      checked={isSelected}\r\n                      onChange={() => handleTrueFalseChange(value)}\r\n                      disabled={disabled}\r\n                      className=\"h-4 w-4 text-blue-600\"\r\n                    />\r\n                    <span className={`font-medium ${disabled ? 'text-gray-500' : 'text-gray-900'}`}>\r\n                      {String.fromCharCode(65 + index)}. {value === 'true' ? 'Benar' : 'Salah'}\r\n                    </span>\r\n                  </div>\r\n                  \r\n                  {/* Show correct/incorrect indicators in results mode */}\r\n                  {showResults && (\r\n                    <div className=\"flex items-center\">\r\n                      {isCorrectOption && (\r\n                        <span className=\"text-green-600 font-medium text-xs bg-green-100 px-2 py-1 rounded-full\">\r\n                          ✓ Benar\r\n                        </span>\r\n                      )}\r\n                      {isSelected && !isCorrectOption && (\r\n                        <span className=\"text-red-600 font-medium text-xs bg-red-100 px-2 py-1 rounded-full\">\r\n                          ✗ Salah\r\n                        </span>\r\n                      )}\r\n                    </div>\r\n                  )}\r\n                </label>\r\n              );\r\n            })}\r\n          </div>\r\n        )}\r\n\r\n        {/* Essay Question */}\r\n        {question.type === 'essay' && (\r\n          <textarea\r\n            className={`\r\n              w-full resize-none rounded-lg border-2 p-4 focus:border-transparent focus:ring-2 focus:ring-blue-500\r\n              ${disabled ? 'bg-gray-50 cursor-not-allowed' : ''}\r\n            `}\r\n            rows={8}\r\n            placeholder=\"Ketik jawaban Anda di sini...\"\r\n            value={selectedAnswer as string || ''}\r\n            onChange={handleEssayChange}\r\n            disabled={disabled}\r\n          />\r\n        )}\r\n\r\n        {/* Show explanation in results */}\r\n        {showResults && question.explanation && (\r\n          <div className=\"mt-4 p-4 bg-gray-50 rounded-lg border-l-4 border-blue-500\">\r\n            <h4 className=\"font-semibold text-gray-900 mb-2\">Penjelasan:</h4>\r\n            <p className=\"text-sm text-gray-700 leading-relaxed\">\r\n              {typeof question.explanation === 'string' ? (\r\n                question.explanation\r\n              ) : Array.isArray(question.explanation) ? (\r\n                question.explanation.map((block, index) => (\r\n                  <React.Fragment key={index}>\r\n                    {block.type === 'text' && <span>{block.value}</span>}\r\n                    {block.type === 'image' && block.value && (\r\n                      <img src={block.value} alt={`Explanation image ${index}`} className=\"inline-block max-h-16 object-contain ml-2\" />\r\n                    )}\r\n                    {block.type === 'video' && <span>[Video: {block.value}]</span>}\r\n                    {block.type === 'pdf' && <span>[PDF: {block.value}]</span>}\r\n                    {block.type === 'zoom-recording' && <span>[Recording: {block.value}]</span>}\r\n                  </React.Fragment>\r\n                ))\r\n              ) : (\r\n                <span>{String(question.explanation)}</span>\r\n              )}\r\n            </p>\r\n          </div>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};", "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\final-exam\\review-question-bank.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\final-exam\\review-question.tsx", ["1520", "1521", "1522"], [], "import React from 'react';\r\nimport { Card, CardContent } from '@/components/ui/card';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { CheckmarkCircle01Icon as CheckCircle2Icon, Cancel01Icon as XCircleIcon } from 'hugeicons-react';\r\nimport { Question as QuestionType } from '@/types/lms';\r\n\r\ninterface ReviewQuestionProps {\r\n  question: QuestionType;\r\n  questionNumber: number;\r\n  totalQuestions: number;\r\n  userAnswer: number | string | number[] | undefined;\r\n  isCorrect: boolean;\r\n}\r\n\r\nexport const ReviewQuestion: React.FC<ReviewQuestionProps> = ({\r\n  question,\r\n  questionNumber,\r\n  totalQuestions,\r\n  userAnswer,\r\n  isCorrect\r\n}) => {\r\n  const getQuestionTypeLabel = (type: string) => {\r\n    switch (type) {\r\n      case 'multiple-choice':\r\n        return '<PERSON><PERSON><PERSON>anda';\r\n      case 'true-false':\r\n        return 'Benar/Salah';\r\n      case 'essay':\r\n        return 'Esai';\r\n      default:\r\n        return type;\r\n    }\r\n  };\r\n\r\n  const getOptionStyle = (optionIndex: number, isUserAnswer: boolean, isCorrectAnswer: boolean) => {\r\n    if (isCorrectAnswer) {\r\n      return 'border-green-500 bg-green-50 text-green-800';\r\n    }\r\n    if (isUserAnswer && !isCorrectAnswer) {\r\n      return 'border-red-500 bg-red-50 text-red-800';\r\n    }\r\n    return 'border-gray-200 bg-gray-50';\r\n  };\r\n\r\n  return (\r\n    <Card className={`\r\n      border-2 transition-all\r\n      ${isCorrect ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}\r\n    `}>\r\n      <CardContent className=\"p-6\">\r\n        <div className=\"mb-4 flex items-center justify-between\">\r\n          <div className=\"flex items-center space-x-3\">\r\n            <Badge variant=\"outline\" className=\"bg-blue-50 text-blue-700 border-blue-200\">\r\n              Soal {questionNumber} dari {totalQuestions}\r\n            </Badge>\r\n            <Badge variant=\"secondary\" className=\"text-xs\">\r\n              {getQuestionTypeLabel(question.type)}\r\n            </Badge>\r\n          </div>\r\n          <Badge \r\n            variant={isCorrect ? 'default' : 'destructive'}\r\n            className={`flex items-center space-x-1 ${isCorrect ? 'bg-green-600 hover:bg-green-700' : ''}`}\r\n          >\r\n            {isCorrect ? (\r\n              <CheckCircle2Icon className=\"h-3 w-3\" />\r\n            ) : (\r\n              <XCircleIcon className=\"h-3 w-3\" />\r\n            )}\r\n            <span>{isCorrect ? 'Benar' : 'Salah'}</span>\r\n          </Badge>\r\n        </div>\r\n\r\n        <div className=\"mb-6\">\r\n          <p className=\"text-lg leading-relaxed text-gray-900 whitespace-pre-wrap\">\r\n            {typeof question.question === 'string' ? (\r\n              question.question\r\n            ) : Array.isArray(question.question) ? (\r\n              question.question.map((block, index) => (\r\n                <React.Fragment key={index}>\r\n                  {block.type === 'text' && <span>{block.value}</span>}\r\n                  {block.type === 'image' && block.value && (\r\n                    <img src={block.value} alt={`Question image ${index}`} className=\"inline-block max-h-16 object-contain ml-2\" />\r\n                  )}\r\n                  {block.type === 'video' && <span>[Video: {block.value}]</span>}\r\n                  {block.type === 'pdf' && <span>[PDF: {block.value}]</span>}\r\n                  {block.type === 'zoom-recording' && <span>[Recording: {block.value}]</span>}\r\n                </React.Fragment>\r\n              ))\r\n            ) : (\r\n              <span>{String(question.question)}</span>\r\n            )}\r\n          </p>\r\n        </div>\r\n\r\n        {/* Multiple Choice Options Review */}\r\n        {question.type === 'multiple-choice' && question.options && (\r\n          <div className=\"space-y-3\">\r\n            {question.options.map((option, index) => {\r\n              const isUserAnswer = userAnswer === index;\r\n              const isCorrectAnswer = question.correctAnswer === index;\r\n              \r\n              return (\r\n                <div\r\n                  key={index}\r\n                  className={`\r\n                    flex items-center space-x-3 p-3 rounded-lg border-2 transition-all\r\n                    ${getOptionStyle(index, isUserAnswer, isCorrectAnswer)}\r\n                  `}\r\n                >\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <div className={`\r\n                      w-4 h-4 rounded-full border-2 flex items-center justify-center\r\n                      ${isCorrectAnswer ? 'border-green-500 bg-green-500' : \r\n                        isUserAnswer ? 'border-red-500 bg-red-500' : 'border-gray-400'}\r\n                    `}>\r\n                      {(isUserAnswer || isCorrectAnswer) && (\r\n                        <div className=\"w-2 h-2 rounded-full bg-white\"></div>\r\n                      )}\r\n                    </div>\r\n                    <span className=\"text-sm font-medium text-gray-600\">\r\n                      {String.fromCharCode(65 + index)}.\r\n                    </span>\r\n                  </div>\r\n                  <div className=\"flex-1 flex items-center justify-between\">\r\n                    <span className={`font-medium ${\r\n                      isCorrectAnswer ? 'text-green-800' : \r\n                      isUserAnswer ? 'text-red-800' : 'text-gray-700'\r\n                    }`}>\r\n                      {typeof option === 'string' ? (\r\n                        option\r\n                      ) : (\r\n                        option.content.map((block, blockIndex) => (\r\n                          <React.Fragment key={blockIndex}>\r\n                            {block.type === 'text' && <span>{block.value}</span>}\r\n                            {block.type === 'image' && block.value && (\r\n                              <img src={block.value} alt={`Option image ${blockIndex}`} className=\"inline-block max-h-8 object-contain ml-1\" />\r\n                            )}\r\n                            {block.type === 'video' && <span>[Video: {block.value}]</span>}\r\n                            {block.type === 'pdf' && <span>[PDF: {block.value}]</span>}\r\n                            {block.type === 'zoom-recording' && <span>[Recording: {block.value}]</span>}\r\n                          </React.Fragment>\r\n                        ))\r\n                      )}\r\n                    </span>\r\n                    {isCorrectAnswer && (\r\n                      <Badge variant=\"outline\" className=\"bg-green-100 text-green-700 border-green-300 ml-2\">\r\n                        Jawaban Benar\r\n                      </Badge>\r\n                    )}\r\n                    {isUserAnswer && !isCorrectAnswer && (\r\n                      <Badge variant=\"outline\" className=\"bg-red-100 text-red-700 border-red-300 ml-2\">\r\n                        Jawaban Anda\r\n                      </Badge>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              );\r\n            })}\r\n          </div>\r\n        )}\r\n\r\n        {/* True/False Review */}\r\n        {question.type === 'true-false' && (\r\n          <div className=\"space-y-3\">\r\n            {['true', 'false'].map((value, index) => {\r\n              const isUserAnswer = userAnswer === value;\r\n              const isCorrectAnswer = question.correctAnswer === value;\r\n              \r\n              return (\r\n                <div\r\n                  key={value}\r\n                  className={`\r\n                    flex items-center space-x-3 p-3 rounded-lg border-2 transition-all\r\n                    ${getOptionStyle(index, isUserAnswer, isCorrectAnswer)}\r\n                  `}\r\n                >\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <div className={`\r\n                      w-4 h-4 rounded-full border-2 flex items-center justify-center\r\n                      ${isCorrectAnswer ? 'border-green-500 bg-green-500' : \r\n                        isUserAnswer ? 'border-red-500 bg-red-500' : 'border-gray-400'}\r\n                    `}>\r\n                      {(isUserAnswer || isCorrectAnswer) && (\r\n                        <div className=\"w-2 h-2 rounded-full bg-white\"></div>\r\n                      )}\r\n                    </div>\r\n                    <span className=\"text-sm font-medium text-gray-600\">\r\n                      {String.fromCharCode(65 + index)}.\r\n                    </span>\r\n                  </div>\r\n                  <div className=\"flex-1 flex items-center justify-between\">\r\n                    <span className={`font-medium ${\r\n                      isCorrectAnswer ? 'text-green-800' : \r\n                      isUserAnswer ? 'text-red-800' : 'text-gray-700'\r\n                    }`}>\r\n                      {value === 'true' ? 'Benar' : 'Salah'}\r\n                    </span>\r\n                    {isCorrectAnswer && (\r\n                      <Badge variant=\"outline\" className=\"bg-green-100 text-green-700 border-green-300 ml-2\">\r\n                        Jawaban Benar\r\n                      </Badge>\r\n                    )}\r\n                    {isUserAnswer && !isCorrectAnswer && (\r\n                      <Badge variant=\"outline\" className=\"bg-red-100 text-red-700 border-red-300 ml-2\">\r\n                        Jawaban Anda\r\n                      </Badge>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              );\r\n            })}\r\n          </div>\r\n        )}\r\n\r\n        {/* Essay Review */}\r\n        {question.type === 'essay' && (\r\n          <div className=\"space-y-4\">\r\n            <div>\r\n              <h4 className=\"font-semibold text-gray-900 mb-2\">Jawaban Anda:</h4>\r\n              <div className=\"p-4 bg-gray-50 rounded-lg border-2 border-gray-200\">\r\n                <p className=\"text-gray-700 whitespace-pre-wrap\">\r\n                  {userAnswer || 'Tidak ada jawaban'}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Explanation */}\r\n        {question.explanation && (\r\n          <div className=\"mt-6 p-4 bg-blue-50 rounded-lg border-l-4 border-blue-500\">\r\n            <h4 className=\"font-semibold text-blue-900 mb-2 flex items-center space-x-2\">\r\n              <span>💡</span>\r\n              <span>Penjelasan:</span>\r\n            </h4>\r\n            <p className=\"text-sm text-blue-800 leading-relaxed whitespace-pre-wrap\">\r\n              {typeof question.explanation === 'string' ? (\r\n                question.explanation\r\n              ) : Array.isArray(question.explanation) ? (\r\n                question.explanation.map((block, index) => (\r\n                  <React.Fragment key={index}>\r\n                    {block.type === 'text' && <span>{block.value}</span>}\r\n                    {block.type === 'image' && block.value && (\r\n                      <img src={block.value} alt={`Explanation image ${index}`} className=\"inline-block max-h-16 object-contain ml-2\" />\r\n                    )}\r\n                    {block.type === 'video' && <span>[Video: {block.value}]</span>}\r\n                    {block.type === 'pdf' && <span>[PDF: {block.value}]</span>}\r\n                    {block.type === 'zoom-recording' && <span>[Recording: {block.value}]</span>}\r\n                  </React.Fragment>\r\n                ))\r\n              ) : (\r\n                <span>{String(question.explanation)}</span>\r\n              )}\r\n            </p>\r\n          </div>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};", "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\module-section.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\payment-modal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\quiz-card.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\quiz-modal.tsx", ["1523", "1524"], [], "import React, { useState } from 'react';\r\nimport {\r\n  <PERSON><PERSON>,\r\n  DialogContent,\r\n  <PERSON><PERSON>Header,\r\n  DialogTitle\r\n} from '@/components/ui/dialog';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Progress } from '@/components/ui/progress';\r\nimport { Card, CardContent } from '@/components/ui/card';\r\nimport { Clock } from 'lucide-react';\r\nimport { QuizModalProps } from '@/types/lms';\r\n\r\nexport const QuizModal: React.FC<QuizModalProps> = ({\r\n  quiz,\r\n  isOpen,\r\n  onClose,\r\n  onComplete\r\n}) => {\r\n  const [currentQuestion, setCurrentQuestion] = useState(0);\r\n  const [answers, setAnswers] = useState<{ [key: string]: any }>({});\r\n  const [timeLeft, setTimeLeft] = useState(\r\n    quiz.timeLimit ? quiz.timeLimit * 60 : null\r\n  );\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n\r\n  React.useEffect(() => {\r\n    if (isOpen && timeLeft !== null && timeLeft > 0) {\r\n      const timer = setInterval(() => {\r\n        setTimeLeft((prev) => {\r\n          if (prev === null || prev <= 1) {\r\n            handleSubmitQuiz();\r\n            return 0;\r\n          }\r\n          return prev - 1;\r\n        });\r\n      }, 1000);\r\n      return () => clearInterval(timer);\r\n    }\r\n  }, [isOpen, timeLeft]);\r\n\r\n  const formatTime = (seconds: number) => {\r\n    const mins = Math.floor(seconds / 60);\r\n    const secs = seconds % 60;\r\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\r\n  };\r\n\r\n  const handleAnswerChange = (questionId: string, answer: any) => {\r\n    setAnswers((prev) => ({ ...prev, [questionId]: answer }));\r\n  };\r\n\r\n  const handleSubmitQuiz = () => {\r\n    if (isSubmitting) return;\r\n    setIsSubmitting(true);\r\n\r\n    // Calculate score\r\n    let correctAnswers = 0;\r\n    quiz.questions.forEach((question) => {\r\n      const userAnswer = answers[question.id];\r\n\r\n      if (question.type === 'multiple_choice' || question.type === 'multiple-choice') {\r\n        // Check if the selected index matches the correct answer\r\n        if (userAnswer === question.correctAnswer) {\r\n          correctAnswers++;\r\n        }\r\n        // Also check for the complex format with isCorrect\r\n        else {\r\n          const selectedOption = question.options?.[userAnswer as number];\r\n          if (selectedOption && typeof selectedOption === 'object' && selectedOption.isCorrect) {\r\n            correctAnswers++;\r\n          }\r\n        }\r\n      } else if (question.type === 'true_false' || question.type === 'true-false') {\r\n        // Compare with correctAnswer for true-false questions\r\n        if (userAnswer === question.correctAnswer) {\r\n          correctAnswers++;\r\n        }\r\n      } else if (question.type === 'essay') {\r\n        // For essay, simply check if an answer was provided.\r\n        // A more complex grading logic would be needed for actual correctness.\r\n        if (userAnswer && userAnswer.trim() !== '') {\r\n          correctAnswers++;\r\n        }\r\n      }\r\n    });\r\n\r\n    const score = Math.round((correctAnswers / quiz.questions.length) * 100);\r\n    setTimeout(() => {\r\n      onComplete(score);\r\n      setIsSubmitting(false);\r\n      setAnswers({});\r\n      setCurrentQuestion(0);\r\n      if (quiz.timeLimit) setTimeLeft(quiz.timeLimit * 60);\r\n    }, 1000);\r\n  };\r\n\r\n  if (!isOpen || quiz.questions.length === 0) return null;\r\n\r\n  const currentQ = quiz.questions[currentQuestion];\r\n  const isLastQuestion = currentQuestion === quiz.questions.length - 1;\r\n  const canProceed = answers[currentQ.id] !== undefined;\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={onClose}>\r\n      <DialogContent className='max-h-[90vh] max-w-4xl overflow-y-auto p-6'>\r\n        <DialogHeader>\r\n          <DialogTitle className='flex items-center justify-between'>\r\n            <span>{quiz.title}</span>\r\n            {timeLeft !== null && (\r\n              <Badge variant='outline' className='border-red-200 text-red-600'>\r\n                <Clock className='mr-1 h-4 w-4' />\r\n                {formatTime(timeLeft)}\r\n              </Badge>\r\n            )}\r\n          </DialogTitle>\r\n        </DialogHeader>\r\n\r\n        <div className='space-y-6'>\r\n          {/* Progress Bar */}\r\n          <div className='space-y-2'>\r\n            <div className='flex justify-between text-sm text-gray-600'>\r\n              <span>\r\n                Question {currentQuestion + 1} of {quiz.questions.length}\r\n              </span>\r\n              <span>\r\n                {Math.round(\r\n                  ((currentQuestion + 1) / quiz.questions.length) * 100\r\n                )}\r\n                % Complete\r\n              </span>\r\n            </div>\r\n            <Progress\r\n              value={((currentQuestion + 1) / quiz.questions.length) * 100}\r\n            />\r\n          </div>\r\n\r\n          {/* Question */}\r\n          <Card>\r\n            <CardContent className='p-6'>\r\n              <h3 className='mb-4 text-lg font-medium'>\r\n                {typeof currentQ.question === 'string' ? (\r\n                  currentQ.question\r\n                ) : Array.isArray(currentQ.question) ? (\r\n                  currentQ.question.map((block, index) => (\r\n                    <React.Fragment key={index}>\r\n                      {block.type === 'text' && <span>{block.value}</span>}\r\n                      {block.type === 'image' && block.value && (\r\n                        <img src={block.value} alt={`Question image ${index}`} className=\"inline-block max-h-16 object-contain ml-2\" />\r\n                      )}\r\n                    </React.Fragment>\r\n                  ))\r\n                ) : (\r\n                  <span>{String(currentQ.question)}</span>\r\n                )}\r\n              </h3>\r\n\r\n              {(currentQ.type === 'multiple_choice' || currentQ.type === 'multiple-choice') && currentQ.options && (\r\n                <div className='space-y-3'>\r\n                  {currentQ.options.map((option, index) => (\r\n                    <label\r\n                      key={index}\r\n                      className='flex cursor-pointer items-center space-x-3'\r\n                    >\r\n                      <input\r\n                        type='radio'\r\n                        name={currentQ.id}\r\n                        value={index}\r\n                        checked={answers[currentQ.id] === index}\r\n                        onChange={() => handleAnswerChange(currentQ.id, index)}\r\n                        className='h-4 w-4 text-blue-600'\r\n                      />\r\n                      <span>\r\n                        {typeof option === 'string' ? (\r\n                          option\r\n                        ) : Array.isArray(option.content) ? (\r\n                          option.content.map((block, optionBlockIndex) => (\r\n                            <React.Fragment key={optionBlockIndex}>\r\n                              {block.type === 'text' && <span>{block.value}</span>}\r\n                              {block.type === 'image' && block.value && (\r\n                                <img src={block.value} alt={`Option image ${optionBlockIndex}`} className=\"inline-block max-h-8 object-contain ml-1\" />\r\n                              )}\r\n                            </React.Fragment>\r\n                          ))\r\n                        ) : (\r\n                          <span>{String(option.content || option)}</span>\r\n                        )}\r\n                      </span>\r\n                    </label>\r\n                  ))}\r\n                </div>\r\n              )}\r\n\r\n              {(currentQ.type === 'true_false' || currentQ.type === 'true-false') && (\r\n                <div className='space-y-3'>\r\n                  <label className='flex cursor-pointer items-center space-x-3'>\r\n                    <input\r\n                      type='radio'\r\n                      name={currentQ.id}\r\n                      value='true'\r\n                      checked={answers[currentQ.id] === 'true'}\r\n                      onChange={() => handleAnswerChange(currentQ.id, 'true')}\r\n                      className='h-4 w-4 text-blue-600'\r\n                    />\r\n                    <span>True</span>\r\n                  </label>\r\n                  <label className='flex cursor-pointer items-center space-x-3'>\r\n                    <input\r\n                      type='radio'\r\n                      name={currentQ.id}\r\n                      value='false'\r\n                      checked={answers[currentQ.id] === 'false'}\r\n                      onChange={() => handleAnswerChange(currentQ.id, 'false')}\r\n                      className='h-4 w-4 text-blue-600'\r\n                    />\r\n                    <span>False</span>\r\n                  </label>\r\n                </div>\r\n              )}\r\n\r\n              {currentQ.type === 'essay' && (\r\n                <textarea\r\n                  className='w-full resize-none rounded-lg border p-3 focus:border-transparent focus:ring-2 focus:ring-blue-500'\r\n                  rows={6}\r\n                  placeholder='Type your answer here...'\r\n                  value={answers[currentQ.id] || ''}\r\n                  onChange={(e) =>\r\n                    handleAnswerChange(currentQ.id, e.target.value)\r\n                  }\r\n                />\r\n              )}\r\n            </CardContent>\r\n          </Card>\r\n\r\n          {/* Navigation */}\r\n          <div className='flex justify-between'>\r\n            <Button\r\n              variant='outline'\r\n              onClick={() =>\r\n                setCurrentQuestion((prev) => Math.max(0, prev - 1))\r\n              }\r\n              disabled={currentQuestion === 0 || isSubmitting}\r\n            >\r\n              Previous\r\n            </Button>\r\n\r\n            <div className='flex space-x-2'>\r\n              {!isLastQuestion ? (\r\n                <Button\r\n                  onClick={() => setCurrentQuestion((prev) => prev + 1)}\r\n                  disabled={!canProceed || isSubmitting}\r\n                >\r\n                  Next\r\n                </Button>\r\n              ) : (\r\n                <Button\r\n                  onClick={handleSubmitQuiz}\r\n                  disabled={!canProceed || isSubmitting}\r\n                  className='bg-green-600 hover:bg-green-700'\r\n                >\r\n                  {isSubmitting ? 'Submitting...' : 'Submit Quiz'}\r\n                </Button>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n};\r\n", "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\table-of-contents.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\tabs\\certificate-tab.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\tabs\\course-tab.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\tabs\\exam-tab.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\tabs\\progress-tab.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\lms\\tree-node.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\modal\\alert-modal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\nav-main.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\nav-projects.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\nav-user.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\not-assigned-to-institution.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\org-switcher.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\quiz\\QuizTaker.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\role-indicator.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\search-input.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\theme-selector.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\accordion.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\alert-dialog.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\alert.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\aspect-ratio.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\avatar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\breadcrumb.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\calendar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\chart.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\checkbox.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\collapsible.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\command.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\context-menu.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\dialog.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\drawer.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\dropdown-menu.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\form.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\heading.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\hover-card.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\input-otp.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\menubar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\modal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\navigation-menu.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\pagination.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\popover.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\progress.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\radio-group.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\resizable.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\scroll-area.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\select.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\separator.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\sheet.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\sidebar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\skeleton.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\slider.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\sonner.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\switch.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\table\\data-table-column-header.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\table\\data-table-date-filter.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\table\\data-table-faceted-filter.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\table\\data-table-pagination.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\table\\data-table-skeleton.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\table\\data-table-slider-filter.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\table\\data-table-toolbar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\table\\data-table-view-options.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\table\\data-table.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\table.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\tabs.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\textarea.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\toggle-group.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\toggle.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\tooltip.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\user-avatar-profile.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\config\\data-table.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\config\\navigation.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\config\\subscriptions.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\constants\\data.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\constants\\mock-api.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\constants\\mock-lms-data.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\constants\\shared-course-data.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\contexts\\enrollment-context.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\auth\\components\\github-auth-button.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\auth\\components\\main-page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\auth\\components\\sign-in-view.tsx", ["1525"], [], "'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { toast } from 'sonner';\r\nimport { Button } from '@/components/ui/button';\r\nimport { authStorage, getRedirectPath } from '@/lib/auth';\r\nimport { Input } from '@/components/ui/input';\r\nimport { CheckCircle2 } from 'lucide-react'; // Import CheckCircle2 icon\r\nimport { Label } from '@/components/ui/label';\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Checkbox } from '@/components/ui/checkbox';\r\nimport { ViewIcon as EyeIcon, ViewOffIcon as EyeOffIcon, Building02Icon as BuildingIcon, BookOpen01Icon as BookIcon, UserIcon, Mail01Icon as MailIcon, LockIcon, ArrowLeft02Icon as ChevronLeftIcon, ArrowRight02Icon as ChevronRightIcon, Award01Icon as AwardIcon, Video01Icon as VideoIcon, Task01Icon as TaskIcon } from 'hugeicons-react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\n\r\n// Feature Carousel Component\r\nconst FeatureCarousel = () => {\r\n  const [currentSlide, setCurrentSlide] = useState(0);\r\n  \r\n  const features = [\r\n    {\r\n      icon: BookIcon,\r\n      title: 'Pembelajaran Interaktif',\r\n      description: 'Akses kursus video berkualitas tinggi dengan materi yang disusun oleh para ahli arsitektur Indonesia',\r\n      highlight: '50+ Kursus Tersedia'\r\n    },\r\n    {\r\n      icon: TaskIcon,\r\n      title: 'Ujian Bersertifikat',\r\n      description: 'Uji kompetensi Anda dengan sistem ujian online yang ketat dan dapatkan sertifikat resmi IAI',\r\n      highlight: 'Sertifikat Diakui Nasional'\r\n    },\r\n    {\r\n      icon: AwardIcon,\r\n      title: 'Pengembangan Karir',\r\n      description: 'Tingkatkan kredibilitas profesional dengan sertifikasi yang diakui industri arsitektur Indonesia',\r\n      highlight: '95% Peserta Berhasil'\r\n    }\r\n  ];\r\n\r\n  useEffect(() => {\r\n    const timer = setInterval(() => {\r\n      setCurrentSlide((prev) => (prev + 1) % features.length);\r\n    }, 4000);\r\n    return () => clearInterval(timer);\r\n  }, [features.length]);\r\n\r\n  const nextSlide = () => {\r\n    setCurrentSlide((prev) => (prev + 1) % features.length);\r\n  };\r\n\r\n  const prevSlide = () => {\r\n    setCurrentSlide((prev) => (prev - 1 + features.length) % features.length);\r\n  };\r\n\r\n  const currentFeature = features[currentSlide];\r\n  const Icon = currentFeature.icon;\r\n\r\n  return (\r\n    <div className='bg-white/10 backdrop-blur-sm rounded-xl p-6 min-h-[200px]'>\r\n      <div className='flex items-center justify-between mb-4'>\r\n        <button\r\n          onClick={prevSlide}\r\n          className='p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors'\r\n        >\r\n          <ChevronLeftIcon className='w-4 h-4 text-white' />\r\n        </button>\r\n        \r\n        <div className='flex space-x-2'>\r\n          {features.map((_, index) => (\r\n            <button\r\n              key={index}\r\n              onClick={() => setCurrentSlide(index)}\r\n              className={`w-2 h-2 rounded-full transition-colors ${\r\n                index === currentSlide ? 'bg-white' : 'bg-white/40'\r\n              }`}\r\n            />\r\n          ))}\r\n        </div>\r\n        \r\n        <button\r\n          onClick={nextSlide}\r\n          className='p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors'\r\n        >\r\n          <ChevronRightIcon className='w-4 h-4 text-white' />\r\n        </button>\r\n      </div>\r\n\r\n      <div className='text-center space-y-4'>\r\n        <div className='flex justify-center'>\r\n          <div className='w-16 h-16 bg-white/20 rounded-xl flex items-center justify-center'>\r\n            <Icon className='w-8 h-8 text-white' />\r\n          </div>\r\n        </div>\r\n        \r\n        <div className='space-y-2'>\r\n          <h3 className='text-xl font-bold text-white'>{currentFeature.title}</h3>\r\n          <p className='text-white/90 text-sm leading-relaxed'>{currentFeature.description}</p>\r\n          <div className='inline-block bg-white/20 px-3 py-1 rounded-full'>\r\n            <span className='text-xs font-medium text-white'>{currentFeature.highlight}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default function SignInViewPage() {\r\n  const [showPassword, setShowPassword] = useState(false);\r\n  const [loading, setLoading] = useState(false);\r\n  const [loginSuccess, setLoginSuccess] = useState(false); // New state for login success animation\r\n  const [error, setError] = useState('');\r\n  const [formData, setFormData] = useState({\r\n    email: '',\r\n    password: '',\r\n    rememberMe: false\r\n  });\r\n\r\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const { name, value, type, checked } = e.target;\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [name]: type === 'checkbox' ? checked : value\r\n    }));\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    setLoading(true);\r\n    setError('');\r\n\r\n    try {\r\n      const response = await fetch('/api/auth/login', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n          email: formData.email,\r\n          password: formData.password,\r\n        }),\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (!response.ok) {\r\n        throw new Error(data.error || 'Login failed');\r\n      }\r\n\r\n      // Store user data using authStorage for consistency\r\n      authStorage.setUser(data.user);\r\n      \r\n      setLoginSuccess(true); // Set success state to true\r\n      toast.success('Login berhasil!', {\r\n        description: `Selamat datang kembali, ${data.user.name}`,\r\n        duration: 3000,\r\n      });\r\n      \r\n      // Redirect to dashboard or home page based on user role after a short delay\r\n      setTimeout(() => {\r\n        const redirectPath = getRedirectPath(data.user);\r\n        console.log('User role:', data.user.role, 'Redirecting to:', redirectPath);\r\n        window.location.href = redirectPath;\r\n      }, 1500);\r\n      \r\n    } catch (err: any) {\r\n      setError(err.message || 'Terjadi kesalahan saat login');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className='h-screen bg-gradient-to-br from-slate-50 to-blue-50 overflow-hidden'>\r\n      <div className='flex h-screen'>\r\n        {/* Left Side - Branding and Information */}\r\n        <div className='hidden lg:flex lg:w-1/2 relative bg-gradient-to-br from-slate-800 to-slate-900 text-white'>\r\n          {/* Background Pattern */}\r\n          <div className='absolute inset-0 opacity-10'>\r\n            <svg width=\"100%\" height=\"100%\" xmlns=\"http://www.w3.org/2000/svg\">\r\n              <defs>\r\n                <pattern id=\"grid\" width=\"60\" height=\"60\" patternUnits=\"userSpaceOnUse\">\r\n                  <path d=\"M 60 0 L 0 0 0 60\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"1\"/>\r\n                </pattern>\r\n              </defs>\r\n              <rect width=\"100%\" height=\"100%\" fill=\"url(#grid)\" />\r\n            </svg>\r\n          </div>\r\n\r\n          <div className='relative z-10 flex flex-col justify-between p-12 w-full'>\r\n            {/* Logo and Branding */}\r\n            <div className='space-y-4'>\r\n              <div className='flex justify-center'>\r\n                <Image\r\n                  src='/assets/logo-iai-putih.png'\r\n                  alt='IAI Logo'\r\n                  width={200}\r\n                  height={200}\r\n                  className='object-contain'\r\n                />\r\n              </div>\r\n              \r\n              <div className='space-y-6 mt-12'>\r\n                <h2 className='text-3xl font-bold leading-tight'>\r\n                  Selamat Datang di<br />\r\n                  Sistem Pembelajaran<br />\r\n                  Profesional\r\n                </h2>\r\n                <p className='text-lg text-white/90 leading-relaxed'>\r\n                  Platform pembelajaran online khusus untuk para arsitek profesional. \r\n                  Tingkatkan keahlian Anda dengan kursus berkualitas tinggi.\r\n                </p>\r\n              </div>\r\n\r\n              {/* Features Carousel */}\r\n              <div className='relative mt-8'>\r\n                <FeatureCarousel />\r\n              </div>\r\n            </div>\r\n\r\n          </div>\r\n        </div>\r\n\r\n        {/* Right Side - Login Form */}\r\n        <div className='flex-1 overflow-y-auto'>\r\n          <div className='flex min-h-full items-center justify-center p-4 sm:p-8'>\r\n            <div className='w-full max-w-md space-y-4 sm:space-y-6 py-4'>\r\n            {/* Mobile Logo */}\r\n            <div className='lg:hidden text-center mb-8'>\r\n              <div className='flex justify-center mb-4'>\r\n                <Image\r\n                  src='/assets/logo-iai.png'\r\n                  alt='IAI Logo'\r\n                  width={200}\r\n                  height={200}\r\n                  className='object-contain'\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            {/* Login Card */}\r\n            <Card className='shadow-xl border-0'>\r\n              <CardHeader className='text-center space-y-1 pb-6'>\r\n                <CardTitle className='text-2xl font-bold text-gray-900'>Masuk ke Akun</CardTitle>\r\n                <CardDescription className='text-gray-600'>\r\n                  Masukkan email dan password untuk mengakses kursus Anda\r\n                </CardDescription>\r\n              </CardHeader>\r\n              \r\n              <CardContent>\r\n                {error && (\r\n                  <div className='mb-4 p-3 bg-red-50 border border-red-200 rounded-md'>\r\n                    <p className='text-sm text-red-600'>{error}</p>\r\n                  </div>\r\n                )}\r\n\r\n                <form onSubmit={handleSubmit} className='space-y-4'>\r\n                  {/* Email Field */}\r\n                  <div className='space-y-2'>\r\n                    <Label htmlFor='email' className='text-sm font-medium text-gray-700'>\r\n                      Email\r\n                    </Label>\r\n                    <div className='relative'>\r\n                      <MailIcon className='absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400' />\r\n                      <Input\r\n                        id='email'\r\n                        name='email'\r\n                        type='email'\r\n                        placeholder='<EMAIL>'\r\n                        value={formData.email}\r\n                        onChange={handleInputChange}\r\n                        className='pl-10 h-12 border-gray-200 focus:border-[var(--iai-primary)] focus:ring-[var(--iai-primary)]'\r\n                        required\r\n                      />\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Password Field */}\r\n                  <div className='space-y-2'>\r\n                    <Label htmlFor='password' className='text-sm font-medium text-gray-700'>\r\n                      Password\r\n                    </Label>\r\n                    <div className='relative'>\r\n                      <LockIcon className='absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400' />\r\n                      <Input\r\n                        id='password'\r\n                        name='password'\r\n                        type={showPassword ? 'text' : 'password'}\r\n                        placeholder='Masukkan password'\r\n                        value={formData.password}\r\n                        onChange={handleInputChange}\r\n                        className='pl-10 pr-10 h-12 border-gray-200 focus:border-[var(--iai-primary)] focus:ring-[var(--iai-primary)]'\r\n                        required\r\n                      />\r\n                      <button\r\n                        type='button'\r\n                        onClick={() => setShowPassword(!showPassword)}\r\n                        className='absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600'\r\n                      >\r\n                        {showPassword ? (\r\n                          <EyeOffIcon className='w-4 h-4' />\r\n                        ) : (\r\n                          <EyeIcon className='w-4 h-4' />\r\n                        )}\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Remember Me and Forgot Password */}\r\n                  <div className='flex items-center justify-between pt-2'>\r\n                    <div className='flex items-center space-x-2'>\r\n                      <Checkbox\r\n                        id='rememberMe'\r\n                        name='rememberMe'\r\n                        checked={formData.rememberMe}\r\n                        onCheckedChange={(checked) => \r\n                          setFormData(prev => ({ ...prev, rememberMe: checked as boolean }))\r\n                        }\r\n                        className='data-[state=checked]:bg-[var(--iai-primary)] data-[state=checked]:border-[var(--iai-primary)]'\r\n                      />\r\n                      <Label \r\n                        htmlFor='rememberMe' \r\n                        className='text-sm text-gray-600 cursor-pointer'\r\n                      >\r\n                        Ingat saya\r\n                      </Label>\r\n                    </div>\r\n                    <Link\r\n                      href='/auth/forgot-password'\r\n                      className='text-sm text-[var(--iai-primary)] hover:text-[var(--iai-secondary)] font-medium'\r\n                    >\r\n                      Lupa password?\r\n                    </Link>\r\n                  </div>\r\n\r\n                  {/* Login Button */}\r\n                  <Button\r\n                    type='submit'\r\n                    variant='iai'\r\n                    className='w-full h-12 text-base font-medium mt-6'\r\n                    disabled={loading || loginSuccess} // Disable button during loading or success animation\r\n                  >\r\n                    {loading ? (\r\n                      'Memproses...'\r\n                    ) : loginSuccess ? (\r\n                      <span className=\"flex items-center justify-center\">\r\n                        <CheckCircle2 className=\"mr-2 h-5 w-5\" /> Berhasil!\r\n                      </span>\r\n                    ) : (\r\n                      'Masuk ke Dashboard'\r\n                    )}\r\n                  </Button>\r\n                </form>\r\n\r\n                {/* Divider */}\r\n                <div className='relative my-6'>\r\n                  <div className='absolute inset-0 flex items-center'>\r\n                    <span className='w-full border-t border-gray-200' />\r\n                  </div>\r\n                  <div className='relative flex justify-center text-xs uppercase'>\r\n                    <span className='bg-white px-2 text-gray-500'>atau</span>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Alternative Login Methods */}\r\n                <div className='space-y-3'>\r\n                  <Button\r\n                    variant='outline'\r\n                    className='w-full h-11 font-medium border-gray-200 hover:bg-gray-50'\r\n                  >\r\n                    <svg className='w-4 h-4 mr-2' viewBox='0 0 24 24'>\r\n                      <path fill='#4285F4' d='M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z'/>\r\n                      <path fill='#34A853' d='M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z'/>\r\n                      <path fill='#FBBC05' d='M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z'/>\r\n                      <path fill='#EA4335' d='M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z'/>\r\n                    </svg>\r\n                    Masuk dengan Google\r\n                  </Button>\r\n                </div>\r\n\r\n                {/* Sign Up Link */}\r\n                <p className='text-center text-sm text-gray-600 mt-6'>\r\n                  Belum punya akun?{' '}\r\n                  <Link\r\n                    href='/auth/sign-up'\r\n                    className='text-[var(--iai-primary)] hover:text-[var(--iai-secondary)] font-medium'\r\n                  >\r\n                    Daftar sekarang\r\n                  </Link>\r\n                </p>\r\n              </CardContent>\r\n            </Card>\r\n\r\n            {/* Footer */}\r\n            <div className='text-center text-xs text-gray-500 space-y-1'>\r\n              <p>\r\n                Dengan masuk, Anda menyetujui{' '}\r\n                <Link href='/terms' className='underline hover:text-gray-700'>\r\n                  Syarat & Ketentuan\r\n                </Link>{' '}\r\n                dan{' '}\r\n                <Link href='/privacy' className='underline hover:text-gray-700'>\r\n                  Kebijakan Privasi\r\n                </Link>\r\n              </p>\r\n              <div className='flex items-center justify-center space-x-2 text-gray-400'>\r\n                <span>© 2024 IAI LMS - Powered by</span>\r\n                <img\r\n                  src=\"https://cdn.terang.ai/images/logo/logo-terang-ai.svg\"\r\n                  alt=\"Terang AI\"\r\n                  className='h-4 inline-block'\r\n                />\r\n              </div>\r\n            </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}", "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\auth\\components\\sign-up-view.tsx", ["1526"], [], "'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { toast } from 'sonner';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Checkbox } from '@/components/ui/checkbox';\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\r\nimport { ViewIcon as EyeIcon, ViewOffIcon as EyeOffIcon, Building02Icon as BuildingIcon, BookOpen01Icon as BookIcon, UserIcon, Mail01Icon as MailIcon, LockIcon, SmartPhone01Icon as PhoneIcon, ArrowLeft02Icon as ChevronLeftIcon, ArrowRight02Icon as ChevronRightIcon, Award01Icon as AwardIcon, Video01Icon as VideoIcon, Task01Icon as TaskIcon, UserGroupIcon as NetworkIcon } from 'hugeicons-react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\n\r\n// Feature Carousel Component for Sign Up\r\nconst FeatureCarousel = () => {\r\n  const [currentSlide, setCurrentSlide] = useState(0);\r\n  \r\n  const features = [\r\n    {\r\n      icon: VideoIcon,\r\n      title: 'Akses Unlimited',\r\n      description: 'Dapatkan akses tak terbatas ke seluruh koleksi kursus arsitektur terlengkap di Indonesia',\r\n      highlight: 'Ribuan Jam Konten'\r\n    },\r\n    {\r\n      icon: AwardIcon,\r\n      title: 'Sertifikat Resmi IAI',\r\n      description: 'Setiap kursus yang diselesaikan akan mendapatkan sertifikat resmi dari Ikatan Arsitek Indonesia',\r\n      highlight: 'Diakui Industri'\r\n    },\r\n    {\r\n      icon: NetworkIcon,\r\n      title: 'Networking Profesional',\r\n      description: 'Bergabung dengan komunitas lebih dari 10,000+ arsitek profesional di seluruh Indonesia',\r\n      highlight: '10,000+ Anggota Aktif'\r\n    }\r\n  ];\r\n\r\n  useEffect(() => {\r\n    const timer = setInterval(() => {\r\n      setCurrentSlide((prev) => (prev + 1) % features.length);\r\n    }, 4000);\r\n    return () => clearInterval(timer);\r\n  }, [features.length]);\r\n\r\n  const nextSlide = () => {\r\n    setCurrentSlide((prev) => (prev + 1) % features.length);\r\n  };\r\n\r\n  const prevSlide = () => {\r\n    setCurrentSlide((prev) => (prev - 1 + features.length) % features.length);\r\n  };\r\n\r\n  const currentFeature = features[currentSlide];\r\n  const Icon = currentFeature.icon;\r\n\r\n  return (\r\n    <div className='bg-white/10 backdrop-blur-sm rounded-xl p-6 min-h-[200px]'>\r\n      <div className='flex items-center justify-between mb-4'>\r\n        <button\r\n          onClick={prevSlide}\r\n          className='p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors'\r\n        >\r\n          <ChevronLeftIcon className='w-4 h-4 text-white' />\r\n        </button>\r\n        \r\n        <div className='flex space-x-2'>\r\n          {features.map((_, index) => (\r\n            <button\r\n              key={index}\r\n              onClick={() => setCurrentSlide(index)}\r\n              className={`w-2 h-2 rounded-full transition-colors ${\r\n                index === currentSlide ? 'bg-white' : 'bg-white/40'\r\n              }`}\r\n            />\r\n          ))}\r\n        </div>\r\n        \r\n        <button\r\n          onClick={nextSlide}\r\n          className='p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors'\r\n        >\r\n          <ChevronRightIcon className='w-4 h-4 text-white' />\r\n        </button>\r\n      </div>\r\n\r\n      <div className='text-center space-y-4'>\r\n        <div className='flex justify-center'>\r\n          <div className='w-16 h-16 bg-white/20 rounded-xl flex items-center justify-center'>\r\n            <Icon className='w-8 h-8 text-white' />\r\n          </div>\r\n        </div>\r\n        \r\n        <div className='space-y-2'>\r\n          <h3 className='text-xl font-bold text-white'>{currentFeature.title}</h3>\r\n          <p className='text-white/90 text-sm leading-relaxed'>{currentFeature.description}</p>\r\n          <div className='inline-block bg-white/20 px-3 py-1 rounded-full'>\r\n            <span className='text-xs font-medium text-white'>{currentFeature.highlight}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default function SignUpViewPage() {\r\n  const [showPassword, setShowPassword] = useState(false);\r\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\r\n  const [formData, setFormData] = useState({\r\n    firstName: '',\r\n    lastName: '',\r\n    email: '',\r\n    phone: '',\r\n    iaiMembership: '',\r\n    organization: '',\r\n    password: '',\r\n    confirmPassword: '',\r\n    agreeTerms: false,\r\n    subscribeUpdates: false\r\n  });\r\n\r\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const { name, value, type, checked } = e.target;\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [name]: type === 'checkbox' ? checked : value\r\n    }));\r\n  };\r\n\r\n  const handleSelectChange = (name: string, value: string) => {\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [name]: value\r\n    }));\r\n  };\r\n\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState('');\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    setLoading(true);\r\n    setError('');\r\n\r\n    // Validate password confirmation\r\n    if (formData.password !== formData.confirmPassword) {\r\n      setError('Password tidak cocok');\r\n      setLoading(false);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const response = await fetch('/api/auth/register', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n          firstName: formData.firstName,\r\n          lastName: formData.lastName,\r\n          email: formData.email,\r\n          phone: formData.phone,\r\n          password: formData.password,\r\n          iaiMembership: formData.iaiMembership,\r\n          organization: formData.organization,\r\n        }),\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (!response.ok) {\r\n        throw new Error(data.error || 'Registration failed');\r\n      }\r\n\r\n      toast.success('Registrasi berhasil!', {\r\n        description: 'Akun Anda telah dibuat. Silakan login untuk melanjutkan.',\r\n        duration: 4000,\r\n      });\r\n      \r\n      // Reset form\r\n      setFormData({\r\n        firstName: '',\r\n        lastName: '',\r\n        email: '',\r\n        phone: '',\r\n        iaiMembership: '',\r\n        organization: '',\r\n        password: '',\r\n        confirmPassword: '',\r\n        agreeTerms: false,\r\n        subscribeUpdates: false\r\n      });\r\n      \r\n      // Optional: redirect to login page after a short delay\r\n      setTimeout(() => {\r\n        window.location.href = '/auth/sign-in';\r\n      }, 2000);\r\n    } catch (err: any) {\r\n      setError(err.message || 'Terjadi kesalahan saat registrasi');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className='h-screen bg-gradient-to-br from-slate-50 to-blue-50 overflow-hidden'>\r\n      <div className='flex h-screen'>\r\n        {/* Left Side - Branding and Information */}\r\n        <div className='hidden lg:flex lg:w-1/2 relative bg-gradient-to-br from-slate-800 to-slate-900 text-white'>\r\n          {/* Background Pattern */}\r\n          <div className='absolute inset-0 opacity-10'>\r\n            <svg width=\"100%\" height=\"100%\" xmlns=\"http://www.w3.org/2000/svg\">\r\n              <defs>\r\n                <pattern id=\"grid\" width=\"60\" height=\"60\" patternUnits=\"userSpaceOnUse\">\r\n                  <path d=\"M 60 0 L 0 0 0 60\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"1\"/>\r\n                </pattern>\r\n              </defs>\r\n              <rect width=\"100%\" height=\"100%\" fill=\"url(#grid)\" />\r\n            </svg>\r\n          </div>\r\n\r\n          <div className='relative z-10 flex flex-col justify-between p-12 w-full'>\r\n            {/* Logo and Branding */}\r\n            <div className='space-y-4'>\r\n              <div className='flex justify-center'>\r\n                <Image\r\n                  src='/assets/logo-iai-putih.png'\r\n                  alt='IAI Logo'\r\n                  width={200}\r\n                  height={200}\r\n                  className='object-contain'\r\n                />\r\n              </div>\r\n              \r\n              <div className='space-y-6 mt-12'>\r\n                <h2 className='text-3xl font-bold leading-tight'>\r\n                  Bergabunglah dengan<br />\r\n                  Komunitas Arsitek<br />\r\n                  Profesional Indonesia\r\n                </h2>\r\n                <p className='text-lg text-white/90 leading-relaxed'>\r\n                  Daftarkan diri Anda dan mulai perjalanan pembelajaran profesional \r\n                  bersama ribuan arsitek Indonesia lainnya.\r\n                </p>\r\n              </div>\r\n\r\n              {/* Features Carousel */}\r\n              <div className='relative mt-8'>\r\n                <FeatureCarousel />\r\n              </div>\r\n            </div>\r\n\r\n          </div>\r\n        </div>\r\n\r\n        {/* Right Side - Registration Form */}\r\n        <div className='flex-1 overflow-y-auto'>\r\n          <div className='flex min-h-full items-start justify-center p-4 sm:p-8'>\r\n            <div className='w-full max-w-md space-y-4 sm:space-y-6 py-4'>\r\n            {/* Mobile Logo */}\r\n            <div className='lg:hidden text-center mb-4 sm:mb-8'>\r\n              <div className='flex justify-center mb-2 sm:mb-4'>\r\n                <Image\r\n                  src='/assets/logo-iai.png'\r\n                  alt='IAI Logo'\r\n                  width={150}\r\n                  height={150}\r\n                  className='object-contain sm:w-[200px] sm:h-[200px]'\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            {/* Registration Card */}\r\n            <Card className='shadow-xl border-0'>\r\n              <CardHeader className='text-center space-y-1 pb-4 sm:pb-6'>\r\n                <CardTitle className='text-2xl font-bold text-gray-900'>Buat Akun Baru</CardTitle>\r\n                <CardDescription className='text-gray-600'>\r\n                  Lengkapi data diri untuk mendaftar sebagai anggota IAI LMS\r\n                </CardDescription>\r\n              </CardHeader>\r\n              \r\n              <CardContent>\r\n                {error && (\r\n                  <div className='mb-4 p-3 bg-red-50 border border-red-200 rounded-md'>\r\n                    <p className='text-sm text-red-600'>{error}</p>\r\n                  </div>\r\n                )}\r\n\r\n                <form onSubmit={handleSubmit} className='space-y-4'>\r\n                  {/* Name Fields */}\r\n                  <div className='grid grid-cols-2 gap-3'>\r\n                    <div className='space-y-2'>\r\n                      <Label htmlFor='firstName' className='text-sm font-medium text-gray-700'>\r\n                        Nama Depan *\r\n                      </Label>\r\n                      <Input\r\n                        id='firstName'\r\n                        name='firstName'\r\n                        type='text'\r\n                        placeholder='Nama depan'\r\n                        value={formData.firstName}\r\n                        onChange={handleInputChange}\r\n                        className='h-11 border-gray-200 focus:border-[var(--iai-primary)] focus:ring-[var(--iai-primary)]'\r\n                        required\r\n                      />\r\n                    </div>\r\n                    <div className='space-y-2'>\r\n                      <Label htmlFor='lastName' className='text-sm font-medium text-gray-700'>\r\n                        Nama Belakang *\r\n                      </Label>\r\n                      <Input\r\n                        id='lastName'\r\n                        name='lastName'\r\n                        type='text'\r\n                        placeholder='Nama belakang'\r\n                        value={formData.lastName}\r\n                        onChange={handleInputChange}\r\n                        className='h-11 border-gray-200 focus:border-[var(--iai-primary)] focus:ring-[var(--iai-primary)]'\r\n                        required\r\n                      />\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Email Field */}\r\n                  <div className='space-y-2'>\r\n                    <Label htmlFor='email' className='text-sm font-medium text-gray-700'>\r\n                      Email *\r\n                    </Label>\r\n                    <div className='relative'>\r\n                      <MailIcon className='absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400' />\r\n                      <Input\r\n                        id='email'\r\n                        name='email'\r\n                        type='email'\r\n                        placeholder='<EMAIL>'\r\n                        value={formData.email}\r\n                        onChange={handleInputChange}\r\n                        className='pl-10 h-11 border-gray-200 focus:border-[var(--iai-primary)] focus:ring-[var(--iai-primary)]'\r\n                        required\r\n                      />\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Phone Field */}\r\n                  <div className='space-y-2'>\r\n                    <Label htmlFor='phone' className='text-sm font-medium text-gray-700'>\r\n                      Nomor Telepon *\r\n                    </Label>\r\n                    <div className='relative'>\r\n                      <PhoneIcon className='absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400' />\r\n                      <Input\r\n                        id='phone'\r\n                        name='phone'\r\n                        type='tel'\r\n                        placeholder='+62 812 3456 7890'\r\n                        value={formData.phone}\r\n                        onChange={handleInputChange}\r\n                        className='pl-10 h-11 border-gray-200 focus:border-[var(--iai-primary)] focus:ring-[var(--iai-primary)]'\r\n                        required\r\n                      />\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* IAI Membership */}\r\n                  <div className='space-y-2'>\r\n                    <Label htmlFor='iaiMembership' className='text-sm font-medium text-gray-700'>\r\n                      Status Keanggotaan IAI\r\n                    </Label>\r\n                    <Select onValueChange={(value) => handleSelectChange('iaiMembership', value)}>\r\n                      <SelectTrigger className='h-11 border-gray-200 focus:border-[var(--iai-primary)] focus:ring-[var(--iai-primary)]'>\r\n                        <SelectValue placeholder='Pilih status keanggotaan' />\r\n                      </SelectTrigger>\r\n                      <SelectContent>\r\n                        <SelectItem value='anggota-biasa'>Anggota Biasa</SelectItem>\r\n                        <SelectItem value='anggota-muda'>Anggota Muda</SelectItem>\r\n                        <SelectItem value='anggota-luar-biasa'>Anggota Luar Biasa</SelectItem>\r\n                        <SelectItem value='belum-anggota'>Belum Menjadi Anggota</SelectItem>\r\n                      </SelectContent>\r\n                    </Select>\r\n                  </div>\r\n\r\n                  {/* Organization */}\r\n                  <div className='space-y-2'>\r\n                    <Label htmlFor='organization' className='text-sm font-medium text-gray-700'>\r\n                      Instansi/Perusahaan\r\n                    </Label>\r\n                    <Input\r\n                      id='organization'\r\n                      name='organization'\r\n                      type='text'\r\n                      placeholder='Nama instansi atau perusahaan'\r\n                      value={formData.organization}\r\n                      onChange={handleInputChange}\r\n                      className='h-11 border-gray-200 focus:border-[var(--iai-primary)] focus:ring-[var(--iai-primary)]'\r\n                    />\r\n                  </div>\r\n\r\n                  {/* Password Fields */}\r\n                  <div className='space-y-2'>\r\n                    <Label htmlFor='password' className='text-sm font-medium text-gray-700'>\r\n                      Password *\r\n                    </Label>\r\n                    <div className='relative'>\r\n                      <LockIcon className='absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400' />\r\n                      <Input\r\n                        id='password'\r\n                        name='password'\r\n                        type={showPassword ? 'text' : 'password'}\r\n                        placeholder='Minimal 8 karakter'\r\n                        value={formData.password}\r\n                        onChange={handleInputChange}\r\n                        className='pl-10 pr-10 h-11 border-gray-200 focus:border-[var(--iai-primary)] focus:ring-[var(--iai-primary)]'\r\n                        required\r\n                      />\r\n                      <button\r\n                        type='button'\r\n                        onClick={() => setShowPassword(!showPassword)}\r\n                        className='absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600'\r\n                      >\r\n                        {showPassword ? (\r\n                          <EyeOffIcon className='w-4 h-4' />\r\n                        ) : (\r\n                          <EyeIcon className='w-4 h-4' />\r\n                        )}\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className='space-y-2'>\r\n                    <Label htmlFor='confirmPassword' className='text-sm font-medium text-gray-700'>\r\n                      Konfirmasi Password *\r\n                    </Label>\r\n                    <div className='relative'>\r\n                      <LockIcon className='absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400' />\r\n                      <Input\r\n                        id='confirmPassword'\r\n                        name='confirmPassword'\r\n                        type={showConfirmPassword ? 'text' : 'password'}\r\n                        placeholder='Ketik ulang password'\r\n                        value={formData.confirmPassword}\r\n                        onChange={handleInputChange}\r\n                        className='pl-10 pr-10 h-11 border-gray-200 focus:border-[var(--iai-primary)] focus:ring-[var(--iai-primary)]'\r\n                        required\r\n                      />\r\n                      <button\r\n                        type='button'\r\n                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}\r\n                        className='absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600'\r\n                      >\r\n                        {showConfirmPassword ? (\r\n                          <EyeOffIcon className='w-4 h-4' />\r\n                        ) : (\r\n                          <EyeIcon className='w-4 h-4' />\r\n                        )}\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Checkboxes */}\r\n                  <div className='space-y-3 pt-2'>\r\n                    <div className='flex items-start space-x-2'>\r\n                      <Checkbox\r\n                        id='agreeTerms'\r\n                        name='agreeTerms'\r\n                        checked={formData.agreeTerms}\r\n                        onCheckedChange={(checked) => \r\n                          setFormData(prev => ({ ...prev, agreeTerms: checked as boolean }))\r\n                        }\r\n                        className='data-[state=checked]:bg-[var(--iai-primary)] data-[state=checked]:border-[var(--iai-primary)] mt-0.5'\r\n                        required\r\n                      />\r\n                      <Label \r\n                        htmlFor='agreeTerms' \r\n                        className='text-sm text-gray-600 cursor-pointer leading-relaxed'\r\n                      >\r\n                        Saya menyetujui{' '}\r\n                        <Link\r\n                          href='/terms'\r\n                          className='text-[var(--iai-primary)] hover:text-[var(--iai-secondary)] font-medium underline'\r\n                        >\r\n                          Syarat & Ketentuan\r\n                        </Link>{' '}\r\n                        dan{' '}\r\n                        <Link\r\n                          href='/privacy'\r\n                          className='text-[var(--iai-primary)] hover:text-[var(--iai-secondary)] font-medium underline'\r\n                        >\r\n                          Kebijakan Privasi\r\n                        </Link>{' '}\r\n                        IAI LMS *\r\n                      </Label>\r\n                    </div>\r\n\r\n                    <div className='flex items-start space-x-2'>\r\n                      <Checkbox\r\n                        id='subscribeUpdates'\r\n                        name='subscribeUpdates'\r\n                        checked={formData.subscribeUpdates}\r\n                        onCheckedChange={(checked) => \r\n                          setFormData(prev => ({ ...prev, subscribeUpdates: checked as boolean }))\r\n                        }\r\n                        className='data-[state=checked]:bg-[var(--iai-primary)] data-[state=checked]:border-[var(--iai-primary)] mt-0.5'\r\n                      />\r\n                      <Label \r\n                        htmlFor='subscribeUpdates' \r\n                        className='text-sm text-gray-600 cursor-pointer leading-relaxed'\r\n                      >\r\n                        Saya ingin menerima informasi kursus baru dan update terbaru dari IAI LMS\r\n                      </Label>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Register Button */}\r\n                  <Button\r\n                    type='submit'\r\n                    variant='iai'\r\n                    className='w-full h-12 text-base font-medium mt-6'\r\n                    disabled={!formData.agreeTerms || loading}\r\n                  >\r\n                    {loading ? 'Mendaftar...' : 'Daftar Sekarang'}\r\n                  </Button>\r\n                </form>\r\n\r\n                {/* Sign In Link */}\r\n                <p className='text-center text-sm text-gray-600 mt-6'>\r\n                  Sudah punya akun?{' '}\r\n                  <Link\r\n                    href='/auth/sign-in'\r\n                    className='text-[var(--iai-primary)] hover:text-[var(--iai-secondary)] font-medium'\r\n                  >\r\n                    Masuk di sini\r\n                  </Link>\r\n                </p>\r\n              </CardContent>\r\n            </Card>\r\n\r\n\r\n            {/* Footer */}\r\n            <div className='text-center text-xs text-gray-500 space-y-1'>\r\n              <div className='flex items-center justify-center space-x-2 text-gray-400'>\r\n                <span>© 2024 IAI LMS - Powered by</span>\r\n                <img\r\n                  src=\"https://cdn.terang.ai/images/logo/logo-terang-ai.svg\"\r\n                  alt=\"Terang AI\"\r\n                  className='h-4 inline-block'\r\n                />\r\n              </div>\r\n            </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}", "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\kanban\\components\\board-column.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\kanban\\components\\column-action.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\kanban\\components\\kanban-board.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\kanban\\components\\kanban-view-page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\kanban\\components\\new-section-dialog.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\kanban\\components\\new-task-dialog.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\kanban\\components\\task-card.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\kanban\\utils\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\kanban\\utils\\store.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\overview\\components\\area-graph-skeleton.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\overview\\components\\area-graph.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\overview\\components\\bar-graph-skeleton.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\overview\\components\\bar-graph.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\overview\\components\\overview.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\overview\\components\\pie-graph-skeleton.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\overview\\components\\pie-graph.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\overview\\components\\recent-sales-skeleton.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\overview\\components\\recent-sales.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\products\\components\\product-form.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\products\\components\\product-listing.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\products\\components\\product-tables\\cell-action.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\products\\components\\product-tables\\columns.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\products\\components\\product-tables\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\products\\components\\product-tables\\options.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\products\\components\\product-view-page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\profile\\components\\profile-create-form.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\profile\\components\\profile-view-page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\profile\\utils\\form-schema.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\hooks\\use-auth.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\hooks\\use-breadcrumbs.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\hooks\\use-callback-ref.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\hooks\\use-callback-ref.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\hooks\\use-controllable-state.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\hooks\\use-data-table.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\hooks\\use-debounce.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\hooks\\use-debounced-callback.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\hooks\\use-media-query.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\hooks\\use-mobile.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\hooks\\use-multistep-form.tsx", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\hooks\\use-toast.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\instrumentation-client.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\instrumentation.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\lib\\auth.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\lib\\certificate.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\lib\\data-table.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\lib\\db\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\lib\\db\\raw.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\lib\\db\\schema.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\lib\\feature-flags.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\lib\\font.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\lib\\format.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\lib\\gemini\\batch-generator.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\lib\\gemini\\client.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\lib\\gemini\\content-generator.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\lib\\gemini\\course-outline.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\lib\\gemini\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\lib\\gemini\\quiz-generator.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\lib\\gemini\\types.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\lib\\parsers.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\lib\\searchparams.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\middleware.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\types\\data-table.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\types\\database.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\types\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\types\\lms.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\types\\tabler-icons.d.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\utils\\course-data-transformer.ts", [], [], "C:\\Users\\<USER>\\Documents\\File <PERSON><PERSON><PERSON>\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\mdx-editor-wrapper.tsx", [], [], {"ruleId": "1527", "severity": 1, "message": "1528", "line": 213, "column": 23, "nodeType": "1529", "endLine": 218, "endColumn": 25}, {"ruleId": "1527", "severity": 1, "message": "1528", "line": 223, "column": 23, "nodeType": "1529", "endLine": 228, "endColumn": 25}, {"ruleId": "1527", "severity": 1, "message": "1528", "line": 162, "column": 13, "nodeType": "1529", "endLine": 166, "endColumn": 15}, {"ruleId": "1530", "severity": 1, "message": "1531", "line": 80, "column": 16, "nodeType": "1529", "endLine": 80, "endColumn": 45}, {"ruleId": "1527", "severity": 1, "message": "1528", "line": 770, "column": 35, "nodeType": "1529", "endLine": 774, "endColumn": 37}, {"ruleId": "1527", "severity": 1, "message": "1528", "line": 952, "column": 37, "nodeType": "1529", "endLine": 952, "endColumn": 149}, {"ruleId": "1527", "severity": 1, "message": "1528", "line": 966, "column": 43, "nodeType": "1529", "endLine": 966, "endColumn": 162}, {"ruleId": "1527", "severity": 1, "message": "1528", "line": 371, "column": 17, "nodeType": "1529", "endLine": 377, "endColumn": 19}, {"ruleId": "1530", "severity": 1, "message": "1531", "line": 380, "column": 19, "nodeType": "1529", "endLine": 380, "endColumn": 70}, {"ruleId": "1527", "severity": 1, "message": "1528", "line": 11, "column": 11, "nodeType": "1529", "endLine": 15, "endColumn": 13}, {"ruleId": "1530", "severity": 1, "message": "1531", "line": 214, "column": 16, "nodeType": "1529", "endLine": 214, "endColumn": 60}, {"ruleId": "1527", "severity": 1, "message": "1528", "line": 449, "column": 19, "nodeType": "1529", "endLine": 462, "endColumn": 21}, {"ruleId": "1527", "severity": 1, "message": "1528", "line": 107, "column": 17, "nodeType": "1529", "endLine": 107, "endColumn": 130}, {"ruleId": "1527", "severity": 1, "message": "1528", "line": 92, "column": 21, "nodeType": "1529", "endLine": 92, "endColumn": 132}, {"ruleId": "1527", "severity": 1, "message": "1528", "line": 220, "column": 23, "nodeType": "1529", "endLine": 220, "endColumn": 137}, {"ruleId": "1527", "severity": 1, "message": "1528", "line": 82, "column": 21, "nodeType": "1529", "endLine": 82, "endColumn": 132}, {"ruleId": "1527", "severity": 1, "message": "1528", "line": 136, "column": 31, "nodeType": "1529", "endLine": 136, "endColumn": 144}, {"ruleId": "1527", "severity": 1, "message": "1528", "line": 244, "column": 23, "nodeType": "1529", "endLine": 244, "endColumn": 137}, {"ruleId": "1527", "severity": 1, "message": "1528", "line": 149, "column": 25, "nodeType": "1529", "endLine": 149, "endColumn": 136}, {"ruleId": "1527", "severity": 1, "message": "1528", "line": 181, "column": 33, "nodeType": "1529", "endLine": 181, "endColumn": 152}, {"ruleId": "1527", "severity": 1, "message": "1528", "line": 408, "column": 17, "nodeType": "1529", "endLine": 412, "endColumn": 19}, {"ruleId": "1527", "severity": 1, "message": "1528", "line": 543, "column": 17, "nodeType": "1529", "endLine": 547, "endColumn": 19}, "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "jsx-a11y/alt-text", "Image elements must have an alt prop, either with meaningful text, or an empty string for decorative images."]