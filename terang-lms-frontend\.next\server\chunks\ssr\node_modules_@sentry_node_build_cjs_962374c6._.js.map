{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/integrations/http/index.ts"], "sourcesContent": ["import type { ClientRequest, IncomingMessage, RequestOptions, ServerResponse } from 'node:http';\nimport { diag } from '@opentelemetry/api';\nimport type { HttpInstrumentationConfig } from '@opentelemetry/instrumentation-http';\nimport { HttpInstrumentation } from '@opentelemetry/instrumentation-http';\nimport type { Span } from '@sentry/core';\nimport { defineIntegration, getClient, hasSpansEnabled } from '@sentry/core';\nimport type { HTTPModuleRequestIncomingMessage, NodeClient } from '@sentry/node-core';\nimport {\n  type SentryHttpInstrumentationOptions,\n  addOriginToSpan,\n  generateInstrumentOnce,\n  getRequestUrl,\n  NODE_VERSION,\n  SentryHttpInstrumentation,\n} from '@sentry/node-core';\nimport type { NodeClientOptions } from '../../types';\n\nconst INTEGRATION_NAME = 'Http';\n\nconst INSTRUMENTATION_NAME = '@opentelemetry_sentry-patched/instrumentation-http';\n\ninterface HttpOptions {\n  /**\n   * Whether breadcrumbs should be recorded for outgoing requests.\n   * Defaults to true\n   */\n  breadcrumbs?: boolean;\n\n  /**\n   * If set to false, do not emit any spans.\n   * This will ensure that the default HttpInstrumentation from OpenTelemetry is not setup,\n   * only the Sentry-specific instrumentation for request isolation is applied.\n   *\n   * If `skipOpenTelemetrySetup: true` is configured, this defaults to `false`, otherwise it defaults to `true`.\n   */\n  spans?: boolean;\n\n  /**\n   * Whether the integration should create [Sessions](https://docs.sentry.io/product/releases/health/#sessions) for incoming requests to track the health and crash-free rate of your releases in Sentry.\n   * Read more about Release Health: https://docs.sentry.io/product/releases/health/\n   *\n   * Defaults to `true`.\n   */\n  trackIncomingRequestsAsSessions?: boolean;\n\n  /**\n   * Number of milliseconds until sessions tracked with `trackIncomingRequestsAsSessions` will be flushed as a session aggregate.\n   *\n   * Defaults to `60000` (60s).\n   */\n  sessionFlushingDelayMS?: number;\n\n  /**\n   * Do not capture spans or breadcrumbs for outgoing HTTP requests to URLs where the given callback returns `true`.\n   * This controls both span & breadcrumb creation - spans will be non recording if tracing is disabled.\n   *\n   * The `url` param contains the entire URL, including query string (if any), protocol, host, etc. of the outgoing request.\n   * For example: `'https://someService.com/users/details?id=123'`\n   *\n   * The `request` param contains the original {@type RequestOptions} object used to make the outgoing request.\n   * You can use it to filter on additional properties like method, headers, etc.\n   */\n  ignoreOutgoingRequests?: (url: string, request: RequestOptions) => boolean;\n\n  /**\n   * Do not capture spans for incoming HTTP requests to URLs where the given callback returns `true`.\n   * Spans will be non recording if tracing is disabled.\n   *\n   * The `urlPath` param consists of the URL path and query string (if any) of the incoming request.\n   * For example: `'/users/details?id=123'`\n   *\n   * The `request` param contains the original {@type IncomingMessage} object of the incoming request.\n   * You can use it to filter on additional properties like method, headers, etc.\n   */\n  ignoreIncomingRequests?: (urlPath: string, request: IncomingMessage) => boolean;\n\n  /**\n   * Do not capture spans for incoming HTTP requests with the given status codes.\n   * By default, spans with 404 status code are ignored.\n   * Expects an array of status codes or a range of status codes, e.g. [[300,399], 404] would ignore 3xx and 404 status codes.\n   *\n   * @default `[[401, 404], [300, 399]]`\n   */\n  dropSpansForIncomingRequestStatusCodes?: (number | [number, number])[];\n\n  /**\n   * Do not capture the request body for incoming HTTP requests to URLs where the given callback returns `true`.\n   * This can be useful for long running requests where the body is not needed and we want to avoid capturing it.\n   *\n   * @param url Contains the entire URL, including query string (if any), protocol, host, etc. of the incoming request.\n   * @param request Contains the {@type RequestOptions} object used to make the incoming request.\n   */\n  ignoreIncomingRequestBody?: (url: string, request: RequestOptions) => boolean;\n\n  /**\n   * Controls the maximum size of incoming HTTP request bodies attached to events.\n   *\n   * Available options:\n   * - 'none': No request bodies will be attached\n   * - 'small': Request bodies up to 1,000 bytes will be attached\n   * - 'medium': Request bodies up to 10,000 bytes will be attached (default)\n   * - 'always': Request bodies will always be attached\n   *\n   * Note that even with 'always' setting, bodies exceeding 1MB will never be attached\n   * for performance and security reasons.\n   *\n   * @default 'medium'\n   */\n  maxIncomingRequestBodySize?: 'none' | 'small' | 'medium' | 'always';\n\n  /**\n   * If true, do not generate spans for incoming requests at all.\n   * This is used by Remix to avoid generating spans for incoming requests, as it generates its own spans.\n   */\n  disableIncomingRequestSpans?: boolean;\n\n  /**\n   * Additional instrumentation options that are passed to the underlying HttpInstrumentation.\n   */\n  instrumentation?: {\n    requestHook?: (span: Span, req: ClientRequest | HTTPModuleRequestIncomingMessage) => void;\n    responseHook?: (span: Span, response: HTTPModuleRequestIncomingMessage | ServerResponse) => void;\n    applyCustomAttributesOnSpan?: (\n      span: Span,\n      request: ClientRequest | HTTPModuleRequestIncomingMessage,\n      response: HTTPModuleRequestIncomingMessage | ServerResponse,\n    ) => void;\n\n    /**\n     * You can pass any configuration through to the underlying instrumentation.\n     * Note that there are no semver guarantees for this!\n     */\n    _experimentalConfig?: ConstructorParameters<typeof HttpInstrumentation>[0];\n  };\n}\n\nconst instrumentSentryHttp = generateInstrumentOnce<SentryHttpInstrumentationOptions>(\n  `${INTEGRATION_NAME}.sentry`,\n  options => {\n    return new SentryHttpInstrumentation(options);\n  },\n);\n\nexport const instrumentOtelHttp = generateInstrumentOnce<HttpInstrumentationConfig>(INTEGRATION_NAME, config => {\n  const instrumentation = new HttpInstrumentation(config);\n\n  // We want to update the logger namespace so we can better identify what is happening here\n  try {\n    instrumentation['_diag'] = diag.createComponentLogger({\n      namespace: INSTRUMENTATION_NAME,\n    });\n    // @ts-expect-error We are writing a read-only property here...\n    instrumentation.instrumentationName = INSTRUMENTATION_NAME;\n  } catch {\n    // ignore errors here...\n  }\n\n  return instrumentation;\n});\n\n/** Exported only for tests. */\nexport function _shouldInstrumentSpans(options: HttpOptions, clientOptions: Partial<NodeClientOptions> = {}): boolean {\n  // If `spans` is passed in, it takes precedence\n  // Else, we by default emit spans, unless `skipOpenTelemetrySetup` is set to `true` or spans are not enabled\n  if (typeof options.spans === 'boolean') {\n    return options.spans;\n  }\n\n  if (clientOptions.skipOpenTelemetrySetup) {\n    return false;\n  }\n\n  // IMPORTANT: We only disable span instrumentation when spans are not enabled _and_ we are on Node 22+,\n  // as otherwise the necessary diagnostics channel is not available yet\n  if (!hasSpansEnabled(clientOptions) && NODE_VERSION.major >= 22) {\n    return false;\n  }\n\n  return true;\n}\n\n/**\n * The http integration instruments Node's internal http and https modules.\n * It creates breadcrumbs and spans for outgoing HTTP requests which will be attached to the currently active span.\n */\nexport const httpIntegration = defineIntegration((options: HttpOptions = {}) => {\n  const dropSpansForIncomingRequestStatusCodes = options.dropSpansForIncomingRequestStatusCodes ?? [\n    [401, 404],\n    [300, 399],\n  ];\n\n  return {\n    name: INTEGRATION_NAME,\n    setupOnce() {\n      const instrumentSpans = _shouldInstrumentSpans(options, getClient<NodeClient>()?.getOptions());\n\n      // This is Sentry-specific instrumentation for request isolation and breadcrumbs\n      instrumentSentryHttp({\n        ...options,\n        // If spans are not instrumented, it means the HttpInstrumentation has not been added\n        // In that case, we want to handle incoming trace extraction ourselves\n        extractIncomingTraceFromHeader: !instrumentSpans,\n        // If spans are not instrumented, it means the HttpInstrumentation has not been added\n        // In that case, we want to handle trace propagation ourselves\n        propagateTraceInOutgoingRequests: !instrumentSpans,\n      });\n\n      // This is the \"regular\" OTEL instrumentation that emits spans\n      if (instrumentSpans) {\n        const instrumentationConfig = getConfigWithDefaults(options);\n        instrumentOtelHttp(instrumentationConfig);\n      }\n    },\n    processEvent(event) {\n      // Drop transaction if it has a status code that should be ignored\n      if (event.type === 'transaction') {\n        const statusCode = event.contexts?.trace?.data?.['http.response.status_code'];\n        if (\n          typeof statusCode === 'number' &&\n          dropSpansForIncomingRequestStatusCodes.some(code => {\n            if (typeof code === 'number') {\n              return code === statusCode;\n            }\n\n            const [min, max] = code;\n            return statusCode >= min && statusCode <= max;\n          })\n        ) {\n          return null;\n        }\n      }\n\n      return event;\n    },\n  };\n});\n\n/**\n * Determines if @param req is a ClientRequest, meaning the request was created within the express app\n * and it's an outgoing request.\n * Checking for properties instead of using `instanceOf` to avoid importing the request classes.\n */\nfunction _isClientRequest(req: ClientRequest | HTTPModuleRequestIncomingMessage): req is ClientRequest {\n  return 'outputData' in req && 'outputSize' in req && !('client' in req) && !('statusCode' in req);\n}\n\n/**\n * Detects if an incoming request is a prefetch request.\n */\nfunction isKnownPrefetchRequest(req: HTTPModuleRequestIncomingMessage): boolean {\n  // Currently only handles Next.js prefetch requests but may check other frameworks in the future.\n  return req.headers['next-router-prefetch'] === '1';\n}\n\nfunction getConfigWithDefaults(options: Partial<HttpOptions> = {}): HttpInstrumentationConfig {\n  const instrumentationConfig = {\n    ...options.instrumentation?._experimentalConfig,\n\n    disableIncomingRequestInstrumentation: options.disableIncomingRequestSpans,\n\n    ignoreOutgoingRequestHook: request => {\n      const url = getRequestUrl(request);\n\n      if (!url) {\n        return false;\n      }\n\n      const _ignoreOutgoingRequests = options.ignoreOutgoingRequests;\n      if (_ignoreOutgoingRequests?.(url, request)) {\n        return true;\n      }\n\n      return false;\n    },\n\n    ignoreIncomingRequestHook: request => {\n      // request.url is the only property that holds any information about the url\n      // it only consists of the URL path and query string (if any)\n      const urlPath = request.url;\n\n      const method = request.method?.toUpperCase();\n      // We do not capture OPTIONS/HEAD requests as transactions\n      if (method === 'OPTIONS' || method === 'HEAD') {\n        return true;\n      }\n\n      const _ignoreIncomingRequests = options.ignoreIncomingRequests;\n      if (urlPath && _ignoreIncomingRequests?.(urlPath, request)) {\n        return true;\n      }\n\n      return false;\n    },\n\n    requireParentforOutgoingSpans: false,\n    requireParentforIncomingSpans: false,\n    requestHook: (span, req) => {\n      addOriginToSpan(span, 'auto.http.otel.http');\n      if (!_isClientRequest(req) && isKnownPrefetchRequest(req)) {\n        span.setAttribute('sentry.http.prefetch', true);\n      }\n\n      options.instrumentation?.requestHook?.(span, req);\n    },\n    responseHook: (span, res) => {\n      options.instrumentation?.responseHook?.(span, res);\n    },\n    applyCustomAttributesOnSpan: (\n      span: Span,\n      request: ClientRequest | HTTPModuleRequestIncomingMessage,\n      response: HTTPModuleRequestIncomingMessage | ServerResponse,\n    ) => {\n      options.instrumentation?.applyCustomAttributesOnSpan?.(span, request, response);\n    },\n  } satisfies HttpInstrumentationConfig;\n\n  return instrumentationConfig;\n}\n"], "names": ["generateInstrumentOnce", "SentryHttpInstrumentation", "HttpInstrumentation", "diag", "hasSpansEnabled", "NODE_VERSION", "defineIntegration", "getClient", "getRequestUrl", "addOriginToSpan"], "mappings": ";;;;;;;AAiBA,MAAM,gBAAA,GAAmB,MAAM;AAE/B,MAAM,oBAAA,GAAuB,oDAAoD;AAqHjF,MAAM,oBAAA,GAAuBA,SAAAA,sBAAsB,CACjD,CAAC,EAAA,gBAAA,CAAA,OAAA,CAAA,GACA,OAAA,IAAA;IACA,OAAA,IAAAC,SAAAA,yBAAA,CAAA,OAAA,CAAA;AACA,CAAA;AAGA,MAAA,kBAAA,GAAAD,SAAAA,sBAAA,CAAA,gBAAA,GAAA,MAAA,IAAA;IACA,MAAA,eAAA,GAAA,IAAAE,oBAAAA,mBAAA,CAAA,MAAA,CAAA;IAEA,0FAAA;IACA,IAAA;QACA,eAAA,CAAA,OAAA,CAAA,GAAAC,IAAAA,IAAA,CAAA,qBAAA,CAAA;YACA,SAAA,EAAA,oBAAA;QACA,CAAA,CAAA;QACA,+DAAA;QACA,eAAA,CAAA,mBAAA,GAAA,oBAAA;IACA,CAAA,CAAA,OAAA;IACA,wBAAA;IACA;IAEA,OAAA,eAAA;AACA,CAAA;AAEA,6BAAA,GACA,SAAA,sBAAA,CAAA,OAAA,EAAA,aAAA,GAAA,CAAA,CAAA,EAAA;IACA,+CAAA;IACA,4GAAA;IACA,IAAA,OAAA,OAAA,CAAA,KAAA,KAAA,SAAA,EAAA;QACA,OAAA,OAAA,CAAA,KAAA;IACA;IAEA,IAAA,aAAA,CAAA,sBAAA,EAAA;QACA,OAAA,KAAA;IACA;IAEA,uGAAA;IACA,sEAAA;IACA,IAAA,CAAAC,KAAAA,eAAA,CAAA,aAAA,CAAA,IAAAC,SAAAA,YAAA,CAAA,KAAA,IAAA,EAAA,EAAA;QACA,OAAA,KAAA;IACA;IAEA,OAAA,IAAA;AACA;AAEA;;;CAGA,GACA,MAAA,eAAA,GAAAC,KAAAA,iBAAA,CAAA,CAAA,OAAA,GAAA,CAAA,CAAA,KAAA;IACA,MAAA,sCAAA,GAAA,OAAA,CAAA,sCAAA,IAAA;QACA;YAAA,GAAA;YAAA,GAAA;SAAA;QACA;YAAA,GAAA;YAAA,GAAA;SAAA;KACA;IAEA,OAAA;QACA,IAAA,EAAA,gBAAA;QACA,SAAA,GAAA;YACA,MAAA,eAAA,GAAA,sBAAA,CAAA,OAAA,EAAAC,KAAAA,SAAA,EAAA,EAAA,UAAA,EAAA,CAAA;YAEA,gFAAA;YACA,oBAAA,CAAA;gBACA,GAAA,OAAA;gBACA,qFAAA;gBACA,sEAAA;gBACA,8BAAA,EAAA,CAAA,eAAA;gBACA,qFAAA;gBACA,8DAAA;gBACA,gCAAA,EAAA,CAAA,eAAA;YACA,CAAA,CAAA;YAEA,8DAAA;YACA,IAAA,eAAA,EAAA;gBACA,MAAA,qBAAA,GAAA,qBAAA,CAAA,OAAA,CAAA;gBACA,kBAAA,CAAA,qBAAA,CAAA;YACA;QACA,CAAA;QACA,YAAA,EAAA,KAAA,EAAA;YACA,kEAAA;YACA,IAAA,KAAA,CAAA,IAAA,KAAA,aAAA,EAAA;gBACA,MAAA,UAAA,GAAA,KAAA,CAAA,QAAA,EAAA,KAAA,EAAA,IAAA,EAAA,CAAA,2BAAA,CAAA;gBACA,IACA,OAAA,UAAA,KAAA,QAAA,IACA,sCAAA,CAAA,IAAA,EAAA,IAAA,IAAA;oBACA,IAAA,OAAA,IAAA,KAAA,QAAA,EAAA;wBACA,OAAA,IAAA,KAAA,UAAA;oBACA;oBAEA,MAAA,CAAA,GAAA,EAAA,GAAA,CAAA,GAAA,IAAA;oBACA,OAAA,UAAA,IAAA,GAAA,IAAA,UAAA,IAAA,GAAA;gBACA,CAAA,GACA;oBACA,OAAA,IAAA;gBACA;YACA;YAEA,OAAA,KAAA;QACA,CAAA;IACA,CAAA;AACA,CAAA;AAEA;;;;CAIA,GACA,SAAA,gBAAA,CAAA,GAAA,EAAA;IACA,OAAA,YAAA,IAAA,GAAA,IAAA,YAAA,IAAA,GAAA,IAAA,CAAA,CAAA,QAAA,IAAA,GAAA,CAAA,IAAA,CAAA,CAAA,YAAA,IAAA,GAAA,CAAA;AACA;AAEA;;CAEA,GACA,SAAA,sBAAA,CAAA,GAAA,EAAA;IACA,iGAAA;IACA,OAAA,GAAA,CAAA,OAAA,CAAA,sBAAA,CAAA,KAAA,GAAA;AACA;AAEA,SAAA,qBAAA,CAAA,OAAA,GAAA,CAAA,CAAA,EAAA;IACA,MAAA,qBAAA,GAAA;QACA,GAAA,OAAA,CAAA,eAAA,EAAA,mBAAA;QAEA,qCAAA,EAAA,OAAA,CAAA,2BAAA;QAEA,yBAAA,GAAA,OAAA,IAAA;YACA,MAAA,GAAA,GAAAC,SAAAA,aAAA,CAAA,OAAA,CAAA;YAEA,IAAA,CAAA,GAAA,EAAA;gBACA,OAAA,KAAA;YACA;YAEA,MAAA,uBAAA,GAAA,OAAA,CAAA,sBAAA;YACA,IAAA,uBAAA,GAAA,GAAA,EAAA,OAAA,CAAA,EAAA;gBACA,OAAA,IAAA;YACA;YAEA,OAAA,KAAA;QACA,CAAA;QAEA,yBAAA,GAAA,OAAA,IAAA;YACA,4EAAA;YACA,6DAAA;YACA,MAAA,OAAA,GAAA,OAAA,CAAA,GAAA;YAEA,MAAA,MAAA,GAAA,OAAA,CAAA,MAAA,EAAA,WAAA,EAAA;YACA,0DAAA;YACA,IAAA,MAAA,KAAA,SAAA,IAAA,MAAA,KAAA,MAAA,EAAA;gBACA,OAAA,IAAA;YACA;YAEA,MAAA,uBAAA,GAAA,OAAA,CAAA,sBAAA;YACA,IAAA,OAAA,IAAA,uBAAA,GAAA,OAAA,EAAA,OAAA,CAAA,EAAA;gBACA,OAAA,IAAA;YACA;YAEA,OAAA,KAAA;QACA,CAAA;QAEA,6BAAA,EAAA,KAAA;QACA,6BAAA,EAAA,KAAA;QACA,WAAA,EAAA,CAAA,IAAA,EAAA,GAAA,KAAA;YACAC,SAAAA,eAAA,CAAA,IAAA,EAAA,qBAAA,CAAA;YACA,IAAA,CAAA,gBAAA,CAAA,GAAA,CAAA,IAAA,sBAAA,CAAA,GAAA,CAAA,EAAA;gBACA,IAAA,CAAA,YAAA,CAAA,sBAAA,EAAA,IAAA,CAAA;YACA;YAEA,OAAA,CAAA,eAAA,EAAA,WAAA,GAAA,IAAA,EAAA,GAAA,CAAA;QACA,CAAA;QACA,YAAA,EAAA,CAAA,IAAA,EAAA,GAAA,KAAA;YACA,OAAA,CAAA,eAAA,EAAA,YAAA,GAAA,IAAA,EAAA,GAAA,CAAA;QACA,CAAA;QACA,2BAAA,EAAA,CACA,IAAA,EACA,OAAA,EACA,QAAA;YAEA,OAAA,CAAA,eAAA,EAAA,2BAAA,GAAA,IAAA,EAAA,OAAA,EAAA,QAAA,CAAA;QACA,CAAA;IACA,CAAA;IAEA,OAAA,qBAAA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 169, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/integrations/node-fetch/index.ts"], "sourcesContent": ["import type { UndiciInstrumentationConfig } from '@opentelemetry/instrumentation-undici';\nimport { UndiciInstrumentation } from '@opentelemetry/instrumentation-undici';\nimport type { IntegrationFn } from '@sentry/core';\nimport { defineIntegration, getClient, hasSpansEnabled, SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN } from '@sentry/core';\nimport type { NodeClient } from '@sentry/node-core';\nimport { generateInstrumentOnce, SentryNodeFetchInstrumentation } from '@sentry/node-core';\nimport type { NodeClientOptions } from '../../types';\n\nconst INTEGRATION_NAME = 'NodeFetch';\n\ninterface NodeFetchOptions {\n  /**\n   * Whether breadcrumbs should be recorded for requests.\n   * Defaults to true\n   */\n  breadcrumbs?: boolean;\n\n  /**\n   * If set to false, do not emit any spans.\n   * This will ensure that the default UndiciInstrumentation from OpenTelemetry is not setup,\n   * only the Sentry-specific instrumentation for breadcrumbs & trace propagation is applied.\n   *\n   * If `skipOpenTelemetrySetup: true` is configured, this defaults to `false`, otherwise it defaults to `true`.\n   */\n  spans?: boolean;\n\n  /**\n   * Do not capture spans or breadcrumbs for outgoing fetch requests to URLs where the given callback returns `true`.\n   * This controls both span & breadcrumb creation - spans will be non recording if tracing is disabled.\n   */\n  ignoreOutgoingRequests?: (url: string) => boolean;\n}\n\nconst instrumentOtelNodeFetch = generateInstrumentOnce(\n  INTEGRATION_NAME,\n  UndiciInstrumentation,\n  (options: NodeFetchOptions) => {\n    return getConfigWithDefaults(options);\n  },\n);\n\nconst instrumentSentryNodeFetch = generateInstrumentOnce(\n  `${INTEGRATION_NAME}.sentry`,\n  SentryNodeFetchInstrumentation,\n  (options: NodeFetchOptions) => {\n    return options;\n  },\n);\n\nconst _nativeNodeFetchIntegration = ((options: NodeFetchOptions = {}) => {\n  return {\n    name: 'NodeFetch',\n    setupOnce() {\n      const instrumentSpans = _shouldInstrumentSpans(options, getClient<NodeClient>()?.getOptions());\n\n      // This is the \"regular\" OTEL instrumentation that emits spans\n      if (instrumentSpans) {\n        instrumentOtelNodeFetch(options);\n      }\n\n      // This is the Sentry-specific instrumentation that creates breadcrumbs & propagates traces\n      // This must be registered after the OTEL one, to ensure that the core trace propagation logic takes presedence\n      // Otherwise, the sentry-trace header may be set multiple times\n      instrumentSentryNodeFetch(options);\n    },\n  };\n}) satisfies IntegrationFn;\n\nexport const nativeNodeFetchIntegration = defineIntegration(_nativeNodeFetchIntegration);\n\n// Matching the behavior of the base instrumentation\nfunction getAbsoluteUrl(origin: string, path: string = '/'): string {\n  const url = `${origin}`;\n\n  if (url.endsWith('/') && path.startsWith('/')) {\n    return `${url}${path.slice(1)}`;\n  }\n\n  if (!url.endsWith('/') && !path.startsWith('/')) {\n    return `${url}/${path.slice(1)}`;\n  }\n\n  return `${url}${path}`;\n}\n\nfunction _shouldInstrumentSpans(options: NodeFetchOptions, clientOptions: Partial<NodeClientOptions> = {}): boolean {\n  // If `spans` is passed in, it takes precedence\n  // Else, we by default emit spans, unless `skipOpenTelemetrySetup` is set to `true` or spans are not enabled\n  return typeof options.spans === 'boolean'\n    ? options.spans\n    : !clientOptions.skipOpenTelemetrySetup && hasSpansEnabled(clientOptions);\n}\n\nfunction getConfigWithDefaults(options: Partial<NodeFetchOptions> = {}): UndiciInstrumentationConfig {\n  const instrumentationConfig = {\n    requireParentforSpans: false,\n    ignoreRequestHook: request => {\n      const url = getAbsoluteUrl(request.origin, request.path);\n      const _ignoreOutgoingRequests = options.ignoreOutgoingRequests;\n      const shouldIgnore = _ignoreOutgoingRequests && url && _ignoreOutgoingRequests(url);\n\n      return !!shouldIgnore;\n    },\n    startSpanHook: () => {\n      return {\n        [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.http.otel.node_fetch',\n      };\n    },\n  } satisfies UndiciInstrumentationConfig;\n\n  return instrumentationConfig;\n}\n"], "names": ["generateInstrumentOnce", "UndiciInstrumentation", "SentryNodeFetchInstrumentation", "getClient", "defineIntegration", "hasSpansEnabled", "SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN"], "mappings": ";;;;;;AAQA,MAAM,gBAAA,GAAmB,WAAW;AAyBpC,MAAM,uBAAA,GAA0BA,SAAAA,sBAAsB,CACpD,gBAAgB,EAChBC,sBAAAA,qBAAqB,EACrB,CAAC,OAAO,KAAuB;IAC7B,OAAO,qBAAqB,CAAC,OAAO,CAAC;AACzC,CAAG;AAGH,MAAM,yBAAA,GAA4BD,SAAAA,sBAAsB,CACtD,CAAC,EAAA,gBAAA,CAAA,OAAA,CAAA,EACAE,SAAAA,8BAAA,EACA,CAAA,OAAA,KAAA;IACA,OAAA,OAAA;AACA,CAAA;AAGA,MAAA,2BAAA,GAAA,CAAA,OAAA,GAAA,CAAA,CAAA,KAAA;IACA,OAAA;QACA,IAAA,EAAA,WAAA;QACA,SAAA,GAAA;YACA,MAAA,eAAA,GAAA,sBAAA,CAAA,OAAA,EAAAC,KAAAA,SAAA,EAAA,EAAA,UAAA,EAAA,CAAA;YAEA,8DAAA;YACA,IAAA,eAAA,EAAA;gBACA,uBAAA,CAAA,OAAA,CAAA;YACA;YAEA,2FAAA;YACA,+GAAA;YACA,+DAAA;YACA,yBAAA,CAAA,OAAA,CAAA;QACA,CAAA;IACA,CAAA;AACA,CAAA,CAAA;AAEA,MAAA,0BAAA,GAAAC,KAAAA,iBAAA,CAAA,2BAAA;AAEA,oDAAA;AACA,SAAA,cAAA,CAAA,MAAA,EAAA,IAAA,GAAA,GAAA,EAAA;IACA,MAAA,GAAA,GAAA,CAAA,EAAA,MAAA,CAAA,CAAA;IAEA,IAAA,GAAA,CAAA,QAAA,CAAA,GAAA,CAAA,IAAA,IAAA,CAAA,UAAA,CAAA,GAAA,CAAA,EAAA;QACA,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACA;IAEA,IAAA,CAAA,GAAA,CAAA,QAAA,CAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,UAAA,CAAA,GAAA,CAAA,EAAA;QACA,OAAA,CAAA,EAAA,GAAA,CAAA,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACA;IAEA,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,IAAA,CAAA,CAAA;AACA;AAEA,SAAA,sBAAA,CAAA,OAAA,EAAA,aAAA,GAAA,CAAA,CAAA,EAAA;IACA,+CAAA;IACA,4GAAA;IACA,OAAA,OAAA,OAAA,CAAA,KAAA,KAAA,YACA,OAAA,CAAA,KAAA,GACA,CAAA,aAAA,CAAA,sBAAA,IAAAC,KAAAA,eAAA,CAAA,aAAA,CAAA;AACA;AAEA,SAAA,qBAAA,CAAA,OAAA,GAAA,CAAA,CAAA,EAAA;IACA,MAAA,qBAAA,GAAA;QACA,qBAAA,EAAA,KAAA;QACA,iBAAA,GAAA,OAAA,IAAA;YACA,MAAA,GAAA,GAAA,cAAA,CAAA,OAAA,CAAA,MAAA,EAAA,OAAA,CAAA,IAAA,CAAA;YACA,MAAA,uBAAA,GAAA,OAAA,CAAA,sBAAA;YACA,MAAA,YAAA,GAAA,uBAAA,IAAA,GAAA,IAAA,uBAAA,CAAA,GAAA,CAAA;YAEA,OAAA,CAAA,CAAA,YAAA;QACA,CAAA;QACA,aAAA,EAAA,MAAA;YACA,OAAA;gBACA,CAAAC,KAAAA,gCAAA,CAAA,EAAA,2BAAA;YACA,CAAA;QACA,CAAA;IACA,CAAA;IAEA,OAAA,qBAAA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 238, "column": 0}, "map": {"version": 3, "file": "fs.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/integrations/fs.ts"], "sourcesContent": ["import { FsInstrumentation } from '@opentelemetry/instrumentation-fs';\nimport { defineIntegration, SEMANTIC_ATTRIBUTE_SENTRY_OP, SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN } from '@sentry/core';\nimport { generateInstrumentOnce } from '@sentry/node-core';\n\nconst INTEGRATION_NAME = 'FileSystem';\n\n/**\n * This integration will create spans for `fs` API operations, like reading and writing files.\n *\n * **WARNING:** This integration may add significant overhead to your application. Especially in scenarios with a lot of\n * file I/O, like for example when running a framework dev server, including this integration can massively slow down\n * your application.\n *\n * @param options Configuration for this integration.\n */\nexport const fsIntegration = defineIntegration(\n  (\n    options: {\n      /**\n       * Setting this option to `true` will include any filepath arguments from your `fs` API calls as span attributes.\n       *\n       * Defaults to `false`.\n       */\n      recordFilePaths?: boolean;\n\n      /**\n       * Setting this option to `true` will include the error messages of failed `fs` API calls as a span attribute.\n       *\n       * Defaults to `false`.\n       */\n      recordErrorMessagesAsSpanAttributes?: boolean;\n    } = {},\n  ) => {\n    return {\n      name: INTEGRATION_NAME,\n      setupOnce() {\n        generateInstrumentOnce(\n          INTEGRATION_NAME,\n          () =>\n            new FsInstrumentation({\n              requireParentSpan: true,\n              endHook(functionName, { args, span, error }) {\n                span.updateName(`fs.${functionName}`);\n\n                span.setAttributes({\n                  [SEMANTIC_ATTRIBUTE_SENTRY_OP]: 'file',\n                  [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.file.fs',\n                });\n\n                if (options.recordErrorMessagesAsSpanAttributes) {\n                  if (typeof args[0] === 'string' && FS_OPERATIONS_WITH_PATH_ARG.includes(functionName)) {\n                    span.setAttribute('path_argument', args[0]);\n                  } else if (\n                    typeof args[0] === 'string' &&\n                    typeof args[1] === 'string' &&\n                    FS_OPERATIONS_WITH_TARGET_PATH.includes(functionName)\n                  ) {\n                    span.setAttribute('target_argument', args[0]);\n                    span.setAttribute('path_argument', args[1]);\n                  } else if (typeof args[0] === 'string' && FS_OPERATIONS_WITH_PREFIX.includes(functionName)) {\n                    span.setAttribute('prefix_argument', args[0]);\n                  } else if (\n                    typeof args[0] === 'string' &&\n                    typeof args[1] === 'string' &&\n                    FS_OPERATIONS_WITH_EXISTING_PATH_NEW_PATH.includes(functionName)\n                  ) {\n                    span.setAttribute('existing_path_argument', args[0]);\n                    span.setAttribute('new_path_argument', args[1]);\n                  } else if (\n                    typeof args[0] === 'string' &&\n                    typeof args[1] === 'string' &&\n                    FS_OPERATIONS_WITH_SRC_DEST.includes(functionName)\n                  ) {\n                    span.setAttribute('src_argument', args[0]);\n                    span.setAttribute('dest_argument', args[1]);\n                  } else if (\n                    typeof args[0] === 'string' &&\n                    typeof args[1] === 'string' &&\n                    FS_OPERATIONS_WITH_OLD_PATH_NEW_PATH.includes(functionName)\n                  ) {\n                    span.setAttribute('old_path_argument', args[0]);\n                    span.setAttribute('new_path_argument', args[1]);\n                  }\n                }\n\n                if (error && options.recordErrorMessagesAsSpanAttributes) {\n                  span.setAttribute('fs_error', error.message);\n                }\n              },\n            }),\n        )();\n      },\n    };\n  },\n);\n\nconst FS_OPERATIONS_WITH_OLD_PATH_NEW_PATH = ['rename', 'renameSync'];\nconst FS_OPERATIONS_WITH_SRC_DEST = ['copyFile', 'cp', 'copyFileSync', 'cpSync'];\nconst FS_OPERATIONS_WITH_EXISTING_PATH_NEW_PATH = ['link', 'linkSync'];\nconst FS_OPERATIONS_WITH_PREFIX = ['mkdtemp', 'mkdtempSync'];\nconst FS_OPERATIONS_WITH_TARGET_PATH = ['symlink', 'symlinkSync'];\nconst FS_OPERATIONS_WITH_PATH_ARG = [\n  'access',\n  'appendFile',\n  'chmod',\n  'chown',\n  'exists',\n  'mkdir',\n  'lchown',\n  'lstat',\n  'lutimes',\n  'open',\n  'opendir',\n  'readdir',\n  'readFile',\n  'readlink',\n  'realpath',\n  'realpath.native',\n  'rm',\n  'rmdir',\n  'stat',\n  'truncate',\n  'unlink',\n  'utimes',\n  'writeFile',\n  'accessSync',\n  'appendFileSync',\n  'chmodSync',\n  'chownSync',\n  'existsSync',\n  'lchownSync',\n  'lstatSync',\n  'lutimesSync',\n  'opendirSync',\n  'mkdirSync',\n  'openSync',\n  'readdirSync',\n  'readFileSync',\n  'readlinkSync',\n  'realpathSync',\n  'realpathSync.native',\n  'rmdirSync',\n  'rmSync',\n  'statSync',\n  'truncateSync',\n  'unlinkSync',\n  'utimesSync',\n  'writeFileSync',\n];\n"], "names": ["defineIntegration", "generateInstrumentOnce", "FsInstrumentation", "SEMANTIC_ATTRIBUTE_SENTRY_OP", "SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN"], "mappings": ";;;;;;AAIA,MAAM,gBAAA,GAAmB,YAAY;AAErC;;;;;;;;CAQA,GACO,MAAM,aAAA,GAAgBA,KAAAA,iBAAiB,CAC5C,CACE,UAcI,CAAA,CAAE;IAEN,OAAO;QACL,IAAI,EAAE,gBAAgB;QACtB,SAAS,GAAG;YACVC,SAAAA,sBAAsB,CACpB,gBAAgB,EAChB,IACE,IAAIC,kBAAAA,iBAAiB,CAAC;oBACpB,iBAAiB,EAAE,IAAI;oBACvB,OAAO,EAAC,YAAY,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,KAAA,EAAO,EAAE;wBAC3C,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,EAAE,YAAY,CAAC,CAAA,CAAA;wBAEA,IAAA,CAAA,aAAA,CAAA;4BACA,CAAAC,KAAAA,4BAAA,CAAA,EAAA,MAAA;4BACA,CAAAC,KAAAA,gCAAA,CAAA,EAAA,cAAA;wBACA,CAAA,CAAA;wBAEA,IAAA,OAAA,CAAA,mCAAA,EAAA;4BACA,IAAA,OAAA,IAAA,CAAA,CAAA,CAAA,KAAA,QAAA,IAAA,2BAAA,CAAA,QAAA,CAAA,YAAA,CAAA,EAAA;gCACA,IAAA,CAAA,YAAA,CAAA,eAAA,EAAA,IAAA,CAAA,CAAA,CAAA,CAAA;4BACA,CAAA,MAAA,IACA,OAAA,IAAA,CAAA,CAAA,CAAA,KAAA,QAAA,IACA,OAAA,IAAA,CAAA,CAAA,CAAA,KAAA,QAAA,IACA,8BAAA,CAAA,QAAA,CAAA,YAAA,GACA;gCACA,IAAA,CAAA,YAAA,CAAA,iBAAA,EAAA,IAAA,CAAA,CAAA,CAAA,CAAA;gCACA,IAAA,CAAA,YAAA,CAAA,eAAA,EAAA,IAAA,CAAA,CAAA,CAAA,CAAA;4BACA,CAAA,MAAA,IAAA,OAAA,IAAA,CAAA,CAAA,CAAA,KAAA,QAAA,IAAA,yBAAA,CAAA,QAAA,CAAA,YAAA,CAAA,EAAA;gCACA,IAAA,CAAA,YAAA,CAAA,iBAAA,EAAA,IAAA,CAAA,CAAA,CAAA,CAAA;4BACA,CAAA,MAAA,IACA,OAAA,IAAA,CAAA,CAAA,CAAA,KAAA,QAAA,IACA,OAAA,IAAA,CAAA,CAAA,CAAA,KAAA,QAAA,IACA,yCAAA,CAAA,QAAA,CAAA,YAAA,GACA;gCACA,IAAA,CAAA,YAAA,CAAA,wBAAA,EAAA,IAAA,CAAA,CAAA,CAAA,CAAA;gCACA,IAAA,CAAA,YAAA,CAAA,mBAAA,EAAA,IAAA,CAAA,CAAA,CAAA,CAAA;4BACA,CAAA,MAAA,IACA,OAAA,IAAA,CAAA,CAAA,CAAA,KAAA,QAAA,IACA,OAAA,IAAA,CAAA,CAAA,CAAA,KAAA,QAAA,IACA,2BAAA,CAAA,QAAA,CAAA,YAAA,GACA;gCACA,IAAA,CAAA,YAAA,CAAA,cAAA,EAAA,IAAA,CAAA,CAAA,CAAA,CAAA;gCACA,IAAA,CAAA,YAAA,CAAA,eAAA,EAAA,IAAA,CAAA,CAAA,CAAA,CAAA;4BACA,CAAA,MAAA,IACA,OAAA,IAAA,CAAA,CAAA,CAAA,KAAA,QAAA,IACA,OAAA,IAAA,CAAA,CAAA,CAAA,KAAA,QAAA,IACA,oCAAA,CAAA,QAAA,CAAA,YAAA,GACA;gCACA,IAAA,CAAA,YAAA,CAAA,mBAAA,EAAA,IAAA,CAAA,CAAA,CAAA,CAAA;gCACA,IAAA,CAAA,YAAA,CAAA,mBAAA,EAAA,IAAA,CAAA,CAAA,CAAA,CAAA;4BACA;wBACA;wBAEA,IAAA,KAAA,IAAA,OAAA,CAAA,mCAAA,EAAA;4BACA,IAAA,CAAA,YAAA,CAAA,UAAA,EAAA,KAAA,CAAA,OAAA,CAAA;wBACA;oBACA,CAAA;gBACA,CAAA,CAAA;QAEA,CAAA;IACA,CAAA;AACA,CAAA;AAGA,MAAA,oCAAA,GAAA;IAAA,QAAA;IAAA,YAAA;CAAA;AACA,MAAA,2BAAA,GAAA;IAAA,UAAA;IAAA,IAAA;IAAA,cAAA;IAAA,QAAA;CAAA;AACA,MAAA,yCAAA,GAAA;IAAA,MAAA;IAAA,UAAA;CAAA;AACA,MAAA,yBAAA,GAAA;IAAA,SAAA;IAAA,aAAA;CAAA;AACA,MAAA,8BAAA,GAAA;IAAA,SAAA;IAAA,aAAA;CAAA;AACA,MAAA,2BAAA,GAAA;IACA,QAAA;IACA,YAAA;IACA,OAAA;IACA,OAAA;IACA,QAAA;IACA,OAAA;IACA,QAAA;IACA,OAAA;IACA,SAAA;IACA,MAAA;IACA,SAAA;IACA,SAAA;IACA,UAAA;IACA,UAAA;IACA,UAAA;IACA,iBAAA;IACA,IAAA;IACA,OAAA;IACA,MAAA;IACA,UAAA;IACA,QAAA;IACA,QAAA;IACA,WAAA;IACA,YAAA;IACA,gBAAA;IACA,WAAA;IACA,WAAA;IACA,YAAA;IACA,YAAA;IACA,WAAA;IACA,aAAA;IACA,aAAA;IACA,WAAA;IACA,UAAA;IACA,aAAA;IACA,cAAA;IACA,cAAA;IACA,cAAA;IACA,qBAAA;IACA,WAAA;IACA,QAAA;IACA,UAAA;IACA,cAAA;IACA,YAAA;IACA,YAAA;IACA,eAAA;CACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 368, "column": 0}, "map": {"version": 3, "file": "debug-build.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/debug-build.ts"], "sourcesContent": ["declare const __DEBUG_BUILD__: boolean;\n\n/**\n * This serves as a build time flag that will be true by default, but false in non-debug builds or if users replace `__SENTRY_DEBUG__` in their generated code.\n *\n * ATTENTION: This constant must never cross package boundaries (i.e. be exported) to guarantee that it can be used for tree shaking.\n */\nexport const DEBUG_BUILD = __DEBUG_BUILD__;\n"], "names": [], "mappings": ";;;AAEA;;;;CAIA,GACO,MAAM,WAAA,GAAc,OAAA,gBAAA,KAAA,WAAA,IAAA,gBAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 382, "column": 0}, "map": {"version": 3, "file": "AttributeNames.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/integrations/tracing/express-v5/enums/AttributeNames.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport enum AttributeNames {\n  EXPRESS_TYPE = 'express.type',\n  EXPRESS_NAME = 'express.name',\n}\n"], "names": ["AttributeNames"], "mappings": ";;;AAAA;;;;;;;;;;;;;;CAcA,GACAA,QAAAA,cAAAA,GAAAA,KAAAA,EAAA;AAAA,CAAA,SAAA,cAAA,EAA2B;IACzB,MAAA,YAAA,GAAe,cAAA,CAAA;IAAA,cAAA,CAAA,cAAA,CAAA,GAAA,YAAA;IACf,MAAA,YAAA,GAAe,cAAA,CAAA;IAAA,cAAA,CAAA,cAAA,CAAA,GAAA,YAAA;AACjB,CAAA,EAAAA,QAAAA,cAAA,IAAA,CAAAA,QAAAA,cAAA,GAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 411, "column": 0}, "map": {"version": 3, "file": "ExpressLayerType.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/integrations/tracing/express-v5/enums/ExpressLayerType.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport enum ExpressLayerType {\n  ROUTER = 'router',\n  MIDDLEWARE = 'middleware',\n  REQUEST_HANDLER = 'request_handler',\n}\n"], "names": ["ExpressLayerType"], "mappings": ";;;AAAA;;;;;;;;;;;;;;CAcA,GACAA,QAAAA,gBAAAA,GAAAA,KAAAA,EAAA;AAAA,CAAA,SAAA,gBAAA,EAA6B;IAC3B,MAAA,MAAA,GAAS,QAAA,CAAA;IAAA,gBAAA,CAAA,QAAA,CAAA,GAAA,MAAA;IACT,MAAA,UAAA,GAAa,YAAA,CAAA;IAAA,gBAAA,CAAA,YAAA,CAAA,GAAA,UAAA;IACb,MAAA,eAAA,GAAkB,iBAAA,CAAA;IAAA,gBAAA,CAAA,iBAAA,CAAA,GAAA,eAAA;AACpB,CAAA,EAAAA,QAAAA,gBAAA,IAAA,CAAAA,QAAAA,gBAAA,GAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 442, "column": 0}, "map": {"version": 3, "file": "internal-types.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/integrations/tracing/express-v5/internal-types.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\n/* eslint-disable @typescript-eslint/ban-types */\n\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { Request } from 'express';\n\n/**\n * This symbol is used to mark express layer as being already instrumented\n * since its possible to use a given layer multiple times (ex: middlewares)\n */\nexport const kLayerPatched: unique symbol = Symbol('express-layer-patched');\n\n/**\n * This const define where on the `request` object the Instrumentation will mount the\n * current stack of express layer.\n *\n * It is necessary because express doesn't store the different layers\n * (ie: middleware, router etc) that it called to get to the current layer.\n * Given that, the only way to know the route of a given layer is to\n * store the path of where each previous layer has been mounted.\n *\n * ex: bodyParser > auth middleware > /users router > get /:id\n *  in this case the stack would be: [\"/users\", \"/:id\"]\n *\n * ex2: bodyParser > /api router > /v1 router > /users router > get /:id\n *  stack: [\"/api\", \"/v1\", \"/users\", \":id\"]\n *\n */\nexport const _LAYERS_STORE_PROPERTY = '__ot_middlewares';\n\nexport type PatchedRequest = {\n  [_LAYERS_STORE_PROPERTY]?: string[];\n} & Request;\nexport type PathParams = string | RegExp | Array<string | RegExp>;\n\n// https://github.com/expressjs/express/blob/main/lib/router/index.js#L53\nexport type ExpressRouter = {\n  stack: ExpressLayer[];\n};\n\n// https://github.com/expressjs/express/blob/main/lib/router/layer.js#L33\nexport type ExpressLayer = {\n  handle: Function & Record<string, any>;\n  [kLayerPatched]?: boolean;\n  name: string;\n  path: string;\n  route?: ExpressLayer;\n};\n"], "names": [], "mappings": ";;;AAqBA;;;CAGA,SACa,aAAa,GAAkB,MAAM,CAAC,uBAAuB;AAE1E;;;;;;;;;;;;;;;CAeA,GACO,MAAM,sBAAA,GAAyB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 472, "column": 0}, "map": {"version": 3, "file": "utils.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/integrations/tracing/express-v5/utils.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-non-null-assertion */\n/* eslint-disable @typescript-eslint/explicit-function-return-type */\n/* eslint-disable @typescript-eslint/no-unsafe-member-access */\n\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { Attributes } from '@opentelemetry/api';\nimport { AttributeNames } from './enums/AttributeNames';\nimport { ExpressLayerType } from './enums/ExpressLayerType';\nimport type { ExpressLayer, PatchedRequest } from './internal-types';\nimport { _LAYERS_STORE_PROPERTY } from './internal-types';\nimport type { ExpressInstrumentationConfig, IgnoreMatcher, LayerPathSegment } from './types';\n\n/**\n * Store layers path in the request to be able to construct route later\n * @param request The request where\n * @param [value] the value to push into the array\n */\nexport const storeLayerPath = (request: PatchedRequest, value?: string): void => {\n  if (Array.isArray(request[_LAYERS_STORE_PROPERTY]) === false) {\n    Object.defineProperty(request, _LAYERS_STORE_PROPERTY, {\n      enumerable: false,\n      value: [],\n    });\n  }\n  if (value === undefined) return;\n  (request[_LAYERS_STORE_PROPERTY] as string[]).push(value);\n};\n\n/**\n * Recursively search the router path from layer stack\n * @param path The path to reconstruct\n * @param layer The layer to reconstruct from\n * @returns The reconstructed path\n */\nexport const getRouterPath = (path: string, layer: ExpressLayer): string => {\n  const stackLayer = layer.handle?.stack?.[0];\n\n  if (stackLayer?.route?.path) {\n    return `${path}${stackLayer.route.path}`;\n  }\n\n  if (stackLayer?.handle?.stack) {\n    return getRouterPath(path, stackLayer);\n  }\n\n  return path;\n};\n\n/**\n * Parse express layer context to retrieve a name and attributes.\n * @param route The route of the layer\n * @param layer Express layer\n * @param [layerPath] if present, the path on which the layer has been mounted\n */\nexport const getLayerMetadata = (\n  route: string,\n  layer: ExpressLayer,\n  layerPath?: string,\n): {\n  attributes: Attributes;\n  name: string;\n} => {\n  if (layer.name === 'router') {\n    const maybeRouterPath = getRouterPath('', layer);\n    const extractedRouterPath = maybeRouterPath ? maybeRouterPath : layerPath || route || '/';\n\n    return {\n      attributes: {\n        [AttributeNames.EXPRESS_NAME]: extractedRouterPath,\n        [AttributeNames.EXPRESS_TYPE]: ExpressLayerType.ROUTER,\n      },\n      name: `router - ${extractedRouterPath}`,\n    };\n  } else if (layer.name === 'bound dispatch' || layer.name === 'handle') {\n    return {\n      attributes: {\n        [AttributeNames.EXPRESS_NAME]: (route || layerPath) ?? 'request handler',\n        [AttributeNames.EXPRESS_TYPE]: ExpressLayerType.REQUEST_HANDLER,\n      },\n      name: `request handler${layer.path ? ` - ${route || layerPath}` : ''}`,\n    };\n  } else {\n    return {\n      attributes: {\n        [AttributeNames.EXPRESS_NAME]: layer.name,\n        [AttributeNames.EXPRESS_TYPE]: ExpressLayerType.MIDDLEWARE,\n      },\n      name: `middleware - ${layer.name}`,\n    };\n  }\n};\n\n/**\n * Check whether the given obj match pattern\n * @param constant e.g URL of request\n * @param obj obj to inspect\n * @param pattern Match pattern\n */\nconst satisfiesPattern = (constant: string, pattern: IgnoreMatcher): boolean => {\n  if (typeof pattern === 'string') {\n    return pattern === constant;\n  } else if (pattern instanceof RegExp) {\n    return pattern.test(constant);\n  } else if (typeof pattern === 'function') {\n    return pattern(constant);\n  } else {\n    throw new TypeError('Pattern is in unsupported datatype');\n  }\n};\n\n/**\n * Check whether the given request is ignored by configuration\n * It will not re-throw exceptions from `list` provided by the client\n * @param constant e.g URL of request\n * @param [list] List of ignore patterns\n * @param [onException] callback for doing something when an exception has\n *     occurred\n */\nexport const isLayerIgnored = (\n  name: string,\n  type: ExpressLayerType,\n  config?: ExpressInstrumentationConfig,\n): boolean => {\n  if (Array.isArray(config?.ignoreLayersType) && config?.ignoreLayersType?.includes(type)) {\n    return true;\n  }\n  if (Array.isArray(config?.ignoreLayers) === false) return false;\n  try {\n    for (const pattern of config!.ignoreLayers!) {\n      if (satisfiesPattern(name, pattern)) {\n        return true;\n      }\n    }\n  } catch {\n    /* catch block */\n  }\n\n  return false;\n};\n\n/**\n * Converts a user-provided error value into an error and error message pair\n *\n * @param error - User-provided error value\n * @returns Both an Error or string representation of the value and an error message\n */\nexport const asErrorAndMessage = (error: unknown): [error: string | Error, message: string] =>\n  error instanceof Error ? [error, error.message] : [String(error), String(error)];\n\n/**\n * Extracts the layer path from the route arguments\n *\n * @param args - Arguments of the route\n * @returns The layer path\n */\nexport const getLayerPath = (args: [LayerPathSegment | LayerPathSegment[], ...unknown[]]): string | undefined => {\n  const firstArg = args[0];\n\n  if (Array.isArray(firstArg)) {\n    return firstArg.map(arg => extractLayerPathSegment(arg) || '').join(',');\n  }\n\n  return extractLayerPathSegment(firstArg);\n};\n\nconst extractLayerPathSegment = (arg: LayerPathSegment) => {\n  if (typeof arg === 'string') {\n    return arg;\n  }\n\n  if (arg instanceof RegExp || typeof arg === 'number') {\n    return arg.toString();\n  }\n\n  return;\n};\n"], "names": ["_LAYERS_STORE_PROPERTY", "AttributeNames", "ExpressLayerType"], "mappings": ";;;;;;AA2BA;;;;CAIA,SACa,cAAA,GAAiB,CAAC,OAAO,EAAkB,KAAK,KAAoB;IAC/E,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAACA,cAAAA,sBAAsB,CAAC,CAAA,KAAM,KAAK,EAAE;QAC5D,MAAM,CAAC,cAAc,CAAC,OAAO,EAAEA,cAAAA,sBAAsB,EAAE;YACrD,UAAU,EAAE,KAAK;YACjB,KAAK,EAAE,EAAE;QACf,CAAK,CAAC;IACN;IACE,IAAI,KAAA,KAAU,SAAS,EAAE;IACxB,OAAO,CAACA,cAAAA,sBAAsB,CAAA,CAAe,IAAI,CAAC,KAAK,CAAC;AAC3D;AAEA;;;;;CAKA,SACa,aAAA,GAAgB,CAAC,IAAI,EAAU,KAAK,KAA2B;IAC1E,MAAM,UAAA,GAAa,KAAK,CAAC,MAAM,EAAE,KAAK,EAAA,CAAG,CAAC,CAAC;IAE3C,IAAI,UAAU,EAAE,KAAK,EAAE,IAAI,EAAE;QAC3B,OAAO,CAAC,EAAA,IAAA,CAAA,EAAA,UAAA,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA;IACA;IAEA,IAAA,UAAA,EAAA,MAAA,EAAA,KAAA,EAAA;QACA,OAAA,aAAA,CAAA,IAAA,EAAA,UAAA,CAAA;IACA;IAEA,OAAA,IAAA;AACA;AAEA;;;;;CAKA,GACA,MAAA,gBAAA,GAAA,CACA,KAAA,EACA,KAAA,EACA,SAAA;IAKA,IAAA,KAAA,CAAA,IAAA,KAAA,QAAA,EAAA;QACA,MAAA,eAAA,GAAA,aAAA,CAAA,EAAA,EAAA,KAAA,CAAA;QACA,MAAA,mBAAA,GAAA,eAAA,GAAA,eAAA,GAAA,SAAA,IAAA,KAAA,IAAA,GAAA;QAEA,OAAA;YACA,UAAA,EAAA;gBACA,CAAAC,eAAAA,cAAA,CAAA,YAAA,CAAA,EAAA,mBAAA;gBACA,CAAAA,eAAAA,cAAA,CAAA,YAAA,CAAA,EAAAC,iBAAAA,gBAAA,CAAA,MAAA;YACA,CAAA;YACA,IAAA,EAAA,CAAA,SAAA,EAAA,mBAAA,CAAA,CAAA;QACA,CAAA;IACA,CAAA,MAAA,IAAA,KAAA,CAAA,IAAA,KAAA,gBAAA,IAAA,KAAA,CAAA,IAAA,KAAA,QAAA,EAAA;QACA,OAAA;YACA,UAAA,EAAA;gBACA,CAAAD,eAAAA,cAAA,CAAA,YAAA,CAAA,EAAA,CAAA,KAAA,IAAA,SAAA,KAAA,iBAAA;gBACA,CAAAA,eAAAA,cAAA,CAAA,YAAA,CAAA,EAAAC,iBAAAA,gBAAA,CAAA,eAAA;YACA,CAAA;YACA,IAAA,EAAA,CAAA,eAAA,EAAA,KAAA,CAAA,IAAA,GAAA,CAAA,GAAA,EAAA,KAAA,IAAA,SAAA,CAAA,CAAA,GAAA,EAAA,CAAA,CAAA;QACA,CAAA;IACA,CAAA,MAAA;QACA,OAAA;YACA,UAAA,EAAA;gBACA,CAAAD,eAAAA,cAAA,CAAA,YAAA,CAAA,EAAA,KAAA,CAAA,IAAA;gBACA,CAAAA,eAAAA,cAAA,CAAA,YAAA,CAAA,EAAAC,iBAAAA,gBAAA,CAAA,UAAA;YACA,CAAA;YACA,IAAA,EAAA,CAAA,aAAA,EAAA,KAAA,CAAA,IAAA,CAAA,CAAA;QACA,CAAA;IACA;AACA;AAEA;;;;;CAKA,GACA,MAAA,gBAAA,GAAA,CAAA,QAAA,EAAA,OAAA,KAAA;IACA,IAAA,OAAA,OAAA,KAAA,QAAA,EAAA;QACA,OAAA,OAAA,KAAA,QAAA;IACA,CAAA,MAAA,IAAA,OAAA,YAAA,MAAA,EAAA;QACA,OAAA,OAAA,CAAA,IAAA,CAAA,QAAA,CAAA;IACA,CAAA,MAAA,IAAA,OAAA,OAAA,KAAA,UAAA,EAAA;QACA,OAAA,OAAA,CAAA,QAAA,CAAA;IACA,CAAA,MAAA;QACA,MAAA,IAAA,SAAA,CAAA,oCAAA,CAAA;IACA;AACA,CAAA;AAEA;;;;;;;CAOA,GACA,MAAA,cAAA,GAAA,CACA,IAAA,EACA,IAAA,EACA,MAAA;IAEA,IAAA,KAAA,CAAA,OAAA,CAAA,MAAA,EAAA,gBAAA,CAAA,IAAA,MAAA,EAAA,gBAAA,EAAA,QAAA,CAAA,IAAA,CAAA,EAAA;QACA,OAAA,IAAA;IACA;IACA,IAAA,KAAA,CAAA,OAAA,CAAA,MAAA,EAAA,YAAA,CAAA,KAAA,KAAA,EAAA,OAAA,KAAA;IACA,IAAA;QACA,KAAA,MAAA,OAAA,IAAA,MAAA,CAAA,YAAA,CAAA;YACA,IAAA,gBAAA,CAAA,IAAA,EAAA,OAAA,CAAA,EAAA;gBACA,OAAA,IAAA;YACA;QACA;IACA,CAAA,CAAA,OAAA;IACA,eAAA,GACA;IAEA,OAAA,KAAA;AACA;AAEA;;;;;CAKA,GACA,MAAA,iBAAA,GAAA,CAAA,KAAA,GACA,KAAA,YAAA,KAAA,GAAA;QAAA,KAAA;QAAA,KAAA,CAAA,OAAA;KAAA,GAAA;QAAA,MAAA,CAAA,KAAA,CAAA;QAAA,MAAA,CAAA,KAAA,CAAA;KAAA;AAEA;;;;;CAKA,GACA,MAAA,YAAA,GAAA,CAAA,IAAA,KAAA;IACA,MAAA,QAAA,GAAA,IAAA,CAAA,CAAA,CAAA;IAEA,IAAA,KAAA,CAAA,OAAA,CAAA,QAAA,CAAA,EAAA;QACA,OAAA,QAAA,CAAA,GAAA,EAAA,GAAA,GAAA,uBAAA,CAAA,GAAA,CAAA,IAAA,EAAA,CAAA,CAAA,IAAA,CAAA,GAAA,CAAA;IACA;IAEA,OAAA,uBAAA,CAAA,QAAA,CAAA;AACA;AAEA,MAAA,uBAAA,GAAA,CAAA,GAAA,KAAA;IACA,IAAA,OAAA,GAAA,KAAA,QAAA,EAAA;QACA,OAAA,GAAA;IACA;IAEA,IAAA,GAAA,YAAA,MAAA,IAAA,OAAA,GAAA,KAAA,QAAA,EAAA;QACA,OAAA,GAAA,CAAA,QAAA,EAAA;IACA;IAEA;AACA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 623, "column": 0}, "map": {"version": 3, "file": "instrumentation.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/integrations/tracing/express-v5/instrumentation.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\n/* eslint-disable @typescript-eslint/no-unsafe-member-access */\n/* eslint-disable @typescript-eslint/member-ordering */\n/* eslint-disable guard-for-in */\n/* eslint-disable @typescript-eslint/ban-types */\n/* eslint-disable prefer-rest-params */\n/* eslint-disable @typescript-eslint/no-this-alias */\n/* eslint-disable jsdoc/require-jsdoc */\n/* eslint-disable @typescript-eslint/explicit-function-return-type */\n/* eslint-disable @typescript-eslint/explicit-member-accessibility */\n\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { Attributes } from '@opentelemetry/api';\nimport { context, diag, SpanStatusCode, trace } from '@opentelemetry/api';\nimport { getRPCMetadata, RPCType } from '@opentelemetry/core';\nimport {\n  InstrumentationBase,\n  InstrumentationNodeModuleDefinition,\n  isWrapped,\n  safeExecuteInTheMiddle,\n} from '@opentelemetry/instrumentation';\nimport { SEMATTRS_HTTP_ROUTE } from '@opentelemetry/semantic-conventions';\nimport type * as express from 'express';\nimport { AttributeNames } from './enums/AttributeNames';\nimport { ExpressLayerType } from './enums/ExpressLayerType';\nimport type { ExpressLayer, ExpressRouter, PatchedRequest } from './internal-types';\nimport { _LAYERS_STORE_PROPERTY, kLayerPatched } from './internal-types';\nimport type { ExpressInstrumentationConfig, ExpressRequestInfo } from './types';\nimport { asErrorAndMessage, getLayerMetadata, getLayerPath, isLayerIgnored, storeLayerPath } from './utils';\n\nexport const PACKAGE_VERSION = '0.1.0';\nexport const PACKAGE_NAME = '@sentry/instrumentation-express-v5';\n\n/** Express instrumentation for OpenTelemetry */\nexport class ExpressInstrumentationV5 extends InstrumentationBase<ExpressInstrumentationConfig> {\n  constructor(config: ExpressInstrumentationConfig = {}) {\n    super(PACKAGE_NAME, PACKAGE_VERSION, config);\n  }\n\n  init() {\n    return [\n      new InstrumentationNodeModuleDefinition(\n        'express',\n        ['>=5.0.0'],\n        moduleExports => this._setup(moduleExports),\n        moduleExports => this._tearDown(moduleExports),\n      ),\n    ];\n  }\n\n  private _setup(moduleExports: any) {\n    const routerProto = moduleExports.Router.prototype;\n    // patch express.Router.route\n    if (isWrapped(routerProto.route)) {\n      this._unwrap(routerProto, 'route');\n    }\n    this._wrap(routerProto, 'route', this._getRoutePatch());\n    // patch express.Router.use\n    if (isWrapped(routerProto.use)) {\n      this._unwrap(routerProto, 'use');\n    }\n    this._wrap(routerProto, 'use', this._getRouterUsePatch() as any);\n    // patch express.Application.use\n    if (isWrapped(moduleExports.application.use)) {\n      this._unwrap(moduleExports.application, 'use');\n    }\n    this._wrap(moduleExports.application, 'use', this._getAppUsePatch() as any);\n    return moduleExports;\n  }\n\n  private _tearDown(moduleExports: any) {\n    if (moduleExports === undefined) return;\n    const routerProto = moduleExports.Router.prototype;\n    this._unwrap(routerProto, 'route');\n    this._unwrap(routerProto, 'use');\n    this._unwrap(moduleExports.application, 'use');\n  }\n\n  /**\n   * Get the patch for Router.route function\n   */\n  private _getRoutePatch() {\n    const instrumentation = this;\n    return function (original: express.Router['route']) {\n      return function route_trace(this: ExpressRouter, ...args: Parameters<typeof original>) {\n        const route = original.apply(this, args);\n        const layer = this.stack[this.stack.length - 1] as ExpressLayer;\n        instrumentation._applyPatch(layer, getLayerPath(args));\n        return route;\n      };\n    };\n  }\n\n  /**\n   * Get the patch for Router.use function\n   */\n  private _getRouterUsePatch() {\n    const instrumentation = this;\n    return function (original: express.Router['use']) {\n      return function use(this: express.Application, ...args: Parameters<typeof original>) {\n        const route = original.apply(this, args);\n        const layer = this.stack[this.stack.length - 1] as ExpressLayer;\n        instrumentation._applyPatch(layer, getLayerPath(args));\n        return route;\n      };\n    };\n  }\n\n  /**\n   * Get the patch for Application.use function\n   */\n  private _getAppUsePatch() {\n    const instrumentation = this;\n    return function (original: express.Application['use']) {\n      return function use(\n        // In express 5.x the router is stored in `router` whereas in 4.x it's stored in `_router`\n        this: { _router?: ExpressRouter; router?: ExpressRouter },\n        ...args: Parameters<typeof original>\n      ) {\n        // if we access app.router in express 4.x we trigger an assertion error\n        // This property existed in v3, was removed in v4 and then re-added in v5\n        const router = this.router;\n        const route = original.apply(this, args);\n        if (router) {\n          const layer = router.stack[router.stack.length - 1] as ExpressLayer;\n          instrumentation._applyPatch(layer, getLayerPath(args));\n        }\n        return route;\n      };\n    };\n  }\n\n  /** Patch each express layer to create span and propagate context */\n  private _applyPatch(this: ExpressInstrumentationV5, layer: ExpressLayer, layerPath?: string) {\n    const instrumentation = this;\n    // avoid patching multiple times the same layer\n    if (layer[kLayerPatched] === true) return;\n    layer[kLayerPatched] = true;\n\n    this._wrap(layer, 'handle', original => {\n      // TODO: instrument error handlers\n      if (original.length === 4) return original;\n\n      const patched = function (this: ExpressLayer, req: PatchedRequest, res: express.Response) {\n        storeLayerPath(req, layerPath);\n        const route = (req[_LAYERS_STORE_PROPERTY] as string[])\n          .filter(path => path !== '/' && path !== '/*')\n          .join('')\n          // remove duplicate slashes to normalize route\n          .replace(/\\/{2,}/g, '/');\n\n        const actualRoute = route.length > 0 ? route : undefined;\n\n        const attributes: Attributes = {\n          // eslint-disable-next-line deprecation/deprecation\n          [SEMATTRS_HTTP_ROUTE]: actualRoute,\n        };\n        const metadata = getLayerMetadata(route, layer, layerPath);\n        const type = metadata.attributes[AttributeNames.EXPRESS_TYPE] as ExpressLayerType;\n\n        const rpcMetadata = getRPCMetadata(context.active());\n        if (rpcMetadata?.type === RPCType.HTTP) {\n          rpcMetadata.route = actualRoute;\n        }\n\n        // verify against the config if the layer should be ignored\n        if (isLayerIgnored(metadata.name, type, instrumentation.getConfig())) {\n          if (type === ExpressLayerType.MIDDLEWARE) {\n            (req[_LAYERS_STORE_PROPERTY] as string[]).pop();\n          }\n          return original.apply(this, arguments);\n        }\n\n        if (trace.getSpan(context.active()) === undefined) {\n          return original.apply(this, arguments);\n        }\n\n        const spanName = instrumentation._getSpanName(\n          {\n            request: req,\n            layerType: type,\n            route,\n          },\n          metadata.name,\n        );\n        const span = instrumentation.tracer.startSpan(spanName, {\n          attributes: Object.assign(attributes, metadata.attributes),\n        });\n\n        const { requestHook } = instrumentation.getConfig();\n        if (requestHook) {\n          safeExecuteInTheMiddle(\n            () =>\n              requestHook(span, {\n                request: req,\n                layerType: type,\n                route,\n              }),\n            e => {\n              if (e) {\n                diag.error('express instrumentation: request hook failed', e);\n              }\n            },\n            true,\n          );\n        }\n\n        let spanHasEnded = false;\n        if (metadata.attributes[AttributeNames.EXPRESS_TYPE] !== ExpressLayerType.MIDDLEWARE) {\n          span.end();\n          spanHasEnded = true;\n        }\n        // listener for response.on('finish')\n        const onResponseFinish = () => {\n          if (spanHasEnded === false) {\n            spanHasEnded = true;\n            span.end();\n          }\n        };\n\n        // verify we have a callback\n        const args = Array.from(arguments);\n        const callbackIdx = args.findIndex(arg => typeof arg === 'function');\n        if (callbackIdx >= 0) {\n          arguments[callbackIdx] = function () {\n            // express considers anything but an empty value, \"route\" or \"router\"\n            // passed to its callback to be an error\n            const maybeError = arguments[0];\n            const isError = ![undefined, null, 'route', 'router'].includes(maybeError);\n            if (!spanHasEnded && isError) {\n              const [error, message] = asErrorAndMessage(maybeError);\n              span.recordException(error);\n              span.setStatus({\n                code: SpanStatusCode.ERROR,\n                message,\n              });\n            }\n\n            if (spanHasEnded === false) {\n              spanHasEnded = true;\n              req.res?.removeListener('finish', onResponseFinish);\n              span.end();\n            }\n            if (!(req.route && isError)) {\n              (req[_LAYERS_STORE_PROPERTY] as string[]).pop();\n            }\n            const callback = args[callbackIdx] as Function;\n            return callback.apply(this, arguments);\n          };\n        }\n\n        try {\n          return original.apply(this, arguments);\n        } catch (anyError) {\n          const [error, message] = asErrorAndMessage(anyError);\n          span.recordException(error);\n          span.setStatus({\n            code: SpanStatusCode.ERROR,\n            message,\n          });\n          throw anyError;\n        } finally {\n          /**\n           * At this point if the callback wasn't called, that means either the\n           * layer is asynchronous (so it will call the callback later on) or that\n           * the layer directly end the http response, so we'll hook into the \"finish\"\n           * event to handle the later case.\n           */\n          if (!spanHasEnded) {\n            res.once('finish', onResponseFinish);\n          }\n        }\n      };\n\n      // `handle` isn't just a regular function in some cases. It also contains\n      // some properties holding metadata and state so we need to proxy them\n      // through through patched function\n      // ref: https://github.com/open-telemetry/opentelemetry-js-contrib/issues/1950\n      // Also some apps/libs do their own patching before OTEL and have these properties\n      // in the proptotype. So we use a `for...in` loop to get own properties and also\n      // any enumerable prop in the prototype chain\n      // ref: https://github.com/open-telemetry/opentelemetry-js-contrib/issues/2271\n      for (const key in original) {\n        Object.defineProperty(patched, key, {\n          get() {\n            return original[key];\n          },\n          set(value) {\n            original[key] = value;\n          },\n        });\n      }\n      return patched;\n    });\n  }\n\n  _getSpanName(info: ExpressRequestInfo, defaultName: string) {\n    const { spanNameHook } = this.getConfig();\n\n    if (!(spanNameHook instanceof Function)) {\n      return defaultName;\n    }\n\n    try {\n      return spanNameHook(info, defaultName) ?? defaultName;\n    } catch (err) {\n      diag.error('express instrumentation: error calling span name rewrite hook', err);\n      return defaultName;\n    }\n  }\n}\n"], "names": ["InstrumentationBase", "InstrumentationNodeModuleDefinition", "isWrapped", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "instrumentation", "kLayerPatched", "storeLayerPath", "_LAYERS_STORE_PROPERTY", "SEMATTRS_HTTP_ROUTE", "getLayerMetadata", "AttributeNames", "getRPCMetadata", "context", "RPCType", "isLayerIgnored", "ExpressLayerType", "trace", "safeExecuteInTheMiddle", "diag", "asErrorAndMessage", "SpanStatusCode"], "mappings": ";;;;;;;;;;;AA6CO,MAAM,eAAA,GAAkB;AACxB,MAAM,YAAA,GAAe;AAE5B,8CAAA,GACO,MAAM,wBAAA,SAAiCA,gBAAAA,mBAAmB,CAA+B;IAC9F,WAAW,CAAC,MAAM,GAAiC,CAAA,CAAE,CAAE;QACrD,KAAK,CAAC,YAAY,EAAE,eAAe,EAAE,MAAM,CAAC;IAChD;IAEE,IAAI,GAAG;QACL,OAAO;YACL,IAAIC,gBAAAA,mCAAmC,CACrC,SAAS,EACT;gBAAC,SAAS;aAAC,GACX,gBAAiB,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,GAC3C,gBAAiB,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC;SAEjD;IACL;IAEU,MAAM,CAAC,aAAa,EAAO;QACjC,MAAM,WAAA,GAAc,aAAa,CAAC,MAAM,CAAC,SAAS;QACtD,6BAAA;QACI,IAAIC,gBAAAA,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE;YAChC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC;QACxC;QACI,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,OAAO,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC;QAC3D,2BAAA;QACI,IAAIA,gBAAAA,SAAS,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE;YAC9B,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,CAAC;QACtC;QACI,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,EAAE,IAAI,CAAC,kBAAkB,IAAU;QACpE,gCAAA;QACI,IAAIA,gBAAAA,SAAS,CAAC,aAAa,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE;YAC5C,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,WAAW,EAAE,KAAK,CAAC;QACpD;QACI,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,WAAW,EAAE,KAAK,EAAE,IAAI,CAAC,eAAe,IAAU;QAC3E,OAAO,aAAa;IACxB;IAEU,SAAS,CAAC,aAAa,EAAO;QACpC,IAAI,aAAA,KAAkB,SAAS,EAAE;QACjC,MAAM,WAAA,GAAc,aAAa,CAAC,MAAM,CAAC,SAAS;QAClD,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC;QAClC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,CAAC;QAChC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,WAAW,EAAE,KAAK,CAAC;IAClD;IAEA;;GAEA,GACU,cAAc,GAAG;QACvB,MAAM,eAAA,GAAkB,IAAI;QAC5B,OAAO,SAAU,QAAQ,EAA2B;YAClD,OAAO,SAAS,WAAW,CAAsB,GAAG,IAAI,EAA+B;gBACrF,MAAM,KAAA,GAAQ,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC;gBACxC,MAAM,KAAA,GAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAA,GAAS,CAAC,CAAA;gBAC9C,eAAe,CAAC,WAAW,CAAC,KAAK,EAAEC,MAAAA,YAAY,CAAC,IAAI,CAAC,CAAC;gBACtD,OAAO,KAAK;YACpB,CAAO;QACP,CAAK;IACL;IAEA;;GAEA,GACU,kBAAkB,GAAG;QAC3B,MAAM,eAAA,GAAkB,IAAI;QAC5B,OAAO,SAAU,QAAQ,EAAyB;YAChD,OAAO,SAAS,GAAG,CAA4B,GAAG,IAAI,EAA+B;gBACnF,MAAM,KAAA,GAAQ,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC;gBACxC,MAAM,KAAA,GAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAA,GAAS,CAAC,CAAA;gBAC9C,eAAe,CAAC,WAAW,CAAC,KAAK,EAAEA,MAAAA,YAAY,CAAC,IAAI,CAAC,CAAC;gBACtD,OAAO,KAAK;YACpB,CAAO;QACP,CAAK;IACL;IAEA;;GAEA,GACU,eAAe,GAAG;QACxB,MAAM,eAAA,GAAkB,IAAI;QAC5B,OAAO,SAAU,QAAQ,EAA8B;YACrD,OAAO,SAAS,GAAG,CACzB,0FAAA;YAEQ,GAAG,IAAA;gBAEX,uEAAA;gBACA,yEAAA;gBACQ,MAAM,MAAA,GAAS,IAAI,CAAC,MAAM;gBAC1B,MAAM,KAAA,GAAQ,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC;gBACxC,IAAI,MAAM,EAAE;oBACV,MAAM,KAAA,GAAQ,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,MAAA,GAAS,CAAC,CAAA;oBAClD,eAAe,CAAC,WAAW,CAAC,KAAK,EAAEA,MAAAA,YAAY,CAAC,IAAI,CAAC,CAAC;gBAChE;gBACQ,OAAO,KAAK;YACpB,CAAO;QACP,CAAK;IACL;IAEA,kEAAA,GACU,WAAW,CAAiC,KAAK,EAAgB,SAAS,EAAW;QAC3F,MAAMC,iBAAA,GAAkB,IAAI;QAChC,+CAAA;QACI,IAAI,KAAK,CAACC,cAAAA,aAAa,CAAA,KAAM,IAAI,EAAE;QACnC,KAAK,CAACA,cAAAA,aAAa,CAAA,GAAI,IAAI;QAE3B,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,QAAQ,GAAE,QAAA,IAAY;YAC5C,kCAAA;YACM,IAAI,QAAQ,CAAC,MAAA,KAAW,CAAC,EAAE,OAAO,QAAQ;YAE1C,MAAM,UAAU,SAA8B,GAAG,EAAkB,GAAG,EAAoB;gBACxFC,MAAAA,cAAc,CAAC,GAAG,EAAE,SAAS,CAAC;gBAC9B,MAAM,KAAA,GAAQ,AAAC,GAAG,CAACC,cAAAA,sBAAsB,CAAA,CACtC,MAAM,EAAC,IAAA,GAAQ,IAAA,KAAS,GAAA,IAAO,IAAA,KAAS,IAAI,EAC5C,IAAI,CAAC,EAAE,CAClB,8CAAA;iBACW,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;gBAE1B,MAAM,WAAA,GAAc,KAAK,CAAC,MAAA,GAAS,CAAA,GAAI,KAAA,GAAQ,SAAS;gBAExD,MAAM,UAAU,GAAe;oBACvC,mDAAA;oBACU,CAACC,oBAAAA,mBAAmB,CAAA,EAAG,WAAW;gBAC5C,CAAS;gBACD,MAAM,QAAA,GAAWC,MAAAA,gBAAgB,CAAC,KAAK,EAAE,KAAK,EAAE,SAAS,CAAC;gBAC1D,MAAM,IAAA,GAAO,QAAQ,CAAC,UAAU,CAACC,eAAAA,cAAc,CAAC,YAAY,CAAA;gBAE5D,MAAM,WAAA,GAAcC,KAAAA,cAAc,CAACC,IAAAA,OAAO,CAAC,MAAM,EAAE,CAAC;gBACpD,IAAI,WAAW,EAAE,SAASC,KAAAA,OAAO,CAAC,IAAI,EAAE;oBACtC,WAAW,CAAC,KAAA,GAAQ,WAAW;gBACzC;gBAEA,2DAAA;gBACQ,IAAIC,MAAAA,cAAc,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,EAAEV,iBAAe,CAAC,SAAS,EAAE,CAAC,EAAE;oBACpE,IAAI,IAAA,KAASW,iBAAAA,gBAAgB,CAAC,UAAU,EAAE;wBACvC,GAAG,CAACR,cAAAA,sBAAsB,CAAA,CAAe,GAAG,EAAE;oBAC3D;oBACU,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC;gBAChD;gBAEQ,IAAIS,IAAAA,KAAK,CAAC,OAAO,CAACJ,IAAAA,OAAO,CAAC,MAAM,EAAE,CAAA,KAAM,SAAS,EAAE;oBACjD,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC;gBAChD;gBAEQ,MAAM,QAAA,GAAWR,iBAAe,CAAC,YAAY,CAC3C;oBACE,OAAO,EAAE,GAAG;oBACZ,SAAS,EAAE,IAAI;oBACf,KAAK;gBACjB,CAAW,EACD,QAAQ,CAAC,IAAI;gBAEf,MAAM,IAAA,GAAOA,iBAAe,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE;oBACtD,UAAU,EAAE,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,CAAC,UAAU,CAAC;gBACpE,CAAS,CAAC;gBAEF,MAAM,EAAE,WAAA,EAAY,GAAIA,iBAAe,CAAC,SAAS,EAAE;gBACnD,IAAI,WAAW,EAAE;oBACfa,gBAAAA,sBAAsB,CACpB,IACE,WAAW,CAAC,IAAI,EAAE;4BAChB,OAAO,EAAE,GAAG;4BACZ,SAAS,EAAE,IAAI;4BACf,KAAK;wBACrB,CAAe,CAAC,GACJ,KAAK;wBACH,IAAI,CAAC,EAAE;4BACLC,IAAAA,IAAI,CAAC,KAAK,CAAC,8CAA8C,EAAE,CAAC,CAAC;wBAC7E;oBACA,CAAa,EACD,IAAI;gBAEhB;gBAEQ,IAAI,YAAA,GAAe,KAAK;gBACxB,IAAI,QAAQ,CAAC,UAAU,CAACR,eAAAA,cAAc,CAAC,YAAY,CAAA,KAAMK,iBAAAA,gBAAgB,CAAC,UAAU,EAAE;oBACpF,IAAI,CAAC,GAAG,EAAE;oBACV,YAAA,GAAe,IAAI;gBAC7B;gBACA,qCAAA;gBACQ,MAAM,gBAAA,GAAmB,MAAM;oBAC7B,IAAI,YAAA,KAAiB,KAAK,EAAE;wBAC1B,YAAA,GAAe,IAAI;wBACnB,IAAI,CAAC,GAAG,EAAE;oBACtB;gBACA,CAAS;gBAET,4BAAA;gBACQ,MAAM,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC;gBAClC,MAAM,WAAA,GAAc,IAAI,CAAC,SAAS,EAAC,GAAA,GAAO,OAAO,GAAA,KAAQ,UAAU,CAAC;gBACpE,IAAI,WAAA,IAAe,CAAC,EAAE;oBACpB,SAAS,CAAC,WAAW,CAAA,GAAI,YAAY;wBAC/C,qEAAA;wBACA,wCAAA;wBACY,MAAM,UAAA,GAAa,SAAS,CAAC,CAAC,CAAC;wBAC/B,MAAM,OAAA,GAAU,CAAC;4BAAC,SAAS;4BAAE,IAAI;4BAAE,OAAO;4BAAE,QAAQ;yBAAC,CAAC,QAAQ,CAAC,UAAU,CAAC;wBAC1E,IAAI,CAAC,YAAA,IAAgB,OAAO,EAAE;4BAC5B,MAAM,CAAC,KAAK,EAAE,OAAO,CAAA,GAAII,MAAAA,iBAAiB,CAAC,UAAU,CAAC;4BACtD,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC;4BAC3B,IAAI,CAAC,SAAS,CAAC;gCACb,IAAI,EAAEC,IAAAA,cAAc,CAAC,KAAK;gCAC1B,OAAO;4BACvB,CAAe,CAAC;wBAChB;wBAEY,IAAI,YAAA,KAAiB,KAAK,EAAE;4BAC1B,YAAA,GAAe,IAAI;4BACnB,GAAG,CAAC,GAAG,EAAE,cAAc,CAAC,QAAQ,EAAE,gBAAgB,CAAC;4BACnD,IAAI,CAAC,GAAG,EAAE;wBACxB;wBACY,IAAI,CAAA,CAAE,GAAG,CAAC,KAAA,IAAS,OAAO,CAAC,EAAE;4BAC1B,GAAG,CAACb,cAAAA,sBAAsB,CAAA,CAAe,GAAG,EAAE;wBAC7D;wBACY,MAAM,QAAA,GAAW,IAAI,CAAC,WAAW,CAAA;wBACjC,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC;oBAClD,CAAW;gBACX;gBAEQ,IAAI;oBACF,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC;gBAChD,CAAQ,CAAE,OAAO,QAAQ,EAAE;oBACjB,MAAM,CAAC,KAAK,EAAE,OAAO,CAAA,GAAIY,MAAAA,iBAAiB,CAAC,QAAQ,CAAC;oBACpD,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC;oBAC3B,IAAI,CAAC,SAAS,CAAC;wBACb,IAAI,EAAEC,IAAAA,cAAc,CAAC,KAAK;wBAC1B,OAAO;oBACnB,CAAW,CAAC;oBACF,MAAM,QAAQ;gBACxB,SAAkB;oBAClB;;;;;WAKA,GACU,IAAI,CAAC,YAAY,EAAE;wBACjB,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,gBAAgB,CAAC;oBAChD;gBACA;YACA,CAAO;YAEP,yEAAA;YACA,sEAAA;YACA,mCAAA;YACA,8EAAA;YACA,kFAAA;YACA,gFAAA;YACA,6CAAA;YACA,8EAAA;YACM,IAAK,MAAM,GAAA,IAAO,QAAQ,CAAE;gBAC1B,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,GAAG,EAAE;oBAClC,GAAG,GAAG;wBACJ,OAAO,QAAQ,CAAC,GAAG,CAAC;oBAChC,CAAW;oBACD,GAAG,EAAC,KAAK,EAAE;wBACT,QAAQ,CAAC,GAAG,CAAA,GAAI,KAAK;oBACjC,CAAW;gBACX,CAAS,CAAC;YACV;YACM,OAAO,OAAO;QACpB,CAAK,CAAC;IACN;IAEE,YAAY,CAAC,IAAI,EAAsB,WAAW,EAAU;QAC1D,MAAM,EAAE,YAAA,EAAa,GAAI,IAAI,CAAC,SAAS,EAAE;QAEzC,IAAI,CAAA,CAAE,wBAAwB,QAAQ,CAAC,EAAE;YACvC,OAAO,WAAW;QACxB;QAEI,IAAI;YACF,OAAO,YAAY,CAAC,IAAI,EAAE,WAAW,CAAA,IAAK,WAAW;QAC3D,CAAI,CAAE,OAAO,GAAG,EAAE;YACZF,IAAAA,IAAI,CAAC,KAAK,CAAC,+DAA+D,EAAE,GAAG,CAAC;YAChF,OAAO,WAAW;QACxB;IACA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 880, "column": 0}, "map": {"version": 3, "file": "express.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/integrations/tracing/express.ts"], "sourcesContent": ["import type * as http from 'node:http';\nimport type { Span } from '@opentelemetry/api';\nimport type { ExpressRequestInfo } from '@opentelemetry/instrumentation-express';\nimport { ExpressInstrumentation } from '@opentelemetry/instrumentation-express';\nimport type { IntegrationFn } from '@sentry/core';\nimport {\n  captureException,\n  debug,\n  defineIntegration,\n  getDefaultIsolationScope,\n  getIsolationScope,\n  httpRequestToRequestData,\n  SEMANTIC_ATTRIBUTE_SENTRY_OP,\n  spanToJSON,\n} from '@sentry/core';\nimport { addOriginToSpan, ensureIsWrapped, generateInstrumentOnce } from '@sentry/node-core';\nimport { DEBUG_BUILD } from '../../debug-build';\nimport { ExpressInstrumentationV5 } from './express-v5/instrumentation';\n\nconst INTEGRATION_NAME = 'Express';\nconst INTEGRATION_NAME_V5 = 'Express-V5';\n\nfunction requestHook(span: Span): void {\n  addOriginToSpan(span, 'auto.http.otel.express');\n\n  const attributes = spanToJSON(span).data;\n  // this is one of: middleware, request_handler, router\n  const type = attributes['express.type'];\n\n  if (type) {\n    span.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_OP, `${type}.express`);\n  }\n\n  // Also update the name, we don't need to \"middleware - \" prefix\n  const name = attributes['express.name'];\n  if (typeof name === 'string') {\n    span.updateName(name);\n  }\n}\n\nfunction spanNameHook(info: ExpressRequestInfo<unknown>, defaultName: string): string {\n  if (getIsolationScope() === getDefaultIsolationScope()) {\n    DEBUG_BUILD && debug.warn('Isolation scope is still default isolation scope - skipping setting transactionName');\n    return defaultName;\n  }\n  if (info.layerType === 'request_handler') {\n    // type cast b/c Otel unfortunately types info.request as any :(\n    const req = info.request as { method?: string };\n    const method = req.method ? req.method.toUpperCase() : 'GET';\n    getIsolationScope().setTransactionName(`${method} ${info.route}`);\n  }\n  return defaultName;\n}\n\nexport const instrumentExpress = generateInstrumentOnce(\n  INTEGRATION_NAME,\n  () =>\n    new ExpressInstrumentation({\n      requestHook: span => requestHook(span),\n      spanNameHook: (info, defaultName) => spanNameHook(info, defaultName),\n    }),\n);\n\nexport const instrumentExpressV5 = generateInstrumentOnce(\n  INTEGRATION_NAME_V5,\n  () =>\n    new ExpressInstrumentationV5({\n      requestHook: span => requestHook(span),\n      spanNameHook: (info, defaultName) => spanNameHook(info, defaultName),\n    }),\n);\n\nconst _expressIntegration = (() => {\n  return {\n    name: INTEGRATION_NAME,\n    setupOnce() {\n      instrumentExpress();\n      instrumentExpressV5();\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Adds Sentry tracing instrumentation for [Express](https://expressjs.com/).\n *\n * If you also want to capture errors, you need to call `setupExpressErrorHandler(app)` after you set up your Express server.\n *\n * For more information, see the [express documentation](https://docs.sentry.io/platforms/javascript/guides/express/).\n *\n * @example\n * ```javascript\n * const Sentry = require('@sentry/node');\n *\n * Sentry.init({\n *   integrations: [Sentry.expressIntegration()],\n * })\n * ```\n */\nexport const expressIntegration = defineIntegration(_expressIntegration);\n\ninterface MiddlewareError extends Error {\n  status?: number | string;\n  statusCode?: number | string;\n  status_code?: number | string;\n  output?: {\n    statusCode?: number | string;\n  };\n}\n\ntype ExpressMiddleware = (req: http.IncomingMessage, res: http.ServerResponse, next: () => void) => void;\n\ntype ExpressErrorMiddleware = (\n  error: MiddlewareError,\n  req: http.IncomingMessage,\n  res: http.ServerResponse,\n  next: (error: MiddlewareError) => void,\n) => void;\n\ninterface ExpressHandlerOptions {\n  /**\n   * Callback method deciding whether error should be captured and sent to Sentry\n   * @param error Captured middleware error\n   */\n  shouldHandleError?(this: void, error: MiddlewareError): boolean;\n}\n\n/**\n * An Express-compatible error handler.\n */\nexport function expressErrorHandler(options?: ExpressHandlerOptions): ExpressErrorMiddleware {\n  return function sentryErrorMiddleware(\n    error: MiddlewareError,\n    request: http.IncomingMessage,\n    res: http.ServerResponse,\n    next: (error: MiddlewareError) => void,\n  ): void {\n    const normalizedRequest = httpRequestToRequestData(request);\n    // Ensure we use the express-enhanced request here, instead of the plain HTTP one\n    // When an error happens, the `expressRequestHandler` middleware does not run, so we set it here too\n    getIsolationScope().setSDKProcessingMetadata({ normalizedRequest });\n\n    const shouldHandleError = options?.shouldHandleError || defaultShouldHandleError;\n\n    if (shouldHandleError(error)) {\n      const eventId = captureException(error, { mechanism: { type: 'middleware', handled: false } });\n      (res as { sentry?: string }).sentry = eventId;\n    }\n\n    next(error);\n  };\n}\n\nfunction expressRequestHandler(): ExpressMiddleware {\n  return function sentryRequestMiddleware(\n    request: http.IncomingMessage,\n    _res: http.ServerResponse,\n    next: () => void,\n  ): void {\n    const normalizedRequest = httpRequestToRequestData(request);\n    // Ensure we use the express-enhanced request here, instead of the plain HTTP one\n    getIsolationScope().setSDKProcessingMetadata({ normalizedRequest });\n\n    next();\n  };\n}\n\n/**\n * Add an Express error handler to capture errors to Sentry.\n *\n * The error handler must be before any other middleware and after all controllers.\n *\n * @param app The Express instances\n * @param options {ExpressHandlerOptions} Configuration options for the handler\n *\n * @example\n * ```javascript\n * const Sentry = require('@sentry/node');\n * const express = require(\"express\");\n *\n * const app = express();\n *\n * // Add your routes, etc.\n *\n * // Add this after all routes,\n * // but before any and other error-handling middlewares are defined\n * Sentry.setupExpressErrorHandler(app);\n *\n * app.listen(3000);\n * ```\n */\nexport function setupExpressErrorHandler(\n  app: { use: (middleware: ExpressMiddleware | ExpressErrorMiddleware) => unknown },\n  options?: ExpressHandlerOptions,\n): void {\n  app.use(expressRequestHandler());\n  app.use(expressErrorHandler(options));\n  ensureIsWrapped(app.use, 'express');\n}\n\nfunction getStatusCodeFromResponse(error: MiddlewareError): number {\n  const statusCode = error.status || error.statusCode || error.status_code || error.output?.statusCode;\n  return statusCode ? parseInt(statusCode as string, 10) : 500;\n}\n\n/** Returns true if response code is internal server error */\nfunction defaultShouldHandleError(error: MiddlewareError): boolean {\n  const status = getStatusCodeFromResponse(error);\n  return status >= 500;\n}\n"], "names": ["addOriginToSpan", "spanToJSON", "SEMANTIC_ATTRIBUTE_SENTRY_OP", "getIsolationScope", "getDefaultIsolationScope", "DEBUG_BUILD", "debug", "generateInstrumentOnce", "ExpressInstrumentation", "ExpressInstrumentationV5", "defineIntegration", "httpRequestToRequestData", "captureException", "ensureIsWrapped"], "mappings": ";;;;;;;;AAmBA,MAAM,gBAAA,GAAmB,SAAS;AAClC,MAAM,mBAAA,GAAsB,YAAY;AAExC,SAAS,WAAW,CAAC,IAAI,EAAc;IACrCA,SAAAA,eAAe,CAAC,IAAI,EAAE,wBAAwB,CAAC;IAE/C,MAAM,aAAaC,KAAAA,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI;IAC1C,sDAAA;IACE,MAAM,IAAA,GAAO,UAAU,CAAC,cAAc,CAAC;IAEvC,IAAI,IAAI,EAAE;QACR,IAAI,CAAC,YAAY,CAACC,KAAAA,4BAA4B,EAAE,CAAC,EAAA,IAAA,CAAA,QAAA,CAAA,CAAA;IACA;IAEA,gEAAA;IACA,MAAA,IAAA,GAAA,UAAA,CAAA,cAAA,CAAA;IACA,IAAA,OAAA,IAAA,KAAA,QAAA,EAAA;QACA,IAAA,CAAA,UAAA,CAAA,IAAA,CAAA;IACA;AACA;AAEA,SAAA,YAAA,CAAA,IAAA,EAAA,WAAA,EAAA;IACA,IAAAC,KAAAA,iBAAA,EAAA,KAAAC,KAAAA,wBAAA,EAAA,EAAA;QACAC,WAAAA,WAAA,IAAAC,KAAAA,KAAA,CAAA,IAAA,CAAA,qFAAA,CAAA;QACA,OAAA,WAAA;IACA;IACA,IAAA,IAAA,CAAA,SAAA,KAAA,iBAAA,EAAA;QACA,gEAAA;QACA,MAAA,GAAA,GAAA,IAAA,CAAA,OAAA;QACA,MAAA,MAAA,GAAA,GAAA,CAAA,MAAA,GAAA,GAAA,CAAA,MAAA,CAAA,WAAA,EAAA,GAAA,KAAA;QACAH,KAAAA,iBAAA,EAAA,CAAA,kBAAA,CAAA,CAAA,EAAA,MAAA,CAAA,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA;IACA;IACA,OAAA,WAAA;AACA;AAEA,MAAA,iBAAA,GAAAI,SAAAA,sBAAA,CACA,gBAAA,EACA,IACA,IAAAC,uBAAAA,sBAAA,CAAA;QACA,WAAA,GAAA,IAAA,GAAA,WAAA,CAAA,IAAA,CAAA;QACA,YAAA,EAAA,CAAA,IAAA,EAAA,WAAA,GAAA,YAAA,CAAA,IAAA,EAAA,WAAA,CAAA;IACA,CAAA,CAAA;AAGA,MAAA,mBAAA,GAAAD,SAAAA,sBAAA,CACA,mBAAA,EACA,IACA,IAAAE,gBAAAA,wBAAA,CAAA;QACA,WAAA,GAAA,IAAA,GAAA,WAAA,CAAA,IAAA,CAAA;QACA,YAAA,EAAA,CAAA,IAAA,EAAA,WAAA,GAAA,YAAA,CAAA,IAAA,EAAA,WAAA,CAAA;IACA,CAAA,CAAA;AAGA,MAAA,mBAAA,GAAA,MAAA;IACA,OAAA;QACA,IAAA,EAAA,gBAAA;QACA,SAAA,GAAA;YACA,iBAAA,EAAA;YACA,mBAAA,EAAA;QACA,CAAA;IACA,CAAA;AACA,CAAA,CAAA;AAEA;;;;;;;;;;;;;;;CAeA,GACA,MAAA,kBAAA,GAAAC,KAAAA,iBAAA,CAAA,mBAAA;AA4BA;;CAEA,GACA,SAAA,mBAAA,CAAA,OAAA,EAAA;IACA,OAAA,SAAA,qBAAA,CACA,KAAA,EACA,OAAA,EACA,GAAA,EACA,IAAA;QAEA,MAAA,iBAAA,GAAAC,KAAAA,wBAAA,CAAA,OAAA,CAAA;QACA,iFAAA;QACA,oGAAA;QACAR,KAAAA,iBAAA,EAAA,CAAA,wBAAA,CAAA;YAAA,iBAAA;QAAA,CAAA,CAAA;QAEA,MAAA,iBAAA,GAAA,OAAA,EAAA,iBAAA,IAAA,wBAAA;QAEA,IAAA,iBAAA,CAAA,KAAA,CAAA,EAAA;YACA,MAAA,OAAA,GAAAS,KAAAA,gBAAA,CAAA,KAAA,EAAA;gBAAA,SAAA,EAAA;oBAAA,IAAA,EAAA,YAAA;oBAAA,OAAA,EAAA,KAAA;gBAAA,CAAA;YAAA,CAAA,CAAA;YACA,GAAA,CAAA,MAAA,GAAA,OAAA;QACA;QAEA,IAAA,CAAA,KAAA,CAAA;IACA,CAAA;AACA;AAEA,SAAA,qBAAA,GAAA;IACA,OAAA,SAAA,uBAAA,CACA,OAAA,EACA,IAAA,EACA,IAAA;QAEA,MAAA,iBAAA,GAAAD,KAAAA,wBAAA,CAAA,OAAA,CAAA;QACA,iFAAA;QACAR,KAAAA,iBAAA,EAAA,CAAA,wBAAA,CAAA;YAAA,iBAAA;QAAA,CAAA,CAAA;QAEA,IAAA,EAAA;IACA,CAAA;AACA;AAEA;;;;;;;;;;;;;;;;;;;;;;;CAuBA,GACA,SAAA,wBAAA,CACA,GAAA,EACA,OAAA;IAEA,GAAA,CAAA,GAAA,CAAA,qBAAA,EAAA,CAAA;IACA,GAAA,CAAA,GAAA,CAAA,mBAAA,CAAA,OAAA,CAAA,CAAA;IACAU,SAAAA,eAAA,CAAA,GAAA,CAAA,GAAA,EAAA,SAAA,CAAA;AACA;AAEA,SAAA,yBAAA,CAAA,KAAA,EAAA;IACA,MAAA,UAAA,GAAA,KAAA,CAAA,MAAA,IAAA,KAAA,CAAA,UAAA,IAAA,KAAA,CAAA,WAAA,IAAA,KAAA,CAAA,MAAA,EAAA,UAAA;IACA,OAAA,UAAA,GAAA,QAAA,CAAA,UAAA,EAAA,EAAA,CAAA,GAAA,GAAA;AACA;AAEA,2DAAA,GACA,SAAA,wBAAA,CAAA,KAAA,EAAA;IACA,MAAA,MAAA,GAAA,yBAAA,CAAA,KAAA,CAAA;IACA,OAAA,MAAA,IAAA,GAAA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1029, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/integrations/tracing/fastify/fastify-otel/index.js"], "sourcesContent": ["/*\nVendored in and modified from @fastify/otel version 0.8.0\nhttps://github.com/fastify/otel/releases/tag/v0.8.0\n\nTried not to modify the original code too much keeping it as a JavaScript CJS module to make it easier to update when required\n\nModifications include:\n- Removed reading of package.json to get the version and package name\n\nMIT License\n\nCopyright (c) 2024 Fastify\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n*/\n\n/* eslint-disable jsdoc/require-jsdoc */\n/* eslint-disable max-lines */\n/* eslint-disable no-param-reassign */\nimport dc from 'node:diagnostics_channel';\nimport { context, diag, propagation, SpanStatusCode, trace } from '@opentelemetry/api';\nimport { getRPCMetadata, RPCType } from '@opentelemetry/core';\nimport { InstrumentationBase } from '@opentelemetry/instrumentation';\nimport {\n  ATTR_HTTP_REQUEST_METHOD,\n  ATTR_HTTP_RESPONSE_STATUS_CODE,\n  ATTR_HTTP_ROUTE,\n  ATTR_SERVICE_NAME,\n} from '@opentelemetry/semantic-conventions';\nimport * as minimatch from 'minimatch';\n\n// SENTRY VENDOR NOTE\n// Instead of using the package.json file, we hard code the package name and version here.\nconst PACKAGE_NAME = '@fastify/otel';\nconst PACKAGE_VERSION = '0.8.0';\n\n// Constants\nconst SUPPORTED_VERSIONS = '>=4.0.0 <6';\nconst FASTIFY_HOOKS = [\n  'onRequest',\n  'preParsing',\n  'preValidation',\n  'preHandler',\n  'preSerialization',\n  'onSend',\n  'onResponse',\n  'onError',\n];\nconst ATTRIBUTE_NAMES = {\n  HOOK_NAME: 'hook.name',\n  FASTIFY_TYPE: 'fastify.type',\n  HOOK_CALLBACK_NAME: 'hook.callback.name',\n  ROOT: 'fastify.root',\n};\nconst HOOK_TYPES = {\n  ROUTE: 'route-hook',\n  INSTANCE: 'hook',\n  HANDLER: 'request-handler',\n};\nconst ANONYMOUS_FUNCTION_NAME = 'anonymous';\n\n// Symbols\nconst kInstrumentation = Symbol('fastify otel instance');\nconst kRequestSpan = Symbol('fastify otel request spans');\nconst kRequestContext = Symbol('fastify otel request context');\nconst kAddHookOriginal = Symbol('fastify otel addhook original');\nconst kSetNotFoundOriginal = Symbol('fastify otel setnotfound original');\nconst kIgnorePaths = Symbol('fastify otel ignore path');\n\nexport class FastifyOtelInstrumentation extends InstrumentationBase {\n  constructor(config) {\n    super(PACKAGE_NAME, PACKAGE_VERSION, config);\n    this.servername = config?.servername ?? process.env.OTEL_SERVICE_NAME ?? 'fastify';\n    this[kIgnorePaths] = null;\n    this._logger = diag.createComponentLogger({ namespace: PACKAGE_NAME });\n\n    if (config?.ignorePaths != null || process.env.OTEL_FASTIFY_IGNORE_PATHS != null) {\n      const ignorePaths = config?.ignorePaths ?? process.env.OTEL_FASTIFY_IGNORE_PATHS;\n\n      if ((typeof ignorePaths !== 'string' || ignorePaths.length === 0) && typeof ignorePaths !== 'function') {\n        throw new TypeError('ignorePaths must be a string or a function');\n      }\n\n      const globMatcher = minimatch.minimatch;\n\n      this[kIgnorePaths] = routeOptions => {\n        if (typeof ignorePaths === 'function') {\n          return ignorePaths(routeOptions);\n        } else {\n          return globMatcher(routeOptions.url, ignorePaths);\n        }\n      };\n    }\n  }\n\n  enable() {\n    if (this._handleInitialization === undefined && this.getConfig().registerOnInitialization) {\n      const FastifyInstrumentationPlugin = this.plugin();\n      this._handleInitialization = message => {\n        message.fastify.register(FastifyInstrumentationPlugin);\n      };\n      dc.subscribe('fastify.initialization', this._handleInitialization);\n    }\n    return super.enable();\n  }\n\n  disable() {\n    if (this._handleInitialization) {\n      dc.unsubscribe('fastify.initialization', this._handleInitialization);\n      this._handleInitialization = undefined;\n    }\n    return super.disable();\n  }\n\n  // We do not do patching in this instrumentation\n  init() {\n    return [];\n  }\n\n  plugin() {\n    const instrumentation = this;\n\n    FastifyInstrumentationPlugin[Symbol.for('skip-override')] = true;\n    FastifyInstrumentationPlugin[Symbol.for('fastify.display-name')] = '@fastify/otel';\n    FastifyInstrumentationPlugin[Symbol.for('plugin-meta')] = {\n      fastify: SUPPORTED_VERSIONS,\n      name: '@fastify/otel',\n    };\n\n    return FastifyInstrumentationPlugin;\n\n    function FastifyInstrumentationPlugin(instance, opts, done) {\n      instance.decorate(kInstrumentation, instrumentation);\n      // addHook and notfoundHandler are essentially inherited from the prototype\n      // what is important is to bound it to the right instance\n      instance.decorate(kAddHookOriginal, instance.addHook);\n      instance.decorate(kSetNotFoundOriginal, instance.setNotFoundHandler);\n      instance.decorateRequest('opentelemetry', function openetelemetry() {\n        const ctx = this[kRequestContext];\n        const span = this[kRequestSpan];\n        return {\n          span,\n          tracer: instrumentation.tracer,\n          context: ctx,\n          inject: (carrier, setter) => {\n            return propagation.inject(ctx, carrier, setter);\n          },\n          extract: (carrier, getter) => {\n            return propagation.extract(ctx, carrier, getter);\n          },\n        };\n      });\n      instance.decorateRequest(kRequestSpan, null);\n      instance.decorateRequest(kRequestContext, null);\n\n      instance.addHook('onRoute', function (routeOptions) {\n        if (instrumentation[kIgnorePaths]?.(routeOptions) === true) {\n          instrumentation._logger.debug(\n            `Ignoring route instrumentation ${routeOptions.method} ${routeOptions.url} because it matches the ignore path`,\n          );\n          return;\n        }\n\n        for (const hook of FASTIFY_HOOKS) {\n          if (routeOptions[hook] != null) {\n            const handlerLike = routeOptions[hook];\n\n            if (typeof handlerLike === 'function') {\n              routeOptions[hook] = handlerWrapper(handlerLike, {\n                [ATTR_SERVICE_NAME]: instance[kInstrumentation].servername,\n                [ATTRIBUTE_NAMES.HOOK_NAME]: `${this.pluginName} - route -> ${hook}`,\n                [ATTRIBUTE_NAMES.FASTIFY_TYPE]: HOOK_TYPES.ROUTE,\n                [ATTR_HTTP_ROUTE]: routeOptions.url,\n                [ATTRIBUTE_NAMES.HOOK_CALLBACK_NAME]:\n                  handlerLike.name?.length > 0 ? handlerLike.name : ANONYMOUS_FUNCTION_NAME /* c8 ignore next */,\n              });\n            } else if (Array.isArray(handlerLike)) {\n              const wrappedHandlers = [];\n\n              for (const handler of handlerLike) {\n                wrappedHandlers.push(\n                  handlerWrapper(handler, {\n                    [ATTR_SERVICE_NAME]: instance[kInstrumentation].servername,\n                    [ATTRIBUTE_NAMES.HOOK_NAME]: `${this.pluginName} - route -> ${hook}`,\n                    [ATTRIBUTE_NAMES.FASTIFY_TYPE]: HOOK_TYPES.ROUTE,\n                    [ATTR_HTTP_ROUTE]: routeOptions.url,\n                    [ATTRIBUTE_NAMES.HOOK_CALLBACK_NAME]:\n                      handler.name?.length > 0 ? handler.name : ANONYMOUS_FUNCTION_NAME,\n                  }),\n                );\n              }\n\n              routeOptions[hook] = wrappedHandlers;\n            }\n          }\n        }\n\n        // We always want to add the onSend hook to the route to be executed last\n        if (routeOptions.onSend != null) {\n          routeOptions.onSend = Array.isArray(routeOptions.onSend)\n            ? [...routeOptions.onSend, onSendHook]\n            : [routeOptions.onSend, onSendHook];\n        } else {\n          routeOptions.onSend = onSendHook;\n        }\n\n        // We always want to add the onError hook to the route to be executed last\n        if (routeOptions.onError != null) {\n          routeOptions.onError = Array.isArray(routeOptions.onError)\n            ? [...routeOptions.onError, onErrorHook]\n            : [routeOptions.onError, onErrorHook];\n        } else {\n          routeOptions.onError = onErrorHook;\n        }\n\n        routeOptions.handler = handlerWrapper(routeOptions.handler, {\n          [ATTR_SERVICE_NAME]: instance[kInstrumentation].servername,\n          [ATTRIBUTE_NAMES.HOOK_NAME]: `${this.pluginName} - route-handler`,\n          [ATTRIBUTE_NAMES.FASTIFY_TYPE]: HOOK_TYPES.HANDLER,\n          [ATTR_HTTP_ROUTE]: routeOptions.url,\n          [ATTRIBUTE_NAMES.HOOK_CALLBACK_NAME]:\n            routeOptions.handler.name.length > 0 ? routeOptions.handler.name : ANONYMOUS_FUNCTION_NAME,\n        });\n      });\n\n      instance.addHook('onRequest', function (request, _reply, hookDone) {\n        if (this[kInstrumentation].isEnabled() === false) {\n          return hookDone();\n        } else if (\n          this[kInstrumentation][kIgnorePaths]?.({\n            url: request.url,\n            method: request.method,\n          }) === true\n        ) {\n          this[kInstrumentation]._logger.debug(\n            `Ignoring request ${request.method} ${request.url} because it matches the ignore path`,\n          );\n          return hookDone();\n        }\n\n        let ctx = context.active();\n\n        if (trace.getSpan(ctx) == null) {\n          ctx = propagation.extract(ctx, request.headers);\n        }\n\n        const rpcMetadata = getRPCMetadata(ctx);\n\n        if (request.routeOptions.url != null && rpcMetadata?.type === RPCType.HTTP) {\n          rpcMetadata.route = request.routeOptions.url;\n        }\n\n        /** @type {import('@opentelemetry/api').Span} */\n        const span = this[kInstrumentation].tracer.startSpan(\n          'request',\n          {\n            attributes: {\n              [ATTR_SERVICE_NAME]: instance[kInstrumentation].servername,\n              [ATTRIBUTE_NAMES.ROOT]: '@fastify/otel',\n              [ATTR_HTTP_ROUTE]: request.url,\n              [ATTR_HTTP_REQUEST_METHOD]: request.method,\n            },\n          },\n          ctx,\n        );\n\n        request[kRequestContext] = trace.setSpan(ctx, span);\n        request[kRequestSpan] = span;\n\n        context.with(request[kRequestContext], () => {\n          hookDone();\n        });\n      });\n\n      // onResponse is the last hook to be executed, only added for 404 handlers\n      instance.addHook('onResponse', function (request, reply, hookDone) {\n        const span = request[kRequestSpan];\n\n        if (span != null) {\n          span.setStatus({\n            code: SpanStatusCode.OK,\n            message: 'OK',\n          });\n          span.setAttributes({\n            [ATTR_HTTP_RESPONSE_STATUS_CODE]: 404,\n          });\n          span.end();\n        }\n\n        request[kRequestSpan] = null;\n\n        hookDone();\n      });\n\n      instance.addHook = addHookPatched;\n      instance.setNotFoundHandler = setNotFoundHandlerPatched;\n\n      done();\n\n      function onSendHook(request, reply, payload, hookDone) {\n        /** @type {import('@opentelemetry/api').Span} */\n        const span = request[kRequestSpan];\n\n        if (span != null) {\n          if (reply.statusCode < 500) {\n            span.setStatus({\n              code: SpanStatusCode.OK,\n              message: 'OK',\n            });\n          }\n\n          span.setAttributes({\n            [ATTR_HTTP_RESPONSE_STATUS_CODE]: reply.statusCode,\n          });\n          span.end();\n        }\n\n        request[kRequestSpan] = null;\n\n        hookDone(null, payload);\n      }\n\n      function onErrorHook(request, reply, error, hookDone) {\n        /** @type {Span} */\n        const span = request[kRequestSpan];\n\n        if (span != null) {\n          span.setStatus({\n            code: SpanStatusCode.ERROR,\n            message: error.message,\n          });\n          span.recordException(error);\n        }\n\n        hookDone();\n      }\n\n      function addHookPatched(name, hook) {\n        const addHookOriginal = this[kAddHookOriginal];\n\n        if (FASTIFY_HOOKS.includes(name)) {\n          return addHookOriginal.call(\n            this,\n            name,\n            handlerWrapper(hook, {\n              [ATTR_SERVICE_NAME]: instance[kInstrumentation].servername,\n              [ATTRIBUTE_NAMES.HOOK_NAME]: `${this.pluginName} - ${name}`,\n              [ATTRIBUTE_NAMES.FASTIFY_TYPE]: HOOK_TYPES.INSTANCE,\n              [ATTRIBUTE_NAMES.HOOK_CALLBACK_NAME]:\n                hook.name?.length > 0 ? hook.name : ANONYMOUS_FUNCTION_NAME /* c8 ignore next */,\n            }),\n          );\n        } else {\n          return addHookOriginal.call(this, name, hook);\n        }\n      }\n\n      function setNotFoundHandlerPatched(hooks, handler) {\n        const setNotFoundHandlerOriginal = this[kSetNotFoundOriginal];\n        if (typeof hooks === 'function') {\n          handler = handlerWrapper(hooks, {\n            [ATTR_SERVICE_NAME]: instance[kInstrumentation].servername,\n            [ATTRIBUTE_NAMES.HOOK_NAME]: `${this.pluginName} - not-found-handler`,\n            [ATTRIBUTE_NAMES.FASTIFY_TYPE]: HOOK_TYPES.INSTANCE,\n            [ATTRIBUTE_NAMES.HOOK_CALLBACK_NAME]:\n              hooks.name?.length > 0 ? hooks.name : ANONYMOUS_FUNCTION_NAME /* c8 ignore next */,\n          });\n          setNotFoundHandlerOriginal.call(this, handler);\n        } else {\n          if (hooks.preValidation != null) {\n            hooks.preValidation = handlerWrapper(hooks.preValidation, {\n              [ATTR_SERVICE_NAME]: instance[kInstrumentation].servername,\n              [ATTRIBUTE_NAMES.HOOK_NAME]: `${this.pluginName} - not-found-handler - preValidation`,\n              [ATTRIBUTE_NAMES.FASTIFY_TYPE]: HOOK_TYPES.INSTANCE,\n              [ATTRIBUTE_NAMES.HOOK_CALLBACK_NAME]:\n                hooks.preValidation.name?.length > 0\n                  ? hooks.preValidation.name\n                  : ANONYMOUS_FUNCTION_NAME /* c8 ignore next */,\n            });\n          }\n\n          if (hooks.preHandler != null) {\n            hooks.preHandler = handlerWrapper(hooks.preHandler, {\n              [ATTR_SERVICE_NAME]: instance[kInstrumentation].servername,\n              [ATTRIBUTE_NAMES.HOOK_NAME]: `${this.pluginName} - not-found-handler - preHandler`,\n              [ATTRIBUTE_NAMES.FASTIFY_TYPE]: HOOK_TYPES.INSTANCE,\n              [ATTRIBUTE_NAMES.HOOK_CALLBACK_NAME]:\n                hooks.preHandler.name?.length > 0\n                  ? hooks.preHandler.name\n                  : ANONYMOUS_FUNCTION_NAME /* c8 ignore next */,\n            });\n          }\n\n          handler = handlerWrapper(handler, {\n            [ATTR_SERVICE_NAME]: instance[kInstrumentation].servername,\n            [ATTRIBUTE_NAMES.HOOK_NAME]: `${this.pluginName} - not-found-handler`,\n            [ATTRIBUTE_NAMES.FASTIFY_TYPE]: HOOK_TYPES.INSTANCE,\n            [ATTRIBUTE_NAMES.HOOK_CALLBACK_NAME]:\n              handler.name?.length > 0 ? handler.name : ANONYMOUS_FUNCTION_NAME /* c8 ignore next */,\n          });\n          setNotFoundHandlerOriginal.call(this, hooks, handler);\n        }\n      }\n\n      function handlerWrapper(handler, spanAttributes = {}) {\n        return function handlerWrapped(...args) {\n          /** @type {FastifyOtelInstrumentation} */\n          const instrumentation = this[kInstrumentation];\n          const [request] = args;\n\n          if (instrumentation.isEnabled() === false) {\n            return handler.call(this, ...args);\n          }\n\n          const ctx = request[kRequestContext] ?? context.active();\n          const span = instrumentation.tracer.startSpan(\n            `handler - ${\n              handler.name?.length > 0\n                ? handler.name\n                : this.pluginName /* c8 ignore next */ ?? ANONYMOUS_FUNCTION_NAME /* c8 ignore next */\n            }`,\n            {\n              attributes: spanAttributes,\n            },\n            ctx,\n          );\n\n          return context.with(\n            trace.setSpan(ctx, span),\n            function () {\n              try {\n                const res = handler.call(this, ...args);\n\n                if (typeof res?.then === 'function') {\n                  return res.then(\n                    result => {\n                      span.end();\n                      return result;\n                    },\n                    error => {\n                      span.setStatus({\n                        code: SpanStatusCode.ERROR,\n                        message: error.message,\n                      });\n                      span.recordException(error);\n                      span.end();\n                      return Promise.reject(error);\n                    },\n                  );\n                }\n\n                span.end();\n                return res;\n              } catch (error) {\n                span.setStatus({\n                  code: SpanStatusCode.ERROR,\n                  message: error.message,\n                });\n                span.recordException(error);\n                span.end();\n                throw error;\n              }\n            },\n            this,\n          );\n        };\n      }\n    }\n  }\n}\n"], "names": ["InstrumentationBase", "diag", "dc", "propagation", "ATTR_SERVICE_NAME", "ATTR_HTTP_ROUTE", "context", "trace", "getRPCMetadata", "RPCType", "ATTR_HTTP_REQUEST_METHOD", "SpanStatusCode", "ATTR_HTTP_RESPONSE_STATUS_CODE"], "mappings": ";;;;;;;;;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BA,GAiBA,qBAAA;AACA,0FAAA;AACA,MAAM,YAAA,GAAe,eAAe;AACpC,MAAM,eAAA,GAAkB,OAAO;AAE/B,YAAA;AACA,MAAM,kBAAA,GAAqB,YAAY;AACvC,MAAM,gBAAgB;IACpB,WAAW;IACX,YAAY;IACZ,eAAe;IACf,YAAY;IACZ,kBAAkB;IAClB,QAAQ;IACR,YAAY;IACZ,SAAS;CACV;AACD,MAAM,kBAAkB;IACtB,SAAS,EAAE,WAAW;IACtB,YAAY,EAAE,cAAc;IAC5B,kBAAkB,EAAE,oBAAoB;IACxC,IAAI,EAAE,cAAc;AACtB,CAAC;AACD,MAAM,aAAa;IACjB,KAAK,EAAE,YAAY;IACnB,QAAQ,EAAE,MAAM;IAChB,OAAO,EAAE,iBAAiB;AAC5B,CAAC;AACD,MAAM,uBAAA,GAA0B,WAAW;AAE3C,UAAA;AACA,MAAM,gBAAA,GAAmB,MAAM,CAAC,uBAAuB,CAAC;AACxD,MAAM,YAAA,GAAe,MAAM,CAAC,4BAA4B,CAAC;AACzD,MAAM,eAAA,GAAkB,MAAM,CAAC,8BAA8B,CAAC;AAC9D,MAAM,gBAAA,GAAmB,MAAM,CAAC,+BAA+B,CAAC;AAChE,MAAM,oBAAA,GAAuB,MAAM,CAAC,mCAAmC,CAAC;AACxE,MAAM,YAAA,GAAe,MAAM,CAAC,0BAA0B,CAAC;AAEhD,MAAM,0BAAA,SAAmCA,gBAAAA,mBAAA,CAAoB;IAClE,WAAW,CAAC,MAAM,CAAE;QAClB,KAAK,CAAC,YAAY,EAAE,eAAe,EAAE,MAAM,CAAC;QAC5C,IAAI,CAAC,UAAA,GAAa,MAAM,EAAE,UAAA,IAAc,OAAO,CAAC,GAAG,CAAC,iBAAA,IAAqB,SAAS;QAClF,IAAI,CAAC,YAAY,CAAA,GAAI,IAAI;QACzB,IAAI,CAAC,OAAA,GAAUC,IAAAA,IAAI,CAAC,qBAAqB,CAAC;YAAE,SAAS,EAAE,YAAA;QAAA,CAAc,CAAC;QAEtE,IAAI,MAAM,EAAE,eAAe,IAAA,IAAQ,OAAO,CAAC,GAAG,CAAC,yBAAA,IAA6B,IAAI,EAAE;YAChF,MAAM,WAAA,GAAc,MAAM,EAAE,WAAA,IAAe,OAAO,CAAC,GAAG,CAAC,yBAAyB;YAEhF,IAAI,CAAC,OAAO,WAAA,KAAgB,YAAY,WAAW,CAAC,MAAA,KAAW,CAAC,KAAK,OAAO,WAAA,KAAgB,UAAU,EAAE;gBACtG,MAAM,IAAI,SAAS,CAAC,4CAA4C,CAAC;YACzE;YAEM,MAAM,WAAA,GAAc,SAAS,CAAC,SAAS;YAEvC,IAAI,CAAC,YAAY,CAAA,IAAI,gBAAgB;gBACnC,IAAI,OAAO,WAAA,KAAgB,UAAU,EAAE;oBACrC,OAAO,WAAW,CAAC,YAAY,CAAC;gBAC1C,OAAe;oBACL,OAAO,WAAW,CAAC,YAAY,CAAC,GAAG,EAAE,WAAW,CAAC;gBAC3D;YACA,CAAO;QACP;IACA;IAEE,MAAM,GAAG;QACP,IAAI,IAAI,CAAC,qBAAA,KAA0B,SAAA,IAAa,IAAI,CAAC,SAAS,EAAE,CAAC,wBAAwB,EAAE;YACzF,MAAM,4BAAA,GAA+B,IAAI,CAAC,MAAM,EAAE;YAClD,IAAI,CAAC,qBAAA,IAAwB,WAAW;gBACtC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,4BAA4B,CAAC;YAC9D,CAAO;YACDC,GAAAA,OAAE,CAAC,SAAS,CAAC,wBAAwB,EAAE,IAAI,CAAC,qBAAqB,CAAC;QACxE;QACI,OAAO,KAAK,CAAC,MAAM,EAAE;IACzB;IAEE,OAAO,GAAG;QACR,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC9BA,GAAAA,OAAE,CAAC,WAAW,CAAC,wBAAwB,EAAE,IAAI,CAAC,qBAAqB,CAAC;YACpE,IAAI,CAAC,qBAAA,GAAwB,SAAS;QAC5C;QACI,OAAO,KAAK,CAAC,OAAO,EAAE;IAC1B;IAEA,gDAAA;IACE,IAAI,GAAG;QACL,OAAO,EAAE;IACb;IAEE,MAAM,GAAG;QACP,MAAM,eAAA,GAAkB,IAAI;QAE5B,4BAA4B,CAAC,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,CAAA,GAAI,IAAI;QAChE,4BAA4B,CAAC,MAAM,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAA,GAAI,eAAe;QAClF,4BAA4B,CAAC,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC,CAAA,GAAI;YACxD,OAAO,EAAE,kBAAkB;YAC3B,IAAI,EAAE,eAAe;QAC3B,CAAK;QAED,OAAO,4BAA4B;;QAEnC,SAAS,4BAA4B,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE;YAC1D,QAAQ,CAAC,QAAQ,CAAC,gBAAgB,EAAE,eAAe,CAAC;YAC1D,2EAAA;YACA,yDAAA;YACM,QAAQ,CAAC,QAAQ,CAAC,gBAAgB,EAAE,QAAQ,CAAC,OAAO,CAAC;YACrD,QAAQ,CAAC,QAAQ,CAAC,oBAAoB,EAAE,QAAQ,CAAC,kBAAkB,CAAC;YACpE,QAAQ,CAAC,eAAe,CAAC,eAAe,EAAE,SAAS,cAAc,GAAG;gBAClE,MAAM,GAAA,GAAM,IAAI,CAAC,eAAe,CAAC;gBACjC,MAAM,IAAA,GAAO,IAAI,CAAC,YAAY,CAAC;gBAC/B,OAAO;oBACL,IAAI;oBACJ,MAAM,EAAE,eAAe,CAAC,MAAM;oBAC9B,OAAO,EAAE,GAAG;oBACZ,MAAM,EAAE,CAAC,OAAO,EAAE,MAAM,KAAK;wBAC3B,OAAOC,IAAAA,WAAW,CAAC,MAAM,CAAC,GAAG,EAAE,OAAO,EAAE,MAAM,CAAC;oBAC3D,CAAW;oBACD,OAAO,EAAE,CAAC,OAAO,EAAE,MAAM,KAAK;wBAC5B,OAAOA,IAAAA,WAAW,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,EAAE,MAAM,CAAC;oBAC5D,CAAW;gBACX,CAAS;YACT,CAAO,CAAC;YACF,QAAQ,CAAC,eAAe,CAAC,YAAY,EAAE,IAAI,CAAC;YAC5C,QAAQ,CAAC,eAAe,CAAC,eAAe,EAAE,IAAI,CAAC;YAE/C,QAAQ,CAAC,OAAO,CAAC,SAAS,EAAE,SAAU,YAAY,EAAE;gBAClD,IAAI,eAAe,CAAC,YAAY,CAAC,GAAG,YAAY,CAAA,KAAM,IAAI,EAAE;oBAC1D,eAAe,CAAC,OAAO,CAAC,KAAK,CAC3B,CAAC,+BAA+B,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC,EAAE,YAAY,CAAC,GAAG,CAAC,mCAAmC,CAAC;oBAEhH;gBACV;gBAEQ,KAAK,MAAM,IAAA,IAAQ,aAAa,CAAE;oBAChC,IAAI,YAAY,CAAC,IAAI,CAAA,IAAK,IAAI,EAAE;wBAC9B,MAAM,WAAA,GAAc,YAAY,CAAC,IAAI,CAAC;wBAEtC,IAAI,OAAO,WAAA,KAAgB,UAAU,EAAE;4BACrC,YAAY,CAAC,IAAI,CAAA,GAAI,cAAc,CAAC,WAAW,EAAE;gCAC/C,CAACC,oBAAAA,iBAAiB,CAAA,EAAG,QAAQ,CAAC,gBAAgB,CAAC,CAAC,UAAU;gCAC1D,CAAC,eAAe,CAAC,SAAS,CAAA,EAAG,CAAC,EAAA,IAAA,CAAA,UAAA,CAAA,YAAA,EAAA,IAAA,CAAA,CAAA;gCACA,CAAA,eAAA,CAAA,YAAA,CAAA,EAAA,UAAA,CAAA,KAAA;gCACA,CAAAC,oBAAAA,eAAA,CAAA,EAAA,YAAA,CAAA,GAAA;gCACA,CAAA,eAAA,CAAA,kBAAA,CAAA,EACA,WAAA,CAAA,IAAA,EAAA,MAAA,GAAA,CAAA,GAAA,WAAA,CAAA,IAAA,GAAA,uBAAA,CAAA,kBAAA;4BACA,CAAA,CAAA;wBACA,CAAA,MAAA,IAAA,KAAA,CAAA,OAAA,CAAA,WAAA,CAAA,EAAA;4BACA,MAAA,eAAA,GAAA,EAAA;4BAEA,KAAA,MAAA,OAAA,IAAA,WAAA,CAAA;gCACA,eAAA,CAAA,IAAA,CACA,cAAA,CAAA,OAAA,EAAA;oCACA,CAAAD,oBAAAA,iBAAA,CAAA,EAAA,QAAA,CAAA,gBAAA,CAAA,CAAA,UAAA;oCACA,CAAA,eAAA,CAAA,SAAA,CAAA,EAAA,CAAA,EAAA,IAAA,CAAA,UAAA,CAAA,YAAA,EAAA,IAAA,CAAA,CAAA;oCACA,CAAA,eAAA,CAAA,YAAA,CAAA,EAAA,UAAA,CAAA,KAAA;oCACA,CAAAC,oBAAAA,eAAA,CAAA,EAAA,YAAA,CAAA,GAAA;oCACA,CAAA,eAAA,CAAA,kBAAA,CAAA,EACA,OAAA,CAAA,IAAA,EAAA,MAAA,GAAA,CAAA,GAAA,OAAA,CAAA,IAAA,GAAA,uBAAA;gCACA,CAAA,CAAA;4BAEA;4BAEA,YAAA,CAAA,IAAA,CAAA,GAAA,eAAA;wBACA;oBACA;gBACA;gBAEA,yEAAA;gBACA,IAAA,YAAA,CAAA,MAAA,IAAA,IAAA,EAAA;oBACA,YAAA,CAAA,MAAA,GAAA,KAAA,CAAA,OAAA,CAAA,YAAA,CAAA,MAAA,IACA,CAAA;2BAAA,YAAA,CAAA,MAAA;wBAAA,UAAA;qBAAA,GACA;wBAAA,YAAA,CAAA,MAAA;wBAAA,UAAA;qBAAA;gBACA,CAAA,MAAA;oBACA,YAAA,CAAA,MAAA,GAAA,UAAA;gBACA;gBAEA,0EAAA;gBACA,IAAA,YAAA,CAAA,OAAA,IAAA,IAAA,EAAA;oBACA,YAAA,CAAA,OAAA,GAAA,KAAA,CAAA,OAAA,CAAA,YAAA,CAAA,OAAA,IACA,CAAA;2BAAA,YAAA,CAAA,OAAA;wBAAA,WAAA;qBAAA,GACA;wBAAA,YAAA,CAAA,OAAA;wBAAA,WAAA;qBAAA;gBACA,CAAA,MAAA;oBACA,YAAA,CAAA,OAAA,GAAA,WAAA;gBACA;gBAEA,YAAA,CAAA,OAAA,GAAA,cAAA,CAAA,YAAA,CAAA,OAAA,EAAA;oBACA,CAAAD,oBAAAA,iBAAA,CAAA,EAAA,QAAA,CAAA,gBAAA,CAAA,CAAA,UAAA;oBACA,CAAA,eAAA,CAAA,SAAA,CAAA,EAAA,CAAA,EAAA,IAAA,CAAA,UAAA,CAAA,gBAAA,CAAA;oBACA,CAAA,eAAA,CAAA,YAAA,CAAA,EAAA,UAAA,CAAA,OAAA;oBACA,CAAAC,oBAAAA,eAAA,CAAA,EAAA,YAAA,CAAA,GAAA;oBACA,CAAA,eAAA,CAAA,kBAAA,CAAA,EACA,YAAA,CAAA,OAAA,CAAA,IAAA,CAAA,MAAA,GAAA,CAAA,GAAA,YAAA,CAAA,OAAA,CAAA,IAAA,GAAA,uBAAA;gBACA,CAAA,CAAA;YACA,CAAA,CAAA;YAEA,QAAA,CAAA,OAAA,CAAA,WAAA,EAAA,SAAA,OAAA,EAAA,MAAA,EAAA,QAAA,EAAA;gBACA,IAAA,IAAA,CAAA,gBAAA,CAAA,CAAA,SAAA,EAAA,KAAA,KAAA,EAAA;oBACA,OAAA,QAAA,EAAA;gBACA,CAAA,MAAA,IACA,IAAA,CAAA,gBAAA,CAAA,CAAA,YAAA,CAAA,GAAA;oBACA,GAAA,EAAA,OAAA,CAAA,GAAA;oBACA,MAAA,EAAA,OAAA,CAAA,MAAA;gBACA,CAAA,CAAA,KAAA,MACA;oBACA,IAAA,CAAA,gBAAA,CAAA,CAAA,OAAA,CAAA,KAAA,CACA,CAAA,iBAAA,EAAA,OAAA,CAAA,MAAA,CAAA,CAAA,EAAA,OAAA,CAAA,GAAA,CAAA,mCAAA,CAAA;oBAEA,OAAA,QAAA,EAAA;gBACA;gBAEA,IAAA,GAAA,GAAAC,IAAAA,OAAA,CAAA,MAAA,EAAA;gBAEA,IAAAC,IAAAA,KAAA,CAAA,OAAA,CAAA,GAAA,CAAA,IAAA,IAAA,EAAA;oBACA,GAAA,GAAAJ,IAAAA,WAAA,CAAA,OAAA,CAAA,GAAA,EAAA,OAAA,CAAA,OAAA,CAAA;gBACA;gBAEA,MAAA,WAAA,GAAAK,KAAAA,cAAA,CAAA,GAAA,CAAA;gBAEA,IAAA,OAAA,CAAA,YAAA,CAAA,GAAA,IAAA,IAAA,IAAA,WAAA,EAAA,IAAA,KAAAC,KAAAA,OAAA,CAAA,IAAA,EAAA;oBACA,WAAA,CAAA,KAAA,GAAA,OAAA,CAAA,YAAA,CAAA,GAAA;gBACA;gBAEA,8CAAA,GACA,MAAA,IAAA,GAAA,IAAA,CAAA,gBAAA,CAAA,CAAA,MAAA,CAAA,SAAA,CACA,SAAA,EACA;oBACA,UAAA,EAAA;wBACA,CAAAL,oBAAAA,iBAAA,CAAA,EAAA,QAAA,CAAA,gBAAA,CAAA,CAAA,UAAA;wBACA,CAAA,eAAA,CAAA,IAAA,CAAA,EAAA,eAAA;wBACA,CAAAC,oBAAAA,eAAA,CAAA,EAAA,OAAA,CAAA,GAAA;wBACA,CAAAK,oBAAAA,wBAAA,CAAA,EAAA,OAAA,CAAA,MAAA;oBACA,CAAA;gBACA,CAAA,EACA,GAAA;gBAGA,OAAA,CAAA,eAAA,CAAA,GAAAH,IAAAA,KAAA,CAAA,OAAA,CAAA,GAAA,EAAA,IAAA,CAAA;gBACA,OAAA,CAAA,YAAA,CAAA,GAAA,IAAA;gBAEAD,IAAAA,OAAA,CAAA,IAAA,CAAA,OAAA,CAAA,eAAA,CAAA,EAAA,MAAA;oBACA,QAAA,EAAA;gBACA,CAAA,CAAA;YACA,CAAA,CAAA;YAEA,0EAAA;YACA,QAAA,CAAA,OAAA,CAAA,YAAA,EAAA,SAAA,OAAA,EAAA,KAAA,EAAA,QAAA,EAAA;gBACA,MAAA,IAAA,GAAA,OAAA,CAAA,YAAA,CAAA;gBAEA,IAAA,IAAA,IAAA,IAAA,EAAA;oBACA,IAAA,CAAA,SAAA,CAAA;wBACA,IAAA,EAAAK,IAAAA,cAAA,CAAA,EAAA;wBACA,OAAA,EAAA,IAAA;oBACA,CAAA,CAAA;oBACA,IAAA,CAAA,aAAA,CAAA;wBACA,CAAAC,oBAAAA,8BAAA,CAAA,EAAA,GAAA;oBACA,CAAA,CAAA;oBACA,IAAA,CAAA,GAAA,EAAA;gBACA;gBAEA,OAAA,CAAA,YAAA,CAAA,GAAA,IAAA;gBAEA,QAAA,EAAA;YACA,CAAA,CAAA;YAEA,QAAA,CAAA,OAAA,GAAA,cAAA;YACA,QAAA,CAAA,kBAAA,GAAA,yBAAA;YAEA,IAAA,EAAA;YAEA,SAAA,UAAA,CAAA,OAAA,EAAA,KAAA,EAAA,OAAA,EAAA,QAAA,EAAA;gBACA,8CAAA,GACA,MAAA,IAAA,GAAA,OAAA,CAAA,YAAA,CAAA;gBAEA,IAAA,IAAA,IAAA,IAAA,EAAA;oBACA,IAAA,KAAA,CAAA,UAAA,GAAA,GAAA,EAAA;wBACA,IAAA,CAAA,SAAA,CAAA;4BACA,IAAA,EAAAD,IAAAA,cAAA,CAAA,EAAA;4BACA,OAAA,EAAA,IAAA;wBACA,CAAA,CAAA;oBACA;oBAEA,IAAA,CAAA,aAAA,CAAA;wBACA,CAAAC,oBAAAA,8BAAA,CAAA,EAAA,KAAA,CAAA,UAAA;oBACA,CAAA,CAAA;oBACA,IAAA,CAAA,GAAA,EAAA;gBACA;gBAEA,OAAA,CAAA,YAAA,CAAA,GAAA,IAAA;gBAEA,QAAA,CAAA,IAAA,EAAA,OAAA,CAAA;YACA;YAEA,SAAA,WAAA,CAAA,OAAA,EAAA,KAAA,EAAA,KAAA,EAAA,QAAA,EAAA;gBACA,iBAAA,GACA,MAAA,IAAA,GAAA,OAAA,CAAA,YAAA,CAAA;gBAEA,IAAA,IAAA,IAAA,IAAA,EAAA;oBACA,IAAA,CAAA,SAAA,CAAA;wBACA,IAAA,EAAAD,IAAAA,cAAA,CAAA,KAAA;wBACA,OAAA,EAAA,KAAA,CAAA,OAAA;oBACA,CAAA,CAAA;oBACA,IAAA,CAAA,eAAA,CAAA,KAAA,CAAA;gBACA;gBAEA,QAAA,EAAA;YACA;YAEA,SAAA,cAAA,CAAA,IAAA,EAAA,IAAA,EAAA;gBACA,MAAA,eAAA,GAAA,IAAA,CAAA,gBAAA,CAAA;gBAEA,IAAA,aAAA,CAAA,QAAA,CAAA,IAAA,CAAA,EAAA;oBACA,OAAA,eAAA,CAAA,IAAA,CACA,IAAA,EACA,IAAA,EACA,cAAA,CAAA,IAAA,EAAA;wBACA,CAAAP,oBAAAA,iBAAA,CAAA,EAAA,QAAA,CAAA,gBAAA,CAAA,CAAA,UAAA;wBACA,CAAA,eAAA,CAAA,SAAA,CAAA,EAAA,CAAA,EAAA,IAAA,CAAA,UAAA,CAAA,GAAA,EAAA,IAAA,CAAA,CAAA;wBACA,CAAA,eAAA,CAAA,YAAA,CAAA,EAAA,UAAA,CAAA,QAAA;wBACA,CAAA,eAAA,CAAA,kBAAA,CAAA,EACA,IAAA,CAAA,IAAA,EAAA,MAAA,GAAA,CAAA,GAAA,IAAA,CAAA,IAAA,GAAA,uBAAA,CAAA,kBAAA;oBACA,CAAA,CAAA;gBAEA,CAAA,MAAA;oBACA,OAAA,eAAA,CAAA,IAAA,CAAA,IAAA,EAAA,IAAA,EAAA,IAAA,CAAA;gBACA;YACA;YAEA,SAAA,yBAAA,CAAA,KAAA,EAAA,OAAA,EAAA;gBACA,MAAA,0BAAA,GAAA,IAAA,CAAA,oBAAA,CAAA;gBACA,IAAA,OAAA,KAAA,KAAA,UAAA,EAAA;oBACA,OAAA,GAAA,cAAA,CAAA,KAAA,EAAA;wBACA,CAAAA,oBAAAA,iBAAA,CAAA,EAAA,QAAA,CAAA,gBAAA,CAAA,CAAA,UAAA;wBACA,CAAA,eAAA,CAAA,SAAA,CAAA,EAAA,CAAA,EAAA,IAAA,CAAA,UAAA,CAAA,oBAAA,CAAA;wBACA,CAAA,eAAA,CAAA,YAAA,CAAA,EAAA,UAAA,CAAA,QAAA;wBACA,CAAA,eAAA,CAAA,kBAAA,CAAA,EACA,KAAA,CAAA,IAAA,EAAA,MAAA,GAAA,CAAA,GAAA,KAAA,CAAA,IAAA,GAAA,uBAAA,CAAA,kBAAA;oBACA,CAAA,CAAA;oBACA,0BAAA,CAAA,IAAA,CAAA,IAAA,EAAA,OAAA,CAAA;gBACA,CAAA,MAAA;oBACA,IAAA,KAAA,CAAA,aAAA,IAAA,IAAA,EAAA;wBACA,KAAA,CAAA,aAAA,GAAA,cAAA,CAAA,KAAA,CAAA,aAAA,EAAA;4BACA,CAAAA,oBAAAA,iBAAA,CAAA,EAAA,QAAA,CAAA,gBAAA,CAAA,CAAA,UAAA;4BACA,CAAA,eAAA,CAAA,SAAA,CAAA,EAAA,CAAA,EAAA,IAAA,CAAA,UAAA,CAAA,oCAAA,CAAA;4BACA,CAAA,eAAA,CAAA,YAAA,CAAA,EAAA,UAAA,CAAA,QAAA;4BACA,CAAA,eAAA,CAAA,kBAAA,CAAA,EACA,KAAA,CAAA,aAAA,CAAA,IAAA,EAAA,MAAA,GAAA,IACA,KAAA,CAAA,aAAA,CAAA,IAAA,GACA,uBAAA,CAAA,kBAAA;wBACA,CAAA,CAAA;oBACA;oBAEA,IAAA,KAAA,CAAA,UAAA,IAAA,IAAA,EAAA;wBACA,KAAA,CAAA,UAAA,GAAA,cAAA,CAAA,KAAA,CAAA,UAAA,EAAA;4BACA,CAAAA,oBAAAA,iBAAA,CAAA,EAAA,QAAA,CAAA,gBAAA,CAAA,CAAA,UAAA;4BACA,CAAA,eAAA,CAAA,SAAA,CAAA,EAAA,CAAA,EAAA,IAAA,CAAA,UAAA,CAAA,iCAAA,CAAA;4BACA,CAAA,eAAA,CAAA,YAAA,CAAA,EAAA,UAAA,CAAA,QAAA;4BACA,CAAA,eAAA,CAAA,kBAAA,CAAA,EACA,KAAA,CAAA,UAAA,CAAA,IAAA,EAAA,MAAA,GAAA,IACA,KAAA,CAAA,UAAA,CAAA,IAAA,GACA,uBAAA,CAAA,kBAAA;wBACA,CAAA,CAAA;oBACA;oBAEA,OAAA,GAAA,cAAA,CAAA,OAAA,EAAA;wBACA,CAAAA,oBAAAA,iBAAA,CAAA,EAAA,QAAA,CAAA,gBAAA,CAAA,CAAA,UAAA;wBACA,CAAA,eAAA,CAAA,SAAA,CAAA,EAAA,CAAA,EAAA,IAAA,CAAA,UAAA,CAAA,oBAAA,CAAA;wBACA,CAAA,eAAA,CAAA,YAAA,CAAA,EAAA,UAAA,CAAA,QAAA;wBACA,CAAA,eAAA,CAAA,kBAAA,CAAA,EACA,OAAA,CAAA,IAAA,EAAA,MAAA,GAAA,CAAA,GAAA,OAAA,CAAA,IAAA,GAAA,uBAAA,CAAA,kBAAA;oBACA,CAAA,CAAA;oBACA,0BAAA,CAAA,IAAA,CAAA,IAAA,EAAA,KAAA,EAAA,OAAA,CAAA;gBACA;YACA;YAEA,SAAA,cAAA,CAAA,OAAA,EAAA,cAAA,GAAA,CAAA,CAAA,EAAA;gBACA,OAAA,SAAA,cAAA,CAAA,GAAA,IAAA,EAAA;oBACA,uCAAA,GACA,MAAA,eAAA,GAAA,IAAA,CAAA,gBAAA,CAAA;oBACA,MAAA,CAAA,OAAA,CAAA,GAAA,IAAA;oBAEA,IAAA,eAAA,CAAA,SAAA,EAAA,KAAA,KAAA,EAAA;wBACA,OAAA,OAAA,CAAA,IAAA,CAAA,IAAA,EAAA,GAAA,IAAA,CAAA;oBACA;oBAEA,MAAA,GAAA,GAAA,OAAA,CAAA,eAAA,CAAA,IAAAE,IAAAA,OAAA,CAAA,MAAA,EAAA;oBACA,MAAA,IAAA,GAAA,eAAA,CAAA,MAAA,CAAA,SAAA,CACA,CAAA,UAAA,EACA,OAAA,CAAA,IAAA,EAAA,MAAA,GAAA,IACA,OAAA,CAAA,IAAA,GACA,IAAA,CAAA,UAAA,CAAA,kBAAA,OAAA,uBAAA,CAAA,kBAAA,KACA,EACA;wBACA,UAAA,EAAA,cAAA;oBACA,CAAA,EACA,GAAA;oBAGA,OAAAA,IAAAA,OAAA,CAAA,IAAA,CACAC,IAAAA,KAAA,CAAA,OAAA,CAAA,GAAA,EAAA,IAAA,CAAA,EACA,YAAA;wBACA,IAAA;4BACA,MAAA,GAAA,GAAA,OAAA,CAAA,IAAA,CAAA,IAAA,EAAA,GAAA,IAAA,CAAA;4BAEA,IAAA,OAAA,GAAA,EAAA,IAAA,KAAA,UAAA,EAAA;gCACA,OAAA,GAAA,CAAA,IAAA,EACA,MAAA,IAAA;oCACA,IAAA,CAAA,GAAA,EAAA;oCACA,OAAA,MAAA;gCACA,CAAA,GACA,KAAA,IAAA;oCACA,IAAA,CAAA,SAAA,CAAA;wCACA,IAAA,EAAAI,IAAAA,cAAA,CAAA,KAAA;wCACA,OAAA,EAAA,KAAA,CAAA,OAAA;oCACA,CAAA,CAAA;oCACA,IAAA,CAAA,eAAA,CAAA,KAAA,CAAA;oCACA,IAAA,CAAA,GAAA,EAAA;oCACA,OAAA,OAAA,CAAA,MAAA,CAAA,KAAA,CAAA;gCACA,CAAA;4BAEA;4BAEA,IAAA,CAAA,GAAA,EAAA;4BACA,OAAA,GAAA;wBACA,CAAA,CAAA,OAAA,KAAA,EAAA;4BACA,IAAA,CAAA,SAAA,CAAA;gCACA,IAAA,EAAAA,IAAAA,cAAA,CAAA,KAAA;gCACA,OAAA,EAAA,KAAA,CAAA,OAAA;4BACA,CAAA,CAAA;4BACA,IAAA,CAAA,eAAA,CAAA,KAAA,CAAA;4BACA,IAAA,CAAA,GAAA,EAAA;4BACA,MAAA,KAAA;wBACA;oBACA,CAAA,EACA,IAAA;gBAEA,CAAA;YACA;QACA;IACA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1422, "column": 0}, "map": {"version": 3, "file": "AttributeNames.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/integrations/tracing/fastify/v3/enums/AttributeNames.ts"], "sourcesContent": ["// Vendored from https://github.com/open-telemetry/opentelemetry-js-contrib/blob/407f61591ba69a39a6908264379d4d98a48dbec4/plugins/node/opentelemetry-instrumentation-fastify/src/enums/AttributeNames.ts\n//\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport enum AttributeNames {\n  FASTIFY_NAME = 'fastify.name',\n  FASTIFY_TYPE = 'fastify.type',\n  HOOK_NAME = 'hook.name',\n  PLUGIN_NAME = 'plugin.name',\n}\n\nexport enum FastifyTypes {\n  MIDDLEWARE = 'middleware',\n  REQUEST_HANDLER = 'request_handler',\n}\n\nexport enum FastifyNames {\n  MIDDLEWARE = 'middleware',\n  REQUEST_HANDLER = 'request handler',\n}\n"], "names": ["AttributeNames", "FastifyTypes", "FastifyNames"], "mappings": ";;;AAAA,wMAAA;AACA,EAAA;AACA;;;;;;;;;;;;;;CAcA,GAEAA,QAAAA,cAAAA,GAAAA,KAAAA,EAAA;AAAA,CAAA,SAAA,cAAA,EAA2B;IACzB,MAAA,YAAA,GAAe,cAAA,CAAA;IAAA,cAAA,CAAA,cAAA,CAAA,GAAA,YAAA;IACf,MAAA,YAAA,GAAe,cAAA,CAAA;IAAA,cAAA,CAAA,cAAA,CAAA,GAAA,YAAA;IACf,MAAA,SAAA,GAAY,WAAA,CAAA;IAAA,cAAA,CAAA,WAAA,CAAA,GAAA,SAAA;IACZ,MAAA,WAAA,GAAc,aAAA,CAAA;IAAA,cAAA,CAAA,aAAA,CAAA,GAAA,WAAA;AAChB,CAAA,EAAAA,QAAAA,cAAA,IAAA,CAAAA,QAAAA,cAAA,GAAA,CAAA,CAAA,CAAA,CAAA;AAEAC,QAAAA,YAAAA,GAAAA,KAAAA,EAAA;AAAA,CAAA,SAAA,YAAA,EAAyB;IACvB,MAAA,UAAA,GAAa,YAAA,CAAA;IAAA,YAAA,CAAA,YAAA,CAAA,GAAA,UAAA;IACb,MAAA,eAAA,GAAkB,iBAAA,CAAA;IAAA,YAAA,CAAA,iBAAA,CAAA,GAAA,eAAA;AACpB,CAAA,EAAAA,QAAAA,YAAA,IAAA,CAAAA,QAAAA,YAAA,GAAA,CAAA,CAAA,CAAA,CAAA;AAEAC,QAAAA,YAAAA,GAAAA,KAAAA,EAAA;AAAA,CAAA,SAAA,YAAA,EAAyB;IACvB,MAAA,UAAA,GAAa,YAAA,CAAA;IAAA,YAAA,CAAA,YAAA,CAAA,GAAA,UAAA;IACb,MAAA,eAAA,GAAkB,iBAAA,CAAA;IAAA,YAAA,CAAA,iBAAA,CAAA,GAAA,eAAA;AACpB,CAAA,EAAAA,QAAAA,YAAA,IAAA,CAAAA,QAAAA,YAAA,GAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1471, "column": 0}, "map": {"version": 3, "file": "constants.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/integrations/tracing/fastify/v3/constants.ts"], "sourcesContent": ["// Vendored from https://github.com/open-telemetry/opentelemetry-js-contrib/blob/407f61591ba69a39a6908264379d4d98a48dbec4/plugins/node/opentelemetry-instrumentation-fastify/src/constants.ts\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport const spanRequestSymbol = Symbol('opentelemetry.instrumentation.fastify.request_active_span');\n\n// The instrumentation creates a span for invocations of lifecycle hook handlers\n// that take `(request, reply, ...[, done])` arguments. Currently this is all\n// lifecycle hooks except `onRequestAbort`.\n// https://fastify.dev/docs/latest/Reference/Hooks\nexport const hooksNamesToWrap = new Set([\n  'onTimeout',\n  'onRequest',\n  'preParsing',\n  'preValidation',\n  'preSerialization',\n  'preHandler',\n  'onSend',\n  'onResponse',\n  'onError',\n]);\n"], "names": [], "mappings": ";;;AAAA,6LAAA;AACA;;;;;;;;;;;;;;CAcA,SAEa,iBAAA,GAAoB,MAAM,CAAC,2DAA2D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1496, "column": 0}, "map": {"version": 3, "file": "utils.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/integrations/tracing/fastify/v3/utils.ts"], "sourcesContent": ["// Vendored from: https://github.com/open-telemetry/opentelemetry-js-contrib/blob/407f61591ba69a39a6908264379d4d98a48dbec4/plugins/node/opentelemetry-instrumentation-fastify/src/utils.ts\n/* eslint-disable jsdoc/require-jsdoc */\n/* eslint-disable @typescript-eslint/no-dynamic-delete */\n/* eslint-disable @typescript-eslint/no-unsafe-member-access */\n/* eslint-disable @typescript-eslint/explicit-function-return-type */\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { type Attributes, type Span, type Tracer, SpanStatusCode } from '@opentelemetry/api';\nimport { spanRequestSymbol } from './constants';\nimport type { PluginFastifyReply } from './internal-types';\n\n/**\n * Starts Span\n * @param reply - reply function\n * @param tracer - tracer\n * @param spanName - span name\n * @param spanAttributes - span attributes\n */\nexport function startSpan(\n  reply: PluginFastifyReply,\n  tracer: Tracer,\n  spanName: string,\n  spanAttributes: Attributes = {},\n) {\n  const span = tracer.startSpan(spanName, { attributes: spanAttributes });\n\n  const spans: Span[] = reply[spanRequestSymbol] || [];\n  spans.push(span);\n\n  // eslint-disable-next-line @typescript-eslint/no-floating-promises\n  Object.defineProperty(reply, spanRequestSymbol, {\n    enumerable: false,\n    configurable: true,\n    value: spans,\n  });\n\n  return span;\n}\n\n/**\n * Ends span\n * @param reply - reply function\n * @param err - error\n */\nexport function endSpan(reply: PluginFastifyReply, err?: any) {\n  const spans = reply[spanRequestSymbol] || [];\n  // there is no active span, or it has already ended\n  if (!spans.length) {\n    return;\n  }\n  // biome-ignore lint/complexity/noForEach: <explanation>\n  spans.forEach((span: Span) => {\n    if (err) {\n      span.setStatus({\n        code: SpanStatusCode.ERROR,\n        message: err.message,\n      });\n      span.recordException(err);\n    }\n    span.end();\n  });\n  delete reply[spanRequestSymbol];\n}\n\n// @TODO after approve add this to instrumentation package and replace usage\n// when it will be released\n\n/**\n * This function handles the missing case from instrumentation package when\n * execute can either return a promise or void. And using async is not an\n * option as it is producing unwanted side effects.\n * @param execute - function to be executed\n * @param onFinish - function called when function executed\n * @param preventThrowingError - prevent to throw error when execute\n * function fails\n */\nexport function safeExecuteInTheMiddleMaybePromise<T>(\n  execute: () => Promise<T>,\n  onFinish: (e: unknown, result?: T) => void,\n  preventThrowingError?: boolean,\n): Promise<T>;\nexport function safeExecuteInTheMiddleMaybePromise<T>(\n  execute: () => T,\n  onFinish: (e: unknown, result?: T) => void,\n  preventThrowingError?: boolean,\n): T;\nexport function safeExecuteInTheMiddleMaybePromise<T>(\n  execute: () => T | Promise<T>,\n  onFinish: (e: unknown, result?: T) => void,\n  preventThrowingError?: boolean,\n): T | Promise<T> | undefined {\n  let error: unknown;\n  let result: T | Promise<T> | undefined = undefined;\n  try {\n    result = execute();\n\n    if (isPromise(result)) {\n      result.then(\n        res => onFinish(undefined, res),\n        err => onFinish(err),\n      );\n    }\n  } catch (e) {\n    error = e;\n  } finally {\n    if (!isPromise(result)) {\n      onFinish(error, result);\n      if (error && !preventThrowingError) {\n        // eslint-disable-next-line no-unsafe-finally\n        throw error;\n      }\n    }\n    // eslint-disable-next-line no-unsafe-finally\n    return result;\n  }\n}\n\nfunction isPromise<T>(val: T | Promise<T>): val is Promise<T> {\n  return (\n    (typeof val === 'object' && val && typeof Object.getOwnPropertyDescriptor(val, 'then')?.value === 'function') ||\n    false\n  );\n}\n"], "names": ["spanRequestSymbol", "SpanStatusCode"], "mappings": ";;;;;AAAA,0LAAA;AACA,sCAAA,GACA,uDAAA,GACA,6DAAA,GACA,mEAAA,GACA;;;;;;;;;;;;;;CAcA,GAMA;;;;;;CAMA,GACO,SAAS,SAAS,CACvB,KAAK,EACL,MAAM,EACN,QAAQ,EACR,cAAc,GAAe,CAAA,CAAE;IAE/B,MAAM,IAAA,GAAO,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE;QAAE,UAAU,EAAE,cAAA;IAAA,CAAgB,CAAC;IAEvE,MAAM,KAAK,GAAW,KAAK,CAACA,UAAAA,iBAAiB,CAAA,IAAK,EAAE;IACpD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;IAElB,mEAAA;IACE,MAAM,CAAC,cAAc,CAAC,KAAK,EAAEA,UAAAA,iBAAiB,EAAE;QAC9C,UAAU,EAAE,KAAK;QACjB,YAAY,EAAE,IAAI;QAClB,KAAK,EAAE,KAAK;IAChB,CAAG,CAAC;IAEF,OAAO,IAAI;AACb;AAEA;;;;CAIA,GACO,SAAS,OAAO,CAAC,KAAK,EAAsB,GAAG,EAAQ;IAC5D,MAAM,QAAQ,KAAK,CAACA,UAAAA,iBAAiB,CAAA,IAAK,EAAE;IAC9C,mDAAA;IACE,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;QACjB;IACJ;IACA,wDAAA;IACE,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,KAAW;QAC5B,IAAI,GAAG,EAAE;YACP,IAAI,CAAC,SAAS,CAAC;gBACb,IAAI,EAAEC,IAAAA,cAAc,CAAC,KAAK;gBAC1B,OAAO,EAAE,GAAG,CAAC,OAAO;YAC5B,CAAO,CAAC;YACF,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;QAC/B;QACI,IAAI,CAAC,GAAG,EAAE;IACd,CAAG,CAAC;IACF,OAAO,KAAK,CAACD,UAAAA,iBAAiB,CAAC;AACjC;AAEA,4EAAA;AACA,2BAAA;AAEA;;;;;;;;CAQA,GAWO,SAAS,kCAAkC,CAChD,OAAO,EACP,QAAQ,EACR,oBAAoB;IAEpB,IAAI,KAAK;IACT,IAAI,MAAM,GAA+B,SAAS;IAClD,IAAI;QACF,MAAA,GAAS,OAAO,EAAE;QAElB,IAAI,SAAS,CAAC,MAAM,CAAC,EAAE;YACrB,MAAM,CAAC,IAAI,EACT,MAAO,QAAQ,CAAC,SAAS,EAAE,GAAG,CAAC,GAC/B,GAAA,GAAO,QAAQ,CAAC,GAAG,CAAC;QAE5B;IACA,CAAE,CAAE,OAAO,CAAC,EAAE;QACV,KAAA,GAAQ,CAAC;IACb,SAAY;QACR,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;YACtB,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC;YACvB,IAAI,KAAA,IAAS,IAAqB,EAAE;gBAC1C,6CAAA;gBACQ,MAAM,KAAK;YACnB;QACA;QACA,6CAAA;QACI,OAAO,MAAM;IACjB;AACA;AAEA,SAAS,SAAS,CAAI,GAAG,EAAqC;IAC5D,OACE,AAAC,OAAO,GAAA,KAAQ,YAAY,GAAA,IAAO,OAAO,MAAM,CAAC,wBAAwB,CAAC,GAAG,EAAE,MAAM,CAAC,EAAE,KAAA,KAAU,UAAU,IAC5G;AAEJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1602, "column": 0}, "map": {"version": 3, "file": "instrumentation.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/integrations/tracing/fastify/v3/instrumentation.ts"], "sourcesContent": ["// Vendored from: https://github.com/open-telemetry/opentelemetry-js-contrib/blob/407f61591ba69a39a6908264379d4d98a48dbec4/plugins/node/opentelemetry-instrumentation-fastify/src/instrumentation.ts\n/* eslint-disable @typescript-eslint/no-this-alias */\n/* eslint-disable jsdoc/require-jsdoc */\n/* eslint-disable @typescript-eslint/explicit-function-return-type */\n/* eslint-disable @typescript-eslint/no-unsafe-member-access */\n\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { type Attributes, context, SpanStatusCode, trace } from '@opentelemetry/api';\nimport { getRPCMetadata, RPCType } from '@opentelemetry/core';\nimport {\n  InstrumentationBase,\n  InstrumentationNodeModuleDefinition,\n  safeExecuteInTheMiddle,\n} from '@opentelemetry/instrumentation';\nimport { SEMATTRS_HTTP_ROUTE } from '@opentelemetry/semantic-conventions';\nimport type { Span } from '@sentry/core';\nimport {\n  getClient,\n  getIsolationScope,\n  SEMANTIC_ATTRIBUTE_SENTRY_OP,\n  SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,\n  spanToJSON,\n} from '@sentry/core';\nimport type {\n  FastifyErrorCodes,\n  FastifyInstance,\n  FastifyReply,\n  FastifyRequest,\n  HandlerOriginal,\n  HookHandlerDoneFunction,\n} from '../types';\nimport { AttributeNames, FastifyNames, FastifyTypes } from './enums/AttributeNames';\nimport type { PluginFastifyReply } from './internal-types';\nimport type { FastifyInstrumentationConfig } from './types';\nimport { endSpan, safeExecuteInTheMiddleMaybePromise, startSpan } from './utils';\n/** @knipignore */\n\nconst PACKAGE_VERSION = '0.1.0';\n\nconst PACKAGE_NAME = '@sentry/instrumentation-fastify-v3';\nconst ANONYMOUS_NAME = 'anonymous';\n\n// The instrumentation creates a span for invocations of lifecycle hook handlers\n// that take `(request, reply, ...[, done])` arguments. Currently this is all\n// lifecycle hooks except `onRequestAbort`.\n// https://fastify.dev/docs/latest/Reference/Hooks\nconst hooksNamesToWrap = new Set([\n  'onTimeout',\n  'onRequest',\n  'preParsing',\n  'preValidation',\n  'preSerialization',\n  'preHandler',\n  'onSend',\n  'onResponse',\n  'onError',\n]);\n\n/**\n * Fastify instrumentation for OpenTelemetry\n */\nexport class FastifyInstrumentationV3 extends InstrumentationBase<FastifyInstrumentationConfig> {\n  public constructor(config: FastifyInstrumentationConfig = {}) {\n    super(PACKAGE_NAME, PACKAGE_VERSION, config);\n  }\n\n  public init(): InstrumentationNodeModuleDefinition[] {\n    return [\n      new InstrumentationNodeModuleDefinition('fastify', ['>=3.0.0 <4'], moduleExports => {\n        return this._patchConstructor(moduleExports);\n      }),\n    ];\n  }\n\n  private _hookOnRequest() {\n    const instrumentation = this;\n\n    return function onRequest(request: FastifyRequest, reply: FastifyReply, done: HookHandlerDoneFunction) {\n      if (!instrumentation.isEnabled()) {\n        return done();\n      }\n      instrumentation._wrap(reply, 'send', instrumentation._patchSend());\n\n      const anyRequest = request as any;\n\n      const rpcMetadata = getRPCMetadata(context.active());\n      const routeName = anyRequest.routeOptions\n        ? anyRequest.routeOptions.url // since fastify@4.10.0\n        : request.routerPath;\n      if (routeName && rpcMetadata?.type === RPCType.HTTP) {\n        rpcMetadata.route = routeName;\n      }\n\n      const method = request.method || 'GET';\n\n      getIsolationScope().setTransactionName(`${method} ${routeName}`);\n      done();\n    };\n  }\n\n  private _wrapHandler(\n    pluginName: string,\n    hookName: string,\n    original: HandlerOriginal,\n    syncFunctionWithDone: boolean,\n  ): () => Promise<unknown> {\n    const instrumentation = this;\n    this._diag.debug('Patching fastify route.handler function');\n\n    return function (this: any, ...args: unknown[]): Promise<unknown> {\n      if (!instrumentation.isEnabled()) {\n        return original.apply(this, args);\n      }\n\n      const name = original.name || pluginName || ANONYMOUS_NAME;\n      const spanName = `${FastifyNames.MIDDLEWARE} - ${name}`;\n\n      const reply = args[1] as PluginFastifyReply;\n\n      const span = startSpan(reply, instrumentation.tracer, spanName, {\n        [AttributeNames.FASTIFY_TYPE]: FastifyTypes.MIDDLEWARE,\n        [AttributeNames.PLUGIN_NAME]: pluginName,\n        [AttributeNames.HOOK_NAME]: hookName,\n      });\n\n      const origDone = syncFunctionWithDone && (args[args.length - 1] as HookHandlerDoneFunction);\n      if (origDone) {\n        args[args.length - 1] = function (...doneArgs: Parameters<HookHandlerDoneFunction>) {\n          endSpan(reply);\n          origDone.apply(this, doneArgs);\n        };\n      }\n\n      return context.with(trace.setSpan(context.active(), span), () => {\n        return safeExecuteInTheMiddleMaybePromise(\n          () => {\n            return original.apply(this, args);\n          },\n          err => {\n            if (err instanceof Error) {\n              span.setStatus({\n                code: SpanStatusCode.ERROR,\n                message: err.message,\n              });\n              span.recordException(err);\n            }\n            // async hooks should end the span as soon as the promise is resolved\n            if (!syncFunctionWithDone) {\n              endSpan(reply);\n            }\n          },\n        );\n      });\n    };\n  }\n\n  private _wrapAddHook(): (original: FastifyInstance['addHook']) => () => FastifyInstance {\n    const instrumentation = this;\n    this._diag.debug('Patching fastify server.addHook function');\n\n    // biome-ignore lint/complexity/useArrowFunction: <explanation>\n    return function (original: FastifyInstance['addHook']): () => FastifyInstance {\n      return function wrappedAddHook(this: any, ...args: any) {\n        const name = args[0] as string;\n        const handler = args[1] as HandlerOriginal;\n        const pluginName = this.pluginName;\n        if (!hooksNamesToWrap.has(name)) {\n          return original.apply(this, args);\n        }\n\n        const syncFunctionWithDone =\n          typeof args[args.length - 1] === 'function' && handler.constructor.name !== 'AsyncFunction';\n\n        return original.apply(this, [\n          name,\n          instrumentation._wrapHandler(pluginName, name, handler, syncFunctionWithDone),\n        ] as never);\n      };\n    };\n  }\n\n  private _patchConstructor(moduleExports: {\n    fastify: () => FastifyInstance;\n    errorCodes: FastifyErrorCodes | undefined;\n  }): () => FastifyInstance {\n    const instrumentation = this;\n\n    function fastify(this: FastifyInstance, ...args: any) {\n      const app: FastifyInstance = moduleExports.fastify.apply(this, args);\n      app.addHook('onRequest', instrumentation._hookOnRequest());\n      app.addHook('preHandler', instrumentation._hookPreHandler());\n\n      instrumentClient();\n\n      instrumentation._wrap(app, 'addHook', instrumentation._wrapAddHook());\n\n      return app;\n    }\n\n    if (moduleExports.errorCodes !== undefined) {\n      fastify.errorCodes = moduleExports.errorCodes;\n    }\n    fastify.fastify = fastify;\n    fastify.default = fastify;\n    return fastify;\n  }\n\n  private _patchSend() {\n    const instrumentation = this;\n    this._diag.debug('Patching fastify reply.send function');\n\n    return function patchSend(original: () => FastifyReply): () => FastifyReply {\n      return function send(this: FastifyReply, ...args: any) {\n        const maybeError: any = args[0];\n\n        if (!instrumentation.isEnabled()) {\n          return original.apply(this, args);\n        }\n\n        return safeExecuteInTheMiddle<FastifyReply>(\n          () => {\n            return original.apply(this, args);\n          },\n          err => {\n            if (!err && maybeError instanceof Error) {\n              // eslint-disable-next-line no-param-reassign\n              err = maybeError;\n            }\n            endSpan(this, err);\n          },\n        );\n      };\n    };\n  }\n\n  private _hookPreHandler() {\n    const instrumentation = this;\n    this._diag.debug('Patching fastify preHandler function');\n\n    return function preHandler(this: any, request: FastifyRequest, reply: FastifyReply, done: HookHandlerDoneFunction) {\n      if (!instrumentation.isEnabled()) {\n        return done();\n      }\n      const anyRequest = request as any;\n\n      const handler = anyRequest.routeOptions?.handler || anyRequest.context?.handler;\n      const handlerName = handler?.name.startsWith('bound ') ? handler.name.substring(6) : handler?.name;\n      const spanName = `${FastifyNames.REQUEST_HANDLER} - ${handlerName || this.pluginName || ANONYMOUS_NAME}`;\n\n      const spanAttributes: Attributes = {\n        [AttributeNames.PLUGIN_NAME]: this.pluginName,\n        [AttributeNames.FASTIFY_TYPE]: FastifyTypes.REQUEST_HANDLER,\n        // eslint-disable-next-line deprecation/deprecation\n        [SEMATTRS_HTTP_ROUTE]: anyRequest.routeOptions\n          ? anyRequest.routeOptions.url // since fastify@4.10.0\n          : request.routerPath,\n      };\n      if (handlerName) {\n        spanAttributes[AttributeNames.FASTIFY_NAME] = handlerName;\n      }\n      const span = startSpan(reply, instrumentation.tracer, spanName, spanAttributes);\n\n      addFastifyV3SpanAttributes(span);\n\n      const { requestHook } = instrumentation.getConfig();\n      if (requestHook) {\n        safeExecuteInTheMiddle(\n          () => requestHook(span, { request }),\n          e => {\n            if (e) {\n              instrumentation._diag.error('request hook failed', e);\n            }\n          },\n          true,\n        );\n      }\n\n      return context.with(trace.setSpan(context.active(), span), () => {\n        done();\n      });\n    };\n  }\n}\n\nfunction instrumentClient(): void {\n  const client = getClient();\n  if (client) {\n    client.on('spanStart', (span: Span) => {\n      addFastifyV3SpanAttributes(span);\n    });\n  }\n}\n\nfunction addFastifyV3SpanAttributes(span: Span): void {\n  const attributes = spanToJSON(span).data;\n\n  // this is one of: middleware, request_handler\n  const type = attributes['fastify.type'];\n\n  // If this is already set, or we have no fastify span, no need to process again...\n  if (attributes[SEMANTIC_ATTRIBUTE_SENTRY_OP] || !type) {\n    return;\n  }\n\n  span.setAttributes({\n    [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.http.otel.fastify',\n    [SEMANTIC_ATTRIBUTE_SENTRY_OP]: `${type}.fastify`,\n  });\n\n  // Also update the name, we don't need to \"middleware - \" prefix\n  const name = attributes['fastify.name'] || attributes['plugin.name'] || attributes['hook.name'];\n  if (typeof name === 'string') {\n    // Try removing `fastify -> ` and `@fastify/otel -> ` prefixes\n    // This is a bit of a hack, and not always working for all spans\n    // But it's the best we can do without a proper API\n    const updatedName = name.replace(/^fastify -> /, '').replace(/^@fastify\\/otel -> /, '');\n\n    span.updateName(updatedName);\n  }\n}\n"], "names": ["InstrumentationBase", "InstrumentationNodeModuleDefinition", "getRPCMetadata", "context", "RPCType", "getIsolationScope", "FastifyNames", "startSpan", "AttributeNames", "FastifyTypes", "endSpan", "trace", "safeExecuteInTheMiddleMaybePromise", "SpanStatusCode", "instrumentation", "safeExecuteInTheMiddle", "SEMATTRS_HTTP_ROUTE", "getClient", "spanToJSON", "SEMANTIC_ATTRIBUTE_SENTRY_OP", "SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN"], "mappings": ";;;;;;;;;;AAAA,oMAAA;AACA,mDAAA,GACA,sCAAA,GACA,mEAAA,GACA,6DAAA,GA8CA,gBAAA,GAEA,MAAM,eAAA,GAAkB,OAAO;AAE/B,MAAM,YAAA,GAAe,oCAAoC;AACzD,MAAM,cAAA,GAAiB,WAAW;AAElC,gFAAA;AACA,6EAAA;AACA,2CAAA;AACA,kDAAA;AACA,MAAM,gBAAA,GAAmB,IAAI,GAAG,CAAC;IAC/B,WAAW;IACX,WAAW;IACX,YAAY;IACZ,eAAe;IACf,kBAAkB;IAClB,YAAY;IACZ,QAAQ;IACR,YAAY;IACZ,SAAS;CACV,CAAC;AAEF;;CAEA,GACO,MAAM,wBAAA,SAAiCA,gBAAAA,mBAAmB,CAA+B;IACvF,WAAW,CAAC,MAAM,GAAiC,CAAA,CAAE,CAAE;QAC5D,KAAK,CAAC,YAAY,EAAE,eAAe,EAAE,MAAM,CAAC;IAChD;IAES,IAAI,GAA0C;QACnD,OAAO;YACL,IAAIC,gBAAAA,mCAAmC,CAAC,SAAS,EAAE;gBAAC,YAAY;aAAC,GAAE,aAAA,IAAiB;gBAClF,OAAO,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC;YACpD,CAAO,CAAC;SACH;IACL;IAEU,cAAc,GAAG;QACvB,MAAM,eAAA,GAAkB,IAAI;QAE5B,OAAO,SAAS,SAAS,CAAC,OAAO,EAAkB,KAAK,EAAgB,IAAI,EAA2B;YACrG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,EAAE;gBAChC,OAAO,IAAI,EAAE;YACrB;YACM,eAAe,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,EAAE,eAAe,CAAC,UAAU,EAAE,CAAC;YAElE,MAAM,UAAA,GAAa,OAAA;YAEnB,MAAM,WAAA,GAAcC,KAAAA,cAAc,CAACC,IAAAA,OAAO,CAAC,MAAM,EAAE,CAAC;YACpD,MAAM,SAAA,GAAY,UAAU,CAAC,YAAA,GACzB,UAAU,CAAC,YAAY,CAAC,GAAA,CAAA,uBAAA;eACxB,OAAO,CAAC,UAAU;YACtB,IAAI,SAAA,IAAa,WAAW,EAAE,IAAA,KAASC,KAAAA,OAAO,CAAC,IAAI,EAAE;gBACnD,WAAW,CAAC,KAAA,GAAQ,SAAS;YACrC;YAEM,MAAM,MAAA,GAAS,OAAO,CAAC,MAAA,IAAU,KAAK;YAEtCC,OAAAA,iBAAiB,EAAE,CAAC,kBAAkB,CAAC,CAAC,EAAA,MAAA,CAAA,CAAA,EAAA,SAAA,CAAA,CAAA,CAAA;YACA,IAAA,EAAA;QACA,CAAA;IACA;IAEA,YAAA,CACA,UAAA,EACA,QAAA,EACA,QAAA,EACA,oBAAA,EACA;QACA,MAAA,eAAA,GAAA,IAAA;QACA,IAAA,CAAA,KAAA,CAAA,KAAA,CAAA,yCAAA,CAAA;QAEA,OAAA,SAAA,GAAA,IAAA,EAAA;YACA,IAAA,CAAA,eAAA,CAAA,SAAA,EAAA,EAAA;gBACA,OAAA,QAAA,CAAA,KAAA,CAAA,IAAA,EAAA,IAAA,CAAA;YACA;YAEA,MAAA,IAAA,GAAA,QAAA,CAAA,IAAA,IAAA,UAAA,IAAA,cAAA;YACA,MAAA,QAAA,GAAA,CAAA,EAAAC,eAAAA,YAAA,CAAA,UAAA,CAAA,GAAA,EAAA,IAAA,CAAA,CAAA;YAEA,MAAA,KAAA,GAAA,IAAA,CAAA,CAAA,CAAA;YAEA,MAAA,IAAA,GAAAC,MAAAA,SAAA,CAAA,KAAA,EAAA,eAAA,CAAA,MAAA,EAAA,QAAA,EAAA;gBACA,CAAAC,eAAAA,cAAA,CAAA,YAAA,CAAA,EAAAC,eAAAA,YAAA,CAAA,UAAA;gBACA,CAAAD,eAAAA,cAAA,CAAA,WAAA,CAAA,EAAA,UAAA;gBACA,CAAAA,eAAAA,cAAA,CAAA,SAAA,CAAA,EAAA,QAAA;YACA,CAAA,CAAA;YAEA,MAAA,QAAA,GAAA,oBAAA,IAAA,IAAA,CAAA,IAAA,CAAA,MAAA,GAAA,CAAA,CAAA,EAAA;YACA,IAAA,QAAA,EAAA;gBACA,IAAA,CAAA,IAAA,CAAA,MAAA,GAAA,CAAA,CAAA,GAAA,SAAA,GAAA,QAAA,EAAA;oBACAE,MAAAA,OAAA,CAAA,KAAA,CAAA;oBACA,QAAA,CAAA,KAAA,CAAA,IAAA,EAAA,QAAA,CAAA;gBACA,CAAA;YACA;YAEA,OAAAP,IAAAA,OAAA,CAAA,IAAA,CAAAQ,IAAAA,KAAA,CAAA,OAAA,CAAAR,IAAAA,OAAA,CAAA,MAAA,EAAA,EAAA,IAAA,CAAA,EAAA,MAAA;gBACA,OAAAS,MAAAA,kCAAA,CACA,MAAA;oBACA,OAAA,QAAA,CAAA,KAAA,CAAA,IAAA,EAAA,IAAA,CAAA;gBACA,CAAA,GACA,GAAA,IAAA;oBACA,IAAA,GAAA,YAAA,KAAA,EAAA;wBACA,IAAA,CAAA,SAAA,CAAA;4BACA,IAAA,EAAAC,IAAAA,cAAA,CAAA,KAAA;4BACA,OAAA,EAAA,GAAA,CAAA,OAAA;wBACA,CAAA,CAAA;wBACA,IAAA,CAAA,eAAA,CAAA,GAAA,CAAA;oBACA;oBACA,qEAAA;oBACA,IAAA,CAAA,oBAAA,EAAA;wBACAH,MAAAA,OAAA,CAAA,KAAA,CAAA;oBACA;gBACA,CAAA;YAEA,CAAA,CAAA;QACA,CAAA;IACA;IAEA,YAAA,GAAA;QACA,MAAA,eAAA,GAAA,IAAA;QACA,IAAA,CAAA,KAAA,CAAA,KAAA,CAAA,0CAAA,CAAA;QAEA,+DAAA;QACA,OAAA,SAAA,QAAA,EAAA;YACA,OAAA,SAAA,cAAA,CAAA,GAAA,IAAA,EAAA;gBACA,MAAA,IAAA,GAAA,IAAA,CAAA,CAAA,CAAA;gBACA,MAAA,OAAA,GAAA,IAAA,CAAA,CAAA,CAAA;gBACA,MAAA,UAAA,GAAA,IAAA,CAAA,UAAA;gBACA,IAAA,CAAA,gBAAA,CAAA,GAAA,CAAA,IAAA,CAAA,EAAA;oBACA,OAAA,QAAA,CAAA,KAAA,CAAA,IAAA,EAAA,IAAA,CAAA;gBACA;gBAEA,MAAA,oBAAA,GACA,OAAA,IAAA,CAAA,IAAA,CAAA,MAAA,GAAA,CAAA,CAAA,KAAA,UAAA,IAAA,OAAA,CAAA,WAAA,CAAA,IAAA,KAAA,eAAA;gBAEA,OAAA,QAAA,CAAA,KAAA,CAAA,IAAA,EAAA;oBACA,IAAA;oBACA,eAAA,CAAA,YAAA,CAAA,UAAA,EAAA,IAAA,EAAA,OAAA,EAAA,oBAAA,CAAA;iBACA,EAAA;YACA,CAAA;QACA,CAAA;IACA;IAEA,iBAAA,CAAA,aAAA,EAGA;QACA,MAAA,eAAA,GAAA,IAAA;QAEA,SAAA,OAAA,CAAA,GAAA,IAAA,EAAA;YACA,MAAA,GAAA,GAAA,aAAA,CAAA,OAAA,CAAA,KAAA,CAAA,IAAA,EAAA,IAAA,CAAA;YACA,GAAA,CAAA,OAAA,CAAA,WAAA,EAAA,eAAA,CAAA,cAAA,EAAA,CAAA;YACA,GAAA,CAAA,OAAA,CAAA,YAAA,EAAA,eAAA,CAAA,eAAA,EAAA,CAAA;YAEA,gBAAA,EAAA;YAEA,eAAA,CAAA,KAAA,CAAA,GAAA,EAAA,SAAA,EAAA,eAAA,CAAA,YAAA,EAAA,CAAA;YAEA,OAAA,GAAA;QACA;QAEA,IAAA,aAAA,CAAA,UAAA,KAAA,SAAA,EAAA;YACA,OAAA,CAAA,UAAA,GAAA,aAAA,CAAA,UAAA;QACA;QACA,OAAA,CAAA,OAAA,GAAA,OAAA;QACA,OAAA,CAAA,OAAA,GAAA,OAAA;QACA,OAAA,OAAA;IACA;IAEA,UAAA,GAAA;QACA,MAAAI,iBAAA,GAAA,IAAA;QACA,IAAA,CAAA,KAAA,CAAA,KAAA,CAAA,sCAAA,CAAA;QAEA,OAAA,SAAA,SAAA,CAAA,QAAA,EAAA;YACA,OAAA,SAAA,IAAA,CAAA,GAAA,IAAA,EAAA;gBACA,MAAA,UAAA,GAAA,IAAA,CAAA,CAAA,CAAA;gBAEA,IAAA,CAAAA,iBAAA,CAAA,SAAA,EAAA,EAAA;oBACA,OAAA,QAAA,CAAA,KAAA,CAAA,IAAA,EAAA,IAAA,CAAA;gBACA;gBAEA,OAAAC,gBAAAA,sBAAA,CACA,MAAA;oBACA,OAAA,QAAA,CAAA,KAAA,CAAA,IAAA,EAAA,IAAA,CAAA;gBACA,CAAA,GACA,GAAA,IAAA;oBACA,IAAA,CAAA,GAAA,IAAA,UAAA,YAAA,KAAA,EAAA;wBACA,6CAAA;wBACA,GAAA,GAAA,UAAA;oBACA;oBACAL,MAAAA,OAAA,CAAA,IAAA,EAAA,GAAA,CAAA;gBACA,CAAA;YAEA,CAAA;QACA,CAAA;IACA;IAEA,eAAA,GAAA;QACA,MAAAI,iBAAA,GAAA,IAAA;QACA,IAAA,CAAA,KAAA,CAAA,KAAA,CAAA,sCAAA,CAAA;QAEA,OAAA,SAAA,UAAA,CAAA,OAAA,EAAA,KAAA,EAAA,IAAA,EAAA;YACA,IAAA,CAAAA,iBAAA,CAAA,SAAA,EAAA,EAAA;gBACA,OAAA,IAAA,EAAA;YACA;YACA,MAAA,UAAA,GAAA,OAAA;YAEA,MAAA,OAAA,GAAA,UAAA,CAAA,YAAA,EAAA,OAAA,IAAA,UAAA,CAAA,OAAA,EAAA,OAAA;YACA,MAAA,WAAA,GAAA,OAAA,EAAA,IAAA,CAAA,UAAA,CAAA,QAAA,CAAA,GAAA,OAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,CAAA,GAAA,OAAA,EAAA,IAAA;YACA,MAAA,QAAA,GAAA,CAAA,EAAAR,eAAAA,YAAA,CAAA,eAAA,CAAA,GAAA,EAAA,WAAA,IAAA,IAAA,CAAA,UAAA,IAAA,cAAA,CAAA,CAAA;YAEA,MAAA,cAAA,GAAA;gBACA,CAAAE,eAAAA,cAAA,CAAA,WAAA,CAAA,EAAA,IAAA,CAAA,UAAA;gBACA,CAAAA,eAAAA,cAAA,CAAA,YAAA,CAAA,EAAAC,eAAAA,YAAA,CAAA,eAAA;gBACA,mDAAA;gBACA,CAAAO,oBAAAA,mBAAA,CAAA,EAAA,UAAA,CAAA,YAAA,GACA,UAAA,CAAA,YAAA,CAAA,GAAA,CAAA,uBAAA;mBACA,OAAA,CAAA,UAAA;YACA,CAAA;YACA,IAAA,WAAA,EAAA;gBACA,cAAA,CAAAR,eAAAA,cAAA,CAAA,YAAA,CAAA,GAAA,WAAA;YACA;YACA,MAAA,IAAA,GAAAD,MAAAA,SAAA,CAAA,KAAA,EAAAO,iBAAA,CAAA,MAAA,EAAA,QAAA,EAAA,cAAA,CAAA;YAEA,0BAAA,CAAA,IAAA,CAAA;YAEA,MAAA,EAAA,WAAA,EAAA,GAAAA,iBAAA,CAAA,SAAA,EAAA;YACA,IAAA,WAAA,EAAA;gBACAC,gBAAAA,sBAAA,CACA,IAAA,WAAA,CAAA,IAAA,EAAA;wBAAA,OAAA;oBAAA,CAAA,CAAA,GACA,CAAA,IAAA;oBACA,IAAA,CAAA,EAAA;wBACAD,iBAAA,CAAA,KAAA,CAAA,KAAA,CAAA,qBAAA,EAAA,CAAA,CAAA;oBACA;gBACA,CAAA,EACA,IAAA;YAEA;YAEA,OAAAX,IAAAA,OAAA,CAAA,IAAA,CAAAQ,IAAAA,KAAA,CAAA,OAAA,CAAAR,IAAAA,OAAA,CAAA,MAAA,EAAA,EAAA,IAAA,CAAA,EAAA,MAAA;gBACA,IAAA,EAAA;YACA,CAAA,CAAA;QACA,CAAA;IACA;AACA;AAEA,SAAA,gBAAA,GAAA;IACA,MAAA,MAAA,GAAAc,OAAAA,SAAA,EAAA;IACA,IAAA,MAAA,EAAA;QACA,MAAA,CAAA,EAAA,CAAA,WAAA,EAAA,CAAA,IAAA,KAAA;YACA,0BAAA,CAAA,IAAA,CAAA;QACA,CAAA,CAAA;IACA;AACA;AAEA,SAAA,0BAAA,CAAA,IAAA,EAAA;IACA,MAAA,UAAA,GAAAC,OAAAA,UAAA,CAAA,IAAA,CAAA,CAAA,IAAA;IAEA,8CAAA;IACA,MAAA,IAAA,GAAA,UAAA,CAAA,cAAA,CAAA;IAEA,kFAAA;IACA,IAAA,UAAA,CAAAC,OAAAA,4BAAA,CAAA,IAAA,CAAA,IAAA,EAAA;QACA;IACA;IAEA,IAAA,CAAA,aAAA,CAAA;QACA,CAAAC,OAAAA,gCAAA,CAAA,EAAA,wBAAA;QACA,CAAAD,OAAAA,4BAAA,CAAA,EAAA,CAAA,EAAA,IAAA,CAAA,QAAA,CAAA;IACA,CAAA,CAAA;IAEA,gEAAA;IACA,MAAA,IAAA,GAAA,UAAA,CAAA,cAAA,CAAA,IAAA,UAAA,CAAA,aAAA,CAAA,IAAA,UAAA,CAAA,WAAA,CAAA;IACA,IAAA,OAAA,IAAA,KAAA,QAAA,EAAA;QACA,8DAAA;QACA,gEAAA;QACA,mDAAA;QACA,MAAA,WAAA,GAAA,IAAA,CAAA,OAAA,CAAA,cAAA,EAAA,EAAA,CAAA,CAAA,OAAA,CAAA,qBAAA,EAAA,EAAA,CAAA;QAEA,IAAA,CAAA,UAAA,CAAA,WAAA,CAAA;IACA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1839, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/integrations/tracing/fastify/index.ts"], "sourcesContent": ["import * as diagnosticsChannel from 'node:diagnostics_channel';\nimport type { Instrumentation, InstrumentationConfig } from '@opentelemetry/instrumentation';\nimport type { IntegrationFn, Span } from '@sentry/core';\nimport {\n  captureException,\n  debug,\n  defineIntegration,\n  getClient,\n  getIsolationScope,\n  SEMANTIC_ATTRIBUTE_SENTRY_OP,\n  SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,\n  spanToJSON,\n} from '@sentry/core';\nimport { generateInstrumentOnce } from '@sentry/node-core';\nimport { DEBUG_BUILD } from '../../../debug-build';\nimport { FastifyOtelInstrumentation } from './fastify-otel/index';\nimport type { FastifyInstance, FastifyReply, FastifyRequest } from './types';\nimport { FastifyInstrumentationV3 } from './v3/instrumentation';\n\n/**\n * Options for the Fastify integration.\n *\n * `shouldHandleError` - Callback method deciding whether error should be captured and sent to Sentry\n * This is used on Fastify v5 where <PERSON><PERSON> handles errors in the diagnostics channel.\n * Fastify v3 and v4 use `setupFastifyErrorHandler` instead.\n *\n * @example\n *\n * ```javascript\n * Sentry.init({\n *   integrations: [\n *     Sentry.fastifyIntegration({\n *       shouldHandleError(_error, _request, reply) {\n *         return reply.statusCode >= 500;\n *       },\n *     });\n *   },\n * });\n * ```\n *\n */\ninterface FastifyIntegrationOptions {\n  /**\n   * Callback method deciding whether error should be captured and sent to Sentry\n   * This is used on Fastify v5 where Sentry handles errors in the diagnostics channel.\n   * Fastify v3 and v4 use `setupFastifyErrorHandler` instead.\n   *\n   * @param error Captured Fastify error\n   * @param request Fastify request (or any object containing at least method, routeOptions.url, and routerPath)\n   * @param reply Fastify reply (or any object containing at least statusCode)\n   */\n  shouldHandleError: (error: Error, request: FastifyRequest, reply: FastifyReply) => boolean;\n}\n\ninterface FastifyHandlerOptions {\n  /**\n   * Callback method deciding whether error should be captured and sent to Sentry\n   *\n   * @param error Captured Fastify error\n   * @param request Fastify request (or any object containing at least method, routeOptions.url, and routerPath)\n   * @param reply Fastify reply (or any object containing at least statusCode)\n   *\n   * @example\n   *\n   *\n   * ```javascript\n   * setupFastifyErrorHandler(app, {\n   *   shouldHandleError(_error, _request, reply) {\n   *     return reply.statusCode >= 400;\n   *   },\n   * });\n   * ```\n   *\n   *\n   * If using TypeScript, you can cast the request and reply to get full type safety.\n   *\n   * ```typescript\n   * import type { FastifyRequest, FastifyReply } from 'fastify';\n   *\n   * setupFastifyErrorHandler(app, {\n   *   shouldHandleError(error, minimalRequest, minimalReply) {\n   *     const request = minimalRequest as FastifyRequest;\n   *     const reply = minimalReply as FastifyReply;\n   *     return reply.statusCode >= 500;\n   *   },\n   * });\n   * ```\n   */\n  shouldHandleError: (error: Error, request: FastifyRequest, reply: FastifyReply) => boolean;\n}\n\nconst INTEGRATION_NAME = 'Fastify';\nconst INTEGRATION_NAME_V5 = 'Fastify-V5';\nconst INTEGRATION_NAME_V3 = 'Fastify-V3';\n\nexport const instrumentFastifyV3 = generateInstrumentOnce(INTEGRATION_NAME_V3, () => new FastifyInstrumentationV3());\n\nfunction getFastifyIntegration(): ReturnType<typeof _fastifyIntegration> | undefined {\n  const client = getClient();\n  if (!client) {\n    return undefined;\n  } else {\n    return client.getIntegrationByName(INTEGRATION_NAME) as ReturnType<typeof _fastifyIntegration> | undefined;\n  }\n}\n\nfunction handleFastifyError(\n  this: {\n    diagnosticsChannelExists?: boolean;\n  },\n  error: Error,\n  request: FastifyRequest & { opentelemetry?: () => { span?: Span } },\n  reply: FastifyReply,\n  handlerOrigin: 'diagnostics-channel' | 'onError-hook',\n): void {\n  const shouldHandleError = getFastifyIntegration()?.getShouldHandleError() || defaultShouldHandleError;\n  // Diagnostics channel runs before the onError hook, so we can use it to check if the handler was already registered\n  if (handlerOrigin === 'diagnostics-channel') {\n    this.diagnosticsChannelExists = true;\n  }\n\n  if (this.diagnosticsChannelExists && handlerOrigin === 'onError-hook') {\n    DEBUG_BUILD &&\n      debug.warn(\n        'Fastify error handler was already registered via diagnostics channel.',\n        'You can safely remove `setupFastifyErrorHandler` call and set `shouldHandleError` on the integration options.',\n      );\n\n    // If the diagnostics channel already exists, we don't need to handle the error again\n    return;\n  }\n\n  if (shouldHandleError(error, request, reply)) {\n    captureException(error, { mechanism: { handled: false, type: 'fastify' } });\n  }\n}\n\nexport const instrumentFastify = generateInstrumentOnce(INTEGRATION_NAME_V5, () => {\n  const fastifyOtelInstrumentationInstance = new FastifyOtelInstrumentation();\n  const plugin = fastifyOtelInstrumentationInstance.plugin();\n\n  // This message handler works for Fastify versions 3, 4 and 5\n  diagnosticsChannel.subscribe('fastify.initialization', message => {\n    const fastifyInstance = (message as { fastify?: FastifyInstance }).fastify;\n\n    fastifyInstance?.register(plugin).after(err => {\n      if (err) {\n        DEBUG_BUILD && debug.error('Failed to setup Fastify instrumentation', err);\n      } else {\n        instrumentClient();\n\n        if (fastifyInstance) {\n          instrumentOnRequest(fastifyInstance);\n        }\n      }\n    });\n  });\n\n  // This diagnostics channel only works on Fastify version 5\n  // For versions 3 and 4, we use `setupFastifyErrorHandler` instead\n  diagnosticsChannel.subscribe('tracing:fastify.request.handler:error', message => {\n    const { error, request, reply } = message as {\n      error: Error;\n      request: FastifyRequest & { opentelemetry?: () => { span?: Span } };\n      reply: FastifyReply;\n    };\n\n    handleFastifyError.call(handleFastifyError, error, request, reply, 'diagnostics-channel');\n  });\n\n  // Returning this as unknown not to deal with the internal types of the FastifyOtelInstrumentation\n  return fastifyOtelInstrumentationInstance as Instrumentation<InstrumentationConfig & FastifyIntegrationOptions>;\n});\n\nconst _fastifyIntegration = (({ shouldHandleError }: Partial<FastifyIntegrationOptions>) => {\n  let _shouldHandleError: (error: Error, request: FastifyRequest, reply: FastifyReply) => boolean;\n\n  return {\n    name: INTEGRATION_NAME,\n    setupOnce() {\n      _shouldHandleError = shouldHandleError || defaultShouldHandleError;\n\n      instrumentFastifyV3();\n      instrumentFastify();\n    },\n    getShouldHandleError() {\n      return _shouldHandleError;\n    },\n    setShouldHandleError(fn: (error: Error, request: FastifyRequest, reply: FastifyReply) => boolean): void {\n      _shouldHandleError = fn;\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Adds Sentry tracing instrumentation for [Fastify](https://fastify.dev/).\n *\n * If you also want to capture errors, you need to call `setupFastifyErrorHandler(app)` after you set up your Fastify server.\n *\n * For more information, see the [fastify documentation](https://docs.sentry.io/platforms/javascript/guides/fastify/).\n *\n * @example\n * ```javascript\n * const Sentry = require('@sentry/node');\n *\n * Sentry.init({\n *   integrations: [Sentry.fastifyIntegration()],\n * })\n * ```\n */\nexport const fastifyIntegration = defineIntegration((options: Partial<FastifyIntegrationOptions> = {}) =>\n  _fastifyIntegration(options),\n);\n\n/**\n * Default function to determine if an error should be sent to Sentry\n *\n * 3xx and 4xx errors are not sent by default.\n */\nfunction defaultShouldHandleError(_error: Error, _request: FastifyRequest, reply: FastifyReply): boolean {\n  const statusCode = reply.statusCode;\n  // 3xx and 4xx errors are not sent by default.\n  return statusCode >= 500 || statusCode <= 299;\n}\n\n/**\n * Add an Fastify error handler to capture errors to Sentry.\n *\n * @param fastify The Fastify instance to which to add the error handler\n * @param options Configuration options for the handler\n *\n * @example\n * ```javascript\n * const Sentry = require('@sentry/node');\n * const Fastify = require(\"fastify\");\n *\n * const app = Fastify();\n *\n * Sentry.setupFastifyErrorHandler(app);\n *\n * // Add your routes, etc.\n *\n * app.listen({ port: 3000 });\n * ```\n */\nexport function setupFastifyErrorHandler(fastify: FastifyInstance, options?: Partial<FastifyHandlerOptions>): void {\n  if (options?.shouldHandleError) {\n    getFastifyIntegration()?.setShouldHandleError(options.shouldHandleError);\n  }\n\n  const plugin = Object.assign(\n    function (fastify: FastifyInstance, _options: unknown, done: () => void): void {\n      fastify.addHook('onError', async (request, reply, error) => {\n        handleFastifyError.call(handleFastifyError, error, request, reply, 'onError-hook');\n      });\n      done();\n    },\n    {\n      [Symbol.for('skip-override')]: true,\n      [Symbol.for('fastify.display-name')]: 'sentry-fastify-error-handler',\n    },\n  );\n\n  // eslint-disable-next-line @typescript-eslint/no-floating-promises\n  fastify.register(plugin);\n}\n\nfunction addFastifySpanAttributes(span: Span): void {\n  const spanJSON = spanToJSON(span);\n  const spanName = spanJSON.description;\n  const attributes = spanJSON.data;\n\n  const type = attributes['fastify.type'];\n\n  const isHook = type === 'hook';\n  const isHandler = type === spanName?.startsWith('handler -');\n  // In @fastify/otel `request-handler` is separated by dash, not underscore\n  const isRequestHandler = spanName === 'request' || type === 'request-handler';\n\n  // If this is already set, or we have no fastify span, no need to process again...\n  if (attributes[SEMANTIC_ATTRIBUTE_SENTRY_OP] || (!isHandler && !isRequestHandler && !isHook)) {\n    return;\n  }\n\n  const opPrefix = isHook ? 'hook' : isHandler ? 'middleware' : isRequestHandler ? 'request-handler' : '<unknown>';\n\n  span.setAttributes({\n    [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.http.otel.fastify',\n    [SEMANTIC_ATTRIBUTE_SENTRY_OP]: `${opPrefix}.fastify`,\n  });\n\n  const attrName = attributes['fastify.name'] || attributes['plugin.name'] || attributes['hook.name'];\n  if (typeof attrName === 'string') {\n    // Try removing `fastify -> ` and `@fastify/otel -> ` prefixes\n    // This is a bit of a hack, and not always working for all spans\n    // But it's the best we can do without a proper API\n    const updatedName = attrName.replace(/^fastify -> /, '').replace(/^@fastify\\/otel -> /, '');\n\n    span.updateName(updatedName);\n  }\n}\n\nfunction instrumentClient(): void {\n  const client = getClient();\n  if (client) {\n    client.on('spanStart', (span: Span) => {\n      addFastifySpanAttributes(span);\n    });\n  }\n}\n\nfunction instrumentOnRequest(fastify: FastifyInstance): void {\n  fastify.addHook('onRequest', async (request: FastifyRequest & { opentelemetry?: () => { span?: Span } }, _reply) => {\n    if (request.opentelemetry) {\n      const { span } = request.opentelemetry();\n\n      if (span) {\n        addFastifySpanAttributes(span);\n      }\n    }\n\n    const routeName = request.routeOptions?.url;\n    const method = request.method || 'GET';\n\n    getIsolationScope().setTransactionName(`${method} ${routeName}`);\n  });\n}\n"], "names": ["generateInstrumentOnce", "FastifyInstrumentationV3", "getClient", "DEBUG_BUILD", "debug", "captureException", "FastifyOtelInstrumentation", "diagnosticsChannel", "defineIntegration", "spanToJSON", "SEMANTIC_ATTRIBUTE_SENTRY_OP", "SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN", "getIsolationScope"], "mappings": ";;;;;;;;;AAmBA;;;;;;;;;;;;;;;;;;;;;CAqBA,GAmDA,MAAM,gBAAA,GAAmB,SAAS;AAClC,MAAM,mBAAA,GAAsB,YAAY;AACxC,MAAM,mBAAA,GAAsB,YAAY;AAEjC,MAAM,mBAAA,GAAsBA,SAAAA,sBAAsB,CAAC,mBAAmB,EAAE,IAAM,IAAIC,gBAAAA,wBAAwB,EAAE;AAEnH,SAAS,qBAAqB,GAAuD;IACnF,MAAM,MAAA,GAASC,KAAAA,SAAS,EAAE;IAC1B,IAAI,CAAC,MAAM,EAAE;QACX,OAAO,SAAS;IACpB,OAAS;QACL,OAAO,MAAM,CAAC,oBAAoB,CAAC,gBAAgB,CAAA;IACvD;AACA;AAEA,SAAS,kBAAkB,CAIzB,KAAK,EACL,OAAO,EACP,KAAK,EACL,aAAa;IAEb,MAAM,iBAAA,GAAoB,qBAAqB,EAAE,EAAE,oBAAoB,EAAC,IAAK,wBAAwB;IACvG,oHAAA;IACE,IAAI,aAAA,KAAkB,qBAAqB,EAAE;QAC3C,IAAI,CAAC,wBAAA,GAA2B,IAAI;IACxC;IAEE,IAAI,IAAI,CAAC,wBAAA,IAA4B,aAAA,KAAkB,cAAc,EAAE;QACrEC,WAAAA,WAAA,IACEC,KAAAA,KAAK,CAAC,IAAI,CACR,uEAAuE,EACvE,+GAA+G;QAGvH,qFAAA;QACI;IACJ;IAEE,IAAI,iBAAiB,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,EAAE;QAC5CC,KAAAA,gBAAgB,CAAC,KAAK,EAAE;YAAE,SAAS,EAAE;gBAAE,OAAO,EAAE,KAAK;gBAAE,IAAI,EAAE,SAAA;YAAA,CAAU;QAAA,CAAG,CAAC;IAC/E;AACA;AAEO,MAAM,oBAAoBL,SAAAA,sBAAsB,CAAC,mBAAmB,EAAE,MAAM;IACjF,MAAM,kCAAA,GAAqC,IAAIM,MAAAA,0BAA0B,EAAE;IAC3E,MAAM,MAAA,GAAS,kCAAkC,CAAC,MAAM,EAAE;IAE5D,6DAAA;IACEC,EAAkB,CAAC,SAAS,CAAC,wBAAwB,GAAE,WAAW;QAChE,MAAM,eAAA,GAAkB,AAAC,OAAA,CAA0C,OAAO;QAE1E,eAAe,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,KAAK,EAAC,GAAA,IAAO;YAC7C,IAAI,GAAG,EAAE;gBACPJ,WAAAA,WAAA,IAAeC,KAAAA,KAAK,CAAC,KAAK,CAAC,yCAAyC,EAAE,GAAG,CAAC;YAClF,OAAa;gBACL,gBAAgB,EAAE;gBAElB,IAAI,eAAe,EAAE;oBACnB,mBAAmB,CAAC,eAAe,CAAC;gBAC9C;YACA;QACA,CAAK,CAAC;IACN,CAAG,CAAC;IAEJ,2DAAA;IACA,kEAAA;IACEG,EAAkB,CAAC,SAAS,CAAC,uCAAuC,GAAE,WAAW;QAC/E,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,KAAA,EAAM,GAAI;QAMlC,kBAAkB,CAAC,IAAI,CAAC,kBAAkB,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,qBAAqB,CAAC;IAC7F,CAAG,CAAC;IAEJ,kGAAA;IACE,OAAO,kCAAA;AACT,CAAC;AAED,MAAM,mBAAA,GAAuB,CAAC,EAAE,iBAAA,EAAmB,KAAyC;IAC1F,IAAI,kBAAkB;IAEtB,OAAO;QACL,IAAI,EAAE,gBAAgB;QACtB,SAAS,GAAG;YACV,kBAAA,GAAqB,iBAAA,IAAqB,wBAAwB;YAElE,mBAAmB,EAAE;YACrB,iBAAiB,EAAE;QACzB,CAAK;QACD,oBAAoB,GAAG;YACrB,OAAO,kBAAkB;QAC/B,CAAK;QACD,oBAAoB,EAAC,EAAE,EAAiF;YACtG,kBAAA,GAAqB,EAAE;QAC7B,CAAK;IACL,CAAG;AACH,CAAC,CAAA;AAED;;;;;;;;;;;;;;;CAeA,GACO,MAAM,kBAAA,GAAqBC,KAAAA,iBAAiB,CAAC,CAAC,OAAO,GAAuC,CAAA,CAAE,GACnG,mBAAmB,CAAC,OAAO,CAAC;AAG9B;;;;CAIA,GACA,SAAS,wBAAwB,CAAC,MAAM,EAAS,QAAQ,EAAkB,KAAK,EAAyB;IACvG,MAAM,UAAA,GAAa,KAAK,CAAC,UAAU;IACrC,8CAAA;IACE,OAAO,UAAA,IAAc,OAAO,UAAA,IAAc,GAAG;AAC/C;AAEA;;;;;;;;;;;;;;;;;;;CAmBA,GACO,SAAS,wBAAwB,CAAC,OAAO,EAAmB,OAAO,EAAyC;IACjH,IAAI,OAAO,EAAE,iBAAiB,EAAE;QAC9B,qBAAqB,EAAE,EAAE,oBAAoB,CAAC,OAAO,CAAC,iBAAiB,CAAC;IAC5E;IAEE,MAAM,MAAA,GAAS,MAAM,CAAC,MAAM,CAC1B,SAAU,OAAO,EAAmB,QAAQ,EAAW,IAAI,EAAoB;QAC7E,OAAO,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,OAAO,EAAE,KAAK,EAAE,KAAK,KAAK;YAC1D,kBAAkB,CAAC,IAAI,CAAC,kBAAkB,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,CAAC;QAC1F,CAAO,CAAC;QACF,IAAI,EAAE;IACZ,CAAK,EACD;QACE,CAAC,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,CAAA,EAAG,IAAI;QACnC,CAAC,MAAM,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAA,EAAG,8BAA8B;IAC1E,CAAK;IAGL,mEAAA;IACE,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC;AAC1B;AAEA,SAAS,wBAAwB,CAAC,IAAI,EAAc;IAClD,MAAM,QAAA,GAAWC,KAAAA,UAAU,CAAC,IAAI,CAAC;IACjC,MAAM,QAAA,GAAW,QAAQ,CAAC,WAAW;IACrC,MAAM,UAAA,GAAa,QAAQ,CAAC,IAAI;IAEhC,MAAM,IAAA,GAAO,UAAU,CAAC,cAAc,CAAC;IAEvC,MAAM,MAAA,GAAS,IAAA,KAAS,MAAM;IAC9B,MAAM,SAAA,GAAY,IAAA,KAAS,QAAQ,EAAE,UAAU,CAAC,WAAW,CAAC;IAC9D,0EAAA;IACE,MAAM,mBAAmB,QAAA,KAAa,SAAA,IAAa,IAAA,KAAS,iBAAiB;IAE/E,kFAAA;IACE,IAAI,UAAU,CAACC,KAAAA,4BAA4B,CAAA,IAAM,CAAC,SAAA,IAAa,CAAC,gBAAA,IAAoB,CAAC,MAAM,CAAC,CAAE;QAC5F;IACJ;IAEE,MAAM,QAAA,GAAW,MAAA,GAAS,MAAA,GAAS,SAAA,GAAY,eAAe,gBAAA,GAAmB,iBAAA,GAAoB,WAAW;IAEhH,IAAI,CAAC,aAAa,CAAC;QACjB,CAACC,KAAAA,gCAAgC,CAAA,EAAG,wBAAwB;QAC5D,CAACD,KAAAA,4BAA4B,CAAA,EAAG,CAAC,EAAA,QAAA,CAAA,QAAA,CAAA;IACA,CAAA,CAAA;IAEA,MAAA,QAAA,GAAA,UAAA,CAAA,cAAA,CAAA,IAAA,UAAA,CAAA,aAAA,CAAA,IAAA,UAAA,CAAA,WAAA,CAAA;IACA,IAAA,OAAA,QAAA,KAAA,QAAA,EAAA;QACA,8DAAA;QACA,gEAAA;QACA,mDAAA;QACA,MAAA,WAAA,GAAA,QAAA,CAAA,OAAA,CAAA,cAAA,EAAA,EAAA,CAAA,CAAA,OAAA,CAAA,qBAAA,EAAA,EAAA,CAAA;QAEA,IAAA,CAAA,UAAA,CAAA,WAAA,CAAA;IACA;AACA;AAEA,SAAA,gBAAA,GAAA;IACA,MAAA,MAAA,GAAAR,KAAAA,SAAA,EAAA;IACA,IAAA,MAAA,EAAA;QACA,MAAA,CAAA,EAAA,CAAA,WAAA,EAAA,CAAA,IAAA,KAAA;YACA,wBAAA,CAAA,IAAA,CAAA;QACA,CAAA,CAAA;IACA;AACA;AAEA,SAAA,mBAAA,CAAA,OAAA,EAAA;IACA,OAAA,CAAA,OAAA,CAAA,WAAA,EAAA,OAAA,OAAA,EAAA,MAAA,KAAA;QACA,IAAA,OAAA,CAAA,aAAA,EAAA;YACA,MAAA,EAAA,IAAA,EAAA,GAAA,OAAA,CAAA,aAAA,EAAA;YAEA,IAAA,IAAA,EAAA;gBACA,wBAAA,CAAA,IAAA,CAAA;YACA;QACA;QAEA,MAAA,SAAA,GAAA,OAAA,CAAA,YAAA,EAAA,GAAA;QACA,MAAA,MAAA,GAAA,OAAA,CAAA,MAAA,IAAA,KAAA;QAEAU,KAAAA,iBAAA,EAAA,CAAA,kBAAA,CAAA,CAAA,EAAA,MAAA,CAAA,CAAA,EAAA,SAAA,CAAA,CAAA,CAAA;IACA,CAAA,CAAA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2061, "column": 0}, "map": {"version": 3, "file": "graphql.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/integrations/tracing/graphql.ts"], "sourcesContent": ["import type { AttributeValue } from '@opentelemetry/api';\nimport { SpanStatusCode } from '@opentelemetry/api';\nimport { GraphQLInstrumentation } from '@opentelemetry/instrumentation-graphql';\nimport type { IntegrationFn } from '@sentry/core';\nimport { defineIntegration, getRootSpan, spanToJSON } from '@sentry/core';\nimport { addOriginToSpan, generateInstrumentOnce } from '@sentry/node-core';\nimport { SEMANTIC_ATTRIBUTE_SENTRY_GRAPHQL_OPERATION } from '@sentry/opentelemetry';\n\ninterface GraphqlOptions {\n  /**\n   * Do not create spans for resolvers.\n   *\n   * Defaults to true.\n   */\n  ignoreResolveSpans?: boolean;\n\n  /**\n   * Don't create spans for the execution of the default resolver on object properties.\n   *\n   * When a resolver function is not defined on the schema for a field, graphql will\n   * use the default resolver which just looks for a property with that name on the object.\n   * If the property is not a function, it's not very interesting to trace.\n   * This option can reduce noise and number of spans created.\n   *\n   * Defaults to true.\n   */\n  ignoreTrivialResolveSpans?: boolean;\n\n  /**\n   * If this is enabled, a http.server root span containing this span will automatically be renamed to include the operation name.\n   * Set this to `false` if you do not want this behavior, and want to keep the default http.server span name.\n   *\n   * Defaults to true.\n   */\n  useOperationNameForRootSpan?: boolean;\n}\n\nconst INTEGRATION_NAME = 'Graphql';\n\nexport const instrumentGraphql = generateInstrumentOnce(\n  INTEGRATION_NAME,\n  GraphQLInstrumentation,\n  (_options: GraphqlOptions) => {\n    const options = getOptionsWithDefaults(_options);\n\n    return {\n      ...options,\n      responseHook(span, result) {\n        addOriginToSpan(span, 'auto.graphql.otel.graphql');\n\n        // We want to ensure spans are marked as errored if there are errors in the result\n        // We only do that if the span is not already marked with a status\n        const resultWithMaybeError = result as { errors?: { message: string }[] };\n        if (resultWithMaybeError.errors?.length && !spanToJSON(span).status) {\n          span.setStatus({ code: SpanStatusCode.ERROR });\n        }\n\n        const attributes = spanToJSON(span).data;\n\n        // If operation.name is not set, we fall back to use operation.type only\n        const operationType = attributes['graphql.operation.type'];\n        const operationName = attributes['graphql.operation.name'];\n\n        if (options.useOperationNameForRootSpan && operationType) {\n          const rootSpan = getRootSpan(span);\n          const rootSpanAttributes = spanToJSON(rootSpan).data;\n\n          const existingOperations = rootSpanAttributes[SEMANTIC_ATTRIBUTE_SENTRY_GRAPHQL_OPERATION] || [];\n\n          const newOperation = operationName ? `${operationType} ${operationName}` : `${operationType}`;\n\n          // We keep track of each operation on the root span\n          // This can either be a string, or an array of strings (if there are multiple operations)\n          if (Array.isArray(existingOperations)) {\n            (existingOperations as string[]).push(newOperation);\n            rootSpan.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_GRAPHQL_OPERATION, existingOperations);\n          } else if (typeof existingOperations === 'string') {\n            rootSpan.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_GRAPHQL_OPERATION, [existingOperations, newOperation]);\n          } else {\n            rootSpan.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_GRAPHQL_OPERATION, newOperation);\n          }\n\n          if (!spanToJSON(rootSpan).data['original-description']) {\n            rootSpan.setAttribute('original-description', spanToJSON(rootSpan).description);\n          }\n          // Important for e.g. @sentry/aws-serverless because this would otherwise overwrite the name again\n          rootSpan.updateName(\n            `${spanToJSON(rootSpan).data['original-description']} (${getGraphqlOperationNamesFromAttribute(\n              existingOperations,\n            )})`,\n          );\n        }\n      },\n    };\n  },\n);\n\nconst _graphqlIntegration = ((options: GraphqlOptions = {}) => {\n  return {\n    name: INTEGRATION_NAME,\n    setupOnce() {\n      // We set defaults here, too, because otherwise we'd update the instrumentation config\n      // to the config without defaults, as `generateInstrumentOnce` automatically calls `setConfig(options)`\n      // when being called the second time\n      instrumentGraphql(getOptionsWithDefaults(options));\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Adds Sentry tracing instrumentation for the [graphql](https://www.npmjs.com/package/graphql) library.\n *\n * For more information, see the [`graphqlIntegration` documentation](https://docs.sentry.io/platforms/javascript/guides/node/configuration/integrations/graphql/).\n *\n * @param {GraphqlOptions} options Configuration options for the GraphQL integration.\n *\n * @example\n * ```javascript\n * const Sentry = require('@sentry/node');\n *\n * Sentry.init({\n *  integrations: [Sentry.graphqlIntegration()],\n * });\n */\nexport const graphqlIntegration = defineIntegration(_graphqlIntegration);\n\nfunction getOptionsWithDefaults(options?: GraphqlOptions): GraphqlOptions {\n  return {\n    ignoreResolveSpans: true,\n    ignoreTrivialResolveSpans: true,\n    useOperationNameForRootSpan: true,\n    ...options,\n  };\n}\n\n// copy from packages/opentelemetry/utils\nfunction getGraphqlOperationNamesFromAttribute(attr: AttributeValue): string {\n  if (Array.isArray(attr)) {\n    const sorted = attr.slice().sort();\n\n    // Up to 5 items, we just add all of them\n    if (sorted.length <= 5) {\n      return sorted.join(', ');\n    } else {\n      // Else, we add the first 5 and the diff of other operations\n      return `${sorted.slice(0, 5).join(', ')}, +${sorted.length - 5}`;\n    }\n  }\n\n  return `${attr}`;\n}\n"], "names": ["generateInstrumentOnce", "GraphQLInstrumentation", "addOriginToSpan", "spanToJSON", "SpanStatusCode", "getRootSpan", "SEMANTIC_ATTRIBUTE_SENTRY_GRAPHQL_OPERATION", "defineIntegration"], "mappings": ";;;;;;;;AAqCA,MAAM,gBAAA,GAAmB,SAAS;AAE3B,MAAM,iBAAA,GAAoBA,SAAAA,sBAAsB,CACrD,gBAAgB,EAChBC,uBAAAA,sBAAsB,EACtB,CAAC,QAAQ,KAAqB;IAC5B,MAAM,OAAA,GAAU,sBAAsB,CAAC,QAAQ,CAAC;IAEhD,OAAO;QACL,GAAG,OAAO;QACV,YAAY,EAAC,IAAI,EAAE,MAAM,EAAE;YACzBC,SAAAA,eAAe,CAAC,IAAI,EAAE,2BAA2B,CAAC;YAE1D,kFAAA;YACA,kEAAA;YACQ,MAAM,oBAAA,GAAuB,MAAA;YAC7B,IAAI,oBAAoB,CAAC,MAAM,EAAE,MAAA,IAAU,CAACC,KAAAA,UAAU,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE;gBACnE,IAAI,CAAC,SAAS,CAAC;oBAAE,IAAI,EAAEC,IAAAA,cAAc,CAAC,KAAA;gBAAA,CAAO,CAAC;YACxD;YAEQ,MAAM,aAAaD,KAAAA,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI;YAEhD,wEAAA;YACQ,MAAM,aAAA,GAAgB,UAAU,CAAC,wBAAwB,CAAC;YAC1D,MAAM,aAAA,GAAgB,UAAU,CAAC,wBAAwB,CAAC;YAE1D,IAAI,OAAO,CAAC,2BAAA,IAA+B,aAAa,EAAE;gBACxD,MAAM,QAAA,GAAWE,KAAAA,WAAW,CAAC,IAAI,CAAC;gBAClC,MAAM,qBAAqBF,KAAAA,UAAU,CAAC,QAAQ,CAAC,CAAC,IAAI;gBAEpD,MAAM,qBAAqB,kBAAkB,CAACG,cAAAA,2CAA2C,CAAA,IAAK,EAAE;gBAEhG,MAAM,YAAA,GAAe,aAAA,GAAgB,CAAC,EAAA,aAAA,CAAA,CAAA,EAAA,aAAA,CAAA,CAAA,GAAA,CAAA,EAAA,aAAA,CAAA,CAAA;gBAEA,mDAAA;gBACA,yFAAA;gBACA,IAAA,KAAA,CAAA,OAAA,CAAA,kBAAA,CAAA,EAAA;oBACA,kBAAA,CAAA,IAAA,CAAA,YAAA,CAAA;oBACA,QAAA,CAAA,YAAA,CAAAA,cAAAA,2CAAA,EAAA,kBAAA,CAAA;gBACA,CAAA,MAAA,IAAA,OAAA,kBAAA,KAAA,QAAA,EAAA;oBACA,QAAA,CAAA,YAAA,CAAAA,cAAAA,2CAAA,EAAA;wBAAA,kBAAA;wBAAA,YAAA;qBAAA,CAAA;gBACA,CAAA,MAAA;oBACA,QAAA,CAAA,YAAA,CAAAA,cAAAA,2CAAA,EAAA,YAAA,CAAA;gBACA;gBAEA,IAAA,CAAAH,KAAAA,UAAA,CAAA,QAAA,CAAA,CAAA,IAAA,CAAA,sBAAA,CAAA,EAAA;oBACA,QAAA,CAAA,YAAA,CAAA,sBAAA,EAAAA,KAAAA,UAAA,CAAA,QAAA,CAAA,CAAA,WAAA,CAAA;gBACA;gBACA,kGAAA;gBACA,QAAA,CAAA,UAAA,CACA,CAAA,EAAAA,KAAAA,UAAA,CAAA,QAAA,CAAA,CAAA,IAAA,CAAA,sBAAA,CAAA,CAAA,EAAA,EAAA,qCAAA,CACA,kBAAA,EACA,CAAA,CAAA;YAEA;QACA,CAAA;IACA,CAAA;AACA,CAAA;AAGA,MAAA,mBAAA,GAAA,CAAA,OAAA,GAAA,CAAA,CAAA,KAAA;IACA,OAAA;QACA,IAAA,EAAA,gBAAA;QACA,SAAA,GAAA;YACA,sFAAA;YACA,uGAAA;YACA,oCAAA;YACA,iBAAA,CAAA,sBAAA,CAAA,OAAA,CAAA,CAAA;QACA,CAAA;IACA,CAAA;AACA,CAAA,CAAA;AAEA;;;;;;;;;;;;;;CAcA,GACA,MAAA,kBAAA,GAAAI,KAAAA,iBAAA,CAAA,mBAAA;AAEA,SAAA,sBAAA,CAAA,OAAA,EAAA;IACA,OAAA;QACA,kBAAA,EAAA,IAAA;QACA,yBAAA,EAAA,IAAA;QACA,2BAAA,EAAA,IAAA;QACA,GAAA,OAAA;IACA,CAAA;AACA;AAEA,yCAAA;AACA,SAAA,qCAAA,CAAA,IAAA,EAAA;IACA,IAAA,KAAA,CAAA,OAAA,CAAA,IAAA,CAAA,EAAA;QACA,MAAA,MAAA,GAAA,IAAA,CAAA,KAAA,EAAA,CAAA,IAAA,EAAA;QAEA,yCAAA;QACA,IAAA,MAAA,CAAA,MAAA,IAAA,CAAA,EAAA;YACA,OAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA;QACA,CAAA,MAAA;YACA,4DAAA;YACA,OAAA,CAAA,EAAA,MAAA,CAAA,KAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,GAAA,EAAA,MAAA,CAAA,MAAA,GAAA,CAAA,CAAA,CAAA;QACA;IACA;IAEA,OAAA,CAAA,EAAA,IAAA,CAAA,CAAA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2170, "column": 0}, "map": {"version": 3, "file": "kafka.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/integrations/tracing/kafka.ts"], "sourcesContent": ["import { KafkaJsInstrumentation } from '@opentelemetry/instrumentation-kafkajs';\nimport type { IntegrationFn } from '@sentry/core';\nimport { defineIntegration } from '@sentry/core';\nimport { addOriginToSpan, generateInstrumentOnce } from '@sentry/node-core';\n\nconst INTEGRATION_NAME = 'Kafka';\n\nexport const instrumentKafka = generateInstrumentOnce(\n  INTEGRATION_NAME,\n  () =>\n    new KafkaJsInstrumentation({\n      consumerHook(span) {\n        addOriginToSpan(span, 'auto.kafkajs.otel.consumer');\n      },\n      producerHook(span) {\n        addOriginToSpan(span, 'auto.kafkajs.otel.producer');\n      },\n    }),\n);\n\nconst _kafkaIntegration = (() => {\n  return {\n    name: INTEGRATION_NAME,\n    setupOnce() {\n      instrumentKafka();\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Adds Sentry tracing instrumentation for the [kafkajs](https://www.npmjs.com/package/kafkajs) library.\n *\n * For more information, see the [`kafkaIntegration` documentation](https://docs.sentry.io/platforms/javascript/guides/node/configuration/integrations/kafka/).\n *\n * @example\n * ```javascript\n * const Sentry = require('@sentry/node');\n *\n * Sentry.init({\n *  integrations: [Sentry.kafkaIntegration()],\n * });\n */\nexport const kafkaIntegration = defineIntegration(_kafkaIntegration);\n"], "names": ["generateInstrumentOnce", "KafkaJsInstrumentation", "addOriginToSpan", "defineIntegration"], "mappings": ";;;;;;AAKA,MAAM,gBAAA,GAAmB,OAAO;AAEzB,MAAM,eAAA,GAAkBA,SAAAA,sBAAsB,CACnD,gBAAgB,EAChB,IACE,IAAIC,uBAAAA,sBAAsB,CAAC;QACzB,YAAY,EAAC,IAAI,EAAE;YACjBC,SAAAA,eAAe,CAAC,IAAI,EAAE,4BAA4B,CAAC;QAC3D,CAAO;QACD,YAAY,EAAC,IAAI,EAAE;YACjBA,SAAAA,eAAe,CAAC,IAAI,EAAE,4BAA4B,CAAC;QAC3D,CAAO;IACP,CAAK,CAAC;AAGN,MAAM,iBAAA,GAAqB,MAAM;IAC/B,OAAO;QACL,IAAI,EAAE,gBAAgB;QACtB,SAAS,GAAG;YACV,eAAe,EAAE;QACvB,CAAK;IACL,CAAG;AACH,CAAC,CAAA;AAED;;;;;;;;;;;;CAYA,SACa,gBAAA,GAAmBC,KAAAA,iBAAiB,CAAC,iBAAiB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2213, "column": 0}, "map": {"version": 3, "file": "lrumemoizer.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/integrations/tracing/lrumemoizer.ts"], "sourcesContent": ["import { LruMemoizerInstrumentation } from '@opentelemetry/instrumentation-lru-memoizer';\nimport type { IntegrationFn } from '@sentry/core';\nimport { defineIntegration } from '@sentry/core';\nimport { generateInstrumentOnce } from '@sentry/node-core';\n\nconst INTEGRATION_NAME = 'LruMemoizer';\n\nexport const instrumentLruMemoizer = generateInstrumentOnce(INTEGRATION_NAME, () => new LruMemoizerInstrumentation());\n\nconst _lruMemoizerIntegration = (() => {\n  return {\n    name: INTEGRATION_NAME,\n    setupOnce() {\n      instrumentLruMemoizer();\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Adds Sentry tracing instrumentation for the [lru-memoizer](https://www.npmjs.com/package/lru-memoizer) library.\n *\n * For more information, see the [`lruMemoizerIntegration` documentation](https://docs.sentry.io/platforms/javascript/guides/node/configuration/integrations/lrumemoizer/).\n *\n * @example\n * ```javascript\n * const Sentry = require('@sentry/node');\n *\n * Sentry.init({\n *  integrations: [Sentry.lruMemoizerIntegration()],\n * });\n */\nexport const lruMemoizerIntegration = defineIntegration(_lruMemoizerIntegration);\n"], "names": ["generateInstrumentOnce", "LruMemoizerInstrumentation", "defineIntegration"], "mappings": ";;;;;;AAKA,MAAM,gBAAA,GAAmB,aAAa;AAE/B,MAAM,qBAAA,GAAwBA,SAAAA,sBAAsB,CAAC,gBAAgB,EAAE,IAAM,IAAIC,2BAAAA,0BAA0B,EAAE;AAEpH,MAAM,uBAAA,GAA2B,MAAM;IACrC,OAAO;QACL,IAAI,EAAE,gBAAgB;QACtB,SAAS,GAAG;YACV,qBAAqB,EAAE;QAC7B,CAAK;IACL,CAAG;AACH,CAAC,CAAA;AAED;;;;;;;;;;;;CAYA,SACa,sBAAA,GAAyBC,KAAAA,iBAAiB,CAAC,uBAAuB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2249, "column": 0}, "map": {"version": 3, "file": "mongo.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/integrations/tracing/mongo.ts"], "sourcesContent": ["import { MongoDBInstrumentation } from '@opentelemetry/instrumentation-mongodb';\nimport type { IntegrationFn } from '@sentry/core';\nimport { defineIntegration } from '@sentry/core';\nimport { addOriginToSpan, generateInstrumentOnce } from '@sentry/node-core';\n\nconst INTEGRATION_NAME = 'Mongo';\n\nexport const instrumentMongo = generateInstrumentOnce(\n  INTEGRATION_NAME,\n  () =>\n    new MongoDBInstrumentation({\n      dbStatementSerializer: _defaultDbStatementSerializer,\n      responseHook(span) {\n        addOriginToSpan(span, 'auto.db.otel.mongo');\n      },\n    }),\n);\n\n/**\n * Replaces values in document with '?', hiding PII and helping grouping.\n */\nexport function _defaultDbStatementSerializer(commandObj: Record<string, unknown>): string {\n  const resultObj = _scrubStatement(commandObj);\n  return JSON.stringify(resultObj);\n}\n\nfunction _scrubStatement(value: unknown): unknown {\n  if (Array.isArray(value)) {\n    return value.map(element => _scrubStatement(element));\n  }\n\n  if (isCommandObj(value)) {\n    const initial: Record<string, unknown> = {};\n    return Object.entries(value)\n      .map(([key, element]) => [key, _scrubStatement(element)])\n      .reduce((prev, current) => {\n        if (isCommandEntry(current)) {\n          prev[current[0]] = current[1];\n        }\n        return prev;\n      }, initial);\n  }\n\n  // A value like string or number, possible contains PII, scrub it\n  return '?';\n}\n\nfunction isCommandObj(value: Record<string, unknown> | unknown): value is Record<string, unknown> {\n  return typeof value === 'object' && value !== null && !isBuffer(value);\n}\n\nfunction isBuffer(value: unknown): boolean {\n  let isBuffer = false;\n  if (typeof Buffer !== 'undefined') {\n    isBuffer = Buffer.isBuffer(value);\n  }\n  return isBuffer;\n}\n\nfunction isCommandEntry(value: [string, unknown] | unknown): value is [string, unknown] {\n  return Array.isArray(value);\n}\n\nconst _mongoIntegration = (() => {\n  return {\n    name: INTEGRATION_NAME,\n    setupOnce() {\n      instrumentMongo();\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Adds Sentry tracing instrumentation for the [mongodb](https://www.npmjs.com/package/mongodb) library.\n *\n * For more information, see the [`mongoIntegration` documentation](https://docs.sentry.io/platforms/javascript/guides/node/configuration/integrations/mongo/).\n *\n * @example\n * ```javascript\n * const Sentry = require('@sentry/node');\n *\n * Sentry.init({\n *  integrations: [Sentry.mongoIntegration()],\n * });\n * ```\n */\nexport const mongoIntegration = defineIntegration(_mongoIntegration);\n"], "names": ["generateInstrumentOnce", "MongoDBInstrumentation", "addOriginToSpan", "defineIntegration"], "mappings": ";;;;;;AAKA,MAAM,gBAAA,GAAmB,OAAO;AAEzB,MAAM,eAAA,GAAkBA,SAAAA,sBAAsB,CACnD,gBAAgB,EAChB,IACE,IAAIC,uBAAAA,sBAAsB,CAAC;QACzB,qBAAqB,EAAE,6BAA6B;QACpD,YAAY,EAAC,IAAI,EAAE;YACjBC,SAAAA,eAAe,CAAC,IAAI,EAAE,oBAAoB,CAAC;QACnD,CAAO;IACP,CAAK,CAAC;AAGN;;CAEA,GACO,SAAS,6BAA6B,CAAC,UAAU,EAAmC;IACzF,MAAM,SAAA,GAAY,eAAe,CAAC,UAAU,CAAC;IAC7C,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;AAClC;AAEA,SAAS,eAAe,CAAC,KAAK,EAAoB;IAChD,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;QACxB,OAAO,KAAK,CAAC,GAAG,EAAC,OAAA,GAAW,eAAe,CAAC,OAAO,CAAC,CAAC;IACzD;IAEE,IAAI,YAAY,CAAC,KAAK,CAAC,EAAE;QACvB,MAAM,OAAO,GAA4B,CAAA,CAAE;QAC3C,OAAO,MAAM,CAAC,OAAO,CAAC,KAAK,EACxB,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,OAAO,CAAC,GAAK;gBAAC,GAAG;gBAAE,eAAe,CAAC,OAAO,CAAC;aAAC,EACvD,MAAM,CAAC,CAAC,IAAI,EAAE,OAAO,KAAK;YACzB,IAAI,cAAc,CAAC,OAAO,CAAC,EAAE;gBAC3B,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA,GAAI,OAAO,CAAC,CAAC,CAAC;YACvC;YACQ,OAAO,IAAI;QACnB,CAAO,EAAE,OAAO,CAAC;IACjB;IAEA,iEAAA;IACE,OAAO,GAAG;AACZ;AAEA,SAAS,YAAY,CAAC,KAAK,EAAuE;IAChG,OAAO,OAAO,KAAA,KAAU,YAAY,KAAA,KAAU,IAAA,IAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC;AACxE;AAEA,SAAS,QAAQ,CAAC,KAAK,EAAoB;IACzC,IAAI,QAAA,GAAW,KAAK;IACpB,IAAI,OAAO,MAAA,KAAW,WAAW,EAAE;QACjC,WAAW,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC;IACrC;IACE,OAAO,QAAQ;AACjB;AAEA,SAAS,cAAc,CAAC,KAAK,EAA2D;IACtF,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;AAC7B;AAEA,MAAM,iBAAA,GAAqB,MAAM;IAC/B,OAAO;QACL,IAAI,EAAE,gBAAgB;QACtB,SAAS,GAAG;YACV,eAAe,EAAE;QACvB,CAAK;IACL,CAAG;AACH,CAAC,CAAA;AAED;;;;;;;;;;;;;CAaA,SACa,gBAAA,GAAmBC,KAAAA,iBAAiB,CAAC,iBAAiB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2330, "column": 0}, "map": {"version": 3, "file": "mongoose.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/integrations/tracing/mongoose.ts"], "sourcesContent": ["import { MongooseInstrumentation } from '@opentelemetry/instrumentation-mongoose';\nimport type { IntegrationFn } from '@sentry/core';\nimport { defineIntegration } from '@sentry/core';\nimport { addOriginToSpan, generateInstrumentOnce } from '@sentry/node-core';\n\nconst INTEGRATION_NAME = 'Mongoose';\n\nexport const instrumentMongoose = generateInstrumentOnce(\n  INTEGRATION_NAME,\n  () =>\n    new MongooseInstrumentation({\n      responseHook(span) {\n        addOriginToSpan(span, 'auto.db.otel.mongoose');\n      },\n    }),\n);\n\nconst _mongooseIntegration = (() => {\n  return {\n    name: INTEGRATION_NAME,\n    setupOnce() {\n      instrumentMongoose();\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Adds Sentry tracing instrumentation for the [mongoose](https://www.npmjs.com/package/mongoose) library.\n *\n * For more information, see the [`mongooseIntegration` documentation](https://docs.sentry.io/platforms/javascript/guides/node/configuration/integrations/mongoose/).\n *\n * @example\n * ```javascript\n * const Sentry = require('@sentry/node');\n *\n * Sentry.init({\n *  integrations: [Sentry.mongooseIntegration()],\n * });\n * ```\n */\nexport const mongooseIntegration = defineIntegration(_mongooseIntegration);\n"], "names": ["generateInstrumentOnce", "MongooseInstrumentation", "addOriginToSpan", "defineIntegration"], "mappings": ";;;;;;AAKA,MAAM,gBAAA,GAAmB,UAAU;AAE5B,MAAM,kBAAA,GAAqBA,SAAAA,sBAAsB,CACtD,gBAAgB,EAChB,IACE,IAAIC,wBAAAA,uBAAuB,CAAC;QAC1B,YAAY,EAAC,IAAI,EAAE;YACjBC,SAAAA,eAAe,CAAC,IAAI,EAAE,uBAAuB,CAAC;QACtD,CAAO;IACP,CAAK,CAAC;AAGN,MAAM,oBAAA,GAAwB,MAAM;IAClC,OAAO;QACL,IAAI,EAAE,gBAAgB;QACtB,SAAS,GAAG;YACV,kBAAkB,EAAE;QAC1B,CAAK;IACL,CAAG;AACH,CAAC,CAAA;AAED;;;;;;;;;;;;;CAaA,SACa,mBAAA,GAAsBC,KAAAA,iBAAiB,CAAC,oBAAoB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2371, "column": 0}, "map": {"version": 3, "file": "mysql.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/integrations/tracing/mysql.ts"], "sourcesContent": ["import { MySQLInstrumentation } from '@opentelemetry/instrumentation-mysql';\nimport type { IntegrationFn } from '@sentry/core';\nimport { defineIntegration } from '@sentry/core';\nimport { generateInstrumentOnce } from '@sentry/node-core';\n\nconst INTEGRATION_NAME = 'Mysql';\n\nexport const instrumentMysql = generateInstrumentOnce(INTEGRATION_NAME, () => new MySQLInstrumentation({}));\n\nconst _mysqlIntegration = (() => {\n  return {\n    name: INTEGRATION_NAME,\n    setupOnce() {\n      instrumentMysql();\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Adds Sentry tracing instrumentation for the [mysql](https://www.npmjs.com/package/mysql) library.\n *\n * For more information, see the [`mysqlIntegration` documentation](https://docs.sentry.io/platforms/javascript/guides/node/configuration/integrations/mysql/).\n *\n * @example\n * ```javascript\n * const Sentry = require('@sentry/node');\n *\n * Sentry.init({\n *  integrations: [Sentry.mysqlIntegration()],\n * });\n * ```\n */\nexport const mysqlIntegration = defineIntegration(_mysqlIntegration);\n"], "names": ["generateInstrumentOnce", "MySQLInstrumentation", "defineIntegration"], "mappings": ";;;;;;AAKA,MAAM,gBAAA,GAAmB,OAAO;MAEnB,eAAA,GAAkBA,SAAAA,sBAAsB,CAAC,gBAAgB,EAAE,IAAM,IAAIC,qBAAAA,oBAAoB,CAAC,CAAA,CAAE,CAAC;AAE1G,MAAM,iBAAA,GAAqB,MAAM;IAC/B,OAAO;QACL,IAAI,EAAE,gBAAgB;QACtB,SAAS,GAAG;YACV,eAAe,EAAE;QACvB,CAAK;IACL,CAAG;AACH,CAAC,CAAA;AAED;;;;;;;;;;;;;CAaA,SACa,gBAAA,GAAmBC,KAAAA,iBAAiB,CAAC,iBAAiB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2408, "column": 0}, "map": {"version": 3, "file": "mysql2.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/integrations/tracing/mysql2.ts"], "sourcesContent": ["import { MySQL2Instrumentation } from '@opentelemetry/instrumentation-mysql2';\nimport type { IntegrationFn } from '@sentry/core';\nimport { defineIntegration } from '@sentry/core';\nimport { addOriginToSpan, generateInstrumentOnce } from '@sentry/node-core';\n\nconst INTEGRATION_NAME = 'Mysql2';\n\nexport const instrumentMysql2 = generateInstrumentOnce(\n  INTEGRATION_NAME,\n  () =>\n    new MySQL2Instrumentation({\n      responseHook(span) {\n        addOriginToSpan(span, 'auto.db.otel.mysql2');\n      },\n    }),\n);\n\nconst _mysql2Integration = (() => {\n  return {\n    name: INTEGRATION_NAME,\n    setupOnce() {\n      instrumentMysql2();\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Adds Sentry tracing instrumentation for the [mysql2](https://www.npmjs.com/package/mysql2) library.\n *\n * For more information, see the [`mysql2Integration` documentation](https://docs.sentry.io/platforms/javascript/guides/node/configuration/integrations/mysql2/).\n *\n * @example\n * ```javascript\n * const Sentry = require('@sentry/node');\n *\n * Sentry.init({\n *  integrations: [Sentry.mysqlIntegration()],\n * });\n * ```\n */\nexport const mysql2Integration = defineIntegration(_mysql2Integration);\n"], "names": ["generateInstrumentOnce", "MySQL2Instrumentation", "addOriginToSpan", "defineIntegration"], "mappings": ";;;;;;AAKA,MAAM,gBAAA,GAAmB,QAAQ;AAE1B,MAAM,gBAAA,GAAmBA,SAAAA,sBAAsB,CACpD,gBAAgB,EAChB,IACE,IAAIC,sBAAAA,qBAAqB,CAAC;QACxB,YAAY,EAAC,IAAI,EAAE;YACjBC,SAAAA,eAAe,CAAC,IAAI,EAAE,qBAAqB,CAAC;QACpD,CAAO;IACP,CAAK,CAAC;AAGN,MAAM,kBAAA,GAAsB,MAAM;IAChC,OAAO;QACL,IAAI,EAAE,gBAAgB;QACtB,SAAS,GAAG;YACV,gBAAgB,EAAE;QACxB,CAAK;IACL,CAAG;AACH,CAAC,CAAA;AAED;;;;;;;;;;;;;CAaA,SACa,iBAAA,GAAoBC,KAAAA,iBAAiB,CAAC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2449, "column": 0}, "map": {"version": 3, "file": "redisCache.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/utils/redisCache.ts"], "sourcesContent": ["import type { CommandArgs as IORedisCommandArgs } from '@opentelemetry/instrumentation-ioredis';\n\nconst SINGLE_ARG_COMMANDS = ['get', 'set', 'setex'];\n\nexport const GET_COMMANDS = ['get', 'mget'];\nexport const SET_COMMANDS = ['set', 'setex'];\n// todo: del, expire\n\n/** Checks if a given command is in the list of redis commands.\n *  Useful because commands can come in lowercase or uppercase (depending on the library). */\nexport function isInCommands(redisCommands: string[], command: string): boolean {\n  return redisCommands.includes(command.toLowerCase());\n}\n\n/** Determine cache operation based on redis statement */\nexport function getCacheOperation(\n  command: string,\n): 'cache.get' | 'cache.put' | 'cache.remove' | 'cache.flush' | undefined {\n  if (isInCommands(GET_COMMANDS, command)) {\n    return 'cache.get';\n  } else if (isInCommands(SET_COMMANDS, command)) {\n    return 'cache.put';\n  } else {\n    return undefined;\n  }\n}\n\nfunction keyHasPrefix(key: string, prefixes: string[]): boolean {\n  return prefixes.some(prefix => key.startsWith(prefix));\n}\n\n/** Safely converts a redis key to a string (comma-separated if there are multiple keys) */\nexport function getCacheKeySafely(redisCommand: string, cmdArgs: IORedisCommandArgs): string[] | undefined {\n  try {\n    if (cmdArgs.length === 0) {\n      return undefined;\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    const processArg = (arg: string | Buffer | number | any[]): string[] => {\n      if (typeof arg === 'string' || typeof arg === 'number' || Buffer.isBuffer(arg)) {\n        return [arg.toString()];\n      } else if (Array.isArray(arg)) {\n        return flatten(arg.map(arg => processArg(arg)));\n      } else {\n        return ['<unknown>'];\n      }\n    };\n\n    const firstArg = cmdArgs[0];\n    if (isInCommands(SINGLE_ARG_COMMANDS, redisCommand) && firstArg != null) {\n      return processArg(firstArg);\n    }\n\n    return flatten(cmdArgs.map(arg => processArg(arg)));\n  } catch {\n    return undefined;\n  }\n}\n\n/** Determines whether a redis operation should be considered as \"cache operation\" by checking if a key is prefixed.\n *  We only support certain commands (such as 'set', 'get', 'mget'). */\nexport function shouldConsiderForCache(redisCommand: string, keys: string[], prefixes: string[]): boolean {\n  if (!getCacheOperation(redisCommand)) {\n    return false;\n  }\n\n  for (const key of keys) {\n    if (keyHasPrefix(key, prefixes)) {\n      return true;\n    }\n  }\n  return false;\n}\n\n/** Calculates size based on the cache response value */\nexport function calculateCacheItemSize(response: unknown): number | undefined {\n  const getSize = (value: unknown): number | undefined => {\n    try {\n      if (Buffer.isBuffer(value)) return value.byteLength;\n      else if (typeof value === 'string') return value.length;\n      else if (typeof value === 'number') return value.toString().length;\n      else if (value === null || value === undefined) return 0;\n      return JSON.stringify(value).length;\n    } catch {\n      return undefined;\n    }\n  };\n\n  return Array.isArray(response)\n    ? response.reduce((acc: number | undefined, curr) => {\n        const size = getSize(curr);\n        return typeof size === 'number' ? (acc !== undefined ? acc + size : size) : acc;\n      }, 0)\n    : getSize(response);\n}\n\ntype NestedArray<T> = Array<NestedArray<T> | T>;\n\nfunction flatten<T>(input: NestedArray<T>): T[] {\n  const result: T[] = [];\n\n  const flattenHelper = (input: NestedArray<T>): void => {\n    input.forEach((el: T | NestedArray<T>) => {\n      if (Array.isArray(el)) {\n        flattenHelper(el as NestedArray<T>);\n      } else {\n        result.push(el as T);\n      }\n    });\n  };\n\n  flattenHelper(input);\n  return result;\n}\n"], "names": [], "mappings": ";;;AAEA,MAAM,mBAAA,GAAsB;IAAC,KAAK;IAAE,KAAK;IAAE,OAAO;CAAC;MAEtC,YAAA,GAAe;IAAC,KAAK;IAAE,MAAM;CAAA;MAC7B,YAAA,GAAe;IAAC,KAAK;IAAE,OAAO;CAAA;AAC3C,oBAAA;AAEA;2FACA,GACO,SAAS,YAAY,CAAC,aAAa,EAAY,OAAO,EAAmB;IAC9E,OAAO,aAAa,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;AACtD;AAEA,uDAAA,GACO,SAAS,iBAAiB,CAC/B,OAAO;IAEP,IAAI,YAAY,CAAC,YAAY,EAAE,OAAO,CAAC,EAAE;QACvC,OAAO,WAAW;IACtB,CAAE,MAAO,IAAI,YAAY,CAAC,YAAY,EAAE,OAAO,CAAC,EAAE;QAC9C,OAAO,WAAW;IACtB,OAAS;QACL,OAAO,SAAS;IACpB;AACA;AAEA,SAAS,YAAY,CAAC,GAAG,EAAU,QAAQ,EAAqB;IAC9D,OAAO,QAAQ,CAAC,IAAI,EAAC,MAAA,GAAU,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;AACxD;AAEA,yFAAA,GACO,SAAS,iBAAiB,CAAC,YAAY,EAAU,OAAO,EAA4C;IACzG,IAAI;QACF,IAAI,OAAO,CAAC,MAAA,KAAW,CAAC,EAAE;YACxB,OAAO,SAAS;QACtB;QAEA,8DAAA;QACI,MAAM,UAAA,GAAa,CAAC,GAAG,KAAiD;YACtE,IAAI,OAAO,QAAQ,QAAA,IAAY,OAAO,GAAA,KAAQ,QAAA,IAAY,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;gBAC9E,OAAO;oBAAC,GAAG,CAAC,QAAQ,EAAE;iBAAC;YAC/B,CAAM,MAAO,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gBAC7B,OAAO,OAAO,CAAC,GAAG,CAAC,GAAG,EAAC,GAAA,GAAO,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;YACvD,OAAa;gBACL,OAAO;oBAAC,WAAW;iBAAC;YAC5B;QACA,CAAK;QAED,MAAM,QAAA,GAAW,OAAO,CAAC,CAAC,CAAC;QAC3B,IAAI,YAAY,CAAC,mBAAmB,EAAE,YAAY,CAAA,IAAK,QAAA,IAAY,IAAI,EAAE;YACvE,OAAO,UAAU,CAAC,QAAQ,CAAC;QACjC;QAEI,OAAO,OAAO,CAAC,OAAO,CAAC,GAAG,EAAC,GAAA,GAAO,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;IACvD,EAAI,OAAM;QACN,OAAO,SAAS;IACpB;AACA;AAEA;qEACA,GACO,SAAS,sBAAsB,CAAC,YAAY,EAAU,IAAI,EAAY,QAAQ,EAAqB;IACxG,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,EAAE;QACpC,OAAO,KAAK;IAChB;IAEE,KAAK,MAAM,GAAA,IAAO,IAAI,CAAE;QACtB,IAAI,YAAY,CAAC,GAAG,EAAE,QAAQ,CAAC,EAAE;YAC/B,OAAO,IAAI;QACjB;IACA;IACE,OAAO,KAAK;AACd;AAEA,sDAAA,GACO,SAAS,sBAAsB,CAAC,QAAQ,EAA+B;IAC5E,MAAM,OAAA,GAAU,CAAC,KAAK,KAAkC;QACtD,IAAI;YACF,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK,CAAC,UAAU;iBAC9C,IAAI,OAAO,KAAA,KAAU,QAAQ,EAAE,OAAO,KAAK,CAAC,MAAM;iBAClD,IAAI,OAAO,UAAU,QAAQ,EAAE,OAAO,KAAK,CAAC,QAAQ,EAAE,CAAC,MAAM;iBAC7D,IAAI,KAAA,KAAU,IAAA,IAAQ,KAAA,KAAU,SAAS,EAAE,OAAO,CAAC;YACxD,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,MAAM;QACzC,EAAM,OAAM;YACN,OAAO,SAAS;QACtB;IACA,CAAG;IAED,OAAO,KAAK,CAAC,OAAO,CAAC,QAAQ,IACzB,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAsB,IAAI,KAAK;QACjD,MAAM,IAAA,GAAO,OAAO,CAAC,IAAI,CAAC;QAC1B,OAAO,OAAO,IAAA,KAAS,QAAA,GAAY,GAAA,KAAQ,SAAA,GAAY,MAAM,IAAA,GAAO,IAAI,GAAI,GAAG;IACvF,CAAO,EAAE,CAAC,IACJ,OAAO,CAAC,QAAQ,CAAC;AACvB;AAIA,SAAS,OAAO,CAAI,KAAK,EAAuB;IAC9C,MAAM,MAAM,GAAQ,EAAE;IAEtB,MAAM,aAAA,GAAgB,CAAC,KAAK,KAA2B;QACrD,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE,KAAyB;YACxC,IAAI,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;gBACrB,aAAa,CAAC,EAAA,EAAqB;YAC3C,OAAa;gBACL,MAAM,CAAC,IAAI,CAAC,IAAQ;YAC5B;QACA,CAAK,CAAC;IACN,CAAG;IAED,aAAa,CAAC,KAAK,CAAC;IACpB,OAAO,MAAM;AACf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2565, "column": 0}, "map": {"version": 3, "file": "redis.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/integrations/tracing/redis.ts"], "sourcesContent": ["import type { Span } from '@opentelemetry/api';\nimport type { RedisResponseCustomAttributeFunction } from '@opentelemetry/instrumentation-ioredis';\nimport { IORedisInstrumentation } from '@opentelemetry/instrumentation-ioredis';\nimport { RedisInstrumentation } from '@opentelemetry/instrumentation-redis-4';\nimport type { IntegrationFn } from '@sentry/core';\nimport {\n  defineIntegration,\n  SEMANTIC_ATTRIBUTE_CACHE_HIT,\n  SEMANTIC_ATTRIBUTE_CACHE_ITEM_SIZE,\n  SEMANTIC_ATTRIBUTE_CACHE_KEY,\n  SEMANTIC_ATTRIBUTE_SENTRY_OP,\n  SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,\n  spanToJSON,\n  truncate,\n} from '@sentry/core';\nimport { generateInstrumentOnce } from '@sentry/node-core';\nimport {\n  calculateCacheItemSize,\n  GET_COMMANDS,\n  getCacheKeySafely,\n  getCacheOperation,\n  isInCommands,\n  shouldConsiderForCache,\n} from '../../utils/redisCache';\n\ninterface RedisOptions {\n  cachePrefixes?: string[];\n}\n\nconst INTEGRATION_NAME = 'Redis';\n\nlet _redisOptions: RedisOptions = {};\n\nconst cacheResponseHook: RedisResponseCustomAttributeFunction = (span: Span, redisCommand, cmdArgs, response) => {\n  span.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN, 'auto.db.otel.redis');\n\n  const safeKey = getCacheKeySafely(redisCommand, cmdArgs);\n  const cacheOperation = getCacheOperation(redisCommand);\n\n  if (\n    !safeKey ||\n    !cacheOperation ||\n    !_redisOptions.cachePrefixes ||\n    !shouldConsiderForCache(redisCommand, safeKey, _redisOptions.cachePrefixes)\n  ) {\n    // not relevant for cache\n    return;\n  }\n\n  // otel/ioredis seems to be using the old standard, as there was a change to those params: https://github.com/open-telemetry/opentelemetry-specification/issues/3199\n  // We are using params based on the docs: https://opentelemetry.io/docs/specs/semconv/attributes-registry/network/\n  const networkPeerAddress = spanToJSON(span).data['net.peer.name'];\n  const networkPeerPort = spanToJSON(span).data['net.peer.port'];\n  if (networkPeerPort && networkPeerAddress) {\n    span.setAttributes({ 'network.peer.address': networkPeerAddress, 'network.peer.port': networkPeerPort });\n  }\n\n  const cacheItemSize = calculateCacheItemSize(response);\n\n  if (cacheItemSize) {\n    span.setAttribute(SEMANTIC_ATTRIBUTE_CACHE_ITEM_SIZE, cacheItemSize);\n  }\n\n  if (isInCommands(GET_COMMANDS, redisCommand) && cacheItemSize !== undefined) {\n    span.setAttribute(SEMANTIC_ATTRIBUTE_CACHE_HIT, cacheItemSize > 0);\n  }\n\n  span.setAttributes({\n    [SEMANTIC_ATTRIBUTE_SENTRY_OP]: cacheOperation,\n    [SEMANTIC_ATTRIBUTE_CACHE_KEY]: safeKey,\n  });\n\n  const spanDescription = safeKey.join(', ');\n\n  span.updateName(truncate(spanDescription, 1024));\n};\n\nconst instrumentIORedis = generateInstrumentOnce('IORedis', () => {\n  return new IORedisInstrumentation({\n    responseHook: cacheResponseHook,\n  });\n});\n\nconst instrumentRedis4 = generateInstrumentOnce('Redis-4', () => {\n  return new RedisInstrumentation({\n    responseHook: cacheResponseHook,\n  });\n});\n\n/** To be able to preload all Redis OTel instrumentations with just one ID (\"Redis\"), all the instrumentations are generated in this one function  */\nexport const instrumentRedis = Object.assign(\n  (): void => {\n    instrumentIORedis();\n    instrumentRedis4();\n\n    // todo: implement them gradually\n    // new LegacyRedisInstrumentation({}),\n  },\n  { id: INTEGRATION_NAME },\n);\n\nconst _redisIntegration = ((options: RedisOptions = {}) => {\n  return {\n    name: INTEGRATION_NAME,\n    setupOnce() {\n      _redisOptions = options;\n      instrumentRedis();\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Adds Sentry tracing instrumentation for the [redis](https://www.npmjs.com/package/redis) and\n * [ioredis](https://www.npmjs.com/package/ioredis) libraries.\n *\n * For more information, see the [`redisIntegration` documentation](https://docs.sentry.io/platforms/javascript/guides/node/configuration/integrations/redis/).\n *\n * @example\n * ```javascript\n * const Sentry = require('@sentry/node');\n *\n * Sentry.init({\n *  integrations: [Sentry.redisIntegration()],\n * });\n * ```\n */\nexport const redisIntegration = defineIntegration(_redisIntegration);\n"], "names": ["SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN", "getCacheKeySafely", "getCacheOperation", "shouldConsiderForCache", "spanToJSON", "calculateCacheItemSize", "SEMANTIC_ATTRIBUTE_CACHE_ITEM_SIZE", "isInCommands", "GET_COMMANDS", "SEMANTIC_ATTRIBUTE_CACHE_HIT", "SEMANTIC_ATTRIBUTE_SENTRY_OP", "SEMANTIC_ATTRIBUTE_CACHE_KEY", "truncate", "generateInstrumentOnce", "IORedisInstrumentation", "RedisInstrumentation", "defineIntegration"], "mappings": ";;;;;;;;AA6BA,MAAM,gBAAA,GAAmB,OAAO;AAEhC,IAAI,aAAa,GAAiB,CAAA,CAAE;AAEpC,MAAM,iBAAiB,GAAyC,CAAC,IAAI,EAAQ,YAAY,EAAE,OAAO,EAAE,QAAQ,KAAK;IAC/G,IAAI,CAAC,YAAY,CAACA,KAAAA,gCAAgC,EAAE,oBAAoB,CAAC;IAEzE,MAAM,UAAUC,WAAAA,iBAAiB,CAAC,YAAY,EAAE,OAAO,CAAC;IACxD,MAAM,cAAA,GAAiBC,WAAAA,iBAAiB,CAAC,YAAY,CAAC;IAEtD,IACE,CAAC,OAAA,IACD,CAAC,cAAA,IACD,CAAC,aAAa,CAAC,aAAA,IACf,CAACC,WAAAA,sBAAsB,CAAC,YAAY,EAAE,OAAO,EAAE,aAAa,CAAC,aAAa,GAC1E;QACJ,yBAAA;QACI;IACJ;IAEA,oKAAA;IACA,kHAAA;IACE,MAAM,kBAAA,GAAqBC,KAAAA,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC;IACjE,MAAM,eAAA,GAAkBA,KAAAA,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC;IAC9D,IAAI,eAAA,IAAmB,kBAAkB,EAAE;QACzC,IAAI,CAAC,aAAa,CAAC;YAAE,sBAAsB,EAAE,kBAAkB;YAAE,mBAAmB,EAAE,eAAA;QAAA,CAAiB,CAAC;IAC5G;IAEE,MAAM,aAAA,GAAgBC,WAAAA,sBAAsB,CAAC,QAAQ,CAAC;IAEtD,IAAI,aAAa,EAAE;QACjB,IAAI,CAAC,YAAY,CAACC,KAAAA,kCAAkC,EAAE,aAAa,CAAC;IACxE;IAEE,IAAIC,WAAAA,YAAY,CAACC,WAAAA,YAAY,EAAE,YAAY,CAAA,IAAK,aAAA,KAAkB,SAAS,EAAE;QAC3E,IAAI,CAAC,YAAY,CAACC,KAAAA,4BAA4B,EAAE,aAAA,GAAgB,CAAC,CAAC;IACtE;IAEE,IAAI,CAAC,aAAa,CAAC;QACjB,CAACC,KAAAA,4BAA4B,CAAA,EAAG,cAAc;QAC9C,CAACC,KAAAA,4BAA4B,CAAA,EAAG,OAAO;IAC3C,CAAG,CAAC;IAEF,MAAM,kBAAkB,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;IAE1C,IAAI,CAAC,UAAU,CAACC,KAAAA,QAAQ,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;AAClD,CAAC;AAED,MAAM,iBAAA,GAAoBC,SAAAA,sBAAsB,CAAC,SAAS,EAAE,MAAM;IAChE,OAAO,IAAIC,uBAAAA,sBAAsB,CAAC;QAChC,YAAY,EAAE,iBAAiB;IACnC,CAAG,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,gBAAA,GAAmBD,SAAAA,sBAAsB,CAAC,SAAS,EAAE,MAAM;IAC/D,OAAO,IAAIE,sBAAAA,oBAAoB,CAAC;QAC9B,YAAY,EAAE,iBAAiB;IACnC,CAAG,CAAC;AACJ,CAAC,CAAC;AAEF,mJAAA,GACO,MAAM,eAAA,GAAkB,MAAM,CAAC,MAAM,CAC1C,MAAY;IACV,iBAAiB,EAAE;IACnB,gBAAgB,EAAE;AAEtB,iCAAA;AACA,sCAAA;AACA,CAAG,EACD;IAAE,EAAE,EAAE,gBAAA;AAAA,CAAkB;AAG1B,MAAM,iBAAA,GAAqB,CAAC,OAAO,GAAiB,CAAA,CAAE,KAAK;IACzD,OAAO;QACL,IAAI,EAAE,gBAAgB;QACtB,SAAS,GAAG;YACV,aAAA,GAAgB,OAAO;YACvB,eAAe,EAAE;QACvB,CAAK;IACL,CAAG;AACH,CAAC,CAAA;AAED;;;;;;;;;;;;;;CAcA,SACa,gBAAA,GAAmBC,KAAAA,iBAAiB,CAAC,iBAAiB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2656, "column": 0}, "map": {"version": 3, "file": "postgres.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/integrations/tracing/postgres.ts"], "sourcesContent": ["import { PgInstrumentation } from '@opentelemetry/instrumentation-pg';\nimport type { IntegrationFn } from '@sentry/core';\nimport { defineIntegration } from '@sentry/core';\nimport { addOriginToSpan, generateInstrumentOnce } from '@sentry/node-core';\n\nconst INTEGRATION_NAME = 'Postgres';\n\nexport const instrumentPostgres = generateInstrumentOnce(\n  INTEGRATION_NAME,\n  () =>\n    new PgInstrumentation({\n      requireParentSpan: true,\n      requestHook(span) {\n        addOriginToSpan(span, 'auto.db.otel.postgres');\n      },\n    }),\n);\n\nconst _postgresIntegration = (() => {\n  return {\n    name: INTEGRATION_NAME,\n    setupOnce() {\n      instrumentPostgres();\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Adds Sentry tracing instrumentation for the [pg](https://www.npmjs.com/package/pg) library.\n *\n * For more information, see the [`postgresIntegration` documentation](https://docs.sentry.io/platforms/javascript/guides/node/configuration/integrations/postgres/).\n *\n * @example\n * ```javascript\n * const Sentry = require('@sentry/node');\n *\n * Sentry.init({\n *  integrations: [Sentry.postgresIntegration()],\n * });\n * ```\n */\nexport const postgresIntegration = defineIntegration(_postgresIntegration);\n"], "names": ["generateInstrumentOnce", "PgInstrumentation", "addOriginToSpan", "defineIntegration"], "mappings": ";;;;;;AAKA,MAAM,gBAAA,GAAmB,UAAU;AAE5B,MAAM,kBAAA,GAAqBA,SAAAA,sBAAsB,CACtD,gBAAgB,EAChB,IACE,IAAIC,kBAAAA,iBAAiB,CAAC;QACpB,iBAAiB,EAAE,IAAI;QACvB,WAAW,EAAC,IAAI,EAAE;YAChBC,SAAAA,eAAe,CAAC,IAAI,EAAE,uBAAuB,CAAC;QACtD,CAAO;IACP,CAAK,CAAC;AAGN,MAAM,oBAAA,GAAwB,MAAM;IAClC,OAAO;QACL,IAAI,EAAE,gBAAgB;QACtB,SAAS,GAAG;YACV,kBAAkB,EAAE;QAC1B,CAAK;IACL,CAAG;AACH,CAAC,CAAA;AAED;;;;;;;;;;;;;CAaA,SACa,mBAAA,GAAsBC,KAAAA,iBAAiB,CAAC,oBAAoB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2698, "column": 0}, "map": {"version": 3, "file": "postgresjs.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/integrations/tracing/postgresjs.ts"], "sourcesContent": ["// Instrumentation for https://github.com/porsager/postgres\nimport { context, trace } from '@opentelemetry/api';\nimport type { InstrumentationConfig } from '@opentelemetry/instrumentation';\nimport {\n  InstrumentationBase,\n  InstrumentationNodeModuleDefinition,\n  InstrumentationNodeModuleFile,\n  safeExecuteInTheMiddle,\n} from '@opentelemetry/instrumentation';\nimport {\n  ATTR_DB_NAMESPACE,\n  ATTR_DB_OPERATION_NAME,\n  ATTR_DB_QUERY_TEXT,\n  ATTR_DB_RESPONSE_STATUS_CODE,\n  ATTR_DB_SYSTEM_NAME,\n  ATTR_ERROR_TYPE,\n  ATTR_SERVER_ADDRESS,\n  ATTR_SERVER_PORT,\n} from '@opentelemetry/semantic-conventions';\nimport type { IntegrationFn, Span } from '@sentry/core';\nimport {\n  debug,\n  defineIntegration,\n  getCurrentScope,\n  SDK_VERSION,\n  SPAN_STATUS_ERROR,\n  startSpanManual,\n} from '@sentry/core';\nimport { addOriginToSpan, generateInstrumentOnce } from '@sentry/node-core';\n\nconst INTEGRATION_NAME = 'PostgresJs';\nconst SUPPORTED_VERSIONS = ['>=3.0.0 <4'];\n\ntype PostgresConnectionContext = {\n  ATTR_DB_NAMESPACE?: string; // Database name\n  ATTR_SERVER_ADDRESS?: string; // Hostname or IP address of the database server\n  ATTR_SERVER_PORT?: string; // Port number of the database server\n};\n\ntype PostgresJsInstrumentationConfig = InstrumentationConfig & {\n  /**\n   * Whether to require a parent span for the instrumentation.\n   * If set to true, the instrumentation will only create spans if there is a parent span\n   * available in the current scope.\n   * @default true\n   */\n  requireParentSpan?: boolean;\n  /**\n   * Hook to modify the span before it is started.\n   * This can be used to set additional attributes or modify the span in any way.\n   */\n  requestHook?: (span: Span, sanitizedSqlQuery: string, postgresConnectionContext?: PostgresConnectionContext) => void;\n};\n\nexport const instrumentPostgresJs = generateInstrumentOnce(\n  INTEGRATION_NAME,\n  (options?: PostgresJsInstrumentationConfig) =>\n    new PostgresJsInstrumentation({\n      requireParentSpan: options?.requireParentSpan ?? true,\n      requestHook: options?.requestHook,\n    }),\n);\n\n/**\n * Instrumentation for the [postgres](https://www.npmjs.com/package/postgres) library.\n * This instrumentation captures postgresjs queries and their attributes,\n */\nexport class PostgresJsInstrumentation extends InstrumentationBase<PostgresJsInstrumentationConfig> {\n  public constructor(config: PostgresJsInstrumentationConfig) {\n    super('sentry-postgres-js', SDK_VERSION, config);\n  }\n\n  /**\n   * Initializes the instrumentation.\n   */\n  public init(): InstrumentationNodeModuleDefinition[] {\n    const instrumentationModule = new InstrumentationNodeModuleDefinition('postgres', SUPPORTED_VERSIONS);\n\n    ['src', 'cf/src', 'cjs/src'].forEach(path => {\n      instrumentationModule.files.push(\n        new InstrumentationNodeModuleFile(\n          `postgres/${path}/connection.js`,\n          ['*'],\n          this._patchConnection.bind(this),\n          this._unwrap.bind(this),\n        ),\n      );\n\n      instrumentationModule.files.push(\n        new InstrumentationNodeModuleFile(\n          `postgres/${path}/query.js`,\n          SUPPORTED_VERSIONS,\n          this._patchQuery.bind(this),\n          this._unwrap.bind(this),\n        ),\n      );\n    });\n\n    return [instrumentationModule];\n  }\n\n  /**\n   * Determines whether a span should be created based on the current context.\n   * If `requireParentSpan` is set to true in the configuration, a span will\n   * only be created if there is a parent span available.\n   */\n  private _shouldCreateSpans(): boolean {\n    const config = this.getConfig();\n    const hasParentSpan = trace.getSpan(context.active()) !== undefined;\n    return hasParentSpan || !config.requireParentSpan;\n  }\n\n  /**\n   * Patches the reject method of the Query class to set the span status and end it\n   */\n  private _patchReject(rejectTarget: any, span: Span): any {\n    return new Proxy(rejectTarget, {\n      apply: (\n        rejectTarget,\n        rejectThisArg,\n        rejectArgs: {\n          message?: string;\n          code?: string;\n          name?: string;\n        }[],\n      ) => {\n        span.setStatus({\n          code: SPAN_STATUS_ERROR,\n          // This message is the error message from the rejectArgs, when available\n          // e.g \"relation 'User' does not exist\"\n          message: rejectArgs?.[0]?.message || 'unknown_error',\n        });\n\n        const result = Reflect.apply(rejectTarget, rejectThisArg, rejectArgs);\n\n        // This status code is PG error code, e.g. '42P01' for \"relation does not exist\"\n        // https://www.postgresql.org/docs/current/errcodes-appendix.html\n        span.setAttribute(ATTR_DB_RESPONSE_STATUS_CODE, rejectArgs?.[0]?.code || 'Unknown error');\n        // This is the error type, e.g. 'PostgresError' for a Postgres error\n        span.setAttribute(ATTR_ERROR_TYPE, rejectArgs?.[0]?.name || 'Unknown error');\n\n        span.end();\n        return result;\n      },\n    });\n  }\n\n  /**\n   * Patches the resolve method of the Query class to end the span when the query is resolved.\n   */\n  private _patchResolve(resolveTarget: any, span: Span): any {\n    return new Proxy(resolveTarget, {\n      apply: (resolveTarget, resolveThisArg, resolveArgs: [{ command?: string }]) => {\n        const result = Reflect.apply(resolveTarget, resolveThisArg, resolveArgs);\n        const sqlCommand = resolveArgs?.[0]?.command;\n\n        if (sqlCommand) {\n          // SQL command is only available when the query is resolved successfully\n          span.setAttribute(ATTR_DB_OPERATION_NAME, sqlCommand);\n        }\n        span.end();\n        return result;\n      },\n    });\n  }\n\n  /**\n   * Patches the Query class to instrument the handle method.\n   */\n  private _patchQuery(moduleExports: {\n    Query: {\n      prototype: {\n        handle: any;\n      };\n    };\n  }): any {\n    moduleExports.Query.prototype.handle = new Proxy(moduleExports.Query.prototype.handle, {\n      apply: async (\n        handleTarget,\n        handleThisArg: {\n          resolve: any;\n          reject: any;\n          strings?: string[];\n        },\n        handleArgs,\n      ) => {\n        if (!this._shouldCreateSpans()) {\n          // If we don't need to create spans, just call the original method\n          return Reflect.apply(handleTarget, handleThisArg, handleArgs);\n        }\n\n        const sanitizedSqlQuery = this._sanitizeSqlQuery(handleThisArg.strings?.[0]);\n\n        return startSpanManual(\n          {\n            name: sanitizedSqlQuery || 'postgresjs.query',\n            op: 'db',\n          },\n          (span: Span) => {\n            const scope = getCurrentScope();\n            const postgresConnectionContext = scope.getScopeData().contexts['postgresjsConnection'] as\n              | PostgresConnectionContext\n              | undefined;\n\n            addOriginToSpan(span, 'auto.db.otel.postgres');\n\n            const { requestHook } = this.getConfig();\n\n            if (requestHook) {\n              safeExecuteInTheMiddle(\n                () => requestHook(span, sanitizedSqlQuery, postgresConnectionContext),\n                error => {\n                  if (error) {\n                    debug.error(`Error in requestHook for ${INTEGRATION_NAME} integration:`, error);\n                  }\n                },\n              );\n            }\n\n            // ATTR_DB_NAMESPACE is used to indicate the database name and the schema name\n            // It's only the database name as we don't have the schema information\n            const databaseName = postgresConnectionContext?.ATTR_DB_NAMESPACE || '<unknown database>';\n            const databaseHost = postgresConnectionContext?.ATTR_SERVER_ADDRESS || '<unknown host>';\n            const databasePort = postgresConnectionContext?.ATTR_SERVER_PORT || '<unknown port>';\n\n            span.setAttribute(ATTR_DB_SYSTEM_NAME, 'postgres');\n            span.setAttribute(ATTR_DB_NAMESPACE, databaseName);\n            span.setAttribute(ATTR_SERVER_ADDRESS, databaseHost);\n            span.setAttribute(ATTR_SERVER_PORT, databasePort);\n            span.setAttribute(ATTR_DB_QUERY_TEXT, sanitizedSqlQuery);\n\n            handleThisArg.resolve = this._patchResolve(handleThisArg.resolve, span);\n            handleThisArg.reject = this._patchReject(handleThisArg.reject, span);\n\n            try {\n              return Reflect.apply(handleTarget, handleThisArg, handleArgs);\n            } catch (error) {\n              span.setStatus({\n                code: SPAN_STATUS_ERROR,\n              });\n              span.end();\n              throw error; // Re-throw the error to propagate it\n            }\n          },\n        );\n      },\n    });\n\n    return moduleExports;\n  }\n\n  /**\n   * Patches the Connection class to set the database, host, and port attributes\n   * when a new connection is created.\n   */\n  private _patchConnection(Connection: any): any {\n    return new Proxy(Connection, {\n      apply: (connectionTarget, thisArg, connectionArgs: { database: string; host: string[]; port: number[] }[]) => {\n        const databaseName = connectionArgs[0]?.database || '<unknown database>';\n        const databaseHost = connectionArgs[0]?.host?.[0] || '<unknown host>';\n        const databasePort = connectionArgs[0]?.port?.[0] || '<unknown port>';\n\n        const scope = getCurrentScope();\n        scope.setContext('postgresjsConnection', {\n          ATTR_DB_NAMESPACE: databaseName,\n          ATTR_SERVER_ADDRESS: databaseHost,\n          ATTR_SERVER_PORT: databasePort,\n        });\n\n        return Reflect.apply(connectionTarget, thisArg, connectionArgs);\n      },\n    });\n  }\n\n  /**\n   * Sanitize SQL query as per the OTEL semantic conventions\n   * https://opentelemetry.io/docs/specs/semconv/database/database-spans/#sanitization-of-dbquerytext\n   */\n  private _sanitizeSqlQuery(sqlQuery: string | undefined): string {\n    if (!sqlQuery) {\n      return 'Unknown SQL Query';\n    }\n\n    return (\n      sqlQuery\n        .replace(/\\s+/g, ' ')\n        .trim() // Remove extra spaces including newlines and trim\n        .substring(0, 1024) // Truncate to 1024 characters\n        .replace(/--.*?(\\r?\\n|$)/g, '') // Single line comments\n        .replace(/\\/\\*[\\s\\S]*?\\*\\//g, '') // Multi-line comments\n        .replace(/;\\s*$/, '') // Remove trailing semicolons\n        .replace(/\\b\\d+\\b/g, '?') // Replace standalone numbers\n        // Collapse whitespace to a single space\n        .replace(/\\s+/g, ' ')\n        // Collapse IN and in clauses\n        // eg. IN (?, ?, ?, ?) to IN (?)\n        .replace(/\\bIN\\b\\s*\\(\\s*\\?(?:\\s*,\\s*\\?)*\\s*\\)/g, 'IN (?)')\n    );\n  }\n}\n\nconst _postgresJsIntegration = (() => {\n  return {\n    name: INTEGRATION_NAME,\n    setupOnce() {\n      instrumentPostgresJs();\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Adds Sentry tracing instrumentation for the [postgres](https://www.npmjs.com/package/postgres) library.\n *\n * For more information, see the [`postgresIntegration` documentation](https://docs.sentry.io/platforms/javascript/guides/node/configuration/integrations/postgres/).\n *\n * @example\n * ```javascript\n * const Sentry = require('@sentry/node');\n *\n * Sentry.init({\n *  integrations: [Sentry.postgresJsIntegration()],\n * });\n * ```\n */\n\nexport const postgresJsIntegration = defineIntegration(_postgresJsIntegration);\n"], "names": ["generateInstrumentOnce", "InstrumentationBase", "SDK_VERSION", "InstrumentationNodeModuleDefinition", "InstrumentationNodeModuleFile", "trace", "context", "SPAN_STATUS_ERROR", "ATTR_DB_RESPONSE_STATUS_CODE", "ATTR_ERROR_TYPE", "ATTR_DB_OPERATION_NAME", "startSpanManual", "getCurrentScope", "addOriginToSpan", "safeExecuteInTheMiddle", "debug", "ATTR_DB_SYSTEM_NAME", "ATTR_DB_NAMESPACE", "ATTR_SERVER_ADDRESS", "ATTR_SERVER_PORT", "ATTR_DB_QUERY_TEXT", "defineIntegration"], "mappings": ";;;;;;;;AAAA,2DAAA;AA8BA,MAAM,gBAAA,GAAmB,YAAY;AACrC,MAAM,kBAAA,GAAqB;IAAC,YAAY;CAAC;AAuBlC,MAAM,oBAAA,GAAuBA,SAAAA,sBAAsB,CACxD,gBAAgB,EAChB,CAAC,OAAO,GACN,IAAI,yBAAyB,CAAC;QAC5B,iBAAiB,EAAE,OAAO,EAAE,iBAAA,IAAqB,IAAI;QACrD,WAAW,EAAE,OAAO,EAAE,WAAW;IACvC,CAAK,CAAC;AAGN;;;CAGA,GACO,MAAM,yBAAA,SAAkCC,gBAAAA,mBAAmB,CAAkC;IAC3F,WAAW,CAAC,MAAM,CAAmC;QAC1D,KAAK,CAAC,oBAAoB,EAAEC,KAAAA,WAAW,EAAE,MAAM,CAAC;IACpD;IAEA;;GAEA,GACS,IAAI,GAA0C;QACnD,MAAM,wBAAwB,IAAIC,gBAAAA,mCAAmC,CAAC,UAAU,EAAE,kBAAkB,CAAC;QAErG;YAAC,KAAK;YAAE,QAAQ;YAAE,SAAS;SAAC,CAAC,OAAO,EAAC,IAAA,IAAQ;YAC3C,qBAAqB,CAAC,KAAK,CAAC,IAAI,CAC9B,IAAIC,gBAAAA,6BAA6B,CAC/B,CAAC,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,EAChC;gBAAC,GAAG;aAAC,EACL,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,EAChC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;YAI3B,qBAAqB,CAAC,KAAK,CAAC,IAAI,CAC9B,IAAIA,gBAAAA,6BAA6B,CAC/B,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,EAC3B,kBAAkB,EAClB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAC3B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;QAGjC,CAAK,CAAC;QAEF,OAAO;YAAC,qBAAqB;SAAC;IAClC;IAEA;;;;GAIA,GACU,kBAAkB,GAAY;QACpC,MAAM,MAAA,GAAS,IAAI,CAAC,SAAS,EAAE;QAC/B,MAAM,aAAA,GAAgBC,IAAAA,KAAK,CAAC,OAAO,CAACC,IAAAA,OAAO,CAAC,MAAM,EAAE,CAAA,KAAM,SAAS;QACnE,OAAO,aAAA,IAAiB,CAAC,MAAM,CAAC,iBAAiB;IACrD;IAEA;;GAEA,GACU,YAAY,CAAC,YAAY,EAAO,IAAI,EAAa;QACvD,OAAO,IAAI,KAAK,CAAC,YAAY,EAAE;YAC7B,KAAK,EAAE,CACL,YAAY,EACZ,aAAa,EACb;gBAMA,IAAI,CAAC,SAAS,CAAC;oBACb,IAAI,EAAEC,KAAAA,iBAAiB;oBACjC,wEAAA;oBACA,uCAAA;oBACU,OAAO,EAAE,UAAU,EAAA,CAAG,CAAC,CAAC,EAAE,OAAA,IAAW,eAAe;gBAC9D,CAAS,CAAC;gBAEF,MAAM,MAAA,GAAS,OAAO,CAAC,KAAK,CAAC,YAAY,EAAE,aAAa,EAAE,UAAU,CAAC;gBAE7E,gFAAA;gBACA,iEAAA;gBACQ,IAAI,CAAC,YAAY,CAACC,oBAAAA,4BAA4B,EAAE,UAAU,EAAA,CAAG,CAAC,CAAC,EAAE,IAAA,IAAQ,eAAe,CAAC;gBACjG,oEAAA;gBACQ,IAAI,CAAC,YAAY,CAACC,oBAAAA,eAAe,EAAE,UAAU,EAAA,CAAG,CAAC,CAAC,EAAE,IAAA,IAAQ,eAAe,CAAC;gBAE5E,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,MAAM;YACrB,CAAO;QACP,CAAK,CAAC;IACN;IAEA;;GAEA,GACU,aAAa,CAAC,aAAa,EAAO,IAAI,EAAa;QACzD,OAAO,IAAI,KAAK,CAAC,aAAa,EAAE;YAC9B,KAAK,EAAE,CAAC,aAAa,EAAE,cAAc,EAAE,WAAW,KAA6B;gBAC7E,MAAM,MAAA,GAAS,OAAO,CAAC,KAAK,CAAC,aAAa,EAAE,cAAc,EAAE,WAAW,CAAC;gBACxE,MAAM,aAAa,WAAW,EAAA,CAAG,CAAC,CAAC,EAAE,OAAO;gBAE5C,IAAI,UAAU,EAAE;oBACxB,wEAAA;oBACU,IAAI,CAAC,YAAY,CAACC,oBAAAA,sBAAsB,EAAE,UAAU,CAAC;gBAC/D;gBACQ,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,MAAM;YACrB,CAAO;QACP,CAAK,CAAC;IACN;IAEA;;GAEA,GACU,WAAW,CAAC,aAAA,EAMZ;QACN,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,MAAA,GAAS,IAAI,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,EAAE;YACrF,KAAK,EAAE,OACL,YAAY,EACZ,eAKA,UAAU;gBAEV,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE;oBACxC,kEAAA;oBACU,OAAO,OAAO,CAAC,KAAK,CAAC,YAAY,EAAE,aAAa,EAAE,UAAU,CAAC;gBACvE;gBAEQ,MAAM,iBAAA,GAAoB,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,OAAO,EAAA,CAAG,CAAC,CAAC,CAAC;gBAE5E,OAAOC,KAAAA,eAAe,CACpB;oBACE,IAAI,EAAE,iBAAA,IAAqB,kBAAkB;oBAC7C,EAAE,EAAE,IAAI;gBACpB,CAAW,EACD,CAAC,IAAI,KAAW;oBACd,MAAM,KAAA,GAAQC,KAAAA,eAAe,EAAE;oBAC/B,MAAM,yBAAA,GAA4B,KAAK,CAAC,YAAY,EAAE,CAAC,QAAQ,CAAC,sBAAsB,CAAA;oBAItFC,SAAAA,eAAe,CAAC,IAAI,EAAE,uBAAuB,CAAC;oBAE9C,MAAM,EAAE,WAAA,EAAY,GAAI,IAAI,CAAC,SAAS,EAAE;oBAExC,IAAI,WAAW,EAAE;wBACfC,gBAAAA,sBAAsB,CACpB,IAAM,WAAW,CAAC,IAAI,EAAE,iBAAiB,EAAE,yBAAyB,CAAC,GACrE,SAAS;4BACP,IAAI,KAAK,EAAE;gCACTC,KAAAA,KAAK,CAAC,KAAK,CAAC,CAAC,yBAAyB,EAAE,gBAAgB,CAAC,aAAa,CAAC,EAAE,KAAK,CAAC;4BACnG;wBACA,CAAiB;oBAEjB;oBAEA,8EAAA;oBACA,sEAAA;oBACY,MAAM,YAAA,GAAe,yBAAyB,EAAE,iBAAA,IAAqB,oBAAoB;oBACzF,MAAM,YAAA,GAAe,yBAAyB,EAAE,mBAAA,IAAuB,gBAAgB;oBACvF,MAAM,YAAA,GAAe,yBAAyB,EAAE,gBAAA,IAAoB,gBAAgB;oBAEpF,IAAI,CAAC,YAAY,CAACC,oBAAAA,mBAAmB,EAAE,UAAU,CAAC;oBAClD,IAAI,CAAC,YAAY,CAACC,oBAAAA,iBAAiB,EAAE,YAAY,CAAC;oBAClD,IAAI,CAAC,YAAY,CAACC,oBAAAA,mBAAmB,EAAE,YAAY,CAAC;oBACpD,IAAI,CAAC,YAAY,CAACC,oBAAAA,gBAAgB,EAAE,YAAY,CAAC;oBACjD,IAAI,CAAC,YAAY,CAACC,oBAAAA,kBAAkB,EAAE,iBAAiB,CAAC;oBAExD,aAAa,CAAC,OAAA,GAAU,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC;oBACvE,aAAa,CAAC,MAAA,GAAS,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,MAAM,EAAE,IAAI,CAAC;oBAEpE,IAAI;wBACF,OAAO,OAAO,CAAC,KAAK,CAAC,YAAY,EAAE,aAAa,EAAE,UAAU,CAAC;oBAC3E,CAAY,CAAE,OAAO,KAAK,EAAE;wBACd,IAAI,CAAC,SAAS,CAAC;4BACb,IAAI,EAAEb,KAAAA,iBAAiB;wBACvC,CAAe,CAAC;wBACF,IAAI,CAAC,GAAG,EAAE;wBACV,MAAM,KAAK,CAAA,CAAA,qCAAA;oBACzB;gBACA,CAAW;YAEX,CAAO;QACP,CAAK,CAAC;QAEF,OAAO,aAAa;IACxB;IAEA;;;GAGA,GACU,gBAAgB,CAAC,UAAU,EAAY;QAC7C,OAAO,IAAI,KAAK,CAAC,UAAU,EAAE;YAC3B,KAAK,EAAE,CAAC,gBAAgB,EAAE,OAAO,EAAE,cAAc,KAA6D;gBAC5G,MAAM,YAAA,GAAe,cAAc,CAAC,CAAC,CAAC,EAAE,QAAA,IAAY,oBAAoB;gBACxE,MAAM,YAAA,GAAe,cAAc,CAAC,CAAC,CAAC,EAAE,IAAI,EAAA,CAAG,CAAC,CAAA,IAAK,gBAAgB;gBACrE,MAAM,YAAA,GAAe,cAAc,CAAC,CAAC,CAAC,EAAE,IAAI,EAAA,CAAG,CAAC,CAAA,IAAK,gBAAgB;gBAErE,MAAM,KAAA,GAAQK,KAAAA,eAAe,EAAE;gBAC/B,KAAK,CAAC,UAAU,CAAC,sBAAsB,EAAE;oBACvC,iBAAiB,EAAE,YAAY;oBAC/B,mBAAmB,EAAE,YAAY;oBACjC,gBAAgB,EAAE,YAAY;gBACxC,CAAS,CAAC;gBAEF,OAAO,OAAO,CAAC,KAAK,CAAC,gBAAgB,EAAE,OAAO,EAAE,cAAc,CAAC;YACvE,CAAO;QACP,CAAK,CAAC;IACN;IAEA;;;GAGA,GACU,iBAAiB,CAAC,QAAQ,EAA8B;QAC9D,IAAI,CAAC,QAAQ,EAAE;YACb,OAAO,mBAAmB;QAChC;QAEI,OACE,SACG,OAAO,CAAC,MAAM,EAAE,GAAG,EACnB,IAAI,EAAC,CAAA,kDAAA;SACL,SAAS,CAAC,CAAC,EAAE,IAAI,CAAA,CAAA,8BAAA;SACjB,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAA,CAAA,uBAAA;SAC7B,OAAO,CAAC,mBAAmB,EAAE,EAAE,CAAA,CAAA,sBAAA;SAC/B,OAAO,CAAC,OAAO,EAAE,EAAE,CAAA,CAAA,6BAAA;SACnB,OAAO,CAAC,UAAU,EAAE,GAAG,CAAA,CAAA,6BAAA;QAChC,wCAAA;SACS,OAAO,CAAC,MAAM,EAAE,GAAG,CAC5B,6BAAA;QACA,gCAAA;SACS,OAAO,CAAC,sCAAsC,EAAE,QAAQ;IAEjE;AACA;AAEA,MAAM,sBAAA,GAA0B,MAAM;IACpC,OAAO;QACL,IAAI,EAAE,gBAAgB;QACtB,SAAS,GAAG;YACV,oBAAoB,EAAE;QAC5B,CAAK;IACL,CAAG;AACH,CAAC,CAAA;AAED;;;;;;;;;;;;;CAaA,SAEa,qBAAA,GAAwBS,KAAAA,iBAAiB,CAAC,sBAAsB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2906, "column": 0}, "map": {"version": 3, "file": "prisma.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/integrations/tracing/prisma.ts"], "sourcesContent": ["import type { Instrumentation } from '@opentelemetry/instrumentation';\nimport { PrismaInstrumentation } from '@prisma/instrumentation';\nimport { consoleSandbox, defineIntegration, SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN, spanToJSON } from '@sentry/core';\nimport { generateInstrumentOnce } from '@sentry/node-core';\nimport type { PrismaV5TracingHelper } from './prisma/vendor/v5-tracing-helper';\nimport type { PrismaV6TracingHelper } from './prisma/vendor/v6-tracing-helper';\n\nconst INTEGRATION_NAME = 'Prisma';\n\ntype CompatibilityLayerTraceHelper = PrismaV5TracingHelper & PrismaV6TracingHelper;\n\nfunction isPrismaV6TracingHelper(helper: unknown): helper is PrismaV6TracingHelper {\n  return !!helper && typeof helper === 'object' && 'dispatchEngineSpans' in helper;\n}\n\nfunction getPrismaTracingHelper(): unknown | undefined {\n  const prismaInstrumentationObject = (globalThis as Record<string, unknown>).PRISMA_INSTRUMENTATION;\n  const prismaTracingHelper =\n    prismaInstrumentationObject &&\n    typeof prismaInstrumentationObject === 'object' &&\n    'helper' in prismaInstrumentationObject\n      ? prismaInstrumentationObject.helper\n      : undefined;\n\n  return prismaTracingHelper;\n}\n\nclass SentryPrismaInteropInstrumentation extends PrismaInstrumentation {\n  public constructor() {\n    super();\n  }\n\n  public enable(): void {\n    super.enable();\n\n    // The PrismaIntegration (super class) defines a global variable `global[\"PRISMA_INSTRUMENTATION\"]` when `enable()` is called. This global variable holds a \"TracingHelper\" which Prisma uses internally to create tracing data. It's their way of not depending on OTEL with their main package. The sucky thing is, prisma broke the interface of the tracing helper with the v6 major update. This means that if you use Prisma 5 with the v6 instrumentation (or vice versa) Prisma just blows up, because tries to call methods on the helper that no longer exist.\n    // Because we actually want to use the v6 instrumentation and not blow up in Prisma 5 user's faces, what we're doing here is backfilling the v5 method (`createEngineSpan`) with a noop so that no longer crashes when it attempts to call that function.\n    // We still won't fully emit all the spans, but this could potentially be implemented in the future.\n    const prismaTracingHelper = getPrismaTracingHelper();\n\n    let emittedWarning = false;\n\n    if (isPrismaV6TracingHelper(prismaTracingHelper)) {\n      (prismaTracingHelper as CompatibilityLayerTraceHelper).createEngineSpan = () => {\n        consoleSandbox(() => {\n          if (!emittedWarning) {\n            emittedWarning = true;\n            // eslint-disable-next-line no-console\n            console.warn(\n              '[Sentry] The Sentry SDK supports tracing with Prisma version 5 only with limited capabilities. For full tracing capabilities pass `prismaInstrumentation` for version 5 to the Sentry `prismaIntegration`. Read more: https://docs.sentry.io/platforms/javascript/guides/node/configuration/integrations/prisma/',\n            );\n          }\n        });\n      };\n    }\n  }\n}\n\nexport const instrumentPrisma = generateInstrumentOnce<{ prismaInstrumentation?: Instrumentation }>(\n  INTEGRATION_NAME,\n  options => {\n    // Use a passed instrumentation instance to support older Prisma versions\n    if (options?.prismaInstrumentation) {\n      return options.prismaInstrumentation;\n    }\n\n    return new SentryPrismaInteropInstrumentation();\n  },\n);\n\n/**\n * Adds Sentry tracing instrumentation for the [prisma](https://www.npmjs.com/package/prisma) library.\n * For more information, see the [`prismaIntegration` documentation](https://docs.sentry.io/platforms/javascript/guides/node/configuration/integrations/prisma/).\n *\n * NOTE: By default, this integration works with Prisma version 6.\n * To get performance instrumentation for other Prisma versions,\n * 1. Install the `@prisma/instrumentation` package with the desired version.\n * 1. Pass a `new PrismaInstrumentation()` instance as exported from `@prisma/instrumentation` to the `prismaInstrumentation` option of this integration:\n *\n *    ```js\n *    import { PrismaInstrumentation } from '@prisma/instrumentation'\n *\n *    Sentry.init({\n *      integrations: [\n *        prismaIntegration({\n *          // Override the default instrumentation that Sentry uses\n *          prismaInstrumentation: new PrismaInstrumentation()\n *        })\n *      ]\n *    })\n *    ```\n *\n *    The passed instrumentation instance will override the default instrumentation instance the integration would use, while the `prismaIntegration` will still ensure data compatibility for the various Prisma versions.\n * 1. Depending on your Prisma version (prior to version 6), add `previewFeatures = [\"tracing\"]` to the client generator block of your Prisma schema:\n *\n *    ```\n *    generator client {\n *      provider = \"prisma-client-js\"\n *      previewFeatures = [\"tracing\"]\n *    }\n *    ```\n */\nexport const prismaIntegration = defineIntegration(\n  ({\n    prismaInstrumentation,\n  }: {\n    /**\n     * Overrides the instrumentation used by the Sentry SDK with the passed in instrumentation instance.\n     *\n     * NOTE: By default, the Sentry SDK uses the Prisma v6 instrumentation. Use this option if you need performance instrumentation different Prisma versions.\n     *\n     * For more information refer to the documentation of `prismaIntegration()` or see https://docs.sentry.io/platforms/javascript/guides/node/configuration/integrations/prisma/\n     */\n    prismaInstrumentation?: Instrumentation;\n  } = {}) => {\n    return {\n      name: INTEGRATION_NAME,\n      setupOnce() {\n        instrumentPrisma({ prismaInstrumentation });\n      },\n      setup(client) {\n        // If no tracing helper exists, we skip any work here\n        // this means that prisma is not being used\n        if (!getPrismaTracingHelper()) {\n          return;\n        }\n\n        client.on('spanStart', span => {\n          const spanJSON = spanToJSON(span);\n          if (spanJSON.description?.startsWith('prisma:')) {\n            span.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN, 'auto.db.otel.prisma');\n          }\n\n          // Make sure we use the query text as the span name, for ex. SELECT * FROM \"User\" WHERE \"id\" = $1\n          if (spanJSON.description === 'prisma:engine:db_query' && spanJSON.data['db.query.text']) {\n            span.updateName(spanJSON.data['db.query.text'] as string);\n          }\n\n          // In Prisma v5.22+, the `db.system` attribute is automatically set\n          // On older versions, this is missing, so we add it here\n          if (spanJSON.description === 'prisma:engine:db_query' && !spanJSON.data['db.system']) {\n            span.setAttribute('db.system', 'prisma');\n          }\n        });\n      },\n    };\n  },\n);\n"], "names": ["PrismaInstrumentation", "consoleSandbox", "generateInstrumentOnce", "defineIntegration", "spanToJSON", "SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN"], "mappings": ";;;;;;AAOA,MAAM,gBAAA,GAAmB,QAAQ;AAIjC,SAAS,uBAAuB,CAAC,MAAM,EAA4C;IACjF,OAAO,CAAC,CAAC,MAAA,IAAU,OAAO,MAAA,KAAW,QAAA,IAAY,qBAAA,IAAyB,MAAM;AAClF;AAEA,SAAS,sBAAsB,GAAwB;IACrD,MAAM,2BAAA,GAA8B,AAAC,UAAA,CAAuC,sBAAsB;IAClG,MAAM,mBAAA,GACJ,2BAAA,IACA,OAAO,2BAAA,KAAgC,QAAA,IACvC,YAAY,8BACR,2BAA2B,CAAC,MAAA,GAC5B,SAAS;IAEf,OAAO,mBAAmB;AAC5B;AAEA,MAAM,kCAAA,SAA2CA,gBAAAA,qBAAA,CAAsB;IAC9D,WAAW,EAAG;QACnB,KAAK,EAAE;IACX;IAES,MAAM,GAAS;QACpB,KAAK,CAAC,MAAM,EAAE;QAElB,wiBAAA;QACA,yPAAA;QACA,oGAAA;QACI,MAAM,mBAAA,GAAsB,sBAAsB,EAAE;QAEpD,IAAI,cAAA,GAAiB,KAAK;QAE1B,IAAI,uBAAuB,CAAC,mBAAmB,CAAC,EAAE;YAC/C,oBAAsD,gBAAA,GAAmB,MAAM;gBAC9EC,KAAAA,cAAc,CAAC,MAAM;oBACnB,IAAI,CAAC,cAAc,EAAE;wBACnB,cAAA,GAAiB,IAAI;wBACjC,sCAAA;wBACY,OAAO,CAAC,IAAI,CACV,kTAAkT;oBAEhU;gBACA,CAAS,CAAC;YACV,CAAO;QACP;IACA;AACA;AAEO,MAAM,gBAAA,GAAmBC,SAAAA,sBAAsB,CACpD,gBAAgB,GAChB,WAAW;IACb,yEAAA;IACI,IAAI,OAAO,EAAE,qBAAqB,EAAE;QAClC,OAAO,OAAO,CAAC,qBAAqB;IAC1C;IAEI,OAAO,IAAI,kCAAkC,EAAE;AACnD,CAAG;AAGH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA+BA,GACO,MAAM,iBAAA,GAAoBC,KAAAA,iBAAiB,CAChD,CAAC,EACC,qBAAqB,EACzB,GASM,CAAA,CAAE,KAAK;IACT,OAAO;QACL,IAAI,EAAE,gBAAgB;QACtB,SAAS,GAAG;YACV,gBAAgB,CAAC;gBAAE,qBAAA;YAAA,CAAuB,CAAC;QACnD,CAAO;QACD,KAAK,EAAC,MAAM,EAAE;YACpB,qDAAA;YACA,2CAAA;YACQ,IAAI,CAAC,sBAAsB,EAAE,EAAE;gBAC7B;YACV;YAEQ,MAAM,CAAC,EAAE,CAAC,WAAW,GAAE,QAAQ;gBAC7B,MAAM,QAAA,GAAWC,KAAAA,UAAU,CAAC,IAAI,CAAC;gBACjC,IAAI,QAAQ,CAAC,WAAW,EAAE,UAAU,CAAC,SAAS,CAAC,EAAE;oBAC/C,IAAI,CAAC,YAAY,CAACC,KAAAA,gCAAgC,EAAE,qBAAqB,CAAC;gBACtF;gBAEA,iGAAA;gBACU,IAAI,QAAQ,CAAC,WAAA,KAAgB,wBAAA,IAA4B,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE;oBACvF,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAA,EAAY;gBACrE;gBAEA,mEAAA;gBACA,wDAAA;gBACU,IAAI,QAAQ,CAAC,WAAA,KAAgB,wBAAA,IAA4B,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;oBACpF,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,QAAQ,CAAC;gBACpD;YACA,CAAS,CAAC;QACV,CAAO;IACP,CAAK;AACL,CAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3022, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/integrations/tracing/hapi/index.ts"], "sourcesContent": ["import { HapiInstrumentation } from '@opentelemetry/instrumentation-hapi';\nimport type { IntegrationFn, Span } from '@sentry/core';\nimport {\n  captureException,\n  debug,\n  defineIntegration,\n  getClient,\n  getDefaultIsolationScope,\n  getIsolationScope,\n  SDK_VERSION,\n  SEMANTIC_ATTRIBUTE_SENTRY_OP,\n  SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,\n  spanToJSON,\n} from '@sentry/core';\nimport { ensureIsWrapped, generateInstrumentOnce } from '@sentry/node-core';\nimport { DEBUG_BUILD } from '../../../debug-build';\nimport type { Request, RequestEvent, Server } from './types';\n\nconst INTEGRATION_NAME = 'Hapi';\n\nexport const instrumentHapi = generateInstrumentOnce(INTEGRATION_NAME, () => new HapiInstrumentation());\n\nconst _hapiIntegration = (() => {\n  return {\n    name: INTEGRATION_NAME,\n    setupOnce() {\n      instrumentHapi();\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Adds Sentry tracing instrumentation for [Hapi](https://hapi.dev/).\n *\n * If you also want to capture errors, you need to call `setupHapiErrorHandler(server)` after you set up your server.\n *\n * For more information, see the [hapi documentation](https://docs.sentry.io/platforms/javascript/guides/hapi/).\n *\n * @example\n * ```javascript\n * const Sentry = require('@sentry/node');\n *\n * Sentry.init({\n *   integrations: [Sentry.hapiIntegration()],\n * })\n * ```\n */\nexport const hapiIntegration = defineIntegration(_hapiIntegration);\n\nfunction isErrorEvent(event: unknown): event is RequestEvent {\n  return !!(event && typeof event === 'object' && 'error' in event && event.error);\n}\n\nfunction sendErrorToSentry(errorData: object): void {\n  captureException(errorData, {\n    mechanism: {\n      type: 'hapi',\n      handled: false,\n      data: {\n        function: 'hapiErrorPlugin',\n      },\n    },\n  });\n}\n\nexport const hapiErrorPlugin = {\n  name: 'SentryHapiErrorPlugin',\n  version: SDK_VERSION,\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  register: async function (serverArg: Record<any, any>) {\n    const server = serverArg as unknown as Server;\n\n    server.events.on({ name: 'request', channels: ['error'] }, (request: Request, event: RequestEvent) => {\n      if (getIsolationScope() !== getDefaultIsolationScope()) {\n        const route = request.route;\n        if (route.path) {\n          getIsolationScope().setTransactionName(`${route.method.toUpperCase()} ${route.path}`);\n        }\n      } else {\n        DEBUG_BUILD &&\n          debug.warn('Isolation scope is still the default isolation scope - skipping setting transactionName');\n      }\n\n      if (isErrorEvent(event)) {\n        sendErrorToSentry(event.error);\n      }\n    });\n  },\n};\n\n/**\n * Add a Hapi plugin to capture errors to Sentry.\n *\n * @param server The Hapi server to attach the error handler to\n *\n * @example\n * ```javascript\n * const Sentry = require('@sentry/node');\n * const Hapi = require('@hapi/hapi');\n *\n * const init = async () => {\n *   const server = Hapi.server();\n *\n *   // all your routes here\n *\n *   await Sentry.setupHapiErrorHandler(server);\n *\n *   await server.start();\n * };\n * ```\n */\nexport async function setupHapiErrorHandler(server: Server): Promise<void> {\n  await server.register(hapiErrorPlugin);\n\n  // Sadly, middleware spans do not go through `requestHook`, so we handle those here\n  // We register this hook in this method, because if we register it in the integration `setup`,\n  // it would always run even for users that are not even using hapi\n  const client = getClient();\n  if (client) {\n    client.on('spanStart', span => {\n      addHapiSpanAttributes(span);\n    });\n  }\n\n  // eslint-disable-next-line @typescript-eslint/unbound-method\n  ensureIsWrapped(server.register, 'hapi');\n}\n\nfunction addHapiSpanAttributes(span: Span): void {\n  const attributes = spanToJSON(span).data;\n\n  // this is one of: router, plugin, server.ext\n  const type = attributes['hapi.type'];\n\n  // If this is already set, or we have no Hapi span, no need to process again...\n  if (attributes[SEMANTIC_ATTRIBUTE_SENTRY_OP] || !type) {\n    return;\n  }\n\n  span.setAttributes({\n    [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.http.otel.hapi',\n    [SEMANTIC_ATTRIBUTE_SENTRY_OP]: `${type}.hapi`,\n  });\n}\n"], "names": ["generateInstrumentOnce", "HapiInstrumentation", "defineIntegration", "captureException", "SDK_VERSION", "getIsolationScope", "getDefaultIsolationScope", "DEBUG_BUILD", "debug", "getClient", "ensureIsWrapped", "spanToJSON", "SEMANTIC_ATTRIBUTE_SENTRY_OP", "SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN"], "mappings": ";;;;;;;AAkBA,MAAM,gBAAA,GAAmB,MAAM;AAExB,MAAM,cAAA,GAAiBA,SAAAA,sBAAsB,CAAC,gBAAgB,EAAE,IAAM,IAAIC,oBAAAA,mBAAmB,EAAE;AAEtG,MAAM,gBAAA,GAAoB,MAAM;IAC9B,OAAO;QACL,IAAI,EAAE,gBAAgB;QACtB,SAAS,GAAG;YACV,cAAc,EAAE;QACtB,CAAK;IACL,CAAG;AACH,CAAC,CAAA;AAED;;;;;;;;;;;;;;;CAeA,SACa,eAAA,GAAkBC,KAAAA,iBAAiB,CAAC,gBAAgB;AAEjE,SAAS,YAAY,CAAC,KAAK,EAAkC;IAC3D,OAAO,CAAC,CAAA,CAAE,KAAA,IAAS,OAAO,KAAA,KAAU,QAAA,IAAY,WAAW,KAAA,IAAS,KAAK,CAAC,KAAK,CAAC;AAClF;AAEA,SAAS,iBAAiB,CAAC,SAAS,EAAgB;IAClDC,KAAAA,gBAAgB,CAAC,SAAS,EAAE;QAC1B,SAAS,EAAE;YACT,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,KAAK;YACd,IAAI,EAAE;gBACJ,QAAQ,EAAE,iBAAiB;YACnC,CAAO;QACP,CAAK;IACL,CAAG,CAAC;AACJ;AAEO,MAAM,kBAAkB;IAC7B,IAAI,EAAE,uBAAuB;IAC7B,OAAO,EAAEC,KAAAA,WAAW;IACtB,8DAAA;IACE,QAAQ,EAAE,eAAgB,SAAS,EAAoB;QACrD,MAAM,MAAA,GAAS,SAAA;QAEf,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;YAAE,IAAI,EAAE,SAAS;YAAE,QAAQ,EAAE;gBAAC,OAAO;aAAA;QAAA,CAAG,EAAE,CAAC,OAAO,EAAW,KAAK,KAAmB;YACpG,IAAIC,KAAAA,iBAAiB,OAAOC,KAAAA,wBAAwB,EAAE,EAAE;gBACtD,MAAM,KAAA,GAAQ,OAAO,CAAC,KAAK;gBAC3B,IAAI,KAAK,CAAC,IAAI,EAAE;oBACdD,KAAAA,iBAAiB,EAAE,CAAC,kBAAkB,CAAC,CAAC,EAAA,KAAA,CAAA,MAAA,CAAA,WAAA,EAAA,CAAA,CAAA,EAAA,KAAA,CAAA,IAAA,CAAA,CAAA,CAAA;gBACA;YACA,CAAA,MAAA;gBACAE,WAAAA,WAAA,IACAC,KAAAA,KAAA,CAAA,IAAA,CAAA,yFAAA,CAAA;YACA;YAEA,IAAA,YAAA,CAAA,KAAA,CAAA,EAAA;gBACA,iBAAA,CAAA,KAAA,CAAA,KAAA,CAAA;YACA;QACA,CAAA,CAAA;IACA,CAAA;AACA;AAEA;;;;;;;;;;;;;;;;;;;;CAoBA,GACA,eAAA,qBAAA,CAAA,MAAA,EAAA;IACA,MAAA,MAAA,CAAA,QAAA,CAAA,eAAA,CAAA;IAEA,mFAAA;IACA,8FAAA;IACA,kEAAA;IACA,MAAA,MAAA,GAAAC,KAAAA,SAAA,EAAA;IACA,IAAA,MAAA,EAAA;QACA,MAAA,CAAA,EAAA,CAAA,WAAA,GAAA,IAAA,IAAA;YACA,qBAAA,CAAA,IAAA,CAAA;QACA,CAAA,CAAA;IACA;IAEA,6DAAA;IACAC,SAAAA,eAAA,CAAA,MAAA,CAAA,QAAA,EAAA,MAAA,CAAA;AACA;AAEA,SAAA,qBAAA,CAAA,IAAA,EAAA;IACA,MAAA,UAAA,GAAAC,KAAAA,UAAA,CAAA,IAAA,CAAA,CAAA,IAAA;IAEA,6CAAA;IACA,MAAA,IAAA,GAAA,UAAA,CAAA,WAAA,CAAA;IAEA,+EAAA;IACA,IAAA,UAAA,CAAAC,KAAAA,4BAAA,CAAA,IAAA,CAAA,IAAA,EAAA;QACA;IACA;IAEA,IAAA,CAAA,aAAA,CAAA;QACA,CAAAC,KAAAA,gCAAA,CAAA,EAAA,qBAAA;QACA,CAAAD,KAAAA,4BAAA,CAAA,EAAA,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA;IACA,CAAA,CAAA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3151, "column": 0}, "map": {"version": 3, "file": "koa.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/integrations/tracing/koa.ts"], "sourcesContent": ["import type { KoaInstrumentationConfig, KoaLayerType } from '@opentelemetry/instrumentation-koa';\nimport { KoaInstrumentation } from '@opentelemetry/instrumentation-koa';\nimport { ATTR_HTTP_ROUTE } from '@opentelemetry/semantic-conventions';\nimport type { IntegrationFn } from '@sentry/core';\nimport {\n  captureException,\n  debug,\n  defineIntegration,\n  getDefaultIsolationScope,\n  getIsolationScope,\n  SEMANTIC_ATTRIBUTE_SENTRY_OP,\n  spanToJSON,\n} from '@sentry/core';\nimport { addOriginToSpan, ensureIsWrapped, generateInstrumentOnce } from '@sentry/node-core';\nimport { DEBUG_BUILD } from '../../debug-build';\n\ninterface KoaOptions {\n  /**\n   * Ignore layers of specified types\n   */\n  ignoreLayersType?: Array<'middleware' | 'router'>;\n}\n\nconst INTEGRATION_NAME = 'Koa';\n\nexport const instrumentKoa = generateInstrumentOnce(\n  INTEGRATION_NAME,\n  KoaInstrumentation,\n  (options: KoaOptions = {}) => {\n    return {\n      ignoreLayersType: options.ignoreLayersType as KoaLayerType[],\n      requestHook(span, info) {\n        addOriginToSpan(span, 'auto.http.otel.koa');\n\n        const attributes = spanToJSON(span).data;\n\n        // this is one of: middleware, router\n        const type = attributes['koa.type'];\n        if (type) {\n          span.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_OP, `${type}.koa`);\n        }\n\n        // Also update the name\n        const name = attributes['koa.name'];\n        if (typeof name === 'string') {\n          // Somehow, name is sometimes `''` for middleware spans\n          // See: https://github.com/open-telemetry/opentelemetry-js-contrib/issues/2220\n          span.updateName(name || '< unknown >');\n        }\n\n        if (getIsolationScope() === getDefaultIsolationScope()) {\n          DEBUG_BUILD && debug.warn('Isolation scope is default isolation scope - skipping setting transactionName');\n          return;\n        }\n        const route = attributes[ATTR_HTTP_ROUTE];\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n        const method = info.context?.request?.method?.toUpperCase() || 'GET';\n        if (route) {\n          getIsolationScope().setTransactionName(`${method} ${route}`);\n        }\n      },\n    } satisfies KoaInstrumentationConfig;\n  },\n);\n\nconst _koaIntegration = ((options: KoaOptions = {}) => {\n  return {\n    name: INTEGRATION_NAME,\n    setupOnce() {\n      instrumentKoa(options);\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Adds Sentry tracing instrumentation for [Koa](https://koajs.com/).\n *\n * If you also want to capture errors, you need to call `setupKoaErrorHandler(app)` after you set up your Koa server.\n *\n * For more information, see the [koa documentation](https://docs.sentry.io/platforms/javascript/guides/koa/).\n *\n * @param {KoaOptions} options Configuration options for the Koa integration.\n *\n * @example\n * ```javascript\n * const Sentry = require('@sentry/node');\n *\n * Sentry.init({\n *   integrations: [Sentry.koaIntegration()],\n * })\n * ```\n *\n * @example\n * ```javascript\n * // To ignore middleware spans\n * const Sentry = require('@sentry/node');\n *\n * Sentry.init({\n *   integrations: [\n *     Sentry.koaIntegration({\n *       ignoreLayersType: ['middleware']\n *     })\n *   ],\n * })\n * ```\n */\nexport const koaIntegration = defineIntegration(_koaIntegration);\n\n/**\n * Add an Koa error handler to capture errors to Sentry.\n *\n * The error handler must be before any other middleware and after all controllers.\n *\n * @param app The Express instances\n * @param options {ExpressHandlerOptions} Configuration options for the handler\n *\n * @example\n * ```javascript\n * const Sentry = require('@sentry/node');\n * const Koa = require(\"koa\");\n *\n * const app = new Koa();\n *\n * Sentry.setupKoaErrorHandler(app);\n *\n * // Add your routes, etc.\n *\n * app.listen(3000);\n * ```\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport const setupKoaErrorHandler = (app: { use: (arg0: (ctx: any, next: any) => Promise<void>) => void }): void => {\n  app.use(async (ctx, next) => {\n    try {\n      await next();\n    } catch (error) {\n      captureException(error);\n      throw error;\n    }\n  });\n\n  ensureIsWrapped(app.use, 'koa');\n};\n"], "names": ["generateInstrumentOnce", "KoaInstrumentation", "addOriginToSpan", "spanToJSON", "SEMANTIC_ATTRIBUTE_SENTRY_OP", "getIsolationScope", "getDefaultIsolationScope", "DEBUG_BUILD", "debug", "ATTR_HTTP_ROUTE", "defineIntegration", "captureException", "ensureIsWrapped"], "mappings": ";;;;;;;;AAuBA,MAAM,gBAAA,GAAmB,KAAK;AAEvB,MAAM,aAAA,GAAgBA,SAAAA,sBAAsB,CACjD,gBAAgB,EAChBC,mBAAAA,kBAAkB,EAClB,CAAC,OAAO,GAAe,CAAA,CAAE,KAAK;IAC5B,OAAO;QACL,gBAAgB,EAAE,OAAO,CAAC,gBAAA;QAC1B,WAAW,EAAC,IAAI,EAAE,IAAI,EAAE;YACtBC,SAAAA,eAAe,CAAC,IAAI,EAAE,oBAAoB,CAAC;YAE3C,MAAM,aAAaC,KAAAA,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI;YAEhD,qCAAA;YACQ,MAAM,IAAA,GAAO,UAAU,CAAC,UAAU,CAAC;YACnC,IAAI,IAAI,EAAE;gBACR,IAAI,CAAC,YAAY,CAACC,KAAAA,4BAA4B,EAAE,CAAC,EAAA,IAAA,CAAA,IAAA,CAAA,CAAA;YACA;YAEA,uBAAA;YACA,MAAA,IAAA,GAAA,UAAA,CAAA,UAAA,CAAA;YACA,IAAA,OAAA,IAAA,KAAA,QAAA,EAAA;gBACA,uDAAA;gBACA,8EAAA;gBACA,IAAA,CAAA,UAAA,CAAA,IAAA,IAAA,aAAA,CAAA;YACA;YAEA,IAAAC,KAAAA,iBAAA,EAAA,KAAAC,KAAAA,wBAAA,EAAA,EAAA;gBACAC,WAAAA,WAAA,IAAAC,KAAAA,KAAA,CAAA,IAAA,CAAA,+EAAA,CAAA;gBACA;YACA;YACA,MAAA,KAAA,GAAA,UAAA,CAAAC,oBAAAA,eAAA,CAAA;YACA,sEAAA;YACA,MAAA,MAAA,GAAA,IAAA,CAAA,OAAA,EAAA,OAAA,EAAA,MAAA,EAAA,WAAA,EAAA,IAAA,KAAA;YACA,IAAA,KAAA,EAAA;gBACAJ,KAAAA,iBAAA,EAAA,CAAA,kBAAA,CAAA,CAAA,EAAA,MAAA,CAAA,CAAA,EAAA,KAAA,CAAA,CAAA,CAAA;YACA;QACA,CAAA;IACA,CAAA;AACA,CAAA;AAGA,MAAA,eAAA,GAAA,CAAA,OAAA,GAAA,CAAA,CAAA,KAAA;IACA,OAAA;QACA,IAAA,EAAA,gBAAA;QACA,SAAA,GAAA;YACA,aAAA,CAAA,OAAA,CAAA;QACA,CAAA;IACA,CAAA;AACA,CAAA,CAAA;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA+BA,GACA,MAAA,cAAA,GAAAK,KAAAA,iBAAA,CAAA,eAAA;AAEA;;;;;;;;;;;;;;;;;;;;;CAqBA,GACA,8DAAA;AACA,MAAA,oBAAA,GAAA,CAAA,GAAA,KAAA;IACA,GAAA,CAAA,GAAA,CAAA,OAAA,GAAA,EAAA,IAAA,KAAA;QACA,IAAA;YACA,MAAA,IAAA,EAAA;QACA,CAAA,CAAA,OAAA,KAAA,EAAA;YACAC,KAAAA,gBAAA,CAAA,KAAA,CAAA;YACA,MAAA,KAAA;QACA;IACA,CAAA,CAAA;IAEAC,SAAAA,eAAA,CAAA,GAAA,CAAA,GAAA,EAAA,KAAA,CAAA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3272, "column": 0}, "map": {"version": 3, "file": "connect.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/integrations/tracing/connect.ts"], "sourcesContent": ["import { ConnectInstrumentation } from '@opentelemetry/instrumentation-connect';\nimport type { IntegrationFn, Span } from '@sentry/core';\nimport {\n  captureException,\n  defineIntegration,\n  getClient,\n  SEMANTIC_ATTRIBUTE_SENTRY_OP,\n  SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,\n  spanToJSON,\n} from '@sentry/core';\nimport { ensureIsWrapped, generateInstrumentOnce } from '@sentry/node-core';\n\ntype ConnectApp = {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  use: (middleware: any) => void;\n};\n\nconst INTEGRATION_NAME = 'Connect';\n\nexport const instrumentConnect = generateInstrumentOnce(INTEGRATION_NAME, () => new ConnectInstrumentation());\n\nconst _connectIntegration = (() => {\n  return {\n    name: INTEGRATION_NAME,\n    setupOnce() {\n      instrumentConnect();\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Adds Sentry tracing instrumentation for [Connect](https://github.com/senchalabs/connect/).\n *\n * If you also want to capture errors, you need to call `setupConnectErrorHandler(app)` after you initialize your connect app.\n *\n * For more information, see the [connect documentation](https://docs.sentry.io/platforms/javascript/guides/connect/).\n *\n * @example\n * ```javascript\n * const Sentry = require('@sentry/node');\n *\n * Sentry.init({\n *   integrations: [Sentry.connectIntegration()],\n * })\n * ```\n */\nexport const connectIntegration = defineIntegration(_connectIntegration);\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction connectErrorMiddleware(err: any, req: any, res: any, next: any): void {\n  captureException(err);\n  next(err);\n}\n\n/**\n * Add a Connect middleware to capture errors to Sentry.\n *\n * @param app The Connect app to attach the error handler to\n *\n * @example\n * ```javascript\n * const Sentry = require('@sentry/node');\n * const connect = require(\"connect\");\n *\n * const app = connect();\n *\n * Sentry.setupConnectErrorHandler(app);\n *\n * // Add you connect routes here\n *\n * app.listen(3000);\n * ```\n */\nexport const setupConnectErrorHandler = (app: ConnectApp): void => {\n  app.use(connectErrorMiddleware);\n\n  // Sadly, ConnectInstrumentation has no requestHook, so we need to add the attributes here\n  // We register this hook in this method, because if we register it in the integration `setup`,\n  // it would always run even for users that are not even using connect\n  const client = getClient();\n  if (client) {\n    client.on('spanStart', span => {\n      addConnectSpanAttributes(span);\n    });\n  }\n\n  ensureIsWrapped(app.use, 'connect');\n};\n\nfunction addConnectSpanAttributes(span: Span): void {\n  const attributes = spanToJSON(span).data;\n\n  // this is one of: middleware, request_handler\n  const type = attributes['connect.type'];\n\n  // If this is already set, or we have no connect span, no need to process again...\n  if (attributes[SEMANTIC_ATTRIBUTE_SENTRY_OP] || !type) {\n    return;\n  }\n\n  span.setAttributes({\n    [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.http.otel.connect',\n    [SEMANTIC_ATTRIBUTE_SENTRY_OP]: `${type}.connect`,\n  });\n\n  // Also update the name, we don't need the \"middleware - \" prefix\n  const name = attributes['connect.name'];\n  if (typeof name === 'string') {\n    span.updateName(name);\n  }\n}\n"], "names": ["generateInstrumentOnce", "ConnectInstrumentation", "defineIntegration", "captureException", "getClient", "ensureIsWrapped", "spanToJSON", "SEMANTIC_ATTRIBUTE_SENTRY_OP", "SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN"], "mappings": ";;;;;;AAiBA,MAAM,gBAAA,GAAmB,SAAS;AAE3B,MAAM,iBAAA,GAAoBA,SAAAA,sBAAsB,CAAC,gBAAgB,EAAE,IAAM,IAAIC,uBAAAA,sBAAsB,EAAE;AAE5G,MAAM,mBAAA,GAAuB,MAAM;IACjC,OAAO;QACL,IAAI,EAAE,gBAAgB;QACtB,SAAS,GAAG;YACV,iBAAiB,EAAE;QACzB,CAAK;IACL,CAAG;AACH,CAAC,CAAA;AAED;;;;;;;;;;;;;;;CAeA,SACa,kBAAA,GAAqBC,KAAAA,iBAAiB,CAAC,mBAAmB;AAEvE,8DAAA;AACA,SAAS,sBAAsB,CAAC,GAAG,EAAO,GAAG,EAAO,GAAG,EAAO,IAAI,EAAa;IAC7EC,KAAAA,gBAAgB,CAAC,GAAG,CAAC;IACrB,IAAI,CAAC,GAAG,CAAC;AACX;AAEA;;;;;;;;;;;;;;;;;;CAkBA,GACO,MAAM,wBAAA,GAA2B,CAAC,GAAG,KAAuB;IACjE,GAAG,CAAC,GAAG,CAAC,sBAAsB,CAAC;IAEjC,0FAAA;IACA,8FAAA;IACA,qEAAA;IACE,MAAM,MAAA,GAASC,KAAAA,SAAS,EAAE;IAC1B,IAAI,MAAM,EAAE;QACV,MAAM,CAAC,EAAE,CAAC,WAAW,GAAE,QAAQ;YAC7B,wBAAwB,CAAC,IAAI,CAAC;QACpC,CAAK,CAAC;IACN;IAEEC,SAAAA,eAAe,CAAC,GAAG,CAAC,GAAG,EAAE,SAAS,CAAC;AACrC;AAEA,SAAS,wBAAwB,CAAC,IAAI,EAAc;IAClD,MAAM,aAAaC,KAAAA,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI;IAE1C,8CAAA;IACE,MAAM,IAAA,GAAO,UAAU,CAAC,cAAc,CAAC;IAEzC,kFAAA;IACE,IAAI,UAAU,CAACC,KAAAA,4BAA4B,CAAA,IAAK,CAAC,IAAI,EAAE;QACrD;IACJ;IAEE,IAAI,CAAC,aAAa,CAAC;QACjB,CAACC,KAAAA,gCAAgC,CAAA,EAAG,wBAAwB;QAC5D,CAACD,KAAAA,4BAA4B,CAAA,EAAG,CAAC,EAAA,IAAA,CAAA,QAAA,CAAA;IACA,CAAA,CAAA;IAEA,iEAAA;IACA,MAAA,IAAA,GAAA,UAAA,CAAA,cAAA,CAAA;IACA,IAAA,OAAA,IAAA,KAAA,QAAA,EAAA;QACA,IAAA,CAAA,UAAA,CAAA,IAAA,CAAA;IACA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3366, "column": 0}, "map": {"version": 3, "file": "knex.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/integrations/tracing/knex.ts"], "sourcesContent": ["import { KnexInstrumentation } from '@opentelemetry/instrumentation-knex';\nimport type { IntegrationFn } from '@sentry/core';\nimport { defineIntegration, SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN, spanToJSON } from '@sentry/core';\nimport { generateInstrumentOnce, instrumentWhenWrapped } from '@sentry/node-core';\n\nconst INTEGRATION_NAME = 'Knex';\n\nexport const instrumentKnex = generateInstrumentOnce(\n  INTEGRATION_NAME,\n  () => new KnexInstrumentation({ requireParentSpan: true }),\n);\n\nconst _knexIntegration = (() => {\n  let instrumentationWrappedCallback: undefined | ((callback: () => void) => void);\n\n  return {\n    name: INTEGRATION_NAME,\n    setupOnce() {\n      const instrumentation = instrumentKnex();\n      instrumentationWrappedCallback = instrumentWhenWrapped(instrumentation);\n    },\n\n    setup(client) {\n      instrumentationWrappedCallback?.(() =>\n        client.on('spanStart', span => {\n          const { data } = spanToJSON(span);\n          // knex.version is always set in the span data\n          // https://github.com/open-telemetry/opentelemetry-js-contrib/blob/0309caeafc44ac9cb13a3345b790b01b76d0497d/plugins/node/opentelemetry-instrumentation-knex/src/instrumentation.ts#L138\n          if ('knex.version' in data) {\n            span.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN, 'auto.db.otel.knex');\n          }\n        }),\n      );\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Knex integration\n *\n * Capture tracing data for [Knex](https://knexjs.org/).\n *\n * @example\n * ```javascript\n * import * as Sentry from '@sentry/node';\n *\n * Sentry.init({\n *  integrations: [Sentry.knexIntegration()],\n * });\n * ```\n */\nexport const knexIntegration = defineIntegration(_knexIntegration);\n"], "names": ["generateInstrumentOnce", "KnexInstrumentation", "instrumentWhenWrapped", "spanToJSON", "SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN", "defineIntegration"], "mappings": ";;;;;;AAKA,MAAM,gBAAA,GAAmB,MAAM;AAExB,MAAM,cAAA,GAAiBA,SAAAA,sBAAsB,CAClD,gBAAgB,EAChB,IAAM,IAAIC,oBAAAA,mBAAmB,CAAC;QAAE,iBAAiB,EAAE,IAAA;IAAA,CAAM,CAAC;AAG5D,MAAM,gBAAA,GAAoB,MAAM;IAC9B,IAAI,8BAA8B;IAElC,OAAO;QACL,IAAI,EAAE,gBAAgB;QACtB,SAAS,GAAG;YACV,MAAM,eAAA,GAAkB,cAAc,EAAE;YACxC,8BAAA,GAAiCC,SAAAA,qBAAqB,CAAC,eAAe,CAAC;QAC7E,CAAK;QAED,KAAK,EAAC,MAAM,EAAE;YACZ,8BAA8B,GAAG,IAC/B,MAAM,CAAC,EAAE,CAAC,WAAW,GAAE,QAAQ;oBAC7B,MAAM,EAAE,IAAA,EAAK,GAAIC,KAAAA,UAAU,CAAC,IAAI,CAAC;oBAC3C,8CAAA;oBACA,uLAAA;oBACU,IAAI,cAAA,IAAkB,IAAI,EAAE;wBAC1B,IAAI,CAAC,YAAY,CAACC,KAAAA,gCAAgC,EAAE,mBAAmB,CAAC;oBACpF;gBACA,CAAS,CAAC;QAEV,CAAK;IACL,CAAG;AACH,CAAC,CAAA;AAED;;;;;;;;;;;;;CAaA,SACa,eAAA,GAAkBC,KAAAA,iBAAiB,CAAC,gBAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3417, "column": 0}, "map": {"version": 3, "file": "tedious.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/integrations/tracing/tedious.ts"], "sourcesContent": ["import { TediousInstrumentation } from '@opentelemetry/instrumentation-tedious';\nimport type { IntegrationFn } from '@sentry/core';\nimport { defineIntegration, SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN, spanToJSON } from '@sentry/core';\nimport { generateInstrumentOnce, instrumentWhenWrapped } from '@sentry/node-core';\n\nconst TEDIUS_INSTRUMENTED_METHODS = new Set([\n  'callProcedure',\n  'execSql',\n  'execSqlBatch',\n  'execBulkLoad',\n  'prepare',\n  'execute',\n]);\n\nconst INTEGRATION_NAME = 'Tedious';\n\nexport const instrumentTedious = generateInstrumentOnce(INTEGRATION_NAME, () => new TediousInstrumentation({}));\n\nconst _tediousIntegration = (() => {\n  let instrumentationWrappedCallback: undefined | ((callback: () => void) => void);\n\n  return {\n    name: INTEGRATION_NAME,\n    setupOnce() {\n      const instrumentation = instrumentTedious();\n      instrumentationWrappedCallback = instrumentWhenWrapped(instrumentation);\n    },\n\n    setup(client) {\n      instrumentationWrappedCallback?.(() =>\n        client.on('spanStart', span => {\n          const { description, data } = spanToJSON(span);\n          // Tedius integration always set a span name and `db.system` attribute to `mssql`.\n          if (!description || data['db.system'] !== 'mssql') {\n            return;\n          }\n\n          const operation = description.split(' ')[0] || '';\n          if (TEDIUS_INSTRUMENTED_METHODS.has(operation)) {\n            span.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN, 'auto.db.otel.tedious');\n          }\n        }),\n      );\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Adds Sentry tracing instrumentation for the [tedious](https://www.npmjs.com/package/tedious) library.\n *\n * For more information, see the [`tediousIntegration` documentation](https://docs.sentry.io/platforms/javascript/guides/node/configuration/integrations/tedious/).\n *\n * @example\n * ```javascript\n * const Sentry = require('@sentry/node');\n *\n * Sentry.init({\n *  integrations: [Sentry.tediousIntegration()],\n * });\n * ```\n */\nexport const tediousIntegration = defineIntegration(_tediousIntegration);\n"], "names": ["generateInstrumentOnce", "TediousInstrumentation", "instrumentWhenWrapped", "spanToJSON", "SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN", "defineIntegration"], "mappings": ";;;;;;AAKA,MAAM,2BAAA,GAA8B,IAAI,GAAG,CAAC;IAC1C,eAAe;IACf,SAAS;IACT,cAAc;IACd,cAAc;IACd,SAAS;IACT,SAAS;CACV,CAAC;AAEF,MAAM,gBAAA,GAAmB,SAAS;MAErB,iBAAA,GAAoBA,SAAAA,sBAAsB,CAAC,gBAAgB,EAAE,IAAM,IAAIC,uBAAAA,sBAAsB,CAAC,CAAA,CAAE,CAAC;AAE9G,MAAM,mBAAA,GAAuB,MAAM;IACjC,IAAI,8BAA8B;IAElC,OAAO;QACL,IAAI,EAAE,gBAAgB;QACtB,SAAS,GAAG;YACV,MAAM,eAAA,GAAkB,iBAAiB,EAAE;YAC3C,8BAAA,GAAiCC,SAAAA,qBAAqB,CAAC,eAAe,CAAC;QAC7E,CAAK;QAED,KAAK,EAAC,MAAM,EAAE;YACZ,8BAA8B,GAAG,IAC/B,MAAM,CAAC,EAAE,CAAC,WAAW,GAAE,QAAQ;oBAC7B,MAAM,EAAE,WAAW,EAAE,IAAA,EAAA,GAASC,KAAAA,UAAU,CAAC,IAAI,CAAC;oBACxD,kFAAA;oBACU,IAAI,CAAC,WAAA,IAAe,IAAI,CAAC,WAAW,CAAA,KAAM,OAAO,EAAE;wBACjD;oBACZ;oBAEU,MAAM,SAAA,GAAY,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA,IAAK,EAAE;oBACjD,IAAI,2BAA2B,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;wBAC9C,IAAI,CAAC,YAAY,CAACC,KAAAA,gCAAgC,EAAE,sBAAsB,CAAC;oBACvF;gBACA,CAAS,CAAC;QAEV,CAAK;IACL,CAAG;AACH,CAAC,CAAA;AAED;;;;;;;;;;;;;CAaA,SACa,kBAAA,GAAqBC,KAAAA,iBAAiB,CAAC,mBAAmB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3477, "column": 0}, "map": {"version": 3, "file": "genericPool.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/integrations/tracing/genericPool.ts"], "sourcesContent": ["import { GenericPoolInstrumentation } from '@opentelemetry/instrumentation-generic-pool';\nimport type { IntegrationFn } from '@sentry/core';\nimport { defineIntegration, SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN, spanToJSON } from '@sentry/core';\nimport { generateInstrumentOnce, instrumentWhenWrapped } from '@sentry/node-core';\n\nconst INTEGRATION_NAME = 'GenericPool';\n\nexport const instrumentGenericPool = generateInstrumentOnce(INTEGRATION_NAME, () => new GenericPoolInstrumentation({}));\n\nconst _genericPoolIntegration = (() => {\n  let instrumentationWrappedCallback: undefined | ((callback: () => void) => void);\n\n  return {\n    name: INTEGRATION_NAME,\n    setupOnce() {\n      const instrumentation = instrumentGenericPool();\n      instrumentationWrappedCallback = instrumentWhenWrapped(instrumentation);\n    },\n\n    setup(client) {\n      instrumentationWrappedCallback?.(() =>\n        client.on('spanStart', span => {\n          const spanJSON = spanToJSON(span);\n\n          const spanDescription = spanJSON.description;\n\n          // typo in emitted span for version <= 0.38.0 of @opentelemetry/instrumentation-generic-pool\n          const isGenericPoolSpan =\n            spanDescription === 'generic-pool.aquire' || spanDescription === 'generic-pool.acquire';\n\n          if (isGenericPoolSpan) {\n            span.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN, 'auto.db.otel.generic_pool');\n          }\n        }),\n      );\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Adds Sentry tracing instrumentation for the [generic-pool](https://www.npmjs.com/package/generic-pool) library.\n *\n * For more information, see the [`genericPoolIntegration` documentation](https://docs.sentry.io/platforms/javascript/guides/node/configuration/integrations/genericpool/).\n *\n * @example\n * ```javascript\n * const Sentry = require('@sentry/node');\n *\n * Sentry.init({\n *  integrations: [Sentry.genericPoolIntegration()],\n * });\n * ```\n */\nexport const genericPoolIntegration = defineIntegration(_genericPoolIntegration);\n"], "names": ["generateInstrumentOnce", "GenericPoolInstrumentation", "instrumentWhenWrapped", "spanToJSON", "SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN", "defineIntegration"], "mappings": ";;;;;;AAKA,MAAM,gBAAA,GAAmB,aAAa;MAEzB,qBAAA,GAAwBA,SAAAA,sBAAsB,CAAC,gBAAgB,EAAE,IAAM,IAAIC,2BAAAA,0BAA0B,CAAC,CAAA,CAAE,CAAC;AAEtH,MAAM,uBAAA,GAA2B,MAAM;IACrC,IAAI,8BAA8B;IAElC,OAAO;QACL,IAAI,EAAE,gBAAgB;QACtB,SAAS,GAAG;YACV,MAAM,eAAA,GAAkB,qBAAqB,EAAE;YAC/C,8BAAA,GAAiCC,SAAAA,qBAAqB,CAAC,eAAe,CAAC;QAC7E,CAAK;QAED,KAAK,EAAC,MAAM,EAAE;YACZ,8BAA8B,GAAG,IAC/B,MAAM,CAAC,EAAE,CAAC,WAAW,GAAE,QAAQ;oBAC7B,MAAM,QAAA,GAAWC,KAAAA,UAAU,CAAC,IAAI,CAAC;oBAEjC,MAAM,eAAA,GAAkB,QAAQ,CAAC,WAAW;oBAEtD,4FAAA;oBACU,MAAM,iBAAA,GACJ,oBAAoB,qBAAA,IAAyB,eAAA,KAAoB,sBAAsB;oBAEzF,IAAI,iBAAiB,EAAE;wBACrB,IAAI,CAAC,YAAY,CAACC,KAAAA,gCAAgC,EAAE,2BAA2B,CAAC;oBAC5F;gBACA,CAAS,CAAC;QAEV,CAAK;IACL,CAAG;AACH,CAAC,CAAA;AAED;;;;;;;;;;;;;CAaA,SACa,sBAAA,GAAyBC,KAAAA,iBAAiB,CAAC,uBAAuB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3527, "column": 0}, "map": {"version": 3, "file": "dataloader.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/integrations/tracing/dataloader.ts"], "sourcesContent": ["import { DataloaderInstrumentation } from '@opentelemetry/instrumentation-dataloader';\nimport type { IntegrationFn } from '@sentry/core';\nimport {\n  defineIntegration,\n  SEMANTIC_ATTRIBUTE_SENTRY_OP,\n  SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,\n  spanToJSON,\n} from '@sentry/core';\nimport { generateInstrumentOnce, instrumentWhenWrapped } from '@sentry/node-core';\n\nconst INTEGRATION_NAME = 'Dataloader';\n\nexport const instrumentDataloader = generateInstrumentOnce(\n  INTEGRATION_NAME,\n  () =>\n    new DataloaderInstrumentation({\n      requireParentSpan: true,\n    }),\n);\n\nconst _dataloaderIntegration = (() => {\n  let instrumentationWrappedCallback: undefined | ((callback: () => void) => void);\n\n  return {\n    name: INTEGRATION_NAME,\n    setupOnce() {\n      const instrumentation = instrumentDataloader();\n      instrumentationWrappedCallback = instrumentWhenWrapped(instrumentation);\n    },\n\n    setup(client) {\n      // This is called either immediately or when the instrumentation is wrapped\n      instrumentationWrappedCallback?.(() => {\n        client.on('spanStart', span => {\n          const spanJSON = spanToJSON(span);\n          if (spanJSON.description?.startsWith('dataloader')) {\n            span.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN, 'auto.db.otel.dataloader');\n          }\n\n          // These are all possible dataloader span descriptions\n          // Still checking for the future versions\n          // in case they add support for `clear` and `prime`\n          if (\n            spanJSON.description === 'dataloader.load' ||\n            spanJSON.description === 'dataloader.loadMany' ||\n            spanJSON.description === 'dataloader.batch'\n          ) {\n            span.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_OP, 'cache.get');\n            // TODO: We can try adding `key` to the `data` attribute upstream.\n            // Or alternatively, we can add `requestHook` to the dataloader instrumentation.\n          }\n        });\n      });\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Adds Sentry tracing instrumentation for the [dataloader](https://www.npmjs.com/package/dataloader) library.\n *\n * For more information, see the [`dataloaderIntegration` documentation](https://docs.sentry.io/platforms/javascript/guides/node/configuration/integrations/dataloader/).\n *\n * @example\n * ```javascript\n * const Sentry = require('@sentry/node');\n *\n * Sentry.init({\n *  integrations: [Sentry.dataloaderIntegration()],\n * });\n * ```\n */\nexport const dataloaderIntegration = defineIntegration(_dataloaderIntegration);\n"], "names": ["generateInstrumentOnce", "DataloaderInstrumentation", "instrumentWhenWrapped", "spanToJSON", "SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN", "SEMANTIC_ATTRIBUTE_SENTRY_OP", "defineIntegration"], "mappings": ";;;;;;AAUA,MAAM,gBAAA,GAAmB,YAAY;AAE9B,MAAM,oBAAA,GAAuBA,SAAAA,sBAAsB,CACxD,gBAAgB,EAChB,IACE,IAAIC,0BAAAA,yBAAyB,CAAC;QAC5B,iBAAiB,EAAE,IAAI;IAC7B,CAAK,CAAC;AAGN,MAAM,sBAAA,GAA0B,MAAM;IACpC,IAAI,8BAA8B;IAElC,OAAO;QACL,IAAI,EAAE,gBAAgB;QACtB,SAAS,GAAG;YACV,MAAM,eAAA,GAAkB,oBAAoB,EAAE;YAC9C,8BAAA,GAAiCC,SAAAA,qBAAqB,CAAC,eAAe,CAAC;QAC7E,CAAK;QAED,KAAK,EAAC,MAAM,EAAE;YAClB,2EAAA;YACM,8BAA8B,GAAG,MAAM;gBACrC,MAAM,CAAC,EAAE,CAAC,WAAW,GAAE,QAAQ;oBAC7B,MAAM,QAAA,GAAWC,KAAAA,UAAU,CAAC,IAAI,CAAC;oBACjC,IAAI,QAAQ,CAAC,WAAW,EAAE,UAAU,CAAC,YAAY,CAAC,EAAE;wBAClD,IAAI,CAAC,YAAY,CAACC,KAAAA,gCAAgC,EAAE,yBAAyB,CAAC;oBAC1F;oBAEA,sDAAA;oBACA,yCAAA;oBACA,mDAAA;oBACU,IACE,QAAQ,CAAC,WAAA,KAAgB,iBAAA,IACzB,QAAQ,CAAC,WAAA,KAAgB,qBAAA,IACzB,QAAQ,CAAC,WAAA,KAAgB,oBACzB;wBACA,IAAI,CAAC,YAAY,CAACC,KAAAA,4BAA4B,EAAE,WAAW,CAAC;oBACxE,kEAAA;oBACA,gFAAA;oBACA;gBACA,CAAS,CAAC;YACV,CAAO,CAAC;QACR,CAAK;IACL,CAAG;AACH,CAAC,CAAA;AAED;;;;;;;;;;;;;CAaA,SACa,qBAAA,GAAwBC,KAAAA,iBAAiB,CAAC,sBAAsB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3587, "column": 0}, "map": {"version": 3, "file": "amqplib.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/integrations/tracing/amqplib.ts"], "sourcesContent": ["import type { Span } from '@opentelemetry/api';\nimport { type AmqplibInstrumentationConfig, AmqplibInstrumentation } from '@opentelemetry/instrumentation-amqplib';\nimport type { IntegrationFn } from '@sentry/core';\nimport { defineIntegration } from '@sentry/core';\nimport { addOriginToSpan, generateInstrumentOnce } from '@sentry/node-core';\n\nconst INTEGRATION_NAME = 'Amqplib';\n\nconst config: AmqplibInstrumentationConfig = {\n  consumeEndHook: (span: Span) => {\n    addOriginToSpan(span, 'auto.amqplib.otel.consumer');\n  },\n  publishHook: (span: Span) => {\n    addOriginToSpan(span, 'auto.amqplib.otel.publisher');\n  },\n};\n\nexport const instrumentAmqplib = generateInstrumentOnce(INTEGRATION_NAME, () => new AmqplibInstrumentation(config));\n\nconst _amqplibIntegration = (() => {\n  return {\n    name: INTEGRATION_NAME,\n    setupOnce() {\n      instrumentAmqplib();\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Adds Sentry tracing instrumentation for the [amqplib](https://www.npmjs.com/package/amqplib) library.\n *\n * For more information, see the [`amqplibIntegration` documentation](https://docs.sentry.io/platforms/javascript/guides/node/configuration/integrations/amqplib/).\n *\n * @example\n * ```javascript\n * const Sentry = require('@sentry/node');\n *\n * Sentry.init({\n *  integrations: [Sentry.amqplibIntegration()],\n * });\n * ```\n */\nexport const amqplibIntegration = defineIntegration(_amqplibIntegration);\n"], "names": ["addOriginToSpan", "generateInstrumentOnce", "AmqplibInstrumentation", "defineIntegration"], "mappings": ";;;;;;AAMA,MAAM,gBAAA,GAAmB,SAAS;AAElC,MAAM,MAAM,GAAiC;IAC3C,cAAc,EAAE,CAAC,IAAI,KAAW;QAC9BA,SAAAA,eAAe,CAAC,IAAI,EAAE,4BAA4B,CAAC;IACvD,CAAG;IACD,WAAW,EAAE,CAAC,IAAI,KAAW;QAC3BA,SAAAA,eAAe,CAAC,IAAI,EAAE,6BAA6B,CAAC;IACxD,CAAG;AACH,CAAC;AAEM,MAAM,iBAAA,GAAoBC,SAAAA,sBAAsB,CAAC,gBAAgB,EAAE,IAAM,IAAIC,uBAAAA,sBAAsB,CAAC,MAAM,CAAC;AAElH,MAAM,mBAAA,GAAuB,MAAM;IACjC,OAAO;QACL,IAAI,EAAE,gBAAgB;QACtB,SAAS,GAAG;YACV,iBAAiB,EAAE;QACzB,CAAK;IACL,CAAG;AACH,CAAC,CAAA;AAED;;;;;;;;;;;;;CAaA,SACa,kBAAA,GAAqBC,KAAAA,iBAAiB,CAAC,mBAAmB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3632, "column": 0}, "map": {"version": 3, "file": "constants.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/integrations/tracing/vercelai/constants.ts"], "sourcesContent": ["export const INTEGRATION_NAME = 'VercelAI';\n"], "names": [], "mappings": ";;;AAAO,MAAM,gBAAA,GAAmB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3642, "column": 0}, "map": {"version": 3, "file": "instrumentation.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/integrations/tracing/vercelai/instrumentation.ts"], "sourcesContent": ["import type { InstrumentationConfig, InstrumentationModuleDefinition } from '@opentelemetry/instrumentation';\nimport { InstrumentationBase, InstrumentationNodeModuleDefinition } from '@opentelemetry/instrumentation';\nimport {\n  addNonEnumerableProperty,\n  getActiveSpan,\n  getCurrentScope,\n  handleCallbackErrors,\n  SDK_VERSION,\n} from '@sentry/core';\nimport { INTEGRATION_NAME } from './constants';\nimport type { TelemetrySettings, VercelAiIntegration } from './types';\n\n// List of patched methods\n// From: https://sdk.vercel.ai/docs/ai-sdk-core/telemetry#collected-data\nconst INSTRUMENTED_METHODS = [\n  'generateText',\n  'streamText',\n  'generateObject',\n  'streamObject',\n  'embed',\n  'embedMany',\n] as const;\n\ninterface MethodFirstArg extends Record<string, unknown> {\n  experimental_telemetry?: TelemetrySettings;\n}\n\ntype MethodArgs = [MethodFirstArg, ...unknown[]];\n\ntype PatchedModuleExports = Record<(typeof INSTRUMENTED_METHODS)[number], (...args: MethodArgs) => unknown> &\n  Record<string, unknown>;\n\ninterface RecordingOptions {\n  recordInputs?: boolean;\n  recordOutputs?: boolean;\n}\n\n/**\n * Determines whether to record inputs and outputs for Vercel AI telemetry based on the configuration hierarchy.\n *\n * The order of precedence is:\n * 1. The vercel ai integration options\n * 2. The experimental_telemetry options in the vercel ai method calls\n * 3. When telemetry is explicitly enabled (isEnabled: true), default to recording\n * 4. Otherwise, use the sendDefaultPii option from client options\n */\nexport function determineRecordingSettings(\n  integrationRecordingOptions: RecordingOptions | undefined,\n  methodTelemetryOptions: RecordingOptions,\n  telemetryExplicitlyEnabled: boolean | undefined,\n  defaultRecordingEnabled: boolean,\n): { recordInputs: boolean; recordOutputs: boolean } {\n  const recordInputs =\n    integrationRecordingOptions?.recordInputs !== undefined\n      ? integrationRecordingOptions.recordInputs\n      : methodTelemetryOptions.recordInputs !== undefined\n        ? methodTelemetryOptions.recordInputs\n        : telemetryExplicitlyEnabled === true\n          ? true // When telemetry is explicitly enabled, default to recording inputs\n          : defaultRecordingEnabled;\n\n  const recordOutputs =\n    integrationRecordingOptions?.recordOutputs !== undefined\n      ? integrationRecordingOptions.recordOutputs\n      : methodTelemetryOptions.recordOutputs !== undefined\n        ? methodTelemetryOptions.recordOutputs\n        : telemetryExplicitlyEnabled === true\n          ? true // When telemetry is explicitly enabled, default to recording inputs\n          : defaultRecordingEnabled;\n\n  return { recordInputs, recordOutputs };\n}\n\n/**\n * This detects is added by the Sentry Vercel AI Integration to detect if the integration should\n * be enabled.\n *\n * It also patches the `ai` module to enable Vercel AI telemetry automatically for all methods.\n */\nexport class SentryVercelAiInstrumentation extends InstrumentationBase {\n  private _isPatched = false;\n  private _callbacks: (() => void)[] = [];\n\n  public constructor(config: InstrumentationConfig = {}) {\n    super('@sentry/instrumentation-vercel-ai', SDK_VERSION, config);\n  }\n\n  /**\n   * Initializes the instrumentation by defining the modules to be patched.\n   */\n  public init(): InstrumentationModuleDefinition {\n    const module = new InstrumentationNodeModuleDefinition('ai', ['>=3.0.0 <5'], this._patch.bind(this));\n    return module;\n  }\n\n  /**\n   * Call the provided callback when the module is patched.\n   * If it has already been patched, the callback will be called immediately.\n   */\n  public callWhenPatched(callback: () => void): void {\n    if (this._isPatched) {\n      callback();\n    } else {\n      this._callbacks.push(callback);\n    }\n  }\n\n  /**\n   * Patches module exports to enable Vercel AI telemetry.\n   */\n  private _patch(moduleExports: PatchedModuleExports): unknown {\n    this._isPatched = true;\n\n    this._callbacks.forEach(callback => callback());\n    this._callbacks = [];\n\n    function generatePatch(originalMethod: (...args: MethodArgs) => unknown) {\n      return (...args: MethodArgs) => {\n        const existingExperimentalTelemetry = args[0].experimental_telemetry || {};\n        const isEnabled = existingExperimentalTelemetry.isEnabled;\n\n        const client = getCurrentScope().getClient();\n        const integration = client?.getIntegrationByName<VercelAiIntegration>(INTEGRATION_NAME);\n        const integrationOptions = integration?.options;\n        const shouldRecordInputsAndOutputs = integration ? Boolean(client?.getOptions().sendDefaultPii) : false;\n\n        const { recordInputs, recordOutputs } = determineRecordingSettings(\n          integrationOptions,\n          existingExperimentalTelemetry,\n          isEnabled,\n          shouldRecordInputsAndOutputs,\n        );\n\n        args[0].experimental_telemetry = {\n          ...existingExperimentalTelemetry,\n          isEnabled: isEnabled !== undefined ? isEnabled : true,\n          recordInputs,\n          recordOutputs,\n        };\n\n        return handleCallbackErrors(\n          () => {\n            // @ts-expect-error we know that the method exists\n            return originalMethod.apply(this, args);\n          },\n          error => {\n            // This error bubbles up to unhandledrejection handler (if not handled before),\n            // where we do not know the active span anymore\n            // So to circumvent this, we set the active span on the error object\n            // which is picked up by the unhandledrejection handler\n            if (error && typeof error === 'object') {\n              addNonEnumerableProperty(error, '_sentry_active_span', getActiveSpan());\n            }\n          },\n        );\n      };\n    }\n\n    // Is this an ESM module?\n    // https://tc39.es/ecma262/#sec-module-namespace-objects\n    if (Object.prototype.toString.call(moduleExports) === '[object Module]') {\n      // In ESM we take the usual route and just replace the exports we want to instrument\n      for (const method of INSTRUMENTED_METHODS) {\n        moduleExports[method] = generatePatch(moduleExports[method]);\n      }\n\n      return moduleExports;\n    } else {\n      // In CJS we can't replace the exports in the original module because they\n      // don't have setters, so we create a new object with the same properties\n      const patchedModuleExports = INSTRUMENTED_METHODS.reduce((acc, curr) => {\n        acc[curr] = generatePatch(moduleExports[curr]);\n        return acc;\n      }, {} as PatchedModuleExports);\n\n      return { ...moduleExports, ...patchedModuleExports };\n    }\n  }\n}\n"], "names": ["InstrumentationBase", "SDK_VERSION", "InstrumentationNodeModuleDefinition", "getCurrentScope", "INTEGRATION_NAME", "handleCallbackErrors", "addNonEnumerableProperty", "getActiveSpan"], "mappings": ";;;;;;AAYA,0BAAA;AACA,wEAAA;AACA,MAAM,uBAAuB;IAC3B,cAAc;IACd,YAAY;IACZ,gBAAgB;IAChB,cAAc;IACd,OAAO;IACP,WAAW;CACb;AAgBA;;;;;;;;CAQA,GACO,SAAS,0BAA0B,CACxC,2BAA2B,EAC3B,sBAAsB,EACtB,0BAA0B,EAC1B,uBAAuB;IAEvB,MAAM,YAAA,GACJ,2BAA2B,EAAE,YAAA,KAAiB,YAC1C,2BAA2B,CAAC,YAAA,GAC5B,sBAAsB,CAAC,YAAA,KAAiB,YACtC,sBAAsB,CAAC,YAAA,GACvB,+BAA+B,OAC7B,IAAA,CAAA,oEAAA;OACA,uBAAuB;IAEjC,MAAM,aAAA,GACJ,2BAA2B,EAAE,aAAA,KAAkB,YAC3C,2BAA2B,CAAC,aAAA,GAC5B,sBAAsB,CAAC,aAAA,KAAkB,YACvC,sBAAsB,CAAC,aAAA,GACvB,+BAA+B,OAC7B,IAAA,CAAA,oEAAA;OACA,uBAAuB;IAEjC,OAAO;QAAE,YAAY;QAAE;IAAA,CAAe;AACxC;AAEA;;;;;CAKA,GACO,MAAM,6BAAA,SAAsCA,gBAAAA,mBAAA,CAAoB;IACrE,MAAA,GAAA;QAAA,IAAA,CAAQ,UAAA,GAAa;IAAA;IACrB,OAAA,GAAA;QAAA,IAAA,CAAQ,UAAU,GAAmB,EAAA;IAAC;IAE/B,WAAW,CAAC,MAAM,GAA0B,CAAA,CAAE,CAAE;QACrD,KAAK,CAAC,mCAAmC,EAAEC,KAAAA,WAAW,EAAE,MAAM,CAAA;QAAA,6BAAA,CAAA,SAAA,CAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA;QAAA,6BAAA,CAAA,SAAA,CAAA,OAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAClE;IAAA;IAEA;;GAEA,GACS,IAAI,GAAoC;QAC7C,MAAM,SAAS,IAAIC,gBAAAA,mCAAmC,CAAC,IAAI,EAAE;YAAC,YAAY;SAAC,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpG,OAAO,MAAM;IACjB;IAEA;;;GAGA,GACS,eAAe,CAAC,QAAQ,EAAoB;QACjD,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,QAAQ,EAAE;QAChB,OAAW;YACL,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC;QACpC;IACA;IAEA;;GAEA,GACU,MAAM,CAAC,aAAa,EAAiC;QAC3D,IAAI,CAAC,UAAA,GAAa,IAAI;QAEtB,IAAI,CAAC,UAAU,CAAC,OAAO,EAAC,QAAA,GAAY,QAAQ,EAAE,CAAC;QAC/C,IAAI,CAAC,UAAA,GAAa,EAAE;QAEpB,SAAS,aAAa,CAAC,cAAc,EAAoC;YACvE,OAAO,CAAC,GAAG,IAAI,KAAiB;gBAC9B,MAAM,6BAAA,GAAgC,IAAI,CAAC,CAAC,CAAC,CAAC,sBAAA,IAA0B,CAAA,CAAE;gBAC1E,MAAM,SAAA,GAAY,6BAA6B,CAAC,SAAS;gBAEzD,MAAM,SAASC,KAAAA,eAAe,EAAE,CAAC,SAAS,EAAE;gBAC5C,MAAM,cAAc,MAAM,EAAE,oBAAoB,CAAsBC,UAAAA,gBAAgB,CAAC;gBACvF,MAAM,kBAAA,GAAqB,WAAW,EAAE,OAAO;gBAC/C,MAAM,4BAAA,GAA+B,WAAA,GAAc,OAAO,CAAC,MAAM,EAAE,UAAU,EAAE,CAAC,cAAc,CAAA,GAAI,KAAK;gBAEvG,MAAM,EAAE,YAAY,EAAE,aAAA,EAAc,GAAI,0BAA0B,CAChE,kBAAkB,EAClB,6BAA6B,EAC7B,SAAS,EACT,4BAA4B;gBAG9B,IAAI,CAAC,CAAC,CAAC,CAAC,sBAAA,GAAyB;oBAC/B,GAAG,6BAA6B;oBAChC,SAAS,EAAE,SAAA,KAAc,YAAY,SAAA,GAAY,IAAI;oBACrD,YAAY;oBACZ,aAAa;gBACvB,CAAS;gBAED,OAAOC,KAAAA,oBAAoB,CACzB,MAAM;oBAChB,kDAAA;oBACY,OAAO,cAAc,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC;gBACnD,CAAW,GACD,SAAS;oBACnB,+EAAA;oBACA,+CAAA;oBACA,oEAAA;oBACA,uDAAA;oBACY,IAAI,KAAA,IAAS,OAAO,KAAA,KAAU,QAAQ,EAAE;wBACtCC,KAAAA,wBAAwB,CAAC,KAAK,EAAE,qBAAqB,EAAEC,KAAAA,aAAa,EAAE,CAAC;oBACrF;gBACA,CAAW;YAEX,CAAO;QACP;QAEA,yBAAA;QACA,wDAAA;QACI,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAA,KAAM,iBAAiB,EAAE;YAC7E,oFAAA;YACM,KAAK,MAAM,MAAA,IAAU,oBAAoB,CAAE;gBACzC,aAAa,CAAC,MAAM,CAAA,GAAI,aAAa,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;YACpE;YAEM,OAAO,aAAa;QAC1B,OAAW;YACX,0EAAA;YACA,yEAAA;YACM,MAAM,oBAAA,GAAuB,oBAAoB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,KAAK;gBACtE,GAAG,CAAC,IAAI,CAAA,GAAI,aAAa,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;gBAC9C,OAAO,GAAG;YAClB,CAAO,EAAE,CAAA,CAAC,EAA0B;YAE9B,OAAO;gBAAE,GAAG,aAAa;gBAAE,GAAG,oBAAA;YAAA,CAAsB;QAC1D;IACA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3775, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/integrations/tracing/vercelai/index.ts"], "sourcesContent": ["import type { Client, IntegrationFn } from '@sentry/core';\nimport { addVercelAiProcessors, defineIntegration } from '@sentry/core';\nimport { type modulesIntegration, generateInstrumentOnce } from '@sentry/node-core';\nimport { INTEGRATION_NAME } from './constants';\nimport { SentryVercelAiInstrumentation } from './instrumentation';\nimport type { VercelAiOptions } from './types';\n\nexport const instrumentVercelAi = generateInstrumentOnce(INTEGRATION_NAME, () => new SentryVercelAiInstrumentation({}));\n\n/**\n * Determines if the integration should be forced based on environment and package availability.\n * Returns true if the 'ai' package is available.\n */\nfunction shouldForceIntegration(client: Client): boolean {\n  const modules = client.getIntegrationByName<ReturnType<typeof modulesIntegration>>('Modules');\n  return !!modules?.getModules?.()?.ai;\n}\n\nconst _vercelAIIntegration = ((options: VercelAiOptions = {}) => {\n  let instrumentation: undefined | SentryVercelAiInstrumentation;\n\n  return {\n    name: INTEGRATION_NAME,\n    options,\n    setupOnce() {\n      instrumentation = instrumentVercelAi();\n    },\n    afterAllSetup(client) {\n      // Auto-detect if we should force the integration when running with 'ai' package available\n      // Note that this can only be detected if the 'Modules' integration is available, and running in CJS mode\n      const shouldForce = options.force ?? shouldForceIntegration(client);\n\n      if (shouldForce) {\n        addVercelAiProcessors(client);\n      } else {\n        instrumentation?.callWhenPatched(() => addVercelAiProcessors(client));\n      }\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Adds Sentry tracing instrumentation for the [ai](https://www.npmjs.com/package/ai) library.\n * This integration is not enabled by default, you need to manually add it.\n *\n * For more information, see the [`ai` documentation](https://sdk.vercel.ai/docs/ai-sdk-core/telemetry).\n *\n * @example\n * ```javascript\n * const Sentry = require('@sentry/node');\n *\n * Sentry.init({\n *  integrations: [Sentry.vercelAIIntegration()],\n * });\n * ```\n *\n * This integration adds tracing support to all `ai` function calls.\n * You need to opt-in to collecting spans for a specific call,\n * you can do so by setting `experimental_telemetry.isEnabled` to `true` in the first argument of the function call.\n *\n * ```javascript\n * const result = await generateText({\n *   model: openai('gpt-4-turbo'),\n *   experimental_telemetry: { isEnabled: true },\n * });\n * ```\n *\n * If you want to collect inputs and outputs for a specific call, you must specifically opt-in to each\n * function call by setting `experimental_telemetry.recordInputs` and `experimental_telemetry.recordOutputs`\n * to `true`.\n *\n * ```javascript\n * const result = await generateText({\n *  model: openai('gpt-4-turbo'),\n *  experimental_telemetry: { isEnabled: true, recordInputs: true, recordOutputs: true },\n * });\n */\nexport const vercelAIIntegration = defineIntegration(_vercelAIIntegration);\n"], "names": ["generateInstrumentOnce", "INTEGRATION_NAME", "SentryVercelAiInstrumentation", "addVercelAiProcessors", "defineIntegration"], "mappings": ";;;;;;;MAOa,kBAAA,GAAqBA,SAAAA,sBAAsB,CAACC,UAAAA,gBAAgB,EAAE,IAAM,IAAIC,gBAAAA,6BAA6B,CAAC,CAAA,CAAE,CAAC;AAEtH;;;CAGA,GACA,SAAS,sBAAsB,CAAC,MAAM,EAAmB;IACvD,MAAM,UAAU,MAAM,CAAC,oBAAoB,CAAwC,SAAS,CAAC;IAC7F,OAAO,CAAC,CAAC,OAAO,EAAE,UAAU,IAAI,EAAE,EAAE;AACtC;AAEA,MAAM,oBAAA,GAAwB,CAAC,OAAO,GAAoB,CAAA,CAAE,KAAK;IAC/D,IAAI,eAAe;IAEnB,OAAO;QACL,IAAI,EAAED,UAAAA,gBAAgB;QACtB,OAAO;QACP,SAAS,GAAG;YACV,eAAA,GAAkB,kBAAkB,EAAE;QAC5C,CAAK;QACD,aAAa,EAAC,MAAM,EAAE;YAC1B,0FAAA;YACA,yGAAA;YACM,MAAM,WAAA,GAAc,OAAO,CAAC,KAAA,IAAS,sBAAsB,CAAC,MAAM,CAAC;YAEnE,IAAI,WAAW,EAAE;gBACfE,KAAAA,qBAAqB,CAAC,MAAM,CAAC;YACrC,OAAa;gBACL,eAAe,EAAE,eAAe,CAAC,IAAMA,KAAAA,qBAAqB,CAAC,MAAM,CAAC,CAAC;YAC7E;QACA,CAAK;IACL,CAAG;AACH,CAAC,CAAA;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAmCA,SACa,mBAAA,GAAsBC,KAAAA,iBAAiB,CAAC,oBAAoB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3853, "column": 0}, "map": {"version": 3, "file": "instrumentation.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/integrations/tracing/openai/instrumentation.ts"], "sourcesContent": ["import {\n  type InstrumentationConfig,\n  type InstrumentationModuleDefinition,\n  InstrumentationBase,\n  InstrumentationNodeModuleDefinition,\n} from '@opentelemetry/instrumentation';\nimport type { Integration, OpenAiClient, OpenAiOptions } from '@sentry/core';\nimport { getCurrentScope, instrumentOpenAiClient, OPENAI_INTEGRATION_NAME, SDK_VERSION } from '@sentry/core';\n\nconst supportedVersions = ['>=4.0.0 <6'];\n\nexport interface OpenAiIntegration extends Integration {\n  options: OpenAiOptions;\n}\n\n/**\n * Represents the patched shape of the OpenAI module export.\n */\ninterface PatchedModuleExports {\n  [key: string]: unknown;\n  OpenAI: abstract new (...args: unknown[]) => OpenAiClient;\n}\n\n/**\n * Determines telemetry recording settings.\n */\nfunction determineRecordingSettings(\n  integrationOptions: OpenAiOptions | undefined,\n  defaultEnabled: boolean,\n): { recordInputs: boolean; recordOutputs: boolean } {\n  const recordInputs = integrationOptions?.recordInputs ?? defaultEnabled;\n  const recordOutputs = integrationOptions?.recordOutputs ?? defaultEnabled;\n  return { recordInputs, recordOutputs };\n}\n\n/**\n * Sentry OpenAI instrumentation using OpenTelemetry.\n */\nexport class SentryOpenAiInstrumentation extends InstrumentationBase<InstrumentationConfig> {\n  public constructor(config: InstrumentationConfig = {}) {\n    super('@sentry/instrumentation-openai', SDK_VERSION, config);\n  }\n\n  /**\n   * Initializes the instrumentation by defining the modules to be patched.\n   */\n  public init(): InstrumentationModuleDefinition {\n    const module = new InstrumentationNodeModuleDefinition('openai', supportedVersions, this._patch.bind(this));\n    return module;\n  }\n\n  /**\n   * Core patch logic applying instrumentation to the OpenAI client constructor.\n   */\n  private _patch(exports: PatchedModuleExports): PatchedModuleExports | void {\n    const Original = exports.OpenAI;\n\n    const WrappedOpenAI = function (this: unknown, ...args: unknown[]) {\n      const instance = Reflect.construct(Original, args);\n      const scopeClient = getCurrentScope().getClient();\n      const integration = scopeClient?.getIntegrationByName<OpenAiIntegration>(OPENAI_INTEGRATION_NAME);\n      const integrationOpts = integration?.options;\n      const defaultPii = Boolean(scopeClient?.getOptions().sendDefaultPii);\n\n      const { recordInputs, recordOutputs } = determineRecordingSettings(integrationOpts, defaultPii);\n\n      return instrumentOpenAiClient(instance as OpenAiClient, {\n        recordInputs,\n        recordOutputs,\n      });\n    } as unknown as abstract new (...args: unknown[]) => OpenAiClient;\n\n    // Preserve static and prototype chains\n    Object.setPrototypeOf(WrappedOpenAI, Original);\n    Object.setPrototypeOf(WrappedOpenAI.prototype, Original.prototype);\n\n    for (const key of Object.getOwnPropertyNames(Original)) {\n      if (!['length', 'name', 'prototype'].includes(key)) {\n        const descriptor = Object.getOwnPropertyDescriptor(Original, key);\n        if (descriptor) {\n          Object.defineProperty(WrappedOpenAI, key, descriptor);\n        }\n      }\n    }\n\n    // Constructor replacement - handle read-only properties\n    // The OpenAI property might have only a getter, so use defineProperty\n    try {\n      exports.OpenAI = WrappedOpenAI;\n    } catch (error) {\n      // If direct assignment fails, override the property descriptor\n      Object.defineProperty(exports, 'OpenAI', {\n        value: WrappedOpenAI,\n        writable: true,\n        configurable: true,\n        enumerable: true,\n      });\n    }\n    return exports;\n  }\n}\n"], "names": ["InstrumentationBase", "SDK_VERSION", "InstrumentationNodeModuleDefinition", "getCurrentScope", "OPENAI_INTEGRATION_NAME", "instrumentOpenAiClient"], "mappings": ";;;;;AASA,MAAM,iBAAA,GAAoB;IAAC,YAAY;CAAC;AAcxC;;CAEA,GACA,SAAS,0BAA0B,CACjC,kBAAkB,EAClB,cAAc;IAEd,MAAM,YAAA,GAAe,kBAAkB,EAAE,YAAA,IAAgB,cAAc;IACvE,MAAM,aAAA,GAAgB,kBAAkB,EAAE,aAAA,IAAiB,cAAc;IACzE,OAAO;QAAE,YAAY;QAAE;IAAA,CAAe;AACxC;AAEA;;CAEA,GACO,MAAM,2BAAA,SAAoCA,gBAAAA,mBAAmB,CAAwB;IACnF,WAAW,CAAC,MAAM,GAA0B,CAAA,CAAE,CAAE;QACrD,KAAK,CAAC,gCAAgC,EAAEC,KAAAA,WAAW,EAAE,MAAM,CAAC;IAChE;IAEA;;GAEA,GACS,IAAI,GAAoC;QAC7C,MAAM,MAAA,GAAS,IAAIC,gBAAAA,mCAAmC,CAAC,QAAQ,EAAE,iBAAiB,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3G,OAAO,MAAM;IACjB;IAEA;;GAEA,GACU,MAAM,CAAC,QAAO,EAAqD;QACzE,MAAM,QAAA,GAAW,OAAO,EAAC,MAAM;QAE/B,MAAM,gBAAgB,SAAyB,GAAG,IAAI,EAAa;YACjE,MAAM,QAAA,GAAW,OAAO,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,CAAC;YAClD,MAAM,cAAcC,KAAAA,eAAe,EAAE,CAAC,SAAS,EAAE;YACjD,MAAM,cAAc,WAAW,EAAE,oBAAoB,CAAoBC,KAAAA,uBAAuB,CAAC;YACjG,MAAM,eAAA,GAAkB,WAAW,EAAE,OAAO;YAC5C,MAAM,UAAA,GAAa,OAAO,CAAC,WAAW,EAAE,UAAU,EAAE,CAAC,cAAc,CAAC;YAEpE,MAAM,EAAE,YAAY,EAAE,aAAA,EAAc,GAAI,0BAA0B,CAAC,eAAe,EAAE,UAAU,CAAC;YAE/F,OAAOC,KAAAA,sBAAsB,CAAC,QAAA,EAA0B;gBACtD,YAAY;gBACZ,aAAa;YACrB,CAAO,CAAC;QACR,CAAI;QAEJ,uCAAA;QACI,MAAM,CAAC,cAAc,CAAC,aAAa,EAAE,QAAQ,CAAC;QAC9C,MAAM,CAAC,cAAc,CAAC,aAAa,CAAC,SAAS,EAAE,QAAQ,CAAC,SAAS,CAAC;QAElE,KAAK,MAAM,GAAA,IAAO,MAAM,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAE;YACtD,IAAI,CAAC;gBAAC,QAAQ;gBAAE,MAAM;gBAAE,WAAW;aAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;gBAClD,MAAM,UAAA,GAAa,MAAM,CAAC,wBAAwB,CAAC,QAAQ,EAAE,GAAG,CAAC;gBACjE,IAAI,UAAU,EAAE;oBACd,MAAM,CAAC,cAAc,CAAC,aAAa,EAAE,GAAG,EAAE,UAAU,CAAC;gBAC/D;YACA;QACA;QAEA,wDAAA;QACA,sEAAA;QACI,IAAI;YACF,OAAO,EAAC,MAAA,GAAS,aAAa;QACpC,CAAI,CAAE,OAAO,KAAK,EAAE;YACpB,+DAAA;YACM,MAAM,CAAC,cAAc,CAAC,OAAO,GAAE,QAAQ,EAAE;gBACvC,KAAK,EAAE,aAAa;gBACpB,QAAQ,EAAE,IAAI;gBACd,YAAY,EAAE,IAAI;gBAClB,UAAU,EAAE,IAAI;YACxB,CAAO,CAAC;QACR;QACI,OAAO,OAAO;IAClB;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3936, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/integrations/tracing/openai/index.ts"], "sourcesContent": ["import type { IntegrationFn, OpenAiOptions } from '@sentry/core';\nimport { defineIntegration, OPENAI_INTEGRATION_NAME } from '@sentry/core';\nimport { generateInstrumentOnce } from '@sentry/node-core';\nimport { SentryOpenAiInstrumentation } from './instrumentation';\n\nexport const instrumentOpenAi = generateInstrumentOnce(\n  OPENAI_INTEGRATION_NAME,\n  () => new SentryOpenAiInstrumentation({}),\n);\n\nconst _openAiIntegration = ((options: OpenAiOptions = {}) => {\n  return {\n    name: OPENAI_INTEGRATION_NAME,\n    options,\n    setupOnce() {\n      instrumentOpenAi();\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Adds Sentry tracing instrumentation for the OpenAI SDK.\n *\n * This integration is enabled by default.\n *\n * When configured, this integration automatically instruments OpenAI SDK client instances\n * to capture telemetry data following OpenTelemetry Semantic Conventions for Generative AI.\n *\n * @example\n * ```javascript\n * import * as Sentry from '@sentry/node';\n *\n * Sentry.init({\n *   integrations: [Sentry.openAIIntegration()],\n * });\n * ```\n *\n * ## Options\n *\n * - `recordInputs`: Whether to record prompt messages (default: respects `sendDefaultPii` client option)\n * - `recordOutputs`: Whether to record response text (default: respects `sendDefaultPii` client option)\n *\n * ### Default Behavior\n *\n * By default, the integration will:\n * - Record inputs and outputs ONLY if `sendDefaultPii` is set to `true` in your Sentry client options\n * - Otherwise, inputs and outputs are NOT recorded unless explicitly enabled\n *\n * @example\n * ```javascript\n * // Record inputs and outputs when sendDefaultPii is false\n * Sentry.init({\n *   integrations: [\n *     Sentry.openAIIntegration({\n *       recordInputs: true,\n *       recordOutputs: true\n *     })\n *   ],\n * });\n *\n * // Never record inputs/outputs regardless of sendDefaultPii\n * Sentry.init({\n *   sendDefaultPii: true,\n *   integrations: [\n *     Sentry.openAIIntegration({\n *       recordInputs: false,\n *       recordOutputs: false\n *     })\n *   ],\n * });\n * ```\n *\n */\nexport const openAIIntegration = defineIntegration(_openAiIntegration);\n"], "names": ["generateInstrumentOnce", "OPENAI_INTEGRATION_NAME", "SentryOpenAiInstrumentation", "defineIntegration"], "mappings": ";;;;;;AAKO,MAAM,gBAAA,GAAmBA,SAAAA,sBAAsB,CACpDC,KAAAA,uBAAuB,EACvB,IAAM,IAAIC,gBAAAA,2BAA2B,CAAC,CAAA,CAAE,CAAC;AAG3C,MAAM,kBAAA,GAAsB,CAAC,OAAO,GAAkB,CAAA,CAAE,KAAK;IAC3D,OAAO;QACL,IAAI,EAAED,KAAAA,uBAAuB;QAC7B,OAAO;QACP,SAAS,GAAG;YACV,gBAAgB,EAAE;QACxB,CAAK;IACL,CAAG;AACH,CAAC,CAAA;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAoDA,SACa,iBAAA,GAAoBE,KAAAA,iBAAiB,CAAC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4012, "column": 0}, "map": {"version": 3, "file": "launchDarkly.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/integrations/featureFlagShims/launchDarkly.ts"], "sourcesContent": ["import { consoleSandbox, defineIntegration, isBrowser } from '@sentry/core';\n\n/**\n * This is a shim for the LaunchDarkly integration.\n * We need this in order to not throw runtime errors when accidentally importing this on the server through a meta framework like Next.js.\n */\nexport const launchDarklyIntegrationShim = defineIntegration((_options?: unknown) => {\n  if (!isBrowser()) {\n    consoleSandbox(() => {\n      // eslint-disable-next-line no-console\n      console.warn('The launchDarklyIntegration() can only be used in the browser.');\n    });\n  }\n\n  return {\n    name: 'LaunchDarkly',\n  };\n});\n\n/**\n * This is a shim for the LaunchDarkly flag used handler.\n */\nexport function buildLaunchDarklyFlagUsedHandlerShim(): unknown {\n  if (!isBrowser()) {\n    consoleSandbox(() => {\n      // eslint-disable-next-line no-console\n      console.warn('The buildLaunchDarklyFlagUsedHandler() can only be used in the browser.');\n    });\n  }\n\n  return {\n    name: 'sentry-flag-auditor',\n    type: 'flag-used',\n    synchronous: true,\n    method: () => null,\n  };\n}\n"], "names": ["defineIntegration", "<PERSON><PERSON><PERSON><PERSON>", "consoleSandbox"], "mappings": ";;;;AAEA;;;CAGA,GACO,MAAM,8BAA8BA,KAAAA,iBAAiB,CAAC,CAAC,QAAQ,KAAe;IACnF,IAAI,CAACC,KAAAA,SAAS,EAAE,EAAE;QAChBC,KAAAA,cAAc,CAAC,MAAM;YACzB,sCAAA;YACM,OAAO,CAAC,IAAI,CAAC,gEAAgE,CAAC;QACpF,CAAK,CAAC;IACN;IAEE,OAAO;QACL,IAAI,EAAE,cAAc;IACxB,CAAG;AACH,CAAC;AAED;;CAEA,GACO,SAAS,oCAAoC,GAAY;IAC9D,IAAI,CAACD,KAAAA,SAAS,EAAE,EAAE;QAChBC,KAAAA,cAAc,CAAC,MAAM;YACzB,sCAAA;YACM,OAAO,CAAC,IAAI,CAAC,yEAAyE,CAAC;QAC7F,CAAK,CAAC;IACN;IAEE,OAAO;QACL,IAAI,EAAE,qBAAqB;QAC3B,IAAI,EAAE,WAAW;QACjB,WAAW,EAAE,IAAI;QACjB,MAAM,EAAE,IAAM,IAAI;IACtB,CAAG;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4053, "column": 0}, "map": {"version": 3, "file": "openFeature.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/integrations/featureFlagShims/openFeature.ts"], "sourcesContent": ["import { consoleSandbox, defineIntegration, isBrowser } from '@sentry/core';\n\n/**\n * This is a shim for the OpenFeature integration.\n * We need this in order to not throw runtime errors when accidentally importing this on the server through a meta framework like Next.js.\n */\nexport const openFeatureIntegrationShim = defineIntegration((_options?: unknown) => {\n  if (!isBrowser()) {\n    consoleSandbox(() => {\n      // eslint-disable-next-line no-console\n      console.warn('The openFeatureIntegration() can only be used in the browser.');\n    });\n  }\n\n  return {\n    name: 'OpenFeature',\n  };\n});\n\n/**\n * This is a shim for the OpenFeature integration hook.\n */\nexport class OpenFeatureIntegrationHookShim {\n  /**\n   *\n   */\n  public constructor() {\n    if (!isBrowser()) {\n      consoleSandbox(() => {\n        // eslint-disable-next-line no-console\n        console.warn('The OpenFeatureIntegrationHook can only be used in the browser.');\n      });\n    }\n  }\n\n  /**\n   *\n   */\n  public after(): void {\n    // No-op\n  }\n\n  /**\n   *\n   */\n  public error(): void {\n    // No-op\n  }\n}\n"], "names": ["defineIntegration", "<PERSON><PERSON><PERSON><PERSON>", "consoleSandbox"], "mappings": ";;;;AAEA;;;CAGA,GACO,MAAM,6BAA6BA,KAAAA,iBAAiB,CAAC,CAAC,QAAQ,KAAe;IAClF,IAAI,CAACC,KAAAA,SAAS,EAAE,EAAE;QAChBC,KAAAA,cAAc,CAAC,MAAM;YACzB,sCAAA;YACM,OAAO,CAAC,IAAI,CAAC,+DAA+D,CAAC;QACnF,CAAK,CAAC;IACN;IAEE,OAAO;QACL,IAAI,EAAE,aAAa;IACvB,CAAG;AACH,CAAC;AAED;;CAEA,GACO,MAAM,8BAAA,CAA+B;IAC5C;;GAEA,GACS,WAAW,EAAG;QACnB,IAAI,CAACD,KAAAA,SAAS,EAAE,EAAE;YAChBC,KAAAA,cAAc,CAAC,MAAM;gBAC3B,sCAAA;gBACQ,OAAO,CAAC,IAAI,CAAC,iEAAiE,CAAC;YACvF,CAAO,CAAC;QACR;IACA;IAEA;;GAEA,GACS,KAAK,GAAS;IACvB,QAAA;IACA;IAEA;;GAEA,GACS,KAAK,GAAS;IACvB,QAAA;IACA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4102, "column": 0}, "map": {"version": 3, "file": "statsig.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/integrations/featureFlagShims/statsig.ts"], "sourcesContent": ["import { consoleSandbox, defineIntegration, isBrowser } from '@sentry/core';\n\n/**\n * This is a shim for the Statsig integration.\n * We need this in order to not throw runtime errors when accidentally importing this on the server through a meta framework like Next.js.\n */\nexport const statsigIntegrationShim = defineIntegration((_options?: unknown) => {\n  if (!isBrowser()) {\n    consoleSandbox(() => {\n      // eslint-disable-next-line no-console\n      console.warn('The statsigIntegration() can only be used in the browser.');\n    });\n  }\n\n  return {\n    name: 'Statsig',\n  };\n});\n"], "names": ["defineIntegration", "<PERSON><PERSON><PERSON><PERSON>", "consoleSandbox"], "mappings": ";;;;AAEA;;;CAGA,GACO,MAAM,yBAAyBA,KAAAA,iBAAiB,CAAC,CAAC,QAAQ,KAAe;IAC9E,IAAI,CAACC,KAAAA,SAAS,EAAE,EAAE;QAChBC,KAAAA,cAAc,CAAC,MAAM;YACzB,sCAAA;YACM,OAAO,CAAC,IAAI,CAAC,2DAA2D,CAAC;QAC/E,CAAK,CAAC;IACN;IAEE,OAAO;QACL,IAAI,EAAE,SAAS;IACnB,CAAG;AACH,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4126, "column": 0}, "map": {"version": 3, "file": "unleash.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/integrations/featureFlagShims/unleash.ts"], "sourcesContent": ["import { consoleSandbox, defineIntegration, isBrowser } from '@sentry/core';\n\n/**\n * This is a shim for the Unleash integration.\n * We need this in order to not throw runtime errors when accidentally importing this on the server through a meta framework like Next.js.\n */\nexport const unleashIntegrationShim = defineIntegration((_options?: unknown) => {\n  if (!isBrowser()) {\n    consoleSandbox(() => {\n      // eslint-disable-next-line no-console\n      console.warn('The unleashIntegration() can only be used in the browser.');\n    });\n  }\n\n  return {\n    name: 'Unleash',\n  };\n});\n"], "names": ["defineIntegration", "<PERSON><PERSON><PERSON><PERSON>", "consoleSandbox"], "mappings": ";;;;AAEA;;;CAGA,GACO,MAAM,yBAAyBA,KAAAA,iBAAiB,CAAC,CAAC,QAAQ,KAAe;IAC9E,IAAI,CAACC,KAAAA,SAAS,EAAE,EAAE;QAChBC,KAAAA,cAAc,CAAC,MAAM;YACzB,sCAAA;YACM,OAAO,CAAC,IAAI,CAAC,2DAA2D,CAAC;QAC/E,CAAK,CAAC;IACN;IAEE,OAAO;QACL,IAAI,EAAE,SAAS;IACnB,CAAG;AACH,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4150, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/integrations/tracing/index.ts"], "sourcesContent": ["import type { Integration } from '@sentry/core';\nimport { instrumentOtelHttp } from '../http';\nimport { amqplibIntegration, instrumentAmqplib } from './amqplib';\nimport { connectIntegration, instrumentConnect } from './connect';\nimport { expressIntegration, instrumentExpress, instrumentExpressV5 } from './express';\nimport { fastifyIntegration, instrumentFastify, instrumentFastifyV3 } from './fastify';\nimport { genericPoolIntegration, instrumentGenericPool } from './genericPool';\nimport { graphqlIntegration, instrumentGraphql } from './graphql';\nimport { hapiIntegration, instrumentHapi } from './hapi';\nimport { instrumentKafka, kafkaIntegration } from './kafka';\nimport { instrumentKoa, koaIntegration } from './koa';\nimport { instrumentLruMemoizer, lruMemoizerIntegration } from './lrumemoizer';\nimport { instrumentMongo, mongoIntegration } from './mongo';\nimport { instrumentMongoose, mongooseIntegration } from './mongoose';\nimport { instrumentMysql, mysqlIntegration } from './mysql';\nimport { instrumentMysql2, mysql2Integration } from './mysql2';\nimport { instrumentOpenAi, openAIIntegration } from './openai';\nimport { instrumentPostgres, postgresIntegration } from './postgres';\nimport { instrumentPostgresJs, postgresJsIntegration } from './postgresjs';\nimport { prismaIntegration } from './prisma';\nimport { instrumentRedis, redisIntegration } from './redis';\nimport { instrumentTedious, tediousIntegration } from './tedious';\nimport { instrumentVercelAi, vercelAIIntegration } from './vercelai';\n\n/**\n * With OTEL, all performance integrations will be added, as OTEL only initializes them when the patched package is actually required.\n */\nexport function getAutoPerformanceIntegrations(): Integration[] {\n  return [\n    expressIntegration(),\n    fastifyIntegration(),\n    graphqlIntegration(),\n    mongoIntegration(),\n    mongooseIntegration(),\n    mysqlIntegration(),\n    mysql2Integration(),\n    redisIntegration(),\n    postgresIntegration(),\n    prismaIntegration(),\n    hapiIntegration(),\n    koaIntegration(),\n    connectIntegration(),\n    tediousIntegration(),\n    genericPoolIntegration(),\n    kafkaIntegration(),\n    amqplibIntegration(),\n    lruMemoizerIntegration(),\n    vercelAIIntegration(),\n    openAIIntegration(),\n    postgresJsIntegration(),\n  ];\n}\n\n/**\n * Get a list of methods to instrument OTEL, when preload instrumentation.\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function getOpenTelemetryInstrumentationToPreload(): (((options?: any) => void) & { id: string })[] {\n  return [\n    instrumentOtelHttp,\n    instrumentExpress,\n    instrumentExpressV5,\n    instrumentConnect,\n    instrumentFastify,\n    instrumentFastifyV3,\n    instrumentHapi,\n    instrumentKafka,\n    instrumentKoa,\n    instrumentLruMemoizer,\n    instrumentMongo,\n    instrumentMongoose,\n    instrumentMysql,\n    instrumentMysql2,\n    instrumentPostgres,\n    instrumentHapi,\n    instrumentGraphql,\n    instrumentRedis,\n    instrumentTedious,\n    instrumentGenericPool,\n    instrumentAmqplib,\n    instrumentVercelAi,\n    instrumentOpenAi,\n    instrumentPostgresJs,\n  ];\n}\n"], "names": ["expressIntegration", "fastifyIntegration", "graphqlIntegration", "mongoIntegration", "mongooseIntegration", "mysqlIntegration", "mysql2Integration", "redisIntegration", "postgresIntegration", "prismaIntegration", "hapiIntegration", "koaIntegration", "connectIntegration", "tediousIntegration", "genericPoolIntegration", "kafkaIntegration", "amqplibIntegration", "lruMemoizerIntegration", "vercelAIIntegration", "openAIIntegration", "postgresJsIntegration", "instrumentOtelHttp", "instrumentExpress", "instrumentExpressV5", "instrumentConnect", "instrumentFastify", "instrumentFastifyV3", "instrumentHapi", "instrumentKafka", "instrumentKoa", "instrumentLruMemoizer", "instrumentMongo", "instrumentMongoose", "instrumentMysql", "instrumentMysql2", "instrumentPostgres", "instrumentGraphql", "instrumentRedis", "instrumentTedious", "instrumentGenericPool", "instrumentAmqplib", "instrumentVercelAi", "instrumentOpenAi", "instrumentPostgresJs"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAwBA;;CAEA,GACO,SAAS,8BAA8B,GAAkB;IAC9D,OAAO;QACLA,QAAAA,kBAAkB,EAAE;QACpBC,QAAAA,kBAAkB,EAAE;QACpBC,QAAAA,kBAAkB,EAAE;QACpBC,MAAAA,gBAAgB,EAAE;QAClBC,SAAAA,mBAAmB,EAAE;QACrBC,MAAAA,gBAAgB,EAAE;QAClBC,OAAAA,iBAAiB,EAAE;QACnBC,MAAAA,gBAAgB,EAAE;QAClBC,SAAAA,mBAAmB,EAAE;QACrBC,OAAAA,iBAAiB,EAAE;QACnBC,QAAAA,eAAe,EAAE;QACjBC,IAAAA,cAAc,EAAE;QAChBC,QAAAA,kBAAkB,EAAE;QACpBC,QAAAA,kBAAkB,EAAE;QACpBC,YAAAA,sBAAsB,EAAE;QACxBC,MAAAA,gBAAgB,EAAE;QAClBC,QAAAA,kBAAkB,EAAE;QACpBC,YAAAA,sBAAsB,EAAE;QACxBC,QAAAA,mBAAmB,EAAE;QACrBC,QAAAA,iBAAiB,EAAE;QACnBC,WAAAA,qBAAqB,EAAE;KACxB;AACH;AAEA;;CAEA,GACA,8DAAA;AACO,SAAS,wCAAwC,GAAmD;IACzG,OAAO;QACLC,MAAAA,kBAAkB;QAClBC,QAAAA,iBAAiB;QACjBC,QAAAA,mBAAmB;QACnBC,QAAAA,iBAAiB;QACjBC,QAAAA,iBAAiB;QACjBC,QAAAA,mBAAmB;QACnBC,QAAAA,cAAc;QACdC,MAAAA,eAAe;QACfC,IAAAA,aAAa;QACbC,YAAAA,qBAAqB;QACrBC,MAAAA,eAAe;QACfC,SAAAA,kBAAkB;QAClBC,MAAAA,eAAe;QACfC,OAAAA,gBAAgB;QAChBC,SAAAA,kBAAkB;QAClBR,QAAAA,cAAc;QACdS,QAAAA,iBAAiB;QACjBC,MAAAA,eAAe;QACfC,QAAAA,iBAAiB;QACjBC,YAAAA,qBAAqB;QACrBC,QAAAA,iBAAiB;QACjBC,QAAAA,kBAAkB;QAClBC,QAAAA,gBAAgB;QAChBC,WAAAA,oBAAoB;KACrB;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4240, "column": 0}, "map": {"version": 3, "file": "initOtel.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/sdk/initOtel.ts"], "sourcesContent": ["import { context, propagation, trace } from '@opentelemetry/api';\nimport { Resource } from '@opentelemetry/resources';\nimport type { SpanProcessor } from '@opentelemetry/sdk-trace-base';\nimport { BasicTracerProvider } from '@opentelemetry/sdk-trace-base';\nimport {\n  ATTR_SERVICE_NAME,\n  ATTR_SERVICE_VERSION,\n  SEMRESATTRS_SERVICE_NAMESPACE,\n} from '@opentelemetry/semantic-conventions';\nimport { consoleSandbox, debug as coreDebug, GLOBAL_OBJ, SDK_VERSION } from '@sentry/core';\nimport { type NodeClient, isCjs, SentryContextManager, setupOpenTelemetryLogger } from '@sentry/node-core';\nimport { SentryPropagator, SentrySampler, SentrySpanProcessor } from '@sentry/opentelemetry';\nimport { createAddHookMessageChannel } from 'import-in-the-middle';\nimport moduleModule from 'module';\nimport { DEBUG_BUILD } from '../debug-build';\nimport { getOpenTelemetryInstrumentationToPreload } from '../integrations/tracing';\n\n// About 277h - this must fit into new Array(len)!\nconst MAX_MAX_SPAN_WAIT_DURATION = 1_000_000;\n\ninterface AdditionalOpenTelemetryOptions {\n  /** Additional SpanProcessor instances that should be used. */\n  spanProcessors?: SpanProcessor[];\n}\n\n/**\n * Initialize OpenTelemetry for Node.\n */\nexport function initOpenTelemetry(client: NodeClient, options: AdditionalOpenTelemetryOptions = {}): void {\n  if (client.getOptions().debug) {\n    setupOpenTelemetryLogger();\n  }\n\n  const provider = setupOtel(client, options);\n  client.traceProvider = provider;\n}\n\n/** Initialize the ESM loader. */\nexport function maybeInitializeEsmLoader(): void {\n  const [nodeMajor = 0, nodeMinor = 0] = process.versions.node.split('.').map(Number);\n\n  // Register hook was added in v20.6.0 and v18.19.0\n  if (nodeMajor >= 21 || (nodeMajor === 20 && nodeMinor >= 6) || (nodeMajor === 18 && nodeMinor >= 19)) {\n    if (!GLOBAL_OBJ._sentryEsmLoaderHookRegistered) {\n      try {\n        const { addHookMessagePort } = createAddHookMessageChannel();\n        // @ts-expect-error register is available in these versions\n        moduleModule.register('import-in-the-middle/hook.mjs', import.meta.url, {\n          data: { addHookMessagePort, include: [] },\n          transferList: [addHookMessagePort],\n        });\n      } catch (error) {\n        coreDebug.warn('Failed to register ESM hook', error);\n      }\n    }\n  } else {\n    consoleSandbox(() => {\n      // eslint-disable-next-line no-console\n      console.warn(\n        `[Sentry] You are using Node.js v${process.versions.node} in ESM mode (\"import syntax\"). The Sentry Node.js SDK is not compatible with ESM in Node.js versions before 18.19.0 or before 20.6.0. Please either build your application with CommonJS (\"require() syntax\"), or upgrade your Node.js version.`,\n      );\n    });\n  }\n}\n\ninterface NodePreloadOptions {\n  debug?: boolean;\n  integrations?: string[];\n}\n\n/**\n * Preload OpenTelemetry for Node.\n * This can be used to preload instrumentation early, but set up Sentry later.\n * By preloading the OTEL instrumentation wrapping still happens early enough that everything works.\n */\nexport function preloadOpenTelemetry(options: NodePreloadOptions = {}): void {\n  const { debug } = options;\n\n  if (debug) {\n    coreDebug.enable();\n  }\n\n  if (!isCjs()) {\n    maybeInitializeEsmLoader();\n  }\n\n  // These are all integrations that we need to pre-load to ensure they are set up before any other code runs\n  getPreloadMethods(options.integrations).forEach(fn => {\n    fn();\n\n    if (debug) {\n      coreDebug.log(`[Sentry] Preloaded ${fn.id} instrumentation`);\n    }\n  });\n}\n\nfunction getPreloadMethods(integrationNames?: string[]): ((() => void) & { id: string })[] {\n  const instruments = getOpenTelemetryInstrumentationToPreload();\n\n  if (!integrationNames) {\n    return instruments;\n  }\n\n  return instruments.filter(instrumentation => integrationNames.includes(instrumentation.id));\n}\n\n/** Just exported for tests. */\nexport function setupOtel(client: NodeClient, options: AdditionalOpenTelemetryOptions = {}): BasicTracerProvider {\n  // Create and configure NodeTracerProvider\n  const provider = new BasicTracerProvider({\n    sampler: new SentrySampler(client),\n    resource: new Resource({\n      [ATTR_SERVICE_NAME]: 'node',\n      // eslint-disable-next-line deprecation/deprecation\n      [SEMRESATTRS_SERVICE_NAMESPACE]: 'sentry',\n      [ATTR_SERVICE_VERSION]: SDK_VERSION,\n    }),\n    forceFlushTimeoutMillis: 500,\n    spanProcessors: [\n      new SentrySpanProcessor({\n        timeout: _clampSpanProcessorTimeout(client.getOptions().maxSpanWaitDuration),\n      }),\n      ...(options.spanProcessors || []),\n    ],\n  });\n\n  // Register as globals\n  trace.setGlobalTracerProvider(provider);\n  propagation.setGlobalPropagator(new SentryPropagator());\n  context.setGlobalContextManager(new SentryContextManager());\n\n  return provider;\n}\n\n/** Just exported for tests. */\nexport function _clampSpanProcessorTimeout(maxSpanWaitDuration: number | undefined): number | undefined {\n  if (maxSpanWaitDuration == null) {\n    return undefined;\n  }\n\n  // We guard for a max. value here, because we create an array with this length\n  // So if this value is too large, this would fail\n  if (maxSpanWaitDuration > MAX_MAX_SPAN_WAIT_DURATION) {\n    DEBUG_BUILD &&\n      coreDebug.warn(`\\`maxSpanWaitDuration\\` is too high, using the maximum value of ${MAX_MAX_SPAN_WAIT_DURATION}`);\n    return MAX_MAX_SPAN_WAIT_DURATION;\n  } else if (maxSpanWaitDuration <= 0 || Number.isNaN(maxSpanWaitDuration)) {\n    DEBUG_BUILD && coreDebug.warn('`maxSpanWaitDuration` must be a positive number, using default value instead.');\n    return undefined;\n  }\n\n  return maxSpanWaitDuration;\n}\n"], "names": ["setupOpenTelemetryLogger", "GLOBAL_OBJ", "createAddHookMessageChannel", "moduleModule", "coreDebug", "consoleSandbox", "isCjs", "getOpenTelemetryInstrumentationToPreload", "BasicTracerProvider", "SentrySampler", "Resource", "ATTR_SERVICE_NAME", "SEMRESATTRS_SERVICE_NAMESPACE", "ATTR_SERVICE_VERSION", "SDK_VERSION", "SentrySpanProcessor", "trace", "propagation", "SentryPropagator", "context", "SentryContextManager", "DEBUG_BUILD"], "mappings": ";;;;;;;;;;;;;;;AAiBA,kDAAA;AACA,MAAM,0BAAA,GAA6B,OAAS;AAO5C;;CAEA,GACO,SAAS,iBAAiB,CAAC,MAAM,EAAc,OAAO,GAAmC,CAAA,CAAE,EAAQ;IACxG,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC,KAAK,EAAE;QAC7BA,SAAAA,wBAAwB,EAAE;IAC9B;IAEE,MAAM,WAAW,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC;IAC3C,MAAM,CAAC,aAAA,GAAgB,QAAQ;AACjC;AAEA,+BAAA,GACO,SAAS,wBAAwB,GAAS;IAC/C,MAAM,CAAC,SAAA,GAAY,CAAC,EAAE,SAAA,GAAY,CAAC,CAAA,GAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC;IAErF,kDAAA;IACE,IAAI,SAAA,IAAa,EAAA,IAAO,SAAA,KAAc,EAAA,IAAM,SAAA,IAAa,CAAC,CAAA,GAAM,SAAA,KAAc,EAAA,IAAM,SAAA,IAAa,EAAE,CAAC,CAAE;QACpG,IAAI,CAACC,KAAAA,UAAU,CAAC,8BAA8B,EAAE;YAC9C,IAAI;gBACF,MAAM,EAAE,kBAAA,EAAA,GAAuBC,kBAAAA,2BAA2B,EAAE;gBACpE,2DAAA;gBACQC,aAAAA,OAAY,CAAC,QAAQ,CAAC,+BAA+B,EAAE,OAAA,aAAA,cAAA,iEAAA,aAAA,CAAA,YAAA,IAAA,GAAA,0BAAA,uBAAA,OAAA,CAAA,WAAA,OAAA,YAAA,uBAAA,GAAA,IAAA,IAAA,IAAA,mBAAA,SAAA,OAAA,EAAA,IAAA,EAAe,AAAE;oBACtE,IAAI,EAAE;wBAAE,kBAAkB;wBAAE,OAAO,EAAE,EAAC;oBAAA,CAAG;oBACzC,YAAY,EAAE;wBAAC,kBAAkB;qBAAC;gBAC5C,CAAS,CAAC;YACV,CAAM,CAAE,OAAO,KAAK,EAAE;gBACdC,KAAAA,KAAS,CAAC,IAAI,CAAC,6BAA6B,EAAE,KAAK,CAAC;YAC5D;QACA;IACA,OAAS;QACLC,KAAAA,cAAc,CAAC,MAAM;YACzB,sCAAA;YACM,OAAO,CAAC,IAAI,CACV,CAAC,gCAAgC,EAAE,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,gPAAgP,CAAC;QAElT,CAAK,CAAC;IACN;AACA;AAOA;;;;CAIA,GACO,SAAS,oBAAoB,CAAC,OAAO,GAAuB,CAAA,CAAE,EAAQ;IAC3E,MAAM,EAAE,KAAA,EAAM,GAAI,OAAO;IAEzB,IAAI,KAAK,EAAE;QACTD,KAAAA,KAAS,CAAC,MAAM,EAAE;IACtB;IAEE,IAAI,CAACE,SAAAA,KAAK,EAAE,EAAE;QACZ,wBAAwB,EAAE;IAC9B;IAEA,2GAAA;IACE,iBAAiB,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,OAAO,EAAC,EAAA,IAAM;QACpD,EAAE,EAAE;QAEJ,IAAI,KAAK,EAAE;YACTF,KAAAA,KAAS,CAAC,GAAG,CAAC,CAAC,mBAAmB,EAAE,EAAE,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC;QAClE;IACA,CAAG,CAAC;AACJ;AAEA,SAAS,iBAAiB,CAAC,gBAAgB,EAAgD;IACzF,MAAM,WAAA,GAAcG,MAAAA,wCAAwC,EAAE;IAE9D,IAAI,CAAC,gBAAgB,EAAE;QACrB,OAAO,WAAW;IACtB;IAEE,OAAO,WAAW,CAAC,MAAM,EAAC,eAAA,GAAmB,gBAAgB,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;AAC7F;AAEA,6BAAA,GACO,SAAS,SAAS,CAAC,MAAM,EAAc,OAAO,GAAmC,CAAA,CAAE,EAAuB;IACjH,0CAAA;IACE,MAAM,QAAA,GAAW,IAAIC,aAAAA,mBAAmB,CAAC;QACvC,OAAO,EAAE,IAAIC,cAAAA,aAAa,CAAC,MAAM,CAAC;QAClC,QAAQ,EAAE,IAAIC,UAAAA,QAAQ,CAAC;YACrB,CAACC,oBAAAA,iBAAiB,CAAA,EAAG,MAAM;YACjC,mDAAA;YACM,CAACC,oBAAAA,6BAA6B,CAAA,EAAG,QAAQ;YACzC,CAACC,oBAAAA,oBAAoB,CAAA,EAAGC,KAAAA,WAAW;QACzC,CAAK,CAAC;QACF,uBAAuB,EAAE,GAAG;QAC5B,cAAc,EAAE;YACd,IAAIC,cAAAA,mBAAmB,CAAC;gBACtB,OAAO,EAAE,0BAA0B,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,mBAAmB,CAAC;YACpF,CAAO,CAAC;eACE,OAAO,CAAC,cAAA,IAAkB,EAAE,CAAC;SAClC;IACL,CAAG,CAAC;IAEJ,sBAAA;IACEC,IAAAA,KAAK,CAAC,uBAAuB,CAAC,QAAQ,CAAC;IACvCC,IAAAA,WAAW,CAAC,mBAAmB,CAAC,IAAIC,cAAAA,gBAAgB,EAAE,CAAC;IACvDC,IAAAA,OAAO,CAAC,uBAAuB,CAAC,IAAIC,SAAAA,oBAAoB,EAAE,CAAC;IAE3D,OAAO,QAAQ;AACjB;AAEA,6BAAA,GACO,SAAS,0BAA0B,CAAC,mBAAmB,EAA0C;IACtG,IAAI,mBAAA,IAAuB,IAAI,EAAE;QAC/B,OAAO,SAAS;IACpB;IAEA,8EAAA;IACA,iDAAA;IACE,IAAI,mBAAA,GAAsB,0BAA0B,EAAE;QACpDC,WAAAA,WAAA,IACEjB,KAAAA,KAAS,CAAC,IAAI,CAAC,CAAC,gEAAgE,EAAE,0BAA0B,CAAC,CAAA,CAAA;QACA,OAAA,0BAAA;IACA,CAAA,MAAA,IAAA,mBAAA,IAAA,CAAA,IAAA,MAAA,CAAA,KAAA,CAAA,mBAAA,CAAA,EAAA;QACAiB,WAAAA,WAAA,IAAAjB,KAAAA,KAAA,CAAA,IAAA,CAAA,+EAAA,CAAA;QACA,OAAA,SAAA;IACA;IAEA,OAAA,mBAAA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4370, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node/src/sdk/index.ts"], "sourcesContent": ["import type { Integration, Options } from '@sentry/core';\nimport { applySdkMetadata, hasSpansEnabled } from '@sentry/core';\nimport type { NodeClient } from '@sentry/node-core';\nimport {\n  getDefaultIntegrations as getNodeCoreDefaultIntegrations,\n  init as initNodeCore,\n  validateOpenTelemetrySetup,\n} from '@sentry/node-core';\nimport { httpIntegration } from '../integrations/http';\nimport { nativeNodeFetchIntegration } from '../integrations/node-fetch';\nimport { getAutoPerformanceIntegrations } from '../integrations/tracing';\nimport type { NodeOptions } from '../types';\nimport { initOpenTelemetry } from './initOtel';\n\n/**\n * Get default integrations, excluding performance.\n */\nexport function getDefaultIntegrationsWithoutPerformance(): Integration[] {\n  const nodeCoreIntegrations = getNodeCoreDefaultIntegrations();\n\n  // Filter out the node-core HTTP and NodeFetch integrations and replace them with Node SDK's composite versions\n  return nodeCoreIntegrations\n    .filter(integration => integration.name !== 'Http' && integration.name !== 'NodeFetch')\n    .concat(httpIntegration(), nativeNodeFetchIntegration());\n}\n\n/** Get the default integrations for the Node SDK. */\nexport function getDefaultIntegrations(options: Options): Integration[] {\n  return [\n    ...getDefaultIntegrationsWithoutPerformance(),\n    // We only add performance integrations if tracing is enabled\n    // Note that this means that without tracing enabled, e.g. `expressIntegration()` will not be added\n    // This means that generally request isolation will work (because that is done by httpIntegration)\n    // But `transactionName` will not be set automatically\n    ...(hasSpansEnabled(options) ? getAutoPerformanceIntegrations() : []),\n  ];\n}\n\n/**\n * Initialize Sentry for Node.\n */\nexport function init(options: NodeOptions | undefined = {}): NodeClient | undefined {\n  return _init(options, getDefaultIntegrations);\n}\n\n/**\n * Internal initialization function.\n */\nfunction _init(\n  options: NodeOptions | undefined = {},\n  getDefaultIntegrationsImpl: (options: Options) => Integration[],\n): NodeClient | undefined {\n  applySdkMetadata(options, 'node');\n\n  const client = initNodeCore({\n    ...options,\n    // Only use Node SDK defaults if none provided\n    defaultIntegrations: options.defaultIntegrations ?? getDefaultIntegrationsImpl(options),\n  });\n\n  // Add Node SDK specific OpenTelemetry setup\n  if (client && !options.skipOpenTelemetrySetup) {\n    initOpenTelemetry(client, {\n      spanProcessors: options.openTelemetrySpanProcessors,\n    });\n    validateOpenTelemetrySetup();\n  }\n\n  return client;\n}\n\n/**\n * Initialize Sentry for Node, without any integrations added by default.\n */\nexport function initWithoutDefaultIntegrations(options: NodeOptions | undefined = {}): NodeClient | undefined {\n  return _init(options, () => []);\n}\n"], "names": ["getNodeCoreDefaultIntegrations", "httpIntegration", "nativeNodeFetchIntegration", "hasSpansEnabled", "getAutoPerformanceIntegrations", "applySdkMetadata", "initNodeCore", "initOpenTelemetry", "validateOpenTelemetrySetup"], "mappings": ";;;;;;;;;AAcA;;CAEA,GACO,SAAS,wCAAwC,GAAkB;IACxE,MAAM,oBAAA,GAAuBA,SAAAA,sBAA8B,EAAE;IAE/D,+GAAA;IACE,OAAO,qBACJ,MAAM,EAAC,WAAA,GAAe,WAAW,CAAC,IAAA,KAAS,UAAU,WAAW,CAAC,IAAA,KAAS,WAAW,EACrF,MAAM,CAACC,QAAAA,eAAe,EAAE,EAAEC,QAAAA,0BAA0B,EAAE,CAAC;AAC5D;AAEA,mDAAA,GACO,SAAS,sBAAsB,CAAC,OAAO,EAA0B;IACtE,OAAO;WACF,wCAAwC,EAAE;QACjD,6DAAA;QACA,mGAAA;QACA,kGAAA;QACA,sDAAA;WACQC,KAAAA,eAAe,CAAC,OAAO,CAAA,GAAIC,MAAAA,8BAA8B,EAAC,GAAI,EAAE,CAAC;KACtE;AACH;AAEA;;CAEA,GACO,SAAS,IAAI,CAAC,OAAO,GAA4B,CAAA,CAAE,EAA0B;IAClF,OAAO,KAAK,CAAC,OAAO,EAAE,sBAAsB,CAAC;AAC/C;AAEA;;CAEA,GACA,SAAS,KAAK,CACZ,OAAO,GAA4B,CAAA,CAAE,EACrC,0BAA0B;IAE1BC,KAAAA,gBAAgB,CAAC,OAAO,EAAE,MAAM,CAAC;IAEjC,MAAM,MAAA,GAASC,SAAAA,IAAY,CAAC;QAC1B,GAAG,OAAO;QACd,8CAAA;QACI,mBAAmB,EAAE,OAAO,CAAC,mBAAA,IAAuB,0BAA0B,CAAC,OAAO,CAAC;IAC3F,CAAG,CAAC;IAEJ,4CAAA;IACE,IAAI,MAAA,IAAU,CAAC,OAAO,CAAC,sBAAsB,EAAE;QAC7CC,SAAAA,iBAAiB,CAAC,MAAM,EAAE;YACxB,cAAc,EAAE,OAAO,CAAC,2BAA2B;QACzD,CAAK,CAAC;QACFC,SAAAA,0BAA0B,EAAE;IAChC;IAEE,OAAO,MAAM;AACf;AAEA;;CAEA,GACO,SAAS,8BAA8B,CAAC,OAAO,GAA4B,CAAA,CAAE,EAA0B;IAC5G,OAAO,KAAK,CAAC,OAAO,EAAE,IAAM,EAAE,CAAC;AACjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4433, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": [], "names": [], "mappings": "", "debugId": null}}]}