{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/lib/db/schema.ts"], "sourcesContent": ["import { relations } from 'drizzle-orm';\r\nimport {\r\n  boolean,\r\n  integer,\r\n  json,\r\n  pgEnum,\r\n  pgTable,\r\n  serial,\r\n  text,\r\n  timestamp,\r\n  varchar,\r\n  decimal\r\n} from 'drizzle-orm/pg-core';\r\n\r\n// Enums\r\nexport const userRoleEnum = pgEnum('user_role', [\r\n  'student',\r\n  'teacher',\r\n  'super_admin'\r\n]);\r\nexport const institutionTypeEnum = pgEnum('institution_type', [\r\n  'sd-negeri',\r\n  'sd-swasta',\r\n  'smp-negeri',\r\n  'smp-swasta',\r\n  'sma-negeri',\r\n  'sma-swasta',\r\n  'university-negeri',\r\n  'university-swasta',\r\n  'institution-training',\r\n  'institution-course',\r\n  'institution-other'\r\n]);\r\nexport const subscriptionPlanEnum = pgEnum('subscription_plan', [\r\n  'basic',\r\n  'pro',\r\n  'enterprise'\r\n]);\r\nexport const billingCycleEnum = pgEnum('billing_cycle', ['monthly', 'yearly']);\r\nexport const paymentStatusEnum = pgEnum('payment_status', ['paid', 'unpaid']);\r\nexport const courseTypeEnum = pgEnum('course_type', ['self_paced', 'verified']);\r\nexport const enrollmentTypeEnum = pgEnum('enrollment_type', ['code', 'invitation', 'both', 'purchase']);\r\nexport const questionTypeEnum = pgEnum('question_type', [\r\n  'multiple_choice',\r\n  'true_false',\r\n  'essay'\r\n]);\r\n\r\n// Users table\r\nexport const users = pgTable('users', {\r\n  id: serial('id').primaryKey(),\r\n  name: varchar('name', { length: 255 }).notNull(),\r\n  email: varchar('email', { length: 255 }).notNull().unique(),\r\n  password: varchar('password', { length: 255 }).notNull(),\r\n  role: userRoleEnum('role').notNull().default('student'),\r\n  institutionId: integer('institution_id').references(() => institutions.id),\r\n  createdAt: timestamp('created_at').defaultNow().notNull(),\r\n  updatedAt: timestamp('updated_at').defaultNow().notNull()\r\n});\r\n\r\n// Institutions table\r\nexport const institutions = pgTable('institutions', {\r\n  id: serial('id').primaryKey(),\r\n  name: varchar('name', { length: 255 }).notNull(),\r\n  type: institutionTypeEnum('type').notNull(),\r\n  subscriptionPlan: subscriptionPlanEnum('subscription_plan')\r\n    .notNull()\r\n    .default('basic'),\r\n  billingCycle: billingCycleEnum('billing_cycle').notNull().default('monthly'),\r\n  paymentStatus: paymentStatusEnum('payment_status')\r\n    .notNull()\r\n    .default('unpaid'),\r\n  paymentDueDate: timestamp('payment_due_date'),\r\n  studentCount: integer('student_count').default(0),\r\n  teacherCount: integer('teacher_count').default(0),\r\n  coverPicture: text('cover_picture'),\r\n  createdAt: timestamp('created_at').defaultNow().notNull(),\r\n  updatedAt: timestamp('updated_at').defaultNow().notNull()\r\n});\r\n\r\n// Classes table\r\nexport const classes = pgTable('classes', {\r\n  id: serial('id').primaryKey(),\r\n  name: varchar('name', { length: 255 }).notNull(),\r\n  description: text('description'),\r\n  institutionId: integer('institution_id')\r\n    .references(() => institutions.id)\r\n    .notNull(),\r\n  teacherId: integer('teacher_id')\r\n    .references(() => users.id)\r\n    .notNull(),\r\n  coverPicture: text('cover_picture'),\r\n  createdAt: timestamp('created_at').defaultNow().notNull(),\r\n  updatedAt: timestamp('updated_at').defaultNow().notNull()\r\n});\r\n\r\n// Courses table\r\nexport const courses = pgTable('courses', {\r\n  id: serial('id').primaryKey(),\r\n  name: varchar('name', { length: 255 }).notNull(),\r\n  description: text('description'),\r\n  instructor: varchar('instructor', { length: 255 }),\r\n  type: courseTypeEnum('type').notNull().default('self_paced'),\r\n  enrollmentType: enrollmentTypeEnum('enrollment_type').notNull().default('code'),\r\n  isPurchasable: boolean('is_purchasable').default(false),\r\n  price: decimal('price', { precision: 10, scale: 2 }),\r\n  currency: varchar('currency', { length: 10 }).default('IDR'),\r\n  previewMode: boolean('preview_mode').default(false),\r\n  startDate: timestamp('start_date'),\r\n  endDate: timestamp('end_date'),\r\n  teacherId: integer('teacher_id')\r\n    .references(() => users.id)\r\n    .notNull(),\r\n  institutionId: integer('institution_id')\r\n    .references(() => institutions.id)\r\n    .notNull(),\r\n  courseCode: varchar('course_code', { length: 50 }).unique(),\r\n  coverPicture: text('cover_picture'),\r\n  createdAt: timestamp('created_at').defaultNow().notNull(),\r\n  updatedAt: timestamp('updated_at').defaultNow().notNull()\r\n});\r\n\r\n// Course Admissions table\r\nexport const courseAdmissions = pgTable('course_admissions', {\r\n  id: serial('id').primaryKey(),\r\n  courseId: integer('course_id')\r\n    .references(() => courses.id)\r\n    .notNull(),\r\n  requirements: json('requirements'), // Array of strings\r\n  applicationDeadline: varchar('application_deadline', { length: 255 }),\r\n  prerequisites: json('prerequisites'), // Array of strings\r\n  createdAt: timestamp('created_at').defaultNow().notNull(),\r\n  updatedAt: timestamp('updated_at').defaultNow().notNull()\r\n});\r\n\r\n// Course Academics table\r\nexport const courseAcademics = pgTable('course_academics', {\r\n  id: serial('id').primaryKey(),\r\n  courseId: integer('course_id')\r\n    .references(() => courses.id)\r\n    .notNull(),\r\n  credits: integer('credits'),\r\n  workload: varchar('workload', { length: 255 }),\r\n  assessment: json('assessment'), // Array of strings\r\n  createdAt: timestamp('created_at').defaultNow().notNull(),\r\n  updatedAt: timestamp('updated_at').defaultNow().notNull()\r\n});\r\n\r\n// Course Tuition and Financing table\r\nexport const courseTuitionAndFinancing = pgTable('course_tuition_financing', {\r\n  id: serial('id').primaryKey(),\r\n  courseId: integer('course_id')\r\n    .references(() => courses.id)\r\n    .notNull(),\r\n  totalCost: decimal('total_cost', { precision: 10, scale: 2 }),\r\n  paymentOptions: json('payment_options'), // Array of strings\r\n  scholarships: json('scholarships'), // Array of strings\r\n  createdAt: timestamp('created_at').defaultNow().notNull(),\r\n  updatedAt: timestamp('updated_at').defaultNow().notNull()\r\n});\r\n\r\n// Course Careers table\r\nexport const courseCareers = pgTable('course_careers', {\r\n  id: serial('id').primaryKey(),\r\n  courseId: integer('course_id')\r\n    .references(() => courses.id)\r\n    .notNull(),\r\n  outcomes: json('outcomes'), // Array of strings\r\n  industries: json('industries'), // Array of strings\r\n  averageSalary: varchar('average_salary', { length: 255 }),\r\n  createdAt: timestamp('created_at').defaultNow().notNull(),\r\n  updatedAt: timestamp('updated_at').defaultNow().notNull()\r\n});\r\n\r\n// Course Student Experience table\r\nexport const courseStudentExperience = pgTable('course_student_experience', {\r\n  id: serial('id').primaryKey(),\r\n  courseId: integer('course_id')\r\n    .references(() => courses.id)\r\n    .notNull(),\r\n  testimonials: json('testimonials'), // Array of objects { name: string, feedback: string }\r\n  facilities: json('facilities'), // Array of strings\r\n  support: json('support'), // Array of strings\r\n  createdAt: timestamp('created_at').defaultNow().notNull(),\r\n  updatedAt: timestamp('updated_at').defaultNow().notNull()\r\n});\r\n\r\n// Course enrollments (many-to-many between courses and classes)\r\nexport const courseEnrollments = pgTable('course_enrollments', {\r\n  id: serial('id').primaryKey(),\r\n  courseId: integer('course_id')\r\n    .references(() => courses.id)\r\n    .notNull(),\r\n  classId: integer('class_id')\r\n    .references(() => classes.id)\r\n    .notNull(),\r\n  enrolledAt: timestamp('enrolled_at').defaultNow().notNull()\r\n});\r\n\r\n// Student enrollments (many-to-many between students and courses)\r\nexport const studentEnrollments = pgTable('student_enrollments', {\r\n  id: serial('id').primaryKey(),\r\n  studentId: integer('student_id')\r\n    .references(() => users.id)\r\n    .notNull(),\r\n  courseId: integer('course_id')\r\n    .references(() => courses.id)\r\n    .notNull(),\r\n  enrolledAt: timestamp('enrolled_at').defaultNow().notNull(),\r\n  completedAt: timestamp('completed_at'),\r\n  finalScore: decimal('final_score', { precision: 5, scale: 2 }),\r\n  certificateGenerated: boolean('certificate_generated').default(false)\r\n});\r\n\r\n// Modules table\r\nexport const modules = pgTable('modules', {\r\n  id: serial('id').primaryKey(),\r\n  name: varchar('name', { length: 255 }).notNull(),\r\n  description: text('description'),\r\n  courseId: integer('course_id')\r\n    .references(() => courses.id)\r\n    .notNull(),\r\n  orderIndex: integer('order_index').notNull(),\r\n  startDate: timestamp('start_date'),\r\n  endDate: timestamp('end_date'),\r\n  createdAt: timestamp('created_at').defaultNow().notNull(),\r\n  updatedAt: timestamp('updated_at').defaultNow().notNull()\r\n});\r\n\r\n// Chapters table\r\nexport const chapters = pgTable('chapters', {\r\n  id: serial('id').primaryKey(),\r\n  name: varchar('name', { length: 255 }).notNull(),\r\n  content: json('content'), // JSON content\r\n  moduleId: integer('module_id')\r\n    .references(() => modules.id)\r\n    .notNull(),\r\n  orderIndex: integer('order_index').notNull(),\r\n  createdAt: timestamp('created_at').defaultNow().notNull(),\r\n  updatedAt: timestamp('updated_at').defaultNow().notNull()\r\n});\r\n\r\n// Quizzes table\r\nexport const quizzes = pgTable('quizzes', {\r\n  id: serial('id').primaryKey(),\r\n  name: varchar('name', { length: 255 }).notNull(),\r\n  description: text('description'),\r\n  chapterId: integer('chapter_id').references(() => chapters.id),\r\n  moduleId: integer('module_id').references(() => modules.id),\r\n  courseId: integer('course_id').references(() => courses.id),\r\n  quizType: varchar('quiz_type', { length: 50 }).notNull().default('chapter'), // 'chapter', 'module', 'final'\r\n  minimumScore: decimal('minimum_score', { precision: 5, scale: 2 })\r\n    .notNull()\r\n    .default('70'),\r\n  timeLimit: integer('time_limit'), // in minutes\r\n  startDate: timestamp('start_date'),\r\n  endDate: timestamp('end_date'),\r\n  isActive: boolean('is_active').default(true),\r\n  createdAt: timestamp('created_at').defaultNow().notNull(),\r\n  updatedAt: timestamp('updated_at').defaultNow().notNull()\r\n});\r\n\r\n// Questions table\r\nexport const questions = pgTable('questions', {\r\n  id: serial('id').primaryKey(),\r\n  quizId: integer('quiz_id')\r\n    .references(() => quizzes.id)\r\n    .notNull(),\r\n  type: questionTypeEnum('type').notNull(),\r\n  question: json('question').notNull(),\r\n  options: json('options'), // For multiple choice questions with choices array and correct_answer boolean\r\n  essayAnswer: text('essay_answer'),\r\n  explanation: json('explanation'),\r\n  points: decimal('points', { precision: 5, scale: 2 }).notNull().default('1'),\r\n  orderIndex: integer('order_index').notNull(),\r\n  createdAt: timestamp('created_at').defaultNow().notNull(),\r\n  updatedAt: timestamp('updated_at').defaultNow().notNull()\r\n});\r\n\r\n// Quiz attempts table\r\nexport const quizAttempts = pgTable('quiz_attempts', {\r\n  id: serial('id').primaryKey(),\r\n  studentId: integer('student_id')\r\n    .references(() => users.id)\r\n    .notNull(),\r\n  quizId: integer('quiz_id')\r\n    .references(() => quizzes.id)\r\n    .notNull(),\r\n  score: decimal('score', { precision: 5, scale: 2 }),\r\n  totalPoints: decimal('total_points', { precision: 5, scale: 2 }),\r\n  passed: boolean('passed').default(false),\r\n  startedAt: timestamp('started_at').defaultNow().notNull(),\r\n  completedAt: timestamp('completed_at'),\r\n  answers: json('answers'), // Store student answers\r\n  createdAt: timestamp('created_at').defaultNow().notNull(),\r\n  updatedAt: timestamp('updated_at').defaultNow().notNull()\r\n});\r\n\r\n// Class enrollments table\r\nexport const classEnrollments = pgTable('class_enrollments', {\r\n  id: serial('id').primaryKey(),\r\n  studentId: integer('student_id')\r\n    .references(() => users.id)\r\n    .notNull(),\r\n  classId: integer('class_id')\r\n    .references(() => classes.id)\r\n    .notNull(),\r\n  enrolledAt: timestamp('enrolled_at').defaultNow().notNull(),\r\n  status: varchar('status', { length: 20 }).default('active').notNull(),\r\n  createdAt: timestamp('created_at').defaultNow().notNull(),\r\n  updatedAt: timestamp('updated_at').defaultNow().notNull()\r\n});\r\n\r\n// Student progress table\r\nexport const studentProgress = pgTable('student_progress', {\r\n  id: serial('id').primaryKey(),\r\n  studentId: integer('student_id')\r\n    .references(() => users.id)\r\n    .notNull(),\r\n  courseId: integer('course_id')\r\n    .references(() => courses.id)\r\n    .notNull(),\r\n  moduleId: integer('module_id').references(() => modules.id),\r\n  chapterId: integer('chapter_id').references(() => chapters.id),\r\n  completed: boolean('completed').default(false),\r\n  completedAt: timestamp('completed_at'),\r\n  createdAt: timestamp('created_at').defaultNow().notNull(),\r\n  updatedAt: timestamp('updated_at').defaultNow().notNull()\r\n});\r\n\r\n// Relations\r\nexport const usersRelations = relations(users, ({ one, many }) => ({\r\n  institution: one(institutions, {\r\n    fields: [users.institutionId],\r\n    references: [institutions.id]\r\n  }),\r\n  teachingClasses: many(classes),\r\n  teachingCourses: many(courses),\r\n  studentEnrollments: many(studentEnrollments),\r\n  classEnrollments: many(classEnrollments),\r\n  quizAttempts: many(quizAttempts),\r\n  progress: many(studentProgress)\r\n}));\r\n\r\nexport const courseAdmissionsRelations = relations(courseAdmissions, ({ one }) => ({\r\n  course: one(courses, {\r\n    fields: [courseAdmissions.courseId],\r\n    references: [courses.id]\r\n  })\r\n}));\r\n\r\nexport const courseAcademicsRelations = relations(courseAcademics, ({ one }) => ({\r\n  course: one(courses, {\r\n    fields: [courseAcademics.courseId],\r\n    references: [courses.id]\r\n  })\r\n}));\r\n\r\nexport const courseTuitionAndFinancingRelations = relations(courseTuitionAndFinancing, ({ one }) => ({\r\n  course: one(courses, {\r\n    fields: [courseTuitionAndFinancing.courseId],\r\n    references: [courses.id]\r\n  })\r\n}));\r\n\r\nexport const courseCareersRelations = relations(courseCareers, ({ one }) => ({\r\n  course: one(courses, {\r\n    fields: [courseCareers.courseId],\r\n    references: [courses.id]\r\n  })\r\n}));\r\n\r\nexport const courseStudentExperienceRelations = relations(courseStudentExperience, ({ one }) => ({\r\n  course: one(courses, {\r\n    fields: [courseStudentExperience.courseId],\r\n    references: [courses.id]\r\n  })\r\n}));\r\n\r\nexport const institutionsRelations = relations(institutions, ({ many }) => ({\r\n  users: many(users),\r\n  classes: many(classes),\r\n  courses: many(courses)\r\n}));\r\n\r\nexport const classesRelations = relations(classes, ({ one, many }) => ({\r\n  institution: one(institutions, {\r\n    fields: [classes.institutionId],\r\n    references: [institutions.id]\r\n  }),\r\n  teacher: one(users, {\r\n    fields: [classes.teacherId],\r\n    references: [users.id]\r\n  }),\r\n  courseEnrollments: many(courseEnrollments),\r\n  classEnrollments: many(classEnrollments)\r\n}));\r\n\r\nexport const coursesRelations = relations(courses, ({ one, many }) => ({\r\n  teacher: one(users, {\r\n    fields: [courses.teacherId],\r\n    references: [users.id]\r\n  }),\r\n  institution: one(institutions, {\r\n    fields: [courses.institutionId],\r\n    references: [institutions.id]\r\n  }),\r\n  modules: many(modules),\r\n  courseEnrollments: many(courseEnrollments),\r\n  studentEnrollments: many(studentEnrollments),\r\n  quizzes: many(quizzes),\r\n  progress: many(studentProgress),\r\n  admissions: many(courseAdmissions),\r\n  academics: many(courseAcademics),\r\n  tuitionAndFinancing: many(courseTuitionAndFinancing),\r\n  careers: many(courseCareers),\r\n  studentExperience: many(courseStudentExperience)\r\n}));\r\n\r\nexport const modulesRelations = relations(modules, ({ one, many }) => ({\r\n  course: one(courses, {\r\n    fields: [modules.courseId],\r\n    references: [courses.id]\r\n  }),\r\n  chapters: many(chapters),\r\n  quizzes: many(quizzes),\r\n  progress: many(studentProgress)\r\n}));\r\n\r\nexport const chaptersRelations = relations(chapters, ({ one, many }) => ({\r\n  module: one(modules, {\r\n    fields: [chapters.moduleId],\r\n    references: [modules.id]\r\n  }),\r\n  quizzes: many(quizzes),\r\n  progress: many(studentProgress)\r\n}));\r\n\r\nexport const quizzesRelations = relations(quizzes, ({ one, many }) => ({\r\n  chapter: one(chapters, {\r\n    fields: [quizzes.chapterId],\r\n    references: [chapters.id]\r\n  }),\r\n  module: one(modules, {\r\n    fields: [quizzes.moduleId],\r\n    references: [modules.id]\r\n  }),\r\n  course: one(courses, {\r\n    fields: [quizzes.courseId],\r\n    references: [courses.id]\r\n  }),\r\n  questions: many(questions),\r\n  attempts: many(quizAttempts)\r\n}));\r\n\r\nexport const questionsRelations = relations(questions, ({ one }) => ({\r\n  quiz: one(quizzes, {\r\n    fields: [questions.quizId],\r\n    references: [quizzes.id]\r\n  })\r\n}));\r\n\r\nexport const quizAttemptsRelations = relations(quizAttempts, ({ one }) => ({\r\n  student: one(users, {\r\n    fields: [quizAttempts.studentId],\r\n    references: [users.id]\r\n  }),\r\n  quiz: one(quizzes, {\r\n    fields: [quizAttempts.quizId],\r\n    references: [quizzes.id]\r\n  })\r\n}));\r\n\r\nexport const studentEnrollmentsRelations = relations(\r\n  studentEnrollments,\r\n  ({ one }) => ({\r\n    student: one(users, {\r\n      fields: [studentEnrollments.studentId],\r\n      references: [users.id]\r\n    }),\r\n    course: one(courses, {\r\n      fields: [studentEnrollments.courseId],\r\n      references: [courses.id]\r\n    })\r\n  })\r\n);\r\n\r\nexport const courseEnrollmentsRelations = relations(\r\n  courseEnrollments,\r\n  ({ one }) => ({\r\n    course: one(courses, {\r\n      fields: [courseEnrollments.courseId],\r\n      references: [courses.id]\r\n    }),\r\n    class: one(classes, {\r\n      fields: [courseEnrollments.classId],\r\n      references: [classes.id]\r\n    })\r\n  })\r\n);\r\n\r\nexport const classEnrollmentsRelations = relations(\r\n  classEnrollments,\r\n  ({ one }) => ({\r\n    student: one(users, {\r\n      fields: [classEnrollments.studentId],\r\n      references: [users.id]\r\n    }),\r\n    class: one(classes, {\r\n      fields: [classEnrollments.classId],\r\n      references: [classes.id]\r\n    })\r\n  })\r\n);\r\n\r\nexport const studentProgressRelations = relations(\r\n  studentProgress,\r\n  ({ one }) => ({\r\n    student: one(users, {\r\n      fields: [studentProgress.studentId],\r\n      references: [users.id]\r\n    }),\r\n    course: one(courses, {\r\n      fields: [studentProgress.courseId],\r\n      references: [courses.id]\r\n    }),\r\n    module: one(modules, {\r\n      fields: [studentProgress.moduleId],\r\n      references: [modules.id]\r\n    }),\r\n    chapter: one(chapters, {\r\n      fields: [studentProgress.chapterId],\r\n      references: [chapters.id]\r\n    })\r\n  })\r\n);\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAcO,MAAM,eAAe,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,aAAa;IAC9C;IACA;IACA;CACD;AACM,MAAM,sBAAsB,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,oBAAoB;IAC5D;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AACM,MAAM,uBAAuB,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,qBAAqB;IAC9D;IACA;IACA;CACD;AACM,MAAM,mBAAmB,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,iBAAiB;IAAC;IAAW;CAAS;AACtE,MAAM,oBAAoB,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,kBAAkB;IAAC;IAAQ;CAAS;AACrE,MAAM,iBAAiB,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,eAAe;IAAC;IAAc;CAAW;AACvE,MAAM,qBAAqB,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,mBAAmB;IAAC;IAAQ;IAAc;IAAQ;CAAW;AAC/F,MAAM,mBAAmB,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,iBAAiB;IACtD;IACA;IACA;CACD;AAGM,MAAM,QAAQ,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,SAAS;IACpC,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,MAAM,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;QAAE,QAAQ;IAAI,GAAG,OAAO;IAC9C,OAAO,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,SAAS;QAAE,QAAQ;IAAI,GAAG,OAAO,GAAG,MAAM;IACzD,UAAU,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,YAAY;QAAE,QAAQ;IAAI,GAAG,OAAO;IACtD,MAAM,aAAa,QAAQ,OAAO,GAAG,OAAO,CAAC;IAC7C,eAAe,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,kBAAkB,UAAU,CAAC,IAAM,aAAa,EAAE;IACzE,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IACvD,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;AACzD;AAGO,MAAM,eAAe,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,gBAAgB;IAClD,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,MAAM,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;QAAE,QAAQ;IAAI,GAAG,OAAO;IAC9C,MAAM,oBAAoB,QAAQ,OAAO;IACzC,kBAAkB,qBAAqB,qBACpC,OAAO,GACP,OAAO,CAAC;IACX,cAAc,iBAAiB,iBAAiB,OAAO,GAAG,OAAO,CAAC;IAClE,eAAe,kBAAkB,kBAC9B,OAAO,GACP,OAAO,CAAC;IACX,gBAAgB,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;IAC1B,cAAc,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,iBAAiB,OAAO,CAAC;IAC/C,cAAc,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,iBAAiB,OAAO,CAAC;IAC/C,cAAc,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACnB,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IACvD,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;AACzD;AAGO,MAAM,UAAU,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,WAAW;IACxC,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,MAAM,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;QAAE,QAAQ;IAAI,GAAG,OAAO;IAC9C,aAAa,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IAClB,eAAe,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,kBACpB,UAAU,CAAC,IAAM,aAAa,EAAE,EAChC,OAAO;IACV,WAAW,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,cAChB,UAAU,CAAC,IAAM,MAAM,EAAE,EACzB,OAAO;IACV,cAAc,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACnB,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IACvD,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;AACzD;AAGO,MAAM,UAAU,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,WAAW;IACxC,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,MAAM,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;QAAE,QAAQ;IAAI,GAAG,OAAO;IAC9C,aAAa,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IAClB,YAAY,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,cAAc;QAAE,QAAQ;IAAI;IAChD,MAAM,eAAe,QAAQ,OAAO,GAAG,OAAO,CAAC;IAC/C,gBAAgB,mBAAmB,mBAAmB,OAAO,GAAG,OAAO,CAAC;IACxE,eAAe,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,kBAAkB,OAAO,CAAC;IACjD,OAAO,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,SAAS;QAAE,WAAW;QAAI,OAAO;IAAE;IAClD,UAAU,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,YAAY;QAAE,QAAQ;IAAG,GAAG,OAAO,CAAC;IACtD,aAAa,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,gBAAgB,OAAO,CAAC;IAC7C,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;IACrB,SAAS,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;IACnB,WAAW,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,cAChB,UAAU,CAAC,IAAM,MAAM,EAAE,EACzB,OAAO;IACV,eAAe,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,kBACpB,UAAU,CAAC,IAAM,aAAa,EAAE,EAChC,OAAO;IACV,YAAY,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,eAAe;QAAE,QAAQ;IAAG,GAAG,MAAM;IACzD,cAAc,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACnB,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IACvD,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;AACzD;AAGO,MAAM,mBAAmB,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,qBAAqB;IAC3D,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,UAAU,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,aACf,UAAU,CAAC,IAAM,QAAQ,EAAE,EAC3B,OAAO;IACV,cAAc,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACnB,qBAAqB,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,wBAAwB;QAAE,QAAQ;IAAI;IACnE,eAAe,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACpB,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IACvD,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;AACzD;AAGO,MAAM,kBAAkB,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,oBAAoB;IACzD,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,UAAU,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,aACf,UAAU,CAAC,IAAM,QAAQ,EAAE,EAC3B,OAAO;IACV,SAAS,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE;IACjB,UAAU,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,YAAY;QAAE,QAAQ;IAAI;IAC5C,YAAY,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACjB,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IACvD,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;AACzD;AAGO,MAAM,4BAA4B,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,4BAA4B;IAC3E,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,UAAU,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,aACf,UAAU,CAAC,IAAM,QAAQ,EAAE,EAC3B,OAAO;IACV,WAAW,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,cAAc;QAAE,WAAW;QAAI,OAAO;IAAE;IAC3D,gBAAgB,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACrB,cAAc,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACnB,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IACvD,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;AACzD;AAGO,MAAM,gBAAgB,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,kBAAkB;IACrD,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,UAAU,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,aACf,UAAU,CAAC,IAAM,QAAQ,EAAE,EAC3B,OAAO;IACV,UAAU,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACf,YAAY,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACjB,eAAe,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,kBAAkB;QAAE,QAAQ;IAAI;IACvD,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IACvD,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;AACzD;AAGO,MAAM,0BAA0B,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,6BAA6B;IAC1E,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,UAAU,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,aACf,UAAU,CAAC,IAAM,QAAQ,EAAE,EAC3B,OAAO;IACV,cAAc,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACnB,YAAY,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACjB,SAAS,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACd,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IACvD,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;AACzD;AAGO,MAAM,oBAAoB,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,sBAAsB;IAC7D,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,UAAU,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,aACf,UAAU,CAAC,IAAM,QAAQ,EAAE,EAC3B,OAAO;IACV,SAAS,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,YACd,UAAU,CAAC,IAAM,QAAQ,EAAE,EAC3B,OAAO;IACV,YAAY,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,eAAe,UAAU,GAAG,OAAO;AAC3D;AAGO,MAAM,qBAAqB,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,uBAAuB;IAC/D,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,WAAW,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,cAChB,UAAU,CAAC,IAAM,MAAM,EAAE,EACzB,OAAO;IACV,UAAU,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,aACf,UAAU,CAAC,IAAM,QAAQ,EAAE,EAC3B,OAAO;IACV,YAAY,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,eAAe,UAAU,GAAG,OAAO;IACzD,aAAa,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;IACvB,YAAY,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,eAAe;QAAE,WAAW;QAAG,OAAO;IAAE;IAC5D,sBAAsB,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,yBAAyB,OAAO,CAAC;AACjE;AAGO,MAAM,UAAU,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,WAAW;IACxC,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,MAAM,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;QAAE,QAAQ;IAAI,GAAG,OAAO;IAC9C,aAAa,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IAClB,UAAU,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,aACf,UAAU,CAAC,IAAM,QAAQ,EAAE,EAC3B,OAAO;IACV,YAAY,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,eAAe,OAAO;IAC1C,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;IACrB,SAAS,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;IACnB,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IACvD,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;AACzD;AAGO,MAAM,WAAW,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,YAAY;IAC1C,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,MAAM,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;QAAE,QAAQ;IAAI,GAAG,OAAO;IAC9C,SAAS,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACd,UAAU,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,aACf,UAAU,CAAC,IAAM,QAAQ,EAAE,EAC3B,OAAO;IACV,YAAY,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,eAAe,OAAO;IAC1C,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IACvD,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;AACzD;AAGO,MAAM,UAAU,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,WAAW;IACxC,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,MAAM,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;QAAE,QAAQ;IAAI,GAAG,OAAO;IAC9C,aAAa,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IAClB,WAAW,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,cAAc,UAAU,CAAC,IAAM,SAAS,EAAE;IAC7D,UAAU,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,aAAa,UAAU,CAAC,IAAM,QAAQ,EAAE;IAC1D,UAAU,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,aAAa,UAAU,CAAC,IAAM,QAAQ,EAAE;IAC1D,UAAU,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAAE,QAAQ;IAAG,GAAG,OAAO,GAAG,OAAO,CAAC;IACjE,cAAc,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,iBAAiB;QAAE,WAAW;QAAG,OAAO;IAAE,GAC7D,OAAO,GACP,OAAO,CAAC;IACX,WAAW,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE;IACnB,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;IACrB,SAAS,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;IACnB,UAAU,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,aAAa,OAAO,CAAC;IACvC,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IACvD,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;AACzD;AAGO,MAAM,YAAY,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,aAAa;IAC5C,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,QAAQ,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,WACb,UAAU,CAAC,IAAM,QAAQ,EAAE,EAC3B,OAAO;IACV,MAAM,iBAAiB,QAAQ,OAAO;IACtC,UAAU,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,YAAY,OAAO;IAClC,SAAS,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACd,aAAa,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IAClB,aAAa,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IAClB,QAAQ,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,UAAU;QAAE,WAAW;QAAG,OAAO;IAAE,GAAG,OAAO,GAAG,OAAO,CAAC;IACxE,YAAY,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,eAAe,OAAO;IAC1C,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IACvD,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;AACzD;AAGO,MAAM,eAAe,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,iBAAiB;IACnD,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,WAAW,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,cAChB,UAAU,CAAC,IAAM,MAAM,EAAE,EACzB,OAAO;IACV,QAAQ,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,WACb,UAAU,CAAC,IAAM,QAAQ,EAAE,EAC3B,OAAO;IACV,OAAO,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,SAAS;QAAE,WAAW;QAAG,OAAO;IAAE;IACjD,aAAa,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,gBAAgB;QAAE,WAAW;QAAG,OAAO;IAAE;IAC9D,QAAQ,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO,CAAC;IAClC,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IACvD,aAAa,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;IACvB,SAAS,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACd,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IACvD,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;AACzD;AAGO,MAAM,mBAAmB,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,qBAAqB;IAC3D,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,WAAW,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,cAChB,UAAU,CAAC,IAAM,MAAM,EAAE,EACzB,OAAO;IACV,SAAS,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,YACd,UAAU,CAAC,IAAM,QAAQ,EAAE,EAC3B,OAAO;IACV,YAAY,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,eAAe,UAAU,GAAG,OAAO;IACzD,QAAQ,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,UAAU;QAAE,QAAQ;IAAG,GAAG,OAAO,CAAC,UAAU,OAAO;IACnE,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IACvD,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;AACzD;AAGO,MAAM,kBAAkB,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,oBAAoB;IACzD,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,WAAW,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,cAChB,UAAU,CAAC,IAAM,MAAM,EAAE,EACzB,OAAO;IACV,UAAU,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,aACf,UAAU,CAAC,IAAM,QAAQ,EAAE,EAC3B,OAAO;IACV,UAAU,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,aAAa,UAAU,CAAC,IAAM,QAAQ,EAAE;IAC1D,WAAW,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,cAAc,UAAU,CAAC,IAAM,SAAS,EAAE;IAC7D,WAAW,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,aAAa,OAAO,CAAC;IACxC,aAAa,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;IACvB,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IACvD,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;AACzD;AAGO,MAAM,iBAAiB,CAAA,GAAA,6IAAA,CAAA,YAAS,AAAD,EAAE,OAAO,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAK,CAAC;QACjE,aAAa,IAAI,cAAc;YAC7B,QAAQ;gBAAC,MAAM,aAAa;aAAC;YAC7B,YAAY;gBAAC,aAAa,EAAE;aAAC;QAC/B;QACA,iBAAiB,KAAK;QACtB,iBAAiB,KAAK;QACtB,oBAAoB,KAAK;QACzB,kBAAkB,KAAK;QACvB,cAAc,KAAK;QACnB,UAAU,KAAK;IACjB,CAAC;AAEM,MAAM,4BAA4B,CAAA,GAAA,6IAAA,CAAA,YAAS,AAAD,EAAE,kBAAkB,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QACjF,QAAQ,IAAI,SAAS;YACnB,QAAQ;gBAAC,iBAAiB,QAAQ;aAAC;YACnC,YAAY;gBAAC,QAAQ,EAAE;aAAC;QAC1B;IACF,CAAC;AAEM,MAAM,2BAA2B,CAAA,GAAA,6IAAA,CAAA,YAAS,AAAD,EAAE,iBAAiB,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QAC/E,QAAQ,IAAI,SAAS;YACnB,QAAQ;gBAAC,gBAAgB,QAAQ;aAAC;YAClC,YAAY;gBAAC,QAAQ,EAAE;aAAC;QAC1B;IACF,CAAC;AAEM,MAAM,qCAAqC,CAAA,GAAA,6IAAA,CAAA,YAAS,AAAD,EAAE,2BAA2B,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QACnG,QAAQ,IAAI,SAAS;YACnB,QAAQ;gBAAC,0BAA0B,QAAQ;aAAC;YAC5C,YAAY;gBAAC,QAAQ,EAAE;aAAC;QAC1B;IACF,CAAC;AAEM,MAAM,yBAAyB,CAAA,GAAA,6IAAA,CAAA,YAAS,AAAD,EAAE,eAAe,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QAC3E,QAAQ,IAAI,SAAS;YACnB,QAAQ;gBAAC,cAAc,QAAQ;aAAC;YAChC,YAAY;gBAAC,QAAQ,EAAE;aAAC;QAC1B;IACF,CAAC;AAEM,MAAM,mCAAmC,CAAA,GAAA,6IAAA,CAAA,YAAS,AAAD,EAAE,yBAAyB,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QAC/F,QAAQ,IAAI,SAAS;YACnB,QAAQ;gBAAC,wBAAwB,QAAQ;aAAC;YAC1C,YAAY;gBAAC,QAAQ,EAAE;aAAC;QAC1B;IACF,CAAC;AAEM,MAAM,wBAAwB,CAAA,GAAA,6IAAA,CAAA,YAAS,AAAD,EAAE,cAAc,CAAC,EAAE,IAAI,EAAE,GAAK,CAAC;QAC1E,OAAO,KAAK;QACZ,SAAS,KAAK;QACd,SAAS,KAAK;IAChB,CAAC;AAEM,MAAM,mBAAmB,CAAA,GAAA,6IAAA,CAAA,YAAS,AAAD,EAAE,SAAS,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAK,CAAC;QACrE,aAAa,IAAI,cAAc;YAC7B,QAAQ;gBAAC,QAAQ,aAAa;aAAC;YAC/B,YAAY;gBAAC,aAAa,EAAE;aAAC;QAC/B;QACA,SAAS,IAAI,OAAO;YAClB,QAAQ;gBAAC,QAAQ,SAAS;aAAC;YAC3B,YAAY;gBAAC,MAAM,EAAE;aAAC;QACxB;QACA,mBAAmB,KAAK;QACxB,kBAAkB,KAAK;IACzB,CAAC;AAEM,MAAM,mBAAmB,CAAA,GAAA,6IAAA,CAAA,YAAS,AAAD,EAAE,SAAS,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAK,CAAC;QACrE,SAAS,IAAI,OAAO;YAClB,QAAQ;gBAAC,QAAQ,SAAS;aAAC;YAC3B,YAAY;gBAAC,MAAM,EAAE;aAAC;QACxB;QACA,aAAa,IAAI,cAAc;YAC7B,QAAQ;gBAAC,QAAQ,aAAa;aAAC;YAC/B,YAAY;gBAAC,aAAa,EAAE;aAAC;QAC/B;QACA,SAAS,KAAK;QACd,mBAAmB,KAAK;QACxB,oBAAoB,KAAK;QACzB,SAAS,KAAK;QACd,UAAU,KAAK;QACf,YAAY,KAAK;QACjB,WAAW,KAAK;QAChB,qBAAqB,KAAK;QAC1B,SAAS,KAAK;QACd,mBAAmB,KAAK;IAC1B,CAAC;AAEM,MAAM,mBAAmB,CAAA,GAAA,6IAAA,CAAA,YAAS,AAAD,EAAE,SAAS,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAK,CAAC;QACrE,QAAQ,IAAI,SAAS;YACnB,QAAQ;gBAAC,QAAQ,QAAQ;aAAC;YAC1B,YAAY;gBAAC,QAAQ,EAAE;aAAC;QAC1B;QACA,UAAU,KAAK;QACf,SAAS,KAAK;QACd,UAAU,KAAK;IACjB,CAAC;AAEM,MAAM,oBAAoB,CAAA,GAAA,6IAAA,CAAA,YAAS,AAAD,EAAE,UAAU,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAK,CAAC;QACvE,QAAQ,IAAI,SAAS;YACnB,QAAQ;gBAAC,SAAS,QAAQ;aAAC;YAC3B,YAAY;gBAAC,QAAQ,EAAE;aAAC;QAC1B;QACA,SAAS,KAAK;QACd,UAAU,KAAK;IACjB,CAAC;AAEM,MAAM,mBAAmB,CAAA,GAAA,6IAAA,CAAA,YAAS,AAAD,EAAE,SAAS,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAK,CAAC;QACrE,SAAS,IAAI,UAAU;YACrB,QAAQ;gBAAC,QAAQ,SAAS;aAAC;YAC3B,YAAY;gBAAC,SAAS,EAAE;aAAC;QAC3B;QACA,QAAQ,IAAI,SAAS;YACnB,QAAQ;gBAAC,QAAQ,QAAQ;aAAC;YAC1B,YAAY;gBAAC,QAAQ,EAAE;aAAC;QAC1B;QACA,QAAQ,IAAI,SAAS;YACnB,QAAQ;gBAAC,QAAQ,QAAQ;aAAC;YAC1B,YAAY;gBAAC,QAAQ,EAAE;aAAC;QAC1B;QACA,WAAW,KAAK;QAChB,UAAU,KAAK;IACjB,CAAC;AAEM,MAAM,qBAAqB,CAAA,GAAA,6IAAA,CAAA,YAAS,AAAD,EAAE,WAAW,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QACnE,MAAM,IAAI,SAAS;YACjB,QAAQ;gBAAC,UAAU,MAAM;aAAC;YAC1B,YAAY;gBAAC,QAAQ,EAAE;aAAC;QAC1B;IACF,CAAC;AAEM,MAAM,wBAAwB,CAAA,GAAA,6IAAA,CAAA,YAAS,AAAD,EAAE,cAAc,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QACzE,SAAS,IAAI,OAAO;YAClB,QAAQ;gBAAC,aAAa,SAAS;aAAC;YAChC,YAAY;gBAAC,MAAM,EAAE;aAAC;QACxB;QACA,MAAM,IAAI,SAAS;YACjB,QAAQ;gBAAC,aAAa,MAAM;aAAC;YAC7B,YAAY;gBAAC,QAAQ,EAAE;aAAC;QAC1B;IACF,CAAC;AAEM,MAAM,8BAA8B,CAAA,GAAA,6IAAA,CAAA,YAAS,AAAD,EACjD,oBACA,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QACZ,SAAS,IAAI,OAAO;YAClB,QAAQ;gBAAC,mBAAmB,SAAS;aAAC;YACtC,YAAY;gBAAC,MAAM,EAAE;aAAC;QACxB;QACA,QAAQ,IAAI,SAAS;YACnB,QAAQ;gBAAC,mBAAmB,QAAQ;aAAC;YACrC,YAAY;gBAAC,QAAQ,EAAE;aAAC;QAC1B;IACF,CAAC;AAGI,MAAM,6BAA6B,CAAA,GAAA,6IAAA,CAAA,YAAS,AAAD,EAChD,mBACA,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QACZ,QAAQ,IAAI,SAAS;YACnB,QAAQ;gBAAC,kBAAkB,QAAQ;aAAC;YACpC,YAAY;gBAAC,QAAQ,EAAE;aAAC;QAC1B;QACA,OAAO,IAAI,SAAS;YAClB,QAAQ;gBAAC,kBAAkB,OAAO;aAAC;YACnC,YAAY;gBAAC,QAAQ,EAAE;aAAC;QAC1B;IACF,CAAC;AAGI,MAAM,4BAA4B,CAAA,GAAA,6IAAA,CAAA,YAAS,AAAD,EAC/C,kBACA,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QACZ,SAAS,IAAI,OAAO;YAClB,QAAQ;gBAAC,iBAAiB,SAAS;aAAC;YACpC,YAAY;gBAAC,MAAM,EAAE;aAAC;QACxB;QACA,OAAO,IAAI,SAAS;YAClB,QAAQ;gBAAC,iBAAiB,OAAO;aAAC;YAClC,YAAY;gBAAC,QAAQ,EAAE;aAAC;QAC1B;IACF,CAAC;AAGI,MAAM,2BAA2B,CAAA,GAAA,6IAAA,CAAA,YAAS,AAAD,EAC9C,iBACA,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QACZ,SAAS,IAAI,OAAO;YAClB,QAAQ;gBAAC,gBAAgB,SAAS;aAAC;YACnC,YAAY;gBAAC,MAAM,EAAE;aAAC;QACxB;QACA,QAAQ,IAAI,SAAS;YACnB,QAAQ;gBAAC,gBAAgB,QAAQ;aAAC;YAClC,YAAY;gBAAC,QAAQ,EAAE;aAAC;QAC1B;QACA,QAAQ,IAAI,SAAS;YACnB,QAAQ;gBAAC,gBAAgB,QAAQ;aAAC;YAClC,YAAY;gBAAC,QAAQ,EAAE;aAAC;QAC1B;QACA,SAAS,IAAI,UAAU;YACrB,QAAQ;gBAAC,gBAAgB,SAAS;aAAC;YACnC,YAAY;gBAAC,SAAS,EAAE;aAAC;QAC3B;IACF,CAAC", "debugId": null}}, {"offset": {"line": 710, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/lib/db/index.ts"], "sourcesContent": ["import { neon } from '@neondatabase/serverless';\r\nimport { drizzle } from 'drizzle-orm/neon-http';\r\nimport * as schema from './schema';\r\n\r\nif (!process.env.DATABASE_URL) {\r\n  throw new Error('DATABASE_URL environment variable is required');\r\n}\r\n\r\nexport const sql = neon(process.env.DATABASE_URL);\r\nexport const db = drizzle(sql, { schema });\r\n\r\nexport * from './schema';\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEA,IAAI,CAAC,QAAQ,GAAG,CAAC,YAAY,EAAE;IAC7B,MAAM,IAAI,MAAM;AAClB;AAEO,MAAM,MAAM,CAAA,GAAA,wJAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,GAAG,CAAC,YAAY;AACzC,MAAM,KAAK,CAAA,GAAA,0JAAA,CAAA,UAAO,AAAD,EAAE,KAAK;IAAE,QAAA;AAAO", "debugId": null}}, {"offset": {"line": 744, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/app/api/courses/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\r\nimport { db } from '@/lib/db';\r\nimport {\r\n  courses,\r\n  users,\r\n  modules,\r\n  chapters,\r\n  quizzes,\r\n  questions,\r\n  courseAdmissions,\r\n  courseAcademics,\r\n  courseTuitionAndFinancing,\r\n  courseCareers,\r\n  courseStudentExperience\r\n} from '@/lib/db/schema';\r\nimport { eq, and } from 'drizzle-orm';\r\n\r\n// Generate a random course code\r\nfunction generateCourseCode(): string {\r\n  const prefix = 'COURSE';\r\n  const randomNum = Math.floor(Math.random() * 10000).toString().padStart(4, '0');\r\n  return `${prefix}${randomNum}`;\r\n}\r\n\r\n// GET /api/courses - Get courses (for teachers or public access for students)\r\nexport async function GET(request: NextRequest) {\r\n  try {\r\n    const searchParams = request.nextUrl.searchParams;\r\n    const teacherId = searchParams.get('teacherId');\r\n    const institutionId = searchParams.get('institutionId');\r\n    const publicAccess = searchParams.get('public'); // For student access to all published courses\r\n\r\n    // If public access is requested, return all published courses\r\n    if (publicAccess === 'true') {\r\n      // Get all published courses with teacher information\r\n      const publicCourses = await db\r\n        .select({\r\n          id: courses.id,\r\n          name: courses.name,\r\n          description: courses.description,\r\n          instructor: courses.instructor,\r\n          type: courses.type,\r\n          enrollmentType: courses.enrollmentType,\r\n          startDate: courses.startDate,\r\n          endDate: courses.endDate,\r\n          teacherId: courses.teacherId,\r\n          institutionId: courses.institutionId,\r\n          courseCode: courses.courseCode,\r\n          coverPicture: courses.coverPicture,\r\n          isPurchasable: courses.isPurchasable,\r\n          price: courses.price,\r\n          currency: courses.currency,\r\n          previewMode: courses.previewMode,\r\n          createdAt: courses.createdAt,\r\n          updatedAt: courses.updatedAt,\r\n          teacherName: users.name,\r\n          teacherEmail: users.email\r\n        })\r\n        .from(courses)\r\n        .leftJoin(users, eq(courses.teacherId, users.id));\r\n\r\n      // Get module count and additional data for each course\r\n      const coursesWithCounts = await Promise.all(\r\n        publicCourses.map(async (course) => {\r\n          // Get modules for this course\r\n          const courseModules = await db\r\n            .select()\r\n            .from(modules)\r\n            .where(eq(modules.courseId, course.id));\r\n\r\n          // Get chapters for each module\r\n          const modulesWithChapters = await Promise.all(\r\n            courseModules.map(async (module) => {\r\n              const moduleChapters = await db\r\n                .select()\r\n                .from(chapters)\r\n                .where(eq(chapters.moduleId, module.id));\r\n\r\n              // Get chapters with their quizzes and contents\r\n              const chaptersWithQuizzes = await Promise.all(\r\n                moduleChapters.map(async (chapter) => {\r\n                  // Get chapter quiz\r\n                  const chapterQuizzes = await db\r\n                    .select()\r\n                    .from(quizzes)\r\n                    .where(eq(quizzes.chapterId, chapter.id));\r\n\r\n                  // Get quiz questions if quiz exists\r\n                  let quizQuestions: any[] = [];\r\n                  if (chapterQuizzes.length > 0) {\r\n                    quizQuestions = await db\r\n                      .select()\r\n                      .from(questions)\r\n                      .where(eq(questions.quizId, chapterQuizzes[0].id));\r\n                  }\r\n\r\n                  // Transform chapter content to match expected format\r\n                  const contents = Array.isArray(chapter.content) ? chapter.content.map((content: any, index: number) => ({\r\n                    id: `content-${chapter.id}-${index}`,\r\n                    title: content.title || `Content ${index + 1}`,\r\n                    type: content.type || 'text',\r\n                    content: content.content || content,\r\n                    isCompleted: false,\r\n                    createdAt: new Date().toISOString()\r\n                  })) : [];\r\n\r\n                  return {\r\n                    id: chapter.id,\r\n                    title: chapter.name,\r\n                    order: chapter.orderIndex,\r\n                    isUnlocked: true,\r\n                    completionPercentage: 0,\r\n                    contents: contents,\r\n                    quiz: chapterQuizzes.length > 0 ? {\r\n                      id: chapterQuizzes[0].id.toString(),\r\n                      title: chapterQuizzes[0].name,\r\n                      type: 'chapter' as const,\r\n                      questions: quizQuestions.map(q => {\r\n                        // Extract correct answer based on question type\r\n                        let correctAnswer;\r\n                        if (q.type === 'true_false') {\r\n                          // For true/false questions, find the correct answer from options\r\n                          const correctOption = q.options?.find((opt: any) => opt.isCorrect);\r\n                          if (correctOption) {\r\n                            // Extract the text value from the content array\r\n                            const textContent = correctOption.content?.find((c: any) => c.type === 'text');\r\n                            correctAnswer = textContent?.value?.toLowerCase() === 'true' ? 'true' : 'false';\r\n                          }\r\n                        } else if (q.type === 'multiple_choice') {\r\n                          // For multiple choice, find the index of the correct option\r\n                          const correctIndex = q.options?.findIndex((opt: any) => opt.isCorrect);\r\n                          correctAnswer = correctIndex !== -1 ? correctIndex : undefined;\r\n                        } else if (q.type === 'essay') {\r\n                          // For essay questions, use essayAnswer\r\n                          correctAnswer = q.essayAnswer;\r\n                        }\r\n\r\n                        return {\r\n                          id: q.id.toString(),\r\n                          question: q.question,\r\n                          type: q.type,\r\n                          options: q.options || [],\r\n                          correctAnswer: correctAnswer,\r\n                          explanation: q.explanation\r\n                        };\r\n                      }),\r\n                      minimumScore: parseFloat(chapterQuizzes[0].minimumScore || '70'),\r\n                      timeLimit: chapterQuizzes[0].timeLimit,\r\n                      attempts: 0,\r\n                      maxAttempts: 3,\r\n                      isPassed: false\r\n                    } : {\r\n                      id: `quiz-chapter-${chapter.id}`,\r\n                      title: `Quiz ${chapter.name}`,\r\n                      type: 'chapter' as const,\r\n                      questions: [],\r\n                      minimumScore: 70,\r\n                      attempts: 0,\r\n                      maxAttempts: 3,\r\n                      isPassed: false\r\n                    }\r\n                  };\r\n                })\r\n              );\r\n\r\n              // Get module quiz\r\n              const moduleQuizzes = await db\r\n                .select()\r\n                .from(quizzes)\r\n                .where(eq(quizzes.moduleId, module.id));\r\n\r\n              // Get module quiz questions if quiz exists\r\n              let moduleQuizQuestions: any[] = [];\r\n              if (moduleQuizzes.length > 0) {\r\n                moduleQuizQuestions = await db\r\n                  .select()\r\n                  .from(questions)\r\n                  .where(eq(questions.quizId, moduleQuizzes[0].id));\r\n              }\r\n\r\n              return {\r\n                id: module.id,\r\n                title: module.name,\r\n                description: module.description || '',\r\n                order: module.orderIndex,\r\n                isUnlocked: true, // For public view, assume all modules are visible\r\n                completionPercentage: 0,\r\n                chapters: chaptersWithQuizzes,\r\n                moduleQuiz: moduleQuizzes.length > 0 ? {\r\n                  id: moduleQuizzes[0].id.toString(),\r\n                  title: moduleQuizzes[0].name,\r\n                  type: 'module' as const,\r\n                  questions: moduleQuizQuestions.map(q => {\r\n                    // Extract correct answer based on question type\r\n                    let correctAnswer;\r\n                    if (q.type === 'true_false') {\r\n                      // For true/false questions, find the correct answer from options\r\n                      const correctOption = q.options?.find((opt: any) => opt.isCorrect);\r\n                      if (correctOption) {\r\n                        // Extract the text value from the content array\r\n                        const textContent = correctOption.content?.find((c: any) => c.type === 'text');\r\n                        correctAnswer = textContent?.value?.toLowerCase() === 'true' ? 'true' : 'false';\r\n                      }\r\n                    } else if (q.type === 'multiple_choice') {\r\n                      // For multiple choice, find the index of the correct option\r\n                      const correctIndex = q.options?.findIndex((opt: any) => opt.isCorrect);\r\n                      correctAnswer = correctIndex !== -1 ? correctIndex : undefined;\r\n                    } else if (q.type === 'essay') {\r\n                      // For essay questions, use essayAnswer\r\n                      correctAnswer = q.essayAnswer;\r\n                    }\r\n\r\n                    return {\r\n                      id: q.id.toString(),\r\n                      question: q.question,\r\n                      type: q.type,\r\n                      options: q.options || [],\r\n                      correctAnswer: correctAnswer,\r\n                      explanation: q.explanation\r\n                    };\r\n                  }),\r\n                  minimumScore: parseFloat(moduleQuizzes[0].minimumScore || '70'),\r\n                  timeLimit: moduleQuizzes[0].timeLimit,\r\n                  attempts: 0,\r\n                  maxAttempts: 3,\r\n                  isPassed: false\r\n                } : {\r\n                  id: `quiz-module-${module.id}`,\r\n                  title: `Quiz ${module.name}`,\r\n                  type: 'module' as const,\r\n                  questions: [],\r\n                  minimumScore: 70,\r\n                  attempts: 0,\r\n                  maxAttempts: 3,\r\n                  isPassed: false\r\n                }\r\n              };\r\n            })\r\n          );\r\n\r\n          // Get admissions data\r\n          const admissionsData = await db\r\n            .select()\r\n            .from(courseAdmissions)\r\n            .where(eq(courseAdmissions.courseId, course.id))\r\n            .limit(1);\r\n\r\n          // Get academics data\r\n          const academicsData = await db\r\n            .select()\r\n            .from(courseAcademics)\r\n            .where(eq(courseAcademics.courseId, course.id))\r\n            .limit(1);\r\n\r\n          // Get tuition and financing data\r\n          const tuitionData = await db\r\n            .select()\r\n            .from(courseTuitionAndFinancing)\r\n            .where(eq(courseTuitionAndFinancing.courseId, course.id))\r\n            .limit(1);\r\n\r\n          // Get careers data\r\n          const careersData = await db\r\n            .select()\r\n            .from(courseCareers)\r\n            .where(eq(courseCareers.courseId, course.id))\r\n            .limit(1);\r\n\r\n          // Get student experience data\r\n          const studentExperienceData = await db\r\n            .select()\r\n            .from(courseStudentExperience)\r\n            .where(eq(courseStudentExperience.courseId, course.id))\r\n            .limit(1);\r\n\r\n          // Get final exam data\r\n          const finalExamData = await db\r\n            .select()\r\n            .from(quizzes)\r\n            .where(\r\n              and(\r\n                eq(quizzes.courseId, course.id),\r\n                eq(quizzes.quizType, 'final')\r\n              )\r\n            )\r\n            .limit(1);\r\n\r\n          // Get final exam questions if final exam exists\r\n          let finalExamQuestions: any[] = [];\r\n          if (finalExamData.length > 0) {\r\n            finalExamQuestions = await db\r\n              .select()\r\n              .from(questions)\r\n              .where(eq(questions.quizId, finalExamData[0].id));\r\n          }\r\n\r\n          return {\r\n            id: course.id.toString(),\r\n            name: course.name,\r\n            code: course.courseCode || '',\r\n            description: course.description || '',\r\n            instructor: course.instructor || course.teacherName || 'Unknown Instructor',\r\n            startDate: course.startDate?.toISOString() || new Date().toISOString(),\r\n            endDate: course.endDate?.toISOString() || new Date().toISOString(),\r\n            enrollmentType: course.enrollmentType || 'code' as const,\r\n            enrollmentCode: course.courseCode,\r\n            isPurchasable: course.isPurchasable !== undefined ? course.isPurchasable : true,\r\n            price: course.price ? parseFloat(course.price) : 0,\r\n            currency: course.currency || 'IDR',\r\n            previewMode: course.previewMode || false,\r\n            thumbnail: course.coverPicture || undefined,\r\n            admissions: admissionsData[0] ? {\r\n              requirements: admissionsData[0].requirements as string[] || [],\r\n              applicationDeadline: admissionsData[0].applicationDeadline || undefined,\r\n              prerequisites: admissionsData[0].prerequisites as string[] || []\r\n            } : undefined,\r\n            academics: academicsData[0] ? {\r\n              credits: academicsData[0].credits || 0,\r\n              workload: academicsData[0].workload || '',\r\n              assessment: academicsData[0].assessment as string[] || []\r\n            } : undefined,\r\n            tuitionAndFinancing: tuitionData[0] ? {\r\n              totalCost: tuitionData[0].totalCost ? parseFloat(tuitionData[0].totalCost) : 0,\r\n              paymentOptions: tuitionData[0].paymentOptions as string[] || [],\r\n              scholarships: tuitionData[0].scholarships as string[] || []\r\n            } : undefined,\r\n            careers: careersData[0] ? {\r\n              outcomes: careersData[0].outcomes as string[] || [],\r\n              industries: careersData[0].industries as string[] || [],\r\n              averageSalary: careersData[0].averageSalary || undefined\r\n            } : undefined,\r\n            studentExperience: studentExperienceData[0] ? {\r\n              testimonials: studentExperienceData[0].testimonials as { name: string; feedback: string }[] || [],\r\n              facilities: studentExperienceData[0].facilities as string[] || [],\r\n              support: studentExperienceData[0].support as string[] || []\r\n            } : undefined,\r\n            modules: modulesWithChapters, // Now populated with actual modules and chapters\r\n            finalExam: finalExamData.length > 0 ? {\r\n              id: finalExamData[0].id.toString(),\r\n              title: finalExamData[0].name,\r\n              type: 'final' as const,\r\n              questions: finalExamQuestions.map(q => {\r\n                // Extract correct answer based on question type\r\n                let correctAnswer;\r\n                if (q.type === 'true_false') {\r\n                  // For true/false questions, find the correct answer from options\r\n                  const correctOption = q.options?.find((opt: any) => opt.isCorrect);\r\n                  if (correctOption) {\r\n                    // Extract the text value from the content array\r\n                    const textContent = correctOption.content?.find((c: any) => c.type === 'text');\r\n                    correctAnswer = textContent?.value?.toLowerCase() === 'true' ? 'true' : 'false';\r\n                  }\r\n                } else if (q.type === 'multiple_choice') {\r\n                  // For multiple choice, find the index of the correct option\r\n                  const correctIndex = q.options?.findIndex((opt: any) => opt.isCorrect);\r\n                  correctAnswer = correctIndex !== -1 ? correctIndex : undefined;\r\n                } else if (q.type === 'essay') {\r\n                  // For essay questions, use essayAnswer\r\n                  correctAnswer = q.essayAnswer;\r\n                }\r\n\r\n                return {\r\n                  id: q.id.toString(),\r\n                  question: q.question,\r\n                  type: q.type,\r\n                  options: q.options || [],\r\n                  correctAnswer: correctAnswer,\r\n                  explanation: q.explanation\r\n                };\r\n              }),\r\n              minimumScore: parseFloat(finalExamData[0].minimumScore || '70'),\r\n              timeLimit: finalExamData[0].timeLimit,\r\n              attempts: 0,\r\n              maxAttempts: 3,\r\n              isPassed: false\r\n            } : {\r\n              id: 'final-exam',\r\n              title: 'Final Exam',\r\n              type: 'final' as const,\r\n              questions: [],\r\n              minimumScore: 70,\r\n              attempts: 0,\r\n              maxAttempts: 3,\r\n              isPassed: false\r\n            },\r\n            certificate: {\r\n              isEligible: true, // nanti diubah, for now asssume all courses are certifiable\r\n              isGenerated: false\r\n            },\r\n            minPassingScore: 70,\r\n            totalProgress: 0,\r\n            status: 'not-started' as const,\r\n            // Additional fields for compatibility\r\n            moduleCount: courseModules.length,\r\n            studentCount: 0\r\n          };\r\n        })\r\n      );\r\n\r\n      return NextResponse.json({ success: true, courses: coursesWithCounts });\r\n    }\r\n\r\n    // Original teacher-specific logic\r\n    if (!teacherId) {\r\n      return NextResponse.json({ error: 'Teacher ID required' }, { status: 400 });\r\n    }\r\n\r\n    let whereCondition;\r\n    if (institutionId) {\r\n      // Get all courses for the institution (for super admin)\r\n      whereCondition = eq(courses.institutionId, parseInt(institutionId));\r\n    } else {\r\n      // Get courses for the specific teacher\r\n      whereCondition = eq(courses.teacherId, parseInt(teacherId));\r\n    }\r\n\r\n    // Get courses with teacher information\r\n    const teacherCourses = await db\r\n      .select({\r\n        id: courses.id,\r\n        name: courses.name,\r\n        description: courses.description,\r\n        instructor: courses.instructor,\r\n        type: courses.type,\r\n        enrollmentType: courses.enrollmentType,\r\n        startDate: courses.startDate,\r\n        endDate: courses.endDate,\r\n        teacherId: courses.teacherId,\r\n        institutionId: courses.institutionId,\r\n        courseCode: courses.courseCode,\r\n        coverPicture: courses.coverPicture,\r\n        isPurchasable: courses.isPurchasable,\r\n        price: courses.price,\r\n        currency: courses.currency,\r\n        previewMode: courses.previewMode,\r\n        createdAt: courses.createdAt,\r\n        updatedAt: courses.updatedAt,\r\n        teacherName: users.name,\r\n        teacherEmail: users.email\r\n      })\r\n      .from(courses)\r\n      .leftJoin(users, eq(courses.teacherId, users.id))\r\n      .where(whereCondition);\r\n\r\n    // Get module count for each course\r\n    const coursesWithCounts = await Promise.all(\r\n      teacherCourses.map(async (course) => {\r\n        const moduleCount = await db\r\n          .select({ count: modules.id })\r\n          .from(modules)\r\n          .where(eq(modules.courseId, course.id));\r\n\r\n        return {\r\n          ...course,\r\n          moduleCount: moduleCount.length,\r\n          studentCount: 0, // TODO: Calculate actual student count from enrollments\r\n          status: 'published' // TODO: Add status field to schema or calculate based on dates\r\n        };\r\n      })\r\n    );\r\n\r\n    return NextResponse.json({ success: true, courses: coursesWithCounts });\r\n  } catch (error) {\r\n    console.error('Error fetching courses:', error);\r\n    return NextResponse.json(\r\n      { error: 'Internal server error' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n\r\n// POST /api/courses - Create a new course\r\nexport async function POST(request: NextRequest) {\r\n  try {\r\n    const body = await request.json();\r\n    const {\r\n      name,\r\n      description,\r\n      instructor,\r\n      type = 'self_paced',\r\n      enrollmentType = 'code',\r\n      startDate,\r\n      endDate,\r\n      teacherId,\r\n      institutionId,\r\n      courseCode,\r\n      coverPicture,\r\n      isPurchasable,\r\n      price,\r\n      currency,\r\n      previewMode,\r\n      admissions,\r\n      academics,\r\n      tuitionAndFinancing,\r\n      careers,\r\n      studentExperience\r\n    } = body;\r\n\r\n    // Validate required fields\r\n    if (!name || !teacherId || !institutionId) {\r\n      return NextResponse.json(\r\n        { error: 'Name, teacher ID, and institution ID are required' },\r\n        { status: 400 }\r\n      );\r\n    }\r\n\r\n    // Verify teacher exists and belongs to the institution\r\n    const teacher = await db\r\n      .select()\r\n      .from(users)\r\n      .where(\r\n        and(\r\n          eq(users.id, teacherId),\r\n          eq(users.institutionId, institutionId),\r\n          eq(users.role, 'teacher')\r\n        )\r\n      )\r\n      .limit(1);\r\n\r\n    if (teacher.length === 0) {\r\n      return NextResponse.json(\r\n        { error: 'Teacher not found or not authorized' },\r\n        { status: 403 }\r\n      );\r\n    }\r\n\r\n    // Generate course code if not provided\r\n    let finalCourseCode = courseCode;\r\n    if (!finalCourseCode) {\r\n      let isUnique = false;\r\n      while (!isUnique) {\r\n        finalCourseCode = generateCourseCode();\r\n        const existing = await db\r\n          .select()\r\n          .from(courses)\r\n          .where(eq(courses.courseCode, finalCourseCode))\r\n          .limit(1);\r\n        isUnique = existing.length === 0;\r\n      }\r\n    } else {\r\n      // Check if provided course code is unique\r\n      const existing = await db\r\n        .select()\r\n        .from(courses)\r\n        .where(eq(courses.courseCode, finalCourseCode))\r\n        .limit(1);\r\n      \r\n      if (existing.length > 0) {\r\n        return NextResponse.json(\r\n          { error: 'Course code already exists' },\r\n          { status: 400 }\r\n        );\r\n      }\r\n    }\r\n\r\n    // Create the course\r\n    const courseInsertResult = await db\r\n      .insert(courses)\r\n      .values({\r\n        name,\r\n        description,\r\n        instructor,\r\n        type,\r\n        enrollmentType,\r\n        startDate: startDate ? new Date(startDate) : null,\r\n        endDate: endDate ? new Date(endDate) : null,\r\n        teacherId,\r\n        institutionId,\r\n        courseCode: finalCourseCode,\r\n        coverPicture,\r\n        isPurchasable,\r\n        price,\r\n        currency,\r\n        previewMode\r\n      })\r\n      .returning();\r\n\r\n    const newCourse = courseInsertResult[0];\r\n\r\n    // Insert related course details if provided\r\n    if (admissions) {\r\n      await db.insert(courseAdmissions).values({\r\n        courseId: newCourse.id,\r\n        requirements: admissions.requirements,\r\n        applicationDeadline: admissions.applicationDeadline,\r\n        prerequisites: admissions.prerequisites,\r\n      });\r\n    }\r\n    if (academics) {\r\n      await db.insert(courseAcademics).values({\r\n        courseId: newCourse.id,\r\n        credits: academics.credits,\r\n        workload: academics.workload,\r\n        assessment: academics.assessment,\r\n      });\r\n    }\r\n    if (tuitionAndFinancing) {\r\n      await db.insert(courseTuitionAndFinancing).values({\r\n        courseId: newCourse.id,\r\n        totalCost: tuitionAndFinancing.totalCost,\r\n        paymentOptions: tuitionAndFinancing.paymentOptions,\r\n        scholarships: tuitionAndFinancing.scholarships,\r\n      });\r\n    }\r\n    if (careers) {\r\n      await db.insert(courseCareers).values({\r\n        courseId: newCourse.id,\r\n        outcomes: careers.outcomes,\r\n        industries: careers.industries,\r\n        averageSalary: careers.averageSalary,\r\n      });\r\n    }\r\n    if (studentExperience) {\r\n      await db.insert(courseStudentExperience).values({\r\n        courseId: newCourse.id,\r\n        testimonials: studentExperience.testimonials,\r\n        facilities: studentExperience.facilities,\r\n        support: studentExperience.support,\r\n      });\r\n    }\r\n\r\n    return NextResponse.json(\r\n      { success: true, course: newCourse, message: 'Course created successfully' },\r\n      { status: 201 }\r\n    );\r\n  } catch (error) {\r\n    console.error('Error creating course:', error);\r\n    return NextResponse.json(\r\n      { error: 'Internal server error' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;AAaA;;;;;AAEA,gCAAgC;AAChC,SAAS;IACP,MAAM,SAAS;IACf,MAAM,YAAY,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,QAAQ,GAAG,QAAQ,CAAC,GAAG;IAC3E,OAAO,GAAG,SAAS,WAAW;AAChC;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,eAAe,QAAQ,OAAO,CAAC,YAAY;QACjD,MAAM,YAAY,aAAa,GAAG,CAAC;QACnC,MAAM,gBAAgB,aAAa,GAAG,CAAC;QACvC,MAAM,eAAe,aAAa,GAAG,CAAC,WAAW,8CAA8C;QAE/F,8DAA8D;QAC9D,IAAI,iBAAiB,QAAQ;YAC3B,qDAAqD;YACrD,MAAM,gBAAgB,MAAM,2IAAA,CAAA,KAAE,CAC3B,MAAM,CAAC;gBACN,IAAI,4HAAA,CAAA,UAAO,CAAC,EAAE;gBACd,MAAM,4HAAA,CAAA,UAAO,CAAC,IAAI;gBAClB,aAAa,4HAAA,CAAA,UAAO,CAAC,WAAW;gBAChC,YAAY,4HAAA,CAAA,UAAO,CAAC,UAAU;gBAC9B,MAAM,4HAAA,CAAA,UAAO,CAAC,IAAI;gBAClB,gBAAgB,4HAAA,CAAA,UAAO,CAAC,cAAc;gBACtC,WAAW,4HAAA,CAAA,UAAO,CAAC,SAAS;gBAC5B,SAAS,4HAAA,CAAA,UAAO,CAAC,OAAO;gBACxB,WAAW,4HAAA,CAAA,UAAO,CAAC,SAAS;gBAC5B,eAAe,4HAAA,CAAA,UAAO,CAAC,aAAa;gBACpC,YAAY,4HAAA,CAAA,UAAO,CAAC,UAAU;gBAC9B,cAAc,4HAAA,CAAA,UAAO,CAAC,YAAY;gBAClC,eAAe,4HAAA,CAAA,UAAO,CAAC,aAAa;gBACpC,OAAO,4HAAA,CAAA,UAAO,CAAC,KAAK;gBACpB,UAAU,4HAAA,CAAA,UAAO,CAAC,QAAQ;gBAC1B,aAAa,4HAAA,CAAA,UAAO,CAAC,WAAW;gBAChC,WAAW,4HAAA,CAAA,UAAO,CAAC,SAAS;gBAC5B,WAAW,4HAAA,CAAA,UAAO,CAAC,SAAS;gBAC5B,aAAa,4HAAA,CAAA,QAAK,CAAC,IAAI;gBACvB,cAAc,4HAAA,CAAA,QAAK,CAAC,KAAK;YAC3B,GACC,IAAI,CAAC,4HAAA,CAAA,UAAO,EACZ,QAAQ,CAAC,4HAAA,CAAA,QAAK,EAAE,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,UAAO,CAAC,SAAS,EAAE,4HAAA,CAAA,QAAK,CAAC,EAAE;YAEjD,uDAAuD;YACvD,MAAM,oBAAoB,MAAM,QAAQ,GAAG,CACzC,cAAc,GAAG,CAAC,OAAO;gBACvB,8BAA8B;gBAC9B,MAAM,gBAAgB,MAAM,2IAAA,CAAA,KAAE,CAC3B,MAAM,GACN,IAAI,CAAC,4HAAA,CAAA,UAAO,EACZ,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,UAAO,CAAC,QAAQ,EAAE,OAAO,EAAE;gBAEvC,+BAA+B;gBAC/B,MAAM,sBAAsB,MAAM,QAAQ,GAAG,CAC3C,cAAc,GAAG,CAAC,OAAO;oBACvB,MAAM,iBAAiB,MAAM,2IAAA,CAAA,KAAE,CAC5B,MAAM,GACN,IAAI,CAAC,4HAAA,CAAA,WAAQ,EACb,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,WAAQ,CAAC,QAAQ,EAAE,OAAO,EAAE;oBAExC,+CAA+C;oBAC/C,MAAM,sBAAsB,MAAM,QAAQ,GAAG,CAC3C,eAAe,GAAG,CAAC,OAAO;wBACxB,mBAAmB;wBACnB,MAAM,iBAAiB,MAAM,2IAAA,CAAA,KAAE,CAC5B,MAAM,GACN,IAAI,CAAC,4HAAA,CAAA,UAAO,EACZ,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,UAAO,CAAC,SAAS,EAAE,QAAQ,EAAE;wBAEzC,oCAAoC;wBACpC,IAAI,gBAAuB,EAAE;wBAC7B,IAAI,eAAe,MAAM,GAAG,GAAG;4BAC7B,gBAAgB,MAAM,2IAAA,CAAA,KAAE,CACrB,MAAM,GACN,IAAI,CAAC,4HAAA,CAAA,YAAS,EACd,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,YAAS,CAAC,MAAM,EAAE,cAAc,CAAC,EAAE,CAAC,EAAE;wBACpD;wBAEA,qDAAqD;wBACrD,MAAM,WAAW,MAAM,OAAO,CAAC,QAAQ,OAAO,IAAI,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,SAAc,QAAkB,CAAC;gCACtG,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAC,EAAE,OAAO;gCACpC,OAAO,QAAQ,KAAK,IAAI,CAAC,QAAQ,EAAE,QAAQ,GAAG;gCAC9C,MAAM,QAAQ,IAAI,IAAI;gCACtB,SAAS,QAAQ,OAAO,IAAI;gCAC5B,aAAa;gCACb,WAAW,IAAI,OAAO,WAAW;4BACnC,CAAC,KAAK,EAAE;wBAER,OAAO;4BACL,IAAI,QAAQ,EAAE;4BACd,OAAO,QAAQ,IAAI;4BACnB,OAAO,QAAQ,UAAU;4BACzB,YAAY;4BACZ,sBAAsB;4BACtB,UAAU;4BACV,MAAM,eAAe,MAAM,GAAG,IAAI;gCAChC,IAAI,cAAc,CAAC,EAAE,CAAC,EAAE,CAAC,QAAQ;gCACjC,OAAO,cAAc,CAAC,EAAE,CAAC,IAAI;gCAC7B,MAAM;gCACN,WAAW,cAAc,GAAG,CAAC,CAAA;oCAC3B,gDAAgD;oCAChD,IAAI;oCACJ,IAAI,EAAE,IAAI,KAAK,cAAc;wCAC3B,iEAAiE;wCACjE,MAAM,gBAAgB,EAAE,OAAO,EAAE,KAAK,CAAC,MAAa,IAAI,SAAS;wCACjE,IAAI,eAAe;4CACjB,gDAAgD;4CAChD,MAAM,cAAc,cAAc,OAAO,EAAE,KAAK,CAAC,IAAW,EAAE,IAAI,KAAK;4CACvE,gBAAgB,aAAa,OAAO,kBAAkB,SAAS,SAAS;wCAC1E;oCACF,OAAO,IAAI,EAAE,IAAI,KAAK,mBAAmB;wCACvC,4DAA4D;wCAC5D,MAAM,eAAe,EAAE,OAAO,EAAE,UAAU,CAAC,MAAa,IAAI,SAAS;wCACrE,gBAAgB,iBAAiB,CAAC,IAAI,eAAe;oCACvD,OAAO,IAAI,EAAE,IAAI,KAAK,SAAS;wCAC7B,uCAAuC;wCACvC,gBAAgB,EAAE,WAAW;oCAC/B;oCAEA,OAAO;wCACL,IAAI,EAAE,EAAE,CAAC,QAAQ;wCACjB,UAAU,EAAE,QAAQ;wCACpB,MAAM,EAAE,IAAI;wCACZ,SAAS,EAAE,OAAO,IAAI,EAAE;wCACxB,eAAe;wCACf,aAAa,EAAE,WAAW;oCAC5B;gCACF;gCACA,cAAc,WAAW,cAAc,CAAC,EAAE,CAAC,YAAY,IAAI;gCAC3D,WAAW,cAAc,CAAC,EAAE,CAAC,SAAS;gCACtC,UAAU;gCACV,aAAa;gCACb,UAAU;4BACZ,IAAI;gCACF,IAAI,CAAC,aAAa,EAAE,QAAQ,EAAE,EAAE;gCAChC,OAAO,CAAC,KAAK,EAAE,QAAQ,IAAI,EAAE;gCAC7B,MAAM;gCACN,WAAW,EAAE;gCACb,cAAc;gCACd,UAAU;gCACV,aAAa;gCACb,UAAU;4BACZ;wBACF;oBACF;oBAGF,kBAAkB;oBAClB,MAAM,gBAAgB,MAAM,2IAAA,CAAA,KAAE,CAC3B,MAAM,GACN,IAAI,CAAC,4HAAA,CAAA,UAAO,EACZ,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,UAAO,CAAC,QAAQ,EAAE,OAAO,EAAE;oBAEvC,2CAA2C;oBAC3C,IAAI,sBAA6B,EAAE;oBACnC,IAAI,cAAc,MAAM,GAAG,GAAG;wBAC5B,sBAAsB,MAAM,2IAAA,CAAA,KAAE,CAC3B,MAAM,GACN,IAAI,CAAC,4HAAA,CAAA,YAAS,EACd,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,YAAS,CAAC,MAAM,EAAE,aAAa,CAAC,EAAE,CAAC,EAAE;oBACnD;oBAEA,OAAO;wBACL,IAAI,OAAO,EAAE;wBACb,OAAO,OAAO,IAAI;wBAClB,aAAa,OAAO,WAAW,IAAI;wBACnC,OAAO,OAAO,UAAU;wBACxB,YAAY;wBACZ,sBAAsB;wBACtB,UAAU;wBACV,YAAY,cAAc,MAAM,GAAG,IAAI;4BACrC,IAAI,aAAa,CAAC,EAAE,CAAC,EAAE,CAAC,QAAQ;4BAChC,OAAO,aAAa,CAAC,EAAE,CAAC,IAAI;4BAC5B,MAAM;4BACN,WAAW,oBAAoB,GAAG,CAAC,CAAA;gCACjC,gDAAgD;gCAChD,IAAI;gCACJ,IAAI,EAAE,IAAI,KAAK,cAAc;oCAC3B,iEAAiE;oCACjE,MAAM,gBAAgB,EAAE,OAAO,EAAE,KAAK,CAAC,MAAa,IAAI,SAAS;oCACjE,IAAI,eAAe;wCACjB,gDAAgD;wCAChD,MAAM,cAAc,cAAc,OAAO,EAAE,KAAK,CAAC,IAAW,EAAE,IAAI,KAAK;wCACvE,gBAAgB,aAAa,OAAO,kBAAkB,SAAS,SAAS;oCAC1E;gCACF,OAAO,IAAI,EAAE,IAAI,KAAK,mBAAmB;oCACvC,4DAA4D;oCAC5D,MAAM,eAAe,EAAE,OAAO,EAAE,UAAU,CAAC,MAAa,IAAI,SAAS;oCACrE,gBAAgB,iBAAiB,CAAC,IAAI,eAAe;gCACvD,OAAO,IAAI,EAAE,IAAI,KAAK,SAAS;oCAC7B,uCAAuC;oCACvC,gBAAgB,EAAE,WAAW;gCAC/B;gCAEA,OAAO;oCACL,IAAI,EAAE,EAAE,CAAC,QAAQ;oCACjB,UAAU,EAAE,QAAQ;oCACpB,MAAM,EAAE,IAAI;oCACZ,SAAS,EAAE,OAAO,IAAI,EAAE;oCACxB,eAAe;oCACf,aAAa,EAAE,WAAW;gCAC5B;4BACF;4BACA,cAAc,WAAW,aAAa,CAAC,EAAE,CAAC,YAAY,IAAI;4BAC1D,WAAW,aAAa,CAAC,EAAE,CAAC,SAAS;4BACrC,UAAU;4BACV,aAAa;4BACb,UAAU;wBACZ,IAAI;4BACF,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,EAAE;4BAC9B,OAAO,CAAC,KAAK,EAAE,OAAO,IAAI,EAAE;4BAC5B,MAAM;4BACN,WAAW,EAAE;4BACb,cAAc;4BACd,UAAU;4BACV,aAAa;4BACb,UAAU;wBACZ;oBACF;gBACF;gBAGF,sBAAsB;gBACtB,MAAM,iBAAiB,MAAM,2IAAA,CAAA,KAAE,CAC5B,MAAM,GACN,IAAI,CAAC,4HAAA,CAAA,mBAAgB,EACrB,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,mBAAgB,CAAC,QAAQ,EAAE,OAAO,EAAE,GAC7C,KAAK,CAAC;gBAET,qBAAqB;gBACrB,MAAM,gBAAgB,MAAM,2IAAA,CAAA,KAAE,CAC3B,MAAM,GACN,IAAI,CAAC,4HAAA,CAAA,kBAAe,EACpB,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,kBAAe,CAAC,QAAQ,EAAE,OAAO,EAAE,GAC5C,KAAK,CAAC;gBAET,iCAAiC;gBACjC,MAAM,cAAc,MAAM,2IAAA,CAAA,KAAE,CACzB,MAAM,GACN,IAAI,CAAC,4HAAA,CAAA,4BAAyB,EAC9B,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,4BAAyB,CAAC,QAAQ,EAAE,OAAO,EAAE,GACtD,KAAK,CAAC;gBAET,mBAAmB;gBACnB,MAAM,cAAc,MAAM,2IAAA,CAAA,KAAE,CACzB,MAAM,GACN,IAAI,CAAC,4HAAA,CAAA,gBAAa,EAClB,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,gBAAa,CAAC,QAAQ,EAAE,OAAO,EAAE,GAC1C,KAAK,CAAC;gBAET,8BAA8B;gBAC9B,MAAM,wBAAwB,MAAM,2IAAA,CAAA,KAAE,CACnC,MAAM,GACN,IAAI,CAAC,4HAAA,CAAA,0BAAuB,EAC5B,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,0BAAuB,CAAC,QAAQ,EAAE,OAAO,EAAE,GACpD,KAAK,CAAC;gBAET,sBAAsB;gBACtB,MAAM,gBAAgB,MAAM,2IAAA,CAAA,KAAE,CAC3B,MAAM,GACN,IAAI,CAAC,4HAAA,CAAA,UAAO,EACZ,KAAK,CACJ,CAAA,GAAA,oKAAA,CAAA,MAAG,AAAD,EACA,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,UAAO,CAAC,QAAQ,EAAE,OAAO,EAAE,GAC9B,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,UAAO,CAAC,QAAQ,EAAE,WAGxB,KAAK,CAAC;gBAET,gDAAgD;gBAChD,IAAI,qBAA4B,EAAE;gBAClC,IAAI,cAAc,MAAM,GAAG,GAAG;oBAC5B,qBAAqB,MAAM,2IAAA,CAAA,KAAE,CAC1B,MAAM,GACN,IAAI,CAAC,4HAAA,CAAA,YAAS,EACd,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,YAAS,CAAC,MAAM,EAAE,aAAa,CAAC,EAAE,CAAC,EAAE;gBACnD;gBAEA,OAAO;oBACL,IAAI,OAAO,EAAE,CAAC,QAAQ;oBACtB,MAAM,OAAO,IAAI;oBACjB,MAAM,OAAO,UAAU,IAAI;oBAC3B,aAAa,OAAO,WAAW,IAAI;oBACnC,YAAY,OAAO,UAAU,IAAI,OAAO,WAAW,IAAI;oBACvD,WAAW,OAAO,SAAS,EAAE,iBAAiB,IAAI,OAAO,WAAW;oBACpE,SAAS,OAAO,OAAO,EAAE,iBAAiB,IAAI,OAAO,WAAW;oBAChE,gBAAgB,OAAO,cAAc,IAAI;oBACzC,gBAAgB,OAAO,UAAU;oBACjC,eAAe,OAAO,aAAa,KAAK,YAAY,OAAO,aAAa,GAAG;oBAC3E,OAAO,OAAO,KAAK,GAAG,WAAW,OAAO,KAAK,IAAI;oBACjD,UAAU,OAAO,QAAQ,IAAI;oBAC7B,aAAa,OAAO,WAAW,IAAI;oBACnC,WAAW,OAAO,YAAY,IAAI;oBAClC,YAAY,cAAc,CAAC,EAAE,GAAG;wBAC9B,cAAc,cAAc,CAAC,EAAE,CAAC,YAAY,IAAgB,EAAE;wBAC9D,qBAAqB,cAAc,CAAC,EAAE,CAAC,mBAAmB,IAAI;wBAC9D,eAAe,cAAc,CAAC,EAAE,CAAC,aAAa,IAAgB,EAAE;oBAClE,IAAI;oBACJ,WAAW,aAAa,CAAC,EAAE,GAAG;wBAC5B,SAAS,aAAa,CAAC,EAAE,CAAC,OAAO,IAAI;wBACrC,UAAU,aAAa,CAAC,EAAE,CAAC,QAAQ,IAAI;wBACvC,YAAY,aAAa,CAAC,EAAE,CAAC,UAAU,IAAgB,EAAE;oBAC3D,IAAI;oBACJ,qBAAqB,WAAW,CAAC,EAAE,GAAG;wBACpC,WAAW,WAAW,CAAC,EAAE,CAAC,SAAS,GAAG,WAAW,WAAW,CAAC,EAAE,CAAC,SAAS,IAAI;wBAC7E,gBAAgB,WAAW,CAAC,EAAE,CAAC,cAAc,IAAgB,EAAE;wBAC/D,cAAc,WAAW,CAAC,EAAE,CAAC,YAAY,IAAgB,EAAE;oBAC7D,IAAI;oBACJ,SAAS,WAAW,CAAC,EAAE,GAAG;wBACxB,UAAU,WAAW,CAAC,EAAE,CAAC,QAAQ,IAAgB,EAAE;wBACnD,YAAY,WAAW,CAAC,EAAE,CAAC,UAAU,IAAgB,EAAE;wBACvD,eAAe,WAAW,CAAC,EAAE,CAAC,aAAa,IAAI;oBACjD,IAAI;oBACJ,mBAAmB,qBAAqB,CAAC,EAAE,GAAG;wBAC5C,cAAc,qBAAqB,CAAC,EAAE,CAAC,YAAY,IAA4C,EAAE;wBACjG,YAAY,qBAAqB,CAAC,EAAE,CAAC,UAAU,IAAgB,EAAE;wBACjE,SAAS,qBAAqB,CAAC,EAAE,CAAC,OAAO,IAAgB,EAAE;oBAC7D,IAAI;oBACJ,SAAS;oBACT,WAAW,cAAc,MAAM,GAAG,IAAI;wBACpC,IAAI,aAAa,CAAC,EAAE,CAAC,EAAE,CAAC,QAAQ;wBAChC,OAAO,aAAa,CAAC,EAAE,CAAC,IAAI;wBAC5B,MAAM;wBACN,WAAW,mBAAmB,GAAG,CAAC,CAAA;4BAChC,gDAAgD;4BAChD,IAAI;4BACJ,IAAI,EAAE,IAAI,KAAK,cAAc;gCAC3B,iEAAiE;gCACjE,MAAM,gBAAgB,EAAE,OAAO,EAAE,KAAK,CAAC,MAAa,IAAI,SAAS;gCACjE,IAAI,eAAe;oCACjB,gDAAgD;oCAChD,MAAM,cAAc,cAAc,OAAO,EAAE,KAAK,CAAC,IAAW,EAAE,IAAI,KAAK;oCACvE,gBAAgB,aAAa,OAAO,kBAAkB,SAAS,SAAS;gCAC1E;4BACF,OAAO,IAAI,EAAE,IAAI,KAAK,mBAAmB;gCACvC,4DAA4D;gCAC5D,MAAM,eAAe,EAAE,OAAO,EAAE,UAAU,CAAC,MAAa,IAAI,SAAS;gCACrE,gBAAgB,iBAAiB,CAAC,IAAI,eAAe;4BACvD,OAAO,IAAI,EAAE,IAAI,KAAK,SAAS;gCAC7B,uCAAuC;gCACvC,gBAAgB,EAAE,WAAW;4BAC/B;4BAEA,OAAO;gCACL,IAAI,EAAE,EAAE,CAAC,QAAQ;gCACjB,UAAU,EAAE,QAAQ;gCACpB,MAAM,EAAE,IAAI;gCACZ,SAAS,EAAE,OAAO,IAAI,EAAE;gCACxB,eAAe;gCACf,aAAa,EAAE,WAAW;4BAC5B;wBACF;wBACA,cAAc,WAAW,aAAa,CAAC,EAAE,CAAC,YAAY,IAAI;wBAC1D,WAAW,aAAa,CAAC,EAAE,CAAC,SAAS;wBACrC,UAAU;wBACV,aAAa;wBACb,UAAU;oBACZ,IAAI;wBACF,IAAI;wBACJ,OAAO;wBACP,MAAM;wBACN,WAAW,EAAE;wBACb,cAAc;wBACd,UAAU;wBACV,aAAa;wBACb,UAAU;oBACZ;oBACA,aAAa;wBACX,YAAY;wBACZ,aAAa;oBACf;oBACA,iBAAiB;oBACjB,eAAe;oBACf,QAAQ;oBACR,sCAAsC;oBACtC,aAAa,cAAc,MAAM;oBACjC,cAAc;gBAChB;YACF;YAGF,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,SAAS;gBAAM,SAAS;YAAkB;QACvE;QAEA,kCAAkC;QAClC,IAAI,CAAC,WAAW;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAsB,GAAG;gBAAE,QAAQ;YAAI;QAC3E;QAEA,IAAI;QACJ,IAAI,eAAe;YACjB,wDAAwD;YACxD,iBAAiB,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,EAAE,SAAS;QACtD,OAAO;YACL,uCAAuC;YACvC,iBAAiB,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,UAAO,CAAC,SAAS,EAAE,SAAS;QAClD;QAEA,uCAAuC;QACvC,MAAM,iBAAiB,MAAM,2IAAA,CAAA,KAAE,CAC5B,MAAM,CAAC;YACN,IAAI,4HAAA,CAAA,UAAO,CAAC,EAAE;YACd,MAAM,4HAAA,CAAA,UAAO,CAAC,IAAI;YAClB,aAAa,4HAAA,CAAA,UAAO,CAAC,WAAW;YAChC,YAAY,4HAAA,CAAA,UAAO,CAAC,UAAU;YAC9B,MAAM,4HAAA,CAAA,UAAO,CAAC,IAAI;YAClB,gBAAgB,4HAAA,CAAA,UAAO,CAAC,cAAc;YACtC,WAAW,4HAAA,CAAA,UAAO,CAAC,SAAS;YAC5B,SAAS,4HAAA,CAAA,UAAO,CAAC,OAAO;YACxB,WAAW,4HAAA,CAAA,UAAO,CAAC,SAAS;YAC5B,eAAe,4HAAA,CAAA,UAAO,CAAC,aAAa;YACpC,YAAY,4HAAA,CAAA,UAAO,CAAC,UAAU;YAC9B,cAAc,4HAAA,CAAA,UAAO,CAAC,YAAY;YAClC,eAAe,4HAAA,CAAA,UAAO,CAAC,aAAa;YACpC,OAAO,4HAAA,CAAA,UAAO,CAAC,KAAK;YACpB,UAAU,4HAAA,CAAA,UAAO,CAAC,QAAQ;YAC1B,aAAa,4HAAA,CAAA,UAAO,CAAC,WAAW;YAChC,WAAW,4HAAA,CAAA,UAAO,CAAC,SAAS;YAC5B,WAAW,4HAAA,CAAA,UAAO,CAAC,SAAS;YAC5B,aAAa,4HAAA,CAAA,QAAK,CAAC,IAAI;YACvB,cAAc,4HAAA,CAAA,QAAK,CAAC,KAAK;QAC3B,GACC,IAAI,CAAC,4HAAA,CAAA,UAAO,EACZ,QAAQ,CAAC,4HAAA,CAAA,QAAK,EAAE,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,UAAO,CAAC,SAAS,EAAE,4HAAA,CAAA,QAAK,CAAC,EAAE,GAC9C,KAAK,CAAC;QAET,mCAAmC;QACnC,MAAM,oBAAoB,MAAM,QAAQ,GAAG,CACzC,eAAe,GAAG,CAAC,OAAO;YACxB,MAAM,cAAc,MAAM,2IAAA,CAAA,KAAE,CACzB,MAAM,CAAC;gBAAE,OAAO,4HAAA,CAAA,UAAO,CAAC,EAAE;YAAC,GAC3B,IAAI,CAAC,4HAAA,CAAA,UAAO,EACZ,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,UAAO,CAAC,QAAQ,EAAE,OAAO,EAAE;YAEvC,OAAO;gBACL,GAAG,MAAM;gBACT,aAAa,YAAY,MAAM;gBAC/B,cAAc;gBACd,QAAQ,YAAY,+DAA+D;YACrF;QACF;QAGF,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;YAAM,SAAS;QAAkB;IACvE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EACJ,IAAI,EACJ,WAAW,EACX,UAAU,EACV,OAAO,YAAY,EACnB,iBAAiB,MAAM,EACvB,SAAS,EACT,OAAO,EACP,SAAS,EACT,aAAa,EACb,UAAU,EACV,YAAY,EACZ,aAAa,EACb,KAAK,EACL,QAAQ,EACR,WAAW,EACX,UAAU,EACV,SAAS,EACT,mBAAmB,EACnB,OAAO,EACP,iBAAiB,EAClB,GAAG;QAEJ,2BAA2B;QAC3B,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,eAAe;YACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAoD,GAC7D;gBAAE,QAAQ;YAAI;QAElB;QAEA,uDAAuD;QACvD,MAAM,UAAU,MAAM,2IAAA,CAAA,KAAE,CACrB,MAAM,GACN,IAAI,CAAC,4HAAA,CAAA,QAAK,EACV,KAAK,CACJ,CAAA,GAAA,oKAAA,CAAA,MAAG,AAAD,EACA,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,QAAK,CAAC,EAAE,EAAE,YACb,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,QAAK,CAAC,aAAa,EAAE,gBACxB,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,QAAK,CAAC,IAAI,EAAE,aAGlB,KAAK,CAAC;QAET,IAAI,QAAQ,MAAM,KAAK,GAAG;YACxB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsC,GAC/C;gBAAE,QAAQ;YAAI;QAElB;QAEA,uCAAuC;QACvC,IAAI,kBAAkB;QACtB,IAAI,CAAC,iBAAiB;YACpB,IAAI,WAAW;YACf,MAAO,CAAC,SAAU;gBAChB,kBAAkB;gBAClB,MAAM,WAAW,MAAM,2IAAA,CAAA,KAAE,CACtB,MAAM,GACN,IAAI,CAAC,4HAAA,CAAA,UAAO,EACZ,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE,kBAC7B,KAAK,CAAC;gBACT,WAAW,SAAS,MAAM,KAAK;YACjC;QACF,OAAO;YACL,0CAA0C;YAC1C,MAAM,WAAW,MAAM,2IAAA,CAAA,KAAE,CACtB,MAAM,GACN,IAAI,CAAC,4HAAA,CAAA,UAAO,EACZ,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE,kBAC7B,KAAK,CAAC;YAET,IAAI,SAAS,MAAM,GAAG,GAAG;gBACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAA6B,GACtC;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,oBAAoB;QACpB,MAAM,qBAAqB,MAAM,2IAAA,CAAA,KAAE,CAChC,MAAM,CAAC,4HAAA,CAAA,UAAO,EACd,MAAM,CAAC;YACN;YACA;YACA;YACA;YACA;YACA,WAAW,YAAY,IAAI,KAAK,aAAa;YAC7C,SAAS,UAAU,IAAI,KAAK,WAAW;YACvC;YACA;YACA,YAAY;YACZ;YACA;YACA;YACA;YACA;QACF,GACC,SAAS;QAEZ,MAAM,YAAY,kBAAkB,CAAC,EAAE;QAEvC,4CAA4C;QAC5C,IAAI,YAAY;YACd,MAAM,2IAAA,CAAA,KAAE,CAAC,MAAM,CAAC,4HAAA,CAAA,mBAAgB,EAAE,MAAM,CAAC;gBACvC,UAAU,UAAU,EAAE;gBACtB,cAAc,WAAW,YAAY;gBACrC,qBAAqB,WAAW,mBAAmB;gBACnD,eAAe,WAAW,aAAa;YACzC;QACF;QACA,IAAI,WAAW;YACb,MAAM,2IAAA,CAAA,KAAE,CAAC,MAAM,CAAC,4HAAA,CAAA,kBAAe,EAAE,MAAM,CAAC;gBACtC,UAAU,UAAU,EAAE;gBACtB,SAAS,UAAU,OAAO;gBAC1B,UAAU,UAAU,QAAQ;gBAC5B,YAAY,UAAU,UAAU;YAClC;QACF;QACA,IAAI,qBAAqB;YACvB,MAAM,2IAAA,CAAA,KAAE,CAAC,MAAM,CAAC,4HAAA,CAAA,4BAAyB,EAAE,MAAM,CAAC;gBAChD,UAAU,UAAU,EAAE;gBACtB,WAAW,oBAAoB,SAAS;gBACxC,gBAAgB,oBAAoB,cAAc;gBAClD,cAAc,oBAAoB,YAAY;YAChD;QACF;QACA,IAAI,SAAS;YACX,MAAM,2IAAA,CAAA,KAAE,CAAC,MAAM,CAAC,4HAAA,CAAA,gBAAa,EAAE,MAAM,CAAC;gBACpC,UAAU,UAAU,EAAE;gBACtB,UAAU,QAAQ,QAAQ;gBAC1B,YAAY,QAAQ,UAAU;gBAC9B,eAAe,QAAQ,aAAa;YACtC;QACF;QACA,IAAI,mBAAmB;YACrB,MAAM,2IAAA,CAAA,KAAE,CAAC,MAAM,CAAC,4HAAA,CAAA,0BAAuB,EAAE,MAAM,CAAC;gBAC9C,UAAU,UAAU,EAAE;gBACtB,cAAc,kBAAkB,YAAY;gBAC5C,YAAY,kBAAkB,UAAU;gBACxC,SAAS,kBAAkB,OAAO;YACpC;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAM,QAAQ;YAAW,SAAS;QAA8B,GAC3E;YAAE,QAAQ;QAAI;IAElB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}