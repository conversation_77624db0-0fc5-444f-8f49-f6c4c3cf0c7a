{"version": 3, "sources": [], "sections": [{"offset": {"line": 263, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/app/global-error.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as Sentry from '@sentry/nextjs';\r\nimport NextError from 'next/error';\r\nimport { useEffect } from 'react';\r\n\r\nexport default function GlobalError({\r\n  error\r\n}: {\r\n  error: Error & { digest?: string };\r\n}) {\r\n  useEffect(() => {\r\n    Sentry.captureException(error);\r\n  }, [error]);\r\n\r\n  return (\r\n    <html>\r\n      <body>\r\n        {/* `NextError` is the default Next.js error page component. Its type\r\n        definition requires a `statusCode` prop. However, since the App Router\r\n        does not expose status codes for errors, we simply pass 0 to render a\r\n        generic error message. */}\r\n        <NextError statusCode={0} />\r\n      </body>\r\n    </html>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS,YAAY,EAClC,KAAK,EAGN;IACC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,CAAA,GAAA,qKAAA,CAAA,mBAAuB,AAAD,EAAE;IAC1B,GAAG;QAAC;KAAM;IAEV,qBACE,8OAAC;kBACC,cAAA,8OAAC;sBAKC,cAAA,8OAAC,6HAAA,CAAA,UAAS;gBAAC,YAAY;;;;;;;;;;;;;;;;AAI/B", "debugId": null}}]}