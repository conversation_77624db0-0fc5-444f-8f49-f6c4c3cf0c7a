module.exports = {

"[project]/node_modules/kbar/lib/utils.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __assign = this && this.__assign || function() {
    __assign = Object.assign || function(t) {
        for(var s, i = 1, n = arguments.length; i < n; i++){
            s = arguments[i];
            for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = this && this.__createBinding || (Object.create ? function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, {
        enumerable: true,
        get: function() {
            return m[k];
        }
    });
} : function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
});
var __setModuleDefault = this && this.__setModuleDefault || (Object.create ? function(o, v) {
    Object.defineProperty(o, "default", {
        enumerable: true,
        value: v
    });
} : function(o, v) {
    o["default"] = v;
});
var __importStar = this && this.__importStar || function(mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) {
        for(var k in mod)if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    }
    __setModuleDefault(result, mod);
    return result;
};
var __spreadArray = this && this.__spreadArray || function(to, from, pack) {
    if (pack || arguments.length === 2) for(var i = 0, l = from.length, ar; i < l; i++){
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.Priority = exports.isModKey = exports.shouldRejectKeystrokes = exports.useThrottledValue = exports.getScrollbarWidth = exports.useIsomorphicLayout = exports.noop = exports.createAction = exports.randomId = exports.usePointerMovedSinceMount = exports.useOuterClick = exports.swallowEvent = void 0;
var React = __importStar(__turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)"));
function swallowEvent(event) {
    event.stopPropagation();
    event.preventDefault();
}
exports.swallowEvent = swallowEvent;
function useOuterClick(dom, cb) {
    var cbRef = React.useRef(cb);
    cbRef.current = cb;
    React.useEffect(function() {
        function handler(event) {
            var _a, _b;
            if (((_a = dom.current) === null || _a === void 0 ? void 0 : _a.contains(event.target)) || // Add support for ReactShadowRoot
            // @ts-expect-error wrong types, the `host` property exists https://stackoverflow.com/a/25340456
            event.target === ((_b = dom.current) === null || _b === void 0 ? void 0 : _b.getRootNode().host)) {
                return;
            }
            event.preventDefault();
            event.stopPropagation();
            cbRef.current();
        }
        window.addEventListener("pointerdown", handler, true);
        return function() {
            return window.removeEventListener("pointerdown", handler, true);
        };
    }, [
        dom
    ]);
}
exports.useOuterClick = useOuterClick;
function usePointerMovedSinceMount() {
    var _a = React.useState(false), moved = _a[0], setMoved = _a[1];
    React.useEffect(function() {
        function handler() {
            setMoved(true);
        }
        if (!moved) {
            window.addEventListener("pointermove", handler);
            return function() {
                return window.removeEventListener("pointermove", handler);
            };
        }
    }, [
        moved
    ]);
    return moved;
}
exports.usePointerMovedSinceMount = usePointerMovedSinceMount;
function randomId() {
    return Math.random().toString(36).substring(2, 9);
}
exports.randomId = randomId;
function createAction(params) {
    return __assign({
        id: randomId()
    }, params);
}
exports.createAction = createAction;
function noop() {}
exports.noop = noop;
exports.useIsomorphicLayout = typeof window === "undefined" ? noop : React.useLayoutEffect;
// https://stackoverflow.com/questions/13382516/getting-scroll-bar-width-using-javascript
function getScrollbarWidth() {
    var outer = document.createElement("div");
    outer.style.visibility = "hidden";
    outer.style.overflow = "scroll";
    document.body.appendChild(outer);
    var inner = document.createElement("div");
    outer.appendChild(inner);
    var scrollbarWidth = outer.offsetWidth - inner.offsetWidth;
    outer.parentNode.removeChild(outer);
    return scrollbarWidth;
}
exports.getScrollbarWidth = getScrollbarWidth;
function useThrottledValue(value, ms) {
    if (ms === void 0) {
        ms = 100;
    }
    var _a = React.useState(value), throttledValue = _a[0], setThrottledValue = _a[1];
    var lastRan = React.useRef(Date.now());
    React.useEffect(function() {
        if (ms === 0) return;
        var timeout = setTimeout(function() {
            setThrottledValue(value);
            lastRan.current = Date.now();
        }, lastRan.current - (Date.now() - ms));
        return function() {
            clearTimeout(timeout);
        };
    }, [
        ms,
        value
    ]);
    return ms === 0 ? value : throttledValue;
}
exports.useThrottledValue = useThrottledValue;
function shouldRejectKeystrokes(_a) {
    var _b, _c, _d;
    var _e = _a === void 0 ? {
        ignoreWhenFocused: []
    } : _a, ignoreWhenFocused = _e.ignoreWhenFocused;
    var inputs = __spreadArray([
        "input",
        "textarea"
    ], ignoreWhenFocused, true).map(function(el) {
        return el.toLowerCase();
    });
    var activeElement = document.activeElement;
    var ignoreStrokes = activeElement && (inputs.indexOf(activeElement.tagName.toLowerCase()) !== -1 || ((_b = activeElement.attributes.getNamedItem("role")) === null || _b === void 0 ? void 0 : _b.value) === "textbox" || ((_c = activeElement.attributes.getNamedItem("contenteditable")) === null || _c === void 0 ? void 0 : _c.value) === "true" || ((_d = activeElement.attributes.getNamedItem("contenteditable")) === null || _d === void 0 ? void 0 : _d.value) === "plaintext-only");
    return ignoreStrokes;
}
exports.shouldRejectKeystrokes = shouldRejectKeystrokes;
var SSR = typeof window === "undefined";
var isMac = !SSR && window.navigator.platform === "MacIntel";
function isModKey(event) {
    return isMac ? event.metaKey : event.ctrlKey;
}
exports.isModKey = isModKey;
exports.Priority = {
    HIGH: 1,
    NORMAL: 0,
    LOW: -1
};
}}),
"[project]/node_modules/fast-equals/dist/fast-equals.esm.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "circularDeepEqual": (()=>circularDeepEqual),
    "circularShallowEqual": (()=>circularShallowEqual),
    "createCustomEqual": (()=>createComparator),
    "deepEqual": (()=>deepEqual),
    "sameValueZeroEqual": (()=>sameValueZeroEqual),
    "shallowEqual": (()=>shallowEqual)
});
var HAS_WEAKSET_SUPPORT = typeof WeakSet === 'function';
var keys = Object.keys;
/**
 * are the values passed strictly equal or both NaN
 *
 * @param a the value to compare against
 * @param b the value to test
 * @returns are the values equal by the SameValueZero principle
 */ function sameValueZeroEqual(a, b) {
    return a === b || a !== a && b !== b;
}
/**
 * is the value a plain object
 *
 * @param value the value to test
 * @returns is the value a plain object
 */ function isPlainObject(value) {
    return value.constructor === Object || value.constructor == null;
}
/**
 * is the value promise-like (meaning it is thenable)
 *
 * @param value the value to test
 * @returns is the value promise-like
 */ function isPromiseLike(value) {
    return !!value && typeof value.then === 'function';
}
/**
 * is the value passed a react element
 *
 * @param value the value to test
 * @returns is the value a react element
 */ function isReactElement(value) {
    return !!(value && value.$$typeof);
}
/**
 * in cases where WeakSet is not supported, creates a new custom
 * object that mimics the necessary API aspects for cache purposes
 *
 * @returns the new cache object
 */ function getNewCacheFallback() {
    var values = [];
    return {
        add: function(value) {
            values.push(value);
        },
        has: function(value) {
            return values.indexOf(value) !== -1;
        }
    };
}
/**
 * get a new cache object to prevent circular references
 *
 * @returns the new cache object
 */ var getNewCache = function(canUseWeakMap) {
    if (canUseWeakMap) {
        return function _getNewCache() {
            return new WeakSet();
        };
    }
    return getNewCacheFallback;
}(HAS_WEAKSET_SUPPORT);
/**
 * create a custom isEqual handler specific to circular objects
 *
 * @param [isEqual] the isEqual comparator to use instead of isDeepEqual
 * @returns the method to create the `isEqual` function
 */ function createCircularEqualCreator(isEqual) {
    return function createCircularEqual(comparator) {
        var _comparator = isEqual || comparator;
        return function circularEqual(a, b, cache) {
            if (cache === void 0) {
                cache = getNewCache();
            }
            var isCacheableA = !!a && typeof a === 'object';
            var isCacheableB = !!b && typeof b === 'object';
            if (isCacheableA || isCacheableB) {
                var hasA = isCacheableA && cache.has(a);
                var hasB = isCacheableB && cache.has(b);
                if (hasA || hasB) {
                    return hasA && hasB;
                }
                if (isCacheableA) {
                    cache.add(a);
                }
                if (isCacheableB) {
                    cache.add(b);
                }
            }
            return _comparator(a, b, cache);
        };
    };
}
/**
 * are the arrays equal in value
 *
 * @param a the array to test
 * @param b the array to test against
 * @param isEqual the comparator to determine equality
 * @param meta the meta object to pass through
 * @returns are the arrays equal
 */ function areArraysEqual(a, b, isEqual, meta) {
    var index = a.length;
    if (b.length !== index) {
        return false;
    }
    while(index-- > 0){
        if (!isEqual(a[index], b[index], meta)) {
            return false;
        }
    }
    return true;
}
/**
 * are the maps equal in value
 *
 * @param a the map to test
 * @param b the map to test against
 * @param isEqual the comparator to determine equality
 * @param meta the meta map to pass through
 * @returns are the maps equal
 */ function areMapsEqual(a, b, isEqual, meta) {
    var isValueEqual = a.size === b.size;
    if (isValueEqual && a.size) {
        var matchedIndices_1 = {};
        a.forEach(function(aValue, aKey) {
            if (isValueEqual) {
                var hasMatch_1 = false;
                var matchIndex_1 = 0;
                b.forEach(function(bValue, bKey) {
                    if (!hasMatch_1 && !matchedIndices_1[matchIndex_1]) {
                        hasMatch_1 = isEqual(aKey, bKey, meta) && isEqual(aValue, bValue, meta);
                        if (hasMatch_1) {
                            matchedIndices_1[matchIndex_1] = true;
                        }
                    }
                    matchIndex_1++;
                });
                isValueEqual = hasMatch_1;
            }
        });
    }
    return isValueEqual;
}
var OWNER = '_owner';
var hasOwnProperty = Function.prototype.bind.call(Function.prototype.call, Object.prototype.hasOwnProperty);
/**
 * are the objects equal in value
 *
 * @param a the object to test
 * @param b the object to test against
 * @param isEqual the comparator to determine equality
 * @param meta the meta object to pass through
 * @returns are the objects equal
 */ function areObjectsEqual(a, b, isEqual, meta) {
    var keysA = keys(a);
    var index = keysA.length;
    if (keys(b).length !== index) {
        return false;
    }
    if (index) {
        var key = void 0;
        while(index-- > 0){
            key = keysA[index];
            if (key === OWNER) {
                var reactElementA = isReactElement(a);
                var reactElementB = isReactElement(b);
                if ((reactElementA || reactElementB) && reactElementA !== reactElementB) {
                    return false;
                }
            }
            if (!hasOwnProperty(b, key) || !isEqual(a[key], b[key], meta)) {
                return false;
            }
        }
    }
    return true;
}
/**
 * are the regExps equal in value
 *
 * @param a the regExp to test
 * @param b the regExp to test agains
 * @returns are the regExps equal
 */ function areRegExpsEqual(a, b) {
    return a.source === b.source && a.global === b.global && a.ignoreCase === b.ignoreCase && a.multiline === b.multiline && a.unicode === b.unicode && a.sticky === b.sticky && a.lastIndex === b.lastIndex;
}
/**
 * are the sets equal in value
 *
 * @param a the set to test
 * @param b the set to test against
 * @param isEqual the comparator to determine equality
 * @param meta the meta set to pass through
 * @returns are the sets equal
 */ function areSetsEqual(a, b, isEqual, meta) {
    var isValueEqual = a.size === b.size;
    if (isValueEqual && a.size) {
        var matchedIndices_2 = {};
        a.forEach(function(aValue) {
            if (isValueEqual) {
                var hasMatch_2 = false;
                var matchIndex_2 = 0;
                b.forEach(function(bValue) {
                    if (!hasMatch_2 && !matchedIndices_2[matchIndex_2]) {
                        hasMatch_2 = isEqual(aValue, bValue, meta);
                        if (hasMatch_2) {
                            matchedIndices_2[matchIndex_2] = true;
                        }
                    }
                    matchIndex_2++;
                });
                isValueEqual = hasMatch_2;
            }
        });
    }
    return isValueEqual;
}
var HAS_MAP_SUPPORT = typeof Map === 'function';
var HAS_SET_SUPPORT = typeof Set === 'function';
function createComparator(createIsEqual) {
    var isEqual = /* eslint-disable no-use-before-define */ typeof createIsEqual === 'function' ? createIsEqual(comparator) : comparator;
    /* eslint-enable */ /**
     * compare the value of the two objects and return true if they are equivalent in values
     *
     * @param a the value to test against
     * @param b the value to test
     * @param [meta] an optional meta object that is passed through to all equality test calls
     * @returns are a and b equivalent in value
     */ function comparator(a, b, meta) {
        if (a === b) {
            return true;
        }
        if (a && b && typeof a === 'object' && typeof b === 'object') {
            if (isPlainObject(a) && isPlainObject(b)) {
                return areObjectsEqual(a, b, isEqual, meta);
            }
            var aShape = Array.isArray(a);
            var bShape = Array.isArray(b);
            if (aShape || bShape) {
                return aShape === bShape && areArraysEqual(a, b, isEqual, meta);
            }
            aShape = a instanceof Date;
            bShape = b instanceof Date;
            if (aShape || bShape) {
                return aShape === bShape && sameValueZeroEqual(a.getTime(), b.getTime());
            }
            aShape = a instanceof RegExp;
            bShape = b instanceof RegExp;
            if (aShape || bShape) {
                return aShape === bShape && areRegExpsEqual(a, b);
            }
            if (isPromiseLike(a) || isPromiseLike(b)) {
                return a === b;
            }
            if (HAS_MAP_SUPPORT) {
                aShape = a instanceof Map;
                bShape = b instanceof Map;
                if (aShape || bShape) {
                    return aShape === bShape && areMapsEqual(a, b, isEqual, meta);
                }
            }
            if (HAS_SET_SUPPORT) {
                aShape = a instanceof Set;
                bShape = b instanceof Set;
                if (aShape || bShape) {
                    return aShape === bShape && areSetsEqual(a, b, isEqual, meta);
                }
            }
            return areObjectsEqual(a, b, isEqual, meta);
        }
        return a !== a && b !== b;
    }
    return comparator;
}
var deepEqual = createComparator();
var shallowEqual = createComparator(function() {
    return sameValueZeroEqual;
});
var circularDeepEqual = createComparator(createCircularEqualCreator());
var circularShallowEqual = createComparator(createCircularEqualCreator(sameValueZeroEqual));
;
 //# sourceMappingURL=fast-equals.esm.js.map
}}),
"[project]/node_modules/tiny-invariant/dist/tiny-invariant.cjs.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var isProduction = ("TURBOPACK compile-time value", "development") === 'production';
var prefix = 'Invariant failed';
function invariant(condition, message) {
    if (condition) {
        return;
    }
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    var provided = typeof message === 'function' ? message() : message;
    var value = provided ? "".concat(prefix, ": ").concat(provided) : prefix;
    throw new Error(value);
}
module.exports = invariant;
}}),
"[project]/node_modules/kbar/lib/action/Command.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.Command = void 0;
var Command = function() {
    function Command(command, options) {
        var _this = this;
        if (options === void 0) {
            options = {};
        }
        this.perform = function() {
            var negate = command.perform();
            // no need for history if non negatable
            if (typeof negate !== "function") return;
            // return if no history enabled
            var history = options.history;
            if (!history) return;
            // since we are performing the same action, we'll clean up the
            // previous call to the action and create a new history record
            if (_this.historyItem) {
                history.remove(_this.historyItem);
            }
            _this.historyItem = history.add({
                perform: command.perform,
                negate: negate
            });
            _this.history = {
                undo: function() {
                    return history.undo(_this.historyItem);
                },
                redo: function() {
                    return history.redo(_this.historyItem);
                }
            };
        };
    }
    return Command;
}();
exports.Command = Command;
}}),
"[project]/node_modules/kbar/lib/action/ActionImpl.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __importDefault = this && this.__importDefault || function(mod) {
    return mod && mod.__esModule ? mod : {
        "default": mod
    };
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.ActionImpl = void 0;
var tiny_invariant_1 = __importDefault(__turbopack_context__.r("[project]/node_modules/tiny-invariant/dist/tiny-invariant.cjs.js [app-ssr] (ecmascript)"));
var Command_1 = __turbopack_context__.r("[project]/node_modules/kbar/lib/action/Command.js [app-ssr] (ecmascript)");
var utils_1 = __turbopack_context__.r("[project]/node_modules/kbar/lib/utils.js [app-ssr] (ecmascript)");
/**
 * Extends the configured keywords to include the section
 * This allows section names to be searched for.
 */ var extendKeywords = function(_a) {
    var _b = _a.keywords, keywords = _b === void 0 ? "" : _b, _c = _a.section, section = _c === void 0 ? "" : _c;
    return (keywords + " " + (typeof section === "string" ? section : section.name)).trim();
};
var ActionImpl = function() {
    function ActionImpl(action, options) {
        var _this = this;
        var _a;
        this.priority = utils_1.Priority.NORMAL;
        this.ancestors = [];
        this.children = [];
        Object.assign(this, action);
        this.id = action.id;
        this.name = action.name;
        this.keywords = extendKeywords(action);
        var perform = action.perform;
        this.command = perform && new Command_1.Command({
            perform: function() {
                return perform(_this);
            }
        }, {
            history: options.history
        });
        // Backwards compatibility
        this.perform = (_a = this.command) === null || _a === void 0 ? void 0 : _a.perform;
        if (action.parent) {
            var parentActionImpl = options.store[action.parent];
            (0, tiny_invariant_1.default)(parentActionImpl, "attempted to create an action whos parent: " + action.parent + " does not exist in the store.");
            parentActionImpl.addChild(this);
        }
    }
    ActionImpl.prototype.addChild = function(childActionImpl) {
        // add all ancestors for the child action
        childActionImpl.ancestors.unshift(this);
        var parent = this.parentActionImpl;
        while(parent){
            childActionImpl.ancestors.unshift(parent);
            parent = parent.parentActionImpl;
        }
        // we ensure that order of adding always goes
        // parent -> children, so no need to recurse
        this.children.push(childActionImpl);
    };
    ActionImpl.prototype.removeChild = function(actionImpl) {
        var _this = this;
        // recursively remove all children
        var index = this.children.indexOf(actionImpl);
        if (index !== -1) {
            this.children.splice(index, 1);
        }
        if (actionImpl.children) {
            actionImpl.children.forEach(function(child) {
                _this.removeChild(child);
            });
        }
    };
    Object.defineProperty(ActionImpl.prototype, "parentActionImpl", {
        // easily access parentActionImpl after creation
        get: function() {
            return this.ancestors[this.ancestors.length - 1];
        },
        enumerable: false,
        configurable: true
    });
    ActionImpl.create = function(action, options) {
        return new ActionImpl(action, options);
    };
    return ActionImpl;
}();
exports.ActionImpl = ActionImpl;
}}),
"[project]/node_modules/kbar/lib/action/ActionInterface.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __assign = this && this.__assign || function() {
    __assign = Object.assign || function(t) {
        for(var s, i = 1, n = arguments.length; i < n; i++){
            s = arguments[i];
            for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __importDefault = this && this.__importDefault || function(mod) {
    return mod && mod.__esModule ? mod : {
        "default": mod
    };
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.ActionInterface = void 0;
var tiny_invariant_1 = __importDefault(__turbopack_context__.r("[project]/node_modules/tiny-invariant/dist/tiny-invariant.cjs.js [app-ssr] (ecmascript)"));
var ActionImpl_1 = __turbopack_context__.r("[project]/node_modules/kbar/lib/action/ActionImpl.js [app-ssr] (ecmascript)");
var ActionInterface = function() {
    function ActionInterface(actions, options) {
        if (actions === void 0) {
            actions = [];
        }
        if (options === void 0) {
            options = {};
        }
        this.actions = {};
        this.options = options;
        this.add(actions);
    }
    ActionInterface.prototype.add = function(actions) {
        for(var i = 0; i < actions.length; i++){
            var action = actions[i];
            if (action.parent) {
                (0, tiny_invariant_1.default)(this.actions[action.parent], "Attempted to create action \"" + action.name + "\" without registering its parent \"" + action.parent + "\" first.");
            }
            this.actions[action.id] = ActionImpl_1.ActionImpl.create(action, {
                history: this.options.historyManager,
                store: this.actions
            });
        }
        return __assign({}, this.actions);
    };
    ActionInterface.prototype.remove = function(actions) {
        var _this = this;
        actions.forEach(function(action) {
            var actionImpl = _this.actions[action.id];
            if (!actionImpl) return;
            var children = actionImpl.children;
            while(children.length){
                var child = children.pop();
                if (!child) return;
                delete _this.actions[child.id];
                if (child.parentActionImpl) child.parentActionImpl.removeChild(child);
                if (child.children) children.push.apply(children, child.children);
            }
            if (actionImpl.parentActionImpl) {
                actionImpl.parentActionImpl.removeChild(actionImpl);
            }
            delete _this.actions[action.id];
        });
        return __assign({}, this.actions);
    };
    return ActionInterface;
}();
exports.ActionInterface = ActionInterface;
}}),
"[project]/node_modules/kbar/lib/action/HistoryImpl.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.history = exports.HistoryItemImpl = void 0;
var utils_1 = __turbopack_context__.r("[project]/node_modules/kbar/lib/utils.js [app-ssr] (ecmascript)");
var HistoryItemImpl = function() {
    function HistoryItemImpl(item) {
        this.perform = item.perform;
        this.negate = item.negate;
    }
    HistoryItemImpl.create = function(item) {
        return new HistoryItemImpl(item);
    };
    return HistoryItemImpl;
}();
exports.HistoryItemImpl = HistoryItemImpl;
var HistoryImpl = function() {
    function HistoryImpl() {
        this.undoStack = [];
        this.redoStack = [];
        if (!HistoryImpl.instance) {
            HistoryImpl.instance = this;
            this.init();
        }
        return HistoryImpl.instance;
    }
    HistoryImpl.prototype.init = function() {
        var _this = this;
        if (typeof window === "undefined") return;
        window.addEventListener("keydown", function(event) {
            var _a;
            if (!_this.redoStack.length && !_this.undoStack.length || (0, utils_1.shouldRejectKeystrokes)()) {
                return;
            }
            var key = (_a = event.key) === null || _a === void 0 ? void 0 : _a.toLowerCase();
            if (event.metaKey && key === "z" && event.shiftKey) {
                _this.redo();
            } else if (event.metaKey && key === "z") {
                _this.undo();
            }
        });
    };
    HistoryImpl.prototype.add = function(item) {
        var historyItem = HistoryItemImpl.create(item);
        this.undoStack.push(historyItem);
        return historyItem;
    };
    HistoryImpl.prototype.remove = function(item) {
        var undoIndex = this.undoStack.findIndex(function(i) {
            return i === item;
        });
        if (undoIndex !== -1) {
            this.undoStack.splice(undoIndex, 1);
            return;
        }
        var redoIndex = this.redoStack.findIndex(function(i) {
            return i === item;
        });
        if (redoIndex !== -1) {
            this.redoStack.splice(redoIndex, 1);
        }
    };
    HistoryImpl.prototype.undo = function(item) {
        // if not undoing a specific item, just undo the latest
        if (!item) {
            var item_1 = this.undoStack.pop();
            if (!item_1) return;
            item_1 === null || item_1 === void 0 ? void 0 : item_1.negate();
            this.redoStack.push(item_1);
            return item_1;
        }
        // else undo the specific item
        var index = this.undoStack.findIndex(function(i) {
            return i === item;
        });
        if (index === -1) return;
        this.undoStack.splice(index, 1);
        item.negate();
        this.redoStack.push(item);
        return item;
    };
    HistoryImpl.prototype.redo = function(item) {
        if (!item) {
            var item_2 = this.redoStack.pop();
            if (!item_2) return;
            item_2 === null || item_2 === void 0 ? void 0 : item_2.perform();
            this.undoStack.push(item_2);
            return item_2;
        }
        var index = this.redoStack.findIndex(function(i) {
            return i === item;
        });
        if (index === -1) return;
        this.redoStack.splice(index, 1);
        item.perform();
        this.undoStack.push(item);
        return item;
    };
    HistoryImpl.prototype.reset = function() {
        this.undoStack.splice(0);
        this.redoStack.splice(0);
    };
    return HistoryImpl;
}();
var history = new HistoryImpl();
exports.history = history;
Object.freeze(history);
}}),
"[project]/node_modules/kbar/lib/types.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.VisualState = void 0;
var VisualState;
(function(VisualState) {
    VisualState["animatingIn"] = "animating-in";
    VisualState["showing"] = "showing";
    VisualState["animatingOut"] = "animating-out";
    VisualState["hidden"] = "hidden";
})(VisualState = exports.VisualState || (exports.VisualState = {}));
}}),
"[project]/node_modules/kbar/lib/useStore.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __assign = this && this.__assign || function() {
    __assign = Object.assign || function(t) {
        for(var s, i = 1, n = arguments.length; i < n; i++){
            s = arguments[i];
            for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = this && this.__createBinding || (Object.create ? function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, {
        enumerable: true,
        get: function() {
            return m[k];
        }
    });
} : function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
});
var __setModuleDefault = this && this.__setModuleDefault || (Object.create ? function(o, v) {
    Object.defineProperty(o, "default", {
        enumerable: true,
        value: v
    });
} : function(o, v) {
    o["default"] = v;
});
var __importStar = this && this.__importStar || function(mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) {
        for(var k in mod)if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    }
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = this && this.__importDefault || function(mod) {
    return mod && mod.__esModule ? mod : {
        "default": mod
    };
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.useStore = void 0;
var fast_equals_1 = __turbopack_context__.r("[project]/node_modules/fast-equals/dist/fast-equals.esm.js [app-ssr] (ecmascript)");
var React = __importStar(__turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)"));
var tiny_invariant_1 = __importDefault(__turbopack_context__.r("[project]/node_modules/tiny-invariant/dist/tiny-invariant.cjs.js [app-ssr] (ecmascript)"));
var ActionInterface_1 = __turbopack_context__.r("[project]/node_modules/kbar/lib/action/ActionInterface.js [app-ssr] (ecmascript)");
var HistoryImpl_1 = __turbopack_context__.r("[project]/node_modules/kbar/lib/action/HistoryImpl.js [app-ssr] (ecmascript)");
var types_1 = __turbopack_context__.r("[project]/node_modules/kbar/lib/types.js [app-ssr] (ecmascript)");
function useStore(props) {
    var optionsRef = React.useRef(__assign({
        animations: {
            enterMs: 200,
            exitMs: 100
        }
    }, props.options));
    var actionsInterface = React.useMemo(function() {
        return new ActionInterface_1.ActionInterface(props.actions || [], {
            historyManager: optionsRef.current.enableHistory ? HistoryImpl_1.history : undefined
        });
    }, // eslint-disable-next-line react-hooks/exhaustive-deps
    []);
    // TODO: at this point useReducer might be a better approach to managing state.
    var _a = React.useState({
        searchQuery: "",
        currentRootActionId: null,
        visualState: types_1.VisualState.hidden,
        actions: __assign({}, actionsInterface.actions),
        activeIndex: 0,
        disabled: false
    }), state = _a[0], setState = _a[1];
    var currState = React.useRef(state);
    currState.current = state;
    var getState = React.useCallback(function() {
        return currState.current;
    }, []);
    var publisher = React.useMemo(function() {
        return new Publisher(getState);
    }, [
        getState
    ]);
    React.useEffect(function() {
        currState.current = state;
        publisher.notify();
    }, [
        state,
        publisher
    ]);
    var registerActions = React.useCallback(function(actions) {
        setState(function(state) {
            return __assign(__assign({}, state), {
                actions: actionsInterface.add(actions)
            });
        });
        return function unregister() {
            setState(function(state) {
                return __assign(__assign({}, state), {
                    actions: actionsInterface.remove(actions)
                });
            });
        };
    }, [
        actionsInterface
    ]);
    var inputRef = React.useRef(null);
    return React.useMemo(function() {
        var query = {
            setCurrentRootAction: function(actionId) {
                setState(function(state) {
                    return __assign(__assign({}, state), {
                        currentRootActionId: actionId
                    });
                });
            },
            setVisualState: function(cb) {
                setState(function(state) {
                    return __assign(__assign({}, state), {
                        visualState: typeof cb === "function" ? cb(state.visualState) : cb
                    });
                });
            },
            setSearch: function(searchQuery) {
                return setState(function(state) {
                    return __assign(__assign({}, state), {
                        searchQuery: searchQuery
                    });
                });
            },
            registerActions: registerActions,
            toggle: function() {
                return setState(function(state) {
                    return __assign(__assign({}, state), {
                        visualState: [
                            types_1.VisualState.animatingOut,
                            types_1.VisualState.hidden
                        ].includes(state.visualState) ? types_1.VisualState.animatingIn : types_1.VisualState.animatingOut
                    });
                });
            },
            setActiveIndex: function(cb) {
                return setState(function(state) {
                    return __assign(__assign({}, state), {
                        activeIndex: typeof cb === "number" ? cb : cb(state.activeIndex)
                    });
                });
            },
            inputRefSetter: function(el) {
                inputRef.current = el;
            },
            getInput: function() {
                (0, tiny_invariant_1.default)(inputRef.current, "Input ref is undefined, make sure you attach `query.inputRefSetter` to your search input.");
                return inputRef.current;
            },
            disable: function(disable) {
                setState(function(state) {
                    return __assign(__assign({}, state), {
                        disabled: disable
                    });
                });
            }
        };
        return {
            getState: getState,
            query: query,
            options: optionsRef.current,
            subscribe: function(collector, cb) {
                return publisher.subscribe(collector, cb);
            }
        };
    }, [
        getState,
        publisher,
        registerActions
    ]);
}
exports.useStore = useStore;
var Publisher = function() {
    function Publisher(getState) {
        this.subscribers = [];
        this.getState = getState;
    }
    Publisher.prototype.subscribe = function(collector, onChange) {
        var _this = this;
        var subscriber = new Subscriber(function() {
            return collector(_this.getState());
        }, onChange);
        this.subscribers.push(subscriber);
        return this.unsubscribe.bind(this, subscriber);
    };
    Publisher.prototype.unsubscribe = function(subscriber) {
        if (this.subscribers.length) {
            var index = this.subscribers.indexOf(subscriber);
            if (index > -1) {
                return this.subscribers.splice(index, 1);
            }
        }
    };
    Publisher.prototype.notify = function() {
        this.subscribers.forEach(function(subscriber) {
            return subscriber.collect();
        });
    };
    return Publisher;
}();
var Subscriber = function() {
    function Subscriber(collector, onChange) {
        this.collector = collector;
        this.onChange = onChange;
    }
    Subscriber.prototype.collect = function() {
        try {
            // grab latest state
            var recollect = this.collector();
            if (!(0, fast_equals_1.deepEqual)(recollect, this.collected)) {
                this.collected = recollect;
                if (this.onChange) {
                    this.onChange(this.collected);
                }
            }
        } catch (error) {
            console.warn(error);
        }
    };
    return Subscriber;
}();
}}),
"[project]/node_modules/kbar/lib/tinykeys.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
// Fixes special character issues; `?` -> `shift+/` + build issue
// https://github.com/jamiebuilds/tinykeys
Object.defineProperty(exports, "__esModule", {
    value: true
});
/**
 * These are the modifier keys that change the meaning of keybindings.
 *
 * Note: Ignoring "AltGraph" because it is covered by the others.
 */ var KEYBINDING_MODIFIER_KEYS = [
    "Shift",
    "Meta",
    "Alt",
    "Control"
];
/**
 * Keybinding sequences should timeout if individual key presses are more than
 * 1s apart by default.
 */ var DEFAULT_TIMEOUT = 1000;
/**
 * Keybinding sequences should bind to this event by default.
 */ var DEFAULT_EVENT = "keydown";
/**
 * An alias for creating platform-specific keybinding aliases.
 */ var MOD = typeof navigator === "object" && /Mac|iPod|iPhone|iPad/.test(navigator.platform) ? "Meta" : "Control";
/**
 * There's a bug in Chrome that causes event.getModifierState not to exist on
 * KeyboardEvent's for F1/F2/etc keys.
 */ function getModifierState(event, mod) {
    return typeof event.getModifierState === "function" ? event.getModifierState(mod) : false;
}
/**
 * Parses a "Key Binding String" into its parts
 *
 * grammar    = `<sequence>`
 * <sequence> = `<press> <press> <press> ...`
 * <press>    = `<key>` or `<mods>+<key>`
 * <mods>     = `<mod>+<mod>+...`
 */ function parse(str) {
    return str.trim().split(" ").map(function(press) {
        var mods = press.split(/\b\+/);
        var key = mods.pop();
        mods = mods.map(function(mod) {
            return mod === "$mod" ? MOD : mod;
        });
        return [
            mods,
            key
        ];
    });
}
/**
 * This tells us if a series of events matches a key binding sequence either
 * partially or exactly.
 */ function match(event, press) {
    // Special characters; `?` `!`
    if (/^[^A-Za-z0-9]$/.test(event.key) && press[1] === event.key) {
        return true;
    }
    // prettier-ignore
    return !(// Allow either the `event.key` or the `event.code`
    // MDN event.key: https://developer.mozilla.org/en-US/docs/Web/API/KeyboardEvent/key
    // MDN event.code: https://developer.mozilla.org/en-US/docs/Web/API/KeyboardEvent/code
    press[1].toUpperCase() !== event.key.toUpperCase() && press[1] !== event.code || // Ensure all the modifiers in the keybinding are pressed.
    press[0].find(function(mod) {
        return !getModifierState(event, mod);
    }) || // KEYBINDING_MODIFIER_KEYS (Shift/Control/etc) change the meaning of a
    // keybinding. So if they are pressed but aren't part of the current
    // keybinding press, then we don't have a match.
    KEYBINDING_MODIFIER_KEYS.find(function(mod) {
        return !press[0].includes(mod) && press[1] !== mod && getModifierState(event, mod);
    }));
}
/**
 * Subscribes to keybindings.
 *
 * Returns an unsubscribe method.
 *
 * @example
 * ```js
 * import keybindings from "../src/keybindings"
 *
 * keybindings(window, {
 * 	"Shift+d": () => {
 * 		alert("The 'Shift' and 'd' keys were pressed at the same time")
 * 	},
 * 	"y e e t": () => {
 * 		alert("The keys 'y', 'e', 'e', and 't' were pressed in order")
 * 	},
 * 	"$mod+d": () => {
 * 		alert("Either 'Control+d' or 'Meta+d' were pressed")
 * 	},
 * })
 * ```
 */ function keybindings(target, keyBindingMap, options) {
    var _a, _b;
    if (options === void 0) {
        options = {};
    }
    var timeout = (_a = options.timeout) !== null && _a !== void 0 ? _a : DEFAULT_TIMEOUT;
    var event = (_b = options.event) !== null && _b !== void 0 ? _b : DEFAULT_EVENT;
    var keyBindings = Object.keys(keyBindingMap).map(function(key) {
        return [
            parse(key),
            keyBindingMap[key]
        ];
    });
    var possibleMatches = new Map();
    var timer = null;
    var onKeyEvent = function(event) {
        // Ensure and stop any event that isn't a full keyboard event.
        // Autocomplete option navigation and selection would fire a instanceof Event,
        // instead of the expected KeyboardEvent
        if (!(event instanceof KeyboardEvent)) {
            return;
        }
        keyBindings.forEach(function(keyBinding) {
            var sequence = keyBinding[0];
            var callback = keyBinding[1];
            var prev = possibleMatches.get(sequence);
            var remainingExpectedPresses = prev ? prev : sequence;
            var currentExpectedPress = remainingExpectedPresses[0];
            var matches = match(event, currentExpectedPress);
            if (!matches) {
                // Modifier keydown events shouldn't break sequences
                // Note: This works because:
                // - non-modifiers will always return false
                // - if the current keypress is a modifier then it will return true when we check its state
                // MDN: https://developer.mozilla.org/en-US/docs/Web/API/KeyboardEvent/getModifierState
                if (!getModifierState(event, event.key)) {
                    possibleMatches.delete(sequence);
                }
            } else if (remainingExpectedPresses.length > 1) {
                possibleMatches.set(sequence, remainingExpectedPresses.slice(1));
            } else {
                possibleMatches.delete(sequence);
                callback(event);
            }
        });
        if (timer) {
            clearTimeout(timer);
        }
        // @ts-ignore
        timer = setTimeout(possibleMatches.clear.bind(possibleMatches), timeout);
    };
    target.addEventListener(event, onKeyEvent);
    return function() {
        target.removeEventListener(event, onKeyEvent);
    };
}
exports.default = keybindings;
}}),
"[project]/node_modules/kbar/lib/InternalEvents.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __createBinding = this && this.__createBinding || (Object.create ? function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, {
        enumerable: true,
        get: function() {
            return m[k];
        }
    });
} : function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
});
var __setModuleDefault = this && this.__setModuleDefault || (Object.create ? function(o, v) {
    Object.defineProperty(o, "default", {
        enumerable: true,
        value: v
    });
} : function(o, v) {
    o["default"] = v;
});
var __importStar = this && this.__importStar || function(mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) {
        for(var k in mod)if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    }
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = this && this.__importDefault || function(mod) {
    return mod && mod.__esModule ? mod : {
        "default": mod
    };
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.InternalEvents = void 0;
var React = __importStar(__turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)"));
var tinykeys_1 = __importDefault(__turbopack_context__.r("[project]/node_modules/kbar/lib/tinykeys.js [app-ssr] (ecmascript)"));
var types_1 = __turbopack_context__.r("[project]/node_modules/kbar/lib/types.js [app-ssr] (ecmascript)");
var useKBar_1 = __turbopack_context__.r("[project]/node_modules/kbar/lib/useKBar.js [app-ssr] (ecmascript)");
var utils_1 = __turbopack_context__.r("[project]/node_modules/kbar/lib/utils.js [app-ssr] (ecmascript)");
function InternalEvents() {
    useToggleHandler();
    useDocumentLock();
    useShortcuts();
    useFocusHandler();
    return null;
}
exports.InternalEvents = InternalEvents;
/**
 * `useToggleHandler` handles the keyboard events for toggling kbar.
 */ function useToggleHandler() {
    var _a, _b;
    var _c = (0, useKBar_1.useKBar)(function(state) {
        return {
            visualState: state.visualState,
            showing: state.visualState !== types_1.VisualState.hidden,
            disabled: state.disabled
        };
    }), query = _c.query, options = _c.options, visualState = _c.visualState, showing = _c.showing, disabled = _c.disabled;
    React.useEffect(function() {
        var _a;
        var close = function() {
            query.setVisualState(function(vs) {
                if (vs === types_1.VisualState.hidden || vs === types_1.VisualState.animatingOut) {
                    return vs;
                }
                return types_1.VisualState.animatingOut;
            });
        };
        if (disabled) {
            close();
            return;
        }
        var shortcut = options.toggleShortcut || "$mod+k";
        var unsubscribe = (0, tinykeys_1.default)(window, (_a = {}, _a[shortcut] = function(event) {
            var _a, _b, _c, _d;
            if (event.defaultPrevented) return;
            event.preventDefault();
            query.toggle();
            if (showing) {
                (_b = (_a = options.callbacks) === null || _a === void 0 ? void 0 : _a.onClose) === null || _b === void 0 ? void 0 : _b.call(_a);
            } else {
                (_d = (_c = options.callbacks) === null || _c === void 0 ? void 0 : _c.onOpen) === null || _d === void 0 ? void 0 : _d.call(_c);
            }
        }, _a.Escape = function(event) {
            var _a, _b;
            if (showing) {
                event.stopPropagation();
                event.preventDefault();
                (_b = (_a = options.callbacks) === null || _a === void 0 ? void 0 : _a.onClose) === null || _b === void 0 ? void 0 : _b.call(_a);
            }
            close();
        }, _a));
        return function() {
            unsubscribe();
        };
    }, [
        options.callbacks,
        options.toggleShortcut,
        query,
        showing,
        disabled
    ]);
    var timeoutRef = React.useRef();
    var runAnimateTimer = React.useCallback(function(vs) {
        var _a, _b;
        var ms = 0;
        if (vs === types_1.VisualState.animatingIn) {
            ms = ((_a = options.animations) === null || _a === void 0 ? void 0 : _a.enterMs) || 0;
        }
        if (vs === types_1.VisualState.animatingOut) {
            ms = ((_b = options.animations) === null || _b === void 0 ? void 0 : _b.exitMs) || 0;
        }
        clearTimeout(timeoutRef.current);
        timeoutRef.current = setTimeout(function() {
            var backToRoot = false;
            // TODO: setVisualState argument should be a function or just a VisualState value.
            query.setVisualState(function() {
                var finalVs = vs === types_1.VisualState.animatingIn ? types_1.VisualState.showing : types_1.VisualState.hidden;
                if (finalVs === types_1.VisualState.hidden) {
                    backToRoot = true;
                }
                return finalVs;
            });
            if (backToRoot) {
                query.setCurrentRootAction(null);
            }
        }, ms);
    }, [
        (_a = options.animations) === null || _a === void 0 ? void 0 : _a.enterMs,
        (_b = options.animations) === null || _b === void 0 ? void 0 : _b.exitMs,
        query
    ]);
    React.useEffect(function() {
        switch(visualState){
            case types_1.VisualState.animatingIn:
            case types_1.VisualState.animatingOut:
                runAnimateTimer(visualState);
                break;
        }
    }, [
        runAnimateTimer,
        visualState
    ]);
}
/**
 * `useDocumentLock` is a simple implementation for preventing the
 * underlying page content from scrolling when kbar is open.
 */ function useDocumentLock() {
    var _a = (0, useKBar_1.useKBar)(function(state) {
        return {
            visualState: state.visualState
        };
    }), visualState = _a.visualState, options = _a.options;
    React.useEffect(function() {
        if (options.disableDocumentLock) return;
        if (visualState === types_1.VisualState.animatingIn) {
            document.body.style.overflow = "hidden";
            if (!options.disableScrollbarManagement) {
                var scrollbarWidth = (0, utils_1.getScrollbarWidth)();
                // take into account the margins explicitly added by the consumer
                var mr = getComputedStyle(document.body)["margin-right"];
                if (mr) {
                    // remove non-numeric values; px, rem, em, etc.
                    scrollbarWidth += Number(mr.replace(/\D/g, ""));
                }
                document.body.style.marginRight = scrollbarWidth + "px";
            }
        } else if (visualState === types_1.VisualState.hidden) {
            document.body.style.removeProperty("overflow");
            if (!options.disableScrollbarManagement) {
                document.body.style.removeProperty("margin-right");
            }
        }
    }, [
        options.disableDocumentLock,
        options.disableScrollbarManagement,
        visualState
    ]);
}
/**
 * Reference: https://github.com/jamiebuilds/tinykeys/issues/37
 *
 * Fixes an issue where simultaneous key commands for shortcuts;
 * ie given two actions with shortcuts ['t','s'] and ['s'], pressing
 * 't' and 's' consecutively will cause both shortcuts to fire.
 *
 * `wrap` sets each keystroke event in a WeakSet, and ensures that
 * if ['t', 's'] are pressed, then the subsequent ['s'] event will
 * be ignored. This depends on the order in which we register the
 * shortcuts to tinykeys, which is handled below.
 */ var handled = new WeakSet();
function wrap(handler) {
    return function(event) {
        if (handled.has(event)) return;
        handler(event);
        handled.add(event);
    };
}
/**
 * `useShortcuts` registers and listens to keyboard strokes and
 * performs actions for patterns that match the user defined `shortcut`.
 */ function useShortcuts() {
    var _a = (0, useKBar_1.useKBar)(function(state) {
        return {
            actions: state.actions,
            open: state.visualState === types_1.VisualState.showing,
            disabled: state.disabled
        };
    }), actions = _a.actions, query = _a.query, open = _a.open, options = _a.options, disabled = _a.disabled;
    React.useEffect(function() {
        var _a;
        if (open || disabled) return;
        var actionsList = Object.keys(actions).map(function(key) {
            return actions[key];
        });
        var actionsWithShortcuts = [];
        for(var _i = 0, actionsList_1 = actionsList; _i < actionsList_1.length; _i++){
            var action = actionsList_1[_i];
            if (!((_a = action.shortcut) === null || _a === void 0 ? void 0 : _a.length)) {
                continue;
            }
            actionsWithShortcuts.push(action);
        }
        actionsWithShortcuts = actionsWithShortcuts.sort(function(a, b) {
            return b.shortcut.join(" ").length - a.shortcut.join(" ").length;
        });
        var shortcutsMap = {};
        var _loop_1 = function(action) {
            var shortcut = action.shortcut.join(" ");
            shortcutsMap[shortcut] = wrap(function(event) {
                var _a, _b, _c, _d, _e, _f;
                if ((0, utils_1.shouldRejectKeystrokes)()) return;
                event.preventDefault();
                if ((_a = action.children) === null || _a === void 0 ? void 0 : _a.length) {
                    query.setCurrentRootAction(action.id);
                    query.toggle();
                    (_c = (_b = options.callbacks) === null || _b === void 0 ? void 0 : _b.onOpen) === null || _c === void 0 ? void 0 : _c.call(_b);
                } else {
                    (_d = action.command) === null || _d === void 0 ? void 0 : _d.perform();
                    (_f = (_e = options.callbacks) === null || _e === void 0 ? void 0 : _e.onSelectAction) === null || _f === void 0 ? void 0 : _f.call(_e, action);
                }
            });
        };
        for(var _b = 0, actionsWithShortcuts_1 = actionsWithShortcuts; _b < actionsWithShortcuts_1.length; _b++){
            var action = actionsWithShortcuts_1[_b];
            _loop_1(action);
        }
        var unsubscribe = (0, tinykeys_1.default)(window, shortcutsMap, {
            timeout: 400
        });
        return function() {
            unsubscribe();
        };
    }, [
        actions,
        open,
        options.callbacks,
        query,
        disabled
    ]);
}
/**
 * `useFocusHandler` ensures that focus is set back on the element which was
 * in focus prior to kbar being triggered.
 */ function useFocusHandler() {
    var rFirstRender = React.useRef(true);
    var _a = (0, useKBar_1.useKBar)(function(state) {
        return {
            isShowing: state.visualState === types_1.VisualState.showing || state.visualState === types_1.VisualState.animatingIn
        };
    }), isShowing = _a.isShowing, query = _a.query;
    var activeElementRef = React.useRef(null);
    React.useEffect(function() {
        if (rFirstRender.current) {
            rFirstRender.current = false;
            return;
        }
        if (isShowing) {
            activeElementRef.current = document.activeElement;
            return;
        }
        // This fixes an issue on Safari where closing kbar causes the entire
        // page to scroll to the bottom. The reason this was happening was due
        // to the search input still in focus when we removed it from the dom.
        var currentActiveElement = document.activeElement;
        if ((currentActiveElement === null || currentActiveElement === void 0 ? void 0 : currentActiveElement.tagName.toLowerCase()) === "input") {
            currentActiveElement.blur();
        }
        var activeElement = activeElementRef.current;
        if (activeElement && activeElement !== currentActiveElement) {
            activeElement.focus();
        }
    }, [
        isShowing
    ]);
    // When focus is blurred from the search input while kbar is still
    // open, any keystroke should set focus back to the search input.
    React.useEffect(function() {
        function handler(event) {
            var input = query.getInput();
            if (event.target !== input) {
                input.focus();
            }
        }
        if (isShowing) {
            window.addEventListener("keydown", handler);
            return function() {
                window.removeEventListener("keydown", handler);
            };
        }
    }, [
        isShowing,
        query
    ]);
}
}}),
"[project]/node_modules/kbar/lib/KBarContextProvider.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __createBinding = this && this.__createBinding || (Object.create ? function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, {
        enumerable: true,
        get: function() {
            return m[k];
        }
    });
} : function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
});
var __setModuleDefault = this && this.__setModuleDefault || (Object.create ? function(o, v) {
    Object.defineProperty(o, "default", {
        enumerable: true,
        value: v
    });
} : function(o, v) {
    o["default"] = v;
});
var __importStar = this && this.__importStar || function(mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) {
        for(var k in mod)if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    }
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.KBarProvider = exports.KBarContext = void 0;
var useStore_1 = __turbopack_context__.r("[project]/node_modules/kbar/lib/useStore.js [app-ssr] (ecmascript)");
var React = __importStar(__turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)"));
var InternalEvents_1 = __turbopack_context__.r("[project]/node_modules/kbar/lib/InternalEvents.js [app-ssr] (ecmascript)");
exports.KBarContext = React.createContext({});
var KBarProvider = function(props) {
    var contextValue = (0, useStore_1.useStore)(props);
    return React.createElement(exports.KBarContext.Provider, {
        value: contextValue
    }, React.createElement(InternalEvents_1.InternalEvents, null), props.children);
};
exports.KBarProvider = KBarProvider;
}}),
"[project]/node_modules/kbar/lib/useKBar.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __assign = this && this.__assign || function() {
    __assign = Object.assign || function(t) {
        for(var s, i = 1, n = arguments.length; i < n; i++){
            s = arguments[i];
            for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = this && this.__createBinding || (Object.create ? function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, {
        enumerable: true,
        get: function() {
            return m[k];
        }
    });
} : function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
});
var __setModuleDefault = this && this.__setModuleDefault || (Object.create ? function(o, v) {
    Object.defineProperty(o, "default", {
        enumerable: true,
        value: v
    });
} : function(o, v) {
    o["default"] = v;
});
var __importStar = this && this.__importStar || function(mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) {
        for(var k in mod)if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    }
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.useKBar = void 0;
var React = __importStar(__turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)"));
var KBarContextProvider_1 = __turbopack_context__.r("[project]/node_modules/kbar/lib/KBarContextProvider.js [app-ssr] (ecmascript)");
function useKBar(collector) {
    var _a = React.useContext(KBarContextProvider_1.KBarContext), query = _a.query, getState = _a.getState, subscribe = _a.subscribe, options = _a.options;
    var collected = React.useRef(collector === null || collector === void 0 ? void 0 : collector(getState()));
    var collectorRef = React.useRef(collector);
    var onCollect = React.useCallback(function(collected) {
        return __assign(__assign({}, collected), {
            query: query,
            options: options
        });
    }, [
        query,
        options
    ]);
    var _b = React.useState(onCollect(collected.current)), render = _b[0], setRender = _b[1];
    React.useEffect(function() {
        var unsubscribe;
        if (collectorRef.current) {
            unsubscribe = subscribe(function(current) {
                return collectorRef.current(current);
            }, function(collected) {
                return setRender(onCollect(collected));
            });
        }
        return function() {
            if (unsubscribe) {
                unsubscribe();
            }
        };
    }, [
        onCollect,
        subscribe
    ]);
    return render;
}
exports.useKBar = useKBar;
}}),
"[project]/node_modules/fuse.js/dist/fuse.esm.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Fuse.js v6.6.2 - Lightweight fuzzy-search (http://fusejs.io)
 *
 * Copyright (c) 2022 Kiro Risk (http://kiro.me)
 * All Rights Reserved. Apache Software License 2.0
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 */ __turbopack_context__.s({
    "default": (()=>Fuse)
});
function isArray(value) {
    return !Array.isArray ? getTag(value) === '[object Array]' : Array.isArray(value);
}
// Adapted from: https://github.com/lodash/lodash/blob/master/.internal/baseToString.js
const INFINITY = 1 / 0;
function baseToString(value) {
    // Exit early for strings to avoid a performance hit in some environments.
    if (typeof value == 'string') {
        return value;
    }
    let result = value + '';
    return result == '0' && 1 / value == -INFINITY ? '-0' : result;
}
function toString(value) {
    return value == null ? '' : baseToString(value);
}
function isString(value) {
    return typeof value === 'string';
}
function isNumber(value) {
    return typeof value === 'number';
}
// Adapted from: https://github.com/lodash/lodash/blob/master/isBoolean.js
function isBoolean(value) {
    return value === true || value === false || isObjectLike(value) && getTag(value) == '[object Boolean]';
}
function isObject(value) {
    return typeof value === 'object';
}
// Checks if `value` is object-like.
function isObjectLike(value) {
    return isObject(value) && value !== null;
}
function isDefined(value) {
    return value !== undefined && value !== null;
}
function isBlank(value) {
    return !value.trim().length;
}
// Gets the `toStringTag` of `value`.
// Adapted from: https://github.com/lodash/lodash/blob/master/.internal/getTag.js
function getTag(value) {
    return value == null ? value === undefined ? '[object Undefined]' : '[object Null]' : Object.prototype.toString.call(value);
}
const EXTENDED_SEARCH_UNAVAILABLE = 'Extended search is not available';
const INCORRECT_INDEX_TYPE = "Incorrect 'index' type";
const LOGICAL_SEARCH_INVALID_QUERY_FOR_KEY = (key)=>`Invalid value for key ${key}`;
const PATTERN_LENGTH_TOO_LARGE = (max)=>`Pattern length exceeds max of ${max}.`;
const MISSING_KEY_PROPERTY = (name)=>`Missing ${name} property in key`;
const INVALID_KEY_WEIGHT_VALUE = (key)=>`Property 'weight' in key '${key}' must be a positive integer`;
const hasOwn = Object.prototype.hasOwnProperty;
class KeyStore {
    constructor(keys){
        this._keys = [];
        this._keyMap = {};
        let totalWeight = 0;
        keys.forEach((key)=>{
            let obj = createKey(key);
            totalWeight += obj.weight;
            this._keys.push(obj);
            this._keyMap[obj.id] = obj;
            totalWeight += obj.weight;
        });
        // Normalize weights so that their sum is equal to 1
        this._keys.forEach((key)=>{
            key.weight /= totalWeight;
        });
    }
    get(keyId) {
        return this._keyMap[keyId];
    }
    keys() {
        return this._keys;
    }
    toJSON() {
        return JSON.stringify(this._keys);
    }
}
function createKey(key) {
    let path = null;
    let id = null;
    let src = null;
    let weight = 1;
    let getFn = null;
    if (isString(key) || isArray(key)) {
        src = key;
        path = createKeyPath(key);
        id = createKeyId(key);
    } else {
        if (!hasOwn.call(key, 'name')) {
            throw new Error(MISSING_KEY_PROPERTY('name'));
        }
        const name = key.name;
        src = name;
        if (hasOwn.call(key, 'weight')) {
            weight = key.weight;
            if (weight <= 0) {
                throw new Error(INVALID_KEY_WEIGHT_VALUE(name));
            }
        }
        path = createKeyPath(name);
        id = createKeyId(name);
        getFn = key.getFn;
    }
    return {
        path,
        id,
        weight,
        src,
        getFn
    };
}
function createKeyPath(key) {
    return isArray(key) ? key : key.split('.');
}
function createKeyId(key) {
    return isArray(key) ? key.join('.') : key;
}
function get(obj, path) {
    let list = [];
    let arr = false;
    const deepGet = (obj, path, index)=>{
        if (!isDefined(obj)) {
            return;
        }
        if (!path[index]) {
            // If there's no path left, we've arrived at the object we care about.
            list.push(obj);
        } else {
            let key = path[index];
            const value = obj[key];
            if (!isDefined(value)) {
                return;
            }
            // If we're at the last value in the path, and if it's a string/number/bool,
            // add it to the list
            if (index === path.length - 1 && (isString(value) || isNumber(value) || isBoolean(value))) {
                list.push(toString(value));
            } else if (isArray(value)) {
                arr = true;
                // Search each item in the array.
                for(let i = 0, len = value.length; i < len; i += 1){
                    deepGet(value[i], path, index + 1);
                }
            } else if (path.length) {
                // An object. Recurse further.
                deepGet(value, path, index + 1);
            }
        }
    };
    // Backwards compatibility (since path used to be a string)
    deepGet(obj, isString(path) ? path.split('.') : path, 0);
    return arr ? list : list[0];
}
const MatchOptions = {
    // Whether the matches should be included in the result set. When `true`, each record in the result
    // set will include the indices of the matched characters.
    // These can consequently be used for highlighting purposes.
    includeMatches: false,
    // When `true`, the matching function will continue to the end of a search pattern even if
    // a perfect match has already been located in the string.
    findAllMatches: false,
    // Minimum number of characters that must be matched before a result is considered a match
    minMatchCharLength: 1
};
const BasicOptions = {
    // When `true`, the algorithm continues searching to the end of the input even if a perfect
    // match is found before the end of the same input.
    isCaseSensitive: false,
    // When true, the matching function will continue to the end of a search pattern even if
    includeScore: false,
    // List of properties that will be searched. This also supports nested properties.
    keys: [],
    // Whether to sort the result list, by score
    shouldSort: true,
    // Default sort function: sort by ascending score, ascending index
    sortFn: (a, b)=>a.score === b.score ? a.idx < b.idx ? -1 : 1 : a.score < b.score ? -1 : 1
};
const FuzzyOptions = {
    // Approximately where in the text is the pattern expected to be found?
    location: 0,
    // At what point does the match algorithm give up. A threshold of '0.0' requires a perfect match
    // (of both letters and location), a threshold of '1.0' would match anything.
    threshold: 0.6,
    // Determines how close the match must be to the fuzzy location (specified above).
    // An exact letter match which is 'distance' characters away from the fuzzy location
    // would score as a complete mismatch. A distance of '0' requires the match be at
    // the exact location specified, a threshold of '1000' would require a perfect match
    // to be within 800 characters of the fuzzy location to be found using a 0.8 threshold.
    distance: 100
};
const AdvancedOptions = {
    // When `true`, it enables the use of unix-like search commands
    useExtendedSearch: false,
    // The get function to use when fetching an object's properties.
    // The default will search nested paths *ie foo.bar.baz*
    getFn: get,
    // When `true`, search will ignore `location` and `distance`, so it won't matter
    // where in the string the pattern appears.
    // More info: https://fusejs.io/concepts/scoring-theory.html#fuzziness-score
    ignoreLocation: false,
    // When `true`, the calculation for the relevance score (used for sorting) will
    // ignore the field-length norm.
    // More info: https://fusejs.io/concepts/scoring-theory.html#field-length-norm
    ignoreFieldNorm: false,
    // The weight to determine how much field length norm effects scoring.
    fieldNormWeight: 1
};
var Config = {
    ...BasicOptions,
    ...MatchOptions,
    ...FuzzyOptions,
    ...AdvancedOptions
};
const SPACE = /[^ ]+/g;
// Field-length norm: the shorter the field, the higher the weight.
// Set to 3 decimals to reduce index size.
function norm(weight = 1, mantissa = 3) {
    const cache = new Map();
    const m = Math.pow(10, mantissa);
    return {
        get (value) {
            const numTokens = value.match(SPACE).length;
            if (cache.has(numTokens)) {
                return cache.get(numTokens);
            }
            // Default function is 1/sqrt(x), weight makes that variable
            const norm = 1 / Math.pow(numTokens, 0.5 * weight);
            // In place of `toFixed(mantissa)`, for faster computation
            const n = parseFloat(Math.round(norm * m) / m);
            cache.set(numTokens, n);
            return n;
        },
        clear () {
            cache.clear();
        }
    };
}
class FuseIndex {
    constructor({ getFn = Config.getFn, fieldNormWeight = Config.fieldNormWeight } = {}){
        this.norm = norm(fieldNormWeight, 3);
        this.getFn = getFn;
        this.isCreated = false;
        this.setIndexRecords();
    }
    setSources(docs = []) {
        this.docs = docs;
    }
    setIndexRecords(records = []) {
        this.records = records;
    }
    setKeys(keys = []) {
        this.keys = keys;
        this._keysMap = {};
        keys.forEach((key, idx)=>{
            this._keysMap[key.id] = idx;
        });
    }
    create() {
        if (this.isCreated || !this.docs.length) {
            return;
        }
        this.isCreated = true;
        // List is Array<String>
        if (isString(this.docs[0])) {
            this.docs.forEach((doc, docIndex)=>{
                this._addString(doc, docIndex);
            });
        } else {
            // List is Array<Object>
            this.docs.forEach((doc, docIndex)=>{
                this._addObject(doc, docIndex);
            });
        }
        this.norm.clear();
    }
    // Adds a doc to the end of the index
    add(doc) {
        const idx = this.size();
        if (isString(doc)) {
            this._addString(doc, idx);
        } else {
            this._addObject(doc, idx);
        }
    }
    // Removes the doc at the specified index of the index
    removeAt(idx) {
        this.records.splice(idx, 1);
        // Change ref index of every subsquent doc
        for(let i = idx, len = this.size(); i < len; i += 1){
            this.records[i].i -= 1;
        }
    }
    getValueForItemAtKeyId(item, keyId) {
        return item[this._keysMap[keyId]];
    }
    size() {
        return this.records.length;
    }
    _addString(doc, docIndex) {
        if (!isDefined(doc) || isBlank(doc)) {
            return;
        }
        let record = {
            v: doc,
            i: docIndex,
            n: this.norm.get(doc)
        };
        this.records.push(record);
    }
    _addObject(doc, docIndex) {
        let record = {
            i: docIndex,
            $: {}
        };
        // Iterate over every key (i.e, path), and fetch the value at that key
        this.keys.forEach((key, keyIndex)=>{
            let value = key.getFn ? key.getFn(doc) : this.getFn(doc, key.path);
            if (!isDefined(value)) {
                return;
            }
            if (isArray(value)) {
                let subRecords = [];
                const stack = [
                    {
                        nestedArrIndex: -1,
                        value
                    }
                ];
                while(stack.length){
                    const { nestedArrIndex, value } = stack.pop();
                    if (!isDefined(value)) {
                        continue;
                    }
                    if (isString(value) && !isBlank(value)) {
                        let subRecord = {
                            v: value,
                            i: nestedArrIndex,
                            n: this.norm.get(value)
                        };
                        subRecords.push(subRecord);
                    } else if (isArray(value)) {
                        value.forEach((item, k)=>{
                            stack.push({
                                nestedArrIndex: k,
                                value: item
                            });
                        });
                    } else ;
                }
                record.$[keyIndex] = subRecords;
            } else if (isString(value) && !isBlank(value)) {
                let subRecord = {
                    v: value,
                    n: this.norm.get(value)
                };
                record.$[keyIndex] = subRecord;
            }
        });
        this.records.push(record);
    }
    toJSON() {
        return {
            keys: this.keys,
            records: this.records
        };
    }
}
function createIndex(keys, docs, { getFn = Config.getFn, fieldNormWeight = Config.fieldNormWeight } = {}) {
    const myIndex = new FuseIndex({
        getFn,
        fieldNormWeight
    });
    myIndex.setKeys(keys.map(createKey));
    myIndex.setSources(docs);
    myIndex.create();
    return myIndex;
}
function parseIndex(data, { getFn = Config.getFn, fieldNormWeight = Config.fieldNormWeight } = {}) {
    const { keys, records } = data;
    const myIndex = new FuseIndex({
        getFn,
        fieldNormWeight
    });
    myIndex.setKeys(keys);
    myIndex.setIndexRecords(records);
    return myIndex;
}
function computeScore$1(pattern, { errors = 0, currentLocation = 0, expectedLocation = 0, distance = Config.distance, ignoreLocation = Config.ignoreLocation } = {}) {
    const accuracy = errors / pattern.length;
    if (ignoreLocation) {
        return accuracy;
    }
    const proximity = Math.abs(expectedLocation - currentLocation);
    if (!distance) {
        // Dodge divide by zero error.
        return proximity ? 1.0 : accuracy;
    }
    return accuracy + proximity / distance;
}
function convertMaskToIndices(matchmask = [], minMatchCharLength = Config.minMatchCharLength) {
    let indices = [];
    let start = -1;
    let end = -1;
    let i = 0;
    for(let len = matchmask.length; i < len; i += 1){
        let match = matchmask[i];
        if (match && start === -1) {
            start = i;
        } else if (!match && start !== -1) {
            end = i - 1;
            if (end - start + 1 >= minMatchCharLength) {
                indices.push([
                    start,
                    end
                ]);
            }
            start = -1;
        }
    }
    // (i-1 - start) + 1 => i - start
    if (matchmask[i - 1] && i - start >= minMatchCharLength) {
        indices.push([
            start,
            i - 1
        ]);
    }
    return indices;
}
// Machine word size
const MAX_BITS = 32;
function search(text, pattern, patternAlphabet, { location = Config.location, distance = Config.distance, threshold = Config.threshold, findAllMatches = Config.findAllMatches, minMatchCharLength = Config.minMatchCharLength, includeMatches = Config.includeMatches, ignoreLocation = Config.ignoreLocation } = {}) {
    if (pattern.length > MAX_BITS) {
        throw new Error(PATTERN_LENGTH_TOO_LARGE(MAX_BITS));
    }
    const patternLen = pattern.length;
    // Set starting location at beginning text and initialize the alphabet.
    const textLen = text.length;
    // Handle the case when location > text.length
    const expectedLocation = Math.max(0, Math.min(location, textLen));
    // Highest score beyond which we give up.
    let currentThreshold = threshold;
    // Is there a nearby exact match? (speedup)
    let bestLocation = expectedLocation;
    // Performance: only computer matches when the minMatchCharLength > 1
    // OR if `includeMatches` is true.
    const computeMatches = minMatchCharLength > 1 || includeMatches;
    // A mask of the matches, used for building the indices
    const matchMask = computeMatches ? Array(textLen) : [];
    let index;
    // Get all exact matches, here for speed up
    while((index = text.indexOf(pattern, bestLocation)) > -1){
        let score = computeScore$1(pattern, {
            currentLocation: index,
            expectedLocation,
            distance,
            ignoreLocation
        });
        currentThreshold = Math.min(score, currentThreshold);
        bestLocation = index + patternLen;
        if (computeMatches) {
            let i = 0;
            while(i < patternLen){
                matchMask[index + i] = 1;
                i += 1;
            }
        }
    }
    // Reset the best location
    bestLocation = -1;
    let lastBitArr = [];
    let finalScore = 1;
    let binMax = patternLen + textLen;
    const mask = 1 << patternLen - 1;
    for(let i = 0; i < patternLen; i += 1){
        // Scan for the best match; each iteration allows for one more error.
        // Run a binary search to determine how far from the match location we can stray
        // at this error level.
        let binMin = 0;
        let binMid = binMax;
        while(binMin < binMid){
            const score = computeScore$1(pattern, {
                errors: i,
                currentLocation: expectedLocation + binMid,
                expectedLocation,
                distance,
                ignoreLocation
            });
            if (score <= currentThreshold) {
                binMin = binMid;
            } else {
                binMax = binMid;
            }
            binMid = Math.floor((binMax - binMin) / 2 + binMin);
        }
        // Use the result from this iteration as the maximum for the next.
        binMax = binMid;
        let start = Math.max(1, expectedLocation - binMid + 1);
        let finish = findAllMatches ? textLen : Math.min(expectedLocation + binMid, textLen) + patternLen;
        // Initialize the bit array
        let bitArr = Array(finish + 2);
        bitArr[finish + 1] = (1 << i) - 1;
        for(let j = finish; j >= start; j -= 1){
            let currentLocation = j - 1;
            let charMatch = patternAlphabet[text.charAt(currentLocation)];
            if (computeMatches) {
                // Speed up: quick bool to int conversion (i.e, `charMatch ? 1 : 0`)
                matchMask[currentLocation] = +!!charMatch;
            }
            // First pass: exact match
            bitArr[j] = (bitArr[j + 1] << 1 | 1) & charMatch;
            // Subsequent passes: fuzzy match
            if (i) {
                bitArr[j] |= (lastBitArr[j + 1] | lastBitArr[j]) << 1 | 1 | lastBitArr[j + 1];
            }
            if (bitArr[j] & mask) {
                finalScore = computeScore$1(pattern, {
                    errors: i,
                    currentLocation,
                    expectedLocation,
                    distance,
                    ignoreLocation
                });
                // This match will almost certainly be better than any existing match.
                // But check anyway.
                if (finalScore <= currentThreshold) {
                    // Indeed it is
                    currentThreshold = finalScore;
                    bestLocation = currentLocation;
                    // Already passed `loc`, downhill from here on in.
                    if (bestLocation <= expectedLocation) {
                        break;
                    }
                    // When passing `bestLocation`, don't exceed our current distance from `expectedLocation`.
                    start = Math.max(1, 2 * expectedLocation - bestLocation);
                }
            }
        }
        // No hope for a (better) match at greater error levels.
        const score = computeScore$1(pattern, {
            errors: i + 1,
            currentLocation: expectedLocation,
            expectedLocation,
            distance,
            ignoreLocation
        });
        if (score > currentThreshold) {
            break;
        }
        lastBitArr = bitArr;
    }
    const result = {
        isMatch: bestLocation >= 0,
        // Count exact matches (those with a score of 0) to be "almost" exact
        score: Math.max(0.001, finalScore)
    };
    if (computeMatches) {
        const indices = convertMaskToIndices(matchMask, minMatchCharLength);
        if (!indices.length) {
            result.isMatch = false;
        } else if (includeMatches) {
            result.indices = indices;
        }
    }
    return result;
}
function createPatternAlphabet(pattern) {
    let mask = {};
    for(let i = 0, len = pattern.length; i < len; i += 1){
        const char = pattern.charAt(i);
        mask[char] = (mask[char] || 0) | 1 << len - i - 1;
    }
    return mask;
}
class BitapSearch {
    constructor(pattern, { location = Config.location, threshold = Config.threshold, distance = Config.distance, includeMatches = Config.includeMatches, findAllMatches = Config.findAllMatches, minMatchCharLength = Config.minMatchCharLength, isCaseSensitive = Config.isCaseSensitive, ignoreLocation = Config.ignoreLocation } = {}){
        this.options = {
            location,
            threshold,
            distance,
            includeMatches,
            findAllMatches,
            minMatchCharLength,
            isCaseSensitive,
            ignoreLocation
        };
        this.pattern = isCaseSensitive ? pattern : pattern.toLowerCase();
        this.chunks = [];
        if (!this.pattern.length) {
            return;
        }
        const addChunk = (pattern, startIndex)=>{
            this.chunks.push({
                pattern,
                alphabet: createPatternAlphabet(pattern),
                startIndex
            });
        };
        const len = this.pattern.length;
        if (len > MAX_BITS) {
            let i = 0;
            const remainder = len % MAX_BITS;
            const end = len - remainder;
            while(i < end){
                addChunk(this.pattern.substr(i, MAX_BITS), i);
                i += MAX_BITS;
            }
            if (remainder) {
                const startIndex = len - MAX_BITS;
                addChunk(this.pattern.substr(startIndex), startIndex);
            }
        } else {
            addChunk(this.pattern, 0);
        }
    }
    searchIn(text) {
        const { isCaseSensitive, includeMatches } = this.options;
        if (!isCaseSensitive) {
            text = text.toLowerCase();
        }
        // Exact match
        if (this.pattern === text) {
            let result = {
                isMatch: true,
                score: 0
            };
            if (includeMatches) {
                result.indices = [
                    [
                        0,
                        text.length - 1
                    ]
                ];
            }
            return result;
        }
        // Otherwise, use Bitap algorithm
        const { location, distance, threshold, findAllMatches, minMatchCharLength, ignoreLocation } = this.options;
        let allIndices = [];
        let totalScore = 0;
        let hasMatches = false;
        this.chunks.forEach(({ pattern, alphabet, startIndex })=>{
            const { isMatch, score, indices } = search(text, pattern, alphabet, {
                location: location + startIndex,
                distance,
                threshold,
                findAllMatches,
                minMatchCharLength,
                includeMatches,
                ignoreLocation
            });
            if (isMatch) {
                hasMatches = true;
            }
            totalScore += score;
            if (isMatch && indices) {
                allIndices = [
                    ...allIndices,
                    ...indices
                ];
            }
        });
        let result = {
            isMatch: hasMatches,
            score: hasMatches ? totalScore / this.chunks.length : 1
        };
        if (hasMatches && includeMatches) {
            result.indices = allIndices;
        }
        return result;
    }
}
class BaseMatch {
    constructor(pattern){
        this.pattern = pattern;
    }
    static isMultiMatch(pattern) {
        return getMatch(pattern, this.multiRegex);
    }
    static isSingleMatch(pattern) {
        return getMatch(pattern, this.singleRegex);
    }
    search() {}
}
function getMatch(pattern, exp) {
    const matches = pattern.match(exp);
    return matches ? matches[1] : null;
}
// Token: 'file
class ExactMatch extends BaseMatch {
    constructor(pattern){
        super(pattern);
    }
    static get type() {
        return 'exact';
    }
    static get multiRegex() {
        return /^="(.*)"$/;
    }
    static get singleRegex() {
        return /^=(.*)$/;
    }
    search(text) {
        const isMatch = text === this.pattern;
        return {
            isMatch,
            score: isMatch ? 0 : 1,
            indices: [
                0,
                this.pattern.length - 1
            ]
        };
    }
}
// Token: !fire
class InverseExactMatch extends BaseMatch {
    constructor(pattern){
        super(pattern);
    }
    static get type() {
        return 'inverse-exact';
    }
    static get multiRegex() {
        return /^!"(.*)"$/;
    }
    static get singleRegex() {
        return /^!(.*)$/;
    }
    search(text) {
        const index = text.indexOf(this.pattern);
        const isMatch = index === -1;
        return {
            isMatch,
            score: isMatch ? 0 : 1,
            indices: [
                0,
                text.length - 1
            ]
        };
    }
}
// Token: ^file
class PrefixExactMatch extends BaseMatch {
    constructor(pattern){
        super(pattern);
    }
    static get type() {
        return 'prefix-exact';
    }
    static get multiRegex() {
        return /^\^"(.*)"$/;
    }
    static get singleRegex() {
        return /^\^(.*)$/;
    }
    search(text) {
        const isMatch = text.startsWith(this.pattern);
        return {
            isMatch,
            score: isMatch ? 0 : 1,
            indices: [
                0,
                this.pattern.length - 1
            ]
        };
    }
}
// Token: !^fire
class InversePrefixExactMatch extends BaseMatch {
    constructor(pattern){
        super(pattern);
    }
    static get type() {
        return 'inverse-prefix-exact';
    }
    static get multiRegex() {
        return /^!\^"(.*)"$/;
    }
    static get singleRegex() {
        return /^!\^(.*)$/;
    }
    search(text) {
        const isMatch = !text.startsWith(this.pattern);
        return {
            isMatch,
            score: isMatch ? 0 : 1,
            indices: [
                0,
                text.length - 1
            ]
        };
    }
}
// Token: .file$
class SuffixExactMatch extends BaseMatch {
    constructor(pattern){
        super(pattern);
    }
    static get type() {
        return 'suffix-exact';
    }
    static get multiRegex() {
        return /^"(.*)"\$$/;
    }
    static get singleRegex() {
        return /^(.*)\$$/;
    }
    search(text) {
        const isMatch = text.endsWith(this.pattern);
        return {
            isMatch,
            score: isMatch ? 0 : 1,
            indices: [
                text.length - this.pattern.length,
                text.length - 1
            ]
        };
    }
}
// Token: !.file$
class InverseSuffixExactMatch extends BaseMatch {
    constructor(pattern){
        super(pattern);
    }
    static get type() {
        return 'inverse-suffix-exact';
    }
    static get multiRegex() {
        return /^!"(.*)"\$$/;
    }
    static get singleRegex() {
        return /^!(.*)\$$/;
    }
    search(text) {
        const isMatch = !text.endsWith(this.pattern);
        return {
            isMatch,
            score: isMatch ? 0 : 1,
            indices: [
                0,
                text.length - 1
            ]
        };
    }
}
class FuzzyMatch extends BaseMatch {
    constructor(pattern, { location = Config.location, threshold = Config.threshold, distance = Config.distance, includeMatches = Config.includeMatches, findAllMatches = Config.findAllMatches, minMatchCharLength = Config.minMatchCharLength, isCaseSensitive = Config.isCaseSensitive, ignoreLocation = Config.ignoreLocation } = {}){
        super(pattern);
        this._bitapSearch = new BitapSearch(pattern, {
            location,
            threshold,
            distance,
            includeMatches,
            findAllMatches,
            minMatchCharLength,
            isCaseSensitive,
            ignoreLocation
        });
    }
    static get type() {
        return 'fuzzy';
    }
    static get multiRegex() {
        return /^"(.*)"$/;
    }
    static get singleRegex() {
        return /^(.*)$/;
    }
    search(text) {
        return this._bitapSearch.searchIn(text);
    }
}
// Token: 'file
class IncludeMatch extends BaseMatch {
    constructor(pattern){
        super(pattern);
    }
    static get type() {
        return 'include';
    }
    static get multiRegex() {
        return /^'"(.*)"$/;
    }
    static get singleRegex() {
        return /^'(.*)$/;
    }
    search(text) {
        let location = 0;
        let index;
        const indices = [];
        const patternLen = this.pattern.length;
        // Get all exact matches
        while((index = text.indexOf(this.pattern, location)) > -1){
            location = index + patternLen;
            indices.push([
                index,
                location - 1
            ]);
        }
        const isMatch = !!indices.length;
        return {
            isMatch,
            score: isMatch ? 0 : 1,
            indices
        };
    }
}
// ❗Order is important. DO NOT CHANGE.
const searchers = [
    ExactMatch,
    IncludeMatch,
    PrefixExactMatch,
    InversePrefixExactMatch,
    InverseSuffixExactMatch,
    SuffixExactMatch,
    InverseExactMatch,
    FuzzyMatch
];
const searchersLen = searchers.length;
// Regex to split by spaces, but keep anything in quotes together
const SPACE_RE = / +(?=(?:[^\"]*\"[^\"]*\")*[^\"]*$)/;
const OR_TOKEN = '|';
// Return a 2D array representation of the query, for simpler parsing.
// Example:
// "^core go$ | rb$ | py$ xy$" => [["^core", "go$"], ["rb$"], ["py$", "xy$"]]
function parseQuery(pattern, options = {}) {
    return pattern.split(OR_TOKEN).map((item)=>{
        let query = item.trim().split(SPACE_RE).filter((item)=>item && !!item.trim());
        let results = [];
        for(let i = 0, len = query.length; i < len; i += 1){
            const queryItem = query[i];
            // 1. Handle multiple query match (i.e, once that are quoted, like `"hello world"`)
            let found = false;
            let idx = -1;
            while(!found && ++idx < searchersLen){
                const searcher = searchers[idx];
                let token = searcher.isMultiMatch(queryItem);
                if (token) {
                    results.push(new searcher(token, options));
                    found = true;
                }
            }
            if (found) {
                continue;
            }
            // 2. Handle single query matches (i.e, once that are *not* quoted)
            idx = -1;
            while(++idx < searchersLen){
                const searcher = searchers[idx];
                let token = searcher.isSingleMatch(queryItem);
                if (token) {
                    results.push(new searcher(token, options));
                    break;
                }
            }
        }
        return results;
    });
}
// These extended matchers can return an array of matches, as opposed
// to a singl match
const MultiMatchSet = new Set([
    FuzzyMatch.type,
    IncludeMatch.type
]);
/**
 * Command-like searching
 * ======================
 *
 * Given multiple search terms delimited by spaces.e.g. `^jscript .python$ ruby !java`,
 * search in a given text.
 *
 * Search syntax:
 *
 * | Token       | Match type                 | Description                            |
 * | ----------- | -------------------------- | -------------------------------------- |
 * | `jscript`   | fuzzy-match                | Items that fuzzy match `jscript`       |
 * | `=scheme`   | exact-match                | Items that are `scheme`                |
 * | `'python`   | include-match              | Items that include `python`            |
 * | `!ruby`     | inverse-exact-match        | Items that do not include `ruby`       |
 * | `^java`     | prefix-exact-match         | Items that start with `java`           |
 * | `!^earlang` | inverse-prefix-exact-match | Items that do not start with `earlang` |
 * | `.js$`      | suffix-exact-match         | Items that end with `.js`              |
 * | `!.go$`     | inverse-suffix-exact-match | Items that do not end with `.go`       |
 *
 * A single pipe character acts as an OR operator. For example, the following
 * query matches entries that start with `core` and end with either`go`, `rb`,
 * or`py`.
 *
 * ```
 * ^core go$ | rb$ | py$
 * ```
 */ class ExtendedSearch {
    constructor(pattern, { isCaseSensitive = Config.isCaseSensitive, includeMatches = Config.includeMatches, minMatchCharLength = Config.minMatchCharLength, ignoreLocation = Config.ignoreLocation, findAllMatches = Config.findAllMatches, location = Config.location, threshold = Config.threshold, distance = Config.distance } = {}){
        this.query = null;
        this.options = {
            isCaseSensitive,
            includeMatches,
            minMatchCharLength,
            findAllMatches,
            ignoreLocation,
            location,
            threshold,
            distance
        };
        this.pattern = isCaseSensitive ? pattern : pattern.toLowerCase();
        this.query = parseQuery(this.pattern, this.options);
    }
    static condition(_, options) {
        return options.useExtendedSearch;
    }
    searchIn(text) {
        const query = this.query;
        if (!query) {
            return {
                isMatch: false,
                score: 1
            };
        }
        const { includeMatches, isCaseSensitive } = this.options;
        text = isCaseSensitive ? text : text.toLowerCase();
        let numMatches = 0;
        let allIndices = [];
        let totalScore = 0;
        // ORs
        for(let i = 0, qLen = query.length; i < qLen; i += 1){
            const searchers = query[i];
            // Reset indices
            allIndices.length = 0;
            numMatches = 0;
            // ANDs
            for(let j = 0, pLen = searchers.length; j < pLen; j += 1){
                const searcher = searchers[j];
                const { isMatch, indices, score } = searcher.search(text);
                if (isMatch) {
                    numMatches += 1;
                    totalScore += score;
                    if (includeMatches) {
                        const type = searcher.constructor.type;
                        if (MultiMatchSet.has(type)) {
                            allIndices = [
                                ...allIndices,
                                ...indices
                            ];
                        } else {
                            allIndices.push(indices);
                        }
                    }
                } else {
                    totalScore = 0;
                    numMatches = 0;
                    allIndices.length = 0;
                    break;
                }
            }
            // OR condition, so if TRUE, return
            if (numMatches) {
                let result = {
                    isMatch: true,
                    score: totalScore / numMatches
                };
                if (includeMatches) {
                    result.indices = allIndices;
                }
                return result;
            }
        }
        // Nothing was matched
        return {
            isMatch: false,
            score: 1
        };
    }
}
const registeredSearchers = [];
function register(...args) {
    registeredSearchers.push(...args);
}
function createSearcher(pattern, options) {
    for(let i = 0, len = registeredSearchers.length; i < len; i += 1){
        let searcherClass = registeredSearchers[i];
        if (searcherClass.condition(pattern, options)) {
            return new searcherClass(pattern, options);
        }
    }
    return new BitapSearch(pattern, options);
}
const LogicalOperator = {
    AND: '$and',
    OR: '$or'
};
const KeyType = {
    PATH: '$path',
    PATTERN: '$val'
};
const isExpression = (query)=>!!(query[LogicalOperator.AND] || query[LogicalOperator.OR]);
const isPath = (query)=>!!query[KeyType.PATH];
const isLeaf = (query)=>!isArray(query) && isObject(query) && !isExpression(query);
const convertToExplicit = (query)=>({
        [LogicalOperator.AND]: Object.keys(query).map((key)=>({
                [key]: query[key]
            }))
    });
// When `auto` is `true`, the parse function will infer and initialize and add
// the appropriate `Searcher` instance
function parse(query, options, { auto = true } = {}) {
    const next = (query)=>{
        let keys = Object.keys(query);
        const isQueryPath = isPath(query);
        if (!isQueryPath && keys.length > 1 && !isExpression(query)) {
            return next(convertToExplicit(query));
        }
        if (isLeaf(query)) {
            const key = isQueryPath ? query[KeyType.PATH] : keys[0];
            const pattern = isQueryPath ? query[KeyType.PATTERN] : query[key];
            if (!isString(pattern)) {
                throw new Error(LOGICAL_SEARCH_INVALID_QUERY_FOR_KEY(key));
            }
            const obj = {
                keyId: createKeyId(key),
                pattern
            };
            if (auto) {
                obj.searcher = createSearcher(pattern, options);
            }
            return obj;
        }
        let node = {
            children: [],
            operator: keys[0]
        };
        keys.forEach((key)=>{
            const value = query[key];
            if (isArray(value)) {
                value.forEach((item)=>{
                    node.children.push(next(item));
                });
            }
        });
        return node;
    };
    if (!isExpression(query)) {
        query = convertToExplicit(query);
    }
    return next(query);
}
// Practical scoring function
function computeScore(results, { ignoreFieldNorm = Config.ignoreFieldNorm }) {
    results.forEach((result)=>{
        let totalScore = 1;
        result.matches.forEach(({ key, norm, score })=>{
            const weight = key ? key.weight : null;
            totalScore *= Math.pow(score === 0 && weight ? Number.EPSILON : score, (weight || 1) * (ignoreFieldNorm ? 1 : norm));
        });
        result.score = totalScore;
    });
}
function transformMatches(result, data) {
    const matches = result.matches;
    data.matches = [];
    if (!isDefined(matches)) {
        return;
    }
    matches.forEach((match)=>{
        if (!isDefined(match.indices) || !match.indices.length) {
            return;
        }
        const { indices, value } = match;
        let obj = {
            indices,
            value
        };
        if (match.key) {
            obj.key = match.key.src;
        }
        if (match.idx > -1) {
            obj.refIndex = match.idx;
        }
        data.matches.push(obj);
    });
}
function transformScore(result, data) {
    data.score = result.score;
}
function format(results, docs, { includeMatches = Config.includeMatches, includeScore = Config.includeScore } = {}) {
    const transformers = [];
    if (includeMatches) transformers.push(transformMatches);
    if (includeScore) transformers.push(transformScore);
    return results.map((result)=>{
        const { idx } = result;
        const data = {
            item: docs[idx],
            refIndex: idx
        };
        if (transformers.length) {
            transformers.forEach((transformer)=>{
                transformer(result, data);
            });
        }
        return data;
    });
}
class Fuse {
    constructor(docs, options = {}, index){
        this.options = {
            ...Config,
            ...options
        };
        if (this.options.useExtendedSearch && !true) {
            "TURBOPACK unreachable";
        }
        this._keyStore = new KeyStore(this.options.keys);
        this.setCollection(docs, index);
    }
    setCollection(docs, index) {
        this._docs = docs;
        if (index && !(index instanceof FuseIndex)) {
            throw new Error(INCORRECT_INDEX_TYPE);
        }
        this._myIndex = index || createIndex(this.options.keys, this._docs, {
            getFn: this.options.getFn,
            fieldNormWeight: this.options.fieldNormWeight
        });
    }
    add(doc) {
        if (!isDefined(doc)) {
            return;
        }
        this._docs.push(doc);
        this._myIndex.add(doc);
    }
    remove(predicate = ()=>false) {
        const results = [];
        for(let i = 0, len = this._docs.length; i < len; i += 1){
            const doc = this._docs[i];
            if (predicate(doc, i)) {
                this.removeAt(i);
                i -= 1;
                len -= 1;
                results.push(doc);
            }
        }
        return results;
    }
    removeAt(idx) {
        this._docs.splice(idx, 1);
        this._myIndex.removeAt(idx);
    }
    getIndex() {
        return this._myIndex;
    }
    search(query, { limit = -1 } = {}) {
        const { includeMatches, includeScore, shouldSort, sortFn, ignoreFieldNorm } = this.options;
        let results = isString(query) ? isString(this._docs[0]) ? this._searchStringList(query) : this._searchObjectList(query) : this._searchLogical(query);
        computeScore(results, {
            ignoreFieldNorm
        });
        if (shouldSort) {
            results.sort(sortFn);
        }
        if (isNumber(limit) && limit > -1) {
            results = results.slice(0, limit);
        }
        return format(results, this._docs, {
            includeMatches,
            includeScore
        });
    }
    _searchStringList(query) {
        const searcher = createSearcher(query, this.options);
        const { records } = this._myIndex;
        const results = [];
        // Iterate over every string in the index
        records.forEach(({ v: text, i: idx, n: norm })=>{
            if (!isDefined(text)) {
                return;
            }
            const { isMatch, score, indices } = searcher.searchIn(text);
            if (isMatch) {
                results.push({
                    item: text,
                    idx,
                    matches: [
                        {
                            score,
                            value: text,
                            norm,
                            indices
                        }
                    ]
                });
            }
        });
        return results;
    }
    _searchLogical(query) {
        const expression = parse(query, this.options);
        const evaluate = (node, item, idx)=>{
            if (!node.children) {
                const { keyId, searcher } = node;
                const matches = this._findMatches({
                    key: this._keyStore.get(keyId),
                    value: this._myIndex.getValueForItemAtKeyId(item, keyId),
                    searcher
                });
                if (matches && matches.length) {
                    return [
                        {
                            idx,
                            item,
                            matches
                        }
                    ];
                }
                return [];
            }
            const res = [];
            for(let i = 0, len = node.children.length; i < len; i += 1){
                const child = node.children[i];
                const result = evaluate(child, item, idx);
                if (result.length) {
                    res.push(...result);
                } else if (node.operator === LogicalOperator.AND) {
                    return [];
                }
            }
            return res;
        };
        const records = this._myIndex.records;
        const resultMap = {};
        const results = [];
        records.forEach(({ $: item, i: idx })=>{
            if (isDefined(item)) {
                let expResults = evaluate(expression, item, idx);
                if (expResults.length) {
                    // Dedupe when adding
                    if (!resultMap[idx]) {
                        resultMap[idx] = {
                            idx,
                            item,
                            matches: []
                        };
                        results.push(resultMap[idx]);
                    }
                    expResults.forEach(({ matches })=>{
                        resultMap[idx].matches.push(...matches);
                    });
                }
            }
        });
        return results;
    }
    _searchObjectList(query) {
        const searcher = createSearcher(query, this.options);
        const { keys, records } = this._myIndex;
        const results = [];
        // List is Array<Object>
        records.forEach(({ $: item, i: idx })=>{
            if (!isDefined(item)) {
                return;
            }
            let matches = [];
            // Iterate over every key (i.e, path), and fetch the value at that key
            keys.forEach((key, keyIndex)=>{
                matches.push(...this._findMatches({
                    key,
                    value: item[keyIndex],
                    searcher
                }));
            });
            if (matches.length) {
                results.push({
                    idx,
                    item,
                    matches
                });
            }
        });
        return results;
    }
    _findMatches({ key, value, searcher }) {
        if (!isDefined(value)) {
            return [];
        }
        let matches = [];
        if (isArray(value)) {
            value.forEach(({ v: text, i: idx, n: norm })=>{
                if (!isDefined(text)) {
                    return;
                }
                const { isMatch, score, indices } = searcher.searchIn(text);
                if (isMatch) {
                    matches.push({
                        score,
                        key,
                        value: text,
                        idx,
                        norm,
                        indices
                    });
                }
            });
        } else {
            const { v: text, n: norm } = value;
            const { isMatch, score, indices } = searcher.searchIn(text);
            if (isMatch) {
                matches.push({
                    score,
                    key,
                    value: text,
                    norm,
                    indices
                });
            }
        }
        return matches;
    }
}
Fuse.version = '6.6.2';
Fuse.createIndex = createIndex;
Fuse.parseIndex = parseIndex;
Fuse.config = Config;
{
    Fuse.parseQuery = parse;
}{
    register(ExtendedSearch);
};
}}),
"[project]/node_modules/kbar/lib/useMatches.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __createBinding = this && this.__createBinding || (Object.create ? function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, {
        enumerable: true,
        get: function() {
            return m[k];
        }
    });
} : function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
});
var __setModuleDefault = this && this.__setModuleDefault || (Object.create ? function(o, v) {
    Object.defineProperty(o, "default", {
        enumerable: true,
        value: v
    });
} : function(o, v) {
    o["default"] = v;
});
var __importStar = this && this.__importStar || function(mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) {
        for(var k in mod)if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    }
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = this && this.__importDefault || function(mod) {
    return mod && mod.__esModule ? mod : {
        "default": mod
    };
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.useDeepMatches = exports.useMatches = exports.NO_GROUP = void 0;
var React = __importStar(__turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)"));
var useKBar_1 = __turbopack_context__.r("[project]/node_modules/kbar/lib/useKBar.js [app-ssr] (ecmascript)");
var utils_1 = __turbopack_context__.r("[project]/node_modules/kbar/lib/utils.js [app-ssr] (ecmascript)");
var fuse_js_1 = __importDefault(__turbopack_context__.r("[project]/node_modules/fuse.js/dist/fuse.esm.js [app-ssr] (ecmascript)"));
exports.NO_GROUP = {
    name: "none",
    priority: utils_1.Priority.NORMAL
};
var fuseOptions = {
    keys: [
        {
            name: "name",
            weight: 0.5
        },
        {
            name: "keywords",
            getFn: function(item) {
                var _a;
                return ((_a = item.keywords) !== null && _a !== void 0 ? _a : "").split(",");
            },
            weight: 0.5
        },
        "subtitle"
    ],
    ignoreLocation: true,
    includeScore: true,
    includeMatches: true,
    threshold: 0.2,
    minMatchCharLength: 1
};
function order(a, b) {
    /**
     * Larger the priority = higher up the list
     */ return b.priority - a.priority;
}
/**
 * returns deep matches only when a search query is present
 */ function useMatches() {
    var _a = (0, useKBar_1.useKBar)(function(state) {
        return {
            search: state.searchQuery,
            actions: state.actions,
            rootActionId: state.currentRootActionId
        };
    }), search = _a.search, actions = _a.actions, rootActionId = _a.rootActionId;
    var rootResults = React.useMemo(function() {
        return Object.keys(actions).reduce(function(acc, actionId) {
            var action = actions[actionId];
            if (!action.parent && !rootActionId) {
                acc.push(action);
            }
            if (action.id === rootActionId) {
                for(var i = 0; i < action.children.length; i++){
                    acc.push(action.children[i]);
                }
            }
            return acc;
        }, []).sort(order);
    }, [
        actions,
        rootActionId
    ]);
    var getDeepResults = React.useCallback(function(actions) {
        var actionsClone = [];
        for(var i = 0; i < actions.length; i++){
            actionsClone.push(actions[i]);
        }
        return function collectChildren(actions, all) {
            if (all === void 0) {
                all = actionsClone;
            }
            for(var i = 0; i < actions.length; i++){
                if (actions[i].children.length > 0) {
                    var childsChildren = actions[i].children;
                    for(var i_1 = 0; i_1 < childsChildren.length; i_1++){
                        all.push(childsChildren[i_1]);
                    }
                    collectChildren(actions[i].children, all);
                }
            }
            return all;
        }(actions);
    }, []);
    var emptySearch = !search;
    var filtered = React.useMemo(function() {
        if (emptySearch) return rootResults;
        return getDeepResults(rootResults);
    }, [
        getDeepResults,
        rootResults,
        emptySearch
    ]);
    var fuse = React.useMemo(function() {
        return new fuse_js_1.default(filtered, fuseOptions);
    }, [
        filtered
    ]);
    var matches = useInternalMatches(filtered, search, fuse);
    var results = React.useMemo(function() {
        var _a, _b;
        /**
         * Store a reference to a section and it's list of actions.
         * Alongside these actions, we'll keep a temporary record of the
         * final priority calculated by taking the commandScore + the
         * explicitly set `action.priority` value.
         */ var map = {};
        /**
         * Store another reference to a list of sections alongside
         * the section's final priority, calculated the same as above.
         */ var list = [];
        /**
         * We'll take the list above and sort by its priority. Then we'll
         * collect all actions from the map above for this specific name and
         * sort by its priority as well.
         */ var ordered = [];
        for(var i = 0; i < matches.length; i++){
            var match = matches[i];
            var action = match.action;
            var score = match.score || utils_1.Priority.NORMAL;
            var section = {
                name: typeof action.section === "string" ? action.section : ((_a = action.section) === null || _a === void 0 ? void 0 : _a.name) || exports.NO_GROUP.name,
                priority: typeof action.section === "string" ? score : ((_b = action.section) === null || _b === void 0 ? void 0 : _b.priority) || 0 + score
            };
            if (!map[section.name]) {
                map[section.name] = [];
                list.push(section);
            }
            map[section.name].push({
                priority: action.priority + score,
                action: action
            });
        }
        ordered = list.sort(order).map(function(group) {
            return {
                name: group.name,
                actions: map[group.name].sort(order).map(function(item) {
                    return item.action;
                })
            };
        });
        /**
         * Our final result is simply flattening the ordered list into
         * our familiar (ActionImpl | string)[] shape.
         */ var results = [];
        for(var i = 0; i < ordered.length; i++){
            var group = ordered[i];
            if (group.name !== exports.NO_GROUP.name) results.push(group.name);
            for(var i_2 = 0; i_2 < group.actions.length; i_2++){
                results.push(group.actions[i_2]);
            }
        }
        return results;
    }, [
        matches
    ]);
    // ensure that users have an accurate `currentRootActionId`
    // that syncs with the throttled return value.
    // eslint-disable-next-line react-hooks/exhaustive-deps
    var memoRootActionId = React.useMemo(function() {
        return rootActionId;
    }, [
        results
    ]);
    return React.useMemo(function() {
        return {
            results: results,
            rootActionId: memoRootActionId
        };
    }, [
        memoRootActionId,
        results
    ]);
}
exports.useMatches = useMatches;
function useInternalMatches(filtered, search, fuse) {
    var value = React.useMemo(function() {
        return {
            filtered: filtered,
            search: search
        };
    }, [
        filtered,
        search
    ]);
    var _a = (0, utils_1.useThrottledValue)(value), throttledFiltered = _a.filtered, throttledSearch = _a.search;
    return React.useMemo(function() {
        if (throttledSearch.trim() === "") {
            return throttledFiltered.map(function(action) {
                return {
                    score: 0,
                    action: action
                };
            });
        }
        var matches = [];
        // Use Fuse's `search` method to perform the search efficiently
        var searchResults = fuse.search(throttledSearch);
        // Format the search results to match the existing structure
        matches = searchResults.map(function(_a) {
            var action = _a.item, score = _a.score;
            return {
                score: 1 / ((score !== null && score !== void 0 ? score : 0) + 1),
                action: action
            };
        });
        return matches;
    }, [
        throttledFiltered,
        throttledSearch,
        fuse
    ]);
}
/**
 * @deprecated use useMatches
 */ exports.useDeepMatches = useMatches;
}}),
"[project]/node_modules/@radix-ui/react-compose-refs/dist/index.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toESM = (mod, isNodeMode, target)=>(target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(// If the importer is in node compatibility mode or this is not an ESM
    // file that has been converted to a CommonJS file using a Babel-
    // compatible transform (i.e. "__esModule" has not been set), then set
    // "default" to the CommonJS "module.exports" for node compatibility.
    isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", {
        value: mod,
        enumerable: true
    }) : target, mod));
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
// packages/react/compose-refs/src/index.ts
var index_exports = {};
__export(index_exports, {
    composeRefs: ()=>composeRefs,
    useComposedRefs: ()=>useComposedRefs
});
module.exports = __toCommonJS(index_exports);
// packages/react/compose-refs/src/compose-refs.tsx
var React = __toESM(__turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)"));
function setRef(ref, value) {
    if (typeof ref === "function") {
        return ref(value);
    } else if (ref !== null && ref !== void 0) {
        ref.current = value;
    }
}
function composeRefs(...refs) {
    return (node)=>{
        let hasCleanup = false;
        const cleanups = refs.map((ref)=>{
            const cleanup = setRef(ref, node);
            if (!hasCleanup && typeof cleanup == "function") {
                hasCleanup = true;
            }
            return cleanup;
        });
        if (hasCleanup) {
            return ()=>{
                for(let i = 0; i < cleanups.length; i++){
                    const cleanup = cleanups[i];
                    if (typeof cleanup == "function") {
                        cleanup();
                    } else {
                        setRef(refs[i], null);
                    }
                }
            };
        }
    };
}
function useComposedRefs(...refs) {
    return React.useCallback(composeRefs(...refs), refs);
} //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)").vendored['react-ssr'].ReactJsxRuntime; //# sourceMappingURL=react-jsx-runtime.js.map
}}),
"[project]/node_modules/@radix-ui/react-slot/dist/index.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toESM = (mod, isNodeMode, target)=>(target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(// If the importer is in node compatibility mode or this is not an ESM
    // file that has been converted to a CommonJS file using a Babel-
    // compatible transform (i.e. "__esModule" has not been set), then set
    // "default" to the CommonJS "module.exports" for node compatibility.
    isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", {
        value: mod,
        enumerable: true
    }) : target, mod));
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
// src/index.ts
var index_exports = {};
__export(index_exports, {
    Root: ()=>Slot,
    Slot: ()=>Slot,
    Slottable: ()=>Slottable,
    createSlot: ()=>createSlot,
    createSlottable: ()=>createSlottable
});
module.exports = __toCommonJS(index_exports);
// src/slot.tsx
var React = __toESM(__turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)"));
var import_react_compose_refs = __turbopack_context__.r("[project]/node_modules/@radix-ui/react-compose-refs/dist/index.js [app-ssr] (ecmascript)");
var import_jsx_runtime = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js [app-ssr] (ecmascript)");
// @__NO_SIDE_EFFECTS__
function createSlot(ownerName) {
    const SlotClone = /* @__PURE__ */ createSlotClone(ownerName);
    const Slot2 = React.forwardRef((props, forwardedRef)=>{
        const { children, ...slotProps } = props;
        const childrenArray = React.Children.toArray(children);
        const slottable = childrenArray.find(isSlottable);
        if (slottable) {
            const newElement = slottable.props.children;
            const newChildren = childrenArray.map((child)=>{
                if (child === slottable) {
                    if (React.Children.count(newElement) > 1) return React.Children.only(null);
                    return React.isValidElement(newElement) ? newElement.props.children : null;
                } else {
                    return child;
                }
            });
            return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(SlotClone, {
                ...slotProps,
                ref: forwardedRef,
                children: React.isValidElement(newElement) ? React.cloneElement(newElement, void 0, newChildren) : null
            });
        }
        return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(SlotClone, {
            ...slotProps,
            ref: forwardedRef,
            children
        });
    });
    Slot2.displayName = `${ownerName}.Slot`;
    return Slot2;
}
var Slot = /* @__PURE__ */ createSlot("Slot");
// @__NO_SIDE_EFFECTS__
function createSlotClone(ownerName) {
    const SlotClone = React.forwardRef((props, forwardedRef)=>{
        const { children, ...slotProps } = props;
        if (React.isValidElement(children)) {
            const childrenRef = getElementRef(children);
            const props2 = mergeProps(slotProps, children.props);
            if (children.type !== React.Fragment) {
                props2.ref = forwardedRef ? (0, import_react_compose_refs.composeRefs)(forwardedRef, childrenRef) : childrenRef;
            }
            return React.cloneElement(children, props2);
        }
        return React.Children.count(children) > 1 ? React.Children.only(null) : null;
    });
    SlotClone.displayName = `${ownerName}.SlotClone`;
    return SlotClone;
}
var SLOTTABLE_IDENTIFIER = Symbol("radix.slottable");
// @__NO_SIDE_EFFECTS__
function createSlottable(ownerName) {
    const Slottable2 = ({ children })=>{
        return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(import_jsx_runtime.Fragment, {
            children
        });
    };
    Slottable2.displayName = `${ownerName}.Slottable`;
    Slottable2.__radixId = SLOTTABLE_IDENTIFIER;
    return Slottable2;
}
var Slottable = /* @__PURE__ */ createSlottable("Slottable");
function isSlottable(child) {
    return React.isValidElement(child) && typeof child.type === "function" && "__radixId" in child.type && child.type.__radixId === SLOTTABLE_IDENTIFIER;
}
function mergeProps(slotProps, childProps) {
    const overrideProps = {
        ...childProps
    };
    for(const propName in childProps){
        const slotPropValue = slotProps[propName];
        const childPropValue = childProps[propName];
        const isHandler = /^on[A-Z]/.test(propName);
        if (isHandler) {
            if (slotPropValue && childPropValue) {
                overrideProps[propName] = (...args)=>{
                    const result = childPropValue(...args);
                    slotPropValue(...args);
                    return result;
                };
            } else if (slotPropValue) {
                overrideProps[propName] = slotPropValue;
            }
        } else if (propName === "style") {
            overrideProps[propName] = {
                ...slotPropValue,
                ...childPropValue
            };
        } else if (propName === "className") {
            overrideProps[propName] = [
                slotPropValue,
                childPropValue
            ].filter(Boolean).join(" ");
        }
    }
    return {
        ...slotProps,
        ...overrideProps
    };
}
function getElementRef(element) {
    let getter = Object.getOwnPropertyDescriptor(element.props, "ref")?.get;
    let mayWarn = getter && "isReactWarning" in getter && getter.isReactWarning;
    if (mayWarn) {
        return element.ref;
    }
    getter = Object.getOwnPropertyDescriptor(element, "ref")?.get;
    mayWarn = getter && "isReactWarning" in getter && getter.isReactWarning;
    if (mayWarn) {
        return element.props.ref;
    }
    return element.props.ref || element.ref;
} //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@radix-ui/react-primitive/dist/index.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toESM = (mod, isNodeMode, target)=>(target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(// If the importer is in node compatibility mode or this is not an ESM
    // file that has been converted to a CommonJS file using a Babel-
    // compatible transform (i.e. "__esModule" has not been set), then set
    // "default" to the CommonJS "module.exports" for node compatibility.
    isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", {
        value: mod,
        enumerable: true
    }) : target, mod));
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
// src/index.ts
var index_exports = {};
__export(index_exports, {
    Primitive: ()=>Primitive,
    Root: ()=>Root,
    dispatchDiscreteCustomEvent: ()=>dispatchDiscreteCustomEvent
});
module.exports = __toCommonJS(index_exports);
// src/primitive.tsx
var React = __toESM(__turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)"));
var ReactDOM = __toESM(__turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js [app-ssr] (ecmascript)"));
var import_react_slot = __turbopack_context__.r("[project]/node_modules/@radix-ui/react-slot/dist/index.js [app-ssr] (ecmascript)");
var import_jsx_runtime = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js [app-ssr] (ecmascript)");
var NODES = [
    "a",
    "button",
    "div",
    "form",
    "h2",
    "h3",
    "img",
    "input",
    "label",
    "li",
    "nav",
    "ol",
    "p",
    "select",
    "span",
    "svg",
    "ul"
];
var Primitive = NODES.reduce((primitive, node)=>{
    const Slot = (0, import_react_slot.createSlot)(`Primitive.${node}`);
    const Node = React.forwardRef((props, forwardedRef)=>{
        const { asChild, ...primitiveProps } = props;
        const Comp = asChild ? Slot : node;
        if (typeof window !== "undefined") {
            window[Symbol.for("radix-ui")] = true;
        }
        return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(Comp, {
            ...primitiveProps,
            ref: forwardedRef
        });
    });
    Node.displayName = `Primitive.${node}`;
    return {
        ...primitive,
        [node]: Node
    };
}, {});
function dispatchDiscreteCustomEvent(target, event) {
    if (target) ReactDOM.flushSync(()=>target.dispatchEvent(event));
}
var Root = Primitive; //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@radix-ui/react-use-layout-effect/dist/index.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toESM = (mod, isNodeMode, target)=>(target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(// If the importer is in node compatibility mode or this is not an ESM
    // file that has been converted to a CommonJS file using a Babel-
    // compatible transform (i.e. "__esModule" has not been set), then set
    // "default" to the CommonJS "module.exports" for node compatibility.
    isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", {
        value: mod,
        enumerable: true
    }) : target, mod));
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
// packages/react/use-layout-effect/src/index.ts
var index_exports = {};
__export(index_exports, {
    useLayoutEffect: ()=>useLayoutEffect2
});
module.exports = __toCommonJS(index_exports);
// packages/react/use-layout-effect/src/use-layout-effect.tsx
var React = __toESM(__turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)"));
var useLayoutEffect2 = globalThis?.document ? React.useLayoutEffect : ()=>{}; //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@radix-ui/react-portal/dist/index.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
"use client";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toESM = (mod, isNodeMode, target)=>(target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(// If the importer is in node compatibility mode or this is not an ESM
    // file that has been converted to a CommonJS file using a Babel-
    // compatible transform (i.e. "__esModule" has not been set), then set
    // "default" to the CommonJS "module.exports" for node compatibility.
    isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", {
        value: mod,
        enumerable: true
    }) : target, mod));
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
// src/index.ts
var index_exports = {};
__export(index_exports, {
    Portal: ()=>Portal,
    Root: ()=>Root
});
module.exports = __toCommonJS(index_exports);
// src/portal.tsx
var React = __toESM(__turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)"));
var import_react_dom = __toESM(__turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js [app-ssr] (ecmascript)"));
var import_react_primitive = __turbopack_context__.r("[project]/node_modules/@radix-ui/react-primitive/dist/index.js [app-ssr] (ecmascript)");
var import_react_use_layout_effect = __turbopack_context__.r("[project]/node_modules/@radix-ui/react-use-layout-effect/dist/index.js [app-ssr] (ecmascript)");
var import_jsx_runtime = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js [app-ssr] (ecmascript)");
var PORTAL_NAME = "Portal";
var Portal = React.forwardRef((props, forwardedRef)=>{
    const { container: containerProp, ...portalProps } = props;
    const [mounted, setMounted] = React.useState(false);
    (0, import_react_use_layout_effect.useLayoutEffect)(()=>setMounted(true), []);
    const container = containerProp || mounted && globalThis?.document?.body;
    return container ? import_react_dom.default.createPortal(/* @__PURE__ */ (0, import_jsx_runtime.jsx)(import_react_primitive.Primitive.div, {
        ...portalProps,
        ref: forwardedRef
    }), container) : null;
});
Portal.displayName = PORTAL_NAME;
var Root = Portal; //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/kbar/lib/KBarPortal.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __createBinding = this && this.__createBinding || (Object.create ? function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, {
        enumerable: true,
        get: function() {
            return m[k];
        }
    });
} : function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
});
var __setModuleDefault = this && this.__setModuleDefault || (Object.create ? function(o, v) {
    Object.defineProperty(o, "default", {
        enumerable: true,
        value: v
    });
} : function(o, v) {
    o["default"] = v;
});
var __importStar = this && this.__importStar || function(mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) {
        for(var k in mod)if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    }
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.KBarPortal = void 0;
var react_portal_1 = __turbopack_context__.r("[project]/node_modules/@radix-ui/react-portal/dist/index.js [app-ssr] (ecmascript)");
var React = __importStar(__turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)"));
var types_1 = __turbopack_context__.r("[project]/node_modules/kbar/lib/types.js [app-ssr] (ecmascript)");
var useKBar_1 = __turbopack_context__.r("[project]/node_modules/kbar/lib/useKBar.js [app-ssr] (ecmascript)");
function KBarPortal(_a) {
    var children = _a.children, container = _a.container;
    var showing = (0, useKBar_1.useKBar)(function(state) {
        return {
            showing: state.visualState !== types_1.VisualState.hidden
        };
    }).showing;
    if (!showing) {
        return null;
    }
    return React.createElement(react_portal_1.Portal, {
        container: container
    }, children);
}
exports.KBarPortal = KBarPortal;
}}),
"[project]/node_modules/kbar/lib/KBarPositioner.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __assign = this && this.__assign || function() {
    __assign = Object.assign || function(t) {
        for(var s, i = 1, n = arguments.length; i < n; i++){
            s = arguments[i];
            for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = this && this.__createBinding || (Object.create ? function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, {
        enumerable: true,
        get: function() {
            return m[k];
        }
    });
} : function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
});
var __setModuleDefault = this && this.__setModuleDefault || (Object.create ? function(o, v) {
    Object.defineProperty(o, "default", {
        enumerable: true,
        value: v
    });
} : function(o, v) {
    o["default"] = v;
});
var __importStar = this && this.__importStar || function(mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) {
        for(var k in mod)if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    }
    __setModuleDefault(result, mod);
    return result;
};
var __rest = this && this.__rest || function(s, e) {
    var t = {};
    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){
        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
    }
    return t;
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.KBarPositioner = void 0;
var React = __importStar(__turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)"));
var defaultStyle = {
    position: "fixed",
    display: "flex",
    alignItems: "flex-start",
    justifyContent: "center",
    width: "100%",
    inset: "0px",
    padding: "14vh 16px 16px"
};
function getStyle(style) {
    return style ? __assign(__assign({}, defaultStyle), style) : defaultStyle;
}
exports.KBarPositioner = React.forwardRef(function(_a, ref) {
    var style = _a.style, children = _a.children, props = __rest(_a, [
        "style",
        "children"
    ]);
    return React.createElement("div", __assign({
        ref: ref,
        style: getStyle(style)
    }, props), children);
});
}}),
"[project]/node_modules/kbar/lib/KBarSearch.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __assign = this && this.__assign || function() {
    __assign = Object.assign || function(t) {
        for(var s, i = 1, n = arguments.length; i < n; i++){
            s = arguments[i];
            for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = this && this.__createBinding || (Object.create ? function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, {
        enumerable: true,
        get: function() {
            return m[k];
        }
    });
} : function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
});
var __setModuleDefault = this && this.__setModuleDefault || (Object.create ? function(o, v) {
    Object.defineProperty(o, "default", {
        enumerable: true,
        value: v
    });
} : function(o, v) {
    o["default"] = v;
});
var __importStar = this && this.__importStar || function(mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) {
        for(var k in mod)if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    }
    __setModuleDefault(result, mod);
    return result;
};
var __rest = this && this.__rest || function(s, e) {
    var t = {};
    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){
        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
    }
    return t;
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.KBarSearch = exports.getListboxItemId = exports.KBAR_LISTBOX = void 0;
var React = __importStar(__turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)"));
var types_1 = __turbopack_context__.r("[project]/node_modules/kbar/lib/types.js [app-ssr] (ecmascript)");
var useKBar_1 = __turbopack_context__.r("[project]/node_modules/kbar/lib/useKBar.js [app-ssr] (ecmascript)");
exports.KBAR_LISTBOX = "kbar-listbox";
var getListboxItemId = function(id) {
    return "kbar-listbox-item-" + id;
};
exports.getListboxItemId = getListboxItemId;
function KBarSearch(props) {
    var _a = (0, useKBar_1.useKBar)(function(state) {
        return {
            search: state.searchQuery,
            currentRootActionId: state.currentRootActionId,
            actions: state.actions,
            activeIndex: state.activeIndex,
            showing: state.visualState === types_1.VisualState.showing
        };
    }), query = _a.query, search = _a.search, actions = _a.actions, currentRootActionId = _a.currentRootActionId, activeIndex = _a.activeIndex, showing = _a.showing, options = _a.options;
    var _b = React.useState(search), inputValue = _b[0], setInputValue = _b[1];
    React.useEffect(function() {
        query.setSearch(inputValue);
    }, [
        inputValue,
        query
    ]);
    var defaultPlaceholder = props.defaultPlaceholder, rest = __rest(props, [
        "defaultPlaceholder"
    ]);
    React.useEffect(function() {
        query.setSearch("");
        query.getInput().focus();
        return function() {
            return query.setSearch("");
        };
    }, [
        currentRootActionId,
        query
    ]);
    var placeholder = React.useMemo(function() {
        var defaultText = defaultPlaceholder !== null && defaultPlaceholder !== void 0 ? defaultPlaceholder : "Type a command or search…";
        return currentRootActionId && actions[currentRootActionId] ? actions[currentRootActionId].name : defaultText;
    }, [
        actions,
        currentRootActionId,
        defaultPlaceholder
    ]);
    return React.createElement("input", __assign({}, rest, {
        ref: query.inputRefSetter,
        autoFocus: true,
        autoComplete: "off",
        role: "combobox",
        spellCheck: "false",
        "aria-expanded": showing,
        "aria-controls": exports.KBAR_LISTBOX,
        "aria-activedescendant": (0, exports.getListboxItemId)(activeIndex),
        value: inputValue,
        placeholder: placeholder,
        onChange: function(event) {
            var _a, _b, _c;
            (_a = props.onChange) === null || _a === void 0 ? void 0 : _a.call(props, event);
            setInputValue(event.target.value);
            (_c = (_b = options === null || options === void 0 ? void 0 : options.callbacks) === null || _b === void 0 ? void 0 : _b.onQueryChange) === null || _c === void 0 ? void 0 : _c.call(_b, event.target.value);
        },
        onKeyDown: function(event) {
            var _a;
            (_a = props.onKeyDown) === null || _a === void 0 ? void 0 : _a.call(props, event);
            if (currentRootActionId && !search && event.key === "Backspace") {
                var parent_1 = actions[currentRootActionId].parent;
                query.setCurrentRootAction(parent_1);
            }
        }
    }));
}
exports.KBarSearch = KBarSearch;
}}),
"[project]/node_modules/react-virtual/dist/react-virtual.mjs [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "defaultRangeExtractor": (()=>defaultRangeExtractor),
    "useVirtual": (()=>useVirtual)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
function _extends() {
    _extends = Object.assign || function(target) {
        for(var i = 1; i < arguments.length; i++){
            var source = arguments[i];
            for(var key in source){
                if (Object.prototype.hasOwnProperty.call(source, key)) {
                    target[key] = source[key];
                }
            }
        }
        return target;
    };
    return _extends.apply(this, arguments);
}
function _objectWithoutPropertiesLoose(source, excluded) {
    if (source == null) return {};
    var target = {};
    var sourceKeys = Object.keys(source);
    var key, i;
    for(i = 0; i < sourceKeys.length; i++){
        key = sourceKeys[i];
        if (excluded.indexOf(key) >= 0) continue;
        target[key] = source[key];
    }
    return target;
}
var props = [
    'bottom',
    'height',
    'left',
    'right',
    'top',
    'width'
];
var rectChanged = function rectChanged(a, b) {
    if (a === void 0) {
        a = {};
    }
    if (b === void 0) {
        b = {};
    }
    return props.some(function(prop) {
        return a[prop] !== b[prop];
    });
};
var observedNodes = /*#__PURE__*/ new Map();
var rafId;
var run = function run() {
    var changedStates = [];
    observedNodes.forEach(function(state, node) {
        var newRect = node.getBoundingClientRect();
        if (rectChanged(newRect, state.rect)) {
            state.rect = newRect;
            changedStates.push(state);
        }
    });
    changedStates.forEach(function(state) {
        state.callbacks.forEach(function(cb) {
            return cb(state.rect);
        });
    });
    rafId = window.requestAnimationFrame(run);
};
function observeRect(node, cb) {
    return {
        observe: function observe() {
            var wasEmpty = observedNodes.size === 0;
            if (observedNodes.has(node)) {
                observedNodes.get(node).callbacks.push(cb);
            } else {
                observedNodes.set(node, {
                    rect: undefined,
                    hasRectChanged: false,
                    callbacks: [
                        cb
                    ]
                });
            }
            if (wasEmpty) run();
        },
        unobserve: function unobserve() {
            var state = observedNodes.get(node);
            if (state) {
                // Remove the callback
                var index = state.callbacks.indexOf(cb);
                if (index >= 0) state.callbacks.splice(index, 1); // Remove the node reference
                if (!state.callbacks.length) observedNodes["delete"](node); // Stop the loop
                if (!observedNodes.size) cancelAnimationFrame(rafId);
            }
        }
    };
}
var useIsomorphicLayoutEffect = typeof window !== 'undefined' ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useLayoutEffect : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect;
function useRect(nodeRef, initialRect) {
    if (initialRect === void 0) {
        initialRect = {
            width: 0,
            height: 0
        };
    }
    var _React$useState = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useState(nodeRef.current), element = _React$useState[0], setElement = _React$useState[1];
    var _React$useReducer = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useReducer(rectReducer, initialRect), rect = _React$useReducer[0], dispatch = _React$useReducer[1];
    var initialRectSet = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useRef(false);
    useIsomorphicLayoutEffect(function() {
        if (nodeRef.current !== element) {
            setElement(nodeRef.current);
        }
    });
    useIsomorphicLayoutEffect(function() {
        if (element && !initialRectSet.current) {
            initialRectSet.current = true;
            var _rect = element.getBoundingClientRect();
            dispatch({
                rect: _rect
            });
        }
    }, [
        element
    ]);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(function() {
        if (!element) {
            return;
        }
        var observer = observeRect(element, function(rect) {
            dispatch({
                rect: rect
            });
        });
        observer.observe();
        return function() {
            observer.unobserve();
        };
    }, [
        element
    ]);
    return rect;
}
function rectReducer(state, action) {
    var rect = action.rect;
    if (state.height !== rect.height || state.width !== rect.width) {
        return rect;
    }
    return state;
}
var defaultEstimateSize = function defaultEstimateSize() {
    return 50;
};
var defaultKeyExtractor = function defaultKeyExtractor(index) {
    return index;
};
var defaultMeasureSize = function defaultMeasureSize(el, horizontal) {
    var key = horizontal ? 'offsetWidth' : 'offsetHeight';
    return el[key];
};
var defaultRangeExtractor = function defaultRangeExtractor(range) {
    var start = Math.max(range.start - range.overscan, 0);
    var end = Math.min(range.end + range.overscan, range.size - 1);
    var arr = [];
    for(var i = start; i <= end; i++){
        arr.push(i);
    }
    return arr;
};
function useVirtual(_ref) {
    var _measurements;
    var _ref$size = _ref.size, size = _ref$size === void 0 ? 0 : _ref$size, _ref$estimateSize = _ref.estimateSize, estimateSize = _ref$estimateSize === void 0 ? defaultEstimateSize : _ref$estimateSize, _ref$overscan = _ref.overscan, overscan = _ref$overscan === void 0 ? 1 : _ref$overscan, _ref$paddingStart = _ref.paddingStart, paddingStart = _ref$paddingStart === void 0 ? 0 : _ref$paddingStart, _ref$paddingEnd = _ref.paddingEnd, paddingEnd = _ref$paddingEnd === void 0 ? 0 : _ref$paddingEnd, parentRef = _ref.parentRef, horizontal = _ref.horizontal, scrollToFn = _ref.scrollToFn, useObserver = _ref.useObserver, initialRect = _ref.initialRect, onScrollElement = _ref.onScrollElement, scrollOffsetFn = _ref.scrollOffsetFn, _ref$keyExtractor = _ref.keyExtractor, keyExtractor = _ref$keyExtractor === void 0 ? defaultKeyExtractor : _ref$keyExtractor, _ref$measureSize = _ref.measureSize, measureSize = _ref$measureSize === void 0 ? defaultMeasureSize : _ref$measureSize, _ref$rangeExtractor = _ref.rangeExtractor, rangeExtractor = _ref$rangeExtractor === void 0 ? defaultRangeExtractor : _ref$rangeExtractor;
    var sizeKey = horizontal ? 'width' : 'height';
    var scrollKey = horizontal ? 'scrollLeft' : 'scrollTop';
    var latestRef = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useRef({
        scrollOffset: 0,
        measurements: []
    });
    var _React$useState = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useState(0), scrollOffset = _React$useState[0], setScrollOffset = _React$useState[1];
    latestRef.current.scrollOffset = scrollOffset;
    var useMeasureParent = useObserver || useRect;
    var _useMeasureParent = useMeasureParent(parentRef, initialRect), outerSize = _useMeasureParent[sizeKey];
    latestRef.current.outerSize = outerSize;
    var defaultScrollToFn = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useCallback(function(offset) {
        if (parentRef.current) {
            parentRef.current[scrollKey] = offset;
        }
    }, [
        parentRef,
        scrollKey
    ]);
    var resolvedScrollToFn = scrollToFn || defaultScrollToFn;
    scrollToFn = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useCallback(function(offset) {
        resolvedScrollToFn(offset, defaultScrollToFn);
    }, [
        defaultScrollToFn,
        resolvedScrollToFn
    ]);
    var _React$useState2 = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useState({}), measuredCache = _React$useState2[0], setMeasuredCache = _React$useState2[1];
    var measure = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useCallback(function() {
        return setMeasuredCache({});
    }, []);
    var pendingMeasuredCacheIndexesRef = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useRef([]);
    var measurements = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useMemo(function() {
        var min = pendingMeasuredCacheIndexesRef.current.length > 0 ? Math.min.apply(Math, pendingMeasuredCacheIndexesRef.current) : 0;
        pendingMeasuredCacheIndexesRef.current = [];
        var measurements = latestRef.current.measurements.slice(0, min);
        for(var i = min; i < size; i++){
            var key = keyExtractor(i);
            var measuredSize = measuredCache[key];
            var _start = measurements[i - 1] ? measurements[i - 1].end : paddingStart;
            var _size = typeof measuredSize === 'number' ? measuredSize : estimateSize(i);
            var _end = _start + _size;
            measurements[i] = {
                index: i,
                start: _start,
                size: _size,
                end: _end,
                key: key
            };
        }
        return measurements;
    }, [
        estimateSize,
        measuredCache,
        paddingStart,
        size,
        keyExtractor
    ]);
    var totalSize = (((_measurements = measurements[size - 1]) == null ? void 0 : _measurements.end) || paddingStart) + paddingEnd;
    latestRef.current.measurements = measurements;
    latestRef.current.totalSize = totalSize;
    var element = onScrollElement ? onScrollElement.current : parentRef.current;
    var scrollOffsetFnRef = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useRef(scrollOffsetFn);
    scrollOffsetFnRef.current = scrollOffsetFn;
    useIsomorphicLayoutEffect(function() {
        if (!element) {
            setScrollOffset(0);
            return;
        }
        var onScroll = function onScroll(event) {
            var offset = scrollOffsetFnRef.current ? scrollOffsetFnRef.current(event) : element[scrollKey];
            setScrollOffset(offset);
        };
        onScroll();
        element.addEventListener('scroll', onScroll, {
            capture: false,
            passive: true
        });
        return function() {
            element.removeEventListener('scroll', onScroll);
        };
    }, [
        element,
        scrollKey
    ]);
    var _calculateRange = calculateRange(latestRef.current), start = _calculateRange.start, end = _calculateRange.end;
    var indexes = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useMemo(function() {
        return rangeExtractor({
            start: start,
            end: end,
            overscan: overscan,
            size: measurements.length
        });
    }, [
        start,
        end,
        overscan,
        measurements.length,
        rangeExtractor
    ]);
    var measureSizeRef = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useRef(measureSize);
    measureSizeRef.current = measureSize;
    var virtualItems = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useMemo(function() {
        var virtualItems = [];
        var _loop = function _loop(k, len) {
            var i = indexes[k];
            var measurement = measurements[i];
            var item = _extends(_extends({}, measurement), {}, {
                measureRef: function measureRef(el) {
                    if (el) {
                        var measuredSize = measureSizeRef.current(el, horizontal);
                        if (measuredSize !== item.size) {
                            var _scrollOffset = latestRef.current.scrollOffset;
                            if (item.start < _scrollOffset) {
                                defaultScrollToFn(_scrollOffset + (measuredSize - item.size));
                            }
                            pendingMeasuredCacheIndexesRef.current.push(i);
                            setMeasuredCache(function(old) {
                                var _extends2;
                                return _extends(_extends({}, old), {}, (_extends2 = {}, _extends2[item.key] = measuredSize, _extends2));
                            });
                        }
                    }
                }
            });
            virtualItems.push(item);
        };
        for(var k = 0, len = indexes.length; k < len; k++){
            _loop(k);
        }
        return virtualItems;
    }, [
        indexes,
        defaultScrollToFn,
        horizontal,
        measurements
    ]);
    var mountedRef = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useRef(false);
    useIsomorphicLayoutEffect(function() {
        if (mountedRef.current) {
            setMeasuredCache({});
        }
        mountedRef.current = true;
    }, [
        estimateSize
    ]);
    var scrollToOffset = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useCallback(function(toOffset, _temp) {
        var _ref2 = _temp === void 0 ? {} : _temp, _ref2$align = _ref2.align, align = _ref2$align === void 0 ? 'start' : _ref2$align;
        var _latestRef$current = latestRef.current, scrollOffset = _latestRef$current.scrollOffset, outerSize = _latestRef$current.outerSize;
        if (align === 'auto') {
            if (toOffset <= scrollOffset) {
                align = 'start';
            } else if (toOffset >= scrollOffset + outerSize) {
                align = 'end';
            } else {
                align = 'start';
            }
        }
        if (align === 'start') {
            scrollToFn(toOffset);
        } else if (align === 'end') {
            scrollToFn(toOffset - outerSize);
        } else if (align === 'center') {
            scrollToFn(toOffset - outerSize / 2);
        }
    }, [
        scrollToFn
    ]);
    var tryScrollToIndex = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useCallback(function(index, _temp2) {
        var _ref3 = _temp2 === void 0 ? {} : _temp2, _ref3$align = _ref3.align, align = _ref3$align === void 0 ? 'auto' : _ref3$align, rest = _objectWithoutPropertiesLoose(_ref3, [
            "align"
        ]);
        var _latestRef$current2 = latestRef.current, measurements = _latestRef$current2.measurements, scrollOffset = _latestRef$current2.scrollOffset, outerSize = _latestRef$current2.outerSize;
        var measurement = measurements[Math.max(0, Math.min(index, size - 1))];
        if (!measurement) {
            return;
        }
        if (align === 'auto') {
            if (measurement.end >= scrollOffset + outerSize) {
                align = 'end';
            } else if (measurement.start <= scrollOffset) {
                align = 'start';
            } else {
                return;
            }
        }
        var toOffset = align === 'center' ? measurement.start + measurement.size / 2 : align === 'end' ? measurement.end : measurement.start;
        scrollToOffset(toOffset, _extends({
            align: align
        }, rest));
    }, [
        scrollToOffset,
        size
    ]);
    var scrollToIndex = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useCallback(function() {
        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){
            args[_key] = arguments[_key];
        }
        // We do a double request here because of
        // dynamic sizes which can cause offset shift
        // and end up in the wrong spot. Unfortunately,
        // we can't know about those dynamic sizes until
        // we try and render them. So double down!
        tryScrollToIndex.apply(void 0, args);
        requestAnimationFrame(function() {
            tryScrollToIndex.apply(void 0, args);
        });
    }, [
        tryScrollToIndex
    ]);
    return {
        virtualItems: virtualItems,
        totalSize: totalSize,
        scrollToOffset: scrollToOffset,
        scrollToIndex: scrollToIndex,
        measure: measure
    };
}
var findNearestBinarySearch = function findNearestBinarySearch(low, high, getCurrentValue, value) {
    while(low <= high){
        var middle = (low + high) / 2 | 0;
        var currentValue = getCurrentValue(middle);
        if (currentValue < value) {
            low = middle + 1;
        } else if (currentValue > value) {
            high = middle - 1;
        } else {
            return middle;
        }
    }
    if (low > 0) {
        return low - 1;
    } else {
        return 0;
    }
};
function calculateRange(_ref4) {
    var measurements = _ref4.measurements, outerSize = _ref4.outerSize, scrollOffset = _ref4.scrollOffset;
    var size = measurements.length - 1;
    var getOffset = function getOffset(index) {
        return measurements[index].start;
    };
    var start = findNearestBinarySearch(0, size, getOffset, scrollOffset);
    var end = start;
    while(end < size && measurements[end].end < scrollOffset + outerSize){
        end++;
    }
    return {
        start: start,
        end: end
    };
}
;
 //# sourceMappingURL=react-virtual.mjs.map
}}),
"[project]/node_modules/kbar/lib/KBarResults.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __assign = this && this.__assign || function() {
    __assign = Object.assign || function(t) {
        for(var s, i = 1, n = arguments.length; i < n; i++){
            s = arguments[i];
            for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = this && this.__createBinding || (Object.create ? function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, {
        enumerable: true,
        get: function() {
            return m[k];
        }
    });
} : function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
});
var __setModuleDefault = this && this.__setModuleDefault || (Object.create ? function(o, v) {
    Object.defineProperty(o, "default", {
        enumerable: true,
        value: v
    });
} : function(o, v) {
    o["default"] = v;
});
var __importStar = this && this.__importStar || function(mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) {
        for(var k in mod)if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    }
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.KBarResults = void 0;
var React = __importStar(__turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)"));
var react_virtual_1 = __turbopack_context__.r("[project]/node_modules/react-virtual/dist/react-virtual.mjs [app-ssr] (ecmascript)");
var KBarSearch_1 = __turbopack_context__.r("[project]/node_modules/kbar/lib/KBarSearch.js [app-ssr] (ecmascript)");
var useKBar_1 = __turbopack_context__.r("[project]/node_modules/kbar/lib/useKBar.js [app-ssr] (ecmascript)");
var utils_1 = __turbopack_context__.r("[project]/node_modules/kbar/lib/utils.js [app-ssr] (ecmascript)");
var START_INDEX = 0;
var KBarResults = function(props) {
    var activeRef = React.useRef(null);
    var parentRef = React.useRef(null);
    // store a ref to all items so we do not have to pass
    // them as a dependency when setting up event listeners.
    var itemsRef = React.useRef(props.items);
    itemsRef.current = props.items;
    var rowVirtualizer = (0, react_virtual_1.useVirtual)({
        size: itemsRef.current.length,
        parentRef: parentRef
    });
    var _a = (0, useKBar_1.useKBar)(function(state) {
        return {
            search: state.searchQuery,
            currentRootActionId: state.currentRootActionId,
            activeIndex: state.activeIndex
        };
    }), query = _a.query, search = _a.search, currentRootActionId = _a.currentRootActionId, activeIndex = _a.activeIndex, options = _a.options;
    React.useEffect(function() {
        var handler = function(event) {
            var _a;
            if (event.isComposing) {
                return;
            }
            if (event.key === "ArrowUp" || event.ctrlKey && event.key === "p") {
                event.preventDefault();
                event.stopPropagation();
                query.setActiveIndex(function(index) {
                    var nextIndex = index > START_INDEX ? index - 1 : index;
                    // avoid setting active index on a group
                    if (typeof itemsRef.current[nextIndex] === "string") {
                        if (nextIndex === 0) return index;
                        nextIndex -= 1;
                    }
                    return nextIndex;
                });
            } else if (event.key === "ArrowDown" || event.ctrlKey && event.key === "n") {
                event.preventDefault();
                event.stopPropagation();
                query.setActiveIndex(function(index) {
                    var nextIndex = index < itemsRef.current.length - 1 ? index + 1 : index;
                    // avoid setting active index on a group
                    if (typeof itemsRef.current[nextIndex] === "string") {
                        if (nextIndex === itemsRef.current.length - 1) return index;
                        nextIndex += 1;
                    }
                    return nextIndex;
                });
            } else if (event.key === "Enter") {
                event.preventDefault();
                event.stopPropagation();
                // storing the active dom element in a ref prevents us from
                // having to calculate the current action to perform based
                // on the `activeIndex`, which we would have needed to add
                // as part of the dependencies array.
                (_a = activeRef.current) === null || _a === void 0 ? void 0 : _a.click();
            }
        };
        window.addEventListener("keydown", handler, {
            capture: true
        });
        return function() {
            return window.removeEventListener("keydown", handler, {
                capture: true
            });
        };
    }, [
        query
    ]);
    // destructuring here to prevent linter warning to pass
    // entire rowVirtualizer in the dependencies array.
    var scrollToIndex = rowVirtualizer.scrollToIndex;
    React.useEffect(function() {
        scrollToIndex(activeIndex, {
            // ensure that if the first item in the list is a group
            // name and we are focused on the second item, to not
            // scroll past that group, hiding it.
            align: activeIndex <= 1 ? "end" : "auto"
        });
    }, [
        activeIndex,
        scrollToIndex
    ]);
    // reset active index only when search or root action changes
    React.useEffect(function() {
        query.setActiveIndex(// avoid setting active index on a group
        typeof itemsRef.current[START_INDEX] === "string" ? START_INDEX + 1 : START_INDEX);
    }, [
        search,
        currentRootActionId,
        query
    ]);
    // adjust active index when items change (ie when actions load async)
    React.useEffect(function() {
        var currentIndex = activeIndex;
        var maxIndex = itemsRef.current.length - 1;
        if (currentIndex > maxIndex && maxIndex >= 0) {
            var newIndex = maxIndex;
            if (typeof itemsRef.current[newIndex] === "string" && newIndex > 0) {
                newIndex -= 1;
            }
            query.setActiveIndex(newIndex);
        } else if (currentIndex <= maxIndex && typeof itemsRef.current[currentIndex] === "string") {
            var newIndex = currentIndex + 1;
            if (newIndex > maxIndex || typeof itemsRef.current[newIndex] === "string") {
                newIndex = currentIndex - 1;
            }
            if (newIndex >= 0 && newIndex <= maxIndex && typeof itemsRef.current[newIndex] !== "string") {
                query.setActiveIndex(newIndex);
            }
        }
    }, [
        props.items,
        activeIndex,
        query
    ]);
    var execute = React.useCallback(function(item) {
        var _a, _b;
        if (typeof item === "string") return;
        if (item.command) {
            item.command.perform(item);
            query.toggle();
        } else {
            query.setSearch("");
            query.setCurrentRootAction(item.id);
        }
        (_b = (_a = options.callbacks) === null || _a === void 0 ? void 0 : _a.onSelectAction) === null || _b === void 0 ? void 0 : _b.call(_a, item);
    }, [
        query,
        options
    ]);
    var pointerMoved = (0, utils_1.usePointerMovedSinceMount)();
    return React.createElement("div", {
        ref: parentRef,
        style: {
            maxHeight: props.maxHeight || 400,
            position: "relative",
            overflow: "auto"
        }
    }, React.createElement("div", {
        role: "listbox",
        id: KBarSearch_1.KBAR_LISTBOX,
        style: {
            height: rowVirtualizer.totalSize + "px",
            width: "100%"
        }
    }, rowVirtualizer.virtualItems.map(function(virtualRow) {
        var item = itemsRef.current[virtualRow.index];
        var handlers = typeof item !== "string" && {
            onPointerMove: function() {
                return pointerMoved && activeIndex !== virtualRow.index && query.setActiveIndex(virtualRow.index);
            },
            onPointerDown: function() {
                return query.setActiveIndex(virtualRow.index);
            },
            onClick: function() {
                return execute(item);
            }
        };
        var active = virtualRow.index === activeIndex;
        return React.createElement("div", __assign({
            ref: active ? activeRef : null,
            id: (0, KBarSearch_1.getListboxItemId)(virtualRow.index),
            role: "option",
            "aria-selected": active,
            key: virtualRow.index,
            style: {
                position: "absolute",
                top: 0,
                left: 0,
                width: "100%",
                transform: "translateY(" + virtualRow.start + "px)"
            }
        }, handlers), React.cloneElement(props.onRender({
            item: item,
            active: active
        }), {
            ref: virtualRow.measureRef
        }));
    })));
};
exports.KBarResults = KBarResults;
}}),
"[project]/node_modules/kbar/lib/useRegisterActions.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __createBinding = this && this.__createBinding || (Object.create ? function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, {
        enumerable: true,
        get: function() {
            return m[k];
        }
    });
} : function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
});
var __setModuleDefault = this && this.__setModuleDefault || (Object.create ? function(o, v) {
    Object.defineProperty(o, "default", {
        enumerable: true,
        value: v
    });
} : function(o, v) {
    o["default"] = v;
});
var __importStar = this && this.__importStar || function(mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) {
        for(var k in mod)if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    }
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.useRegisterActions = void 0;
var React = __importStar(__turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)"));
var useKBar_1 = __turbopack_context__.r("[project]/node_modules/kbar/lib/useKBar.js [app-ssr] (ecmascript)");
function useRegisterActions(actions, dependencies) {
    if (dependencies === void 0) {
        dependencies = [];
    }
    var query = (0, useKBar_1.useKBar)().query;
    // eslint-disable-next-line react-hooks/exhaustive-deps
    var actionsCache = React.useMemo(function() {
        return actions;
    }, dependencies);
    React.useEffect(function() {
        if (!actionsCache.length) {
            return;
        }
        var unregister = query.registerActions(actionsCache);
        return function() {
            unregister();
        };
    }, [
        query,
        actionsCache
    ]);
}
exports.useRegisterActions = useRegisterActions;
}}),
"[project]/node_modules/kbar/lib/KBarAnimator.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __assign = this && this.__assign || function() {
    __assign = Object.assign || function(t) {
        for(var s, i = 1, n = arguments.length; i < n; i++){
            s = arguments[i];
            for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = this && this.__createBinding || (Object.create ? function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, {
        enumerable: true,
        get: function() {
            return m[k];
        }
    });
} : function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
});
var __setModuleDefault = this && this.__setModuleDefault || (Object.create ? function(o, v) {
    Object.defineProperty(o, "default", {
        enumerable: true,
        value: v
    });
} : function(o, v) {
    o["default"] = v;
});
var __importStar = this && this.__importStar || function(mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) {
        for(var k in mod)if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    }
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.KBarAnimator = void 0;
var React = __importStar(__turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)"));
var types_1 = __turbopack_context__.r("[project]/node_modules/kbar/lib/types.js [app-ssr] (ecmascript)");
var useKBar_1 = __turbopack_context__.r("[project]/node_modules/kbar/lib/useKBar.js [app-ssr] (ecmascript)");
var utils_1 = __turbopack_context__.r("[project]/node_modules/kbar/lib/utils.js [app-ssr] (ecmascript)");
var appearanceAnimationKeyframes = [
    {
        opacity: 0,
        transform: "scale(.99)"
    },
    {
        opacity: 1,
        transform: "scale(1.01)"
    },
    {
        opacity: 1,
        transform: "scale(1)"
    }
];
var bumpAnimationKeyframes = [
    {
        transform: "scale(1)"
    },
    {
        transform: "scale(.98)"
    },
    {
        transform: "scale(1)"
    }
];
var KBarAnimator = function(_a) {
    var _b, _c;
    var children = _a.children, style = _a.style, className = _a.className, disableCloseOnOuterClick = _a.disableCloseOnOuterClick;
    var _d = (0, useKBar_1.useKBar)(function(state) {
        return {
            visualState: state.visualState,
            currentRootActionId: state.currentRootActionId
        };
    }), visualState = _d.visualState, currentRootActionId = _d.currentRootActionId, query = _d.query, options = _d.options;
    var outerRef = React.useRef(null);
    var innerRef = React.useRef(null);
    var enterMs = ((_b = options === null || options === void 0 ? void 0 : options.animations) === null || _b === void 0 ? void 0 : _b.enterMs) || 0;
    var exitMs = ((_c = options === null || options === void 0 ? void 0 : options.animations) === null || _c === void 0 ? void 0 : _c.exitMs) || 0;
    // Show/hide animation
    React.useEffect(function() {
        if (visualState === types_1.VisualState.showing) {
            return;
        }
        var duration = visualState === types_1.VisualState.animatingIn ? enterMs : exitMs;
        var element = outerRef.current;
        element === null || element === void 0 ? void 0 : element.animate(appearanceAnimationKeyframes, {
            duration: duration,
            easing: // TODO: expose easing in options
            visualState === types_1.VisualState.animatingOut ? "ease-in" : "ease-out",
            direction: visualState === types_1.VisualState.animatingOut ? "reverse" : "normal",
            fill: "forwards"
        });
    }, [
        options,
        visualState,
        enterMs,
        exitMs
    ]);
    // Height animation
    var previousHeight = React.useRef();
    React.useEffect(function() {
        // Only animate if we're actually showing
        if (visualState === types_1.VisualState.showing) {
            var outer_1 = outerRef.current;
            var inner_1 = innerRef.current;
            if (!outer_1 || !inner_1) {
                return;
            }
            var ro_1 = new ResizeObserver(function(entries) {
                for(var _i = 0, entries_1 = entries; _i < entries_1.length; _i++){
                    var entry = entries_1[_i];
                    var cr = entry.contentRect;
                    if (!previousHeight.current) {
                        previousHeight.current = cr.height;
                    }
                    outer_1.animate([
                        {
                            height: previousHeight.current + "px"
                        },
                        {
                            height: cr.height + "px"
                        }
                    ], {
                        duration: enterMs / 2,
                        // TODO: expose configs here
                        easing: "ease-out",
                        fill: "forwards"
                    });
                    previousHeight.current = cr.height;
                }
            });
            ro_1.observe(inner_1);
            return function() {
                ro_1.unobserve(inner_1);
            };
        }
    }, [
        visualState,
        options,
        enterMs,
        exitMs
    ]);
    // Bump animation between nested actions
    var firstRender = React.useRef(true);
    React.useEffect(function() {
        if (firstRender.current) {
            firstRender.current = false;
            return;
        }
        var element = outerRef.current;
        if (element) {
            element.animate(bumpAnimationKeyframes, {
                duration: enterMs,
                easing: "ease-out"
            });
        }
    }, [
        currentRootActionId,
        enterMs
    ]);
    (0, utils_1.useOuterClick)(outerRef, function() {
        var _a, _b;
        if (disableCloseOnOuterClick) {
            return;
        }
        query.setVisualState(types_1.VisualState.animatingOut);
        (_b = (_a = options.callbacks) === null || _a === void 0 ? void 0 : _a.onClose) === null || _b === void 0 ? void 0 : _b.call(_a);
    });
    return React.createElement("div", {
        ref: outerRef,
        style: __assign(__assign(__assign({}, appearanceAnimationKeyframes[0]), style), {
            pointerEvents: "auto"
        }),
        className: className
    }, React.createElement("div", {
        ref: innerRef
    }, children));
};
exports.KBarAnimator = KBarAnimator;
}}),
"[project]/node_modules/kbar/lib/action/index.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __createBinding = this && this.__createBinding || (Object.create ? function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, {
        enumerable: true,
        get: function() {
            return m[k];
        }
    });
} : function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
});
var __exportStar = this && this.__exportStar || function(m, exports1) {
    for(var p in m)if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports1, p)) __createBinding(exports1, m, p);
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
__exportStar(__turbopack_context__.r("[project]/node_modules/kbar/lib/action/ActionInterface.js [app-ssr] (ecmascript)"), exports);
__exportStar(__turbopack_context__.r("[project]/node_modules/kbar/lib/action/ActionImpl.js [app-ssr] (ecmascript)"), exports);
}}),
"[project]/node_modules/kbar/lib/index.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __createBinding = this && this.__createBinding || (Object.create ? function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, {
        enumerable: true,
        get: function() {
            return m[k];
        }
    });
} : function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
});
var __exportStar = this && this.__exportStar || function(m, exports1) {
    for(var p in m)if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports1, p)) __createBinding(exports1, m, p);
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.Priority = exports.createAction = void 0;
var utils_1 = __turbopack_context__.r("[project]/node_modules/kbar/lib/utils.js [app-ssr] (ecmascript)");
Object.defineProperty(exports, "createAction", {
    enumerable: true,
    get: function() {
        return utils_1.createAction;
    }
});
Object.defineProperty(exports, "Priority", {
    enumerable: true,
    get: function() {
        return utils_1.Priority;
    }
});
__exportStar(__turbopack_context__.r("[project]/node_modules/kbar/lib/useMatches.js [app-ssr] (ecmascript)"), exports);
__exportStar(__turbopack_context__.r("[project]/node_modules/kbar/lib/KBarPortal.js [app-ssr] (ecmascript)"), exports);
__exportStar(__turbopack_context__.r("[project]/node_modules/kbar/lib/KBarPositioner.js [app-ssr] (ecmascript)"), exports);
__exportStar(__turbopack_context__.r("[project]/node_modules/kbar/lib/KBarSearch.js [app-ssr] (ecmascript)"), exports);
__exportStar(__turbopack_context__.r("[project]/node_modules/kbar/lib/KBarResults.js [app-ssr] (ecmascript)"), exports);
__exportStar(__turbopack_context__.r("[project]/node_modules/kbar/lib/useKBar.js [app-ssr] (ecmascript)"), exports);
__exportStar(__turbopack_context__.r("[project]/node_modules/kbar/lib/useRegisterActions.js [app-ssr] (ecmascript)"), exports);
__exportStar(__turbopack_context__.r("[project]/node_modules/kbar/lib/KBarContextProvider.js [app-ssr] (ecmascript)"), exports);
__exportStar(__turbopack_context__.r("[project]/node_modules/kbar/lib/KBarAnimator.js [app-ssr] (ecmascript)"), exports);
__exportStar(__turbopack_context__.r("[project]/node_modules/kbar/lib/types.js [app-ssr] (ecmascript)"), exports);
__exportStar(__turbopack_context__.r("[project]/node_modules/kbar/lib/action/index.js [app-ssr] (ecmascript)"), exports);
}}),

};

//# sourceMappingURL=node_modules_f2e7d5be._.js.map