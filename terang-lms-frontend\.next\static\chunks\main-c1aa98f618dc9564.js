try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="cd1bf5bb-d8ce-44a8-8a1e-4a682e1e0bfc",e._sentryDebugIdIdentifier="sentry-dbid-cd1bf5bb-d8ce-44a8-8a1e-4a682e1e0bfc")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8792],{321:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return i}});let n=r(82585);function i(e,t){if("string"!=typeof e)return!1;let{pathname:r}=(0,n.parsePath)(e);return r===t||r.startsWith(t+"/")}},359:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatUrl:function(){return a},formatWithValidation:function(){return o},urlObjectKeys:function(){return s}});let n=r(63732)._(r(67825)),i=/https?|ftp|gopher|file/;function a(e){let{auth:t,hostname:r}=e,a=e.protocol||"",s=e.pathname||"",o=e.hash||"",l=e.query||"",u=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?u=t+e.host:r&&(u=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(u+=":"+e.port)),l&&"object"==typeof l&&(l=String(n.urlQueryToSearchParams(l)));let c=e.search||l&&"?"+l||"";return a&&!a.endsWith(":")&&(a+=":"),e.slashes||(!a||i.test(a))&&!1!==u?(u="//"+(u||""),s&&"/"!==s[0]&&(s="/"+s)):u||(u=""),o&&"#"!==o[0]&&(o="#"+o),c&&"?"!==c[0]&&(c="?"+c),""+a+u+(s=s.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+o}let s=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function o(e){return a(e)}},1033:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return f},MiddlewareNotFoundError:function(){return y},MissingStaticPage:function(){return _},NormalizeError:function(){return m},PageNotFoundError:function(){return g},SP:function(){return p},ST:function(){return h},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return l},getLocationOrigin:function(){return s},getURL:function(){return o},isAbsoluteUrl:function(){return a},isResSent:function(){return u},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return v}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,i=Array(n),a=0;a<n;a++)i[a]=arguments[a];return r||(r=!0,t=e(...i)),t}}let i=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>i.test(e);function s(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function o(){let{href:e}=window.location,t=s();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&u(r))return n;if(!n)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let p="undefined"!=typeof performance,h=p&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class f extends Error{}class m extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class _ extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class y extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function v(e){return JSON.stringify({message:e.message,stack:e.stack})}},2326:(e,t)=>{"use strict";function r(e){return"/api"===e||!!(null==e?void 0:e.startsWith("/api/"))}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isAPIRoute",{enumerable:!0,get:function(){return r}})},2786:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{normalizeAppPath:function(){return a},normalizeRscURL:function(){return s}});let n=r(71775),i=r(54933);function a(e){return(0,n.ensureLeadingSlash)(e.split("/").reduce((e,t,r,n)=>!t||(0,i.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t,""))}function s(e){return e.replace(/\.rsc($|\?)/,"$1")}},2974:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"HTML_LIMITED_BOT_UA_RE",{enumerable:!0,get:function(){return r}});let r=/Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti/i},3239:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return i}});let r=/[|\\{}()[\]^$+*?.-]/,n=/[|\\{}()[\]^$+*?.-]/g;function i(e){return r.test(e)?e.replace(n,"\\$&"):e}},3669:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{AppRouterContext:function(){return i},GlobalLayoutRouterContext:function(){return s},LayoutRouterContext:function(){return a},MissingSlotContext:function(){return l},TemplateContext:function(){return o}});let n=r(72687)._(r(14232)),i=n.default.createContext(null),a=n.default.createContext(null),s=n.default.createContext(null),o=n.default.createContext(null),l=n.default.createContext(new Set)},4316:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTML_LIMITED_BOT_UA_RE:function(){return n.HTML_LIMITED_BOT_UA_RE},HTML_LIMITED_BOT_UA_RE_STRING:function(){return a},getBotType:function(){return l},isBot:function(){return o}});let n=r(2974),i=/Googlebot|Google-PageRenderer|AdsBot-Google|googleweblight|Storebot-Google/i,a=n.HTML_LIMITED_BOT_UA_RE.source;function s(e){return n.HTML_LIMITED_BOT_UA_RE.test(e)}function o(e){return i.test(e)||s(e)}function l(e){return i.test(e)?"dom":s(e)?"html":void 0}},5025:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RouteAnnouncer:function(){return l},default:function(){return u}});let n=r(72687),i=r(37876),a=n._(r(14232)),s=r(6763),o={border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"absolute",top:0,width:"1px",whiteSpace:"nowrap",wordWrap:"normal"},l=()=>{let{asPath:e}=(0,s.useRouter)(),[t,r]=a.default.useState(""),n=a.default.useRef(e);return a.default.useEffect(()=>{if(n.current!==e)if(n.current=e,document.title)r(document.title);else{var t;let n=document.querySelector("h1");r((null!=(t=null==n?void 0:n.innerText)?t:null==n?void 0:n.textContent)||e)}},[e]),(0,i.jsx)("p",{"aria-live":"assertive",id:"__next-route-announcer__",role:"alert",style:o,children:t})},u=l;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5402:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseRelativeUrl",{enumerable:!0,get:function(){return a}});let n=r(1033),i=r(67825);function a(e,t,r){void 0===r&&(r=!0);let a=new URL((0,n.getLocationOrigin)()),s=t?new URL(t,a):e.startsWith(".")?new URL(window.location.href):a,{pathname:o,searchParams:l,search:u,hash:c,href:d,origin:p}=new URL(e,s);if(p!==a.origin)throw Object.defineProperty(Error("invariant: invalid relative URL, router received "+e),"__NEXT_ERROR_CODE",{value:"E159",enumerable:!1,configurable:!0});return{pathname:o,query:r?(0,i.searchParamsToUrlQuery)(l):void 0,search:u,hash:c,href:d.slice(p.length)}}},6763:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{Router:function(){return a.default},createRouter:function(){return m},default:function(){return h},makePublicRouterInstance:function(){return g},useRouter:function(){return f},withRouter:function(){return l.default}});let n=r(72687),i=n._(r(14232)),a=n._(r(54267)),s=r(54387),o=n._(r(66240)),l=n._(r(73824)),u={router:null,readyCallbacks:[],ready(e){if(this.router)return e();this.readyCallbacks.push(e)}},c=["pathname","route","query","asPath","components","isFallback","basePath","locale","locales","defaultLocale","isReady","isPreview","isLocaleDomain","domainLocales"],d=["push","replace","reload","back","prefetch","beforePopState"];function p(){if(!u.router)throw Object.defineProperty(Error('No router instance found.\nYou should only use "next/router" on the client side of your app.\n'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return u.router}Object.defineProperty(u,"events",{get:()=>a.default.events}),c.forEach(e=>{Object.defineProperty(u,e,{get:()=>p()[e]})}),d.forEach(e=>{u[e]=function(){for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];return p()[e](...r)}}),["routeChangeStart","beforeHistoryChange","routeChangeComplete","routeChangeError","hashChangeStart","hashChangeComplete"].forEach(e=>{u.ready(()=>{a.default.events.on(e,function(){for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];let i="on"+e.charAt(0).toUpperCase()+e.substring(1);if(u[i])try{u[i](...r)}catch(e){console.error("Error when running the Router event: "+i),console.error((0,o.default)(e)?e.message+"\n"+e.stack:e+"")}})})});let h=u;function f(){let e=i.default.useContext(s.RouterContext);if(!e)throw Object.defineProperty(Error("NextRouter was not mounted. https://nextjs.org/docs/messages/next-router-not-mounted"),"__NEXT_ERROR_CODE",{value:"E509",enumerable:!1,configurable:!0});return e}function m(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return u.router=new a.default(...t),u.readyCallbacks.forEach(e=>e()),u.readyCallbacks=[],u.router}function g(e){let t={};for(let r of c){if("object"==typeof e[r]){t[r]=Object.assign(Array.isArray(e[r])?[]:{},e[r]);continue}t[r]=e[r]}return t.events=a.default.events,d.forEach(r=>{t[r]=function(){for(var t=arguments.length,n=Array(t),i=0;i<t;i++)n[i]=arguments[i];return e[r](...n)}}),t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9440:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{PathParamsContext:function(){return s},PathnameContext:function(){return a},SearchParamsContext:function(){return i}});let n=r(14232),i=(0,n.createContext)(null),a=(0,n.createContext)(null),s=(0,n.createContext)(null)},13666:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}});var r=function(e){return e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},15047:(e,t)=>{"use strict";function r(e,t){if(void 0===t&&(t={}),t.onlyHashChange)return void e();let r=document.documentElement,n=r.style.scrollBehavior;r.style.scrollBehavior="auto",t.dontForceLayout||r.getClientRects(),e(),r.style.scrollBehavior=n}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleSmoothScroll",{enumerable:!0,get:function(){return r}})},15358:(e,t)=>{"use strict";function r(e,t){return void 0===t&&(t=""),("/"===e?"/index":/^\/index(\/|$)/.test(e)?"/index"+e:e)+t}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return r}})},15861:e=>{!function(){var t={229:function(e){var t,r,n,i=e.exports={};function a(){throw Error("setTimeout has not been defined")}function s(){throw Error("clearTimeout has not been defined")}try{t="function"==typeof setTimeout?setTimeout:a}catch(e){t=a}try{r="function"==typeof clearTimeout?clearTimeout:s}catch(e){r=s}function o(e){if(t===setTimeout)return setTimeout(e,0);if((t===a||!t)&&setTimeout)return t=setTimeout,setTimeout(e,0);try{return t(e,0)}catch(r){try{return t.call(null,e,0)}catch(r){return t.call(this,e,0)}}}var l=[],u=!1,c=-1;function d(){u&&n&&(u=!1,n.length?l=n.concat(l):c=-1,l.length&&p())}function p(){if(!u){var e=o(d);u=!0;for(var t=l.length;t;){for(n=l,l=[];++c<t;)n&&n[c].run();c=-1,t=l.length}n=null,u=!1,function(e){if(r===clearTimeout)return clearTimeout(e);if((r===s||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(e);try{r(e)}catch(t){try{return r.call(null,e)}catch(t){return r.call(this,e)}}}(e)}}function h(e,t){this.fun=e,this.array=t}function f(){}i.nextTick=function(e){var t=Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];l.push(new h(e,t)),1!==l.length||u||o(p)},h.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=f,i.addListener=f,i.once=f,i.off=f,i.removeListener=f,i.removeAllListeners=f,i.emit=f,i.prependListener=f,i.prependOnceListener=f,i.listeners=function(e){return[]},i.binding=function(e){throw Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(e){throw Error("process.chdir is not supported")},i.umask=function(){return 0}}},r={};function n(e){var i=r[e];if(void 0!==i)return i.exports;var a=r[e]={exports:{}},s=!0;try{t[e](a,a.exports,n),s=!1}finally{s&&delete r[e]}return a.exports}n.ab="//",e.exports=n(229)}()},16450:(e,t,r)=>{"use strict";let n,i,a,s,o,l,u,c,d,p,h,f;Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{emitter:function(){return $},hydrate:function(){return eo},initialize:function(){return z},router:function(){return n},version:function(){return B}});let m=r(72687),g=r(37876);r(21291);let _=m._(r(14232)),y=m._(r(78944)),v=r(74324),b=m._(r(24622)),E=r(54387),S=r(15047),w=r(88668),R=r(67825),P=r(83378),T=r(1033),x=r(27311),O=m._(r(57962)),I=m._(r(82155)),C=r(5025),M=r(6763),k=r(66240),A=r(81854),N=r(39116),j=r(92860),L=r(3669),D=r(83250),U=r(9440),F=r(49452);r(69272),r(23428);let B="15.3.2",$=(0,b.default)(),H=e=>[].slice.call(e),W=!1;class q extends _.default.Component{componentDidCatch(e,t){this.props.fn(e,t)}componentDidMount(){this.scrollToHash(),n.isSsr&&(i.isFallback||i.nextExport&&((0,w.isDynamicRoute)(n.pathname)||location.search||1)||i.props&&i.props.__N_SSG&&(location.search||1))&&n.replace(n.pathname+"?"+String((0,R.assign)((0,R.urlQueryToSearchParams)(n.query),new URLSearchParams(location.search))),a,{_h:1,shallow:!i.isFallback&&!W}).catch(e=>{if(!e.cancelled)throw e})}componentDidUpdate(){this.scrollToHash()}scrollToHash(){let{hash:e}=location;if(!(e=e&&e.substring(1)))return;let t=document.getElementById(e);t&&setTimeout(()=>t.scrollIntoView(),0)}render(){return this.props.children}}async function z(e){void 0===e&&(e={}),i=JSON.parse(document.getElementById("__NEXT_DATA__").textContent),window.__NEXT_DATA__=i,f=i.defaultLocale;let t=i.assetPrefix||"";if(self.__next_set_public_path__(""+t+"/_next/"),(0,P.setConfig)({serverRuntimeConfig:{},publicRuntimeConfig:i.runtimeConfig||{}}),a=(0,T.getURL)(),(0,j.hasBasePath)(a)&&(a=(0,N.removeBasePath)(a)),i.scriptLoader){let{initScriptLoader:e}=r(87665);e(i.scriptLoader)}s=new I.default(i.buildId,t);let u=e=>{let[t,r]=e;return s.routeLoader.onEntrypoint(t,r)};return window.__NEXT_P&&window.__NEXT_P.map(e=>setTimeout(()=>u(e),0)),window.__NEXT_P=[],window.__NEXT_P.push=u,(l=(0,O.default)()).getIsSsr=()=>n.isSsr,o=document.getElementById("__next"),{assetPrefix:t}}function X(e,t){return(0,g.jsx)(e,{...t})}function G(e){var t;let{children:r}=e,i=_.default.useMemo(()=>(0,D.adaptForAppRouterInstance)(n),[]);return(0,g.jsx)(q,{fn:e=>Y({App:d,err:e}).catch(e=>console.error("Error rendering page: ",e)),children:(0,g.jsx)(L.AppRouterContext.Provider,{value:i,children:(0,g.jsx)(U.SearchParamsContext.Provider,{value:(0,D.adaptForSearchParams)(n),children:(0,g.jsx)(D.PathnameContextProviderAdapter,{router:n,isAutoExport:null!=(t=self.__NEXT_DATA__.autoExport)&&t,children:(0,g.jsx)(U.PathParamsContext.Provider,{value:(0,D.adaptForPathParams)(n),children:(0,g.jsx)(E.RouterContext.Provider,{value:(0,M.makePublicRouterInstance)(n),children:(0,g.jsx)(v.HeadManagerContext.Provider,{value:l,children:(0,g.jsx)(A.ImageConfigContext.Provider,{value:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1},children:r})})})})})})})})}let V=e=>t=>{let r={...t,Component:h,err:i.err,router:n};return(0,g.jsx)(G,{children:X(e,r)})};function Y(e){let{App:t,err:o}=e;return console.error(o),console.error("A client-side exception has occurred, see here for more info: https://nextjs.org/docs/messages/client-side-exception-occurred"),s.loadPage("/_error").then(n=>{let{page:i,styleSheets:a}=n;return(null==u?void 0:u.Component)===i?r.e(1646).then(r.t.bind(r,31646,23)).then(n=>r.e(5139).then(r.t.bind(r,45139,23)).then(r=>(e.App=t=r.default,n))).then(e=>({ErrorComponent:e.default,styleSheets:[]})):{ErrorComponent:i,styleSheets:a}}).then(r=>{var s;let{ErrorComponent:l,styleSheets:u}=r,c=V(t),d={Component:l,AppTree:c,router:n,ctx:{err:o,pathname:i.page,query:i.query,asPath:a,AppTree:c}};return Promise.resolve((null==(s=e.props)?void 0:s.err)?e.props:(0,T.loadGetInitialProps)(t,d)).then(t=>ea({...e,err:o,Component:l,styleSheets:u,props:t}))})}function K(e){let{callback:t}=e;return _.default.useLayoutEffect(()=>t(),[t]),null}let J={navigationStart:"navigationStart",beforeRender:"beforeRender",afterRender:"afterRender",afterHydrate:"afterHydrate",routeChange:"routeChange"},Q={hydration:"Next.js-hydration",beforeHydration:"Next.js-before-hydration",routeChangeToRender:"Next.js-route-change-to-render",render:"Next.js-render"},Z=null,ee=!0;function et(){[J.beforeRender,J.afterHydrate,J.afterRender,J.routeChange].forEach(e=>performance.clearMarks(e))}function er(){T.ST&&(performance.mark(J.afterHydrate),performance.getEntriesByName(J.beforeRender,"mark").length&&(performance.measure(Q.beforeHydration,J.navigationStart,J.beforeRender),performance.measure(Q.hydration,J.beforeRender,J.afterHydrate)),p&&performance.getEntriesByName(Q.hydration).forEach(p),et())}function en(){if(!T.ST)return;performance.mark(J.afterRender);let e=performance.getEntriesByName(J.routeChange,"mark");e.length&&(performance.getEntriesByName(J.beforeRender,"mark").length&&(performance.measure(Q.routeChangeToRender,e[0].name,J.beforeRender),performance.measure(Q.render,J.beforeRender,J.afterRender),p&&(performance.getEntriesByName(Q.render).forEach(p),performance.getEntriesByName(Q.routeChangeToRender).forEach(p))),et(),[Q.routeChangeToRender,Q.render].forEach(e=>performance.clearMeasures(e)))}function ei(e){let{callbacks:t,children:r}=e;return _.default.useLayoutEffect(()=>t.forEach(e=>e()),[t]),r}function ea(e){let t,r,{App:i,Component:a,props:s,err:l}=e,d="initial"in e?void 0:e.styleSheets;a=a||u.Component;let p={...s=s||u.props,Component:a,err:l,router:n};u=p;let h=!1,f=new Promise((e,t)=>{c&&c(),r=()=>{c=null,e()},c=()=>{h=!0,c=null;let e=Object.defineProperty(Error("Cancel rendering route"),"__NEXT_ERROR_CODE",{value:"E503",enumerable:!1,configurable:!0});e.cancelled=!0,t(e)}});function m(){r()}!function(){if(!d)return;let e=new Set(H(document.querySelectorAll("style[data-n-href]")).map(e=>e.getAttribute("data-n-href"))),t=document.querySelector("noscript[data-n-css]"),r=null==t?void 0:t.getAttribute("data-n-css");d.forEach(t=>{let{href:n,text:i}=t;if(!e.has(n)){let e=document.createElement("style");e.setAttribute("data-n-href",n),e.setAttribute("media","x"),r&&e.setAttribute("nonce",r),document.head.appendChild(e),e.appendChild(document.createTextNode(i))}})}();let v=(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(K,{callback:function(){if(d&&!h){let e=new Set(d.map(e=>e.href)),t=H(document.querySelectorAll("style[data-n-href]")),r=t.map(e=>e.getAttribute("data-n-href"));for(let n=0;n<r.length;++n)e.has(r[n])?t[n].removeAttribute("media"):t[n].setAttribute("media","x");let n=document.querySelector("noscript[data-n-css]");n&&d.forEach(e=>{let{href:t}=e,r=document.querySelector('style[data-n-href="'+t+'"]');r&&(n.parentNode.insertBefore(r,n.nextSibling),n=r)}),H(document.querySelectorAll("link[data-n-p]")).forEach(e=>{e.parentNode.removeChild(e)})}if(e.scroll){let{x:t,y:r}=e.scroll;(0,S.handleSmoothScroll)(()=>{window.scrollTo(t,r)})}}}),(0,g.jsxs)(G,{children:[X(i,p),(0,g.jsx)(x.Portal,{type:"next-route-announcer",children:(0,g.jsx)(C.RouteAnnouncer,{})})]})]});var b=o;T.ST&&performance.mark(J.beforeRender);let E=(t=ee?er:en,(0,g.jsx)(ei,{callbacks:[t,m],children:v}));return Z?(0,_.default.startTransition)(()=>{Z.render(E)}):(Z=y.default.hydrateRoot(b,E,{onRecoverableError:F.onRecoverableError}),ee=!1),f}async function es(e){if(e.err&&(void 0===e.Component||!e.isHydratePass))return void await Y(e);try{await ea(e)}catch(r){let t=(0,k.getProperError)(r);if(t.cancelled)throw t;await Y({...e,err:t})}}async function eo(e){let t=i.err;try{let e=await s.routeLoader.whenEntrypoint("/_app");if("error"in e)throw e.error;let{component:t,exports:r}=e;d=t,r&&r.reportWebVitals&&(p=e=>{let t,{id:n,name:i,startTime:a,value:s,duration:o,entryType:l,entries:u,attribution:c}=e,d=Date.now()+"-"+(Math.floor(Math.random()*(9e12-1))+1e12);u&&u.length&&(t=u[0].startTime);let p={id:n||d,name:i,startTime:a||t,value:null==s?o:s,label:"mark"===l||"measure"===l?"custom":"web-vital"};c&&(p.attribution=c),r.reportWebVitals(p)});let n=await s.routeLoader.whenEntrypoint(i.page);if("error"in n)throw n.error;h=n.component}catch(e){t=(0,k.getProperError)(e)}window.__NEXT_PRELOADREADY&&await window.__NEXT_PRELOADREADY(i.dynamicIds),n=(0,M.createRouter)(i.page,i.query,a,{initialProps:i.props,pageLoader:s,App:d,Component:h,wrapApp:V,err:t,isFallback:!!i.isFallback,subscription:(e,t,r)=>es(Object.assign({},e,{App:t,scroll:r})),locale:i.locale,locales:i.locales,defaultLocale:f,domainLocales:i.domainLocales,isPreview:i.isPreview}),W=await n._initialMatchesMiddlewarePromise;let r={App:d,initial:!0,Component:h,props:i.props,err:t,isHydratePass:!0};(null==e?void 0:e.beforeRender)&&await e.beforeRender(),es(r)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},17136:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getNextPathnameInfo",{enumerable:!0,get:function(){return s}});let n=r(83847),i=r(98091),a=r(321);function s(e,t){var r,s;let{basePath:o,i18n:l,trailingSlash:u}=null!=(r=t.nextConfig)?r:{},c={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):u};o&&(0,a.pathHasPrefix)(c.pathname,o)&&(c.pathname=(0,i.removePathPrefix)(c.pathname,o),c.basePath=o);let d=c.pathname;if(c.pathname.startsWith("/_next/data/")&&c.pathname.endsWith(".json")){let e=c.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/");c.buildId=e[0],d="index"!==e[1]?"/"+e.slice(1).join("/"):"/",!0===t.parseData&&(c.pathname=d)}if(l){let e=t.i18nProvider?t.i18nProvider.analyze(c.pathname):(0,n.normalizeLocalePath)(c.pathname,l.locales);c.locale=e.detectedLocale,c.pathname=null!=(s=e.pathname)?s:c.pathname,!e.detectedLocale&&c.buildId&&(e=t.i18nProvider?t.i18nProvider.analyze(d):(0,n.normalizeLocalePath)(d,l.locales)).detectedLocale&&(c.locale=e.detectedLocale)}return c}},18804:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTTPAccessErrorStatus:function(){return r},HTTP_ERROR_FALLBACK_ERROR_CODE:function(){return i},getAccessFallbackErrorTypeByStatus:function(){return o},getAccessFallbackHTTPStatus:function(){return s},isHTTPAccessFallbackError:function(){return a}});let r={NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401},n=new Set(Object.values(r)),i="NEXT_HTTP_ERROR_FALLBACK";function a(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r]=e.digest.split(";");return t===i&&n.has(Number(r))}function s(e){return Number(e.digest.split(";")[1])}function o(e){switch(e){case 401:return"unauthorized";case 403:return"forbidden";case 404:return"not-found";default:return}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},20718:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"interpolateAs",{enumerable:!0,get:function(){return a}});let n=r(51082),i=r(70159);function a(e,t,r){let a="",s=(0,i.getRouteRegex)(e),o=s.groups,l=(t!==e?(0,n.getRouteMatcher)(s)(t):"")||r;a=e;let u=Object.keys(o);return u.every(e=>{let t=l[e]||"",{repeat:r,optional:n}=o[e],i="["+(r?"...":"")+e+"]";return n&&(i=(t?"":"/")+"["+i+"]"),r&&!Array.isArray(t)&&(t=[t]),(n||e in l)&&(a=a.replace(i,r?t.map(e=>encodeURIComponent(e)).join("/"):encodeURIComponent(t))||"/")})||(a=""),{params:u,result:a}}},21112:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{cancelIdleCallback:function(){return n},requestIdleCallback:function(){return r}});let r="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},n="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},21291:()=>{"trimStart"in String.prototype||(String.prototype.trimStart=String.prototype.trimLeft),"trimEnd"in String.prototype||(String.prototype.trimEnd=String.prototype.trimRight),"description"in Symbol.prototype||Object.defineProperty(Symbol.prototype,"description",{configurable:!0,get:function(){var e=/\((.*)\)/.exec(this.toString());return e?e[1]:void 0}}),Array.prototype.flat||(Array.prototype.flat=function(e,t){return t=this.concat.apply([],this),e>1&&t.some(Array.isArray)?t.flat(e-1):t},Array.prototype.flatMap=function(e,t){return this.map(e,t).flat()}),Promise.prototype.finally||(Promise.prototype.finally=function(e){if("function"!=typeof e)return this.then(e,e);var t=this.constructor||Promise;return this.then(function(r){return t.resolve(e()).then(function(){return r})},function(r){return t.resolve(e()).then(function(){throw r})})}),Object.fromEntries||(Object.fromEntries=function(e){return Array.from(e).reduce(function(e,t){return e[t[0]]=t[1],e},{})}),Array.prototype.at||(Array.prototype.at=function(e){var t=Math.trunc(e)||0;if(t<0&&(t+=this.length),!(t<0||t>=this.length))return this[t]}),Object.hasOwn||(Object.hasOwn=function(e,t){if(null==e)throw TypeError("Cannot convert undefined or null to object");return Object.prototype.hasOwnProperty.call(Object(e),t)}),"canParse"in URL||(URL.canParse=function(e,t){try{return new URL(e,t),!0}catch(e){return!1}})},23428:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNextRouterError",{enumerable:!0,get:function(){return a}});let n=r(18804),i=r(86500);function a(e){return(0,i.isRedirectError)(e)||(0,n.isHTTPAccessFallbackError)(e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},24622:(e,t)=>{"use strict";function r(){let e=Object.create(null);return{on(t,r){(e[t]||(e[t]=[])).push(r)},off(t,r){e[t]&&e[t].splice(e[t].indexOf(r)>>>0,1)},emit(t){for(var r=arguments.length,n=Array(r>1?r-1:0),i=1;i<r;i++)n[i-1]=arguments[i];(e[t]||[]).slice().map(e=>{e(...n)})}}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return r}})},24802:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getSortedRouteObjects:function(){return i},getSortedRoutes:function(){return n}});class r{insert(e){this._insert(e.split("/").filter(Boolean),[],!1)}smoosh(){return this._smoosh()}_smoosh(e){void 0===e&&(e="/");let t=[...this.children.keys()].sort();null!==this.slugName&&t.splice(t.indexOf("[]"),1),null!==this.restSlugName&&t.splice(t.indexOf("[...]"),1),null!==this.optionalRestSlugName&&t.splice(t.indexOf("[[...]]"),1);let r=t.map(t=>this.children.get(t)._smoosh(""+e+t+"/")).reduce((e,t)=>[...e,...t],[]);if(null!==this.slugName&&r.push(...this.children.get("[]")._smoosh(e+"["+this.slugName+"]/")),!this.placeholder){let t="/"===e?"/":e.slice(0,-1);if(null!=this.optionalRestSlugName)throw Object.defineProperty(Error('You cannot define a route with the same specificity as a optional catch-all route ("'+t+'" and "'+t+"[[..."+this.optionalRestSlugName+']]").'),"__NEXT_ERROR_CODE",{value:"E458",enumerable:!1,configurable:!0});r.unshift(t)}return null!==this.restSlugName&&r.push(...this.children.get("[...]")._smoosh(e+"[..."+this.restSlugName+"]/")),null!==this.optionalRestSlugName&&r.push(...this.children.get("[[...]]")._smoosh(e+"[[..."+this.optionalRestSlugName+"]]/")),r}_insert(e,t,n){if(0===e.length){this.placeholder=!1;return}if(n)throw Object.defineProperty(Error("Catch-all must be the last part of the URL."),"__NEXT_ERROR_CODE",{value:"E392",enumerable:!1,configurable:!0});let i=e[0];if(i.startsWith("[")&&i.endsWith("]")){let r=i.slice(1,-1),s=!1;if(r.startsWith("[")&&r.endsWith("]")&&(r=r.slice(1,-1),s=!0),r.startsWith("…"))throw Object.defineProperty(Error("Detected a three-dot character ('…') at ('"+r+"'). Did you mean ('...')?"),"__NEXT_ERROR_CODE",{value:"E147",enumerable:!1,configurable:!0});if(r.startsWith("...")&&(r=r.substring(3),n=!0),r.startsWith("[")||r.endsWith("]"))throw Object.defineProperty(Error("Segment names may not start or end with extra brackets ('"+r+"')."),"__NEXT_ERROR_CODE",{value:"E421",enumerable:!1,configurable:!0});if(r.startsWith("."))throw Object.defineProperty(Error("Segment names may not start with erroneous periods ('"+r+"')."),"__NEXT_ERROR_CODE",{value:"E288",enumerable:!1,configurable:!0});function a(e,r){if(null!==e&&e!==r)throw Object.defineProperty(Error("You cannot use different slug names for the same dynamic path ('"+e+"' !== '"+r+"')."),"__NEXT_ERROR_CODE",{value:"E337",enumerable:!1,configurable:!0});t.forEach(e=>{if(e===r)throw Object.defineProperty(Error('You cannot have the same slug name "'+r+'" repeat within a single dynamic path'),"__NEXT_ERROR_CODE",{value:"E247",enumerable:!1,configurable:!0});if(e.replace(/\W/g,"")===i.replace(/\W/g,""))throw Object.defineProperty(Error('You cannot have the slug names "'+e+'" and "'+r+'" differ only by non-word symbols within a single dynamic path'),"__NEXT_ERROR_CODE",{value:"E499",enumerable:!1,configurable:!0})}),t.push(r)}if(n)if(s){if(null!=this.restSlugName)throw Object.defineProperty(Error('You cannot use both an required and optional catch-all route at the same level ("[...'+this.restSlugName+']" and "'+e[0]+'" ).'),"__NEXT_ERROR_CODE",{value:"E299",enumerable:!1,configurable:!0});a(this.optionalRestSlugName,r),this.optionalRestSlugName=r,i="[[...]]"}else{if(null!=this.optionalRestSlugName)throw Object.defineProperty(Error('You cannot use both an optional and required catch-all route at the same level ("[[...'+this.optionalRestSlugName+']]" and "'+e[0]+'").'),"__NEXT_ERROR_CODE",{value:"E300",enumerable:!1,configurable:!0});a(this.restSlugName,r),this.restSlugName=r,i="[...]"}else{if(s)throw Object.defineProperty(Error('Optional route parameters are not yet supported ("'+e[0]+'").'),"__NEXT_ERROR_CODE",{value:"E435",enumerable:!1,configurable:!0});a(this.slugName,r),this.slugName=r,i="[]"}}this.children.has(i)||this.children.set(i,new r),this.children.get(i)._insert(e.slice(1),t,n)}constructor(){this.placeholder=!0,this.children=new Map,this.slugName=null,this.restSlugName=null,this.optionalRestSlugName=null}}function n(e){let t=new r;return e.forEach(e=>t.insert(e)),t.smoosh()}function i(e,t){let r={},i=[];for(let n=0;n<e.length;n++){let a=t(e[n]);r[a]=n,i[n]=a}return n(i).map(t=>e[r[t]])}},27311:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Portal",{enumerable:!0,get:function(){return a}});let n=r(14232),i=r(98477),a=e=>{let{children:t,type:r}=e,[a,s]=(0,n.useState)(null);return(0,n.useEffect)(()=>{let e=document.createElement(r);return document.body.appendChild(e),s(e),()=>{document.body.removeChild(e)}},[r]),a?(0,i.createPortal)(t,a):null};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},27426:(e,t)=>{"use strict";function r(e){return new URL(e,"http://n").searchParams}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"asPathToSearchParams",{enumerable:!0,get:function(){return r}})},29509:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab="//");var t={};(()=>{function e(e,t){void 0===t&&(t={});for(var r=function(e){for(var t=[],r=0;r<e.length;){var n=e[r];if("*"===n||"+"===n||"?"===n){t.push({type:"MODIFIER",index:r,value:e[r++]});continue}if("\\"===n){t.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});continue}if("{"===n){t.push({type:"OPEN",index:r,value:e[r++]});continue}if("}"===n){t.push({type:"CLOSE",index:r,value:e[r++]});continue}if(":"===n){for(var i="",a=r+1;a<e.length;){var s=e.charCodeAt(a);if(s>=48&&s<=57||s>=65&&s<=90||s>=97&&s<=122||95===s){i+=e[a++];continue}break}if(!i)throw TypeError("Missing parameter name at "+r);t.push({type:"NAME",index:r,value:i}),r=a;continue}if("("===n){var o=1,l="",a=r+1;if("?"===e[a])throw TypeError('Pattern cannot start with "?" at '+a);for(;a<e.length;){if("\\"===e[a]){l+=e[a++]+e[a++];continue}if(")"===e[a]){if(0==--o){a++;break}}else if("("===e[a]&&(o++,"?"!==e[a+1]))throw TypeError("Capturing groups are not allowed at "+a);l+=e[a++]}if(o)throw TypeError("Unbalanced pattern at "+r);if(!l)throw TypeError("Missing pattern at "+r);t.push({type:"PATTERN",index:r,value:l}),r=a;continue}t.push({type:"CHAR",index:r,value:e[r++]})}return t.push({type:"END",index:r,value:""}),t}(e),n=t.prefixes,a=void 0===n?"./":n,s="[^"+i(t.delimiter||"/#?")+"]+?",o=[],l=0,u=0,c="",d=function(e){if(u<r.length&&r[u].type===e)return r[u++].value},p=function(e){var t=d(e);if(void 0!==t)return t;var n=r[u];throw TypeError("Unexpected "+n.type+" at "+n.index+", expected "+e)},h=function(){for(var e,t="";e=d("CHAR")||d("ESCAPED_CHAR");)t+=e;return t};u<r.length;){var f=d("CHAR"),m=d("NAME"),g=d("PATTERN");if(m||g){var _=f||"";-1===a.indexOf(_)&&(c+=_,_=""),c&&(o.push(c),c=""),o.push({name:m||l++,prefix:_,suffix:"",pattern:g||s,modifier:d("MODIFIER")||""});continue}var y=f||d("ESCAPED_CHAR");if(y){c+=y;continue}if(c&&(o.push(c),c=""),d("OPEN")){var _=h(),v=d("NAME")||"",b=d("PATTERN")||"",E=h();p("CLOSE"),o.push({name:v||(b?l++:""),pattern:v&&!b?s:b,prefix:_,suffix:E,modifier:d("MODIFIER")||""});continue}p("END")}return o}function r(e,t){void 0===t&&(t={});var r=a(t),n=t.encode,i=void 0===n?function(e){return e}:n,s=t.validate,o=void 0===s||s,l=e.map(function(e){if("object"==typeof e)return RegExp("^(?:"+e.pattern+")$",r)});return function(t){for(var r="",n=0;n<e.length;n++){var a=e[n];if("string"==typeof a){r+=a;continue}var s=t?t[a.name]:void 0,u="?"===a.modifier||"*"===a.modifier,c="*"===a.modifier||"+"===a.modifier;if(Array.isArray(s)){if(!c)throw TypeError('Expected "'+a.name+'" to not repeat, but got an array');if(0===s.length){if(u)continue;throw TypeError('Expected "'+a.name+'" to not be empty')}for(var d=0;d<s.length;d++){var p=i(s[d],a);if(o&&!l[n].test(p))throw TypeError('Expected all "'+a.name+'" to match "'+a.pattern+'", but got "'+p+'"');r+=a.prefix+p+a.suffix}continue}if("string"==typeof s||"number"==typeof s){var p=i(String(s),a);if(o&&!l[n].test(p))throw TypeError('Expected "'+a.name+'" to match "'+a.pattern+'", but got "'+p+'"');r+=a.prefix+p+a.suffix;continue}if(!u){var h=c?"an array":"a string";throw TypeError('Expected "'+a.name+'" to be '+h)}}return r}}function n(e,t,r){void 0===r&&(r={});var n=r.decode,i=void 0===n?function(e){return e}:n;return function(r){var n=e.exec(r);if(!n)return!1;for(var a=n[0],s=n.index,o=Object.create(null),l=1;l<n.length;l++)!function(e){if(void 0!==n[e]){var r=t[e-1];"*"===r.modifier||"+"===r.modifier?o[r.name]=n[e].split(r.prefix+r.suffix).map(function(e){return i(e,r)}):o[r.name]=i(n[e],r)}}(l);return{path:a,index:s,params:o}}}function i(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function a(e){return e&&e.sensitive?"":"i"}function s(e,t,r){void 0===r&&(r={});for(var n=r.strict,s=void 0!==n&&n,o=r.start,l=r.end,u=r.encode,c=void 0===u?function(e){return e}:u,d="["+i(r.endsWith||"")+"]|$",p="["+i(r.delimiter||"/#?")+"]",h=void 0===o||o?"^":"",f=0;f<e.length;f++){var m=e[f];if("string"==typeof m)h+=i(c(m));else{var g=i(c(m.prefix)),_=i(c(m.suffix));if(m.pattern)if(t&&t.push(m),g||_)if("+"===m.modifier||"*"===m.modifier){var y="*"===m.modifier?"?":"";h+="(?:"+g+"((?:"+m.pattern+")(?:"+_+g+"(?:"+m.pattern+"))*)"+_+")"+y}else h+="(?:"+g+"("+m.pattern+")"+_+")"+m.modifier;else h+="("+m.pattern+")"+m.modifier;else h+="(?:"+g+_+")"+m.modifier}}if(void 0===l||l)s||(h+=p+"?"),h+=r.endsWith?"(?="+d+")":"$";else{var v=e[e.length-1],b="string"==typeof v?p.indexOf(v[v.length-1])>-1:void 0===v;s||(h+="(?:"+p+"(?="+d+"))?"),b||(h+="(?="+p+"|"+d+")")}return new RegExp(h,a(r))}function o(t,r,n){if(t instanceof RegExp){if(!r)return t;var i=t.source.match(/\((?!\?)/g);if(i)for(var l=0;l<i.length;l++)r.push({name:l,prefix:"",suffix:"",modifier:"",pattern:""});return t}return Array.isArray(t)?RegExp("(?:"+t.map(function(e){return o(e,r,n).source}).join("|")+")",a(n)):s(e(t,n),r,n)}Object.defineProperty(t,"__esModule",{value:!0}),t.parse=e,t.compile=function(t,n){return r(e(t,n),n)},t.tokensToFunction=r,t.match=function(e,t){var r=[];return n(o(e,r,t),r,t)},t.regexpToFunction=n,t.tokensToRegexp=s,t.pathToRegexp=o})(),e.exports=t})()},29663:(e,t,r)=>{"use strict";function n(e){return function(){let{cookie:t}=e;if(!t)return{};let{parse:n}=r(55040);return n(Array.isArray(t)?t.join("; "):t)}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getCookieParser",{enumerable:!0,get:function(){return n}})},30425:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createRouteLoader:function(){return g},getClientBuildManifest:function(){return f},isAssetError:function(){return c},markAssetError:function(){return u}}),r(72687),r(15358);let n=r(87233),i=r(21112),a=r(78757),s=r(44047);function o(e,t,r){let n,i=t.get(e);if(i)return"future"in i?i.future:Promise.resolve(i);let a=new Promise(e=>{n=e});return t.set(e,{resolve:n,future:a}),r?r().then(e=>(n(e),e)).catch(r=>{throw t.delete(e),r}):a}let l=Symbol("ASSET_LOAD_ERROR");function u(e){return Object.defineProperty(e,l,{})}function c(e){return e&&l in e}let d=function(e){try{return e=document.createElement("link"),!!window.MSInputMethodContext&&!!document.documentMode||e.relList.supports("prefetch")}catch(e){return!1}}(),p=()=>(0,a.getDeploymentIdQueryOrEmptyString)();function h(e,t,r){return new Promise((n,a)=>{let s=!1;e.then(e=>{s=!0,n(e)}).catch(a),(0,i.requestIdleCallback)(()=>setTimeout(()=>{s||a(r)},t))})}function f(){return self.__BUILD_MANIFEST?Promise.resolve(self.__BUILD_MANIFEST):h(new Promise(e=>{let t=self.__BUILD_MANIFEST_CB;self.__BUILD_MANIFEST_CB=()=>{e(self.__BUILD_MANIFEST),t&&t()}}),3800,u(Object.defineProperty(Error("Failed to load client build manifest"),"__NEXT_ERROR_CODE",{value:"E273",enumerable:!1,configurable:!0})))}function m(e,t){return f().then(r=>{if(!(t in r))throw u(Object.defineProperty(Error("Failed to lookup route: "+t),"__NEXT_ERROR_CODE",{value:"E446",enumerable:!1,configurable:!0}));let i=r[t].map(t=>e+"/_next/"+(0,s.encodeURIPath)(t));return{scripts:i.filter(e=>e.endsWith(".js")).map(e=>(0,n.__unsafeCreateTrustedScriptURL)(e)+p()),css:i.filter(e=>e.endsWith(".css")).map(e=>e+p())}})}function g(e){let t=new Map,r=new Map,n=new Map,a=new Map;function s(e){{var t;let n=r.get(e.toString());return n?n:document.querySelector('script[src^="'+e+'"]')?Promise.resolve():(r.set(e.toString(),n=new Promise((r,n)=>{(t=document.createElement("script")).onload=r,t.onerror=()=>n(u(Object.defineProperty(Error("Failed to load script: "+e),"__NEXT_ERROR_CODE",{value:"E74",enumerable:!1,configurable:!0}))),t.crossOrigin=void 0,t.src=e,document.body.appendChild(t)})),n)}}function l(e){let t=n.get(e);return t||n.set(e,t=fetch(e,{credentials:"same-origin"}).then(t=>{if(!t.ok)throw Object.defineProperty(Error("Failed to load stylesheet: "+e),"__NEXT_ERROR_CODE",{value:"E189",enumerable:!1,configurable:!0});return t.text().then(t=>({href:e,content:t}))}).catch(e=>{throw u(e)})),t}return{whenEntrypoint:e=>o(e,t),onEntrypoint(e,r){(r?Promise.resolve().then(()=>r()).then(e=>({component:e&&e.default||e,exports:e}),e=>({error:e})):Promise.resolve(void 0)).then(r=>{let n=t.get(e);n&&"resolve"in n?r&&(t.set(e,r),n.resolve(r)):(r?t.set(e,r):t.delete(e),a.delete(e))})},loadRoute(r,n){return o(r,a,()=>{let i;return h(m(e,r).then(e=>{let{scripts:n,css:i}=e;return Promise.all([t.has(r)?[]:Promise.all(n.map(s)),Promise.all(i.map(l))])}).then(e=>this.whenEntrypoint(r).then(t=>({entrypoint:t,styles:e[1]}))),3800,u(Object.defineProperty(Error("Route did not complete loading: "+r),"__NEXT_ERROR_CODE",{value:"E12",enumerable:!1,configurable:!0}))).then(e=>{let{entrypoint:t,styles:r}=e,n=Object.assign({styles:r},t);return"error"in t?t:n}).catch(e=>{if(n)throw e;return{error:e}}).finally(()=>null==i?void 0:i())})},prefetch(t){let r;return(r=navigator.connection)&&(r.saveData||/2g/.test(r.effectiveType))?Promise.resolve():m(e,t).then(e=>Promise.all(d?e.scripts.map(e=>{var t,r,n;return t=e.toString(),r="script",new Promise((e,i)=>{let a='\n      link[rel="prefetch"][href^="'+t+'"],\n      link[rel="preload"][href^="'+t+'"],\n      script[src^="'+t+'"]';if(document.querySelector(a))return e();n=document.createElement("link"),r&&(n.as=r),n.rel="prefetch",n.crossOrigin=void 0,n.onload=e,n.onerror=()=>i(u(Object.defineProperty(Error("Failed to prefetch: "+t),"__NEXT_ERROR_CODE",{value:"E268",enumerable:!1,configurable:!0}))),n.href=t,document.head.appendChild(n)})}):[])).then(()=>{(0,i.requestIdleCallback)(()=>this.loadRoute(t,!0).catch(()=>{}))}).catch(()=>{})}}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},31277:(e,t)=>{"use strict";function r(e,t){let r={};return Object.keys(e).forEach(n=>{t.includes(n)||(r[n]=e[n])}),r}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"omit",{enumerable:!0,get:function(){return r}})},32911:(e,t,r)=>{"use strict";globalThis._sentryRewritesTunnelPath="/monitoring",globalThis.SENTRY_RELEASE={id:"4f114066cfffc2184b89e81ba9495ab6e8e5cf06"},globalThis._sentryBasePath=void 0,globalThis._sentryRewriteFramesAssetPrefixPath="",globalThis._sentryAssetPrefix=void 0,globalThis._sentryExperimentalThirdPartyOriginStackFrames=void 0,globalThis._sentryRouteManifest='{"dynamicRoutes":[{"path":"/my-courses/:courseId","regex":"^/my-courses/([^/]+)$","paramNames":["courseId"]},{"path":"/my-courses/:courseId/exam","regex":"^/my-courses/([^/]+)/exam$","paramNames":["courseId"]},{"path":"/my-courses/:courseId/exam/results","regex":"^/my-courses/([^/]+)/exam/results$","paramNames":["courseId"]},{"path":"/auth/sign-in/:sign-in*?","regex":"^/auth/sign-in(?:/(.*))?$","paramNames":["sign-in"]},{"path":"/auth/sign-up/:sign-up*?","regex":"^/auth/sign-up(?:/(.*))?$","paramNames":["sign-up"]},{"path":"/dashboard/admin/institutions/:id","regex":"^/dashboard/admin/institutions/([^/]+)$","paramNames":["id"]},{"path":"/dashboard/admin/users/:id","regex":"^/dashboard/admin/users/([^/]+)$","paramNames":["id"]},{"path":"/dashboard/product/:productId","regex":"^/dashboard/product/([^/]+)$","paramNames":["productId"]},{"path":"/dashboard/profile/:profile*?","regex":"^/dashboard/profile(?:/(.*))?$","paramNames":["profile"]},{"path":"/dashboard/student/courses/:id","regex":"^/dashboard/student/courses/([^/]+)$","paramNames":["id"]},{"path":"/dashboard/teacher/classes/:id","regex":"^/dashboard/teacher/classes/([^/]+)$","paramNames":["id"]},{"path":"/dashboard/teacher/classes/:id/courses","regex":"^/dashboard/teacher/classes/([^/]+)/courses$","paramNames":["id"]},{"path":"/dashboard/teacher/classes/:id/students","regex":"^/dashboard/teacher/classes/([^/]+)/students$","paramNames":["id"]},{"path":"/dashboard/teacher/courses/:id","regex":"^/dashboard/teacher/courses/([^/]+)$","paramNames":["id"]}],"staticRoutes":[{"path":"/"},{"path":"/courses"},{"path":"/my-courses"},{"path":"/dashboard"},{"path":"/dashboard/admin"},{"path":"/dashboard/admin/institutions"},{"path":"/dashboard/admin/institutions/new"},{"path":"/dashboard/admin/subscriptions"},{"path":"/dashboard/admin/users"},{"path":"/dashboard/admin/users/new"},{"path":"/dashboard/kanban"},{"path":"/dashboard/overview/@area_stats"},{"path":"/dashboard/overview/@bar_stats"},{"path":"/dashboard/overview/@pie_stats"},{"path":"/dashboard/overview/@sales"},{"path":"/dashboard/product"},{"path":"/dashboard/student"},{"path":"/dashboard/student/certificates"},{"path":"/dashboard/student/courses"},{"path":"/dashboard/student/progress"},{"path":"/dashboard/teacher"},{"path":"/dashboard/teacher/classes"},{"path":"/dashboard/teacher/classes/new"},{"path":"/dashboard/teacher/courses"},{"path":"/dashboard/teacher/courses/generate"},{"path":"/dashboard/teacher/courses/new"},{"path":"/dashboard/teacher/reports"}]}',e.exports=r(92808)},37535:(e,t,r)=>{"use strict";function n(e,t){return e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeLocale",{enumerable:!0,get:function(){return n}}),r(82585),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},38089:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{copyNextErrorCode:function(){return n},createDigestWithErrorCode:function(){return r},extractNextErrorCode:function(){return i}});let r=(e,t)=>"object"==typeof e&&null!==e&&"__NEXT_ERROR_CODE"in e?`${t}@${e.__NEXT_ERROR_CODE}`:t,n=(e,t)=>{let r=i(e);r&&"object"==typeof t&&null!==t&&Object.defineProperty(t,"__NEXT_ERROR_CODE",{value:r,enumerable:!1,configurable:!0})},i=e=>"object"==typeof e&&null!==e&&"__NEXT_ERROR_CODE"in e&&"string"==typeof e.__NEXT_ERROR_CODE?e.__NEXT_ERROR_CODE:"object"==typeof e&&null!==e&&"digest"in e&&"string"==typeof e.digest?e.digest.split("@").find(e=>e.startsWith("E")):void 0},39116:(e,t,r)=>{"use strict";function n(e){return e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeBasePath",{enumerable:!0,get:function(){return n}}),r(92860),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},39308:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ACTION_SUFFIX:function(){return d},APP_DIR_ALIAS:function(){return M},CACHE_ONE_YEAR:function(){return w},DOT_NEXT_ALIAS:function(){return I},ESLINT_DEFAULT_DIRS:function(){return K},GSP_NO_RETURNED_VALUE:function(){return q},GSSP_COMPONENT_MEMBER_ERROR:function(){return G},GSSP_NO_RETURNED_VALUE:function(){return z},INFINITE_CACHE:function(){return R},INSTRUMENTATION_HOOK_FILENAME:function(){return x},MATCHED_PATH_HEADER:function(){return i},MIDDLEWARE_FILENAME:function(){return P},MIDDLEWARE_LOCATION_REGEXP:function(){return T},NEXT_BODY_SUFFIX:function(){return f},NEXT_CACHE_IMPLICIT_TAG_ID:function(){return S},NEXT_CACHE_REVALIDATED_TAGS_HEADER:function(){return g},NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER:function(){return _},NEXT_CACHE_SOFT_TAG_MAX_LENGTH:function(){return E},NEXT_CACHE_TAGS_HEADER:function(){return m},NEXT_CACHE_TAG_MAX_ITEMS:function(){return v},NEXT_CACHE_TAG_MAX_LENGTH:function(){return b},NEXT_DATA_SUFFIX:function(){return p},NEXT_INTERCEPTION_MARKER_PREFIX:function(){return n},NEXT_META_SUFFIX:function(){return h},NEXT_QUERY_PARAM_PREFIX:function(){return r},NEXT_RESUME_HEADER:function(){return y},NON_STANDARD_NODE_ENV:function(){return V},PAGES_DIR_ALIAS:function(){return O},PRERENDER_REVALIDATE_HEADER:function(){return a},PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER:function(){return s},PUBLIC_DIR_MIDDLEWARE_CONFLICT:function(){return U},ROOT_DIR_ALIAS:function(){return C},RSC_ACTION_CLIENT_WRAPPER_ALIAS:function(){return D},RSC_ACTION_ENCRYPTION_ALIAS:function(){return L},RSC_ACTION_PROXY_ALIAS:function(){return N},RSC_ACTION_VALIDATE_ALIAS:function(){return A},RSC_CACHE_WRAPPER_ALIAS:function(){return j},RSC_MOD_REF_PROXY_ALIAS:function(){return k},RSC_PREFETCH_SUFFIX:function(){return o},RSC_SEGMENTS_DIR_SUFFIX:function(){return l},RSC_SEGMENT_SUFFIX:function(){return u},RSC_SUFFIX:function(){return c},SERVER_PROPS_EXPORT_ERROR:function(){return W},SERVER_PROPS_GET_INIT_PROPS_CONFLICT:function(){return B},SERVER_PROPS_SSG_CONFLICT:function(){return $},SERVER_RUNTIME:function(){return J},SSG_FALLBACK_EXPORT_ERROR:function(){return Y},SSG_GET_INITIAL_PROPS_CONFLICT:function(){return F},STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR:function(){return H},UNSTABLE_REVALIDATE_RENAME_ERROR:function(){return X},WEBPACK_LAYERS:function(){return Z},WEBPACK_RESOURCE_QUERIES:function(){return ee}});let r="nxtP",n="nxtI",i="x-matched-path",a="x-prerender-revalidate",s="x-prerender-revalidate-if-generated",o=".prefetch.rsc",l=".segments",u=".segment.rsc",c=".rsc",d=".action",p=".json",h=".meta",f=".body",m="x-next-cache-tags",g="x-next-revalidated-tags",_="x-next-revalidate-tag-token",y="next-resume",v=128,b=256,E=1024,S="_N_T_",w=31536e3,R=0xfffffffe,P="middleware",T=`(?:src/)?${P}`,x="instrumentation",O="private-next-pages",I="private-dot-next",C="private-next-root-dir",M="private-next-app-dir",k="private-next-rsc-mod-ref-proxy",A="private-next-rsc-action-validate",N="private-next-rsc-server-reference",j="private-next-rsc-cache-wrapper",L="private-next-rsc-action-encryption",D="private-next-rsc-action-client-wrapper",U="You can not have a '_next' folder inside of your public folder. This conflicts with the internal '/_next' route. https://nextjs.org/docs/messages/public-next-folder-conflict",F="You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps",B="You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.",$="You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps",H="can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props",W="pages with `getServerSideProps` can not be exported. See more info here: https://nextjs.org/docs/messages/gssp-export",q="Your `getStaticProps` function did not return an object. Did you forget to add a `return`?",z="Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?",X="The `unstable_revalidate` property is available for general use.\nPlease use `revalidate` instead.",G="can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member",V='You are using a non-standard "NODE_ENV" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env',Y="Pages with `fallback` enabled in `getStaticPaths` can not be exported. See more info here: https://nextjs.org/docs/messages/ssg-fallback-true-export",K=["app","pages","components","lib","src"],J={edge:"edge",experimentalEdge:"experimental-edge",nodejs:"nodejs"},Q={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",apiNode:"api-node",apiEdge:"api-edge",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",pagesDirBrowser:"pages-dir-browser",pagesDirEdge:"pages-dir-edge",pagesDirNode:"pages-dir-node"},Z={...Q,GROUP:{builtinReact:[Q.reactServerComponents,Q.actionBrowser],serverOnly:[Q.reactServerComponents,Q.actionBrowser,Q.instrument,Q.middleware],neutralTarget:[Q.apiNode,Q.apiEdge],clientOnly:[Q.serverSideRendering,Q.appPagesBrowser],bundled:[Q.reactServerComponents,Q.actionBrowser,Q.serverSideRendering,Q.appPagesBrowser,Q.shared,Q.instrument,Q.middleware],appPages:[Q.reactServerComponents,Q.serverSideRendering,Q.appPagesBrowser,Q.actionBrowser]}},ee={edgeSSREntry:"__next_edge_ssr_entry__",metadata:"__next_metadata__",metadataRoute:"__next_metadata_route__",metadataImageMeta:"__next_metadata_image_meta__"}},40342:(e,t)=>{"use strict";function r(e){return e.replace(/\\/g,"/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathSep",{enumerable:!0,get:function(){return r}})},40840:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return i}});let n=r(82585);function i(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:i,hash:a}=(0,n.parsePath)(e);return""+t+r+i+a}},42133:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"detectDomainLocale",{enumerable:!0,get:function(){return r}});let r=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r]};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},42560:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addLocale",{enumerable:!0,get:function(){return a}});let n=r(40840),i=r(321);function a(e,t,r,a){if(!t||t===r)return e;let s=e.toLowerCase();return!a&&((0,i.pathHasPrefix)(s,"/api")||(0,i.pathHasPrefix)(s,"/"+t.toLowerCase()))?e:(0,n.addPathPrefix)(e,"/"+t)}},43004:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"reportGlobalError",{enumerable:!0,get:function(){return r}});let r="function"==typeof reportError?reportError:e=>{globalThis.console.error(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},44047:(e,t)=>{"use strict";function r(e){return e.split("/").map(e=>encodeURIComponent(e)).join("/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"encodeURIPath",{enumerable:!0,get:function(){return r}})},46175:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),r(63021),r(32911);let n=r(16450);window.next={version:n.version,get router(){return n.router},emitter:n.emitter},(0,n.initialize)({}).then(()=>(0,n.hydrate)()).catch(console.error),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},48076:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BloomFilter",{enumerable:!0,get:function(){return r}});class r{static from(e,t){void 0===t&&(t=1e-4);let n=new r(e.length,t);for(let t of e)n.add(t);return n}export(){return{numItems:this.numItems,errorRate:this.errorRate,numBits:this.numBits,numHashes:this.numHashes,bitArray:this.bitArray}}import(e){this.numItems=e.numItems,this.errorRate=e.errorRate,this.numBits=e.numBits,this.numHashes=e.numHashes,this.bitArray=e.bitArray}add(e){this.getHashValues(e).forEach(e=>{this.bitArray[e]=1})}contains(e){return this.getHashValues(e).every(e=>this.bitArray[e])}getHashValues(e){let t=[];for(let r=1;r<=this.numHashes;r++){let n=function(e){let t=0;for(let r=0;r<e.length;r++)t=Math.imul(t^e.charCodeAt(r),0x5bd1e995),t^=t>>>13,t=Math.imul(t,0x5bd1e995);return t>>>0}(""+e+r)%this.numBits;t.push(n)}return t}constructor(e,t=1e-4){this.numItems=e,this.errorRate=t,this.numBits=Math.ceil(-(e*Math.log(t))/(Math.log(2)*Math.log(2))),this.numHashes=Math.ceil(this.numBits/e*Math.log(2)),this.bitArray=Array(this.numBits).fill(0)}}},49452:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"onRecoverableError",{enumerable:!0,get:function(){return l}});let n=r(72687),i=r(66336),a=r(43004),s=r(57766),o=n._(r(66240)),l=(e,t)=>{let r=(0,o.default)(e)&&"cause"in e?e.cause:e,n=(0,s.getReactStitchedError)(r);(0,i.isBailoutToCSRError)(r)||(0,a.reportGlobalError)(n)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},50563:e=>{"use strict";e.exports=["chrome 64","edge 79","firefox 67","opera 51","safari 12"]},50781:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return i},extractInterceptionRouteInformation:function(){return s},isInterceptionRouteAppPath:function(){return a}});let n=r(2786),i=["(..)(..)","(.)","(..)","(...)"];function a(e){return void 0!==e.split("/").find(e=>i.find(t=>e.startsWith(t)))}function s(e){let t,r,a;for(let n of e.split("/"))if(r=i.find(e=>n.startsWith(e))){[t,a]=e.split(r,2);break}if(!t||!r||!a)throw Object.defineProperty(Error("Invalid interception route: "+e+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(t=(0,n.normalizeAppPath)(t),r){case"(.)":a="/"===t?"/"+a:t+"/"+a;break;case"(..)":if("/"===t)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});a=t.split("/").slice(0,-1).concat(a).join("/");break;case"(...)":a="/"+a;break;case"(..)(..)":let s=t.split("/");if(s.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});a=s.slice(0,-2).concat(a).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:t,interceptedRoute:a}}},51082:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return i}});let n=r(1033);function i(e){let{re:t,groups:r}=e;return e=>{let i=t.exec(e);if(!i)return!1;let a=e=>{try{return decodeURIComponent(e)}catch(e){throw Object.defineProperty(new n.DecodeError("failed to decode param"),"__NEXT_ERROR_CODE",{value:"E528",enumerable:!1,configurable:!0})}},s={};for(let[e,t]of Object.entries(r)){let r=i[t.pos];void 0!==r&&(t.repeat?s[e]=r.split("/").map(e=>a(e)):s[e]=a(r))}return s}}},54267:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createKey:function(){return z},default:function(){return V},matchesMiddleware:function(){return D}});let n=r(72687),i=r(63732),a=r(69239),s=r(30425),o=r(87665),l=i._(r(66240)),u=r(88524),c=r(83847),d=n._(r(24622)),p=r(1033),h=r(88668),f=r(5402),m=n._(r(90086)),g=r(51082),_=r(70159),y=r(359);r(42133);let v=r(82585),b=r(89266),E=r(37535),S=r(39116),w=r(69011),R=r(92860),P=r(58004),T=r(2326),x=r(17136),O=r(72129),I=r(99328),C=r(65790),M=r(4316),k=r(31277),A=r(20718),N=r(15047),j=r(39308);function L(){return Object.assign(Object.defineProperty(Error("Route Cancelled"),"__NEXT_ERROR_CODE",{value:"E315",enumerable:!1,configurable:!0}),{cancelled:!0})}async function D(e){let t=await Promise.resolve(e.router.pageLoader.getMiddleware());if(!t)return!1;let{pathname:r}=(0,v.parsePath)(e.asPath),n=(0,R.hasBasePath)(r)?(0,S.removeBasePath)(r):r,i=(0,w.addBasePath)((0,b.addLocale)(n,e.locale));return t.some(e=>new RegExp(e.regexp).test(i))}function U(e){let t=(0,p.getLocationOrigin)();return e.startsWith(t)?e.substring(t.length):e}function F(e,t,r){let[n,i]=(0,P.resolveHref)(e,t,!0),a=(0,p.getLocationOrigin)(),s=n.startsWith(a),o=i&&i.startsWith(a);n=U(n),i=i?U(i):i;let l=s?n:(0,w.addBasePath)(n),u=r?U((0,P.resolveHref)(e,r)):i||n;return{url:l,as:o?u:(0,w.addBasePath)(u)}}function B(e,t){let r=(0,a.removeTrailingSlash)((0,u.denormalizePagePath)(e));return"/404"===r||"/_error"===r?e:(t.includes(r)||t.some(t=>{if((0,h.isDynamicRoute)(t)&&(0,_.getRouteRegex)(t).re.test(r))return e=t,!0}),(0,a.removeTrailingSlash)(e))}async function $(e){if(!await D(e)||!e.fetchData)return null;let t=await e.fetchData(),r=await function(e,t,r){let n={basePath:r.router.basePath,i18n:{locales:r.router.locales},trailingSlash:!1},i=t.headers.get("x-nextjs-rewrite"),o=i||t.headers.get("x-nextjs-matched-path"),l=t.headers.get(j.MATCHED_PATH_HEADER);if(!l||o||l.includes("__next_data_catchall")||l.includes("/_error")||l.includes("/404")||(o=l),o){if(o.startsWith("/")){let t=(0,f.parseRelativeUrl)(o),l=(0,x.getNextPathnameInfo)(t.pathname,{nextConfig:n,parseData:!0}),u=(0,a.removeTrailingSlash)(l.pathname);return Promise.all([r.router.pageLoader.getPageList(),(0,s.getClientBuildManifest)()]).then(n=>{let[a,{__rewrites:s}]=n,o=(0,b.addLocale)(l.pathname,l.locale);if((0,h.isDynamicRoute)(o)||!i&&a.includes((0,c.normalizeLocalePath)((0,S.removeBasePath)(o),r.router.locales).pathname)){let r=(0,x.getNextPathnameInfo)((0,f.parseRelativeUrl)(e).pathname,{nextConfig:void 0,parseData:!0});t.pathname=o=(0,w.addBasePath)(r.pathname)}{let e=(0,m.default)(o,a,s,t.query,e=>B(e,a),r.router.locales);e.matchedPage&&(t.pathname=e.parsedAs.pathname,o=t.pathname,Object.assign(t.query,e.parsedAs.query))}let d=a.includes(u)?u:B((0,c.normalizeLocalePath)((0,S.removeBasePath)(t.pathname),r.router.locales).pathname,a);if((0,h.isDynamicRoute)(d)){let e=(0,g.getRouteMatcher)((0,_.getRouteRegex)(d))(o);Object.assign(t.query,e||{})}return{type:"rewrite",parsedAs:t,resolvedHref:d}})}let t=(0,v.parsePath)(e);return Promise.resolve({type:"redirect-external",destination:""+(0,O.formatNextPathnameInfo)({...(0,x.getNextPathnameInfo)(t.pathname,{nextConfig:n,parseData:!0}),defaultLocale:r.router.defaultLocale,buildId:""})+t.query+t.hash})}let u=t.headers.get("x-nextjs-redirect");if(u){if(u.startsWith("/")){let e=(0,v.parsePath)(u),t=(0,O.formatNextPathnameInfo)({...(0,x.getNextPathnameInfo)(e.pathname,{nextConfig:n,parseData:!0}),defaultLocale:r.router.defaultLocale,buildId:""});return Promise.resolve({type:"redirect-internal",newAs:""+t+e.query+e.hash,newUrl:""+t+e.query+e.hash})}return Promise.resolve({type:"redirect-external",destination:u})}return Promise.resolve({type:"next"})}(t.dataHref,t.response,e);return{dataHref:t.dataHref,json:t.json,response:t.response,text:t.text,cacheKey:t.cacheKey,effect:r}}let H=Symbol("SSG_DATA_NOT_FOUND");function W(e){try{return JSON.parse(e)}catch(e){return null}}function q(e){let{dataHref:t,inflightCache:r,isPrefetch:n,hasMiddleware:i,isServerRender:a,parseJSON:o,persistCache:l,isBackground:u,unstable_skipClientCache:c}=e,{href:d}=new URL(t,window.location.href),p=e=>{var u;return(function e(t,r,n){return fetch(t,{credentials:"same-origin",method:n.method||"GET",headers:Object.assign({},n.headers,{"x-nextjs-data":"1"})}).then(i=>!i.ok&&r>1&&i.status>=500?e(t,r-1,n):i)})(t,a?3:1,{headers:Object.assign({},n?{purpose:"prefetch"}:{},n&&i?{"x-middleware-prefetch":"1"}:{},{}),method:null!=(u=null==e?void 0:e.method)?u:"GET"}).then(r=>r.ok&&(null==e?void 0:e.method)==="HEAD"?{dataHref:t,response:r,text:"",json:{},cacheKey:d}:r.text().then(e=>{if(!r.ok){if(i&&[301,302,307,308].includes(r.status))return{dataHref:t,response:r,text:e,json:{},cacheKey:d};if(404===r.status){var n;if(null==(n=W(e))?void 0:n.notFound)return{dataHref:t,json:{notFound:H},response:r,text:e,cacheKey:d}}let o=Object.defineProperty(Error("Failed to load static props"),"__NEXT_ERROR_CODE",{value:"E124",enumerable:!1,configurable:!0});throw a||(0,s.markAssetError)(o),o}return{dataHref:t,json:o?W(e):null,response:r,text:e,cacheKey:d}})).then(e=>(l&&"no-cache"!==e.response.headers.get("x-middleware-cache")||delete r[d],e)).catch(e=>{throw c||delete r[d],("Failed to fetch"===e.message||"NetworkError when attempting to fetch resource."===e.message||"Load failed"===e.message)&&(0,s.markAssetError)(e),e})};return c&&l?p({}).then(e=>("no-cache"!==e.response.headers.get("x-middleware-cache")&&(r[d]=Promise.resolve(e)),e)):void 0!==r[d]?r[d]:r[d]=p(u?{method:"HEAD"}:{})}function z(){return Math.random().toString(36).slice(2,10)}function X(e){let{url:t,router:r}=e;if(t===(0,w.addBasePath)((0,b.addLocale)(r.asPath,r.locale)))throw Object.defineProperty(Error("Invariant: attempted to hard navigate to the same URL "+t+" "+location.href),"__NEXT_ERROR_CODE",{value:"E282",enumerable:!1,configurable:!0});window.location.href=t}let G=e=>{let{route:t,router:r}=e,n=!1,i=r.clc=()=>{n=!0};return()=>{if(n){let e=Object.defineProperty(Error('Abort fetching component for route: "'+t+'"'),"__NEXT_ERROR_CODE",{value:"E483",enumerable:!1,configurable:!0});throw e.cancelled=!0,e}i===r.clc&&(r.clc=null)}};class V{reload(){window.location.reload()}back(){window.history.back()}forward(){window.history.forward()}push(e,t,r){return void 0===r&&(r={}),{url:e,as:t}=F(this,e,t),this.change("pushState",e,t,r)}replace(e,t,r){return void 0===r&&(r={}),{url:e,as:t}=F(this,e,t),this.change("replaceState",e,t,r)}async _bfl(e,t,n,i){{if(!this._bfl_s&&!this._bfl_d){let t,a,{BloomFilter:o}=r(48076);try{({__routerFilterStatic:t,__routerFilterDynamic:a}=await (0,s.getClientBuildManifest)())}catch(t){if(console.error(t),i)return!0;return X({url:(0,w.addBasePath)((0,b.addLocale)(e,n||this.locale,this.defaultLocale)),router:this}),new Promise(()=>{})}(null==t?void 0:t.numHashes)&&(this._bfl_s=new o(t.numItems,t.errorRate),this._bfl_s.import(t)),(null==a?void 0:a.numHashes)&&(this._bfl_d=new o(a.numItems,a.errorRate),this._bfl_d.import(a))}let c=!1,d=!1;for(let{as:r,allowMatchCurrent:s}of[{as:e},{as:t}])if(r){let t=(0,a.removeTrailingSlash)(new URL(r,"http://n").pathname),p=(0,w.addBasePath)((0,b.addLocale)(t,n||this.locale));if(s||t!==(0,a.removeTrailingSlash)(new URL(this.asPath,"http://n").pathname)){var o,l,u;for(let e of(c=c||!!(null==(o=this._bfl_s)?void 0:o.contains(t))||!!(null==(l=this._bfl_s)?void 0:l.contains(p)),[t,p])){let t=e.split("/");for(let e=0;!d&&e<t.length+1;e++){let r=t.slice(0,e).join("/");if(r&&(null==(u=this._bfl_d)?void 0:u.contains(r))){d=!0;break}}}if(c||d){if(i)return!0;return X({url:(0,w.addBasePath)((0,b.addLocale)(e,n||this.locale,this.defaultLocale)),router:this}),new Promise(()=>{})}}}}return!1}async change(e,t,r,n,i){var u,c,d,P,T,x,O,M,N;let j,U;if(!(0,C.isLocalURL)(t))return X({url:t,router:this}),!1;let $=1===n._h;$||n.shallow||await this._bfl(r,void 0,n.locale);let W=$||n._shouldResolveHref||(0,v.parsePath)(t).pathname===(0,v.parsePath)(r).pathname,q={...this.state},z=!0!==this.isReady;this.isReady=!0;let G=this.isSsr;if($||(this.isSsr=!1),$&&this.clc)return!1;let Y=q.locale;p.ST&&performance.mark("routeChange");let{shallow:K=!1,scroll:J=!0}=n,Q={shallow:K};this._inFlightRoute&&this.clc&&(G||V.events.emit("routeChangeError",L(),this._inFlightRoute,Q),this.clc(),this.clc=null),r=(0,w.addBasePath)((0,b.addLocale)((0,R.hasBasePath)(r)?(0,S.removeBasePath)(r):r,n.locale,this.defaultLocale));let Z=(0,E.removeLocale)((0,R.hasBasePath)(r)?(0,S.removeBasePath)(r):r,q.locale);this._inFlightRoute=r;let ee=Y!==q.locale;if(!$&&this.onlyAHashChange(Z)&&!ee){q.asPath=Z,V.events.emit("hashChangeStart",r,Q),this.changeState(e,t,r,{...n,scroll:!1}),J&&this.scrollToHash(Z);try{await this.set(q,this.components[q.route],null)}catch(e){throw(0,l.default)(e)&&e.cancelled&&V.events.emit("routeChangeError",e,Z,Q),e}return V.events.emit("hashChangeComplete",r,Q),!0}let et=(0,f.parseRelativeUrl)(t),{pathname:er,query:en}=et;try{[j,{__rewrites:U}]=await Promise.all([this.pageLoader.getPageList(),(0,s.getClientBuildManifest)(),this.pageLoader.getMiddleware()])}catch(e){return X({url:r,router:this}),!1}this.urlIsNew(Z)||ee||(e="replaceState");let ei=r;er=er?(0,a.removeTrailingSlash)((0,S.removeBasePath)(er)):er;let ea=(0,a.removeTrailingSlash)(er),es=r.startsWith("/")&&(0,f.parseRelativeUrl)(r).pathname;if(null==(u=this.components[er])?void 0:u.__appRouter)return X({url:r,router:this}),new Promise(()=>{});let eo=!!(es&&ea!==es&&(!(0,h.isDynamicRoute)(ea)||!(0,g.getRouteMatcher)((0,_.getRouteRegex)(ea))(es))),el=!n.shallow&&await D({asPath:r,locale:q.locale,router:this});if($&&el&&(W=!1),W&&"/_error"!==er)if(n._shouldResolveHref=!0,r.startsWith("/")){let e=(0,m.default)((0,w.addBasePath)((0,b.addLocale)(Z,q.locale),!0),j,U,en,e=>B(e,j),this.locales);if(e.externalDest)return X({url:r,router:this}),!0;el||(ei=e.asPath),e.matchedPage&&e.resolvedHref&&(er=e.resolvedHref,et.pathname=(0,w.addBasePath)(er),el||(t=(0,y.formatWithValidation)(et)))}else et.pathname=B(er,j),et.pathname!==er&&(er=et.pathname,et.pathname=(0,w.addBasePath)(er),el||(t=(0,y.formatWithValidation)(et)));if(!(0,C.isLocalURL)(r))return X({url:r,router:this}),!1;ei=(0,E.removeLocale)((0,S.removeBasePath)(ei),q.locale),ea=(0,a.removeTrailingSlash)(er);let eu=!1;if((0,h.isDynamicRoute)(ea)){let e=(0,f.parseRelativeUrl)(ei),n=e.pathname,i=(0,_.getRouteRegex)(ea);eu=(0,g.getRouteMatcher)(i)(n);let a=ea===n,s=a?(0,A.interpolateAs)(ea,n,en):{};if(eu&&(!a||s.result))a?r=(0,y.formatWithValidation)(Object.assign({},e,{pathname:s.result,query:(0,k.omit)(en,s.params)})):Object.assign(en,eu);else{let e=Object.keys(i.groups).filter(e=>!en[e]&&!i.groups[e].optional);if(e.length>0&&!el)throw Object.defineProperty(Error((a?"The provided `href` ("+t+") value is missing query values ("+e.join(", ")+") to be interpolated properly. ":"The provided `as` value ("+n+") is incompatible with the `href` value ("+ea+"). ")+"Read more: https://nextjs.org/docs/messages/"+(a?"href-interpolation-failed":"incompatible-href-as")),"__NEXT_ERROR_CODE",{value:"E344",enumerable:!1,configurable:!0})}}$||V.events.emit("routeChangeStart",r,Q);let ec="/404"===this.pathname||"/_error"===this.pathname;try{let a=await this.getRouteInfo({route:ea,pathname:er,query:en,as:r,resolvedAs:ei,routeProps:Q,locale:q.locale,isPreview:q.isPreview,hasMiddleware:el,unstable_skipClientCache:n.unstable_skipClientCache,isQueryUpdating:$&&!this.isFallback,isMiddlewareRewrite:eo});if($||n.shallow||await this._bfl(r,"resolvedAs"in a?a.resolvedAs:void 0,q.locale),"route"in a&&el){ea=er=a.route||ea,Q.shallow||(en=Object.assign({},a.query||{},en));let e=(0,R.hasBasePath)(et.pathname)?(0,S.removeBasePath)(et.pathname):et.pathname;if(eu&&er!==e&&Object.keys(eu).forEach(e=>{eu&&en[e]===eu[e]&&delete en[e]}),(0,h.isDynamicRoute)(er)){let e=!Q.shallow&&a.resolvedAs?a.resolvedAs:(0,w.addBasePath)((0,b.addLocale)(new URL(r,location.href).pathname,q.locale),!0);(0,R.hasBasePath)(e)&&(e=(0,S.removeBasePath)(e));let t=(0,_.getRouteRegex)(er),n=(0,g.getRouteMatcher)(t)(new URL(e,location.href).pathname);n&&Object.assign(en,n)}}if("type"in a)if("redirect-internal"===a.type)return this.change(e,a.newUrl,a.newAs,n);else return X({url:a.destination,router:this}),new Promise(()=>{});let s=a.Component;if(s&&s.unstable_scriptLoader&&[].concat(s.unstable_scriptLoader()).forEach(e=>{(0,o.handleClientScriptLoad)(e.props)}),(a.__N_SSG||a.__N_SSP)&&a.props){if(a.props.pageProps&&a.props.pageProps.__N_REDIRECT){n.locale=!1;let t=a.props.pageProps.__N_REDIRECT;if(t.startsWith("/")&&!1!==a.props.pageProps.__N_REDIRECT_BASE_PATH){let r=(0,f.parseRelativeUrl)(t);r.pathname=B(r.pathname,j);let{url:i,as:a}=F(this,t,t);return this.change(e,i,a,n)}return X({url:t,router:this}),new Promise(()=>{})}if(q.isPreview=!!a.props.__N_PREVIEW,a.props.notFound===H){let e;try{await this.fetchComponent("/404"),e="/404"}catch(t){e="/_error"}if(a=await this.getRouteInfo({route:e,pathname:e,query:en,as:r,resolvedAs:ei,routeProps:{shallow:!1},locale:q.locale,isPreview:q.isPreview,isNotFound:!0}),"type"in a)throw Object.defineProperty(Error("Unexpected middleware effect on /404"),"__NEXT_ERROR_CODE",{value:"E158",enumerable:!1,configurable:!0})}}$&&"/_error"===this.pathname&&(null==(d=self.__NEXT_DATA__.props)||null==(c=d.pageProps)?void 0:c.statusCode)===500&&(null==(P=a.props)?void 0:P.pageProps)&&(a.props.pageProps.statusCode=500);let u=n.shallow&&q.route===(null!=(T=a.route)?T:ea),p=null!=(x=n.scroll)?x:!$&&!u,m=null!=i?i:p?{x:0,y:0}:null,y={...q,route:ea,pathname:er,query:en,asPath:Z,isFallback:!1};if($&&ec){if(a=await this.getRouteInfo({route:this.pathname,pathname:this.pathname,query:en,as:r,resolvedAs:ei,routeProps:{shallow:!1},locale:q.locale,isPreview:q.isPreview,isQueryUpdating:$&&!this.isFallback}),"type"in a)throw Object.defineProperty(Error("Unexpected middleware effect on "+this.pathname),"__NEXT_ERROR_CODE",{value:"E225",enumerable:!1,configurable:!0});"/_error"===this.pathname&&(null==(M=self.__NEXT_DATA__.props)||null==(O=M.pageProps)?void 0:O.statusCode)===500&&(null==(N=a.props)?void 0:N.pageProps)&&(a.props.pageProps.statusCode=500);try{await this.set(y,a,m)}catch(e){throw(0,l.default)(e)&&e.cancelled&&V.events.emit("routeChangeError",e,Z,Q),e}return!0}if(V.events.emit("beforeHistoryChange",r,Q),this.changeState(e,t,r,n),!($&&!m&&!z&&!ee&&(0,I.compareRouterStates)(y,this.state))){try{await this.set(y,a,m)}catch(e){if(e.cancelled)a.error=a.error||e;else throw e}if(a.error)throw $||V.events.emit("routeChangeError",a.error,Z,Q),a.error;$||V.events.emit("routeChangeComplete",r,Q),p&&/#.+$/.test(r)&&this.scrollToHash(r)}return!0}catch(e){if((0,l.default)(e)&&e.cancelled)return!1;throw e}}changeState(e,t,r,n){void 0===n&&(n={}),("pushState"!==e||(0,p.getURL)()!==r)&&(this._shallow=n.shallow,window.history[e]({url:t,as:r,options:n,__N:!0,key:this._key="pushState"!==e?this._key:z()},"",r))}async handleRouteInfoError(e,t,r,n,i,a){if(e.cancelled)throw e;if((0,s.isAssetError)(e)||a)throw V.events.emit("routeChangeError",e,n,i),X({url:n,router:this}),L();console.error(e);try{let n,{page:i,styleSheets:a}=await this.fetchComponent("/_error"),s={props:n,Component:i,styleSheets:a,err:e,error:e};if(!s.props)try{s.props=await this.getInitialProps(i,{err:e,pathname:t,query:r})}catch(e){console.error("Error in error page `getInitialProps`: ",e),s.props={}}return s}catch(e){return this.handleRouteInfoError((0,l.default)(e)?e:Object.defineProperty(Error(e+""),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0}),t,r,n,i,!0)}}async getRouteInfo(e){let{route:t,pathname:r,query:n,as:i,resolvedAs:s,routeProps:o,locale:u,hasMiddleware:d,isPreview:p,unstable_skipClientCache:h,isQueryUpdating:f,isMiddlewareRewrite:m,isNotFound:g}=e,_=t;try{var v,b,E,w;let e=this.components[_];if(o.shallow&&e&&this.route===_)return e;let t=G({route:_,router:this});d&&(e=void 0);let l=!e||"initial"in e?void 0:e,R={dataHref:this.pageLoader.getDataHref({href:(0,y.formatWithValidation)({pathname:r,query:n}),skipInterpolation:!0,asPath:g?"/404":s,locale:u}),hasMiddleware:!0,isServerRender:this.isSsr,parseJSON:!0,inflightCache:f?this.sbc:this.sdc,persistCache:!p,isPrefetch:!1,unstable_skipClientCache:h,isBackground:f},P=f&&!m?null:await $({fetchData:()=>q(R),asPath:g?"/404":s,locale:u,router:this}).catch(e=>{if(f)return null;throw e});if(P&&("/_error"===r||"/404"===r)&&(P.effect=void 0),f&&(P?P.json=self.__NEXT_DATA__.props:P={json:self.__NEXT_DATA__.props}),t(),(null==P||null==(v=P.effect)?void 0:v.type)==="redirect-internal"||(null==P||null==(b=P.effect)?void 0:b.type)==="redirect-external")return P.effect;if((null==P||null==(E=P.effect)?void 0:E.type)==="rewrite"){let t=(0,a.removeTrailingSlash)(P.effect.resolvedHref),i=await this.pageLoader.getPageList();if((!f||i.includes(t))&&(_=t,r=P.effect.resolvedHref,n={...n,...P.effect.parsedAs.query},s=(0,S.removeBasePath)((0,c.normalizeLocalePath)(P.effect.parsedAs.pathname,this.locales).pathname),e=this.components[_],o.shallow&&e&&this.route===_&&!d))return{...e,route:_}}if((0,T.isAPIRoute)(_))return X({url:i,router:this}),new Promise(()=>{});let x=l||await this.fetchComponent(_).then(e=>({Component:e.page,styleSheets:e.styleSheets,__N_SSG:e.mod.__N_SSG,__N_SSP:e.mod.__N_SSP})),O=null==P||null==(w=P.response)?void 0:w.headers.get("x-middleware-skip"),I=x.__N_SSG||x.__N_SSP;O&&(null==P?void 0:P.dataHref)&&delete this.sdc[P.dataHref];let{props:C,cacheKey:M}=await this._getData(async()=>{if(I){if((null==P?void 0:P.json)&&!O)return{cacheKey:P.cacheKey,props:P.json};let e=(null==P?void 0:P.dataHref)?P.dataHref:this.pageLoader.getDataHref({href:(0,y.formatWithValidation)({pathname:r,query:n}),asPath:s,locale:u}),t=await q({dataHref:e,isServerRender:this.isSsr,parseJSON:!0,inflightCache:O?{}:this.sdc,persistCache:!p,isPrefetch:!1,unstable_skipClientCache:h});return{cacheKey:t.cacheKey,props:t.json||{}}}return{headers:{},props:await this.getInitialProps(x.Component,{pathname:r,query:n,asPath:i,locale:u,locales:this.locales,defaultLocale:this.defaultLocale})}});return x.__N_SSP&&R.dataHref&&M&&delete this.sdc[M],this.isPreview||!x.__N_SSG||f||q(Object.assign({},R,{isBackground:!0,persistCache:!1,inflightCache:this.sbc})).catch(()=>{}),C.pageProps=Object.assign({},C.pageProps),x.props=C,x.route=_,x.query=n,x.resolvedAs=s,this.components[_]=x,x}catch(e){return this.handleRouteInfoError((0,l.getProperError)(e),r,n,i,o)}}set(e,t,r){return this.state=e,this.sub(t,this.components["/_app"].Component,r)}beforePopState(e){this._bps=e}onlyAHashChange(e){if(!this.asPath)return!1;let[t,r]=this.asPath.split("#",2),[n,i]=e.split("#",2);return!!i&&t===n&&r===i||t===n&&r!==i}scrollToHash(e){let[,t=""]=e.split("#",2);(0,N.handleSmoothScroll)(()=>{if(""===t||"top"===t)return void window.scrollTo(0,0);let e=decodeURIComponent(t),r=document.getElementById(e);if(r)return void r.scrollIntoView();let n=document.getElementsByName(e)[0];n&&n.scrollIntoView()},{onlyHashChange:this.onlyAHashChange(e)})}urlIsNew(e){return this.asPath!==e}async prefetch(e,t,r){if(void 0===t&&(t=e),void 0===r&&(r={}),(0,M.isBot)(window.navigator.userAgent))return;let n=(0,f.parseRelativeUrl)(e),i=n.pathname,{pathname:o,query:l}=n,u=o,c=await this.pageLoader.getPageList(),d=t,p=void 0!==r.locale?r.locale||void 0:this.locale,R=await D({asPath:t,locale:p,router:this});if(t.startsWith("/")){let r;({__rewrites:r}=await (0,s.getClientBuildManifest)());let i=(0,m.default)((0,w.addBasePath)((0,b.addLocale)(t,this.locale),!0),c,r,n.query,e=>B(e,c),this.locales);if(i.externalDest)return;R||(d=(0,E.removeLocale)((0,S.removeBasePath)(i.asPath),this.locale)),i.matchedPage&&i.resolvedHref&&(n.pathname=o=i.resolvedHref,R||(e=(0,y.formatWithValidation)(n)))}n.pathname=B(n.pathname,c),(0,h.isDynamicRoute)(n.pathname)&&(o=n.pathname,n.pathname=o,Object.assign(l,(0,g.getRouteMatcher)((0,_.getRouteRegex)(n.pathname))((0,v.parsePath)(t).pathname)||{}),R||(e=(0,y.formatWithValidation)(n)));let P=await $({fetchData:()=>q({dataHref:this.pageLoader.getDataHref({href:(0,y.formatWithValidation)({pathname:u,query:l}),skipInterpolation:!0,asPath:d,locale:p}),hasMiddleware:!0,isServerRender:!1,parseJSON:!0,inflightCache:this.sdc,persistCache:!this.isPreview,isPrefetch:!0}),asPath:t,locale:p,router:this});if((null==P?void 0:P.effect.type)==="rewrite"&&(n.pathname=P.effect.resolvedHref,o=P.effect.resolvedHref,l={...l,...P.effect.parsedAs.query},d=P.effect.parsedAs.pathname,e=(0,y.formatWithValidation)(n)),(null==P?void 0:P.effect.type)==="redirect-external")return;let T=(0,a.removeTrailingSlash)(o);await this._bfl(t,d,r.locale,!0)&&(this.components[i]={__appRouter:!0}),await Promise.all([this.pageLoader._isSsg(T).then(t=>!!t&&q({dataHref:(null==P?void 0:P.json)?null==P?void 0:P.dataHref:this.pageLoader.getDataHref({href:e,asPath:d,locale:p}),isServerRender:!1,parseJSON:!0,inflightCache:this.sdc,persistCache:!this.isPreview,isPrefetch:!0,unstable_skipClientCache:r.unstable_skipClientCache||r.priority&&!0}).then(()=>!1).catch(()=>!1)),this.pageLoader[r.priority?"loadPage":"prefetch"](T)])}async fetchComponent(e){let t=G({route:e,router:this});try{let r=await this.pageLoader.loadPage(e);return t(),r}catch(e){throw t(),e}}_getData(e){let t=!1,r=()=>{t=!0};return this.clc=r,e().then(e=>{if(r===this.clc&&(this.clc=null),t){let e=Object.defineProperty(Error("Loading initial props cancelled"),"__NEXT_ERROR_CODE",{value:"E405",enumerable:!1,configurable:!0});throw e.cancelled=!0,e}return e})}getInitialProps(e,t){let{Component:r}=this.components["/_app"],n=this._wrapApp(r);return t.AppTree=n,(0,p.loadGetInitialProps)(r,{AppTree:n,Component:e,router:this,ctx:t})}get route(){return this.state.route}get pathname(){return this.state.pathname}get query(){return this.state.query}get asPath(){return this.state.asPath}get locale(){return this.state.locale}get isFallback(){return this.state.isFallback}get isPreview(){return this.state.isPreview}constructor(e,t,r,{initialProps:n,pageLoader:i,App:s,wrapApp:o,Component:l,err:u,subscription:c,isFallback:d,locale:m,locales:g,defaultLocale:_,domainLocales:v,isPreview:b}){this.sdc={},this.sbc={},this.isFirstPopStateEvent=!0,this._key=z(),this.onPopState=e=>{let t,{isFirstPopStateEvent:r}=this;this.isFirstPopStateEvent=!1;let n=e.state;if(!n){let{pathname:e,query:t}=this;this.changeState("replaceState",(0,y.formatWithValidation)({pathname:(0,w.addBasePath)(e),query:t}),(0,p.getURL)());return}if(n.__NA)return void window.location.reload();if(!n.__N||r&&this.locale===n.options.locale&&n.as===this.asPath)return;let{url:i,as:a,options:s,key:o}=n;this._key=o;let{pathname:l}=(0,f.parseRelativeUrl)(i);(!this.isSsr||a!==(0,w.addBasePath)(this.asPath)||l!==(0,w.addBasePath)(this.pathname))&&(!this._bps||this._bps(n))&&this.change("replaceState",i,a,Object.assign({},s,{shallow:s.shallow&&this._shallow,locale:s.locale||this.defaultLocale,_h:0}),t)};let E=(0,a.removeTrailingSlash)(e);this.components={},"/_error"!==e&&(this.components[E]={Component:l,initial:!0,props:n,err:u,__N_SSG:n&&n.__N_SSG,__N_SSP:n&&n.__N_SSP}),this.components["/_app"]={Component:s,styleSheets:[]},this.events=V.events,this.pageLoader=i;let S=(0,h.isDynamicRoute)(e)&&self.__NEXT_DATA__.autoExport;if(this.basePath="",this.sub=c,this.clc=null,this._wrapApp=o,this.isSsr=!0,this.isLocaleDomain=!1,this.isReady=!!(self.__NEXT_DATA__.gssp||self.__NEXT_DATA__.gip||self.__NEXT_DATA__.isExperimentalCompile||self.__NEXT_DATA__.appGip&&!self.__NEXT_DATA__.gsp||!S&&!self.location.search&&0),this.state={route:E,pathname:e,query:t,asPath:S?e:r,isPreview:!!b,locale:void 0,isFallback:d},this._initialMatchesMiddlewarePromise=Promise.resolve(!1),!r.startsWith("//")){let n={locale:m},i=(0,p.getURL)();this._initialMatchesMiddlewarePromise=D({router:this,locale:m,asPath:i}).then(a=>(n._shouldResolveHref=r!==e,this.changeState("replaceState",a?i:(0,y.formatWithValidation)({pathname:(0,w.addBasePath)(e),query:t}),i,n),a))}window.addEventListener("popstate",this.onPopState)}}V.events=(0,d.default)()},54387:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RouterContext",{enumerable:!0,get:function(){return n}});let n=r(72687)._(r(14232)).default.createContext(null)},54933:(e,t)=>{"use strict";function r(e){return"("===e[0]&&e.endsWith(")")}function n(e){return e.startsWith("@")&&"@children"!==e}function i(e,t){if(e.includes(a)){let e=JSON.stringify(t);return"{}"!==e?a+"?"+e:a}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DEFAULT_SEGMENT_KEY:function(){return s},PAGE_SEGMENT_KEY:function(){return a},addSearchParamsIfPageSegment:function(){return i},isGroupSegment:function(){return r},isParallelRouteSegment:function(){return n}});let a="__PAGE__",s="__DEFAULT__"},54968:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return a}});let n=r(69239),i=r(82585),a=e=>{if(!e.startsWith("/"))return e;let{pathname:t,query:r,hash:a}=(0,i.parsePath)(e);return""+(0,n.removeTrailingSlash)(t)+r+a};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},55040:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab="//");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var i={},a=t.split(n),s=(r||{}).decode||e,o=0;o<a.length;o++){var l=a[o],u=l.indexOf("=");if(!(u<0)){var c=l.substr(0,u).trim(),d=l.substr(++u,l.length).trim();'"'==d[0]&&(d=d.slice(1,-1)),void 0==i[c]&&(i[c]=function(e,t){try{return t(e)}catch(t){return e}}(d,s))}}return i},t.serialize=function(e,t,n){var a=n||{},s=a.encode||r;if("function"!=typeof s)throw TypeError("option encode is invalid");if(!i.test(e))throw TypeError("argument name is invalid");var o=s(t);if(o&&!i.test(o))throw TypeError("argument val is invalid");var l=e+"="+o;if(null!=a.maxAge){var u=a.maxAge-0;if(isNaN(u)||!isFinite(u))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(u)}if(a.domain){if(!i.test(a.domain))throw TypeError("option domain is invalid");l+="; Domain="+a.domain}if(a.path){if(!i.test(a.path))throw TypeError("option path is invalid");l+="; Path="+a.path}if(a.expires){if("function"!=typeof a.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+a.expires.toUTCString()}if(a.httpOnly&&(l+="; HttpOnly"),a.secure&&(l+="; Secure"),a.sameSite)switch("string"==typeof a.sameSite?a.sameSite.toLowerCase():a.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,r=encodeURIComponent,n=/; */,i=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},57766:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getReactStitchedError",{enumerable:!0,get:function(){return u}});let n=r(72687),i=n._(r(14232)),a=n._(r(66240)),s=r(38089),o="react-stack-bottom-frame",l=RegExp("(at "+o+" )|("+o+"\\@)");function u(e){let t=(0,a.default)(e),r=t&&e.stack||"",n=t?e.message:"",o=r.split("\n"),u=o.findIndex(e=>l.test(e)),c=u>=0?o.slice(0,u).join("\n"):r,d=Object.defineProperty(Error(n),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return Object.assign(d,e),(0,s.copyNextErrorCode)(e,d),d.stack=c,function(e){if(!i.default.captureOwnerStack)return;let t=e.stack||"",r=i.default.captureOwnerStack();r&&!1===t.endsWith(r)&&(e.stack=t+=r)}(d),d}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},57962:(e,t,r)=>{"use strict";let n;Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return s},isEqualNode:function(){return a}});let i=r(60348);function a(e,t){if(e instanceof HTMLElement&&t instanceof HTMLElement){let r=t.getAttribute("nonce");if(r&&!e.getAttribute("nonce")){let n=t.cloneNode(!0);return n.setAttribute("nonce",""),n.nonce=r,r===e.nonce&&e.isEqualNode(n)}}return e.isEqualNode(t)}function s(){return{mountedInstances:new Set,updateHead:e=>{let t={};e.forEach(e=>{if("link"===e.type&&e.props["data-optimized-fonts"])if(document.querySelector('style[data-href="'+e.props["data-href"]+'"]'))return;else e.props.href=e.props["data-href"],e.props["data-href"]=void 0;let r=t[e.type]||[];r.push(e),t[e.type]=r});let r=t.title?t.title[0]:null,i="";if(r){let{children:e}=r.props;i="string"==typeof e?e:Array.isArray(e)?e.join(""):""}i!==document.title&&(document.title=i),["meta","base","link","style","script"].forEach(e=>{n(e,t[e]||[])})}}}n=(e,t)=>{let r=document.querySelector("head");if(!r)return;let n=new Set(r.querySelectorAll(""+e+"[data-next-head]"));if("meta"===e){let e=r.querySelector("meta[charset]");null!==e&&n.add(e)}let s=[];for(let e=0;e<t.length;e++){let r=function(e){let{type:t,props:r}=e,n=document.createElement(t);(0,i.setAttributesFromProps)(n,r);let{children:a,dangerouslySetInnerHTML:s}=r;return s?n.innerHTML=s.__html||"":a&&(n.textContent="string"==typeof a?a:Array.isArray(a)?a.join(""):""),n}(t[e]);r.setAttribute("data-next-head","");let o=!0;for(let e of n)if(a(e,r)){n.delete(e),o=!1;break}o&&s.push(r)}for(let e of n){var o;null==(o=e.parentNode)||o.removeChild(e)}for(let e of s)"meta"===e.tagName.toLowerCase()&&null!==e.getAttribute("charset")&&r.prepend(e),r.appendChild(e)},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},58004:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"resolveHref",{enumerable:!0,get:function(){return d}});let n=r(67825),i=r(359),a=r(31277),s=r(1033),o=r(54968),l=r(65790),u=r(92712),c=r(20718);function d(e,t,r){let d,p="string"==typeof t?t:(0,i.formatWithValidation)(t),h=p.match(/^[a-zA-Z]{1,}:\/\//),f=h?p.slice(h[0].length):p;if((f.split("?",1)[0]||"").match(/(\/\/|\\)/)){console.error("Invalid href '"+p+"' passed to next/router in page: '"+e.pathname+"'. Repeated forward-slashes (//) or backslashes \\ are not valid in the href.");let t=(0,s.normalizeRepeatedSlashes)(f);p=(h?h[0]:"")+t}if(!(0,l.isLocalURL)(p))return r?[p]:p;try{d=new URL(p.startsWith("#")?e.asPath:e.pathname,"http://n")}catch(e){d=new URL("/","http://n")}try{let e=new URL(p,d);e.pathname=(0,o.normalizePathTrailingSlash)(e.pathname);let t="";if((0,u.isDynamicRoute)(e.pathname)&&e.searchParams&&r){let r=(0,n.searchParamsToUrlQuery)(e.searchParams),{result:s,params:o}=(0,c.interpolateAs)(e.pathname,e.pathname,r);s&&(t=(0,i.formatWithValidation)({pathname:s,hash:e.hash,query:(0,a.omit)(r,o)}))}let s=e.origin===d.origin?e.href.slice(e.origin.length):e.href;return r?[s,t||s]:s}catch(e){return r?[p]:p}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},60348:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"setAttributesFromProps",{enumerable:!0,get:function(){return a}});let r={acceptCharset:"accept-charset",className:"class",htmlFor:"for",httpEquiv:"http-equiv",noModule:"noModule"},n=["onLoad","onReady","dangerouslySetInnerHTML","children","onError","strategy","stylesheets"];function i(e){return["async","defer","noModule"].includes(e)}function a(e,t){for(let[a,s]of Object.entries(t)){if(!t.hasOwnProperty(a)||n.includes(a)||void 0===s)continue;let o=r[a]||a.toLowerCase();"SCRIPT"===e.tagName&&i(o)?e[o]=!!s:e.setAttribute(o,String(s)),(!1===s||"SCRIPT"===e.tagName&&i(o)&&(!s||"false"===s))&&(e.setAttribute(o,""),e.removeAttribute(o))}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},63021:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),r(78757),self.__next_set_public_path__=e=>{r.p=e},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},63258:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return n}});let r=["default","imgix","cloudinary","akamai","custom"],n={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},63732:(e,t,r)=>{"use strict";function n(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(n=function(e){return e?r:t})(e)}function i(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=n(t);if(r&&r.has(e))return r.get(e);var i={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&Object.prototype.hasOwnProperty.call(e,s)){var o=a?Object.getOwnPropertyDescriptor(e,s):null;o&&(o.get||o.set)?Object.defineProperty(i,s,o):i[s]=e[s]}return i.default=e,r&&r.set(e,i),i}r.r(t),r.d(t,{_:()=>i})},65364:(e,t,r)=>{"use strict";var n,i;e.exports=(null==(n=r.g.process)?void 0:n.env)&&"object"==typeof(null==(i=r.g.process)?void 0:i.env)?r.g.process:r(15861)},65790:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return a}});let n=r(1033),i=r(92860);function a(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let t=(0,n.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,i.hasBasePath)(r.pathname)}catch(e){return!1}}},66240:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return i},getProperError:function(){return a}});let n=r(77891);function i(e){return"object"==typeof e&&null!==e&&"name"in e&&"message"in e}function a(e){return i(e)?e:Object.defineProperty(Error((0,n.isPlainObject)(e)?function(e){let t=new WeakSet;return JSON.stringify(e,(e,r)=>{if("object"==typeof r&&null!==r){if(t.has(r))return"[Circular]";t.add(r)}return r})}(e):e+""),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}},66336:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{BailoutToCSRError:function(){return n},isBailoutToCSRError:function(){return i}});let r="BAILOUT_TO_CLIENT_SIDE_RENDERING";class n extends Error{constructor(e){super("Bail out to client-side rendering: "+e),this.reason=e,this.digest=r}}function i(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}},67825:(e,t)=>{"use strict";function r(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function i(e){let t=new URLSearchParams;for(let[r,i]of Object.entries(e))if(Array.isArray(i))for(let e of i)t.append(r,n(e));else t.set(r,n(i));return t}function a(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return a},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return i}})},68063:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getPathMatch",{enumerable:!0,get:function(){return i}});let n=r(29509);function i(e,t){let r=[],i=(0,n.pathToRegexp)(e,r,{delimiter:"/",sensitive:"boolean"==typeof(null==t?void 0:t.sensitive)&&t.sensitive,strict:null==t?void 0:t.strict}),a=(0,n.regexpToFunction)((null==t?void 0:t.regexModifier)?new RegExp(t.regexModifier(i.source),i.flags):i,r);return(e,n)=>{if("string"!=typeof e)return!1;let i=a(e);if(!i)return!1;if(null==t?void 0:t.removeUnnamedParams)for(let e of r)"number"==typeof e.name&&delete i.params[e.name];return{...n,...i.params}}}},69011:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addBasePath",{enumerable:!0,get:function(){return a}});let n=r(40840),i=r(54968);function a(e,t){return(0,i.normalizePathTrailingSlash)((0,n.addPathPrefix)(e,""))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},69239:(e,t)=>{"use strict";function r(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return r}})},69272:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s}});let n=r(72687)._(r(24622));class i{end(e){if("ended"===this.state.state)throw Object.defineProperty(Error("Span has already ended"),"__NEXT_ERROR_CODE",{value:"E17",enumerable:!1,configurable:!0});this.state={state:"ended",endTime:null!=e?e:Date.now()},this.onSpanEnd(this)}constructor(e,t,r){var n,i;this.name=e,this.attributes=null!=(n=t.attributes)?n:{},this.startTime=null!=(i=t.startTime)?i:Date.now(),this.onSpanEnd=r,this.state={state:"inprogress"}}}class a{startSpan(e,t){return new i(e,t,this.handleSpanEnd)}onSpanEnd(e){return this._emitter.on("spanend",e),()=>{this._emitter.off("spanend",e)}}constructor(){this._emitter=(0,n.default)(),this.handleSpanEnd=e=>{this._emitter.emit("spanend",e)}}}let s=new a;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},70159:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getNamedMiddlewareRegex:function(){return m},getNamedRouteRegex:function(){return f},getRouteRegex:function(){return d},parseParameter:function(){return l}});let n=r(39308),i=r(50781),a=r(3239),s=r(69239),o=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function l(e){let t=e.match(o);return t?u(t[2]):u(e)}function u(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function c(e,t,r){let n={},l=1,c=[];for(let d of(0,s.removeTrailingSlash)(e).slice(1).split("/")){let e=i.INTERCEPTION_ROUTE_MARKERS.find(e=>d.startsWith(e)),s=d.match(o);if(e&&s&&s[2]){let{key:t,optional:r,repeat:i}=u(s[2]);n[t]={pos:l++,repeat:i,optional:r},c.push("/"+(0,a.escapeStringRegexp)(e)+"([^/]+?)")}else if(s&&s[2]){let{key:e,repeat:t,optional:i}=u(s[2]);n[e]={pos:l++,repeat:t,optional:i},r&&s[1]&&c.push("/"+(0,a.escapeStringRegexp)(s[1]));let o=t?i?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";r&&s[1]&&(o=o.substring(1)),c.push(o)}else c.push("/"+(0,a.escapeStringRegexp)(d));t&&s&&s[3]&&c.push((0,a.escapeStringRegexp)(s[3]))}return{parameterizedRoute:c.join(""),groups:n}}function d(e,t){let{includeSuffix:r=!1,includePrefix:n=!1,excludeOptionalTrailingSlash:i=!1}=void 0===t?{}:t,{parameterizedRoute:a,groups:s}=c(e,r,n),o=a;return i||(o+="(?:/)?"),{re:RegExp("^"+o+"$"),groups:s}}function p(e){let t,{interceptionMarker:r,getSafeRouteKey:n,segment:i,routeKeys:s,keyPrefix:o,backreferenceDuplicateKeys:l}=e,{key:c,optional:d,repeat:p}=u(i),h=c.replace(/\W/g,"");o&&(h=""+o+h);let f=!1;(0===h.length||h.length>30)&&(f=!0),isNaN(parseInt(h.slice(0,1)))||(f=!0),f&&(h=n());let m=h in s;o?s[h]=""+o+c:s[h]=c;let g=r?(0,a.escapeStringRegexp)(r):"";return t=m&&l?"\\k<"+h+">":p?"(?<"+h+">.+?)":"(?<"+h+">[^/]+?)",d?"(?:/"+g+t+")?":"/"+g+t}function h(e,t,r,l,u){let c,d=(c=0,()=>{let e="",t=++c;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),h={},f=[];for(let c of(0,s.removeTrailingSlash)(e).slice(1).split("/")){let e=i.INTERCEPTION_ROUTE_MARKERS.some(e=>c.startsWith(e)),s=c.match(o);if(e&&s&&s[2])f.push(p({getSafeRouteKey:d,interceptionMarker:s[1],segment:s[2],routeKeys:h,keyPrefix:t?n.NEXT_INTERCEPTION_MARKER_PREFIX:void 0,backreferenceDuplicateKeys:u}));else if(s&&s[2]){l&&s[1]&&f.push("/"+(0,a.escapeStringRegexp)(s[1]));let e=p({getSafeRouteKey:d,segment:s[2],routeKeys:h,keyPrefix:t?n.NEXT_QUERY_PARAM_PREFIX:void 0,backreferenceDuplicateKeys:u});l&&s[1]&&(e=e.substring(1)),f.push(e)}else f.push("/"+(0,a.escapeStringRegexp)(c));r&&s&&s[3]&&f.push((0,a.escapeStringRegexp)(s[3]))}return{namedParameterizedRoute:f.join(""),routeKeys:h}}function f(e,t){var r,n,i;let a=h(e,t.prefixRouteKeys,null!=(r=t.includeSuffix)&&r,null!=(n=t.includePrefix)&&n,null!=(i=t.backreferenceDuplicateKeys)&&i),s=a.namedParameterizedRoute;return t.excludeOptionalTrailingSlash||(s+="(?:/)?"),{...d(e,t),namedRegex:"^"+s+"$",routeKeys:a.routeKeys}}function m(e,t){let{parameterizedRoute:r}=c(e,!1,!1),{catchAll:n=!0}=t;if("/"===r)return{namedRegex:"^/"+(n?".*":"")+"$"};let{namedParameterizedRoute:i}=h(e,!1,!1,!1,!1);return{namedRegex:"^"+i+(n?"(?:(/.*)?)":"")+"$"}}},71775:(e,t)=>{"use strict";function r(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return r}})},71905:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathSuffix",{enumerable:!0,get:function(){return i}});let n=r(82585);function i(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:i,hash:a}=(0,n.parsePath)(e);return""+r+t+i+a}},72129:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"formatNextPathnameInfo",{enumerable:!0,get:function(){return o}});let n=r(69239),i=r(40840),a=r(71905),s=r(42560);function o(e){let t=(0,s.addLocale)(e.pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix);return(e.buildId||!e.trailingSlash)&&(t=(0,n.removeTrailingSlash)(t)),e.buildId&&(t=(0,a.addPathSuffix)((0,i.addPathPrefix)(t,"/_next/data/"+e.buildId),"/"===e.pathname?"index.json":".json")),t=(0,i.addPathPrefix)(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:(0,a.addPathSuffix)(t,"/"):(0,n.removeTrailingSlash)(t)}},72687:(e,t,r)=>{"use strict";function n(e){return e&&e.__esModule?e:{default:e}}r.r(t),r.d(t,{_:()=>n})},73824:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}}),r(72687);let n=r(37876);r(14232);let i=r(6763);function a(e){function t(t){return(0,n.jsx)(e,{router:(0,i.useRouter)(),...t})}return t.getInitialProps=e.getInitialProps,t.origGetInitialProps=e.origGetInitialProps,t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},74324:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"HeadManagerContext",{enumerable:!0,get:function(){return n}});let n=r(72687)._(r(14232)).default.createContext({})},77891:(e,t)=>{"use strict";function r(e){return Object.prototype.toString.call(e)}function n(e){if("[object Object]"!==r(e))return!1;let t=Object.getPrototypeOf(e);return null===t||t.hasOwnProperty("isPrototypeOf")}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getObjectClassLabel:function(){return r},isPlainObject:function(){return n}})},78757:(e,t)=>{"use strict";function r(){return""}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getDeploymentIdQueryOrEmptyString",{enumerable:!0,get:function(){return r}})},81854:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ImageConfigContext",{enumerable:!0,get:function(){return a}});let n=r(72687)._(r(14232)),i=r(63258),a=n.default.createContext(i.imageConfigDefault)},82155:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return p}});let n=r(72687),i=r(69011),a=r(20718),s=n._(r(15358)),o=r(89266),l=r(88668),u=r(5402),c=r(69239),d=r(30425);r(93309);class p{getPageList(){return(0,d.getClientBuildManifest)().then(e=>e.sortedPages)}getMiddleware(){return window.__MIDDLEWARE_MATCHERS=[{regexp:"^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next|static|.*\\..*|_static|_vercel).*))(\\.json)?[\\/#\\?]?$",originalSource:"/((?!_next|static|.*\\..*|_static|_vercel).*)"}],window.__MIDDLEWARE_MATCHERS}getDataHref(e){let{asPath:t,href:r,locale:n}=e,{pathname:d,query:p,search:h}=(0,u.parseRelativeUrl)(r),{pathname:f}=(0,u.parseRelativeUrl)(t),m=(0,c.removeTrailingSlash)(d);if("/"!==m[0])throw Object.defineProperty(Error('Route name should start with a "/", got "'+m+'"'),"__NEXT_ERROR_CODE",{value:"E303",enumerable:!1,configurable:!0});var g=e.skipInterpolation?f:(0,l.isDynamicRoute)(m)?(0,a.interpolateAs)(d,f,p).result:m;let _=(0,s.default)((0,c.removeTrailingSlash)((0,o.addLocale)(g,n)),".json");return(0,i.addBasePath)("/_next/data/"+this.buildId+_+h,!0)}_isSsg(e){return this.promisedSsgManifest.then(t=>t.has(e))}loadPage(e){return this.routeLoader.loadRoute(e).then(e=>{if("component"in e)return{page:e.component,mod:e.exports,styleSheets:e.styles.map(e=>({href:e.href,text:e.content}))};throw e.error})}prefetch(e){return this.routeLoader.prefetch(e)}constructor(e,t){this.routeLoader=(0,d.createRouteLoader)(t),this.buildId=e,this.assetPrefix=t,this.promisedSsgManifest=new Promise(e=>{window.__SSG_MANIFEST?e(window.__SSG_MANIFEST):window.__SSG_MANIFEST_CB=()=>{e(window.__SSG_MANIFEST)}})}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},82585:(e,t)=>{"use strict";function r(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return r}})},83250:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{PathnameContextProviderAdapter:function(){return h},adaptForAppRouterInstance:function(){return c},adaptForPathParams:function(){return p},adaptForSearchParams:function(){return d}});let n=r(63732),i=r(37876),a=n._(r(14232)),s=r(9440),o=r(92712),l=r(27426),u=r(70159);function c(e){return{back(){e.back()},forward(){e.forward()},refresh(){e.reload()},hmrRefresh(){},push(t,r){let{scroll:n}=void 0===r?{}:r;e.push(t,void 0,{scroll:n})},replace(t,r){let{scroll:n}=void 0===r?{}:r;e.replace(t,void 0,{scroll:n})},prefetch(t){e.prefetch(t)}}}function d(e){return e.isReady&&e.query?(0,l.asPathToSearchParams)(e.asPath):new URLSearchParams}function p(e){if(!e.isReady||!e.query)return null;let t={};for(let r of Object.keys((0,u.getRouteRegex)(e.pathname).groups))t[r]=e.query[r];return t}function h(e){let{children:t,router:r,...n}=e,l=(0,a.useRef)(n.isAutoExport),u=(0,a.useMemo)(()=>{let e,t=l.current;if(t&&(l.current=!1),(0,o.isDynamicRoute)(r.pathname)&&(r.isFallback||t&&!r.isReady))return null;try{e=new URL(r.asPath,"http://f")}catch(e){return"/"}return e.pathname},[r.asPath,r.isFallback,r.isReady,r.pathname]);return(0,i.jsx)(s.PathnameContext.Provider,{value:u,children:t})}},83378:(e,t)=>{"use strict";let r;Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return n},setConfig:function(){return i}});let n=()=>r;function i(e){r=e}},83847:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizeLocalePath",{enumerable:!0,get:function(){return n}});let r=new WeakMap;function n(e,t){let n;if(!t)return{pathname:e};let i=r.get(t);i||(i=t.map(e=>e.toLowerCase()),r.set(t,i));let a=e.split("/",2);if(!a[1])return{pathname:e};let s=a[1].toLowerCase(),o=i.indexOf(s);return o<0?{pathname:e}:(n=t[o],{pathname:e=e.slice(n.length+1)||"/",detectedLocale:n})}},84350:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{compileNonPath:function(){return c},matchHas:function(){return u},parseDestination:function(){return d},prepareDestination:function(){return p}});let n=r(29509),i=r(3239),a=r(94387),s=r(50781),o=r(29663);function l(e){return e.replace(/__ESC_COLON_/gi,":")}function u(e,t,r,n){void 0===r&&(r=[]),void 0===n&&(n=[]);let i={},a=r=>{let n,a=r.key;switch(r.type){case"header":a=a.toLowerCase(),n=e.headers[a];break;case"cookie":n="cookies"in e?e.cookies[r.key]:(0,o.getCookieParser)(e.headers)()[r.key];break;case"query":n=t[a];break;case"host":{let{host:t}=(null==e?void 0:e.headers)||{};n=null==t?void 0:t.split(":",1)[0].toLowerCase()}}if(!r.value&&n)return i[function(e){let t="";for(let r=0;r<e.length;r++){let n=e.charCodeAt(r);(n>64&&n<91||n>96&&n<123)&&(t+=e[r])}return t}(a)]=n,!0;if(n){let e=RegExp("^"+r.value+"$"),t=Array.isArray(n)?n.slice(-1)[0].match(e):n.match(e);if(t)return Array.isArray(t)&&(t.groups?Object.keys(t.groups).forEach(e=>{i[e]=t.groups[e]}):"host"===r.type&&t[0]&&(i.host=t[0])),!0}return!1};return!(!r.every(e=>a(e))||n.some(e=>a(e)))&&i}function c(e,t){if(!e.includes(":"))return e;for(let r of Object.keys(t))e.includes(":"+r)&&(e=e.replace(RegExp(":"+r+"\\*","g"),":"+r+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+r+"\\?","g"),":"+r+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+r+"\\+","g"),":"+r+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+r+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+r));return e=e.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,n.compile)("/"+e,{validate:!1})(t).slice(1)}function d(e){let t=e.destination;for(let r of Object.keys({...e.params,...e.query}))r&&(t=t.replace(RegExp(":"+(0,i.escapeStringRegexp)(r),"g"),"__ESC_COLON_"+r));let r=(0,a.parseUrl)(t),n=r.pathname;n&&(n=l(n));let s=r.href;s&&(s=l(s));let o=r.hostname;o&&(o=l(o));let u=r.hash;return u&&(u=l(u)),{...r,pathname:n,hostname:o,href:s,hash:u}}function p(e){let t,r,i=Object.assign({},e.query),a=d(e),{hostname:o,query:u}=a,p=a.pathname;a.hash&&(p=""+p+a.hash);let h=[],f=[];for(let e of((0,n.pathToRegexp)(p,f),f))h.push(e.name);if(o){let e=[];for(let t of((0,n.pathToRegexp)(o,e),e))h.push(t.name)}let m=(0,n.compile)(p,{validate:!1});for(let[r,i]of(o&&(t=(0,n.compile)(o,{validate:!1})),Object.entries(u)))Array.isArray(i)?u[r]=i.map(t=>c(l(t),e.params)):"string"==typeof i&&(u[r]=c(l(i),e.params));let g=Object.keys(e.params).filter(e=>"nextInternalLocale"!==e);if(e.appendParamsToQuery&&!g.some(e=>h.includes(e)))for(let t of g)t in u||(u[t]=e.params[t]);if((0,s.isInterceptionRouteAppPath)(p))for(let t of p.split("/")){let r=s.INTERCEPTION_ROUTE_MARKERS.find(e=>t.startsWith(e));if(r){"(..)(..)"===r?(e.params["0"]="(..)",e.params["1"]="(..)"):e.params["0"]=r;break}}try{let[n,i]=(r=m(e.params)).split("#",2);t&&(a.hostname=t(e.params)),a.pathname=n,a.hash=(i?"#":"")+(i||""),delete a.search}catch(e){if(e.message.match(/Expected .*? to not repeat, but got an array/))throw Object.defineProperty(Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match"),"__NEXT_ERROR_CODE",{value:"E329",enumerable:!1,configurable:!0});throw e}return a.query={...i,...a.query},{newUrl:r,destQuery:u,parsedDestination:a}}},86500:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{REDIRECT_ERROR_CODE:function(){return i},RedirectType:function(){return a},isRedirectError:function(){return s}});let n=r(13666),i="NEXT_REDIRECT";var a=function(e){return e.push="push",e.replace="replace",e}({});function s(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let t=e.digest.split(";"),[r,a]=t,s=t.slice(2,-2).join(";"),o=Number(t.at(-2));return r===i&&("replace"===a||"push"===a)&&"string"==typeof s&&!isNaN(o)&&o in n.RedirectStatusCode}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},87233:(e,t)=>{"use strict";let r;function n(e){var t;return(null==(t=function(){if(void 0===r){var e;r=(null==(e=window.trustedTypes)?void 0:e.createPolicy("nextjs",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e}))||null}return r}())?void 0:t.createScriptURL(e))||e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"__unsafeCreateTrustedScriptURL",{enumerable:!0,get:function(){return n}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},87665:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return y},handleClientScriptLoad:function(){return m},initScriptLoader:function(){return g}});let n=r(72687),i=r(63732),a=r(37876),s=n._(r(98477)),o=i._(r(14232)),l=r(74324),u=r(60348),c=r(21112),d=new Map,p=new Set,h=e=>{if(s.default.preinit)return void e.forEach(e=>{s.default.preinit(e,{as:"style"})});{let t=document.head;e.forEach(e=>{let r=document.createElement("link");r.type="text/css",r.rel="stylesheet",r.href=e,t.appendChild(r)})}},f=e=>{let{src:t,id:r,onLoad:n=()=>{},onReady:i=null,dangerouslySetInnerHTML:a,children:s="",strategy:o="afterInteractive",onError:l,stylesheets:c}=e,f=r||t;if(f&&p.has(f))return;if(d.has(t)){p.add(f),d.get(t).then(n,l);return}let m=()=>{i&&i(),p.add(f)},g=document.createElement("script"),_=new Promise((e,t)=>{g.addEventListener("load",function(t){e(),n&&n.call(this,t),m()}),g.addEventListener("error",function(e){t(e)})}).catch(function(e){l&&l(e)});a?(g.innerHTML=a.__html||"",m()):s?(g.textContent="string"==typeof s?s:Array.isArray(s)?s.join(""):"",m()):t&&(g.src=t,d.set(t,_)),(0,u.setAttributesFromProps)(g,e),"worker"===o&&g.setAttribute("type","text/partytown"),g.setAttribute("data-nscript",o),c&&h(c),document.body.appendChild(g)};function m(e){let{strategy:t="afterInteractive"}=e;"lazyOnload"===t?window.addEventListener("load",()=>{(0,c.requestIdleCallback)(()=>f(e))}):f(e)}function g(e){e.forEach(m),[...document.querySelectorAll('[data-nscript="beforeInteractive"]'),...document.querySelectorAll('[data-nscript="beforePageRender"]')].forEach(e=>{let t=e.id||e.getAttribute("src");p.add(t)})}function _(e){let{id:t,src:r="",onLoad:n=()=>{},onReady:i=null,strategy:u="afterInteractive",onError:d,stylesheets:h,...m}=e,{updateScripts:g,scripts:_,getIsSsr:y,appDir:v,nonce:b}=(0,o.useContext)(l.HeadManagerContext),E=(0,o.useRef)(!1);(0,o.useEffect)(()=>{let e=t||r;E.current||(i&&e&&p.has(e)&&i(),E.current=!0)},[i,t,r]);let S=(0,o.useRef)(!1);if((0,o.useEffect)(()=>{if(!S.current){if("afterInteractive"===u)f(e);else"lazyOnload"===u&&("complete"===document.readyState?(0,c.requestIdleCallback)(()=>f(e)):window.addEventListener("load",()=>{(0,c.requestIdleCallback)(()=>f(e))}));S.current=!0}},[e,u]),("beforeInteractive"===u||"worker"===u)&&(g?(_[u]=(_[u]||[]).concat([{id:t,src:r,onLoad:n,onReady:i,onError:d,...m}]),g(_)):y&&y()?p.add(t||r):y&&!y()&&f(e)),v){if(h&&h.forEach(e=>{s.default.preinit(e,{as:"style"})}),"beforeInteractive"===u)if(!r)return m.dangerouslySetInnerHTML&&(m.children=m.dangerouslySetInnerHTML.__html,delete m.dangerouslySetInnerHTML),(0,a.jsx)("script",{nonce:b,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([0,{...m,id:t}])+")"}});else return s.default.preload(r,m.integrity?{as:"script",integrity:m.integrity,nonce:b,crossOrigin:m.crossOrigin}:{as:"script",nonce:b,crossOrigin:m.crossOrigin}),(0,a.jsx)("script",{nonce:b,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([r,{...m,id:t}])+")"}});"afterInteractive"===u&&r&&s.default.preload(r,m.integrity?{as:"script",integrity:m.integrity,nonce:b,crossOrigin:m.crossOrigin}:{as:"script",nonce:b,crossOrigin:m.crossOrigin})}return null}Object.defineProperty(_,"__nextScript",{value:!0});let y=_;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},88524:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"denormalizePagePath",{enumerable:!0,get:function(){return a}});let n=r(92712),i=r(40342);function a(e){let t=(0,i.normalizePathSep)(e);return t.startsWith("/index/")&&!(0,n.isDynamicRoute)(t)?t.slice(6):"/index"!==t?t:"/"}},88668:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isDynamicRoute",{enumerable:!0,get:function(){return s}});let n=r(50781),i=/\/[^/]*\[[^/]+\][^/]*(?=\/|$)/,a=/\/\[[^/]+\](?=\/|$)/;function s(e,t){return(void 0===t&&(t=!0),(0,n.isInterceptionRouteAppPath)(e)&&(e=(0,n.extractInterceptionRouteInformation)(e).interceptedRoute),t)?a.test(e):i.test(e)}},89099:(e,t,r)=>{e.exports=r(6763)},89266:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addLocale",{enumerable:!0,get:function(){return n}}),r(54968);let n=function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},90086:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return u}});let n=r(68063),i=r(84350),a=r(69239),s=r(83847),o=r(39116),l=r(5402);function u(e,t,r,u,c,d){let p,h=!1,f=!1,m=(0,l.parseRelativeUrl)(e),g=(0,a.removeTrailingSlash)((0,s.normalizeLocalePath)((0,o.removeBasePath)(m.pathname),d).pathname),_=r=>{let l=(0,n.getPathMatch)(r.source+"",{removeUnnamedParams:!0,strict:!0})(m.pathname);if((r.has||r.missing)&&l){let e=(0,i.matchHas)({headers:{host:document.location.hostname,"user-agent":navigator.userAgent},cookies:document.cookie.split("; ").reduce((e,t)=>{let[r,...n]=t.split("=");return e[r]=n.join("="),e},{})},m.query,r.has,r.missing);e?Object.assign(l,e):l=!1}if(l){if(!r.destination)return f=!0,!0;let n=(0,i.prepareDestination)({appendParamsToQuery:!0,destination:r.destination,params:l,query:u});if(m=n.parsedDestination,e=n.newUrl,Object.assign(u,n.parsedDestination.query),g=(0,a.removeTrailingSlash)((0,s.normalizeLocalePath)((0,o.removeBasePath)(e),d).pathname),t.includes(g))return h=!0,p=g,!0;if((p=c(g))!==e&&t.includes(p))return h=!0,!0}},y=!1;for(let e=0;e<r.beforeFiles.length;e++)_(r.beforeFiles[e]);if(!(h=t.includes(g))){if(!y){for(let e=0;e<r.afterFiles.length;e++)if(_(r.afterFiles[e])){y=!0;break}}if(y||(p=c(g),y=h=t.includes(p)),!y){for(let e=0;e<r.fallback.length;e++)if(_(r.fallback[e])){y=!0;break}}}return{asPath:e,parsedAs:m,matchedPage:h,resolvedHref:p,externalDest:f}}},92712:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getSortedRouteObjects:function(){return n.getSortedRouteObjects},getSortedRoutes:function(){return n.getSortedRoutes},isDynamicRoute:function(){return i.isDynamicRoute}});let n=r(24802),i=r(88668)},92808:(e,t,r)=>{"use strict";let n,i,a,s,o,l,u,c,d,p,h,f,m,g,_,y,v,b,E,S,w,R,P,T,x,O,I;r.r(t),r.d(t,{onRouterTransitionStart:()=>lx});let C=globalThis,M="9.44.0";function k(){return A(C),C}function A(e){let t=e.__SENTRY__=e.__SENTRY__||{};return t.version=t.version||M,t[M]=t[M]||{}}function N(e,t,r=C){let n=r.__SENTRY__=r.__SENTRY__||{},i=n[M]=n[M]||{};return i[e]||(i[e]=t())}let j=["debug","info","warn","error","log","assert","trace"],L={};function D(e){if(!("console"in C))return e();let t=C.console,r={},n=Object.keys(L);n.forEach(e=>{let n=L[e];r[e]=t[e],t[e]=n});try{return e()}finally{n.forEach(e=>{t[e]=r[e]})}}function U(e,...t){}function F(){return{enabled:!1}}let B={enable:function(){F().enabled=!0},disable:function(){F().enabled=!1},isEnabled:function(){return F().enabled},log:function(...e){[...e]},warn:function(...e){[...e]},error:function(...e){[...e]}};function $(e,t,r=[t],n="npm"){let i=e._metadata||{};i.sdk||(i.sdk={name:`sentry.javascript.${t}`,packages:r.map(e=>({name:`${n}:@sentry/${e}`,version:M})),version:M}),e._metadata=i}let H=Object.prototype.toString;function W(e){switch(H.call(e)){case"[object Error]":case"[object Exception]":case"[object DOMException]":case"[object WebAssembly.Exception]":return!0;default:return Z(e,Error)}}function q(e,t){return H.call(e)===`[object ${t}]`}function z(e){return q(e,"ErrorEvent")}function X(e){return q(e,"DOMError")}function G(e){return q(e,"String")}function V(e){return"object"==typeof e&&null!==e&&"__sentry_template_string__"in e&&"__sentry_template_values__"in e}function Y(e){return null===e||V(e)||"object"!=typeof e&&"function"!=typeof e}function K(e){return q(e,"Object")}function J(e){return"undefined"!=typeof Event&&Z(e,Event)}function Q(e){return!!(e?.then&&"function"==typeof e.then)}function Z(e,t){try{return e instanceof t}catch{return!1}}function ee(e){return!!("object"==typeof e&&null!==e&&(e.__isVue||e._isVue))}function et(e){return"undefined"!=typeof Request&&Z(e,Request)}function er(e,t={}){if(!e)return"<unknown>";try{let r,n=e,i=[],a=0,s=0,o=Array.isArray(t)?t:t.keyAttrs,l=!Array.isArray(t)&&t.maxStringLength||80;for(;n&&a++<5&&(r=function(e,t){let r=[];if(!e?.tagName)return"";if(C.HTMLElement&&e instanceof HTMLElement&&e.dataset){if(e.dataset.sentryComponent)return e.dataset.sentryComponent;if(e.dataset.sentryElement)return e.dataset.sentryElement}r.push(e.tagName.toLowerCase());let n=t?.length?t.filter(t=>e.getAttribute(t)).map(t=>[t,e.getAttribute(t)]):null;if(n?.length)n.forEach(e=>{r.push(`[${e[0]}="${e[1]}"]`)});else{e.id&&r.push(`#${e.id}`);let t=e.className;if(t&&G(t))for(let e of t.split(/\s+/))r.push(`.${e}`)}for(let t of["aria-label","type","name","title","alt"]){let n=e.getAttribute(t);n&&r.push(`[${t}="${n}"]`)}return r.join("")}(n,o),"html"!==r&&(!(a>1)||!(s+3*i.length+r.length>=l)));)i.push(r),s+=r.length,n=n.parentNode;return i.reverse().join(" > ")}catch{return"<unknown>"}}function en(){try{return C.document.location.href}catch{return""}}function ei(e){if(!C.HTMLElement)return null;let t=e;for(let e=0;e<5&&t;e++){if(t instanceof HTMLElement){if(t.dataset.sentryComponent)return t.dataset.sentryComponent;if(t.dataset.sentryElement)return t.dataset.sentryElement}t=t.parentNode}return null}function ea(e,t=0){return"string"!=typeof e||0===t||e.length<=t?e:`${e.slice(0,t)}...`}function es(e,t){if(!Array.isArray(e))return"";let r=[];for(let t=0;t<e.length;t++){let n=e[t];try{ee(n)?r.push("[VueViewModel]"):r.push(String(n))}catch{r.push("[value cannot be serialized]")}}return r.join(t)}function eo(e,t=[],r=!1){return t.some(t=>(function(e,t,r=!1){return!!G(e)&&(q(t,"RegExp")?t.test(e):!!G(t)&&(r?e===t:e.includes(t)))})(e,t,r))}function el(e,t,r){if(!(t in e))return;let n=e[t];if("function"!=typeof n)return;let i=r(n);"function"==typeof i&&ec(i,n);try{e[t]=i}catch{}}function eu(e,t,r){try{Object.defineProperty(e,t,{value:r,writable:!0,configurable:!0})}catch{}}function ec(e,t){try{let r=t.prototype||{};e.prototype=t.prototype=r,eu(e,"__sentry_original__",t)}catch{}}function ed(e){return e.__sentry_original__}function ep(e){if(W(e))return{message:e.message,name:e.name,stack:e.stack,...ef(e)};if(!J(e))return e;{let t={type:e.type,target:eh(e.target),currentTarget:eh(e.currentTarget),...ef(e)};return"undefined"!=typeof CustomEvent&&Z(e,CustomEvent)&&(t.detail=e.detail),t}}function eh(e){try{return"undefined"!=typeof Element&&Z(e,Element)?er(e):Object.prototype.toString.call(e)}catch{return"<unknown>"}}function ef(e){if("object"!=typeof e||null===e)return{};{let t={};for(let r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}}function em(e=C.crypto||C.msCrypto){let t=()=>16*Math.random();try{if(e?.randomUUID)return e.randomUUID().replace(/-/g,"");e?.getRandomValues&&(t=()=>{let t=new Uint8Array(1);return e.getRandomValues(t),t[0]})}catch{}return"10000000100040008000100000000000".replace(/[018]/g,e=>(e^(15&t())>>e/4).toString(16))}function eg(e){return e.exception?.values?.[0]}function e_(e,t,r){let n=e.exception=e.exception||{},i=n.values=n.values||[],a=i[0]=i[0]||{};a.value||(a.value=t||""),a.type||(a.type=r||"Error")}function ey(e,t){let r=eg(e);if(!r)return;let n=r.mechanism;if(r.mechanism={type:"generic",handled:!0,...n,...t},t&&"data"in t){let e={...n?.data,...t.data};r.mechanism.data=e}}function ev(e){if(function(e){try{return e.__sentry_captured__}catch{}}(e))return!0;try{eu(e,"__sentry_captured__",!0)}catch{}return!1}function eb(){return Date.now()/1e3}function eE(){return(n??(n=function(){let{performance:e}=C;if(!e?.now||!e.timeOrigin)return eb;let t=e.timeOrigin;return()=>(t+e.now())/1e3}()))()}function eS(){return i||(i=function(){let{performance:e}=C;if(!e?.now)return[void 0,"none"];let t=e.now(),r=Date.now(),n=e.timeOrigin?Math.abs(e.timeOrigin+t-r):36e5,i=e.timing?.navigationStart,a="number"==typeof i?Math.abs(i+t-r):36e5;if(n<36e5||a<36e5)if(n<=a)return[e.timeOrigin,"timeOrigin"];else return[i,"navigationStart"];return[r,"dateNow"]}()),i[0]}function ew(e,t={}){if(t.user&&(!e.ipAddress&&t.user.ip_address&&(e.ipAddress=t.user.ip_address),e.did||t.did||(e.did=t.user.id||t.user.email||t.user.username)),e.timestamp=t.timestamp||eE(),t.abnormal_mechanism&&(e.abnormal_mechanism=t.abnormal_mechanism),t.ignoreDuration&&(e.ignoreDuration=t.ignoreDuration),t.sid&&(e.sid=32===t.sid.length?t.sid:em()),void 0!==t.init&&(e.init=t.init),!e.did&&t.did&&(e.did=`${t.did}`),"number"==typeof t.started&&(e.started=t.started),e.ignoreDuration)e.duration=void 0;else if("number"==typeof t.duration)e.duration=t.duration;else{let t=e.timestamp-e.started;e.duration=t>=0?t:0}t.release&&(e.release=t.release),t.environment&&(e.environment=t.environment),!e.ipAddress&&t.ipAddress&&(e.ipAddress=t.ipAddress),!e.userAgent&&t.userAgent&&(e.userAgent=t.userAgent),"number"==typeof t.errors&&(e.errors=t.errors),t.status&&(e.status=t.status)}function eR(e,t,r=2){if(!t||"object"!=typeof t||r<=0)return t;if(e&&0===Object.keys(t).length)return e;let n={...e};for(let e in t)Object.prototype.hasOwnProperty.call(t,e)&&(n[e]=eR(n[e],t[e],r-1));return n}function eP(){return em().substring(16)}let eT="_sentrySpan";function ex(e,t){t?eu(e,eT,t):delete e[eT]}class eO{constructor(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._attachments=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={},this._propagationContext={traceId:em(),sampleRand:Math.random()}}clone(){let e=new eO;return e._breadcrumbs=[...this._breadcrumbs],e._tags={...this._tags},e._extra={...this._extra},e._contexts={...this._contexts},this._contexts.flags&&(e._contexts.flags={values:[...this._contexts.flags.values]}),e._user=this._user,e._level=this._level,e._session=this._session,e._transactionName=this._transactionName,e._fingerprint=this._fingerprint,e._eventProcessors=[...this._eventProcessors],e._attachments=[...this._attachments],e._sdkProcessingMetadata={...this._sdkProcessingMetadata},e._propagationContext={...this._propagationContext},e._client=this._client,e._lastEventId=this._lastEventId,ex(e,this[eT]),e}setClient(e){this._client=e}setLastEventId(e){this._lastEventId=e}getClient(){return this._client}lastEventId(){return this._lastEventId}addScopeListener(e){this._scopeListeners.push(e)}addEventProcessor(e){return this._eventProcessors.push(e),this}setUser(e){return this._user=e||{email:void 0,id:void 0,ip_address:void 0,username:void 0},this._session&&ew(this._session,{user:e}),this._notifyScopeListeners(),this}getUser(){return this._user}setTags(e){return this._tags={...this._tags,...e},this._notifyScopeListeners(),this}setTag(e,t){return this._tags={...this._tags,[e]:t},this._notifyScopeListeners(),this}setExtras(e){return this._extra={...this._extra,...e},this._notifyScopeListeners(),this}setExtra(e,t){return this._extra={...this._extra,[e]:t},this._notifyScopeListeners(),this}setFingerprint(e){return this._fingerprint=e,this._notifyScopeListeners(),this}setLevel(e){return this._level=e,this._notifyScopeListeners(),this}setTransactionName(e){return this._transactionName=e,this._notifyScopeListeners(),this}setContext(e,t){return null===t?delete this._contexts[e]:this._contexts[e]=t,this._notifyScopeListeners(),this}setSession(e){return e?this._session=e:delete this._session,this._notifyScopeListeners(),this}getSession(){return this._session}update(e){if(!e)return this;let t="function"==typeof e?e(this):e,{tags:r,extra:n,user:i,contexts:a,level:s,fingerprint:o=[],propagationContext:l}=(t instanceof eO?t.getScopeData():K(t)?e:void 0)||{};return this._tags={...this._tags,...r},this._extra={...this._extra,...n},this._contexts={...this._contexts,...a},i&&Object.keys(i).length&&(this._user=i),s&&(this._level=s),o.length&&(this._fingerprint=o),l&&(this._propagationContext=l),this}clear(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._session=void 0,ex(this,void 0),this._attachments=[],this.setPropagationContext({traceId:em(),sampleRand:Math.random()}),this._notifyScopeListeners(),this}addBreadcrumb(e,t){let r="number"==typeof t?t:100;if(r<=0)return this;let n={timestamp:eb(),...e,message:e.message?ea(e.message,2048):e.message};return this._breadcrumbs.push(n),this._breadcrumbs.length>r&&(this._breadcrumbs=this._breadcrumbs.slice(-r),this._client?.recordDroppedEvent("buffer_overflow","log_item")),this._notifyScopeListeners(),this}getLastBreadcrumb(){return this._breadcrumbs[this._breadcrumbs.length-1]}clearBreadcrumbs(){return this._breadcrumbs=[],this._notifyScopeListeners(),this}addAttachment(e){return this._attachments.push(e),this}clearAttachments(){return this._attachments=[],this}getScopeData(){return{breadcrumbs:this._breadcrumbs,attachments:this._attachments,contexts:this._contexts,tags:this._tags,extra:this._extra,user:this._user,level:this._level,fingerprint:this._fingerprint||[],eventProcessors:this._eventProcessors,propagationContext:this._propagationContext,sdkProcessingMetadata:this._sdkProcessingMetadata,transactionName:this._transactionName,span:this[eT]}}setSDKProcessingMetadata(e){return this._sdkProcessingMetadata=eR(this._sdkProcessingMetadata,e,2),this}setPropagationContext(e){return this._propagationContext=e,this}getPropagationContext(){return this._propagationContext}captureException(e,t){let r=t?.event_id||em();if(!this._client)return r;let n=Error("Sentry syntheticException");return this._client.captureException(e,{originalException:e,syntheticException:n,...t,event_id:r},this),r}captureMessage(e,t,r){let n=r?.event_id||em();if(!this._client)return n;let i=Error(e);return this._client.captureMessage(e,t,{originalException:e,syntheticException:i,...r,event_id:n},this),n}captureEvent(e,t){let r=t?.event_id||em();return this._client&&this._client.captureEvent(e,{...t,event_id:r},this),r}_notifyScopeListeners(){this._notifyingListeners||(this._notifyingListeners=!0,this._scopeListeners.forEach(e=>{e(this)}),this._notifyingListeners=!1)}}class eI{constructor(e,t){let r,n;r=e||new eO,n=t||new eO,this._stack=[{scope:r}],this._isolationScope=n}withScope(e){let t,r=this._pushScope();try{t=e(r)}catch(e){throw this._popScope(),e}return Q(t)?t.then(e=>(this._popScope(),e),e=>{throw this._popScope(),e}):(this._popScope(),t)}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getIsolationScope(){return this._isolationScope}getStackTop(){return this._stack[this._stack.length-1]}_pushScope(){let e=this.getScope().clone();return this._stack.push({client:this.getClient(),scope:e}),e}_popScope(){return!(this._stack.length<=1)&&!!this._stack.pop()}}function eC(){let e=A(k());return e.stack=e.stack||new eI(N("defaultCurrentScope",()=>new eO),N("defaultIsolationScope",()=>new eO))}function eM(e){return eC().withScope(e)}function ek(e,t){let r=eC();return r.withScope(()=>(r.getStackTop().scope=e,t(e)))}function eA(e){return eC().withScope(()=>e(eC().getIsolationScope()))}function eN(e){let t=A(e);return t.acs?t.acs:{withIsolationScope:eA,withScope:eM,withSetScope:ek,withSetIsolationScope:(e,t)=>eA(t),getCurrentScope:()=>eC().getScope(),getIsolationScope:()=>eC().getIsolationScope()}}function ej(){return eN(k()).getCurrentScope()}function eL(){return eN(k()).getIsolationScope()}function eD(...e){let t=eN(k());if(2===e.length){let[r,n]=e;return r?t.withSetScope(r,n):t.withScope(n)}return t.withScope(e[0])}function eU(){return ej().getClient()}let eF="production";function eB(e){return new eH(t=>{t(e)})}function e$(e){return new eH((t,r)=>{r(e)})}class eH{constructor(e){this._state=0,this._handlers=[],this._runExecutor(e)}then(e,t){return new eH((r,n)=>{this._handlers.push([!1,t=>{if(e)try{r(e(t))}catch(e){n(e)}else r(t)},e=>{if(t)try{r(t(e))}catch(e){n(e)}else n(e)}]),this._executeHandlers()})}catch(e){return this.then(e=>e,e)}finally(e){return new eH((t,r)=>{let n,i;return this.then(t=>{i=!1,n=t,e&&e()},t=>{i=!0,n=t,e&&e()}).then(()=>{if(i)return void r(n);t(n)})})}_executeHandlers(){if(0===this._state)return;let e=this._handlers.slice();this._handlers=[],e.forEach(e=>{e[0]||(1===this._state&&e[1](this._value),2===this._state&&e[2](this._value),e[0]=!0)})}_runExecutor(e){let t=(e,t)=>{if(0===this._state){if(Q(t))return void t.then(r,n);this._state=e,this._value=t,this._executeHandlers()}},r=e=>{t(1,e)},n=e=>{t(2,e)};try{e(r,n)}catch(e){n(e)}}}let eW="sentry.source",eq="sentry.sample_rate",ez="sentry.previous_trace_sample_rate",eX="sentry.op",eG="sentry.origin",eV="sentry.idle_span_finish_reason",eY="sentry.measurement_unit",eK="sentry.measurement_value",eJ="sentry.custom_span_name",eQ="sentry.profile_id",eZ="sentry.exclusive_time",e0="sentry-",e1=/^sentry-/;function e2(e){let t=e3(e);if(!t)return;let r=Object.entries(t).reduce((e,[t,r])=>(t.match(e1)&&(e[t.slice(e0.length)]=r),e),{});return Object.keys(r).length>0?r:void 0}function e3(e){if(e&&(G(e)||Array.isArray(e)))return Array.isArray(e)?e.reduce((e,t)=>(Object.entries(e8(t)).forEach(([t,r])=>{e[t]=r}),e),{}):e8(e)}function e8(e){return e.split(",").map(e=>e.split("=").map(e=>{try{return decodeURIComponent(e.trim())}catch{return}})).reduce((e,[t,r])=>(t&&r&&(e[t]=r),e),{})}let e5=/^o(\d+)\./,e4=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;function e6(e,t=!1){let{host:r,path:n,pass:i,port:a,projectId:s,protocol:o,publicKey:l}=e;return`${o}://${l}${t&&i?`:${i}`:""}@${r}${a?`:${a}`:""}/${n?`${n}/`:n}${s}`}function e7(e){let t=e4.exec(e);if(!t)return void D(()=>{console.error(`Invalid Sentry Dsn: ${e}`)});let[r,n,i="",a="",s="",o=""]=t.slice(1),l="",u=o,c=u.split("/");if(c.length>1&&(l=c.slice(0,-1).join("/"),u=c.pop()),u){let e=u.match(/^\d+/);e&&(u=e[0])}return e9({host:a,pass:i,path:l,projectId:u,port:s,protocol:r,publicKey:n})}function e9(e){return{protocol:e.protocol,publicKey:e.publicKey||"",pass:e.pass||"",host:e.host,port:e.port||"",path:e.path||"",projectId:e.projectId}}function te(e){if("boolean"==typeof __SENTRY_TRACING__&&!__SENTRY_TRACING__)return!1;let t=e||eU()?.getOptions();return!!t&&(null!=t.tracesSampleRate||!!t.tracesSampler)}function tt(e,t){e.setAttribute("http.response.status_code",t);let r=function(e){if(e<400&&e>=100)return{code:1};if(e>=400&&e<500)switch(e){case 401:return{code:2,message:"unauthenticated"};case 403:return{code:2,message:"permission_denied"};case 404:return{code:2,message:"not_found"};case 409:return{code:2,message:"already_exists"};case 413:return{code:2,message:"failed_precondition"};case 429:return{code:2,message:"resource_exhausted"};case 499:return{code:2,message:"cancelled"};default:return{code:2,message:"invalid_argument"}}if(e>=500&&e<600)switch(e){case 501:return{code:2,message:"unimplemented"};case 503:return{code:2,message:"unavailable"};case 504:return{code:2,message:"deadline_exceeded"};default:return{code:2,message:"internal_error"}}return{code:2,message:"unknown_error"}}(t);"unknown_error"!==r.message&&e.setStatus(r)}let tr="_sentryScope",tn="_sentryIsolationScope";function ti(e){return{scope:e[tr],isolationScope:e[tn]}}function ta(e){if("boolean"==typeof e)return Number(e);let t="string"==typeof e?parseFloat(e):e;if(!("number"!=typeof t||isNaN(t))&&!(t<0)&&!(t>1))return t}let ts=RegExp("^[ \\t]*([0-9a-f]{32})?-?([0-9a-f]{16})?-?([01])?[ \\t]*$");function to(e=em(),t=eP(),r){let n="";return void 0!==r&&(n=r?"-1":"-0"),`${e}-${t}${n}`}let tl=!1;function tu(e){return e&&e.length>0?e.map(({context:{spanId:e,traceId:t,traceFlags:r,...n},attributes:i})=>({span_id:e,trace_id:t,sampled:1===r,attributes:i,...n})):void 0}function tc(e){return"number"==typeof e?td(e):Array.isArray(e)?e[0]+e[1]/1e9:e instanceof Date?td(e.getTime()):eE()}function td(e){return e>0x2540be3ff?e/1e3:e}function tp(e){var t;if("function"==typeof e.getSpanJSON)return e.getSpanJSON();let{spanId:r,traceId:n}=e.spanContext();if((t=e).attributes&&t.startTime&&t.name&&t.endTime&&t.status){let{attributes:t,startTime:i,name:a,endTime:s,status:o,links:l}=e;return{span_id:r,trace_id:n,data:t,description:a,parent_span_id:"parentSpanId"in e?e.parentSpanId:"parentSpanContext"in e?e.parentSpanContext?.spanId:void 0,start_timestamp:tc(i),timestamp:tc(s)||void 0,status:tf(o),op:t[eX],origin:t[eG],links:tu(l)}}return{span_id:r,trace_id:n,start_timestamp:0,data:{}}}function th(e){let{traceFlags:t}=e.spanContext();return 1===t}function tf(e){if(e&&0!==e.code)return 1===e.code?"ok":e.message||"unknown_error"}let tm="_sentryChildSpans",tg="_sentryRootSpan";function t_(e,t){let r=e[tg]||e;eu(t,tg,r),e[tm]?e[tm].add(t):eu(e,tm,new Set([t]))}function ty(e){let t=new Set;return!function e(r){if(!t.has(r)&&th(r))for(let n of(t.add(r),r[tm]?Array.from(r[tm]):[]))e(n)}(e),Array.from(t)}function tv(e){return e[tg]||e}function tb(){let e=eN(k());return e.getActiveSpan?e.getActiveSpan():ej()[eT]}function tE(){tl||(D(()=>{console.warn("[Sentry] Returning null from `beforeSendSpan` is disallowed. To drop certain spans, configure the respective integrations directly.")}),tl=!0)}let tS="_frozenDsc";function tw(e,t){let r,n=t.getOptions(),{publicKey:i,host:a}=t.getDsn()||{};n.orgId?r=String(n.orgId):a&&(r=function(e){let t=e.match(e5);return t?.[1]}(a));let s={environment:n.environment||eF,release:n.release,public_key:i,trace_id:e,org_id:r};return t.emit("createDsc",s),s}function tR(e,t){let r=t.getPropagationContext();return r.dsc||tw(r.traceId,e)}function tP(e){let t=eU();if(!t)return{};let r=tv(e),n=tp(r),i=n.data,a=r.spanContext().traceState,s=a?.get("sentry.sample_rate")??i[eq]??i[ez];function o(e){return("number"==typeof s||"string"==typeof s)&&(e.sample_rate=`${s}`),e}let l=r[tS];if(l)return o(l);let u=a?.get("sentry.dsc"),c=u&&e2(u);if(c)return o(c);let d=tw(e.spanContext().traceId,t),p=i[eW],h=n.description;return"url"!==p&&h&&(d.transaction=h),te()&&(d.sampled=String(th(r)),d.sample_rand=a?.get("sentry.sample_rand")??ti(r).scope?.getPropagationContext().sampleRand.toString()),o(d),t.emit("createDsc",d,r),d}function tT(e,t){let{extra:r,tags:n,user:i,contexts:a,level:s,sdkProcessingMetadata:o,breadcrumbs:l,fingerprint:u,eventProcessors:c,attachments:d,propagationContext:p,transactionName:h,span:f}=t;tx(e,"extra",r),tx(e,"tags",n),tx(e,"user",i),tx(e,"contexts",a),e.sdkProcessingMetadata=eR(e.sdkProcessingMetadata,o,2),s&&(e.level=s),h&&(e.transactionName=h),f&&(e.span=f),l.length&&(e.breadcrumbs=[...e.breadcrumbs,...l]),u.length&&(e.fingerprint=[...e.fingerprint,...u]),c.length&&(e.eventProcessors=[...e.eventProcessors,...c]),d.length&&(e.attachments=[...e.attachments,...d]),e.propagationContext={...e.propagationContext,...p}}function tx(e,t,r){e[t]=eR(e[t],r,1)}let tO=/\(error: (.*)\)/,tI=/captureMessage|captureException/;function tC(...e){let t=e.sort((e,t)=>e[0]-t[0]).map(e=>e[1]);return(e,r=0,n=0)=>{let i=[],a=e.split("\n");for(let e=r;e<a.length;e++){let r=a[e];if(r.length>1024)continue;let s=tO.test(r)?r.replace(tO,"$1"):r;if(!s.match(/\S*Error: /)){for(let e of t){let t=e(s);if(t){i.push(t);break}}if(i.length>=50+n)break}}var s=i.slice(n);if(!s.length)return[];let o=Array.from(s);return/sentryWrapped/.test(tM(o).function||"")&&o.pop(),o.reverse(),tI.test(tM(o).function||"")&&(o.pop(),tI.test(tM(o).function||"")&&o.pop()),o.slice(0,50).map(e=>({...e,filename:e.filename||tM(o).filename,function:e.function||"?"}))}}function tM(e){return e[e.length-1]||{}}let tk="<anonymous>";function tA(e){try{if(!e||"function"!=typeof e)return tk;return e.name||tk}catch{return tk}}function tN(e){let t=e.exception;if(t){let e=[];try{return t.values.forEach(t=>{t.stacktrace.frames&&e.push(...t.stacktrace.frames)}),e}catch{}}}function tj(e,t=100,r=Infinity){try{return function e(t,r,n=Infinity,i=Infinity,a=function(){let e=new WeakSet;return[function(t){return!!e.has(t)||(e.add(t),!1)},function(t){e.delete(t)}]}()){let[s,o]=a;if(null==r||["boolean","string"].includes(typeof r)||"number"==typeof r&&Number.isFinite(r))return r;let l=function(e,t){try{if("domain"===e&&t&&"object"==typeof t&&t._events)return"[Domain]";if("domainEmitter"===e)return"[DomainEmitter]";if("undefined"!=typeof global&&t===global)return"[Global]";if("undefined"!=typeof window&&t===window)return"[Window]";if("undefined"!=typeof document&&t===document)return"[Document]";if(ee(t))return"[VueViewModel]";if(K(t)&&"nativeEvent"in t&&"preventDefault"in t&&"stopPropagation"in t)return"[SyntheticEvent]";if("number"==typeof t&&!Number.isFinite(t))return`[${t}]`;if("function"==typeof t)return`[Function: ${tA(t)}]`;if("symbol"==typeof t)return`[${String(t)}]`;if("bigint"==typeof t)return`[BigInt: ${String(t)}]`;let r=function(e){let t=Object.getPrototypeOf(e);return t?.constructor?t.constructor.name:"null prototype"}(t);if(/^HTML(\w*)Element$/.test(r))return`[HTMLElement: ${r}]`;return`[object ${r}]`}catch(e){return`**non-serializable** (${e})`}}(t,r);if(!l.startsWith("[object "))return l;if(r.__sentry_skip_normalization__)return r;let u="number"==typeof r.__sentry_override_normalization_depth__?r.__sentry_override_normalization_depth__:n;if(0===u)return l.replace("object ","");if(s(r))return"[Circular ~]";if(r&&"function"==typeof r.toJSON)try{let t=r.toJSON();return e("",t,u-1,i,a)}catch{}let c=Array.isArray(r)?[]:{},d=0,p=ep(r);for(let t in p){if(!Object.prototype.hasOwnProperty.call(p,t))continue;if(d>=i){c[t]="[MaxProperties ~]";break}let r=p[t];c[t]=e(t,r,u-1,i,a),d++}return o(r),c}("",e,t,r)}catch(e){return{ERROR:`**non-serializable** (${e})`}}}function tL(e,t,r,n,i,l){var u,c,d,p,h,f;let{normalizeDepth:m=3,normalizeMaxBreadth:g=1e3}=e,_={...t,event_id:t.event_id||r.event_id||em(),timestamp:t.timestamp||eb()},y=r.integrations||e.integrations.map(e=>e.name);(function(e,t){let{environment:r,release:n,dist:i,maxValueLength:a=250}=t;e.environment=e.environment||r||eF,!e.release&&n&&(e.release=n),!e.dist&&i&&(e.dist=i);let s=e.request;s?.url&&(s.url=ea(s.url,a))})(_,e),u=_,(c=y).length>0&&(u.sdk=u.sdk||{},u.sdk.integrations=[...u.sdk.integrations||[],...c]),i&&i.emit("applyFrameMetadata",t),void 0===t.type&&function(e,t){let r=function(e){let t=C._sentryDebugIds;if(!t)return{};let r=Object.keys(t);return o&&r.length===s?o:(s=r.length,o=r.reduce((r,n)=>{a||(a={});let i=a[n];if(i)r[i[0]]=i[1];else{let i=e(n);for(let e=i.length-1;e>=0;e--){let s=i[e],o=s?.filename,l=t[n];if(o&&l){r[o]=l,a[n]=[o,l];break}}}return r},{}))}(t);e.exception?.values?.forEach(e=>{e.stacktrace?.frames?.forEach(e=>{e.filename&&(e.debug_id=r[e.filename])})})}(_,e.stackParser);let v=function(e,t){if(!t)return e;let r=e?e.clone():new eO;return r.update(t),r}(n,r.captureContext);r.mechanism&&ey(_,r.mechanism);let b=i?i.getEventProcessors():[],E=N("globalScope",()=>new eO).getScopeData();l&&tT(E,l.getScopeData()),v&&tT(E,v.getScopeData());let S=[...r.attachments||[],...E.attachments];S.length&&(r.attachments=S);let{fingerprint:w,span:R,breadcrumbs:P,sdkProcessingMetadata:T}=E;return function(e,t){let{extra:r,tags:n,user:i,contexts:a,level:s,transactionName:o}=t;Object.keys(r).length&&(e.extra={...r,...e.extra}),Object.keys(n).length&&(e.tags={...n,...e.tags}),Object.keys(i).length&&(e.user={...i,...e.user}),Object.keys(a).length&&(e.contexts={...a,...e.contexts}),s&&(e.level=s),o&&"transaction"!==e.type&&(e.transaction=o)}(_,E),R&&function(e,t){e.contexts={trace:function(e){let{spanId:t,traceId:r,isRemote:n}=e.spanContext(),i=n?t:tp(e).parent_span_id,a=ti(e).scope;return{parent_span_id:i,span_id:n?a?.getPropagationContext().propagationSpanId||eP():t,trace_id:r}}(t),...e.contexts},e.sdkProcessingMetadata={dynamicSamplingContext:tP(t),...e.sdkProcessingMetadata};let r=tp(tv(t)).description;r&&!e.transaction&&"transaction"===e.type&&(e.transaction=r)}(_,R),d=_,p=w,d.fingerprint=d.fingerprint?Array.isArray(d.fingerprint)?d.fingerprint:[d.fingerprint]:[],p&&(d.fingerprint=d.fingerprint.concat(p)),d.fingerprint.length||delete d.fingerprint,function(e,t){let r=[...e.breadcrumbs||[],...t];e.breadcrumbs=r.length?r:void 0}(_,P),h=_,f=T,h.sdkProcessingMetadata={...h.sdkProcessingMetadata,...f},(function e(t,r,n,i=0){return new eH((a,s)=>{let o=t[i];if(null===r||"function"!=typeof o)a(r);else{let l=o({...r},n);Q(l)?l.then(r=>e(t,r,n,i+1).then(a)).then(null,s):e(t,l,n,i+1).then(a).then(null,s)}})})([...b,...E.eventProcessors],_,r).then(e=>(e&&function(e){let t={};if(e.exception?.values?.forEach(e=>{e.stacktrace?.frames?.forEach(e=>{e.debug_id&&(e.abs_path?t[e.abs_path]=e.debug_id:e.filename&&(t[e.filename]=e.debug_id),delete e.debug_id)})}),0===Object.keys(t).length)return;e.debug_meta=e.debug_meta||{},e.debug_meta.images=e.debug_meta.images||[];let r=e.debug_meta.images;Object.entries(t).forEach(([e,t])=>{r.push({type:"sourcemap",code_file:e,debug_id:t})})}(e),"number"==typeof m&&m>0)?function(e,t,r){if(!e)return null;let n={...e,...e.breadcrumbs&&{breadcrumbs:e.breadcrumbs.map(e=>({...e,...e.data&&{data:tj(e.data,t,r)}}))},...e.user&&{user:tj(e.user,t,r)},...e.contexts&&{contexts:tj(e.contexts,t,r)},...e.extra&&{extra:tj(e.extra,t,r)}};return e.contexts?.trace&&n.contexts&&(n.contexts.trace=e.contexts.trace,e.contexts.trace.data&&(n.contexts.trace.data=tj(e.contexts.trace.data,t,r))),e.spans&&(n.spans=e.spans.map(e=>({...e,...e.data&&{data:tj(e.data,t,r)}}))),e.contexts?.flags&&n.contexts&&(n.contexts.flags=tj(e.contexts.flags,3,r)),n}(e,m,g):e)}let tD=["user","level","extra","contexts","tags","fingerprint","propagationContext"];function tU(e,t){return ej().captureEvent(e,t)}function tF(e,t){eL().setContext(e,t)}function tB(e){eL().addEventProcessor(e)}function t$(e){let t=eL(),r=ej(),{userAgent:n}=C.navigator||{},i=function(e){let t=eE(),r={sid:em(),init:!0,timestamp:t,started:t,duration:0,status:"ok",errors:0,ignoreDuration:!1,toJSON:()=>{var e;return e=r,{sid:`${e.sid}`,init:e.init,started:new Date(1e3*e.started).toISOString(),timestamp:new Date(1e3*e.timestamp).toISOString(),status:e.status,errors:e.errors,did:"number"==typeof e.did||"string"==typeof e.did?`${e.did}`:void 0,duration:e.duration,abnormal_mechanism:e.abnormal_mechanism,attrs:{release:e.release,environment:e.environment,ip_address:e.ipAddress,user_agent:e.userAgent}}}};return e&&ew(r,e),r}({user:r.getUser()||t.getUser(),...n&&{userAgent:n},...e}),a=t.getSession();return a?.status==="ok"&&ew(a,{status:"exited"}),tH(),t.setSession(i),i}function tH(){let e,t=eL(),r=ej().getSession()||t.getSession();r&&(e={},"ok"===r.status&&(e={status:"exited"}),ew(r,e)),tW(),t.setSession()}function tW(){let e=eL(),t=eU(),r=e.getSession();r&&t&&t.captureSession(r)}function tq(e=!1){if(e)return void tH();tW()}let tz=[];function tX(e,t){for(let r of t)r?.afterAllSetup&&r.afterAllSetup(e)}function tG(e,t,r){if(!r[t.name]){if(r[t.name]=t,-1===tz.indexOf(t.name)&&"function"==typeof t.setupOnce&&(t.setupOnce(),tz.push(t.name)),t.setup&&"function"==typeof t.setup&&t.setup(e),"function"==typeof t.preprocessEvent){let r=t.preprocessEvent.bind(t);e.on("preprocessEvent",(t,n)=>r(t,n,e))}if("function"==typeof t.processEvent){let r=t.processEvent.bind(t),n=Object.assign((t,n)=>r(t,n,e),{id:t.name});e.addEventProcessor(n)}}}let tV=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/,/^ResizeObserver loop completed with undelivered notifications.$/,/^Cannot redefine property: googletag$/,/^Can't find variable: gmo$/,/^undefined is not an object \(evaluating 'a\.[A-Z]'\)$/,'can\'t redefine non-configurable property "solana"',"vv().getRestrictions is not a function. (In 'vv().getRestrictions(1,a)', 'vv().getRestrictions' is undefined)","Can't find variable: _AutofillCallbackHandler",/^Non-Error promise rejection captured with value: Object Not Found Matching Id:\d+, MethodName:simulateEvent, ParamCount:\d+$/,/^Java exception was raised during method invocation$/],tY=(e={})=>{let t;return{name:"EventFilters",setup(r){t=tJ(e,r.getOptions())},processEvent:(r,n,i)=>(t||(t=tJ(e,i.getOptions())),!function(e,t){if(e.type){if("transaction"===e.type&&function(e,t){if(!t?.length)return!1;let r=e.transaction;return!!r&&eo(r,t)}(e,t.ignoreTransactions))return!0}else{var r,n,i;if(r=e,n=t.ignoreErrors,n?.length&&(function(e){let t=[];e.message&&t.push(e.message);try{let r=e.exception.values[e.exception.values.length-1];r?.value&&(t.push(r.value),r.type&&t.push(`${r.type}: ${r.value}`))}catch{}return t})(r).some(e=>eo(e,n)))return!0;if(i=e,i.exception?.values?.length&&!i.message&&!i.exception.values.some(e=>e.stacktrace||e.type&&"Error"!==e.type||e.value)||function(e,t){if(!t?.length)return!1;let r=tQ(e);return!!r&&eo(r,t)}(e,t.denyUrls)||!function(e,t){if(!t?.length)return!0;let r=tQ(e);return!r||eo(r,t)}(e,t.allowUrls))return!0}return!1}(r,t)?r:null)}},tK=(e={})=>({...tY(e),name:"InboundFilters"});function tJ(e={},t={}){return{allowUrls:[...e.allowUrls||[],...t.allowUrls||[]],denyUrls:[...e.denyUrls||[],...t.denyUrls||[]],ignoreErrors:[...e.ignoreErrors||[],...t.ignoreErrors||[],...e.disableErrorDefaults?[]:tV],ignoreTransactions:[...e.ignoreTransactions||[],...t.ignoreTransactions||[]]}}function tQ(e){try{let t=[...e.exception?.values??[]].reverse().find(e=>e.mechanism?.parent_id===void 0&&e.stacktrace?.frames?.length),r=t?.stacktrace?.frames;return r?function(e=[]){for(let t=e.length-1;t>=0;t--){let r=e[t];if(r&&"<anonymous>"!==r.filename&&"[native code]"!==r.filename)return r.filename||null}return null}(r):null}catch{return null}}let tZ=new WeakMap,t0=()=>({name:"FunctionToString",setupOnce(){l=Function.prototype.toString;try{Function.prototype.toString=function(...e){let t=ed(this),r=tZ.has(eU())&&void 0!==t?t:this;return l.apply(r,e)}}catch{}},setup(e){tZ.set(e,!0)}}),t1=()=>{let e;return{name:"Dedupe",processEvent(t){if(t.type)return t;try{var r,n;if(r=t,(n=e)&&(function(e,t){let r=e.message,n=t.message;return(!!r||!!n)&&(!r||!!n)&&(!!r||!n)&&r===n&&!!t3(e,t)&&!!t2(e,t)&&!0}(r,n)||function(e,t){let r=t8(t),n=t8(e);return!!r&&!!n&&r.type===n.type&&r.value===n.value&&!!t3(e,t)&&!!t2(e,t)}(r,n)))return null}catch{}return e=t}}};function t2(e,t){let r=tN(e),n=tN(t);if(!r&&!n)return!0;if(r&&!n||!r&&n||n.length!==r.length)return!1;for(let e=0;e<n.length;e++){let t=n[e],i=r[e];if(t.filename!==i.filename||t.lineno!==i.lineno||t.colno!==i.colno||t.function!==i.function)return!1}return!0}function t3(e,t){let r=e.fingerprint,n=t.fingerprint;if(!r&&!n)return!0;if(r&&!n||!r&&n)return!1;try{return r.join("")===n.join("")}catch{return!1}}function t8(e){return e.exception?.values?.[0]}function t5(e,t=[]){return[e,t]}function t4(e,t){for(let r of e[1]){let e=r[0].type;if(t(r,e))return!0}return!1}function t6(e){let t=A(C);return t.encodePolyfill?t.encodePolyfill(e):new TextEncoder().encode(e)}let t7={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default",profile:"profile",profile_chunk:"profile",replay_event:"replay",replay_recording:"replay",check_in:"monitor",feedback:"feedback",span:"span",raw_security:"security",log:"log_item"};function t9(e){if(!e?.sdk)return;let{name:t,version:r}=e.sdk;return{name:t,version:r}}function re(e,t,r,n){let i=e.sdkProcessingMetadata?.dynamicSamplingContext;return{event_id:e.event_id,sent_at:new Date().toISOString(),...t&&{sdk:t},...!!r&&n&&{dsn:e6(n)},...i&&{trace:i}}}let rt=Symbol.for("SentryInternalError"),rr=Symbol.for("SentryDoNotSendEventError");function rn(e){return{message:e,[rt]:!0}}function ri(e){return{message:e,[rr]:!0}}class ra{constructor(e){if(this._options=e,this._integrations={},this._numProcessing=0,this._outcomes={},this._hooks={},this._eventProcessors=[],e.dsn&&(this._dsn=function(e){var t;let r="string"==typeof e?e7(e):e9(e);if(r&&(t=0,1))return r}(e.dsn)),this._dsn){let t=function(e,t,r){return t||`${function(e){let t=e.protocol?`${e.protocol}:`:"",r=e.port?`:${e.port}`:"";return`${t}//${e.host}${r}${e.path?`/${e.path}`:""}/api/`}(e)}${e.projectId}/envelope/?${function(e,t){let r={sentry_version:"7"};return e.publicKey&&(r.sentry_key=e.publicKey),t&&(r.sentry_client=`${t.name}/${t.version}`),new URLSearchParams(r).toString()}(e,r)}`}(this._dsn,e.tunnel,e._metadata?e._metadata.sdk:void 0);this._transport=e.transport({tunnel:this._options.tunnel,recordDroppedEvent:this.recordDroppedEvent.bind(this),...e.transportOptions,url:t})}}captureException(e,t,r){let n=em();if(ev(e))return n;let i={event_id:n,...t};return this._process(this.eventFromException(e,i).then(e=>this._captureEvent(e,i,r))),i.event_id}captureMessage(e,t,r,n){let i={event_id:em(),...r},a=V(e)?e:String(e),s=Y(e)?this.eventFromMessage(a,t,i):this.eventFromException(e,i);return this._process(s.then(e=>this._captureEvent(e,i,n))),i.event_id}captureEvent(e,t,r){let n=em();if(t?.originalException&&ev(t.originalException))return n;let i={event_id:n,...t},a=e.sdkProcessingMetadata||{},s=a.capturedSpanScope,o=a.capturedSpanIsolationScope;return this._process(this._captureEvent(e,i,s||r,o)),i.event_id}captureSession(e){this.sendSession(e),ew(e,{init:!1})}getDsn(){return this._dsn}getOptions(){return this._options}getSdkMetadata(){return this._options._metadata}getTransport(){return this._transport}flush(e){let t=this._transport;return t?(this.emit("flush"),this._isClientDoneProcessing(e).then(r=>t.flush(e).then(e=>r&&e))):eB(!0)}close(e){return this.flush(e).then(e=>(this.getOptions().enabled=!1,this.emit("close"),e))}getEventProcessors(){return this._eventProcessors}addEventProcessor(e){this._eventProcessors.push(e)}init(){(this._isEnabled()||this._options.integrations.some(({name:e})=>e.startsWith("Spotlight")))&&this._setupIntegrations()}getIntegrationByName(e){return this._integrations[e]}addIntegration(e){let t=this._integrations[e.name];tG(this,e,this._integrations),t||tX(this,[e])}sendEvent(e,t={}){this.emit("beforeSendEvent",e,t);let r=function(e,t,r,n){var i;let a=t9(r),s=e.type&&"replay_event"!==e.type?e.type:"event";(i=r?.sdk)&&(e.sdk=e.sdk||{},e.sdk.name=e.sdk.name||i.name,e.sdk.version=e.sdk.version||i.version,e.sdk.integrations=[...e.sdk.integrations||[],...i.integrations||[]],e.sdk.packages=[...e.sdk.packages||[],...i.packages||[]]);let o=re(e,a,n,t);return delete e.sdkProcessingMetadata,t5(o,[[{type:s},e]])}(e,this._dsn,this._options._metadata,this._options.tunnel);for(let e of t.attachments||[])r=function(e,t){let[r,n]=e;return[r,[...n,t]]}(r,function(e){let t="string"==typeof e.data?t6(e.data):e.data;return[{type:"attachment",length:t.length,filename:e.filename,content_type:e.contentType,attachment_type:e.attachmentType},t]}(e));let n=this.sendEnvelope(r);n&&n.then(t=>this.emit("afterSendEvent",e,t),null)}sendSession(e){let{release:t,environment:r=eF}=this._options;if("aggregates"in e){let n=e.attrs||{};if(!n.release&&!t)return;n.release=n.release||t,n.environment=n.environment||r,e.attrs=n}else{if(!e.release&&!t)return;e.release=e.release||t,e.environment=e.environment||r}this.emit("beforeSendSession",e);let n=function(e,t,r,n){let i=t9(r);return t5({sent_at:new Date().toISOString(),...i&&{sdk:i},...!!n&&t&&{dsn:e6(t)}},["aggregates"in e?[{type:"sessions"},e]:[{type:"session"},e.toJSON()]])}(e,this._dsn,this._options._metadata,this._options.tunnel);this.sendEnvelope(n)}recordDroppedEvent(e,t,r=1){if(this._options.sendClientReports){let n=`${e}:${t}`;this._outcomes[n]=(this._outcomes[n]||0)+r}}on(e,t){let r=this._hooks[e]=this._hooks[e]||[];return r.push(t),()=>{let e=r.indexOf(t);e>-1&&r.splice(e,1)}}emit(e,...t){let r=this._hooks[e];r&&r.forEach(e=>e(...t))}sendEnvelope(e){return(this.emit("beforeEnvelope",e),this._isEnabled()&&this._transport)?this._transport.send(e).then(null,e=>e):eB({})}_setupIntegrations(){let{integrations:e}=this._options;this._integrations=function(e,t){let r={};return t.forEach(t=>{t&&tG(e,t,r)}),r}(this,e),tX(this,e)}_updateSessionFromEvent(e,t){let r="fatal"===t.level,n=!1,i=t.exception?.values;if(i)for(let e of(n=!0,i)){let t=e.mechanism;if(t?.handled===!1){r=!0;break}}let a="ok"===e.status;(a&&0===e.errors||a&&r)&&(ew(e,{...r&&{status:"crashed"},errors:e.errors||Number(n||r)}),this.captureSession(e))}_isClientDoneProcessing(e){return new eH(t=>{let r=0,n=setInterval(()=>{0==this._numProcessing?(clearInterval(n),t(!0)):(r+=1,e&&r>=e&&(clearInterval(n),t(!1)))},1)})}_isEnabled(){return!1!==this.getOptions().enabled&&void 0!==this._transport}_prepareEvent(e,t,r,n){let i=this.getOptions(),a=Object.keys(this._integrations);return!t.integrations&&a?.length&&(t.integrations=a),this.emit("preprocessEvent",e,t),e.type||n.setLastEventId(e.event_id||t.event_id),tL(i,e,t,r,this,n).then(e=>(null===e||(this.emit("postprocessEvent",e,t),e.contexts={trace:function(e){let{traceId:t,parentSpanId:r,propagationSpanId:n}=e.getPropagationContext(),i={trace_id:t,span_id:n||eP()};return r&&(i.parent_span_id=r),i}(r),...e.contexts},e.sdkProcessingMetadata={dynamicSamplingContext:tR(this,r),...e.sdkProcessingMetadata}),e))}_captureEvent(e,t={},r=ej(),n=eL()){return this._processEvent(e,t,r,n).then(e=>e.event_id,e=>{})}_processEvent(e,t,r,n){let i=this.getOptions(),{sampleRate:a}=i,s=ro(e),o=rs(e),l=e.type||"error",u=`before send for type \`${l}\``,c=void 0===a?void 0:ta(a);if(o&&"number"==typeof c&&Math.random()>c)return this.recordDroppedEvent("sample_rate","error"),e$(ri(`Discarding event because it's not included in the random sample (sampling rate = ${a})`));let d="replay_event"===l?"replay":l;return this._prepareEvent(e,t,r,n).then(e=>{if(null===e)throw this.recordDroppedEvent("event_processor",d),ri("An event processor returned `null`, will not send event.");return t.data&&!0===t.data.__sentry__?e:function(e,t){let r=`${t} must return \`null\` or a valid event.`;if(Q(e))return e.then(e=>{if(!K(e)&&null!==e)throw rn(r);return e},e=>{throw rn(`${t} rejected with ${e}`)});if(!K(e)&&null!==e)throw rn(r);return e}(function(e,t,r,n){let{beforeSend:i,beforeSendTransaction:a,beforeSendSpan:s}=t,o=r;if(rs(o)&&i)return i(o,n);if(ro(o)){if(s){let e=s(function(e){let{trace_id:t,parent_span_id:r,span_id:n,status:i,origin:a,data:s,op:o}=e.contexts?.trace??{};return{data:s??{},description:e.transaction,op:o,parent_span_id:r,span_id:n??"",start_timestamp:e.start_timestamp??0,status:i,timestamp:e.timestamp,trace_id:t??"",origin:a,profile_id:s?.[eQ],exclusive_time:s?.[eZ],measurements:e.measurements,is_segment:!0}}(o));if(e)o=eR(r,{type:"transaction",timestamp:e.timestamp,start_timestamp:e.start_timestamp,transaction:e.description,contexts:{trace:{trace_id:e.trace_id,span_id:e.span_id,parent_span_id:e.parent_span_id,op:e.op,status:e.status,origin:e.origin,data:{...e.data,...e.profile_id&&{[eQ]:e.profile_id},...e.exclusive_time&&{[eZ]:e.exclusive_time}}}},measurements:e.measurements});else tE();if(o.spans){let e=[];for(let t of o.spans){let r=s(t);r?e.push(r):(tE(),e.push(t))}o.spans=e}}if(a){if(o.spans){let e=o.spans.length;o.sdkProcessingMetadata={...r.sdkProcessingMetadata,spanCountBeforeProcessing:e}}return a(o,n)}}return o}(0,i,e,t),u)}).then(i=>{if(null===i){if(this.recordDroppedEvent("before_send",d),s){let t=1+(e.spans||[]).length;this.recordDroppedEvent("before_send","span",t)}throw ri(`${u} returned \`null\`, will not send event.`)}let a=r.getSession()||n.getSession();if(o&&a&&this._updateSessionFromEvent(a,i),s){let e=(i.sdkProcessingMetadata?.spanCountBeforeProcessing||0)-(i.spans?i.spans.length:0);e>0&&this.recordDroppedEvent("before_send","span",e)}let l=i.transaction_info;return s&&l&&i.transaction!==e.transaction&&(i.transaction_info={...l,source:"custom"}),this.sendEvent(i,t),i}).then(null,e=>{var t,r;if((t=e)&&"object"==typeof t&&rr in t||(r=e)&&"object"==typeof r&&rt in r)throw e;throw this.captureException(e,{data:{__sentry__:!0},originalException:e}),rn(`Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.
Reason: ${e}`)})}_process(e){this._numProcessing++,e.then(e=>(this._numProcessing--,e),e=>(this._numProcessing--,e))}_clearOutcomes(){let e=this._outcomes;return this._outcomes={},Object.entries(e).map(([e,t])=>{let[r,n]=e.split(":");return{reason:r,category:n,quantity:t}})}_flushOutcomes(){var e;let t=this._clearOutcomes();if(0===t.length||!this._dsn)return;let r=t5((e=this._options.tunnel&&e6(this._dsn))?{dsn:e}:{},[[{type:"client_report"},{timestamp:eb(),discarded_events:t}]]);this.sendEnvelope(r)}}function rs(e){return void 0===e.type}function ro(e){return"transaction"===e.type}function rl(e,t){var r;let n=t??(r=e,ru().get(r))??[];if(0===n.length)return;let i=e.getOptions(),a=function(e,t,r,n){let i={};return t?.sdk&&(i.sdk={name:t.sdk.name,version:t.sdk.version}),r&&n&&(i.dsn=e6(n)),t5(i,[[{type:"log",item_count:e.length,content_type:"application/vnd.sentry.items.log+json"},{items:e}]])}(n,i._metadata,i.tunnel,e.getDsn());ru().set(e,[]),e.emit("flushLogs"),e.sendEnvelope(a)}function ru(){return N("clientToLogBufferMap",()=>new WeakMap)}function rc(e){e.user?.ip_address===void 0&&(e.user={...e.user,ip_address:"{{auto}}"})}function rd(e){"aggregates"in e?e.attrs?.ip_address===void 0&&(e.attrs={...e.attrs,ip_address:"{{auto}}"}):void 0===e.ipAddress&&(e.ipAddress="{{auto}}")}function rp(e,t){let r=rf(e,t),n={type:function(e){let t=e?.name;return!t&&rg(e)?e.message&&Array.isArray(e.message)&&2==e.message.length?e.message[0]:"WebAssembly.Exception":t}(t),value:function(e){let t=e?.message;return rg(e)?Array.isArray(e.message)&&2==e.message.length?e.message[1]:"wasm exception":t?t.error&&"string"==typeof t.error.message?t.error.message:t:"No error message"}(t)};return r.length&&(n.stacktrace={frames:r}),void 0===n.type&&""===n.value&&(n.value="Unrecoverable error caught"),n}function rh(e,t){return{exception:{values:[rp(e,t)]}}}function rf(e,t){var r,n;let i=t.stacktrace||t.stack||"",a=(r=t)&&rm.test(r.message)?1:0,s="number"==typeof(n=t).framesToPop?n.framesToPop:0;try{return e(i,a,s)}catch{}return[]}let rm=/Minified React error #\d+;/i;function rg(e){return"undefined"!=typeof WebAssembly&&void 0!==WebAssembly.Exception&&e instanceof WebAssembly.Exception}function r_(e,t,r,n,i){let a;if(z(t)&&t.error)return rh(e,t.error);if(X(t)||q(t,"DOMException")){if("stack"in t)a=rh(e,t);else{let i=t.name||(X(t)?"DOMError":"DOMException"),s=t.message?`${i}: ${t.message}`:i;e_(a=ry(e,s,r,n),s)}return"code"in t&&(a.tags={...a.tags,"DOMException.code":`${t.code}`}),a}return W(t)?rh(e,t):(K(t)||J(t)?ey(a=function(e,t,r,n){let i=eU(),a=i?.getOptions().normalizeDepth,s=function(e){for(let t in e)if(Object.prototype.hasOwnProperty.call(e,t)){let r=e[t];if(r instanceof Error)return r}}(t),o={__serialized__:function e(t,r=3,n=102400){let i=tj(t,r);return~-encodeURI(JSON.stringify(i)).split(/%..|./).length>n?e(t,r-1,n):i}(t,a)};if(s)return{exception:{values:[rp(e,s)]},extra:o};let l={exception:{values:[{type:J(t)?t.constructor.name:n?"UnhandledRejection":"Error",value:function(e,{isUnhandledRejection:t}){let r=function(e,t=40){let r=Object.keys(ep(e));r.sort();let n=r[0];if(!n)return"[object has no keys]";if(n.length>=t)return ea(n,t);for(let e=r.length;e>0;e--){let n=r.slice(0,e).join(", ");if(!(n.length>t)){if(e===r.length)return n;return ea(n,t)}}return""}(e),n=t?"promise rejection":"exception";if(z(e))return`Event \`ErrorEvent\` captured as ${n} with message \`${e.message}\``;if(J(e)){let t=function(e){try{let t=Object.getPrototypeOf(e);return t?t.constructor.name:void 0}catch{}}(e);return`Event \`${t}\` (type=${e.type}) captured as ${n}`}return`Object captured as ${n} with keys: ${r}`}(t,{isUnhandledRejection:n})}]},extra:o};if(r){let t=rf(e,r);t.length&&(l.exception.values[0].stacktrace={frames:t})}return l}(e,t,r,i),{synthetic:!0}):(e_(a=ry(e,t,r,n),`${t}`,void 0),ey(a,{synthetic:!0})),a)}function ry(e,t,r,n){let i={};if(n&&r){let n=rf(e,r);n.length&&(i.exception={values:[{value:t,stacktrace:{frames:n}}]}),ey(i,{synthetic:!0})}if(V(t)){let{__sentry_template_string__:e,__sentry_template_values__:r}=t;return i.logentry={message:e,params:r},i}return i.message=t,i}let rv=0;function rb(e,t={}){if("function"!=typeof e)return e;try{let t=e.__sentry_wrapped__;if(t)if("function"==typeof t)return t;else return e;if(ed(e))return e}catch{return e}let r=function(...r){try{let n=r.map(e=>rb(e,t));return e.apply(this,n)}catch(e){throw rv++,setTimeout(()=>{rv--}),eD(n=>{var i,a;n.addEventProcessor(e=>(t.mechanism&&(e_(e,void 0,void 0),ey(e,t.mechanism)),e.extra={...e.extra,arguments:r},e)),i=e,ej().captureException(i,function(e){if(e){var t;return(t=e)instanceof eO||"function"==typeof t||Object.keys(e).some(e=>tD.includes(e))?{captureContext:e}:e}}(void 0))}),e}};try{for(let t in e)Object.prototype.hasOwnProperty.call(e,t)&&(r[t]=e[t])}catch{}ec(r,e),eu(e,"__sentry_wrapped__",r);try{Object.getOwnPropertyDescriptor(r,"name").configurable&&Object.defineProperty(r,"name",{get:()=>e.name})}catch{}return r}function rE(){let e=en(),{referrer:t}=C.document||{},{userAgent:r}=C.navigator||{};return{url:e,headers:{...t&&{Referer:t},...r&&{"User-Agent":r}}}}class rS extends ra{constructor(e){var t;let r=(t=e,{release:"string"==typeof __SENTRY_RELEASE__?__SENTRY_RELEASE__:C.SENTRY_RELEASE?.id,sendClientReports:!0,parentSpanIsAlwaysRootSpan:!0,...t});$(r,"browser",["browser"],C.SENTRY_SDK_SOURCE||"npm"),super(r);let{sendDefaultPii:n,sendClientReports:i,enableLogs:a,_experiments:s}=this._options,o=a??s?.enableLogs;C.document&&(i||o)&&C.document.addEventListener("visibilitychange",()=>{"hidden"===C.document.visibilityState&&(i&&this._flushOutcomes(),o&&rl(this))}),o&&(this.on("flush",()=>{rl(this)}),this.on("afterCaptureLog",()=>{this._logFlushIdleTimeout&&clearTimeout(this._logFlushIdleTimeout),this._logFlushIdleTimeout=setTimeout(()=>{rl(this)},5e3)})),n&&(this.on("postprocessEvent",rc),this.on("beforeSendSession",rd))}eventFromException(e,t){return function(e,t,r,n){let i=r_(e,t,r?.syntheticException||void 0,n);return ey(i),i.level="error",r?.event_id&&(i.event_id=r.event_id),eB(i)}(this._options.stackParser,e,t,this._options.attachStacktrace)}eventFromMessage(e,t="info",r){return function(e,t,r="info",n,i){let a=ry(e,t,n?.syntheticException||void 0,i);return a.level=r,n?.event_id&&(a.event_id=n.event_id),eB(a)}(this._options.stackParser,e,t,r,this._options.attachStacktrace)}_prepareEvent(e,t,r,n){return e.platform=e.platform||"javascript",super._prepareEvent(e,t,r,n)}}let rw={},rR={};function rP(e,t){rw[e]=rw[e]||[],rw[e].push(t)}function rT(e,t){if(!rR[e]){rR[e]=!0;try{t()}catch(e){}}}function rx(e,t){let r=e&&rw[e];if(r)for(let e of r)try{e(t)}catch(e){}}function rO(){"console"in C&&j.forEach(function(e){e in C.console&&el(C.console,e,function(t){return L[e]=t,function(...t){rx("console",{args:t,level:e});let r=L[e];r?.apply(C.console,t)}})})}function rI(e){return e&&/^function\s+\w+\(\)\s+\{\s+\[native code\]\s+\}$/.test(e.toString())}function rC(e,t){let r="fetch";rP(r,e),rT(r,()=>rM(void 0,t))}function rM(e,t=!1){(!t||function(){if("string"==typeof EdgeRuntime)return!0;if(!function(){if(!("fetch"in C))return!1;try{return new Headers,new Request("http://www.example.com"),new Response,!0}catch{return!1}}())return!1;if(rI(C.fetch))return!0;let e=!1,t=C.document;if(t&&"function"==typeof t.createElement)try{let r=t.createElement("iframe");r.hidden=!0,t.head.appendChild(r),r.contentWindow?.fetch&&(e=rI(r.contentWindow.fetch)),t.head.removeChild(r)}catch(e){}return e}())&&el(C,"fetch",function(t){return function(...r){let n=Error(),{method:i,url:a}=function(e){if(0===e.length)return{method:"GET",url:""};if(2===e.length){let[t,r]=e;return{url:rj(t),method:rN(r,"method")?String(r.method).toUpperCase():"GET"}}let t=e[0];return{url:rj(t),method:rN(t,"method")?String(t.method).toUpperCase():"GET"}}(r),s={args:r,fetchData:{method:i,url:a},startTimestamp:1e3*eE(),virtualError:n,headers:function(e){let[t,r]=e;try{if("object"==typeof r&&null!==r&&"headers"in r&&r.headers)return new Headers(r.headers);if(et(t))return new Headers(t.headers)}catch{}}(r)};return e||rx("fetch",{...s}),t.apply(C,r).then(async t=>(e?e(t):rx("fetch",{...s,endTimestamp:1e3*eE(),response:t}),t),e=>{if(rx("fetch",{...s,endTimestamp:1e3*eE(),error:e}),W(e)&&void 0===e.stack&&(e.stack=n.stack,eu(e,"framesToPop",1)),e instanceof TypeError&&("Failed to fetch"===e.message||"Load failed"===e.message||"NetworkError when attempting to fetch resource."===e.message))try{let t=new URL(s.fetchData.url);e.message=`${e.message} (${t.host})`}catch{}throw e})}})}async function rk(e,t){if(e?.body){let r=e.body,n=r.getReader(),i=setTimeout(()=>{r.cancel().then(null,()=>{})},9e4),a=!0;for(;a;){let e;try{e=setTimeout(()=>{r.cancel().then(null,()=>{})},5e3);let{done:i}=await n.read();clearTimeout(e),i&&(t(),a=!1)}catch{a=!1}finally{clearTimeout(e)}}clearTimeout(i),n.releaseLock(),r.cancel().then(null,()=>{})}}function rA(e){let t;try{t=e.clone()}catch{return}rk(t,()=>{rx("fetch-body-resolved",{endTimestamp:1e3*eE(),response:e})})}function rN(e,t){return!!e&&"object"==typeof e&&!!e[t]}function rj(e){return"string"==typeof e?e:e?rN(e,"url")?e.url:e.toString?e.toString():"":""}function rL(e,t){let r=eU(),n=eL();if(!r)return;let{beforeBreadcrumb:i=null,maxBreadcrumbs:a=100}=r.getOptions();if(a<=0)return;let s={timestamp:eb(),...e},o=i?D(()=>i(s,t)):s;null!==o&&(r.emit&&r.emit("beforeAddBreadcrumb",o,t),n.addBreadcrumb(o,a))}function rD(e){if(void 0!==e)return e>=400&&e<500?"warning":e>=500?"error":void 0}function rU(e){return"isRelative"in e}function rF(e,t){let r=0>=e.indexOf("://")&&0!==e.indexOf("//"),n=t??(r?"thismessage:/":void 0);try{if("canParse"in URL&&!URL.canParse(e,n))return;let t=new URL(e,n);if(r)return{isRelative:r,pathname:t.pathname,search:t.search,hash:t.hash};return t}catch{}}function rB(e){if(!e)return{};let t=e.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!t)return{};let r=t[6]||"",n=t[8]||"";return{host:t[4],path:t[5],protocol:t[2],search:r,hash:n,relative:t[5]+r+n}}function r$(e){return e.split(/[?#]/,1)[0]}function rH(e){rP("dom",e),rT("dom",rW)}function rW(){if(!C.document)return;let e=rx.bind(null,"dom"),t=rq(e,!0);C.document.addEventListener("click",t,!1),C.document.addEventListener("keypress",t,!1),["EventTarget","Node"].forEach(t=>{let r=C[t]?.prototype;r?.hasOwnProperty?.("addEventListener")&&(el(r,"addEventListener",function(t){return function(r,n,i){if("click"===r||"keypress"==r)try{let n=this.__sentry_instrumentation_handlers__=this.__sentry_instrumentation_handlers__||{},a=n[r]=n[r]||{refCount:0};if(!a.handler){let n=rq(e);a.handler=n,t.call(this,r,n,i)}a.refCount++}catch{}return t.call(this,r,n,i)}}),el(r,"removeEventListener",function(e){return function(t,r,n){if("click"===t||"keypress"==t)try{let r=this.__sentry_instrumentation_handlers__||{},i=r[t];i&&(i.refCount--,i.refCount<=0&&(e.call(this,t,i.handler,n),i.handler=void 0,delete r[t]),0===Object.keys(r).length&&delete this.__sentry_instrumentation_handlers__)}catch{}return e.call(this,t,r,n)}}))})}function rq(e,t=!1){return r=>{var n;if(!r||r._sentryCaptured)return;let i=function(e){try{return e.target}catch{return null}}(r);if(n=r.type,"keypress"===n&&(!i?.tagName||"INPUT"!==i.tagName&&"TEXTAREA"!==i.tagName&&!i.isContentEditable&&1))return;eu(r,"_sentryCaptured",!0),i&&!i._sentryId&&eu(i,"_sentryId",em());let a="keypress"===r.type?"input":r.type;!function(e){if(e.type!==c)return!1;try{if(!e.target||e.target._sentryId!==d)return!1}catch{}return!0}(r)&&(e({event:r,name:a,global:t}),c=r.type,d=i?i._sentryId:void 0),clearTimeout(u),u=C.setTimeout(()=>{d=void 0,c=void 0},1e3)}}let rz="__sentry_xhr_v3__";function rX(e){rP("xhr",e),rT("xhr",rG)}function rG(){if(!C.XMLHttpRequest)return;let e=XMLHttpRequest.prototype;e.open=new Proxy(e.open,{apply(e,t,r){let n=Error(),i=1e3*eE(),a=G(r[0])?r[0].toUpperCase():void 0,s=function(e){if(G(e))return e;try{return e.toString()}catch{}}(r[1]);if(!a||!s)return e.apply(t,r);t[rz]={method:a,url:s,request_headers:{}},"POST"===a&&s.match(/sentry_key/)&&(t.__sentry_own_request__=!0);let o=()=>{let e=t[rz];if(e&&4===t.readyState){try{e.status_code=t.status}catch{}rx("xhr",{endTimestamp:1e3*eE(),startTimestamp:i,xhr:t,virtualError:n})}};return"onreadystatechange"in t&&"function"==typeof t.onreadystatechange?t.onreadystatechange=new Proxy(t.onreadystatechange,{apply:(e,t,r)=>(o(),e.apply(t,r))}):t.addEventListener("readystatechange",o),t.setRequestHeader=new Proxy(t.setRequestHeader,{apply(e,t,r){let[n,i]=r,a=t[rz];return a&&G(n)&&G(i)&&(a.request_headers[n.toLowerCase()]=i),e.apply(t,r)}}),e.apply(t,r)}}),e.send=new Proxy(e.send,{apply(e,t,r){let n=t[rz];return n&&(void 0!==r[0]&&(n.body=r[0]),rx("xhr",{startTimestamp:1e3*eE(),xhr:t})),e.apply(t,r)}})}function rV(e){let t="history";rP(t,e),rT(t,rY)}function rY(){function e(e){return function(...t){let r=t.length>2?t[2]:void 0;if(r){let n=p,i=function(e){try{return new URL(e,C.location.origin).toString()}catch{return e}}(String(r));if(p=i,n===i)return e.apply(this,t);rx("history",{from:n,to:i})}return e.apply(this,t)}}C.addEventListener("popstate",()=>{let e=C.location.href,t=p;p=e,t!==e&&rx("history",{from:t,to:e})}),"history"in C&&C.history&&(el(C.history,"pushState",e),el(C.history,"replaceState",e))}let rK=(e={})=>{let t={console:!0,dom:!0,fetch:!0,history:!0,sentry:!0,xhr:!0,...e};return{name:"Breadcrumbs",setup(e){var r,n,i,a,s,o,l;t.console&&function(e){let t="console";rP(t,e),rT(t,rO)}((r=e,function(e){var t;if(eU()!==r)return;let n={category:"console",data:{arguments:e.args,logger:"console"},level:"warn"===(t=e.level)?"warning":["fatal","error","warning","log","info","debug"].includes(t)?t:"log",message:es(e.args," ")};if("assert"===e.level)if(!1!==e.args[0])return;else n.message=`Assertion failed: ${es(e.args.slice(1)," ")||"console.assert"}`,n.data.arguments=e.args.slice(1);rL(n,{input:e.args,level:e.level})})),t.dom&&rH((n=e,i=t.dom,function(e){let t,r;if(eU()!==n)return;let a="object"==typeof i?i.serializeAttribute:void 0,s="object"==typeof i&&"number"==typeof i.maxStringLength?i.maxStringLength:void 0;s&&s>1024&&(s=1024),"string"==typeof a&&(a=[a]);try{var o;let n=e.event,i=(o=n)&&o.target?n.target:n;t=er(i,{keyAttrs:a,maxStringLength:s}),r=ei(i)}catch{t="<unknown>"}if(0===t.length)return;let l={category:`ui.${e.name}`,message:t};r&&(l.data={"ui.component_name":r}),rL(l,{event:e.event,name:e.name,global:e.global})})),t.xhr&&rX((a=e,function(e){if(eU()!==a)return;let{startTimestamp:t,endTimestamp:r}=e,n=e.xhr[rz];if(!t||!r||!n)return;let{method:i,url:s,status_code:o,body:l}=n,u={xhr:e.xhr,input:l,startTimestamp:t,endTimestamp:r},c={category:"xhr",data:{method:i,url:s,status_code:o},type:"http",level:rD(o)};a.emit("beforeOutgoingRequestBreadcrumb",c,u),rL(c,u)})),t.fetch&&rC((s=e,function(e){if(eU()!==s)return;let{startTimestamp:t,endTimestamp:r}=e;if(r&&(!e.fetchData.url.match(/sentry_key/)||"POST"!==e.fetchData.method))if(e.fetchData.method,e.fetchData.url,e.error){let n=e.fetchData,i={data:e.error,input:e.args,startTimestamp:t,endTimestamp:r},a={category:"fetch",data:n,level:"error",type:"http"};s.emit("beforeOutgoingRequestBreadcrumb",a,i),rL(a,i)}else{let n=e.response,i={...e.fetchData,status_code:n?.status};e.fetchData.request_body_size,e.fetchData.response_body_size,n?.status;let a={input:e.args,response:n,startTimestamp:t,endTimestamp:r},o={category:"fetch",data:i,type:"http",level:rD(i.status_code)};s.emit("beforeOutgoingRequestBreadcrumb",o,a),rL(o,a)}})),t.history&&rV((o=e,function(e){if(eU()!==o)return;let t=e.from,r=e.to,n=rB(C.location.href),i=t?rB(t):void 0,a=rB(r);i?.path||(i=n),n.protocol===a.protocol&&n.host===a.host&&(r=a.relative),n.protocol===i.protocol&&n.host===i.host&&(t=i.relative),rL({category:"navigation",data:{from:t,to:r}})})),t.sentry&&e.on("beforeSendEvent",(l=e,function(e){eU()===l&&rL({category:`sentry.${"transaction"===e.type?"transaction":"event"}`,event_id:e.event_id,level:e.level,message:function(e){let{message:t,event_id:r}=e;if(t)return t;let n=eg(e);return n?n.type&&n.value?`${n.type}: ${n.value}`:n.type||n.value||r||"<unknown>":r||"<unknown>"}(e)},{event:e})}))}}},rJ=["EventTarget","Window","Node","ApplicationCache","AudioTrackList","BroadcastChannel","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","SharedWorker","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"],rQ=(e={})=>{let t={XMLHttpRequest:!0,eventTarget:!0,requestAnimationFrame:!0,setInterval:!0,setTimeout:!0,unregisterOriginalCallbacks:!1,...e};return{name:"BrowserApiErrors",setupOnce(){t.setTimeout&&el(C,"setTimeout",rZ),t.setInterval&&el(C,"setInterval",rZ),t.requestAnimationFrame&&el(C,"requestAnimationFrame",r0),t.XMLHttpRequest&&"XMLHttpRequest"in C&&el(XMLHttpRequest.prototype,"send",r1);let e=t.eventTarget;e&&(Array.isArray(e)?e:rJ).forEach(e=>(function(e,t){let r=C[e]?.prototype;r?.hasOwnProperty?.("addEventListener")&&(el(r,"addEventListener",function(r){return function(n,i,a){var s,o,l,u;try{s=i,"function"==typeof s.handleEvent&&(i.handleEvent=rb(i.handleEvent,{mechanism:{data:{function:"handleEvent",handler:tA(i),target:e},handled:!1,type:"instrument"}}))}catch{}return t.unregisterOriginalCallbacks&&(o=this,l=n,u=i,o&&"object"==typeof o&&"removeEventListener"in o&&"function"==typeof o.removeEventListener&&o.removeEventListener(l,u)),r.apply(this,[n,rb(i,{mechanism:{data:{function:"addEventListener",handler:tA(i),target:e},handled:!1,type:"instrument"}}),a])}}),el(r,"removeEventListener",function(e){return function(t,r,n){try{let i=r.__sentry_wrapped__;i&&e.call(this,t,i,n)}catch{}return e.call(this,t,r,n)}}))})(e,t))}}};function rZ(e){return function(...t){let r=t[0];return t[0]=rb(r,{mechanism:{data:{function:tA(e)},handled:!1,type:"instrument"}}),e.apply(this,t)}}function r0(e){return function(t){return e.apply(this,[rb(t,{mechanism:{data:{function:"requestAnimationFrame",handler:tA(e)},handled:!1,type:"instrument"}})])}}function r1(e){return function(...t){let r=this;return["onload","onerror","onprogress","onreadystatechange"].forEach(e=>{e in r&&"function"==typeof r[e]&&el(r,e,function(t){let r={mechanism:{data:{function:e,handler:tA(t)},handled:!1,type:"instrument"}},n=ed(t);return n&&(r.mechanism.data.handler=tA(n)),rb(t,r)})}),e.apply(this,t)}}let r2=()=>({name:"BrowserSession",setupOnce(){void 0!==C.document&&(t$({ignoreDuration:!0}),tq(),rV(({from:e,to:t})=>{void 0!==e&&e!==t&&(t$({ignoreDuration:!0}),tq())}))}}),r3=null;function r8(e){let t="error";rP(t,e),rT(t,r5)}function r5(){r3=C.onerror,C.onerror=function(e,t,r,n,i){return rx("error",{column:n,error:i,line:r,msg:e,url:t}),!!r3&&r3.apply(this,arguments)},C.onerror.__SENTRY_INSTRUMENTED__=!0}let r4=null;function r6(e){let t="unhandledrejection";rP(t,e),rT(t,r7)}function r7(){r4=C.onunhandledrejection,C.onunhandledrejection=function(e){return rx("unhandledrejection",e),!r4||r4.apply(this,arguments)},C.onunhandledrejection.__SENTRY_INSTRUMENTED__=!0}let r9=(e={})=>{let t={onerror:!0,onunhandledrejection:!0,...e};return{name:"GlobalHandlers",setupOnce(){Error.stackTraceLimit=50},setup(e){var r,n;t.onerror&&(r=e,r8(e=>{let{stackParser:t,attachStacktrace:n}=nt();if(eU()!==r||rv>0)return;let{msg:i,url:a,line:s,column:o,error:l}=e,u=function(e,t,r,n){let i=e.exception=e.exception||{},a=i.values=i.values||[],s=a[0]=a[0]||{},o=s.stacktrace=s.stacktrace||{},l=o.frames=o.frames||[],u=G(t)&&t.length>0?t:en();return 0===l.length&&l.push({colno:n,filename:u,function:"?",in_app:!0,lineno:r}),e}(r_(t,l||i,void 0,n,!1),a,s,o);u.level="error",tU(u,{originalException:l,mechanism:{handled:!1,type:"onerror"}})})),t.onunhandledrejection&&(n=e,r6(e=>{var t;let{stackParser:r,attachStacktrace:i}=nt();if(eU()!==n||rv>0)return;let a=function(e){if(Y(e))return e;try{if("reason"in e)return e.reason;if("detail"in e&&"reason"in e.detail)return e.detail.reason}catch{}return e}(e),s=Y(a)?(t=a,{exception:{values:[{type:"UnhandledRejection",value:`Non-Error promise rejection captured with value: ${String(t)}`}]}}):r_(r,a,void 0,i,!0);s.level="error",tU(s,{originalException:a,mechanism:{handled:!1,type:"onunhandledrejection"}})}))}}};function ne(e){}function nt(){let e=eU();return e?.getOptions()||{stackParser:()=>[],attachStacktrace:!1}}let nr=()=>({name:"HttpContext",preprocessEvent(e){if(!C.navigator&&!C.location&&!C.document)return;let t=rE(),r={...t.headers,...e.request?.headers};e.request={...t,...e.request,headers:r}}});function nn(e,t){e.mechanism=e.mechanism||{type:"generic",handled:!0},e.mechanism={...e.mechanism,..."AggregateError"===e.type&&{is_exception_group:!0},exception_id:t}}function ni(e,t,r,n){e.mechanism=e.mechanism||{type:"generic",handled:!0},e.mechanism={...e.mechanism,type:"chained",source:t,exception_id:r,parent_id:n}}let na=(e={})=>{let t=e.limit||5,r=e.key||"cause";return{name:"LinkedErrors",preprocessEvent(e,n,i){!function(e,t,r,n,i,a){if(!i.exception?.values||!a||!Z(a.originalException,Error))return;let s=i.exception.values.length>0?i.exception.values[i.exception.values.length-1]:void 0;s&&(i.exception.values=function e(t,r,n,i,a,s,o,l){if(s.length>=n+1)return s;let u=[...s];if(Z(i[a],Error)){nn(o,l);let s=t(r,i[a]),c=u.length;ni(s,a,c,l),u=e(t,r,n,i[a],a,[s,...u],s,c)}return Array.isArray(i.errors)&&i.errors.forEach((i,s)=>{if(Z(i,Error)){nn(o,l);let c=t(r,i),d=u.length;ni(c,`errors[${s}]`,d,l),u=e(t,r,n,i,a,[c,...u],c,d)}}),u}(e,t,n,a.originalException,r,i.exception.values,s,0))}(rp,i.getOptions().stackParser,r,t,e,n)}}};function ns(e,t,r,n){let i={filename:e,function:"<anonymous>"===t?"?":t,in_app:!0};return void 0!==r&&(i.lineno=r),void 0!==n&&(i.colno=n),i}let no=/^\s*at (\S+?)(?::(\d+))(?::(\d+))\s*$/i,nl=/^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,nu=/\((\S*)(?::(\d+))(?::(\d+))\)/,nc=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,nd=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,np=tC([30,e=>{let t=no.exec(e);if(t){let[,e,r,n]=t;return ns(e,"?",+r,+n)}let r=nl.exec(e);if(r){if(r[2]&&0===r[2].indexOf("eval")){let e=nu.exec(r[2]);e&&(r[2]=e[1],r[3]=e[2],r[4]=e[3])}let[e,t]=nh(r[1]||"?",r[2]);return ns(t,e,r[3]?+r[3]:void 0,r[4]?+r[4]:void 0)}}],[50,e=>{let t=nc.exec(e);if(t){if(t[3]&&t[3].indexOf(" > eval")>-1){let e=nd.exec(t[3]);e&&(t[1]=t[1]||"eval",t[3]=e[1],t[4]=e[2],t[5]="")}let e=t[3],r=t[1]||"?";return[r,e]=nh(r,e),ns(e,r,t[4]?+t[4]:void 0,t[5]?+t[5]:void 0)}}]),nh=(e,t)=>{let r=-1!==e.indexOf("safari-extension"),n=-1!==e.indexOf("safari-web-extension");return r||n?[-1!==e.indexOf("@")?e.split("@")[0]:"?",r?`safari-extension:${t}`:`safari-web-extension:${t}`]:[e,t]},nf=Symbol.for("SentryBufferFullError");function nm(e,t,r=Date.now()){return(e[t]||e.all||0)>r}function ng(e,{statusCode:t,headers:r},n=Date.now()){let i={...e},a=r?.["x-sentry-rate-limits"],s=r?.["retry-after"];if(a)for(let e of a.trim().split(",")){let[t,r,,,a]=e.split(":",5),s=parseInt(t,10),o=(isNaN(s)?60:s)*1e3;if(r)for(let e of r.split(";"))"metric_bucket"===e?(!a||a.split(";").includes("custom"))&&(i[e]=n+o):i[e]=n+o;else i.all=n+o}else s?i.all=n+function(e,t=Date.now()){let r=parseInt(`${e}`,10);if(!isNaN(r))return 1e3*r;let n=Date.parse(`${e}`);return isNaN(n)?6e4:n-t}(s,n):429===t&&(i.all=n+6e4);return i}let n_={};function ny(e){let t=n_[e];if(t)return t;let r=C[e];if(rI(r))return n_[e]=r.bind(C);let n=C.document;if(n&&"function"==typeof n.createElement)try{let t=n.createElement("iframe");t.hidden=!0,n.head.appendChild(t);let i=t.contentWindow;i?.[e]&&(r=i[e]),n.head.removeChild(t)}catch(e){}return r?n_[e]=r.bind(C):r}function nv(...e){return ny("setTimeout")(...e)}function nb(e,t=ny("fetch")){let r=0,n=0;return function(e,t,r=function(e){let t=[];function r(e){return t.splice(t.indexOf(e),1)[0]||Promise.resolve(void 0)}return{$:t,add:function(n){if(!(void 0===e||t.length<e))return e$(nf);let i=n();return -1===t.indexOf(i)&&t.push(i),i.then(()=>r(i)).then(null,()=>r(i).then(null,()=>{})),i},drain:function(e){return new eH((r,n)=>{let i=t.length;if(!i)return r(!0);let a=setTimeout(()=>{e&&e>0&&r(!1)},e);t.forEach(e=>{eB(e).then(()=>{--i||(clearTimeout(a),r(!0))},n)})})}}}(e.bufferSize||64)){let n={};return{send:function(i){let a=[];if(t4(i,(t,r)=>{let i=t7[r];nm(n,i)?e.recordDroppedEvent("ratelimit_backoff",i):a.push(t)}),0===a.length)return eB({});let s=t5(i[0],a),o=t=>{t4(s,(r,n)=>{e.recordDroppedEvent(t,t7[n])})};return r.add(()=>t({body:function(e){let[t,r]=e,n=JSON.stringify(t);function i(e){"string"==typeof n?n="string"==typeof e?n+e:[t6(n),e]:n.push("string"==typeof e?t6(e):e)}for(let e of r){let[t,r]=e;if(i(`
${JSON.stringify(t)}
`),"string"==typeof r||r instanceof Uint8Array)i(r);else{let e;try{e=JSON.stringify(r)}catch{e=JSON.stringify(tj(r))}i(e)}}return"string"==typeof n?n:function(e){let t=new Uint8Array(e.reduce((e,t)=>e+t.length,0)),r=0;for(let n of e)t.set(n,r),r+=n.length;return t}(n)}(s)}).then(e=>(void 0!==e.statusCode&&(e.statusCode<200||e.statusCode),n=ng(n,e),e),e=>{throw o("network_error"),e})).then(e=>e,e=>{if(e===nf)return o("queue_overflow"),eB({});throw e})},flush:e=>r.drain(e)}}(e,function(i){let a=i.body.length;r+=a,n++;let s={body:i.body,method:"POST",referrerPolicy:"strict-origin",headers:e.headers,keepalive:r<=6e4&&n<15,...e.fetchOptions};if(!t)return n_.fetch=void 0,e$("No fetch implementation available");try{return t(e.url,s).then(e=>(r-=a,n--,{statusCode:e.status,headers:{"x-sentry-rate-limits":e.headers.get("X-Sentry-Rate-Limits"),"retry-after":e.headers.get("Retry-After")}}))}catch(e){return n_.fetch=void 0,r-=a,n--,e$(e)}})}function nE(e){return[tK(),t0(),rQ(),rK(),r9(),na(),t1(),nr(),r2()]}var nS,nw=r(14232),nR=r(65364);class nP{constructor(e={}){this._traceId=e.traceId||em(),this._spanId=e.spanId||eP()}spanContext(){return{spanId:this._spanId,traceId:this._traceId,traceFlags:0}}end(e){}setAttribute(e,t){return this}setAttributes(e){return this}setStatus(e){return this}updateName(e){return this}isRecording(){return!1}addEvent(e,t,r){return this}addLink(e){return this}addLinks(e){return this}recordException(e,t){}}function nT(e){if(!e||0===e.length)return;let t={};return e.forEach(e=>{let r=e.attributes||{},n=r[eY],i=r[eK];"string"==typeof n&&"number"==typeof i&&(t[e.name]={value:i,unit:n})}),t}class nx{constructor(e={}){this._traceId=e.traceId||em(),this._spanId=e.spanId||eP(),this._startTime=e.startTimestamp||eE(),this._links=e.links,this._attributes={},this.setAttributes({[eG]:"manual",[eX]:e.op,...e.attributes}),this._name=e.name,e.parentSpanId&&(this._parentSpanId=e.parentSpanId),"sampled"in e&&(this._sampled=e.sampled),e.endTimestamp&&(this._endTime=e.endTimestamp),this._events=[],this._isStandaloneSpan=e.isStandalone,this._endTime&&this._onSpanEnded()}addLink(e){return this._links?this._links.push(e):this._links=[e],this}addLinks(e){return this._links?this._links.push(...e):this._links=e,this}recordException(e,t){}spanContext(){let{_spanId:e,_traceId:t,_sampled:r}=this;return{spanId:e,traceId:t,traceFlags:+!!r}}setAttribute(e,t){return void 0===t?delete this._attributes[e]:this._attributes[e]=t,this}setAttributes(e){return Object.keys(e).forEach(t=>this.setAttribute(t,e[t])),this}updateStartTime(e){this._startTime=tc(e)}setStatus(e){return this._status=e,this}updateName(e){return this._name=e,this.setAttribute(eW,"custom"),this}end(e){var t;this._endTime||(this._endTime=tc(e),t=0,this._onSpanEnded())}getSpanJSON(){return{data:this._attributes,description:this._name,op:this._attributes[eX],parent_span_id:this._parentSpanId,span_id:this._spanId,start_timestamp:this._startTime,status:tf(this._status),timestamp:this._endTime,trace_id:this._traceId,origin:this._attributes[eG],profile_id:this._attributes[eQ],exclusive_time:this._attributes[eZ],measurements:nT(this._events),is_segment:this._isStandaloneSpan&&tv(this)===this||void 0,segment_id:this._isStandaloneSpan?tv(this).spanContext().spanId:void 0,links:tu(this._links)}}isRecording(){return!this._endTime&&!!this._sampled}addEvent(e,t,r){let n=nO(t)?t:r||eE(),i=nO(t)?{}:t||{},a={name:e,time:tc(n),attributes:i};return this._events.push(a),this}isStandaloneSpan(){return!!this._isStandaloneSpan}_onSpanEnded(){let e=eU();if(e&&e.emit("spanEnd",this),!(this._isStandaloneSpan||this===tv(this)))return;if(this._isStandaloneSpan)return void(this._sampled?function(e){let t=eU();if(!t)return;let r=e[1];if(!r||0===r.length)return t.recordDroppedEvent("before_send","span");t.sendEnvelope(e)}(function(e,t){let r=tP(e[0]),n=t?.getDsn(),i=t?.getOptions().tunnel,a={sent_at:new Date().toISOString(),...!!r.trace_id&&!!r.public_key&&{trace:r},...!!i&&n&&{dsn:e6(n)}},s=t?.getOptions().beforeSendSpan,o=s?e=>{let t=tp(e),r=s(t);return r||(tE(),t)}:tp,l=[];for(let t of e){let e=o(t);e&&l.push([{type:"span"},e])}return t5(a,l)}([this],e)):e&&e.recordDroppedEvent("sample_rate","span"));let t=this._convertSpanToTransaction();t&&(ti(this).scope||ej()).captureEvent(t)}_convertSpanToTransaction(){if(!nI(tp(this)))return;this._name||(this._name="<unlabeled transaction>");let{scope:e,isolationScope:t}=ti(this),r=e?.getScopeData().sdkProcessingMetadata?.normalizedRequest;if(!0!==this._sampled)return;let n=ty(this).filter(e=>{var t;return e!==this&&!((t=e)instanceof nx&&t.isStandaloneSpan())}).map(e=>tp(e)).filter(nI),i=this._attributes[eW];delete this._attributes[eJ],n.forEach(e=>{delete e.data[eJ]});let a={contexts:{trace:function(e){let{spanId:t,traceId:r}=e.spanContext(),{data:n,op:i,parent_span_id:a,status:s,origin:o,links:l}=tp(e);return{parent_span_id:a,span_id:t,trace_id:r,data:n,op:i,status:s,origin:o,links:l}}(this)},spans:n.length>1e3?n.sort((e,t)=>e.start_timestamp-t.start_timestamp).slice(0,1e3):n,start_timestamp:this._startTime,timestamp:this._endTime,transaction:this._name,type:"transaction",sdkProcessingMetadata:{capturedSpanScope:e,capturedSpanIsolationScope:t,dynamicSamplingContext:tP(this)},request:r,...i&&{transaction_info:{source:i}}},s=nT(this._events);return s&&Object.keys(s).length&&(a.measurements=s),a}}function nO(e){return e&&"number"==typeof e||e instanceof Date||Array.isArray(e)}function nI(e){return!!e.start_timestamp&&!!e.timestamp&&!!e.span_id&&!!e.trace_id}let nC="__SENTRY_SUPPRESS_TRACING__";function nM(e){let t=function(){return eN(k())}();if(t.startInactiveSpan)return t.startInactiveSpan(e);let r=nN(e),{forceTransaction:n,parentSpan:i}=e;return(e.scope?t=>eD(e.scope,t):void 0!==i?e=>nk(i,e):e=>e())(()=>{let t=ej(),a=nD(t,i);return e.onlyIfParent&&!a?new nP:nA({parentSpan:a,spanArguments:r,forceTransaction:n,scope:t})})}function nk(e,t){let r=function(){return eN(k())}();return r.withActiveSpan?r.withActiveSpan(e,t):eD(r=>(ex(r,e||void 0),t(r)))}function nA({parentSpan:e,spanArguments:t,forceTransaction:r,scope:n}){var i,a,s,o;let l;if(!te()){let n=new nP;if(r||!e){let e={sampled:"false",sample_rate:"0",transaction:t.name,...tP(n)};eu(n,tS,e)}return n}let u=eL();if(e&&!r)l=function(e,t,r){let{spanId:n,traceId:i}=e.spanContext(),a=!t.getScopeData().sdkProcessingMetadata[nC]&&th(e),s=a?new nx({...r,parentSpanId:n,traceId:i,sampled:a}):new nP({traceId:i});t_(e,s);let o=eU();return o&&(o.emit("spanStart",s),r.endTimestamp&&o.emit("spanEnd",s)),s}(e,n,t),t_(e,l);else if(e){let r=tP(e),{traceId:i,spanId:a}=e.spanContext(),s=th(e);eu(l=nL({traceId:i,parentSpanId:a,...t},n,s),tS,r)}else{let{traceId:e,dsc:r,parentSpanId:i,sampled:a}={...u.getPropagationContext(),...n.getPropagationContext()};l=nL({traceId:e,parentSpanId:i,...t},n,a),r&&eu(l,tS,r)}return o=0,(s=l)&&(eu(s,tn,u),eu(s,tr,n)),l}function nN(e){let t={isStandalone:(e.experimental||{}).standalone,...e};if(e.startTime){let r={...t};return r.startTimestamp=tc(e.startTime),delete r.startTime,r}return t}function nj(){return eN(k())}function nL(e,t,r){let n=eU(),i=n?.getOptions()||{},{name:a=""}=e,s={spanAttributes:{...e.attributes},spanName:a,parentSampled:r};n?.emit("beforeSampling",s,{decision:!1});let o=s.parentSampled??r,l=s.spanAttributes,u=t.getPropagationContext(),[c,d,p]=t.getScopeData().sdkProcessingMetadata[nC]?[!1]:function(e,t,r){let n,i;if(!te(e))return[!1];"function"==typeof e.tracesSampler?(n=e.tracesSampler({...t,inheritOrSampleWith:e=>"number"==typeof t.parentSampleRate?t.parentSampleRate:"boolean"==typeof t.parentSampled?Number(t.parentSampled):e}),i=!0):void 0!==t.parentSampled?n=t.parentSampled:void 0!==e.tracesSampleRate&&(n=e.tracesSampleRate,i=!0);let a=ta(n);if(void 0===a)return[!1];if(!a)return[!1,a,i];let s=r<a;return[s,a,i]}(i,{name:a,parentSampled:o,attributes:l,parentSampleRate:ta(u.dsc?.sample_rate)},u.sampleRand),h=new nx({...e,attributes:{[eW]:"custom",[eq]:void 0!==d&&p?d:void 0,...l},sampled:c});return!c&&n&&n.recordDroppedEvent("sample_rate","transaction"),n&&n.emit("spanStart",h),h}function nD(e,t){if(t)return t;if(null===t)return;let r=e[eT];if(!r)return;let n=eU();return(n?n.getOptions():{}).parentSpanIsAlwaysRootSpan?tv(r):r}let nU={idleTimeout:1e3,finalTimeout:3e4,childSpanTimeout:15e3};function nF(e,t={}){let r,n=new Map,i=!1,a="externalFinish",s=!t.disableAutoFinish,o=[],{idleTimeout:l=nU.idleTimeout,finalTimeout:u=nU.finalTimeout,childSpanTimeout:c=nU.childSpanTimeout,beforeSpanEnd:d}=t,p=eU();if(!p||!te()){let e=new nP,t={sample_rate:"0",sampled:"false",...tP(e)};return eu(e,tS,t),e}let h=ej(),f=tb(),m=function(e){let t=nM(e);return ex(ej(),t),t}(e);function g(){r&&(clearTimeout(r),r=void 0)}function _(e){g(),r=setTimeout(()=>{!i&&0===n.size&&s&&(a="idleTimeout",m.end(e))},l)}function y(e){r=setTimeout(()=>{!i&&s&&(a="heartbeatFailed",m.end(e))},c)}function v(e){i=!0,n.clear(),o.forEach(e=>e()),ex(h,f);let t=tp(m),{start_timestamp:r}=t;if(!r)return;t.data[eV]||m.setAttribute(eV,a),B.log(`[Tracing] Idle span "${t.op}" finished`);let s=ty(m).filter(e=>e!==m),c=0;s.forEach(t=>{t.isRecording()&&(t.setStatus({code:2,message:"cancelled"}),t.end(e));let{timestamp:r=0,start_timestamp:n=0}=tp(t),i=n<=e;(!(r-n<=(u+l)/1e3)||!i)&&(m[tm]&&m[tm].delete(t),c++)}),c>0&&m.setAttribute("sentry.idle_span_discarded_spans",c)}return m.end=new Proxy(m.end,{apply(e,t,r){if(d&&d(m),t instanceof nP)return;let[n,...i]=r,a=tc(n||eE()),s=ty(m).filter(e=>e!==m);if(!s.length)return v(a),Reflect.apply(e,t,[a,...i]);let o=s.map(e=>tp(e).timestamp).filter(e=>!!e),l=o.length?Math.max(...o):void 0,c=tp(m).start_timestamp,p=Math.min(c?c+u/1e3:1/0,Math.max(c||-1/0,Math.min(a,l||1/0)));return v(p),Reflect.apply(e,t,[p,...i])}}),o.push(p.on("spanStart",e=>{var t;!(i||e===m||tp(e).timestamp||e instanceof nx&&e.isStandaloneSpan())&&ty(m).includes(e)&&(t=e.spanContext().spanId,g(),n.set(t,!0),y(eE()+c/1e3))})),o.push(p.on("spanEnd",e=>{if(!i){var t;t=e.spanContext().spanId,n.has(t)&&n.delete(t),0===n.size&&_(eE()+l/1e3)}})),o.push(p.on("idleSpanEnableAutoFinish",e=>{e===m&&(s=!0,_(),n.size&&y())})),t.disableAutoFinish||_(),setTimeout(()=>{i||(m.setStatus({code:2,message:"deadline_exceeded"}),a="finalTimeout",m.end())},u),m}let nB=!1,n$=(e,t)=>e>t[1]?"poor":e>t[0]?"needs-improvement":"good",nH=(e,t,r,n)=>{let i,a;return s=>{t.value>=0&&(s||n)&&((a=t.value-(i??0))||void 0===i)&&(i=t.value,t.delta=a,t.rating=n$(t.value,r),e(t))}},nW=()=>`v5-${Date.now()}-${Math.floor(Math.random()*(9e12-1))+1e12}`,nq=(e=!0)=>{let t=C.performance?.getEntriesByType?.("navigation")[0];if(!e||t&&t.responseStart>0&&t.responseStart<performance.now())return t},nz=()=>{let e=nq();return e?.activationStart??0},nX=(e,t=-1)=>{let r=nq(),n="navigate";return r&&(C.document?.prerendering||nz()>0?n="prerender":C.document?.wasDiscarded?n="restore":r.type&&(n=r.type.replace(/_/g,"-"))),{name:e,value:t,rating:"good",delta:0,entries:[],id:nW(),navigationType:n}},nG=new WeakMap;function nV(e,t){return nG.get(e)||nG.set(e,new t),nG.get(e)}class nY{constructor(){nY.prototype.__init.call(this),nY.prototype.__init2.call(this)}__init(){this._sessionValue=0}__init2(){this._sessionEntries=[]}_processEntry(e){if(e.hadRecentInput)return;let t=this._sessionEntries[0],r=this._sessionEntries[this._sessionEntries.length-1];this._sessionValue&&t&&r&&e.startTime-r.startTime<1e3&&e.startTime-t.startTime<5e3?(this._sessionValue+=e.value,this._sessionEntries.push(e)):(this._sessionValue=e.value,this._sessionEntries=[e]),this._onAfterProcessingUnexpectedShift?.(e)}}let nK=(e,t,r={})=>{try{if(PerformanceObserver.supportedEntryTypes.includes(e)){let n=new PerformanceObserver(e=>{Promise.resolve().then(()=>{t(e.getEntries())})});return n.observe({type:e,buffered:!0,...r}),n}}catch{}},nJ=e=>{let t=!1;return()=>{t||(e(),t=!0)}},nQ=-1,nZ=()=>C.document?.visibilityState!=="hidden"||C.document?.prerendering?1/0:0,n0=e=>{"hidden"===C.document.visibilityState&&nQ>-1&&(nQ="visibilitychange"===e.type?e.timeStamp:0,n2())},n1=()=>{addEventListener("visibilitychange",n0,!0),addEventListener("prerenderingchange",n0,!0)},n2=()=>{removeEventListener("visibilitychange",n0,!0),removeEventListener("prerenderingchange",n0,!0)},n3=()=>{if(C.document&&nQ<0){let e=nz();nQ=(C.document.prerendering?void 0:globalThis.performance.getEntriesByType("visibility-state").filter(t=>"hidden"===t.name&&t.startTime>e)[0]?.startTime)??nZ(),n1()}return{get firstHiddenTime(){return nQ}}},n8=e=>{C.document?.prerendering?addEventListener("prerenderingchange",()=>e(),!0):e()},n5=[1800,3e3],n4=(e,t={})=>{n8(()=>{let r,n=n3(),i=nX("FCP"),a=nK("paint",e=>{for(let t of e)"first-contentful-paint"===t.name&&(a.disconnect(),t.startTime<n.firstHiddenTime&&(i.value=Math.max(t.startTime-nz(),0),i.entries.push(t),r(!0)))});a&&(r=nH(e,i,n5,t.reportAllChanges))})},n6=[.1,.25],n7=(e,t={})=>{n4(nJ(()=>{let r,n=nX("CLS",0),i=nV(t,nY),a=e=>{for(let t of e)i._processEntry(t);i._sessionValue>n.value&&(n.value=i._sessionValue,n.entries=i._sessionEntries,r())},s=nK("layout-shift",a);s&&(r=nH(e,n,n6,t.reportAllChanges),C.document?.addEventListener("visibilitychange",()=>{C.document?.visibilityState==="hidden"&&(a(s.takeRecords()),r(!0))}),C?.setTimeout?.(r))}))},n9=e=>{let t=t=>{("pagehide"===t.type||C.document?.visibilityState==="hidden")&&e(t)};C.document&&(addEventListener("visibilitychange",t,!0),addEventListener("pagehide",t,!0))},ie=[100,300],it=(e,t={})=>{n8(()=>{let r,n=n3(),i=nX("FID"),a=e=>{e.startTime<n.firstHiddenTime&&(i.value=e.processingStart-e.startTime,i.entries.push(e),r(!0))},s=e=>{e.forEach(a)},o=nK("first-input",s);r=nH(e,i,ie,t.reportAllChanges),o&&n9(nJ(()=>{s(o.takeRecords()),o.disconnect()}))})},ir=0,ii=1/0,ia=0,is=e=>{e.forEach(e=>{e.interactionId&&(ii=Math.min(ii,e.interactionId),ir=(ia=Math.max(ia,e.interactionId))?(ia-ii)/7+1:0)})},io=()=>h?ir:performance.interactionCount||0,il=()=>{"interactionCount"in performance||h||(h=nK("event",is,{type:"event",buffered:!0,durationThreshold:0}))},iu=0,ic=()=>io()-iu;class id{constructor(){id.prototype.__init.call(this),id.prototype.__init2.call(this)}__init(){this._longestInteractionList=[]}__init2(){this._longestInteractionMap=new Map}_resetInteractions(){iu=io(),this._longestInteractionList.length=0,this._longestInteractionMap.clear()}_estimateP98LongestInteraction(){let e=Math.min(this._longestInteractionList.length-1,Math.floor(ic()/50));return this._longestInteractionList[e]}_processEntry(e){if(this._onBeforeProcessingEntry?.(e),!(e.interactionId||"first-input"===e.entryType))return;let t=this._longestInteractionList.at(-1),r=this._longestInteractionMap.get(e.interactionId);if(r||this._longestInteractionList.length<10||e.duration>t._latency){if(r?e.duration>r._latency?(r.entries=[e],r._latency=e.duration):e.duration===r._latency&&e.startTime===r.entries[0].startTime&&r.entries.push(e):(r={id:e.interactionId,entries:[e],_latency:e.duration},this._longestInteractionMap.set(r.id,r),this._longestInteractionList.push(r)),this._longestInteractionList.sort((e,t)=>t._latency-e._latency),this._longestInteractionList.length>10)for(let e of this._longestInteractionList.splice(10))this._longestInteractionMap.delete(e.id);this._onAfterProcessingINPCandidate?.(r)}}}let ip=e=>{let t=C.requestIdleCallback||C.setTimeout;C.document?.visibilityState==="hidden"?e():(t(e=nJ(e)),n9(e))},ih=[200,500],im=(e,t={})=>{globalThis.PerformanceEventTiming&&"interactionId"in PerformanceEventTiming.prototype&&n8(()=>{let r;il();let n=nX("INP"),i=nV(t,id),a=e=>{ip(()=>{for(let t of e)i._processEntry(t);let t=i._estimateP98LongestInteraction();t&&t._latency!==n.value&&(n.value=t._latency,n.entries=t.entries,r())})},s=nK("event",a,{durationThreshold:t.durationThreshold??40});r=nH(e,n,ih,t.reportAllChanges),s&&(s.observe({type:"first-input",buffered:!0}),n9(()=>{a(s.takeRecords()),r(!0)}))})};class ig{_processEntry(e){this._onBeforeProcessingEntry?.(e)}}let i_=[2500,4e3],iy=(e,t={})=>{n8(()=>{let r,n=n3(),i=nX("LCP"),a=nV(t,ig),s=e=>{for(let s of(t.reportAllChanges||(e=e.slice(-1)),e))a._processEntry(s),s.startTime<n.firstHiddenTime&&(i.value=Math.max(s.startTime-nz(),0),i.entries=[s],r())},o=nK("largest-contentful-paint",s);if(o){r=nH(e,i,i_,t.reportAllChanges);let n=nJ(()=>{s(o.takeRecords()),o.disconnect(),r(!0)});for(let e of["keydown","click","visibilitychange"])C.document&&addEventListener(e,()=>ip(n),{capture:!0,once:!0})}})},iv=[800,1800],ib=e=>{C.document?.prerendering?n8(()=>ib(e)):C.document?.readyState!=="complete"?addEventListener("load",()=>ib(e),!0):setTimeout(e)},iE=(e,t={})=>{let r=nX("TTFB"),n=nH(e,r,iv,t.reportAllChanges);ib(()=>{let e=nq();e&&(r.value=Math.max(e.responseStart-nz(),0),r.entries=[e],n(!0))})},iS={},iw={};function iR(e,t=!1){return ij("cls",e,iC,f,t)}function iP(e,t=!1){return ij("lcp",e,ik,g,t)}function iT(e){return ij("fid",e,iM,m)}function ix(e){return ij("inp",e,iN,y)}function iO(e,t){return iL(e,t),iw[e]||(function(e){let t={};"event"===e&&(t.durationThreshold=0),nK(e,t=>{iI(e,{entries:t})},t)}(e),iw[e]=!0),iD(e,t)}function iI(e,t){let r=iS[e];if(r?.length)for(let e of r)try{e(t)}catch(e){}}function iC(){return n7(e=>{iI("cls",{metric:e}),f=e},{reportAllChanges:!0})}function iM(){return it(e=>{iI("fid",{metric:e}),m=e})}function ik(){return iy(e=>{iI("lcp",{metric:e}),g=e},{reportAllChanges:!0})}function iA(){return iE(e=>{iI("ttfb",{metric:e}),_=e})}function iN(){return im(e=>{iI("inp",{metric:e}),y=e})}function ij(e,t,r,n,i=!1){let a;return iL(e,t),iw[e]||(a=r(),iw[e]=!0),n&&t({metric:n}),iD(e,t,i?a:void 0)}function iL(e,t){iS[e]=iS[e]||[],iS[e].push(t)}function iD(e,t,r){return()=>{r&&r();let n=iS[e];if(!n)return;let i=n.indexOf(t);-1!==i&&n.splice(i,1)}}function iU(e){return"number"==typeof e&&isFinite(e)}function iF(e,t,r,{...n}){let i=tp(e).start_timestamp;return i&&i>t&&"function"==typeof e.updateStartTime&&e.updateStartTime(t),nk(e,()=>{let e=nM({startTime:t,...n});return e&&e.end(r),e})}function iB(e){let t,r=eU();if(!r)return;let{name:n,transaction:i,attributes:a,startTime:s}=e,{release:o,environment:l,sendDefaultPii:u}=r.getOptions(),c=r.getIntegrationByName("Replay"),d=c?.getReplayId(),p=ej(),h=p.getUser(),f=void 0!==h?h.email||h.id||h.ip_address:void 0;try{t=p.getScopeData().contexts.profile.profile_id}catch{}return nM({name:n,attributes:{release:o,environment:l,user:f||void 0,profile_id:t||void 0,replay_id:d||void 0,transaction:i,"user_agent.original":C.navigator?.userAgent,"client.address":u?"{{auto}}":void 0,...a},startTime:s,experimental:{standalone:!0}})}function i$(){return C.addEventListener&&C.performance}function iH(e){return e/1e3}function iW(e){let t="unknown",r="unknown",n="";for(let i of e){if("/"===i){[t,r]=e.split("/");break}if(!isNaN(Number(i))){t="h"===n?"http":n,r=e.split(n)[1];break}n+=i}return n===e&&(t=n),{name:t,version:r}}function iq(e){try{return PerformanceObserver.supportedEntryTypes.includes(e)}catch{return!1}}function iz(e,t){let r,n=!1;function i(e){!n&&r&&t(e,r),n=!0}n9(()=>{i("pagehide")});let a=e.on("beforeStartNavigationSpan",(e,t)=>{t?.isRedirect||(i("navigation"),a?.(),s?.())}),s=e.on("afterStartPageLoadSpan",e=>{r=e.spanContext().spanId,s?.()})}let iX=0,iG={};function iV(e,t,r,n,i=r){var a;let s=t["secureConnection"===(a=r)?"connectEnd":"fetch"===a?"domainLookupStart":`${a}End`],o=t[`${r}Start`];o&&s&&iF(e,n+iH(o),n+iH(s),{op:`browser.${i}`,name:t.name,attributes:{[eG]:"auto.ui.browser.metrics",..."redirect"===r&&null!=t.redirectCount?{"http.redirect_count":t.redirectCount}:{}}})}function iY(e,t,r,n){let i=t[r];null!=i&&i<0x7fffffff&&(e[n]=i)}let iK=[],iJ=new Map,iQ={click:"click",pointerdown:"click",pointerup:"click",mousedown:"click",mouseup:"click",touchstart:"click",touchend:"click",mouseover:"hover",mouseout:"hover",mouseenter:"hover",mouseleave:"hover",pointerover:"hover",pointerout:"hover",pointerenter:"hover",pointerleave:"hover",dragstart:"drag",dragend:"drag",drag:"drag",dragenter:"drag",dragleave:"drag",dragover:"drag",drop:"drag",keydown:"press",keyup:"press",keypress:"press",input:"press"},iZ=({metric:e})=>{if(void 0==e.value)return;let t=iH(e.value);if(t>60)return;let r=e.entries.find(t=>t.duration===e.value&&iQ[t.name]);if(!r)return;let{interactionId:n}=r,i=iQ[r.name],a=iH(eS()+r.startTime),s=tb(),o=s?tv(s):void 0,l=(null!=n?iJ.get(n):void 0)||o,u=l?tp(l).description:ej().getScopeData().transactionName,c=iB({name:er(r.target),transaction:u,attributes:{[eG]:"auto.http.browser.inp",[eX]:`ui.interaction.${i}`,[eZ]:r.duration},startTime:a});c&&(c.addEvent("inp",{[eY]:"millisecond",[eK]:e.value}),c.end(a+t))},i0=({entries:e})=>{let t=tb(),r=t?tv(t):void 0,n=r?tp(r).description:ej().getScopeData().transactionName;e.forEach(e=>{if(!e.identifier)return;let t=e.name,r=e.renderTime,i=e.loadTime,[a,s]=i?[iH(i),"load-time"]:r?[iH(r),"render-time"]:[eE(),"entry-emission"],o="image-paint"===t?iH(Math.max(0,(r??0)-(i??0))):0,l={[eG]:"auto.ui.browser.elementtiming",[eX]:"ui.elementtiming",[eW]:"component","sentry.span_start_time_source":s,"sentry.transaction_name":n,"element.id":e.id,"element.type":e.element?.tagName?.toLowerCase()||"unknown","element.size":e.naturalWidth&&e.naturalHeight?`${e.naturalWidth}x${e.naturalHeight}`:void 0,"element.render_time":r,"element.load_time":i,"element.url":e.url||void 0,"element.identifier":e.identifier,"element.paint_type":t};!function(e,t){let r=eN(k());if(r.startSpan)return r.startSpan(e,t);let n=nN(e),{forceTransaction:i,parentSpan:a,scope:s}=e;eD(s?.clone(),()=>{var r;return(void 0!==(r=a)?e=>nk(r,e):e=>e())(()=>{let r=ej(),s=nD(r,a),o=e.onlyIfParent&&!s?new nP:nA({parentSpan:s,spanArguments:n,forceTransaction:i,scope:r});return ex(r,o),function(e,t,r=()=>{}){var n,i,a;let s;try{s=e()}catch(e){throw t(e),r(),e}return n=s,i=t,a=r,Q(n)?n.then(e=>(a(),e),e=>{throw i(e),a(),e}):(a(),n)}(()=>t(o),()=>{let{status:e}=tp(o);o.isRecording()&&(!e||"ok"===e)&&o.setStatus({code:2,message:"internal_error"})},()=>{o.end()})})})}({name:`element[${e.identifier}]`,attributes:l,startTime:a,onlyIfParent:!0},e=>{e.end(a+o)})})},i1="sentry_previous_trace";function i2(e){return 1===e.traceFlags}function i3(e={}){let t=e.client||eU();if(!function(){let e=eU();return e?.getOptions().enabled!==!1&&!!e?.getTransport()}()||!t)return{};let r=eN(k());if(r.getTraceData)return r.getTraceData(e);let n=e.scope||ej(),i=e.span||tb(),a=i?function(e){let{traceId:t,spanId:r}=e.spanContext();return to(t,r,th(e))}(i):function(e){let{traceId:t,sampled:r,propagationSpanId:n}=e.getPropagationContext();return to(t,n,r)}(n),s=function(e){if(e){var t=Object.entries(e).reduce((e,[t,r])=>(r&&(e[`${e0}${t}`]=r),e),{});return 0!==Object.keys(t).length?Object.entries(t).reduce((e,[t,r],n)=>{let i=`${encodeURIComponent(t)}=${encodeURIComponent(r)}`,a=0===n?i:`${e},${i}`;return a.length>8192?e:a},""):void 0}}(i?tP(i):tR(t,n));return ts.test(a)?{"sentry-trace":a,baggage:s}:(B.warn("Invalid sentry-trace data. Cannot generate trace data"),{})}function i8(e){return e.split(",").some(e=>e.trim().startsWith(e0))}function i5(e=0){return((eS()||performance.timeOrigin)+e)/1e3}let i4=new WeakMap,i6=new Map,i7={traceFetch:!0,traceXHR:!0,enableHTTPTimings:!0,trackFetchStreamPerformance:!1};function i9(e){let{url:t}=tp(e).data;if(!t||"string"!=typeof t)return;let r=iO("resource",({entries:n})=>{n.forEach(n=>{"resource"===n.entryType&&"initiatorType"in n&&"string"==typeof n.nextHopProtocol&&("fetch"===n.initiatorType||"xmlhttprequest"===n.initiatorType)&&n.name.endsWith(t)&&((function(e){let t=[];if(void 0!=e.nextHopProtocol){let{name:r,version:n}=iW(e.nextHopProtocol);t.push(["network.protocol.version",n],["network.protocol.name",r])}return eS()?[...t,["http.request.redirect_start",i5(e.redirectStart)],["http.request.fetch_start",i5(e.fetchStart)],["http.request.domain_lookup_start",i5(e.domainLookupStart)],["http.request.domain_lookup_end",i5(e.domainLookupEnd)],["http.request.connect_start",i5(e.connectStart)],["http.request.secure_connection_start",i5(e.secureConnectionStart)],["http.request.connection_end",i5(e.connectEnd)],["http.request.request_start",i5(e.requestStart)],["http.request.response_start",i5(e.responseStart)],["http.request.response_end",i5(e.responseEnd)]]:t})(n).forEach(t=>e.setAttribute(...t)),setTimeout(r))})})}function ae(e){try{return new URL(e,C.location.origin).href}catch{return}}let at={...nU,instrumentNavigation:!0,instrumentPageLoad:!0,markBackgroundSpan:!0,enableLongTask:!0,enableLongAnimationFrame:!0,enableInp:!0,enableElementTiming:!0,ignoreResourceSpans:[],ignorePerformanceApiSpans:[],detectRedirects:!0,linkPreviousTrace:"in-memory",consistentTraceSampling:!1,_experiments:{},...i7},ar=(e={})=>{let t,r,n={name:void 0,source:void 0},i=C.document,{enableInp:a,enableElementTiming:s,enableLongTask:o,enableLongAnimationFrame:l,_experiments:{enableInteractions:u,enableStandaloneClsSpans:c,enableStandaloneLcpSpans:d},beforeStartSpan:p,idleTimeout:h,finalTimeout:f,childSpanTimeout:m,markBackgroundSpan:g,traceFetch:y,traceXHR:E,trackFetchStreamPerformance:S,shouldCreateSpanForRequest:w,enableHTTPTimings:R,ignoreResourceSpans:P,ignorePerformanceApiSpans:T,instrumentPageLoad:x,instrumentNavigation:O,detectRedirects:I,linkPreviousTrace:M,consistentTraceSampling:k,onRequestSpanStart:A}={...at,...e};function N(e,r,a=!0){let s="pageload"===r.op,o=p?p(r):r,l=o.attributes||{};if(r.name!==o.name&&(l[eW]="custom",o.attributes=l),!a){let e=eb();nM({...o,startTime:e}).end(e);return}n.name=o.name,n.source=l[eW];let u=nF(o,{idleTimeout:h,finalTimeout:f,childSpanTimeout:m,disableAutoFinish:s,beforeSpanEnd:r=>{t?.(),function(e,t){let r=i$(),n=eS();if(!r?.getEntries||!n)return;let i=iH(n),a=r.getEntries(),{op:s,start_timestamp:o}=tp(e);if(a.slice(iX).forEach(r=>{let n=iH(r.startTime),a=iH(Math.max(0,r.duration));if("navigation"!==s||!o||!(i+n<o))switch(r.entryType){case"navigation":var l,u,c;l=e,u=r,c=i,["unloadEvent","redirect","domContentLoadedEvent","loadEvent","connect"].forEach(e=>{iV(l,u,e,c)}),iV(l,u,"secureConnection",c,"TLS/SSL"),iV(l,u,"fetch",c,"cache"),iV(l,u,"domainLookup",c,"DNS"),function(e,t,r){let n=r+iH(t.requestStart),i=r+iH(t.responseEnd),a=r+iH(t.responseStart);t.responseEnd&&(iF(e,n,i,{op:"browser.request",name:t.name,attributes:{[eG]:"auto.ui.browser.metrics"}}),iF(e,a,i,{op:"browser.response",name:t.name,attributes:{[eG]:"auto.ui.browser.metrics"}}))}(l,u,c);break;case"mark":case"paint":case"measure":{!function(e,t,r,n,i,a){if(["mark","measure"].includes(t.entryType)&&eo(t.name,a))return;let s=nq(!1),o=i+Math.max(r,iH(s?s.requestStart:0)),l=i+r,u=l+n,c={[eG]:"auto.resource.browser.metrics"};o!==l&&(c["sentry.browser.measure_happened_before_request"]=!0,c["sentry.browser.measure_start_time"]=o),function(e,t){try{let r=t.detail;if(!r)return;if("object"==typeof r){for(let[t,n]of Object.entries(r))if(n&&Y(n))e[`sentry.browser.measure.detail.${t}`]=n;else if(void 0!==n)try{e[`sentry.browser.measure.detail.${t}`]=JSON.stringify(n)}catch{}return}if(Y(r)){e["sentry.browser.measure.detail"]=r;return}try{e["sentry.browser.measure.detail"]=JSON.stringify(r)}catch{}}catch{}}(c,t),o<=u&&iF(e,o,u,{name:t.name,op:t.entryType,attributes:c})}(e,r,n,a,i,t.ignorePerformanceApiSpans);let s=n3(),o=r.startTime<s.firstHiddenTime;"first-paint"===r.name&&o&&(iG.fp={value:r.startTime,unit:"millisecond"}),"first-contentful-paint"===r.name&&o&&(iG.fcp={value:r.startTime,unit:"millisecond"});break}case"resource":!function(e,t,r,n,i,a,s){if("xmlhttprequest"===t.initiatorType||"fetch"===t.initiatorType)return;let o=t.initiatorType?`resource.${t.initiatorType}`:"resource.other";if(s?.includes(o))return;let l=rB(r),u={[eG]:"auto.resource.browser.metrics"};iY(u,t,"transferSize","http.response_transfer_size"),iY(u,t,"encodedBodySize","http.response_content_length"),iY(u,t,"decodedBodySize","http.decoded_response_content_length");let c=t.deliveryType;null!=c&&(u["http.response_delivery_type"]=c);let d=t.renderBlockingStatus;if(d&&(u["resource.render_blocking_status"]=d),l.protocol&&(u["url.scheme"]=l.protocol.split(":").pop()),l.host&&(u["server.address"]=l.host),u["url.same_origin"]=r.includes(C.location.origin),null!=t.nextHopProtocol){let{name:e,version:r}=iW(t.nextHopProtocol);u["network.protocol.name"]=e,u["network.protocol.version"]=r}let p=a+n;iF(e,p,p+i,{name:r.replace(C.location.origin,""),op:o,attributes:u})}(e,r,r.name,n,a,i,t.ignoreResourceSpans)}}),iX=Math.max(a.length-1,0),function(e){let t=C.navigator;if(!t)return;let r=t.connection;r&&(r.effectiveType&&e.setAttribute("effectiveConnectionType",r.effectiveType),r.type&&e.setAttribute("connectionType",r.type),iU(r.rtt)&&(iG["connection.rtt"]={value:r.rtt,unit:"millisecond"})),iU(t.deviceMemory)&&e.setAttribute("deviceMemory",`${t.deviceMemory} GB`),iU(t.hardwareConcurrency)&&e.setAttribute("hardwareConcurrency",String(t.hardwareConcurrency))}(e),"pageload"===s){var l,u;!function(e){let t=nq(!1);if(!t)return;let{responseStart:r,requestStart:n}=t;n<=r&&(e["ttfb.requestTime"]={value:r-n,unit:"millisecond"})}(iG);let r=iG["mark.fid"];r&&iG.fid&&(iF(e,r.value,r.value+iH(iG.fid.value),{name:"first input delay",op:"ui.action",attributes:{[eG]:"auto.ui.browser.metrics"}}),delete iG["mark.fid"]),t.recordClsOnPageloadSpan||delete iG.cls,t.recordLcpOnPageloadSpan||delete iG.lcp,Object.entries(iG).forEach(([e,t])=>{!function(e,t,r,n=tb()){let i=n&&tv(n);i&&i.addEvent(e,{[eK]:t,[eY]:r})}(e,t.value,t.unit)}),e.setAttribute("performance.timeOrigin",i),e.setAttribute("performance.activationStart",nz()),l=e,u=t,v&&u.recordLcpOnPageloadSpan&&(v.element&&l.setAttribute("lcp.element",er(v.element)),v.id&&l.setAttribute("lcp.id",v.id),v.url&&l.setAttribute("lcp.url",v.url.trim().slice(0,200)),null!=v.loadTime&&l.setAttribute("lcp.loadTime",v.loadTime),null!=v.renderTime&&l.setAttribute("lcp.renderTime",v.renderTime),l.setAttribute("lcp.size",v.size)),b?.sources&&u.recordClsOnPageloadSpan&&b.sources.forEach((e,t)=>l.setAttribute(`cls.source.${t+1}`,er(e.node)))}v=void 0,b=void 0,iG={}}(r,{recordClsOnPageloadSpan:!c,recordLcpOnPageloadSpan:!d,ignoreResourceSpans:P,ignorePerformanceApiSpans:T}),eu(e,as,void 0);let n=ej(),i=n.getPropagationContext();n.setPropagationContext({...i,traceId:u.spanContext().traceId,sampled:th(u),dsc:tP(r)})}});function g(){i&&["interactive","complete"].includes(i.readyState)&&e.emit("idleSpanEnableAutoFinish",u)}eu(e,as,u),s&&i&&(i.addEventListener("readystatechange",()=>{g()}),g())}return{name:"BrowserTracing",setup(e){function n(){let e=tb(),t=e&&tv(e);t&&t.setStatus({code:2,message:"internal_error"})}if(nB||(n.tag="sentry_tracingErrorCallback",nB=!0,r8(n),r6(n)),t=function({recordClsStandaloneSpans:e,recordLcpStandaloneSpans:t,client:r}){let n=i$();if(n&&eS()){n.mark&&C.performance.mark("sentry-tracing-init");let i=iT(({metric:e})=>{let t=e.entries[e.entries.length-1];if(!t)return;let r=iH(eS()),n=iH(t.startTime);iG.fid={value:e.value,unit:"millisecond"},iG["mark.fid"]={value:r+n,unit:"second"}}),a=t?function(e){let t,r=0;if(!iq("largest-contentful-paint"))return;let n=iP(({metric:e})=>{let n=e.entries[e.entries.length-1];n&&(r=e.value,t=n)},!0);iz(e,(e,i)=>{(function(e,t,r,n){let i=iH((eS()||0)+(t?.startTime||0)),a=ej().getScopeData().transactionName,s=t?er(t.element):"Largest contentful paint",o={[eG]:"auto.http.browser.lcp",[eX]:"ui.webvital.lcp",[eZ]:0,"sentry.pageload.span_id":r,"sentry.report_event":n};t&&(t.element&&(o["lcp.element"]=er(t.element)),t.id&&(o["lcp.id"]=t.id),t.url&&(o["lcp.url"]=t.url.trim().slice(0,200)),null!=t.loadTime&&(o["lcp.loadTime"]=t.loadTime),null!=t.renderTime&&(o["lcp.renderTime"]=t.renderTime),null!=t.size&&(o["lcp.size"]=t.size));let l=iB({name:s,transaction:a,attributes:o,startTime:i});l&&(l.addEvent("lcp",{[eY]:"millisecond",[eK]:e}),l.end(i))})(r,t,i,e),n()})}(r):iP(({metric:e})=>{let t=e.entries[e.entries.length-1];t&&(iG.lcp={value:e.value,unit:"millisecond"},v=t)},!0),s=ij("ttfb",({metric:e})=>{e.entries[e.entries.length-1]&&(iG.ttfb={value:e.value,unit:"millisecond"})},iA,_),o=e?function(e){let t,r=0;if(!iq("layout-shift"))return;let n=iR(({metric:e})=>{let n=e.entries[e.entries.length-1];n&&(r=e.value,t=n)},!0);iz(e,(e,i)=>{(function(e,t,r,n){let i=iH((eS()||0)+(t?.startTime||0)),a=ej().getScopeData().transactionName,s=t?er(t.sources[0]?.node):"Layout shift",o={[eG]:"auto.http.browser.cls",[eX]:"ui.webvital.cls",[eZ]:t?.duration||0,"sentry.pageload.span_id":r,"sentry.report_event":n};t?.sources&&t.sources.forEach((e,t)=>{o[`cls.source.${t+1}`]=er(e.node)});let l=iB({name:s,transaction:a,attributes:o,startTime:i});l&&(l.addEvent("cls",{[eY]:"",[eK]:e}),l.end(i))})(r,t,i,e),n()})}(r):iR(({metric:e})=>{let t=e.entries[e.entries.length-1];t&&(iG.cls={value:e.value,unit:""},b=t)},!0);return()=>{i(),a?.(),s(),o?.()}}return()=>void 0}({recordClsStandaloneSpans:c||!1,recordLcpStandaloneSpans:d||!1,client:e}),a&&function(){if(i$()&&eS()){let e=ix(iZ);()=>{e()}}}(),s&&i$()&&eS()&&iO("element",i0),l&&C.PerformanceObserver&&PerformanceObserver.supportedEntryTypes&&PerformanceObserver.supportedEntryTypes.includes("long-animation-frame")?new PerformanceObserver(e=>{let t=tb();if(t)for(let r of e.getEntries()){if(!r.scripts[0])continue;let e=iH(eS()+r.startTime),{start_timestamp:n,op:i}=tp(t);if("navigation"===i&&n&&e<n)continue;let a=iH(r.duration),s={[eG]:"auto.ui.browser.metrics"},{invoker:o,invokerType:l,sourceURL:u,sourceFunctionName:c,sourceCharPosition:d}=r.scripts[0];s["browser.script.invoker"]=o,s["browser.script.invoker_type"]=l,u&&(s["code.filepath"]=u),c&&(s["code.function"]=c),-1!==d&&(s["browser.script.source_char_position"]=d),iF(t,e,e+a,{name:"Main UI thread blocked",op:"ui.long-animation-frame",attributes:s})}}).observe({type:"long-animation-frame",buffered:!0}):o&&iO("longtask",({entries:e})=>{let t=tb();if(!t)return;let{op:r,start_timestamp:n}=tp(t);for(let i of e){let e=iH(eS()+i.startTime),a=iH(i.duration);"navigation"===r&&n&&e<n||iF(t,e,e+a,{name:"Main UI thread blocked",op:"ui.long-task",attributes:{[eG]:"auto.ui.browser.metrics"}})}}),u&&iO("event",({entries:e})=>{let t=tb();if(t){for(let r of e)if("click"===r.name){let e=iH(eS()+r.startTime),n=iH(r.duration),i={name:er(r.target),op:`ui.interaction.${r.name}`,startTime:e,attributes:{[eG]:"auto.ui.browser.metrics"}},a=ei(r.target);a&&(i.attributes["ui.component_name"]=a),iF(t,e,e+n,i)}}}),I&&i){let e=()=>{r=eE()};addEventListener("click",e,{capture:!0}),addEventListener("keydown",e,{capture:!0,passive:!0})}function p(){let t=e[as];t&&!tp(t).timestamp&&(t.setAttribute(eV,"cancelled"),t.end())}e.on("startNavigationSpan",(t,r)=>{if(eU()!==e)return;if(r?.isRedirect)return void N(e,{op:"navigation.redirect",...t},!1);p(),eL().setPropagationContext({traceId:em(),sampleRand:Math.random()});let n=ej();n.setPropagationContext({traceId:em(),sampleRand:Math.random()}),n.setSDKProcessingMetadata({normalizedRequest:void 0}),N(e,{op:"navigation",...t})}),e.on("startPageLoadSpan",(t,r={})=>{if(eU()!==e)return;p();let n=function(e,t){let r=function(e){let t;if(!e)return;let r=e.match(ts);if(r)return"1"===r[3]?t=!0:"0"===r[3]&&(t=!1),{traceId:r[1],parentSampled:t,parentSpanId:r[2]}}(e),n=e2(t);if(!r?.traceId)return{traceId:em(),sampleRand:Math.random()};let i=function(e,t){let r=ta(t?.sample_rand);if(void 0!==r)return r;let n=ta(t?.sample_rate);return n&&e?.parentSampled!==void 0?e.parentSampled?Math.random()*n:n+Math.random()*(1-n):Math.random()}(r,n);n&&(n.sample_rand=i.toString());let{traceId:a,parentSpanId:s,parentSampled:o}=r;return{traceId:a,parentSpanId:s,sampled:o,dsc:n||{},sampleRand:i}}(r.sentryTrace||aa("sentry-trace"),r.baggage||aa("baggage")),i=ej();i.setPropagationContext(n),i.setSDKProcessingMetadata({normalizedRequest:rE()}),N(e,{op:"pageload",...t})})},afterAllSetup(e){var t,i,s,o,l;let c,d=en();if("off"!==M&&function(e,{linkPreviousTrace:t,consistentTraceSampling:r}){let n="session-storage"===t,i=n?function(){try{let e=C.sessionStorage?.getItem(i1);return JSON.parse(e)}catch{return}}():void 0;e.on("spanStart",e=>{if(tv(e)!==e)return;let t=ej().getPropagationContext();i=function(e,t,r){let n=tp(t),i={spanContext:t.spanContext(),startTimestamp:n.start_timestamp,sampleRate:function(){try{return Number(r.dsc?.sample_rate)??Number(n.data?.[eq])}catch{return 0}}(),sampleRand:r.sampleRand};if(!e)return i;let a=e.spanContext;return a.traceId===n.trace_id?e:(Date.now()/1e3-e.startTimestamp<=3600&&(t.addLink({context:a,attributes:{"sentry.link.type":"previous_trace"}}),t.setAttribute("sentry.previous_trace",`${a.traceId}-${a.spanId}-${+!!i2(a)}`)),i)}(i,e,t),n&&function(e){try{C.sessionStorage.setItem(i1,JSON.stringify(e))}catch(e){}}(i)});let a=!0;r&&e.on("beforeSampling",e=>{if(!i)return;let t=ej(),r=t.getPropagationContext();if(a&&r.parentSpanId){a=!1;return}t.setPropagationContext({...r,dsc:{...r.dsc,sample_rate:String(i.sampleRate),sampled:String(i2(i.spanContext))},sampleRand:i.sampleRand}),e.parentSampled=i2(i.spanContext),e.parentSampleRate=i.sampleRate,e.spanAttributes={...e.spanAttributes,[ez]:i.sampleRate}})}(e,{linkPreviousTrace:M,consistentTraceSampling:k}),C.location){if(x){let t=eS();an(e,{name:C.location.pathname,startTime:t?t/1e3:void 0,attributes:{[eW]:"url",[eG]:"auto.pageload.browser"}})}O&&rV(({to:t,from:n})=>{if(void 0===n&&d?.indexOf(t)!==-1){d=void 0;return}d=void 0;let i=rF(t),a=e[as],s=a&&I&&function(e,t){let r=tp(e),n=eb();return!(n-r.start_timestamp>.3)&&(!t||!(n-t<=.3))}(a,r);ai(e,{name:i?.pathname||C.location.pathname,attributes:{[eW]:"url",[eG]:"auto.navigation.browser"}},{url:t,isRedirect:s})})}g&&C.document&&C.document.addEventListener("visibilitychange",()=>{let e=tb();if(!e)return;let t=tv(e);if(C.document.hidden&&t){let{op:e,status:r}=tp(t);r||t.setStatus({code:2,message:"cancelled"}),t.setAttribute("sentry.cancellation_reason","document.hidden"),t.end()}}),u&&(t=e,i=h,s=f,o=m,l=n,C.document&&addEventListener("click",()=>{let e=function(e){return e[as]}(t);if(!(e&&["navigation","pageload"].includes(tp(e).op)))c&&(c.setAttribute(eV,"interactionInterrupted"),c.end(),c=void 0),l.name&&(c=nF({name:l.name,op:"ui.action.click",attributes:{[eW]:l.source||"url"}},{idleTimeout:i,finalTimeout:s,childSpanTimeout:o}))},{capture:!0})),a&&function(){let e=({entries:e})=>{let t=tb(),r=t&&tv(t);e.forEach(e=>{if(!("duration"in e)||!r)return;let t=e.interactionId;if(null!=t&&!iJ.has(t)){if(iK.length>10){let e=iK.shift();iJ.delete(e)}iK.push(t),iJ.set(t,r)}})};iO("event",e),iO("first-input",e)}(),function(e,t){let{traceFetch:r,traceXHR:n,trackFetchStreamPerformance:i,shouldCreateSpanForRequest:a,enableHTTPTimings:s,tracePropagationTargets:o,onRequestSpanStart:l}={...i7,...t},u="function"==typeof a?a:e=>!0,c=e=>(function(e,t){let r=en();if(r){let n,i;try{n=new URL(e,r),i=new URL(r).origin}catch{return!1}let a=n.origin===i;return t?eo(n.toString(),t)||a&&eo(n.pathname,t):a}{let r=!!e.match(/^\/(?!\/)/);return t?eo(e,t):r}})(e,o),d={};r&&(e.addEventProcessor(e=>("transaction"===e.type&&e.spans&&e.spans.forEach(e=>{if("http.client"===e.op){let t=i6.get(e.span_id);t&&(e.timestamp=t/1e3,i6.delete(e.span_id))}}),e)),i&&function(e){let t="fetch-body-resolved";rP(t,e),rT(t,()=>rM(rA))}(e=>{if(e.response){let t=i4.get(e.response);t&&e.endTimestamp&&i6.set(t,e.endTimestamp)}}),rC(e=>{let t=function(e,t,r,n,i="auto.http.browser"){if(!e.fetchData)return;let{method:a,url:s}=e.fetchData,o=te()&&t(s);if(e.endTimestamp&&o){let t=e.fetchData.__span;if(!t)return;let r=n[t];r&&(function(e,t){if(t.response){tt(e,t.response.status);let r=t.response?.headers?.get("content-length");if(r){let t=parseInt(r);t>0&&e.setAttribute("http.response_content_length",t)}}else t.error&&e.setStatus({code:2,message:"internal_error"});e.end()}(r,e),delete n[t]);return}let l=!!tb(),u=o&&l?nM(function(e,t,r){let n=rF(e);return{name:n?`${t} ${function(e){if(rU(e))return e.pathname;let t=new URL(e);return t.search="",t.hash="",["80","443"].includes(t.port)&&(t.port=""),t.password&&(t.password="%filtered%"),t.username&&(t.username="%filtered%"),t.toString()}(n)}`:t,attributes:function(e,t,r,n){let i={url:e,type:"fetch","http.method":r,[eG]:n,[eX]:"http.client"};return t&&(rU(t)||(i["http.url"]=t.href,i["server.address"]=t.host),t.search&&(i["http.query"]=t.search),t.hash&&(i["http.fragment"]=t.hash)),i}(e,n,t,r)}}(s,a,i)):new nP;if(e.fetchData.__span=u.spanContext().spanId,n[u.spanContext().spanId]=u,r(e.fetchData.url)){let t=e.args[0],r=e.args[1]||{},n=function(e,t,r){var n;let i=i3({span:r}),a=i["sentry-trace"],s=i.baggage;if(!a)return;let o=t.headers||(et(e)?e.headers:void 0);if(!o)return{...i};if(n=o,"undefined"!=typeof Headers&&Z(n,Headers)){let e=new Headers(o);if(e.get("sentry-trace")||e.set("sentry-trace",a),s){let t=e.get("baggage");t?i8(t)||e.set("baggage",`${t},${s}`):e.set("baggage",s)}return e}if(Array.isArray(o)){let e=[...o];o.find(e=>"sentry-trace"===e[0])||e.push(["sentry-trace",a]);let t=o.find(e=>"baggage"===e[0]&&i8(e[1]));return s&&!t&&e.push(["baggage",s]),e}{let e="sentry-trace"in o?o["sentry-trace"]:void 0,t="baggage"in o?o.baggage:void 0,r=t?Array.isArray(t)?[...t]:[t]:[],n=t&&(Array.isArray(t)?t.find(e=>i8(e)):i8(t));return s&&!n&&r.push(s),{...o,"sentry-trace":e??a,baggage:r.length>0?r.join(","):void 0}}}(t,r,te()&&l?u:void 0);n&&(e.args[1]=r,r.headers=n)}let c=eU();if(c){let t={input:e.args,response:e.response,startTimestamp:e.startTimestamp,endTimestamp:e.endTimestamp};c.emit("beforeOutgoingRequestSpan",u,t)}return u}(e,u,c,d);if(e.response&&e.fetchData.__span&&i4.set(e.response,e.fetchData.__span),t){let r=ae(e.fetchData.url),n=r?rB(r).host:void 0;t.setAttributes({"http.url":r,"server.address":n}),s&&i9(t),l?.(t,{headers:e.headers})}})),n&&rX(e=>{let t=function(e,t,r,n){let i=e.xhr,a=i?.[rz];if(!i||i.__sentry_own_request__||!a)return;let{url:s,method:o}=a,l=te()&&t(s);if(e.endTimestamp&&l){let e=i.__sentry_xhr_span_id__;if(!e)return;let t=n[e];t&&void 0!==a.status_code&&(tt(t,a.status_code),t.end(),delete n[e]);return}let u=ae(s),c=u?rB(u):rB(s),d=r$(s),p=!!tb(),h=l&&p?nM({name:`${o} ${d}`,attributes:{url:s,type:"xhr","http.method":o,"http.url":u,"server.address":c?.host,[eG]:"auto.http.browser",[eX]:"http.client",...c?.search&&{"http.query":c?.search},...c?.hash&&{"http.fragment":c?.hash}}}):new nP;i.__sentry_xhr_span_id__=h.spanContext().spanId,n[i.__sentry_xhr_span_id__]=h,r(s)&&function(e,t){let{"sentry-trace":r,baggage:n}=i3({span:t});r&&function(e,t,r){let n=e.__sentry_xhr_v3__?.request_headers;if(!n?.["sentry-trace"])try{if(e.setRequestHeader("sentry-trace",t),r){let t=n?.baggage;t&&t.split(",").some(e=>e.trim().startsWith("sentry-"))||e.setRequestHeader("baggage",r)}}catch{}}(e,r,n)}(i,te()&&p?h:void 0);let f=eU();return f&&f.emit("beforeOutgoingRequestSpan",h,e),h}(e,u,c,d);if(t){let r;s&&i9(t);try{r=new Headers(e.xhr.__sentry_xhr_v3__?.request_headers)}catch{}l?.(t,{headers:r})}})}(e,{traceFetch:y,traceXHR:E,trackFetchStreamPerformance:S,tracePropagationTargets:e.getOptions().tracePropagationTargets,shouldCreateSpanForRequest:w,enableHTTPTimings:R,onRequestSpanStart:A})}}};function an(e,t,r){e.emit("startPageLoadSpan",t,r),ej().setTransactionName(t.name);let n=e[as];return n&&e.emit("afterStartPageLoadSpan",n),n}function ai(e,t,r){let{url:n,isRedirect:i}=r||{};e.emit("beforeStartNavigationSpan",t,{isRedirect:i}),e.emit("startNavigationSpan",t,{isRedirect:i});let a=ej();return a.setTransactionName(t.name),n&&!i&&a.setSDKProcessingMetadata({normalizedRequest:{...rE(),url:n}}),e[as]}function aa(e){let t=C.document,r=t?.querySelector(`meta[name=${e}]`);return r?.getAttribute("content")||void 0}let as="_sentry_idleSpan",ao=null,al=new Map,au=new Map;function ac(e){let t=e.split("/").filter(Boolean),r=0;for(let e of t)if(e.startsWith(":")){let t=e.substring(1);t.endsWith("*?")?r+=1e3:t.endsWith("*")?r+=100:r+=10}return r}let ad=e=>{let t=function(){if(!C?._sentryRouteManifest||"string"!=typeof C._sentryRouteManifest)return null;let e=C._sentryRouteManifest;if(ao&&x===e)return ao;al.clear(),au.clear();let t={staticRoutes:[],dynamicRoutes:[]};try{if(t=JSON.parse(e),!Array.isArray(t.staticRoutes)||!Array.isArray(t.dynamicRoutes))return null;return ao=t,x=e,t}catch{return null}}();if(!t)return;if(au.has(e))return au.get(e);let{staticRoutes:r,dynamicRoutes:n}=t;if(!Array.isArray(r)||!Array.isArray(n))return;let i=(function(e,t,r){let n=[];if(t.some(t=>t.path===e))return n;for(let t of r)if(t.regex){let r=function(e){if(al.has(e))return al.get(e)??null;try{let t=new RegExp(e);return al.set(e,t),t}catch(e){return null}}(t.regex);r?.test(e)&&n.push(t.path)}return n})(e,r,n).sort((e,t)=>ac(e)-ac(t))[0];return au.set(e,i),i},ap="incomplete-app-router-transaction",ah="router-patch",af={current:void 0};function am(e){try{return new URL(e,"http://example.com/").pathname}catch{return"/"}}let ag=new WeakSet;function a_(e,t,r){ag.has(t)||(ag.add(t),["back","forward","push","replace"].forEach(n=>{t?.[n]&&(t[n]=new Proxy(t[n],{apply(t,i,a){if("router-patch"!==ah)return t.apply(i,a);let s=ap,o={[eX]:"navigation",[eG]:"auto.navigation.nextjs.app_router_instrumentation",[eW]:"url"};"push"===n?(s=am(a[0]),o["navigation.type"]="router.push"):"replace"===n?(s=am(a[0]),o["navigation.type"]="router.replace"):"back"===n?o["navigation.type"]="router.back":"forward"===n&&(o["navigation.type"]="router.forward");let l=ad(s);return r.current=ai(e,{name:l??s,attributes:{...o,[eW]:l?"route":"url"}}),t.apply(i,a)}}))}))}var ay=r(89099);let av=ay.events?ay:ay.default,ab=/^(\S+:\\|\/?)([\s\S]*?)((?:\.{1,2}|[^/\\]+?|)(\.[^./\\]*|))(?:[/\\]*)$/;function aE(...e){let t="",r=!1;for(let n=e.length-1;n>=-1&&!r;n--){let i=n>=0?e[n]:"/";i&&(t=`${i}/${t}`,r="/"===i.charAt(0))}return t=(function(e,t){let r=0;for(let t=e.length-1;t>=0;t--){let n=e[t];"."===n?e.splice(t,1):".."===n?(e.splice(t,1),r++):r&&(e.splice(t,1),r--)}if(t)for(;r--;)e.unshift("..");return e})(t.split("/").filter(e=>!!e),!r).join("/"),(r?"/":"")+t||"."}function aS(e){let t=0;for(;t<e.length&&""===e[t];t++);let r=e.length-1;for(;r>=0&&""===e[r];r--);return t>r?[]:e.slice(t,r-t+1)}let aw=(e={})=>{let t=e.root,r=e.prefix||"app:///",n="window"in C&&!!C.window,i=e.iteratee||function({isBrowser:e,root:t,prefix:r}){return n=>{if(!n.filename)return n;let i=/^[a-zA-Z]:\\/.test(n.filename)||n.filename.includes("\\")&&!n.filename.includes("/"),a=/^\//.test(n.filename);if(e){if(t){let e=n.filename;0===e.indexOf(t)&&(n.filename=e.replace(t,r))}}else if(i||a){let e=i?n.filename.replace(/^[a-zA-Z]:/,"").replace(/\\/g,"/"):n.filename,a=t?function(e,t){e=aE(e).slice(1),t=aE(t).slice(1);let r=aS(e.split("/")),n=aS(t.split("/")),i=Math.min(r.length,n.length),a=i;for(let e=0;e<i;e++)if(r[e]!==n[e]){a=e;break}let s=[];for(let e=a;e<r.length;e++)s.push("..");return(s=s.concat(n.slice(a))).join("/")}(t,e):function(e){let t=e.length>1024?`<truncated>${e.slice(-1024)}`:e,r=ab.exec(t);return r?r.slice(1):[]}(e)[2]||"";n.filename=`${r}${a}`}return n}}({isBrowser:n,root:t,prefix:r});return{name:"RewriteFrames",processEvent(e){let t=e;return e.exception&&Array.isArray(e.exception.values)&&(t=function(e){try{return{...e,exception:{...e.exception,values:e.exception.values.map(e=>{var t;return{...e,...e.stacktrace&&{stacktrace:{...t=e.stacktrace,frames:t?.frames?.map(e=>i(e))}}}})}}}catch{return e}}(t)),t}}},aR=({assetPrefix:e,basePath:t,rewriteFramesAssetPrefixPath:r,experimentalThirdPartyOriginStackFrames:n})=>({...aw({iteratee:i=>{if(n){let r="undefined"!=typeof window&&window.location?window.location.origin:"";if(i.filename?.startsWith(r)&&!i.filename.endsWith(".js"))return i;if(e)i.filename?.startsWith(e)&&(i.filename=i.filename.replace(e,"app://"));else if(t)try{let{origin:e}=new URL(i.filename);e===r&&(i.filename=i.filename?.replace(e,"app://").replace(t,""))}catch{}}else try{let{origin:e}=new URL(i.filename);i.filename=i.filename?.replace(e,"app://").replace(r,"")}catch{}return n?(i.filename?.includes("/_next")&&(i.filename=decodeURI(i.filename)),i.filename?.match(/\/_next\/static\/chunks\/(main-|main-app-|polyfills-|webpack-|framework-|framework\.)[0-9a-f]+\.js$/)&&(i.in_app=!1)):(i.filename?.startsWith("app:///_next")&&(i.filename=decodeURI(i.filename)),i.filename?.match(/^app:\/\/\/_next\/static\/chunks\/(main-|main-app-|polyfills-|webpack-|framework-|framework\.)[0-9a-f]+\.js$/)&&(i.in_app=!1)),i}}),name:"NextjsClientStackFrameNormalization"});var aP=r(65364);let aT=!1;function ax(e){return"/"===e[e.length-1]?e.slice(0,-1):e}var aO=r(65364);function aI(){return"undefined"!=typeof window&&(!(!("undefined"!=typeof __SENTRY_BROWSER_BUNDLE__&&__SENTRY_BROWSER_BUNDLE__)&&"[object process]"===Object.prototype.toString.call(void 0!==aO?aO:0))||function(){let e=C.process;return e?.type==="renderer"}())}function aC(e){return new URLSearchParams(e).toString()}function aM(e,t=B){try{if("string"==typeof e)return[e];if(e instanceof URLSearchParams)return[e.toString()];if(e instanceof FormData)return[aC(e)];if(!e)return[void 0]}catch(e){return[void 0,"BODY_PARSE_ERROR"]}return[void 0,"UNPARSEABLE_BODY_TYPE"]}function ak(e=[]){if(2===e.length&&"object"==typeof e[1])return e[1].body}let aA="sentryReplaySession",aN="Unable to send Replay";var aj=Object.defineProperty,aL=(e,t,r)=>t in e?aj(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,aD=(e,t,r)=>aL(e,"symbol"!=typeof t?t+"":t,r),aU=(e=>(e[e.Document=0]="Document",e[e.DocumentType=1]="DocumentType",e[e.Element=2]="Element",e[e.Text=3]="Text",e[e.CDATA=4]="CDATA",e[e.Comment=5]="Comment",e))(aU||{});function aF(e){let t=e?.host;return t?.shadowRoot===e}function aB(e){return"[object ShadowRoot]"===Object.prototype.toString.call(e)}function a$(e){try{var t;let r=e.rules||e.cssRules;return r?((t=Array.from(r,aH).join("")).includes(" background-clip: text;")&&!t.includes(" -webkit-background-clip: text;")&&(t=t.replace(/\sbackground-clip:\s*text;/g," -webkit-background-clip: text; background-clip: text;")),t):null}catch(e){return null}}function aH(e){let t;if("styleSheet"in e)try{t=a$(e.styleSheet)||function(e){let{cssText:t}=e;if(t.split('"').length<3)return t;let r=["@import",`url(${JSON.stringify(e.href)})`];return""===e.layerName?r.push("layer"):e.layerName&&r.push(`layer(${e.layerName})`),e.supportsText&&r.push(`supports(${e.supportsText})`),e.media.length&&r.push(e.media.mediaText),r.join(" ")+";"}(e)}catch(e){}else if("selectorText"in e){let t=e.cssText,r=e.selectorText.includes(":"),n="string"==typeof e.style.all&&e.style.all;if(n&&(t=function(e){let t="";for(let r=0;r<e.style.length;r++){let n=e.style,i=n[r],a=n.getPropertyPriority(i);t+=`${i}:${n.getPropertyValue(i)}${a?" !important":""};`}return`${e.selectorText} { ${t} }`}(e)),r&&(t=t.replace(/(\[(?:[\w-]+)[^\\])(:(?:[\w-]+)\])/gm,"$1\\$2")),r||n)return t}return t||e.cssText}class aW{constructor(){aD(this,"idNodeMap",new Map),aD(this,"nodeMetaMap",new WeakMap)}getId(e){return e?this.getMeta(e)?.id??-1:-1}getNode(e){return this.idNodeMap.get(e)||null}getIds(){return Array.from(this.idNodeMap.keys())}getMeta(e){return this.nodeMetaMap.get(e)||null}removeNodeFromMap(e){let t=this.getId(e);this.idNodeMap.delete(t),e.childNodes&&e.childNodes.forEach(e=>this.removeNodeFromMap(e))}has(e){return this.idNodeMap.has(e)}hasNode(e){return this.nodeMetaMap.has(e)}add(e,t){let r=t.id;this.idNodeMap.set(r,e),this.nodeMetaMap.set(e,t)}replace(e,t){let r=this.getNode(e);if(r){let e=this.nodeMetaMap.get(r);e&&this.nodeMetaMap.set(t,e)}this.idNodeMap.set(e,t)}reset(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}}function aq({maskInputOptions:e,tagName:t,type:r}){return"OPTION"===t&&(t="SELECT"),!!(e[t.toLowerCase()]||r&&e[r]||"password"===r||"INPUT"===t&&!r&&e.text)}function az({isMasked:e,element:t,value:r,maskInputFn:n}){let i=r||"";return e?(n&&(i=n(i,t)),"*".repeat(i.length)):i}function aX(e){return e.toLowerCase()}function aG(e){return e.toUpperCase()}let aV="__rrweb_original__";function aY(e){let t=e.type;return e.hasAttribute("data-rr-is-password")?"password":t?aX(t):null}function aK(e,t,r){return"INPUT"===t&&("radio"===r||"checkbox"===r)?e.getAttribute("value")||"":e.value}function aJ(e,t){let r;try{r=new URL(e,t??window.location.href)}catch(e){return null}let n=r.pathname.match(/\.([0-9a-z]+)(?:$)/i);return n?.[1]??null}let aQ={};function aZ(e){let t=aQ[e];if(t)return t;let r=window.document,n=window[e];if(r&&"function"==typeof r.createElement)try{let t=r.createElement("iframe");t.hidden=!0,r.head.appendChild(t);let i=t.contentWindow;i&&i[e]&&(n=i[e]),r.head.removeChild(t)}catch(e){}return aQ[e]=n.bind(window)}function a0(...e){return aZ("setTimeout")(...e)}function a1(...e){return aZ("clearTimeout")(...e)}function a2(e){try{return e.contentDocument}catch(e){}}let a3=1,a8=RegExp("[^a-z0-9-_:]");function a5(){return a3++}let a4=/url\((?:(')([^']*)'|(")(.*?)"|([^)]*))\)/gm,a6=/^(?:[a-z+]+:)?\/\//i,a7=/^www\..*/i,a9=/^(data:)([^,]*),(.*)/i;function se(e,t){return(e||"").replace(a4,(e,r,n,i,a,s)=>{let o=n||a||s,l=r||i||"";if(!o)return e;if(a6.test(o)||a7.test(o)||a9.test(o))return`url(${l}${o}${l})`;if("/"===o[0]){let e;return`url(${l}${(t.indexOf("//")>-1?t.split("/").slice(0,3).join("/"):t.split("/")[0]).split("?")[0]+o}${l})`}let u=t.split("/"),c=o.split("/");for(let e of(u.pop(),c))if("."===e)continue;else".."===e?u.pop():u.push(e);return`url(${l}${u.join("/")}${l})`})}let st=/^[^ \t\n\r\u000c]+/,sr=/^[, \t\n\r\u000c]+/,sn=new WeakMap;function si(e,t){return t&&""!==t.trim()?sa(e,t):t}function sa(e,t){let r=sn.get(e);if(r||(r=e.createElement("a"),sn.set(e,r)),t){if(t.startsWith("blob:")||t.startsWith("data:"))return t}else t="";return r.setAttribute("href",t),r.href}function ss(e,t,r,n,i,a){if(!n)return n;if("src"===r||"href"===r&&("use"!==t||"#"!==n[0])||"xlink:href"===r&&"#"!==n[0])return si(e,n);if("background"===r&&("table"===t||"td"===t||"th"===t))return si(e,n);if("srcset"===r)return function(e,t){if(""===t.trim())return t;let r=0;function n(e){let n,i=e.exec(t.substring(r));return i?(n=i[0],r+=n.length,n):""}let i=[];for(;n(sr),!(r>=t.length);){let a=n(st);if(","===a.slice(-1))a=si(e,a.substring(0,a.length-1)),i.push(a);else{let n="";a=si(e,a);let s=!1;for(;;){let e=t.charAt(r);if(""===e){i.push((a+n).trim());break}if(s)")"===e&&(s=!1);else if(","===e){r+=1,i.push((a+n).trim());break}else"("===e&&(s=!0);n+=e,r+=1}}}return i.join(", ")}(e,n);if("style"===r)return se(n,sa(e));else if("object"===t&&"data"===r)return si(e,n);return"function"==typeof a?a(r,n,i):n}function so(e,t,r){return("video"===e||"audio"===e)&&"autoplay"===t}function sl(e,t,r,n){try{if(n&&e.matches(n))return!1;if("string"==typeof t){if(e.classList.contains(t))return!0}else for(let r=e.classList.length;r--;){let n=e.classList[r];if(t.test(n))return!0}if(r)return e.matches(r)}catch(e){}return!1}function su(e,t,r=1/0,n=0){return!e||e.nodeType!==e.ELEMENT_NODE||n>r?-1:t(e)?n:su(e.parentNode,t,r,n+1)}function sc(e,t){return r=>{if(null===r)return!1;try{if(e){if("string"==typeof e){if(r.matches(`.${e}`))return!0}else if(function(e,t){for(let r=e.classList.length;r--;){let n=e.classList[r];if(t.test(n))return!0}return!1}(r,e))return!0}if(t&&r.matches(t))return!0;return!1}catch{return!1}}}function sd(e,t,r,n,i,a){try{let s=e.nodeType===e.ELEMENT_NODE?e:e.parentElement;if(null===s)return!1;if("INPUT"===s.tagName){let e=s.getAttribute("autocomplete");if(["current-password","new-password","cc-number","cc-exp","cc-exp-month","cc-exp-year","cc-csc"].includes(e))return!0}let o=-1,l=-1;if(a){if((l=su(s,sc(n,i)))<0)return!0;o=su(s,sc(t,r),l>=0?l:1/0)}else{if((o=su(s,sc(t,r)))<0)return!1;l=su(s,sc(n,i),o>=0?o:1/0)}return o>=0?!(l>=0)||o<=l:!(l>=0)&&!!a}catch(e){}return!!a}function sp(e){return null==e?"":e.toLowerCase()}function sh(e,t){let r,{doc:n,mirror:i,blockClass:a,blockSelector:s,unblockSelector:o,maskAllText:l,maskTextClass:u,unmaskTextClass:c,maskTextSelector:d,unmaskTextSelector:p,skipChild:h=!1,inlineStylesheet:f=!0,maskInputOptions:m={},maskAttributeFn:g,maskTextFn:_,maskInputFn:y,slimDOMOptions:v,dataURLOptions:b={},inlineImages:w=!1,recordCanvas:R=!1,onSerialize:P,onIframeLoad:T,iframeLoadTimeout:x=5e3,onStylesheetLoad:O,stylesheetLoadTimeout:I=5e3,keepIframeSrcFn:C=()=>!1,newlyAddedElement:M=!1}=t,{preserveWhiteSpace:k=!0}=t,A=function(e,t){let{doc:r,mirror:n,blockClass:i,blockSelector:a,unblockSelector:s,maskAllText:o,maskAttributeFn:l,maskTextClass:u,unmaskTextClass:c,maskTextSelector:d,unmaskTextSelector:p,inlineStylesheet:h,maskInputOptions:f={},maskTextFn:m,maskInputFn:g,dataURLOptions:_={},inlineImages:y,recordCanvas:v,keepIframeSrcFn:b,newlyAddedElement:w=!1}=t,R=function(e,t){if(!t.hasNode(e))return;let r=t.getId(e);return 1===r?void 0:r}(r,n);switch(e.nodeType){case e.DOCUMENT_NODE:if("CSS1Compat"!==e.compatMode)return{type:aU.Document,childNodes:[],compatMode:e.compatMode};return{type:aU.Document,childNodes:[]};case e.DOCUMENT_TYPE_NODE:return{type:aU.DocumentType,name:e.name,publicId:e.publicId,systemId:e.systemId,rootId:R};case e.ELEMENT_NODE:return function(e,t){let r,{doc:n,blockClass:i,blockSelector:a,unblockSelector:s,inlineStylesheet:o,maskInputOptions:l={},maskAttributeFn:u,maskInputFn:c,dataURLOptions:d={},inlineImages:p,recordCanvas:h,keepIframeSrcFn:f,newlyAddedElement:m=!1,rootId:g,maskTextClass:_,unmaskTextClass:y,maskTextSelector:v,unmaskTextSelector:b}=t,w=sl(e,i,a,s),R=function(e){if(e instanceof HTMLFormElement)return"form";let t=aX(e.tagName);return a8.test(t)?"div":t}(e),P={},T=e.attributes.length;for(let t=0;t<T;t++){let r=e.attributes[t];r.name&&!so(R,r.name,r.value)&&(P[r.name]=ss(n,R,aX(r.name),r.value,e,u))}if("link"===R&&o){let t=Array.from(n.styleSheets).find(t=>t.href===e.href),r=null;t&&(r=a$(t)),r&&(P.rel=null,P.href=null,P.crossorigin=null,P._cssText=se(r,t.href))}if("style"===R&&e.sheet&&!(e.innerText||e.textContent||"").trim().length){let t=a$(e.sheet);t&&(P._cssText=se(t,sa(n)))}if("input"===R||"textarea"===R||"select"===R||"option"===R){let t=aY(e),r=aK(e,aG(R),t),n=e.checked;if("submit"!==t&&"button"!==t&&r){let n=sd(e,_,v,y,b,aq({type:t,tagName:aG(R),maskInputOptions:l}));P.value=az({isMasked:n,element:e,value:r,maskInputFn:c})}n&&(P.checked=n)}if("option"===R&&(e.selected&&!l.select?P.selected=!0:delete P.selected),"canvas"===R&&h){if("2d"===e.__context)!function(e){let t=e.getContext("2d");if(!t)return!0;for(let r=0;r<e.width;r+=50)for(let n=0;n<e.height;n+=50){let i=t.getImageData;if(new Uint32Array((aV in i?i[aV]:i).call(t,r,n,Math.min(50,e.width-r),Math.min(50,e.height-n)).data.buffer).some(e=>0!==e))return!1}return!0}(e)&&(P.rr_dataURL=e.toDataURL(d.type,d.quality));else if(!("__context"in e)){let t=e.toDataURL(d.type,d.quality),r=n.createElement("canvas");r.width=e.width,r.height=e.height,t!==r.toDataURL(d.type,d.quality)&&(P.rr_dataURL=t)}}if("img"===R&&p){E||(S=(E=n.createElement("canvas")).getContext("2d"));let t=e.currentSrc||e.getAttribute("src")||"<unknown-src>",r=e.crossOrigin,i=()=>{e.removeEventListener("load",i);try{E.width=e.naturalWidth,E.height=e.naturalHeight,S.drawImage(e,0,0),P.rr_dataURL=E.toDataURL(d.type,d.quality)}catch(r){if("anonymous"!==e.crossOrigin){e.crossOrigin="anonymous",e.complete&&0!==e.naturalWidth?i():e.addEventListener("load",i);return}console.warn(`Cannot inline img src=${t}! Error: ${r}`)}"anonymous"===e.crossOrigin&&(r?P.crossOrigin=r:e.removeAttribute("crossorigin"))};e.complete&&0!==e.naturalWidth?i():e.addEventListener("load",i)}if(("audio"===R||"video"===R)&&(P.rr_mediaState=e.paused?"paused":"played",P.rr_mediaCurrentTime=e.currentTime),!m&&(e.scrollLeft&&(P.rr_scrollLeft=e.scrollLeft),e.scrollTop&&(P.rr_scrollTop=e.scrollTop)),w){let{width:t,height:r}=e.getBoundingClientRect();P={class:P.class,rr_width:`${t}px`,rr_height:`${r}px`}}"iframe"!==R||f(P.src)||(w||a2(e)||(P.rr_src=P.src),delete P.src);try{customElements.get(R)&&(r=!0)}catch(e){}return{type:aU.Element,tagName:R,attributes:P,childNodes:[],isSVG:!!("svg"===e.tagName||e.ownerSVGElement)||void 0,needBlock:w,rootId:g,isCustom:r}}(e,{doc:r,blockClass:i,blockSelector:a,unblockSelector:s,inlineStylesheet:h,maskAttributeFn:l,maskInputOptions:f,maskInputFn:g,dataURLOptions:_,inlineImages:y,recordCanvas:v,keepIframeSrcFn:b,newlyAddedElement:w,rootId:R,maskTextClass:u,unmaskTextClass:c,maskTextSelector:d,unmaskTextSelector:p});case e.TEXT_NODE:return function(e,t){let{maskAllText:r,maskTextClass:n,unmaskTextClass:i,maskTextSelector:a,unmaskTextSelector:s,maskTextFn:o,maskInputOptions:l,maskInputFn:u,rootId:c}=t,d=e.parentNode&&e.parentNode.tagName,p=e.textContent,h="STYLE"===d||void 0,f="SCRIPT"===d||void 0,m="TEXTAREA"===d||void 0;if(h&&p){try{e.nextSibling||e.previousSibling||e.parentNode.sheet?.cssRules&&(p=a$(e.parentNode.sheet))}catch(t){console.warn(`Cannot get CSS styles from text's parentNode. Error: ${t}`,e)}p=se(p,sa(t.doc))}f&&(p="SCRIPT_PLACEHOLDER");let g=sd(e,n,a,i,s,r);return h||f||m||!p||!g||(p=o?o(p,e.parentElement):p.replace(/[\S]/g,"*")),m&&p&&(l.textarea||g)&&(p=u?u(p,e.parentNode):p.replace(/[\S]/g,"*")),"OPTION"===d&&p&&(p=az({isMasked:sd(e,n,a,i,s,aq({type:null,tagName:d,maskInputOptions:l})),element:e,value:p,maskInputFn:u})),{type:aU.Text,textContent:p||"",isStyle:h,rootId:c}}(e,{doc:r,maskAllText:o,maskTextClass:u,unmaskTextClass:c,maskTextSelector:d,unmaskTextSelector:p,maskTextFn:m,maskInputOptions:f,maskInputFn:g,rootId:R});case e.CDATA_SECTION_NODE:return{type:aU.CDATA,textContent:"",rootId:R};case e.COMMENT_NODE:return{type:aU.Comment,textContent:e.textContent||"",rootId:R};default:return!1}}(e,{doc:n,mirror:i,blockClass:a,blockSelector:s,maskAllText:l,unblockSelector:o,maskTextClass:u,unmaskTextClass:c,maskTextSelector:d,unmaskTextSelector:p,inlineStylesheet:f,maskInputOptions:m,maskAttributeFn:g,maskTextFn:_,maskInputFn:y,dataURLOptions:b,inlineImages:w,recordCanvas:R,keepIframeSrcFn:C,newlyAddedElement:M});if(!A)return console.warn(e,"not serialized"),null;r=i.hasNode(e)?i.getId(e):!function(e,t){if(t.comment&&e.type===aU.Comment)return!0;if(e.type===aU.Element){if(t.script&&("script"===e.tagName||"link"===e.tagName&&("preload"===e.attributes.rel||"modulepreload"===e.attributes.rel)||"link"===e.tagName&&"prefetch"===e.attributes.rel&&"string"==typeof e.attributes.href&&"js"===aJ(e.attributes.href)))return!0;else if(t.headFavicon&&("link"===e.tagName&&"shortcut icon"===e.attributes.rel||"meta"===e.tagName&&(sp(e.attributes.name).match(/^msapplication-tile(image|color)$/)||"application-name"===sp(e.attributes.name)||"icon"===sp(e.attributes.rel)||"apple-touch-icon"===sp(e.attributes.rel)||"shortcut icon"===sp(e.attributes.rel))))return!0;else if("meta"===e.tagName){if(t.headMetaDescKeywords&&sp(e.attributes.name).match(/^description|keywords$/))return!0;else if(t.headMetaSocial&&(sp(e.attributes.property).match(/^(og|twitter|fb):/)||sp(e.attributes.name).match(/^(og|twitter):/)||"pinterest"===sp(e.attributes.name)))return!0;else if(t.headMetaRobots&&("robots"===sp(e.attributes.name)||"googlebot"===sp(e.attributes.name)||"bingbot"===sp(e.attributes.name)))return!0;else if(t.headMetaHttpEquiv&&void 0!==e.attributes["http-equiv"])return!0;else if(t.headMetaAuthorship&&("author"===sp(e.attributes.name)||"generator"===sp(e.attributes.name)||"framework"===sp(e.attributes.name)||"publisher"===sp(e.attributes.name)||"progid"===sp(e.attributes.name)||sp(e.attributes.property).match(/^article:/)||sp(e.attributes.property).match(/^product:/)))return!0;else if(t.headMetaVerification&&("google-site-verification"===sp(e.attributes.name)||"yandex-verification"===sp(e.attributes.name)||"csrf-token"===sp(e.attributes.name)||"p:domain_verify"===sp(e.attributes.name)||"verify-v1"===sp(e.attributes.name)||"verification"===sp(e.attributes.name)||"shopify-checkout-api-token"===sp(e.attributes.name)))return!0}}return!1}(A,v)&&(k||A.type!==aU.Text||A.isStyle||A.textContent.replace(/^\s+|\s+$/gm,"").length)?a5():-2;let N=Object.assign(A,{id:r});if(i.add(e,N),-2===r)return null;P&&P(e);let j=!h;if(N.type===aU.Element){j=j&&!N.needBlock,delete N.needBlock;let t=e.shadowRoot;t&&aB(t)&&(N.isShadowHost=!0)}if((N.type===aU.Document||N.type===aU.Element)&&j){v.headWhitespace&&N.type===aU.Element&&"head"===N.tagName&&(k=!1);let t={doc:n,mirror:i,blockClass:a,blockSelector:s,maskAllText:l,unblockSelector:o,maskTextClass:u,unmaskTextClass:c,maskTextSelector:d,unmaskTextSelector:p,skipChild:h,inlineStylesheet:f,maskInputOptions:m,maskAttributeFn:g,maskTextFn:_,maskInputFn:y,slimDOMOptions:v,dataURLOptions:b,inlineImages:w,recordCanvas:R,preserveWhiteSpace:k,onSerialize:P,onIframeLoad:T,iframeLoadTimeout:x,onStylesheetLoad:O,stylesheetLoadTimeout:I,keepIframeSrcFn:C};for(let r of Array.from(e.childNodes)){let e=sh(r,t);e&&N.childNodes.push(e)}if(e.nodeType===e.ELEMENT_NODE&&e.shadowRoot)for(let r of Array.from(e.shadowRoot.childNodes)){let n=sh(r,t);n&&(aB(e.shadowRoot)&&(n.isShadow=!0),N.childNodes.push(n))}}return e.parentNode&&aF(e.parentNode)&&aB(e.parentNode)&&(N.isShadow=!0),N.type!==aU.Element||"iframe"!==N.tagName||sl(e,a,s,o)||function(e,t,r){let n,i=e.contentWindow;if(!i)return;let a=!1;try{n=i.document.readyState}catch(e){return}if("complete"!==n){let n=a0(()=>{a||(t(),a=!0)},r);e.addEventListener("load",()=>{a1(n),a=!0,t()});return}let s="about:blank";if(i.location.href!==s||e.src===s||""===e.src)return a0(t,0),e.addEventListener("load",t);e.addEventListener("load",t)}(e,()=>{let t=a2(e);if(t&&T){let r=sh(t,{doc:t,mirror:i,blockClass:a,blockSelector:s,unblockSelector:o,maskAllText:l,maskTextClass:u,unmaskTextClass:c,maskTextSelector:d,unmaskTextSelector:p,skipChild:!1,inlineStylesheet:f,maskInputOptions:m,maskAttributeFn:g,maskTextFn:_,maskInputFn:y,slimDOMOptions:v,dataURLOptions:b,inlineImages:w,recordCanvas:R,preserveWhiteSpace:k,onSerialize:P,onIframeLoad:T,iframeLoadTimeout:x,onStylesheetLoad:O,stylesheetLoadTimeout:I,keepIframeSrcFn:C});r&&T(e,r)}},x),N.type===aU.Element&&"link"===N.tagName&&"string"==typeof N.attributes.rel&&("stylesheet"===N.attributes.rel||"preload"===N.attributes.rel&&"string"==typeof N.attributes.href&&"css"===aJ(N.attributes.href))&&function(e,t,r){let n,i=!1;try{n=e.sheet}catch(e){return}if(n)return;let a=a0(()=>{i||(t(),i=!0)},r);e.addEventListener("load",()=>{a1(a),i=!0,t()})}(e,()=>{if(O){let t=sh(e,{doc:n,mirror:i,blockClass:a,blockSelector:s,unblockSelector:o,maskAllText:l,maskTextClass:u,unmaskTextClass:c,maskTextSelector:d,unmaskTextSelector:p,skipChild:!1,inlineStylesheet:f,maskInputOptions:m,maskAttributeFn:g,maskTextFn:_,maskInputFn:y,slimDOMOptions:v,dataURLOptions:b,inlineImages:w,recordCanvas:R,preserveWhiteSpace:k,onSerialize:P,onIframeLoad:T,iframeLoadTimeout:x,onStylesheetLoad:O,stylesheetLoadTimeout:I,keepIframeSrcFn:C});t&&O(e,t)}},I),N}function sf(e,t,r=document){let n={capture:!0,passive:!0};return r.addEventListener(e,t,n),()=>r.removeEventListener(e,t,n)}let sm="Please stop import mirror directly. Instead of that,\r\nnow you can use replayer.getMirror() to access the mirror instance of a replayer,\r\nor you can use record.mirror to access the mirror instance during recording.",sg={map:{},getId:()=>(console.error(sm),-1),getNode:()=>(console.error(sm),null),removeNodeFromMap(){console.error(sm)},has:()=>(console.error(sm),!1),reset(){console.error(sm)}};function s_(e,t,r={}){let n=null,i=0;return function(...a){let s=Date.now();i||!1!==r.leading||(i=s);let o=t-(s-i),l=this;o<=0||o>t?(n&&(function(...e){sN("clearTimeout")(...e)}(n),n=null),i=s,e.apply(l,a)):n||!1===r.trailing||(n=sj(()=>{i=!1===r.leading?0:Date.now(),n=null,e.apply(l,a)},o))}}function sy(e,t,r){try{if(!(t in e))return()=>{};let n=e[t],i=r(n);return"function"==typeof i&&(i.prototype=i.prototype||{},Object.defineProperties(i,{__rrweb_original__:{enumerable:!1,value:n}})),e[t]=i,()=>{e[t]=n}}catch{return()=>{}}}"undefined"!=typeof window&&window.Proxy&&window.Reflect&&(sg=new Proxy(sg,{get:(e,t,r)=>("map"===t&&console.error(sm),Reflect.get(e,t,r))}));let sv=Date.now;function sb(e){let t=e.document;return{left:t.scrollingElement?t.scrollingElement.scrollLeft:void 0!==e.pageXOffset?e.pageXOffset:t?.documentElement.scrollLeft||t?.body?.parentElement?.scrollLeft||t?.body?.scrollLeft||0,top:t.scrollingElement?t.scrollingElement.scrollTop:void 0!==e.pageYOffset?e.pageYOffset:t?.documentElement.scrollTop||t?.body?.parentElement?.scrollTop||t?.body?.scrollTop||0}}function sE(){return window.innerHeight||document.documentElement&&document.documentElement.clientHeight||document.body&&document.body.clientHeight}function sS(){return window.innerWidth||document.documentElement&&document.documentElement.clientWidth||document.body&&document.body.clientWidth}function sw(e){if(!e)return null;try{return e.nodeType===e.ELEMENT_NODE?e:e.parentElement}catch(e){return null}}function sR(e,t,r,n,i){if(!e)return!1;let a=sw(e);if(!a)return!1;let s=sc(t,r);if(!i){let e=n&&a.matches(n);return s(a)&&!e}let o=su(a,s),l=-1;return!(o<0)&&(n&&(l=su(a,sc(null,n))),o>-1&&l<0||o<l)}function sP(e,t){return -2===t.getId(e)}function sT(e){return!!e.changedTouches}function sx(e,t){return!!("IFRAME"===e.nodeName&&t.getMeta(e))}function sO(e,t){return!!("LINK"===e.nodeName&&e.nodeType===e.ELEMENT_NODE&&e.getAttribute&&"stylesheet"===e.getAttribute("rel")&&t.getMeta(e))}function sI(e){return!!e?.shadowRoot}/[1-9][0-9]{12}/.test(Date.now().toString())||(sv=()=>new Date().getTime());class sC{constructor(){this.id=1,this.styleIDMap=new WeakMap,this.idStyleMap=new Map}getId(e){return this.styleIDMap.get(e)??-1}has(e){return this.styleIDMap.has(e)}add(e,t){let r;return this.has(e)?this.getId(e):(r=void 0===t?this.id++:t,this.styleIDMap.set(e,r),this.idStyleMap.set(r,e),r)}getStyle(e){return this.idStyleMap.get(e)||null}reset(){this.styleIDMap=new WeakMap,this.idStyleMap=new Map,this.id=1}generateId(){return this.id++}}function sM(e){let t=null;return e.getRootNode?.()?.nodeType===Node.DOCUMENT_FRAGMENT_NODE&&e.getRootNode().host&&(t=e.getRootNode().host),t}function sk(e){let t=e.ownerDocument;return!!t&&(t.contains(e)||function(e){let t=e.ownerDocument;if(!t)return!1;let r=function(e){let t,r=e;for(;t=sM(r);)r=t;return r}(e);return t.contains(r)}(e))}let sA={};function sN(e){let t=sA[e];if(t)return t;let r=window.document,n=window[e];if(r&&"function"==typeof r.createElement)try{let t=r.createElement("iframe");t.hidden=!0,r.head.appendChild(t);let i=t.contentWindow;i&&i[e]&&(n=i[e]),r.head.removeChild(t)}catch(e){}return sA[e]=n.bind(window)}function sj(...e){return sN("setTimeout")(...e)}var sL=(e=>(e[e.DomContentLoaded=0]="DomContentLoaded",e[e.Load=1]="Load",e[e.FullSnapshot=2]="FullSnapshot",e[e.IncrementalSnapshot=3]="IncrementalSnapshot",e[e.Meta=4]="Meta",e[e.Custom=5]="Custom",e[e.Plugin=6]="Plugin",e))(sL||{}),sD=(e=>(e[e.Mutation=0]="Mutation",e[e.MouseMove=1]="MouseMove",e[e.MouseInteraction=2]="MouseInteraction",e[e.Scroll=3]="Scroll",e[e.ViewportResize=4]="ViewportResize",e[e.Input=5]="Input",e[e.TouchMove=6]="TouchMove",e[e.MediaInteraction=7]="MediaInteraction",e[e.StyleSheetRule=8]="StyleSheetRule",e[e.CanvasMutation=9]="CanvasMutation",e[e.Font=10]="Font",e[e.Log=11]="Log",e[e.Drag=12]="Drag",e[e.StyleDeclaration=13]="StyleDeclaration",e[e.Selection=14]="Selection",e[e.AdoptedStyleSheet=15]="AdoptedStyleSheet",e[e.CustomElement=16]="CustomElement",e))(sD||{}),sU=(e=>(e[e.MouseUp=0]="MouseUp",e[e.MouseDown=1]="MouseDown",e[e.Click=2]="Click",e[e.ContextMenu=3]="ContextMenu",e[e.DblClick=4]="DblClick",e[e.Focus=5]="Focus",e[e.Blur=6]="Blur",e[e.TouchStart=7]="TouchStart",e[e.TouchMove_Departed=8]="TouchMove_Departed",e[e.TouchEnd=9]="TouchEnd",e[e.TouchCancel=10]="TouchCancel",e))(sU||{}),sF=(e=>(e[e.Mouse=0]="Mouse",e[e.Pen=1]="Pen",e[e.Touch=2]="Touch",e))(sF||{}),sB=(e=>(e[e.Play=0]="Play",e[e.Pause=1]="Pause",e[e.Seeked=2]="Seeked",e[e.VolumeChange=3]="VolumeChange",e[e.RateChange=4]="RateChange",e))(sB||{});function s$(e){try{return e.contentDocument}catch(e){}}class sH{constructor(){this.length=0,this.head=null,this.tail=null}get(e){if(e>=this.length)throw Error("Position outside of list range");let t=this.head;for(let r=0;r<e;r++)t=t?.next||null;return t}addNode(e){let t={value:e,previous:null,next:null};if(e.__ln=t,e.previousSibling&&"__ln"in e.previousSibling){let r=e.previousSibling.__ln.next;t.next=r,t.previous=e.previousSibling.__ln,e.previousSibling.__ln.next=t,r&&(r.previous=t)}else if(e.nextSibling&&"__ln"in e.nextSibling&&e.nextSibling.__ln.previous){let r=e.nextSibling.__ln.previous;t.previous=r,t.next=e.nextSibling.__ln,e.nextSibling.__ln.previous=t,r&&(r.next=t)}else this.head&&(this.head.previous=t),t.next=this.head,this.head=t;null===t.next&&(this.tail=t),this.length++}removeNode(e){let t=e.__ln;this.head&&(t.previous?(t.previous.next=t.next,t.next?t.next.previous=t.previous:this.tail=t.previous):(this.head=t.next,this.head?this.head.previous=null:this.tail=null),e.__ln&&delete e.__ln,this.length--)}}let sW=(e,t)=>`${e}@${t}`;class sq{constructor(){this.frozen=!1,this.locked=!1,this.texts=[],this.attributes=[],this.attributeMap=new WeakMap,this.removes=[],this.mapRemoves=[],this.movedMap={},this.addedSet=new Set,this.movedSet=new Set,this.droppedSet=new Set,this.processMutations=e=>{e.forEach(this.processMutation),this.emit()},this.emit=()=>{if(this.frozen||this.locked)return;let e=[],t=new Set,r=new sH,n=e=>{let t=e,r=-2;for(;-2===r;)r=(t=t&&t.nextSibling)&&this.mirror.getId(t);return r},i=i=>{if(!i.parentNode||!sk(i))return;let a=aF(i.parentNode)?this.mirror.getId(sM(i)):this.mirror.getId(i.parentNode),s=n(i);if(-1===a||-1===s)return r.addNode(i);let o=sh(i,{doc:this.doc,mirror:this.mirror,blockClass:this.blockClass,blockSelector:this.blockSelector,maskAllText:this.maskAllText,unblockSelector:this.unblockSelector,maskTextClass:this.maskTextClass,unmaskTextClass:this.unmaskTextClass,maskTextSelector:this.maskTextSelector,unmaskTextSelector:this.unmaskTextSelector,skipChild:!0,newlyAddedElement:!0,inlineStylesheet:this.inlineStylesheet,maskInputOptions:this.maskInputOptions,maskAttributeFn:this.maskAttributeFn,maskTextFn:this.maskTextFn,maskInputFn:this.maskInputFn,slimDOMOptions:this.slimDOMOptions,dataURLOptions:this.dataURLOptions,recordCanvas:this.recordCanvas,inlineImages:this.inlineImages,onSerialize:e=>{sx(e,this.mirror)&&!sR(e,this.blockClass,this.blockSelector,this.unblockSelector,!1)&&this.iframeManager.addIframe(e),sO(e,this.mirror)&&this.stylesheetManager.trackLinkElement(e),sI(i)&&this.shadowDomManager.addShadowRoot(i.shadowRoot,this.doc)},onIframeLoad:(e,t)=>{sR(e,this.blockClass,this.blockSelector,this.unblockSelector,!1)||(this.iframeManager.attachIframe(e,t),e.contentWindow&&this.canvasManager.addWindow(e.contentWindow),this.shadowDomManager.observeAttachShadow(e))},onStylesheetLoad:(e,t)=>{this.stylesheetManager.attachLinkElement(e,t)}});o&&(e.push({parentId:a,nextId:s,node:o}),t.add(o.id))};for(;this.mapRemoves.length;)this.mirror.removeNodeFromMap(this.mapRemoves.shift());for(let e of this.movedSet)(!sX(this.removes,e,this.mirror)||this.movedSet.has(e.parentNode))&&i(e);for(let e of this.addedSet)sG(this.droppedSet,e)||sX(this.removes,e,this.mirror)?sG(this.movedSet,e)?i(e):this.droppedSet.add(e):i(e);let a=null;for(;r.length;){let e=null;if(a){let t=this.mirror.getId(a.value.parentNode),r=n(a.value);-1!==t&&-1!==r&&(e=a)}if(!e){let t=r.tail;for(;t;){let r=t;if(t=t.previous,r){let t=this.mirror.getId(r.value.parentNode);if(-1===n(r.value))continue;if(-1!==t){e=r;break}{let t=r.value;if(t.parentNode&&t.parentNode.nodeType===Node.DOCUMENT_FRAGMENT_NODE){let n=t.parentNode.host;if(-1!==this.mirror.getId(n)){e=r;break}}}}}}if(!e){for(;r.head;)r.removeNode(r.head.value);break}a=e.previous,r.removeNode(e.value),i(e.value)}let s={texts:this.texts.map(e=>({id:this.mirror.getId(e.node),value:e.value})).filter(e=>!t.has(e.id)).filter(e=>this.mirror.has(e.id)),attributes:this.attributes.map(e=>{let{attributes:t}=e;if("string"==typeof t.style){let r=JSON.stringify(e.styleDiff),n=JSON.stringify(e._unchangedStyles);r.length<t.style.length&&(r+n).split("var(").length===t.style.split("var(").length&&(t.style=e.styleDiff)}return{id:this.mirror.getId(e.node),attributes:t}}).filter(e=>!t.has(e.id)).filter(e=>this.mirror.has(e.id)),removes:this.removes,adds:e};(s.texts.length||s.attributes.length||s.removes.length||s.adds.length)&&(this.texts=[],this.attributes=[],this.attributeMap=new WeakMap,this.removes=[],this.addedSet=new Set,this.movedSet=new Set,this.droppedSet=new Set,this.movedMap={},this.mutationCb(s))},this.processMutation=e=>{if(!sP(e.target,this.mirror))switch(e.type){case"characterData":{let t=e.target.textContent;sR(e.target,this.blockClass,this.blockSelector,this.unblockSelector,!1)||t===e.oldValue||this.texts.push({value:sd(e.target,this.maskTextClass,this.maskTextSelector,this.unmaskTextClass,this.unmaskTextSelector,this.maskAllText)&&t?this.maskTextFn?this.maskTextFn(t,sw(e.target)):t.replace(/[\S]/g,"*"):t,node:e.target});break}case"attributes":{let t=e.target,r=e.attributeName,n=e.target.getAttribute(r);if("value"===r){let r=aY(t),i=t.tagName;n=aK(t,i,r);let a=aq({maskInputOptions:this.maskInputOptions,tagName:i,type:r});n=az({isMasked:sd(e.target,this.maskTextClass,this.maskTextSelector,this.unmaskTextClass,this.unmaskTextSelector,a),element:t,value:n,maskInputFn:this.maskInputFn})}if(sR(e.target,this.blockClass,this.blockSelector,this.unblockSelector,!1)||n===e.oldValue)return;let i=this.attributeMap.get(e.target);if("IFRAME"===t.tagName&&"src"===r&&!this.keepIframeSrcFn(n)){if(s$(t))return;r="rr_src"}if(i||(i={node:e.target,attributes:{},styleDiff:{},_unchangedStyles:{}},this.attributes.push(i),this.attributeMap.set(e.target,i)),"type"===r&&"INPUT"===t.tagName&&"password"===(e.oldValue||"").toLowerCase()&&t.setAttribute("data-rr-is-password","true"),!so(t.tagName,r)&&(i.attributes[r]=ss(this.doc,aX(t.tagName),aX(r),n,t,this.maskAttributeFn),"style"===r)){if(!this.unattachedDoc)try{this.unattachedDoc=document.implementation.createHTMLDocument()}catch(e){this.unattachedDoc=this.doc}let r=this.unattachedDoc.createElement("span");for(let n of(e.oldValue&&r.setAttribute("style",e.oldValue),Array.from(t.style))){let e=t.style.getPropertyValue(n),a=t.style.getPropertyPriority(n);e!==r.style.getPropertyValue(n)||a!==r.style.getPropertyPriority(n)?""===a?i.styleDiff[n]=e:i.styleDiff[n]=[e,a]:i._unchangedStyles[n]=[e,a]}for(let e of Array.from(r.style))""===t.style.getPropertyValue(e)&&(i.styleDiff[e]=!1)}break}case"childList":if(sR(e.target,this.blockClass,this.blockSelector,this.unblockSelector,!0))return;e.addedNodes.forEach(t=>this.genAdds(t,e.target)),e.removedNodes.forEach(t=>{let r=this.mirror.getId(t),n=aF(e.target)?this.mirror.getId(e.target.host):this.mirror.getId(e.target);sR(e.target,this.blockClass,this.blockSelector,this.unblockSelector,!1)||sP(t,this.mirror)||-1===this.mirror.getId(t)||(this.addedSet.has(t)?(sz(this.addedSet,t),this.droppedSet.add(t)):this.addedSet.has(e.target)&&-1===r||function e(t,r){if(aF(t))return!1;let n=r.getId(t);return!r.has(n)||(!t.parentNode||t.parentNode.nodeType!==t.DOCUMENT_NODE)&&(!t.parentNode||e(t.parentNode,r))}(e.target,this.mirror)||(this.movedSet.has(t)&&this.movedMap[sW(r,n)]?sz(this.movedSet,t):this.removes.push({parentId:n,id:r,isShadow:!!(aF(e.target)&&aB(e.target))||void 0})),this.mapRemoves.push(t))})}},this.genAdds=(e,t)=>{if(!this.processedNodeManager.inOtherBuffer(e,this)&&!(this.addedSet.has(e)||this.movedSet.has(e))){if(this.mirror.hasNode(e)){if(sP(e,this.mirror))return;this.movedSet.add(e);let r=null;t&&this.mirror.hasNode(t)&&(r=this.mirror.getId(t)),r&&-1!==r&&(this.movedMap[sW(this.mirror.getId(e),r)]=!0)}else this.addedSet.add(e),this.droppedSet.delete(e);!sR(e,this.blockClass,this.blockSelector,this.unblockSelector,!1)&&(e.childNodes.forEach(e=>this.genAdds(e)),sI(e)&&e.shadowRoot.childNodes.forEach(t=>{this.processedNodeManager.add(t,this),this.genAdds(t,e)}))}}}init(e){["mutationCb","blockClass","blockSelector","unblockSelector","maskAllText","maskTextClass","unmaskTextClass","maskTextSelector","unmaskTextSelector","inlineStylesheet","maskInputOptions","maskAttributeFn","maskTextFn","maskInputFn","keepIframeSrcFn","recordCanvas","inlineImages","slimDOMOptions","dataURLOptions","doc","mirror","iframeManager","stylesheetManager","shadowDomManager","canvasManager","processedNodeManager"].forEach(t=>{this[t]=e[t]})}freeze(){this.frozen=!0,this.canvasManager.freeze()}unfreeze(){this.frozen=!1,this.canvasManager.unfreeze(),this.emit()}isFrozen(){return this.frozen}lock(){this.locked=!0,this.canvasManager.lock()}unlock(){this.locked=!1,this.canvasManager.unlock(),this.emit()}reset(){this.shadowDomManager.reset(),this.canvasManager.reset()}}function sz(e,t){e.delete(t),t.childNodes.forEach(t=>sz(e,t))}function sX(e,t,r){return 0!==e.length&&function(e,t,r){let n=t.parentNode;for(;n;){let t=r.getId(n);if(e.some(e=>e.id===t))return!0;n=n.parentNode}return!1}(e,t,r)}function sG(e,t){return 0!==e.size&&function e(t,r){let{parentNode:n}=r;return!!n&&(!!t.has(n)||e(t,n))}(e,t)}let sV=e=>w?(...t)=>{try{return e(...t)}catch(e){if(w&&!0===w(e))return()=>{};throw e}}:e,sY=[];function sK(e){try{if("composedPath"in e){let t=e.composedPath();if(t.length)return t[0]}else if("path"in e&&e.path.length)return e.path[0]}catch{}return e&&e.target}function sJ(e,t){let r=new sq;sY.push(r),r.init(e);let n=window.MutationObserver||window.__rrMutationObserver,i=window?.Zone?.__symbol__?.("MutationObserver");i&&window[i]&&(n=window[i]);let a=new n(sV(t=>{e.onMutation&&!1===e.onMutation(t)||r.processMutations.bind(r)(t)}));return a.observe(t,{attributes:!0,attributeOldValue:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0}),a}function sQ({scrollCb:e,doc:t,mirror:r,blockClass:n,blockSelector:i,unblockSelector:a,sampling:s}){return sf("scroll",sV(s_(sV(s=>{let o=sK(s);if(!o||sR(o,n,i,a,!0))return;let l=r.getId(o);if(o===t&&t.defaultView){let r=sb(t.defaultView);e({id:l,x:r.left,y:r.top})}else e({id:l,x:o.scrollLeft,y:o.scrollTop})}),s.scroll||100)),t)}let sZ=["INPUT","TEXTAREA","SELECT"],s0=new WeakMap;function s1(e){var t=[];if(s5("CSSGroupingRule")&&e.parentRule instanceof CSSGroupingRule||s5("CSSMediaRule")&&e.parentRule instanceof CSSMediaRule||s5("CSSSupportsRule")&&e.parentRule instanceof CSSSupportsRule||s5("CSSConditionRule")&&e.parentRule instanceof CSSConditionRule){let r=Array.from(e.parentRule.cssRules).indexOf(e);t.unshift(r)}else if(e.parentStyleSheet){let r=Array.from(e.parentStyleSheet.cssRules).indexOf(e);t.unshift(r)}return t}function s2(e,t,r){let n,i;return e?(e.ownerNode?n=t.getId(e.ownerNode):i=r.getId(e),{styleId:i,id:n}):{}}function s3({mirror:e,stylesheetManager:t},r){let n=null;n="#document"===r.nodeName?e.getId(r):e.getId(r.host);let i="#document"===r.nodeName?r.defaultView?.Document:r.ownerDocument?.defaultView?.ShadowRoot,a=i?.prototype?Object.getOwnPropertyDescriptor(i?.prototype,"adoptedStyleSheets"):void 0;return null!==n&&-1!==n&&i&&a?(Object.defineProperty(r,"adoptedStyleSheets",{configurable:a.configurable,enumerable:a.enumerable,get(){return a.get?.call(this)},set(e){let r=a.set?.call(this,e);if(null!==n&&-1!==n)try{t.adoptStyleSheets(e,n)}catch(e){}return r}}),sV(()=>{Object.defineProperty(r,"adoptedStyleSheets",{configurable:a.configurable,enumerable:a.enumerable,get:a.get,set:a.set})})):()=>{}}function s8(e,t={}){let r,n=e.doc.defaultView;if(!n)return()=>{};e.recordDOM&&(r=sJ(e,e.doc));let i=function({mousemoveCb:e,sampling:t,doc:r,mirror:n}){let i;if(!1===t.mousemove)return()=>{};let a="number"==typeof t.mousemove?t.mousemove:50,s="number"==typeof t.mousemoveCallback?t.mousemoveCallback:500,o=[],l=s_(sV(t=>{let r=Date.now()-i;e(o.map(e=>(e.timeOffset-=r,e)),t),o=[],i=null}),s),u=sV(s_(sV(e=>{let t=sK(e),{clientX:r,clientY:a}=sT(e)?e.changedTouches[0]:e;i||(i=sv()),o.push({x:r,y:a,id:n.getId(t),timeOffset:sv()-i}),l("undefined"!=typeof DragEvent&&e instanceof DragEvent?sD.Drag:e instanceof MouseEvent?sD.MouseMove:sD.TouchMove)}),a,{trailing:!1})),c=[sf("mousemove",u,r),sf("touchmove",u,r),sf("drag",u,r)];return sV(()=>{c.forEach(e=>e())})}(e),a=function({mouseInteractionCb:e,doc:t,mirror:r,blockClass:n,blockSelector:i,unblockSelector:a,sampling:s}){if(!1===s.mouseInteraction)return()=>{};let o=!0===s.mouseInteraction||void 0===s.mouseInteraction?{}:s.mouseInteraction,l=[],u=null,c=t=>s=>{let o=sK(s);if(sR(o,n,i,a,!0))return;let l=null,c=t;if("pointerType"in s){switch(s.pointerType){case"mouse":l=sF.Mouse;break;case"touch":l=sF.Touch;break;case"pen":l=sF.Pen}l===sF.Touch?sU[t]===sU.MouseDown?c="TouchStart":sU[t]===sU.MouseUp&&(c="TouchEnd"):sF.Pen}else sT(s)&&(l=sF.Touch);null!==l?(u=l,(c.startsWith("Touch")&&l===sF.Touch||c.startsWith("Mouse")&&l===sF.Mouse)&&(l=null)):sU[t]===sU.Click&&(l=u,u=null);let d=sT(s)?s.changedTouches[0]:s;if(!d)return;let p=r.getId(o),{clientX:h,clientY:f}=d;sV(e)({type:sU[c],id:p,x:h,y:f,...null!==l&&{pointerType:l}})};return Object.keys(sU).filter(e=>Number.isNaN(Number(e))&&!e.endsWith("_Departed")&&!1!==o[e]).forEach(e=>{let r=aX(e),n=c(e);if(window.PointerEvent)switch(sU[e]){case sU.MouseDown:case sU.MouseUp:r=r.replace("mouse","pointer");break;case sU.TouchStart:case sU.TouchEnd:return}l.push(sf(r,n,t))}),sV(()=>{l.forEach(e=>e())})}(e),s=sQ(e),o=function({viewportResizeCb:e},{win:t}){let r=-1,n=-1;return sf("resize",sV(s_(sV(()=>{let t=sE(),i=sS();(r!==t||n!==i)&&(e({width:Number(i),height:Number(t)}),r=t,n=i)}),200)),t)}(e,{win:n}),l=function({inputCb:e,doc:t,mirror:r,blockClass:n,blockSelector:i,unblockSelector:a,ignoreClass:s,ignoreSelector:o,maskInputOptions:l,maskInputFn:u,sampling:c,userTriggeredOnInput:d,maskTextClass:p,unmaskTextClass:h,maskTextSelector:f,unmaskTextSelector:m}){function g(e){let r=sK(e),c=e.isTrusted,g=r&&aG(r.tagName);if("OPTION"===g&&(r=r.parentElement),!r||!g||0>sZ.indexOf(g)||sR(r,n,i,a,!0))return;let y=r;if(y.classList.contains(s)||o&&y.matches(o))return;let v=aY(r),b=aK(y,g,v),E=!1,S=aq({maskInputOptions:l,tagName:g,type:v}),w=sd(r,p,f,h,m,S);("radio"===v||"checkbox"===v)&&(E=r.checked),b=az({isMasked:w,element:r,value:b,maskInputFn:u}),_(r,d?{text:b,isChecked:E,userTriggered:c}:{text:b,isChecked:E});let R=r.name;"radio"===v&&R&&E&&t.querySelectorAll(`input[type="radio"][name="${R}"]`).forEach(e=>{if(e!==r){let t=az({isMasked:w,element:e,value:aK(e,g,v),maskInputFn:u});_(e,d?{text:t,isChecked:!E,userTriggered:!1}:{text:t,isChecked:!E})}})}function _(t,n){let i=s0.get(t);if(!i||i.text!==n.text||i.isChecked!==n.isChecked){s0.set(t,n);let i=r.getId(t);sV(e)({...n,id:i})}}let y=("last"===c.input?["change"]:["input","change"]).map(e=>sf(e,sV(g),t)),v=t.defaultView;if(!v)return()=>{y.forEach(e=>e())};let b=v.Object.getOwnPropertyDescriptor(v.HTMLInputElement.prototype,"value"),E=[[v.HTMLInputElement.prototype,"value"],[v.HTMLInputElement.prototype,"checked"],[v.HTMLSelectElement.prototype,"value"],[v.HTMLTextAreaElement.prototype,"value"],[v.HTMLSelectElement.prototype,"selectedIndex"],[v.HTMLOptionElement.prototype,"selected"]];return b&&b.set&&y.push(...E.map(e=>(function e(t,r,n,i,a=window){let s=a.Object.getOwnPropertyDescriptor(t,r);return a.Object.defineProperty(t,r,i?n:{set(e){sj(()=>{n.set.call(this,e)},0),s&&s.set&&s.set.call(this,e)}}),()=>e(t,r,s||{},!0)})(e[0],e[1],{set(){sV(g)({target:this,isTrusted:!1})}},!1,v))),sV(()=>{y.forEach(e=>e())})}(e),u=function({mediaInteractionCb:e,blockClass:t,blockSelector:r,unblockSelector:n,mirror:i,sampling:a,doc:s}){let o=sV(s=>s_(sV(a=>{let o=sK(a);if(!o||sR(o,t,r,n,!0))return;let{currentTime:l,volume:u,muted:c,playbackRate:d}=o;e({type:s,id:i.getId(o),currentTime:l,volume:u,muted:c,playbackRate:d})}),a.media||500)),l=[sf("play",o(sB.Play),s),sf("pause",o(sB.Pause),s),sf("seeked",o(sB.Seeked),s),sf("volumechange",o(sB.VolumeChange),s),sf("ratechange",o(sB.RateChange),s)];return sV(()=>{l.forEach(e=>e())})}(e),c=()=>{},d=()=>{},p=()=>{},h=()=>{};e.recordDOM&&(c=function({styleSheetRuleCb:e,mirror:t,stylesheetManager:r},{win:n}){let i,a;if(!n.CSSStyleSheet||!n.CSSStyleSheet.prototype)return()=>{};let s=n.CSSStyleSheet.prototype.insertRule;n.CSSStyleSheet.prototype.insertRule=new Proxy(s,{apply:sV((n,i,a)=>{let[s,o]=a,{id:l,styleId:u}=s2(i,t,r.styleMirror);return(l&&-1!==l||u&&-1!==u)&&e({id:l,styleId:u,adds:[{rule:s,index:o}]}),n.apply(i,a)})});let o=n.CSSStyleSheet.prototype.deleteRule;n.CSSStyleSheet.prototype.deleteRule=new Proxy(o,{apply:sV((n,i,a)=>{let[s]=a,{id:o,styleId:l}=s2(i,t,r.styleMirror);return(o&&-1!==o||l&&-1!==l)&&e({id:o,styleId:l,removes:[{index:s}]}),n.apply(i,a)})}),n.CSSStyleSheet.prototype.replace&&(i=n.CSSStyleSheet.prototype.replace,n.CSSStyleSheet.prototype.replace=new Proxy(i,{apply:sV((n,i,a)=>{let[s]=a,{id:o,styleId:l}=s2(i,t,r.styleMirror);return(o&&-1!==o||l&&-1!==l)&&e({id:o,styleId:l,replace:s}),n.apply(i,a)})})),n.CSSStyleSheet.prototype.replaceSync&&(a=n.CSSStyleSheet.prototype.replaceSync,n.CSSStyleSheet.prototype.replaceSync=new Proxy(a,{apply:sV((n,i,a)=>{let[s]=a,{id:o,styleId:l}=s2(i,t,r.styleMirror);return(o&&-1!==o||l&&-1!==l)&&e({id:o,styleId:l,replaceSync:s}),n.apply(i,a)})}));let l={};s4("CSSGroupingRule")?l.CSSGroupingRule=n.CSSGroupingRule:(s4("CSSMediaRule")&&(l.CSSMediaRule=n.CSSMediaRule),s4("CSSConditionRule")&&(l.CSSConditionRule=n.CSSConditionRule),s4("CSSSupportsRule")&&(l.CSSSupportsRule=n.CSSSupportsRule));let u={};return Object.entries(l).forEach(([n,i])=>{u[n]={insertRule:i.prototype.insertRule,deleteRule:i.prototype.deleteRule},i.prototype.insertRule=new Proxy(u[n].insertRule,{apply:sV((n,i,a)=>{let[s,o]=a,{id:l,styleId:u}=s2(i.parentStyleSheet,t,r.styleMirror);return(l&&-1!==l||u&&-1!==u)&&e({id:l,styleId:u,adds:[{rule:s,index:[...s1(i),o||0]}]}),n.apply(i,a)})}),i.prototype.deleteRule=new Proxy(u[n].deleteRule,{apply:sV((n,i,a)=>{let[s]=a,{id:o,styleId:l}=s2(i.parentStyleSheet,t,r.styleMirror);return(o&&-1!==o||l&&-1!==l)&&e({id:o,styleId:l,removes:[{index:[...s1(i),s]}]}),n.apply(i,a)})})}),sV(()=>{n.CSSStyleSheet.prototype.insertRule=s,n.CSSStyleSheet.prototype.deleteRule=o,i&&(n.CSSStyleSheet.prototype.replace=i),a&&(n.CSSStyleSheet.prototype.replaceSync=a),Object.entries(l).forEach(([e,t])=>{t.prototype.insertRule=u[e].insertRule,t.prototype.deleteRule=u[e].deleteRule})})}(e,{win:n}),d=s3(e,e.doc),p=function({styleDeclarationCb:e,mirror:t,ignoreCSSAttributes:r,stylesheetManager:n},{win:i}){let a=i.CSSStyleDeclaration.prototype.setProperty;i.CSSStyleDeclaration.prototype.setProperty=new Proxy(a,{apply:sV((i,s,o)=>{let[l,u,c]=o;if(r.has(l))return a.apply(s,[l,u,c]);let{id:d,styleId:p}=s2(s.parentRule?.parentStyleSheet,t,n.styleMirror);return(d&&-1!==d||p&&-1!==p)&&e({id:d,styleId:p,set:{property:l,value:u,priority:c},index:s1(s.parentRule)}),i.apply(s,o)})});let s=i.CSSStyleDeclaration.prototype.removeProperty;return i.CSSStyleDeclaration.prototype.removeProperty=new Proxy(s,{apply:sV((i,a,o)=>{let[l]=o;if(r.has(l))return s.apply(a,[l]);let{id:u,styleId:c}=s2(a.parentRule?.parentStyleSheet,t,n.styleMirror);return(u&&-1!==u||c&&-1!==c)&&e({id:u,styleId:c,remove:{property:l},index:s1(a.parentRule)}),i.apply(a,o)})}),sV(()=>{i.CSSStyleDeclaration.prototype.setProperty=a,i.CSSStyleDeclaration.prototype.removeProperty=s})}(e,{win:n}),e.collectFonts&&(h=function({fontCb:e,doc:t}){let r=t.defaultView;if(!r)return()=>{};let n=[],i=new WeakMap,a=r.FontFace;r.FontFace=function(e,t,r){let n=new a(e,t,r);return i.set(n,{family:e,buffer:"string"!=typeof t,descriptors:r,fontSource:"string"==typeof t?t:JSON.stringify(Array.from(new Uint8Array(t)))}),n};let s=sy(t.fonts,"add",function(t){return function(r){return sj(sV(()=>{let t=i.get(r);t&&(e(t),i.delete(r))}),0),t.apply(this,[r])}});return n.push(()=>{r.FontFace=a}),n.push(s),sV(()=>{n.forEach(e=>e())})}(e)));let f=function(e){let{doc:t,mirror:r,blockClass:n,blockSelector:i,unblockSelector:a,selectionCb:s}=e,o=!0,l=sV(()=>{let e=t.getSelection();if(!e||o&&e?.isCollapsed)return;o=e.isCollapsed||!1;let l=[],u=e.rangeCount||0;for(let t=0;t<u;t++){let{startContainer:s,startOffset:o,endContainer:u,endOffset:c}=e.getRangeAt(t);sR(s,n,i,a,!0)||sR(u,n,i,a,!0)||l.push({start:r.getId(s),startOffset:o,end:r.getId(u),endOffset:c})}s({ranges:l})});return l(),sf("selectionchange",l)}(e),m=function({doc:e,customElementCb:t}){let r=e.defaultView;return r&&r.customElements?sy(r.customElements,"define",function(e){return function(r,n,i){try{t({define:{name:r}})}catch(e){}return e.apply(this,[r,n,i])}}):()=>{}}(e),g=[];for(let t of e.plugins)g.push(t.observer(t.callback,n,t.options));return sV(()=>{sY.forEach(e=>e.reset()),r?.disconnect(),i(),a(),s(),o(),l(),u(),c(),d(),p(),h(),f(),m(),g.forEach(e=>e())})}function s5(e){return void 0!==window[e]}function s4(e){return!!(void 0!==window[e]&&window[e].prototype&&"insertRule"in window[e].prototype&&"deleteRule"in window[e].prototype)}class s6{constructor(e){this.generateIdFn=e,this.iframeIdToRemoteIdMap=new WeakMap,this.iframeRemoteIdToIdMap=new WeakMap}getId(e,t,r,n){let i=r||this.getIdToRemoteIdMap(e),a=n||this.getRemoteIdToIdMap(e),s=i.get(t);return s||(s=this.generateIdFn(),i.set(t,s),a.set(s,t)),s}getIds(e,t){let r=this.getIdToRemoteIdMap(e),n=this.getRemoteIdToIdMap(e);return t.map(t=>this.getId(e,t,r,n))}getRemoteId(e,t,r){let n=r||this.getRemoteIdToIdMap(e);if("number"!=typeof t)return t;let i=n.get(t);return i||-1}getRemoteIds(e,t){let r=this.getRemoteIdToIdMap(e);return t.map(t=>this.getRemoteId(e,t,r))}reset(e){if(!e){this.iframeIdToRemoteIdMap=new WeakMap,this.iframeRemoteIdToIdMap=new WeakMap;return}this.iframeIdToRemoteIdMap.delete(e),this.iframeRemoteIdToIdMap.delete(e)}getIdToRemoteIdMap(e){let t=this.iframeIdToRemoteIdMap.get(e);return t||(t=new Map,this.iframeIdToRemoteIdMap.set(e,t)),t}getRemoteIdToIdMap(e){let t=this.iframeRemoteIdToIdMap.get(e);return t||(t=new Map,this.iframeRemoteIdToIdMap.set(e,t)),t}}class s7{constructor(){this.crossOriginIframeMirror=new s6(a5),this.crossOriginIframeRootIdMap=new WeakMap}addIframe(){}addLoadListener(){}attachIframe(){}}class s9{constructor(e){this.iframes=new WeakMap,this.crossOriginIframeMap=new WeakMap,this.crossOriginIframeMirror=new s6(a5),this.crossOriginIframeRootIdMap=new WeakMap,this.mutationCb=e.mutationCb,this.wrappedEmit=e.wrappedEmit,this.stylesheetManager=e.stylesheetManager,this.recordCrossOriginIframes=e.recordCrossOriginIframes,this.crossOriginIframeStyleMirror=new s6(this.stylesheetManager.styleMirror.generateId.bind(this.stylesheetManager.styleMirror)),this.mirror=e.mirror,this.recordCrossOriginIframes&&window.addEventListener("message",this.handleMessage.bind(this))}addIframe(e){this.iframes.set(e,!0),e.contentWindow&&this.crossOriginIframeMap.set(e.contentWindow,e)}addLoadListener(e){this.loadListener=e}attachIframe(e,t){this.mutationCb({adds:[{parentId:this.mirror.getId(e),nextId:null,node:t}],removes:[],texts:[],attributes:[],isAttachIframe:!0}),this.recordCrossOriginIframes&&e.contentWindow?.addEventListener("message",this.handleMessage.bind(this)),this.loadListener?.(e);let r=s$(e);r&&r.adoptedStyleSheets&&r.adoptedStyleSheets.length>0&&this.stylesheetManager.adoptStyleSheets(r.adoptedStyleSheets,this.mirror.getId(r))}handleMessage(e){if("rrweb"!==e.data.type||e.origin!==e.data.origin||!e.source)return;let t=this.crossOriginIframeMap.get(e.source);if(!t)return;let r=this.transformCrossOriginEvent(t,e.data.event);r&&this.wrappedEmit(r,e.data.isCheckout)}transformCrossOriginEvent(e,t){switch(t.type){case sL.FullSnapshot:{this.crossOriginIframeMirror.reset(e),this.crossOriginIframeStyleMirror.reset(e),this.replaceIdOnNode(t.data.node,e);let r=t.data.node.id;return this.crossOriginIframeRootIdMap.set(e,r),this.patchRootIdOnNode(t.data.node,r),{timestamp:t.timestamp,type:sL.IncrementalSnapshot,data:{source:sD.Mutation,adds:[{parentId:this.mirror.getId(e),nextId:null,node:t.data.node}],removes:[],texts:[],attributes:[],isAttachIframe:!0}}}case sL.Meta:case sL.Load:case sL.DomContentLoaded:break;case sL.Plugin:return t;case sL.Custom:return this.replaceIds(t.data.payload,e,["id","parentId","previousId","nextId"]),t;case sL.IncrementalSnapshot:switch(t.data.source){case sD.Mutation:return t.data.adds.forEach(t=>{this.replaceIds(t,e,["parentId","nextId","previousId"]),this.replaceIdOnNode(t.node,e);let r=this.crossOriginIframeRootIdMap.get(e);r&&this.patchRootIdOnNode(t.node,r)}),t.data.removes.forEach(t=>{this.replaceIds(t,e,["parentId","id"])}),t.data.attributes.forEach(t=>{this.replaceIds(t,e,["id"])}),t.data.texts.forEach(t=>{this.replaceIds(t,e,["id"])}),t;case sD.Drag:case sD.TouchMove:case sD.MouseMove:return t.data.positions.forEach(t=>{this.replaceIds(t,e,["id"])}),t;case sD.ViewportResize:return!1;case sD.MediaInteraction:case sD.MouseInteraction:case sD.Scroll:case sD.CanvasMutation:case sD.Input:return this.replaceIds(t.data,e,["id"]),t;case sD.StyleSheetRule:case sD.StyleDeclaration:return this.replaceIds(t.data,e,["id"]),this.replaceStyleIds(t.data,e,["styleId"]),t;case sD.Font:return t;case sD.Selection:return t.data.ranges.forEach(t=>{this.replaceIds(t,e,["start","end"])}),t;case sD.AdoptedStyleSheet:return this.replaceIds(t.data,e,["id"]),this.replaceStyleIds(t.data,e,["styleIds"]),t.data.styles?.forEach(t=>{this.replaceStyleIds(t,e,["styleId"])}),t}}return!1}replace(e,t,r,n){for(let i of n)(Array.isArray(t[i])||"number"==typeof t[i])&&(Array.isArray(t[i])?t[i]=e.getIds(r,t[i]):t[i]=e.getId(r,t[i]));return t}replaceIds(e,t,r){return this.replace(this.crossOriginIframeMirror,e,t,r)}replaceStyleIds(e,t,r){return this.replace(this.crossOriginIframeStyleMirror,e,t,r)}replaceIdOnNode(e,t){this.replaceIds(e,t,["id","rootId"]),"childNodes"in e&&e.childNodes.forEach(e=>{this.replaceIdOnNode(e,t)})}patchRootIdOnNode(e,t){e.type===aU.Document||e.rootId||(e.rootId=t),"childNodes"in e&&e.childNodes.forEach(e=>{this.patchRootIdOnNode(e,t)})}}class oe{init(){}addShadowRoot(){}observeAttachShadow(){}reset(){}}class ot{constructor(e){this.shadowDoms=new WeakSet,this.restoreHandlers=[],this.mutationCb=e.mutationCb,this.scrollCb=e.scrollCb,this.bypassOptions=e.bypassOptions,this.mirror=e.mirror,this.init()}init(){this.reset(),this.patchAttachShadow(Element,document)}addShadowRoot(e,t){if(!aB(e)||this.shadowDoms.has(e))return;this.shadowDoms.add(e),this.bypassOptions.canvasManager.addShadowRoot(e);let r=sJ({...this.bypassOptions,doc:t,mutationCb:this.mutationCb,mirror:this.mirror,shadowDomManager:this},e);this.restoreHandlers.push(()=>r.disconnect()),this.restoreHandlers.push(sQ({...this.bypassOptions,scrollCb:this.scrollCb,doc:e,mirror:this.mirror})),sj(()=>{e.adoptedStyleSheets&&e.adoptedStyleSheets.length>0&&this.bypassOptions.stylesheetManager.adoptStyleSheets(e.adoptedStyleSheets,this.mirror.getId(e.host)),this.restoreHandlers.push(s3({mirror:this.mirror,stylesheetManager:this.bypassOptions.stylesheetManager},e))},0)}observeAttachShadow(e){let t=s$(e),r=function(e){try{return e.contentWindow}catch(e){}}(e);t&&r&&this.patchAttachShadow(r.Element,t)}patchAttachShadow(e,t){let r=this;this.restoreHandlers.push(sy(e.prototype,"attachShadow",function(e){return function(n){let i=e.call(this,n);return this.shadowRoot&&sk(this)&&r.addShadowRoot(this.shadowRoot,t),i}}))}reset(){this.restoreHandlers.forEach(e=>{try{e()}catch(e){}}),this.restoreHandlers=[],this.shadowDoms=new WeakSet,this.bypassOptions.canvasManager.resetShadowRoots()}}for(var or="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",on="undefined"==typeof Uint8Array?[]:new Uint8Array(256),oi=0;oi<or.length;oi++)on[or.charCodeAt(oi)]=oi;class oa{reset(){}freeze(){}unfreeze(){}lock(){}unlock(){}snapshot(){}addWindow(){}addShadowRoot(){}resetShadowRoots(){}}class os{constructor(e){this.trackedLinkElements=new WeakSet,this.styleMirror=new sC,this.mutationCb=e.mutationCb,this.adoptedStyleSheetCb=e.adoptedStyleSheetCb}attachLinkElement(e,t){"_cssText"in t.attributes&&this.mutationCb({adds:[],removes:[],texts:[],attributes:[{id:t.id,attributes:t.attributes}]}),this.trackLinkElement(e)}trackLinkElement(e){this.trackedLinkElements.has(e)||(this.trackedLinkElements.add(e),this.trackStylesheetInLinkElement(e))}adoptStyleSheets(e,t){if(0===e.length)return;let r={id:t,styleIds:[]},n=[];for(let t of e){let e;this.styleMirror.has(t)?e=this.styleMirror.getId(t):(e=this.styleMirror.add(t),n.push({styleId:e,rules:Array.from(t.rules||CSSRule,(e,t)=>({rule:aH(e),index:t}))})),r.styleIds.push(e)}n.length>0&&(r.styles=n),this.adoptedStyleSheetCb(r)}reset(){this.styleMirror.reset(),this.trackedLinkElements=new WeakSet}trackStylesheetInLinkElement(e){}}class oo{constructor(){this.nodeMap=new WeakMap,this.active=!1}inOtherBuffer(e,t){let r=this.nodeMap.get(e);return r&&Array.from(r).some(e=>e!==t)}add(e,t){this.active||(this.active=!0,function(...e){sN("requestAnimationFrame")(...e)}(()=>{this.nodeMap=new WeakMap,this.active=!1})),this.nodeMap.set(e,(this.nodeMap.get(e)||new Set).add(t))}destroy(){}}try{if(2!==Array.from([1],e=>2*e)[0]){let e=document.createElement("iframe");document.body.appendChild(e),Array.from=e.contentWindow?.Array.from||Array.from,document.body.removeChild(e)}}catch(e){console.debug("Unable to override Array.from",e)}let ol=new aW;function ou(e={}){let t,{emit:r,checkoutEveryNms:n,checkoutEveryNth:i,blockClass:a="rr-block",blockSelector:s=null,unblockSelector:o=null,ignoreClass:l="rr-ignore",ignoreSelector:u=null,maskAllText:c=!1,maskTextClass:d="rr-mask",unmaskTextClass:p=null,maskTextSelector:h=null,unmaskTextSelector:f=null,inlineStylesheet:m=!0,maskAllInputs:g,maskInputOptions:_,slimDOMOptions:y,maskAttributeFn:v,maskInputFn:b,maskTextFn:E,maxCanvasSize:S=null,packFn:T,sampling:x={},dataURLOptions:O={},mousemoveWait:I,recordDOM:C=!0,recordCanvas:M=!1,recordCrossOriginIframes:k=!1,recordAfter:A="DOMContentLoaded"===e.recordAfter?e.recordAfter:"load",userTriggeredOnInput:N=!1,collectFonts:j=!1,inlineImages:L=!1,plugins:D,keepIframeSrcFn:U=()=>!1,ignoreCSSAttributes:F=new Set([]),errorHandler:B,onMutation:$,getCanvasManager:H}=e;w=B;let W=!k||window.parent===window,q=!1;if(!W)try{window.parent.document&&(q=!1)}catch(e){q=!0}if(W&&!r)throw Error("emit function is required");if(!W&&!q)return()=>{};void 0!==I&&void 0===x.mousemove&&(x.mousemove=I),ol.reset();let z=!0===g?{color:!0,date:!0,"datetime-local":!0,email:!0,month:!0,number:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0,textarea:!0,select:!0,radio:!0,checkbox:!0}:void 0!==_?_:{},X=!0===y||"all"===y?{script:!0,comment:!0,headFavicon:!0,headWhitespace:!0,headMetaSocial:!0,headMetaRobots:!0,headMetaHttpEquiv:!0,headMetaVerification:!0,headMetaAuthorship:"all"===y,headMetaDescKeywords:"all"===y}:y||{};!function(e=window){"NodeList"in e&&!e.NodeList.prototype.forEach&&(e.NodeList.prototype.forEach=Array.prototype.forEach),"DOMTokenList"in e&&!e.DOMTokenList.prototype.forEach&&(e.DOMTokenList.prototype.forEach=Array.prototype.forEach),Node.prototype.contains||(Node.prototype.contains=(...e)=>{let t=e[0];if(!(0 in e))throw TypeError("1 argument is required");do if(this===t)return!0;while(t=t&&t.parentNode);return!1})}();let G=0,V=e=>{for(let t of D||[])t.eventProcessor&&(e=t.eventProcessor(e));return T&&!q&&(e=T(e)),e};R=(e,a)=>{if(e.timestamp=sv(),sY[0]?.isFrozen()&&e.type!==sL.FullSnapshot&&(e.type!==sL.IncrementalSnapshot||e.data.source!==sD.Mutation)&&sY.forEach(e=>e.unfreeze()),W)r?.(V(e),a);else if(q){let t={type:"rrweb",event:V(e),origin:window.location.origin,isCheckout:a};window.parent.postMessage(t,"*")}if(e.type===sL.FullSnapshot)t=e,G=0;else if(e.type===sL.IncrementalSnapshot){if(e.data.source===sD.Mutation&&e.data.isAttachIframe)return;G++;let r=i&&G>=i,a=n&&t&&e.timestamp-t.timestamp>n;(r||a)&&en(!0)}};let Y=e=>{R({type:sL.IncrementalSnapshot,data:{source:sD.Mutation,...e}})},K=e=>R({type:sL.IncrementalSnapshot,data:{source:sD.Scroll,...e}}),J=e=>R({type:sL.IncrementalSnapshot,data:{source:sD.CanvasMutation,...e}}),Q=new os({mutationCb:Y,adoptedStyleSheetCb:e=>R({type:sL.IncrementalSnapshot,data:{source:sD.AdoptedStyleSheet,...e}})}),Z="boolean"==typeof __RRWEB_EXCLUDE_IFRAME__&&__RRWEB_EXCLUDE_IFRAME__?new s7:new s9({mirror:ol,mutationCb:Y,stylesheetManager:Q,recordCrossOriginIframes:k,wrappedEmit:R});for(let e of D||[])e.getMirror&&e.getMirror({nodeMirror:ol,crossOriginIframeMirror:Z.crossOriginIframeMirror,crossOriginIframeStyleMirror:Z.crossOriginIframeStyleMirror});let ee=new oo,et=function(e,t){try{return e?e(t):new oa}catch{return console.warn("Unable to initialize CanvasManager"),new oa}}(H,{mirror:ol,win:window,mutationCb:e=>R({type:sL.IncrementalSnapshot,data:{source:sD.CanvasMutation,...e}}),recordCanvas:M,blockClass:a,blockSelector:s,unblockSelector:o,maxCanvasSize:S,sampling:x.canvas,dataURLOptions:O,errorHandler:B}),er="boolean"==typeof __RRWEB_EXCLUDE_SHADOW_DOM__&&__RRWEB_EXCLUDE_SHADOW_DOM__?new oe:new ot({mutationCb:Y,scrollCb:K,bypassOptions:{onMutation:$,blockClass:a,blockSelector:s,unblockSelector:o,maskAllText:c,maskTextClass:d,unmaskTextClass:p,maskTextSelector:h,unmaskTextSelector:f,inlineStylesheet:m,maskInputOptions:z,dataURLOptions:O,maskAttributeFn:v,maskTextFn:E,maskInputFn:b,recordCanvas:M,inlineImages:L,sampling:x,slimDOMOptions:X,iframeManager:Z,stylesheetManager:Q,canvasManager:et,keepIframeSrcFn:U,processedNodeManager:ee},mirror:ol}),en=(e=!1)=>{if(!C)return;R({type:sL.Meta,data:{href:window.location.href,width:sS(),height:sE()}},e),Q.reset(),er.init(),sY.forEach(e=>e.lock());let t=function(e,t){let{mirror:r=new aW,blockClass:n="rr-block",blockSelector:i=null,unblockSelector:a=null,maskAllText:s=!1,maskTextClass:o="rr-mask",unmaskTextClass:l=null,maskTextSelector:u=null,unmaskTextSelector:c=null,inlineStylesheet:d=!0,inlineImages:p=!1,recordCanvas:h=!1,maskAllInputs:f=!1,maskAttributeFn:m,maskTextFn:g,maskInputFn:_,slimDOM:y=!1,dataURLOptions:v,preserveWhiteSpace:b,onSerialize:E,onIframeLoad:S,iframeLoadTimeout:w,onStylesheetLoad:R,stylesheetLoadTimeout:P,keepIframeSrcFn:T=()=>!1}=t||{};return sh(e,{doc:e,mirror:r,blockClass:n,blockSelector:i,unblockSelector:a,maskAllText:s,maskTextClass:o,unmaskTextClass:l,maskTextSelector:u,unmaskTextSelector:c,skipChild:!1,inlineStylesheet:d,maskInputOptions:!0===f?{color:!0,date:!0,"datetime-local":!0,email:!0,month:!0,number:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0,textarea:!0,select:!0}:!1===f?{}:f,maskAttributeFn:m,maskTextFn:g,maskInputFn:_,slimDOMOptions:!0===y||"all"===y?{script:!0,comment:!0,headFavicon:!0,headWhitespace:!0,headMetaDescKeywords:"all"===y,headMetaSocial:!0,headMetaRobots:!0,headMetaHttpEquiv:!0,headMetaAuthorship:!0,headMetaVerification:!0}:!1===y?{}:y,dataURLOptions:v,inlineImages:p,recordCanvas:h,preserveWhiteSpace:b,onSerialize:E,onIframeLoad:S,iframeLoadTimeout:w,onStylesheetLoad:R,stylesheetLoadTimeout:P,keepIframeSrcFn:T,newlyAddedElement:!1})}(document,{mirror:ol,blockClass:a,blockSelector:s,unblockSelector:o,maskAllText:c,maskTextClass:d,unmaskTextClass:p,maskTextSelector:h,unmaskTextSelector:f,inlineStylesheet:m,maskAllInputs:z,maskAttributeFn:v,maskInputFn:b,maskTextFn:E,slimDOM:X,dataURLOptions:O,recordCanvas:M,inlineImages:L,onSerialize:e=>{sx(e,ol)&&Z.addIframe(e),sO(e,ol)&&Q.trackLinkElement(e),sI(e)&&er.addShadowRoot(e.shadowRoot,document)},onIframeLoad:(e,t)=>{Z.attachIframe(e,t),e.contentWindow&&et.addWindow(e.contentWindow),er.observeAttachShadow(e)},onStylesheetLoad:(e,t)=>{Q.attachLinkElement(e,t)},keepIframeSrcFn:U});if(!t)return console.warn("Failed to snapshot the document");R({type:sL.FullSnapshot,data:{node:t,initialOffset:sb(window)}}),sY.forEach(e=>e.unlock()),document.adoptedStyleSheets&&document.adoptedStyleSheets.length>0&&Q.adoptStyleSheets(document.adoptedStyleSheets,ol.getId(document))};P=en;try{let e=[],t=e=>sV(s8)({onMutation:$,mutationCb:Y,mousemoveCb:(e,t)=>R({type:sL.IncrementalSnapshot,data:{source:t,positions:e}}),mouseInteractionCb:e=>R({type:sL.IncrementalSnapshot,data:{source:sD.MouseInteraction,...e}}),scrollCb:K,viewportResizeCb:e=>R({type:sL.IncrementalSnapshot,data:{source:sD.ViewportResize,...e}}),inputCb:e=>R({type:sL.IncrementalSnapshot,data:{source:sD.Input,...e}}),mediaInteractionCb:e=>R({type:sL.IncrementalSnapshot,data:{source:sD.MediaInteraction,...e}}),styleSheetRuleCb:e=>R({type:sL.IncrementalSnapshot,data:{source:sD.StyleSheetRule,...e}}),styleDeclarationCb:e=>R({type:sL.IncrementalSnapshot,data:{source:sD.StyleDeclaration,...e}}),canvasMutationCb:J,fontCb:e=>R({type:sL.IncrementalSnapshot,data:{source:sD.Font,...e}}),selectionCb:e=>{R({type:sL.IncrementalSnapshot,data:{source:sD.Selection,...e}})},customElementCb:e=>{R({type:sL.IncrementalSnapshot,data:{source:sD.CustomElement,...e}})},blockClass:a,ignoreClass:l,ignoreSelector:u,maskAllText:c,maskTextClass:d,unmaskTextClass:p,maskTextSelector:h,unmaskTextSelector:f,maskInputOptions:z,inlineStylesheet:m,sampling:x,recordDOM:C,recordCanvas:M,inlineImages:L,userTriggeredOnInput:N,collectFonts:j,doc:e,maskAttributeFn:v,maskInputFn:b,maskTextFn:E,keepIframeSrcFn:U,blockSelector:s,unblockSelector:o,slimDOMOptions:X,dataURLOptions:O,mirror:ol,iframeManager:Z,stylesheetManager:Q,shadowDomManager:er,processedNodeManager:ee,canvasManager:et,ignoreCSSAttributes:F,plugins:D?.filter(e=>e.observer)?.map(e=>({observer:e.observer,options:e.options,callback:t=>R({type:sL.Plugin,data:{plugin:e.name,payload:t}})}))||[]},{});Z.addLoadListener(r=>{try{e.push(t(r.contentDocument))}catch(e){console.warn(e)}});let r=()=>{en(),e.push(t(document))};return"interactive"===document.readyState||"complete"===document.readyState?r():(e.push(sf("DOMContentLoaded",()=>{R({type:sL.DomContentLoaded,data:{}}),"DOMContentLoaded"===A&&r()})),e.push(sf("load",()=>{R({type:sL.Load,data:{}}),"load"===A&&r()},window))),()=>{e.forEach(e=>e()),ee.destroy(),P=void 0,w=void 0}}catch(e){console.warn(e)}}function oc(e){return e>0x2540be3ff?e:1e3*e}function od(e){return e>0x2540be3ff?e/1e3:e}function op(e,t){"sentry.transaction"!==t.category&&(["ui.click","ui.input"].includes(t.category)?e.triggerUserActivity():e.checkAndHandleExpiredSession(),e.addUpdate(()=>(e.throttledAddEvent({type:sL.Custom,timestamp:1e3*(t.timestamp||0),data:{tag:"breadcrumb",payload:tj(t,10,1e3)}}),"console"===t.category)))}function oh(e){return e.closest("button,a")||e}function of(e){let t=om(e);return t&&t instanceof Element?oh(t):t}function om(e){var t;return"object"==typeof(t=e)&&t&&"target"in t?e.target:e}ou.mirror=ol,ou.takeFullSnapshot=function(e){if(!P)throw Error("please take full snapshot after start recording");P(e)},!function(e){e[e.NotStarted=0]="NotStarted",e[e.Running=1]="Running",e[e.Stopped=2]="Stopped"}(nS||(nS={}));let og=new Set([sD.Mutation,sD.StyleSheetRule,sD.StyleDeclaration,sD.AdoptedStyleSheet,sD.CanvasMutation,sD.Selection,sD.MediaInteraction]);class o_{constructor(e,t,r=op){this._lastMutation=0,this._lastScroll=0,this._clicks=[],this._timeout=t.timeout/1e3,this._threshold=t.threshold/1e3,this._scrollTimeout=t.scrollTimeout/1e3,this._replay=e,this._ignoreSelector=t.ignoreSelector,this._addBreadcrumbEvent=r}addListeners(){var e;let t=(e=()=>{this._lastMutation=ov()},T||(T=[],el(C,"open",function(e){return function(...t){if(T)try{T.forEach(e=>e())}catch{}return e.apply(C,t)}})),T.push(e),()=>{let t=T?T.indexOf(e):-1;t>-1&&T.splice(t,1)});this._teardown=()=>{t(),this._clicks=[],this._lastMutation=0,this._lastScroll=0}}removeListeners(){this._teardown&&this._teardown(),this._checkClickTimeout&&clearTimeout(this._checkClickTimeout)}handleClick(e,t){var r,n,i;if(r=t,n=this._ignoreSelector,!oy.includes(r.tagName)||"INPUT"===r.tagName&&!["submit","button"].includes(r.getAttribute("type")||"")||"A"===r.tagName&&(r.hasAttribute("download")||r.hasAttribute("target")&&"_self"!==r.getAttribute("target"))||n&&r.matches(n)||!((i=e).data&&"number"==typeof i.data.nodeId&&i.timestamp))return;let a={timestamp:od(e.timestamp),clickBreadcrumb:e,clickCount:0,node:t};this._clicks.some(e=>e.node===a.node&&1>Math.abs(e.timestamp-a.timestamp))||(this._clicks.push(a),1===this._clicks.length&&this._scheduleCheckClicks())}registerMutation(e=Date.now()){this._lastMutation=od(e)}registerScroll(e=Date.now()){this._lastScroll=od(e)}registerClick(e){let t=oh(e);this._handleMultiClick(t)}_handleMultiClick(e){this._getClicks(e).forEach(e=>{e.clickCount++})}_getClicks(e){return this._clicks.filter(t=>t.node===e)}_checkClicks(){let e=[],t=ov();for(let r of(this._clicks.forEach(r=>{!r.mutationAfter&&this._lastMutation&&(r.mutationAfter=r.timestamp<=this._lastMutation?this._lastMutation-r.timestamp:void 0),!r.scrollAfter&&this._lastScroll&&(r.scrollAfter=r.timestamp<=this._lastScroll?this._lastScroll-r.timestamp:void 0),r.timestamp+this._timeout<=t&&e.push(r)}),e)){let e=this._clicks.indexOf(r);e>-1&&(this._generateBreadcrumbs(r),this._clicks.splice(e,1))}this._clicks.length&&this._scheduleCheckClicks()}_generateBreadcrumbs(e){let t=this._replay,r=e.scrollAfter&&e.scrollAfter<=this._scrollTimeout,n=e.mutationAfter&&e.mutationAfter<=this._threshold,{clickCount:i,clickBreadcrumb:a}=e;if(!r&&!n){let r=1e3*Math.min(e.mutationAfter||this._timeout,this._timeout),n=r<1e3*this._timeout?"mutation":"timeout",s={type:"default",message:a.message,timestamp:a.timestamp,category:"ui.slowClickDetected",data:{...a.data,url:C.location.href,route:t.getCurrentRoute(),timeAfterClickMs:r,endReason:n,clickCount:i||1}};this._addBreadcrumbEvent(t,s);return}if(i>1){let e={type:"default",message:a.message,timestamp:a.timestamp,category:"ui.multiClick",data:{...a.data,url:C.location.href,route:t.getCurrentRoute(),clickCount:i,metric:!0}};this._addBreadcrumbEvent(t,e)}}_scheduleCheckClicks(){this._checkClickTimeout&&clearTimeout(this._checkClickTimeout),this._checkClickTimeout=nv(()=>this._checkClicks(),1e3)}}let oy=["A","BUTTON","INPUT"];function ov(){return Date.now()/1e3}function ob(e){return{timestamp:Date.now()/1e3,type:"default",...e}}var oE=(e=>(e[e.Document=0]="Document",e[e.DocumentType=1]="DocumentType",e[e.Element=2]="Element",e[e.Text=3]="Text",e[e.CDATA=4]="CDATA",e[e.Comment=5]="Comment",e))(oE||{});let oS=new Set(["id","class","aria-label","role","name","alt","title","data-test-id","data-testid","disabled","aria-disabled","data-sentry-component"]),ow=e=>t=>{var r,n;if(!e.isEnabled())return;let i=function(e){let{target:t,message:r}=function(e){let t,r="click"===e.name,n=null;try{n=r?of(e.event):om(e.event),t=er(n,{maxStringLength:200})||"<unknown>"}catch{t="<unknown>"}return{target:n,message:t}}(e);return ob({category:`ui.${e.name}`,...oR(t,r)})}(t);if(!i)return;let a="click"===t.name,s=a?t.event:void 0;a&&e.clickDetector&&s?.target&&!s.altKey&&!s.metaKey&&!s.ctrlKey&&!s.shiftKey&&(r=e.clickDetector,n=of(t.event),r.handleClick(i,n)),op(e,i)};function oR(e,t){let r=ou.mirror.getId(e),n=r&&ou.mirror.getNode(r),i=n&&ou.mirror.getMeta(n),a=i&&i.type===oE.Element?i:null;return{message:t,data:a?{nodeId:r,node:{id:r,tagName:a.tagName,textContent:Array.from(a.childNodes).map(e=>e.type===oE.Text&&e.textContent).filter(Boolean).map(e=>e.trim()).join(""),attributes:function(e){let t={};for(let r in!e["data-sentry-component"]&&e["data-sentry-element"]&&(e["data-sentry-component"]=e["data-sentry-element"]),e)if(oS.has(r)){let n=r;("data-testid"===r||"data-test-id"===r)&&(n="testId"),t[n]=e[r]}return t}(a.attributes)}}:{}}}let oP={resource:function(e){let{entryType:t,initiatorType:r,name:n,responseEnd:i,startTime:a,decodedBodySize:s,encodedBodySize:o,responseStatus:l,transferSize:u}=e;return["fetch","xmlhttprequest"].includes(r)?null:{type:`${t}.${r}`,start:oO(a),end:oO(i),name:n,data:{size:u,statusCode:l,decodedBodySize:s,encodedBodySize:o}}},paint:function(e){let{duration:t,entryType:r,name:n,startTime:i}=e,a=oO(i);return{type:r,name:n,start:a,end:a+t,data:void 0}},navigation:function(e){let{entryType:t,name:r,decodedBodySize:n,duration:i,domComplete:a,encodedBodySize:s,domContentLoadedEventStart:o,domContentLoadedEventEnd:l,domInteractive:u,loadEventStart:c,loadEventEnd:d,redirectCount:p,startTime:h,transferSize:f,type:m}=e;return 0===i?null:{type:`${t}.${m}`,start:oO(h),end:oO(a),name:r,data:{size:f,decodedBodySize:n,encodedBodySize:s,duration:i,domInteractive:u,domContentLoadedEventStart:o,domContentLoadedEventEnd:l,loadEventStart:c,loadEventEnd:d,domComplete:a,redirectCount:p}}}};function oT(e,t){return({metric:r})=>void t.replayPerformanceEntries.push(e(r))}function ox(e){let t=oP[e.entryType];return t?t(e):null}function oO(e){return((eS()||C.performance.timeOrigin)+e)/1e3}function oI(e){let t=e.entries[e.entries.length-1];return oA(e,"largest-contentful-paint",t?.element?[t.element]:void 0)}function oC(e){let t=[],r=[];for(let n of e.entries)if(void 0!==n.sources){let e=[];for(let t of n.sources)if(t.node){r.push(t.node);let n=ou.mirror.getId(t.node);n&&e.push(n)}t.push({value:n.value,nodeIds:e.length?e:void 0})}return oA(e,"cumulative-layout-shift",r,t)}function oM(e){let t=e.entries[e.entries.length-1];return oA(e,"first-input-delay",t?.target?[t.target]:void 0)}function ok(e){let t=e.entries[e.entries.length-1];return oA(e,"interaction-to-next-paint",t?.target?[t.target]:void 0)}function oA(e,t,r,n){let i=e.value,a=e.rating,s=oO(i);return{type:"web-vital",name:t,start:s,end:s,data:{value:i,size:i,rating:a,nodeIds:r?r.map(e=>ou.mirror.getId(e)):void 0,attributions:n}}}let oN=["log","warn","error"],oj=function(){let e=!1,t=!1,r={exception:()=>void 0,infoTick:()=>void 0,setConfig:e=>{e.captureExceptions,e.traceInternals}};return oN.forEach(e=>{r[e]=()=>void 0}),r}();class oL extends Error{constructor(){super("Event buffer exceeded maximum size of 20000000.")}}class oD{constructor(){this.events=[],this._totalSize=0,this.hasCheckout=!1,this.waitForCheckout=!1}get hasEvents(){return this.events.length>0}get type(){return"sync"}destroy(){this.events=[]}async addEvent(e){let t=JSON.stringify(e).length;if(this._totalSize+=t,this._totalSize>2e7)throw new oL;this.events.push(e)}finish(){return new Promise(e=>{let t=this.events;this.clear(),e(JSON.stringify(t))})}clear(){this.events=[],this._totalSize=0,this.hasCheckout=!1}getEarliestTimestamp(){let e=this.events.map(e=>e.timestamp).sort()[0];return e?oc(e):null}}class oU{constructor(e){this._worker=e,this._id=0}ensureReady(){return this._ensureReadyPromise||(this._ensureReadyPromise=new Promise((e,t)=>{this._worker.addEventListener("message",({data:r})=>{r.success?e():t()},{once:!0}),this._worker.addEventListener("error",e=>{t(e)},{once:!0})})),this._ensureReadyPromise}destroy(){this._worker.terminate()}postMessage(e,t){let r=this._getAndIncrementId();return new Promise((n,i)=>{let a=({data:t})=>{if(t.method===e&&t.id===r){if(this._worker.removeEventListener("message",a),!t.success)return void i(Error("Error in compression worker"));n(t.response)}};this._worker.addEventListener("message",a),this._worker.postMessage({id:r,method:e,arg:t})})}_getAndIncrementId(){return this._id++}}class oF{constructor(e){this._worker=new oU(e),this._earliestTimestamp=null,this._totalSize=0,this.hasCheckout=!1,this.waitForCheckout=!1}get hasEvents(){return!!this._earliestTimestamp}get type(){return"worker"}ensureReady(){return this._worker.ensureReady()}destroy(){this._worker.destroy()}addEvent(e){let t=oc(e.timestamp);(!this._earliestTimestamp||t<this._earliestTimestamp)&&(this._earliestTimestamp=t);let r=JSON.stringify(e);return(this._totalSize+=r.length,this._totalSize>2e7)?Promise.reject(new oL):this._sendEventToWorker(r)}finish(){return this._finishRequest()}clear(){this._earliestTimestamp=null,this._totalSize=0,this.hasCheckout=!1,this._worker.postMessage("clear").then(null,e=>{})}getEarliestTimestamp(){return this._earliestTimestamp}_sendEventToWorker(e){return this._worker.postMessage("addEvent",e)}async _finishRequest(){let e=await this._worker.postMessage("finish");return this._earliestTimestamp=null,this._totalSize=0,e}}class oB{constructor(e){this._fallback=new oD,this._compression=new oF(e),this._used=this._fallback,this._ensureWorkerIsLoadedPromise=this._ensureWorkerIsLoaded()}get waitForCheckout(){return this._used.waitForCheckout}get type(){return this._used.type}get hasEvents(){return this._used.hasEvents}get hasCheckout(){return this._used.hasCheckout}set hasCheckout(e){this._used.hasCheckout=e}set waitForCheckout(e){this._used.waitForCheckout=e}destroy(){this._fallback.destroy(),this._compression.destroy()}clear(){return this._used.clear()}getEarliestTimestamp(){return this._used.getEarliestTimestamp()}addEvent(e){return this._used.addEvent(e)}async finish(){return await this.ensureWorkerIsLoaded(),this._used.finish()}ensureWorkerIsLoaded(){return this._ensureWorkerIsLoadedPromise}async _ensureWorkerIsLoaded(){try{await this._compression.ensureReady()}catch(e){return}await this._switchToCompressionWorker()}async _switchToCompressionWorker(){let{events:e,hasCheckout:t,waitForCheckout:r}=this._fallback,n=[];for(let t of e)n.push(this._compression.addEvent(t));this._compression.hasCheckout=t,this._compression.waitForCheckout=r,this._used=this._compression;try{await Promise.all(n),this._fallback.clear()}catch(e){}}}function o$(){try{return"sessionStorage"in C&&!!C.sessionStorage}catch{return!1}}function oH(e){return void 0!==e&&Math.random()<e}function oW(e){if(o$())try{C.sessionStorage.setItem(aA,JSON.stringify(e))}catch{}}function oq(e){let t=Date.now(),r=e.id||em(),n=e.started||t,i=e.lastActivity||t,a=e.segmentId||0;return{id:r,started:n,lastActivity:i,segmentId:a,sampled:e.sampled,previousSessionId:e.previousSessionId}}function oz({sessionSampleRate:e,allowBuffering:t,stickySession:r=!1},{previousSessionId:n}={}){let i=oq({sampled:oH(e)?"session":!!t&&"buffer",previousSessionId:n});return r&&oW(i),i}function oX(e,t,r=+new Date){return null===e||void 0===t||t<0||0!==t&&e+t<=r}function oG(e,{maxReplayDuration:t,sessionIdleExpire:r,targetTime:n=Date.now()}){return oX(e.started,t,n)||oX(e.lastActivity,r,n)}function oV(e,{sessionIdleExpire:t,maxReplayDuration:r}){return!!oG(e,{sessionIdleExpire:t,maxReplayDuration:r})&&("buffer"!==e.sampled||0!==e.segmentId)}function oY({sessionIdleExpire:e,maxReplayDuration:t,previousSessionId:r},n){let i=n.stickySession&&function(){if(!o$())return null;try{let e=C.sessionStorage.getItem(aA);if(!e)return null;let t=JSON.parse(e);return oq(t)}catch{return null}}();return i?oV(i,{sessionIdleExpire:e,maxReplayDuration:t})?oz(n,{previousSessionId:i.id}):i:oz(n,{previousSessionId:r})}function oK(e,t,r){return!!oQ(e,t)&&(oJ(e,t,r),!0)}async function oJ(e,t,r){let{eventBuffer:n}=e;if(!n||n.waitForCheckout&&!r)return null;let i="buffer"===e.recordingMode;try{r&&i&&n.clear(),r&&(n.hasCheckout=!0,n.waitForCheckout=!1);let a=e.getOptions(),s=function(e,t){try{if("function"==typeof t&&e.type===sL.Custom)return t(e)}catch(e){return null}return e}(t,a.beforeAddRecordingEvent);if(!s)return;return await n.addEvent(s)}catch(a){let t=a&&a instanceof oL;if(t&&i)return n.clear(),n.waitForCheckout=!0,null;e.handleException(a),await e.stop({reason:t?"addEventSizeExceeded":"addEvent"});let r=eU();r&&r.recordDroppedEvent("internal_sdk_error","replay")}}function oQ(e,t){if(!e.eventBuffer||e.isPaused()||!e.isEnabled())return!1;let r=oc(t.timestamp);return!(r+e.timeouts.sessionIdlePause<Date.now())&&!(r>e.getContext().initialTimestamp+e.getOptions().maxReplayDuration)}function oZ(e){return"transaction"===e.type}function o0(e){return"feedback"===e.type}function o1(e){return!!e.category}function o2(){let e=ej().getPropagationContext().dsc;e&&delete e.replay_id;let t=tb();if(t){let e=tP(t);delete e.replay_id}}function o3(e,t){return t.map(({type:t,start:r,end:n,name:i,data:a})=>{let s=e.throttledAddEvent({type:sL.Custom,timestamp:r,data:{tag:"performanceSpan",payload:{op:t,description:i,startTimestamp:r,endTimestamp:n,data:a}}});return"string"==typeof s?Promise.resolve(null):s})}function o8(e,t){e.isEnabled()&&null!==t&&!function(e,t){var r,n,i,a,s=eU();let o=s?.getDsn(),l=s?.getOptions().tunnel;return r=t,!!(n=o)&&r.includes(n.host)||(i=t,!!(a=l)&&ax(i)===ax(a))}(0,t.name)&&e.addUpdate(()=>(o3(e,[t]),!0))}function o5(e){if(!e)return;let t=new TextEncoder;try{if("string"==typeof e)return t.encode(e).length;if(e instanceof URLSearchParams)return t.encode(e.toString()).length;if(e instanceof FormData){let r=aC(e);return t.encode(r).length}if(e instanceof Blob)return e.size;if(e instanceof ArrayBuffer)return e.byteLength}catch{}}function o4(e){if(!e)return;let t=parseInt(e,10);return isNaN(t)?void 0:t}function o6(e,t){if(!e)return{headers:{},size:void 0,_meta:{warnings:[t]}};let r={...e._meta},n=r.warnings||[];return r.warnings=[...n,t],e._meta=r,e}function o7(e,t){if(!t)return null;let{startTimestamp:r,endTimestamp:n,url:i,method:a,statusCode:s,request:o,response:l}=t;return{type:e,start:r/1e3,end:n/1e3,name:i,data:{method:a,statusCode:s,request:o,response:l}}}function o9(e){return{headers:{},size:e,_meta:{warnings:["URL_SKIPPED"]}}}function le(e,t,r){if(!t&&0===Object.keys(e).length)return;if(!t)return{headers:e};if(!r)return{headers:e,size:t};let n={headers:e,size:t},{body:i,warnings:a}=function(e){if(!e||"string"!=typeof e)return{body:e};let t=e.length>15e4,r=function(e){let t=e[0],r=e[e.length-1];return"["===t&&"]"===r||"{"===t&&"}"===r}(e);if(t){let t=e.slice(0,15e4);return r?{body:t,warnings:["MAYBE_JSON_TRUNCATED"]}:{body:`${t}…`,warnings:["TEXT_TRUNCATED"]}}if(r)try{return{body:JSON.parse(e)}}catch{}return{body:e}}(r);return n.body=i,a?.length&&(n._meta={warnings:a}),n}function lt(e,t){return Object.entries(e).reduce((r,[n,i])=>{let a=n.toLowerCase();return t.includes(a)&&e[n]&&(r[a]=i),r},{})}function lr(e,t=C.document.baseURI){if(e.startsWith("http://")||e.startsWith("https://")||e.startsWith(C.location.origin))return e;let r=new URL(e,t);if(r.origin!==new URL(t).origin)return e;let n=r.href;return!e.endsWith("/")&&n.endsWith("/")?n.slice(0,-1):n}async function ln(e,t,r){try{let n=await li(e,t,r),i=o7("resource.fetch",n);o8(r.replay,i)}catch(e){}}async function li(e,t,r){var n,i;let a=Date.now(),{startTimestamp:s=a,endTimestamp:o=a}=t,{url:l,method:u,status_code:c=0,request_body_size:d,response_body_size:p}=e.data,h=(n=r.networkDetailAllowUrls,eo(lr(l),n)&&(i=r.networkDetailDenyUrls,!eo(lr(l),i)));return{startTimestamp:s,endTimestamp:o,url:l,method:u,statusCode:c,request:h?function({networkCaptureBodies:e,networkRequestHeaders:t},r,n){var i,a;let s=r?(i=r,a=t,1===i.length&&"string"!=typeof i[0]?ll(i[0],a):2===i.length?ll(i[1],a):{}):{};if(!e)return le(s,n,void 0);let[o,l]=aM(ak(r),oj),u=le(s,n,o);return l?o6(u,l):u}(r,t.input,d):o9(d),response:await la(h,r,t.response,p)}}async function la(e,{networkCaptureBodies:t,networkResponseHeaders:r},n,i){if(!e&&void 0!==i)return o9(i);let a=n?lo(n.headers,r):{};if(!n||!t&&void 0!==i)return le(a,i,void 0);let[s,o]=await ls(n),l=function(e,{networkCaptureBodies:t,responseBodySize:r,captureDetails:n,headers:i}){try{let a=e?.length&&void 0===r?o5(e):r;if(!n)return o9(a);if(t)return le(i,a,e);return le(i,a,void 0)}catch(e){return le(i,r,void 0)}}(s,{networkCaptureBodies:t,responseBodySize:i,captureDetails:e,headers:a});return o?o6(l,o):l}async function ls(e){let t=function(e){try{return e.clone()}catch(e){}}(e);if(!t)return[void 0,"BODY_PARSE_ERROR"];try{var r;return[await (r=t,new Promise((e,t)=>{let n=nv(()=>t(Error("Timeout while trying to read response body")),500);lu(r).then(t=>e(t),e=>t(e)).finally(()=>clearTimeout(n))}))]}catch(e){if(e instanceof Error&&e.message.indexOf("Timeout")>-1)return[void 0,"BODY_PARSE_TIMEOUT"];return[void 0,"BODY_PARSE_ERROR"]}}function lo(e,t){let r={};return t.forEach(t=>{e.get(t)&&(r[t]=e.get(t))}),r}function ll(e,t){if(!e)return{};let r=e.headers;return r?r instanceof Headers?lo(r,t):Array.isArray(r)?{}:lt(r,t):{}}async function lu(e){return await e.text()}async function lc(e,t,r){try{let n=function(e,t,r){var n,i;let a=Date.now(),{startTimestamp:s=a,endTimestamp:o=a,input:l,xhr:u}=t,{url:c,method:d,status_code:p=0,request_body_size:h,response_body_size:f}=e.data;if(!c)return null;if(!u||(n=r.networkDetailAllowUrls,!eo(lr(c),n))||(i=r.networkDetailDenyUrls,eo(lr(c),i))){let e=o9(h);return{startTimestamp:s,endTimestamp:o,url:c,method:d,statusCode:p,request:e,response:o9(f)}}let m=u[rz],g=m?lt(m.request_headers,r.networkRequestHeaders):{},_=lt(function(e){let t=e.getAllResponseHeaders();return t?t.split("\r\n").reduce((e,t)=>{let[r,n]=t.split(": ");return n&&(e[r.toLowerCase()]=n),e},{}):{}}(u),r.networkResponseHeaders),[y,v]=r.networkCaptureBodies?aM(l,oj):[void 0],[b,E]=r.networkCaptureBodies?function(e){let t=[];try{return[e.responseText]}catch(e){t.push(e)}try{var r=e.response,n=e.responseType;try{if("string"==typeof r)return[r];if(r instanceof Document)return[r.body.outerHTML];if("json"===n&&r&&"object"==typeof r)return[JSON.stringify(r)];if(!r)return[void 0]}catch(e){return[void 0,"BODY_PARSE_ERROR"]}return[void 0,"UNPARSEABLE_BODY_TYPE"]}catch(e){t.push(e)}return[void 0]}(u):[void 0],S=le(g,h,y),w=le(_,f,b);return{startTimestamp:s,endTimestamp:o,url:c,method:d,statusCode:p,request:v?o6(S,v):S,response:E?o6(w,E):w}}(e,t,r),i=o7("resource.xhr",n);o8(r.replay,i)}catch(e){}}async function ld(e){try{return Promise.all(o3(e,[function(e){let{jsHeapSizeLimit:t,totalJSHeapSize:r,usedJSHeapSize:n}=e,i=Date.now()/1e3;return{type:"memory",name:"memory",start:i,end:i,data:{memory:{jsHeapSizeLimit:t,totalJSHeapSize:r,usedJSHeapSize:n}}}}(C.performance.memory)]))}catch{return[]}}let lp=C.navigator;async function lh({client:e,scope:t,replayId:r,event:n}){let i={event_id:r,integrations:"object"!=typeof e._integrations||null===e._integrations||Array.isArray(e._integrations)?void 0:Object.keys(e._integrations)};e.emit("preprocessEvent",n,i);let a=await tL(e.getOptions(),n,i,t,e,eL());if(!a)return null;e.emit("postprocessEvent",a,i),a.platform=a.platform||"javascript";let s=e.getSdkMetadata(),{name:o,version:l}=s?.sdk||{};return a.sdk={...a.sdk,name:o||"sentry.javascript.unknown",version:l||"0.0.0"},a}async function lf({recordingData:e,replayId:t,segmentId:r,eventContext:n,timestamp:i,session:a}){var s;let o,l=function({recordingData:e,headers:t}){let r,n=`${JSON.stringify(t)}
`;if("string"==typeof e)r=`${n}${e}`;else{let t=new TextEncoder().encode(n);(r=new Uint8Array(t.length+e.length)).set(t),r.set(e,t.length)}return r}({recordingData:e,headers:{segment_id:r}}),{urls:u,errorIds:c,traceIds:d,initialTimestamp:p}=n,h=eU(),f=ej(),m=h?.getTransport(),g=h?.getDsn();if(!h||!m||!g||!a.sampled)return eB({});let _={type:"replay_event",replay_start_timestamp:p/1e3,timestamp:i/1e3,error_ids:c,trace_ids:d,urls:u,replay_id:t,segment_id:r,replay_type:a.sampled},y=await lh({scope:f,client:h,replayId:t,event:_});if(!y)return h.recordDroppedEvent("event_processor","replay"),eB({});delete y.sdkProcessingMetadata;let v=(s=h.getOptions().tunnel,t5(re(y,t9(y),s,g),[[{type:"replay_event"},y],[{type:"replay_recording",length:"string"==typeof l?new TextEncoder().encode(l).length:l.length},l]]));try{o=await m.send(v)}catch(t){let e=Error(aN);try{e.cause=t}catch{}throw e}if("number"==typeof o.statusCode&&(o.statusCode<200||o.statusCode>=300))throw new lm(o.statusCode);let b=ng({},o);if(nm(b,"replay"))throw new lg(b);return o}class lm extends Error{constructor(e){super(`Transport returned status code ${e}`)}}class lg extends Error{constructor(e){super("Rate limit hit"),this.rateLimits=e}}async function l_(e,t={count:0,interval:5e3}){let{recordingData:r,onError:n}=e;if(r.length)try{return await lf(e),!0}catch(r){if(r instanceof lm||r instanceof lg)throw r;if(tF("Replays",{_retryCount:t.count}),n&&n(r),t.count>=3){let e=Error(`${aN} - max retries exceeded`);try{e.cause=r}catch{}throw e}return t.interval*=++t.count,new Promise((r,n)=>{nv(async()=>{try{await l_(e,t),r(!0)}catch(e){n(e)}},t.interval)})}}let ly="__THROTTLED";class lv{constructor({options:e,recordingOptions:t}){this.eventBuffer=null,this.performanceEntries=[],this.replayPerformanceEntries=[],this.recordingMode="session",this.timeouts={sessionIdlePause:3e5,sessionIdleExpire:9e5},this._lastActivity=Date.now(),this._isEnabled=!1,this._isPaused=!1,this._requiresManualStart=!1,this._hasInitializedCoreListeners=!1,this._context={errorIds:new Set,traceIds:new Set,urls:[],initialTimestamp:Date.now(),initialUrl:""},this._recordingOptions=t,this._options=e,this._debouncedFlush=function(e,t,r){let n,i,a,s=r?.maxWait?Math.max(r.maxWait,t):0,o=r?.setTimeoutImpl||setTimeout;function l(){return u(),n=e()}function u(){void 0!==i&&clearTimeout(i),void 0!==a&&clearTimeout(a),i=a=void 0}function c(){return i&&clearTimeout(i),i=o(l,t),s&&void 0===a&&(a=o(l,s)),n}return c.cancel=u,c.flush=function(){return void 0!==i||void 0!==a?l():n},c}(()=>this._flush(),this._options.flushMinDelay,{...{maxWait:this._options.flushMaxDelay},setTimeoutImpl:nv}),this._throttledAddEvent=function(e,t,r){let n=new Map,i=e=>{let t=e-5;n.forEach((e,r)=>{r<t&&n.delete(r)})},a=()=>[...n.values()].reduce((e,t)=>e+t,0),s=!1;return(...t)=>{let r=Math.floor(Date.now()/1e3);if(i(r),a()>=300){let e=s;return s=!0,e?"__SKIPPED":ly}s=!1;let o=n.get(r)||0;return n.set(r,o+1),e(...t)}}((e,t)=>(function(e,t,r){return oQ(e,t)?oJ(e,t,r):Promise.resolve(null)})(this,e,t),0,0);let{slowClickTimeout:r,slowClickIgnoreSelectors:n}=this.getOptions(),i=r?{threshold:Math.min(3e3,r),timeout:r,scrollTimeout:300,ignoreSelector:n?n.join(","):""}:void 0;i&&(this.clickDetector=new o_(this,i)),this._handleVisibilityChange=()=>{"visible"===C.document.visibilityState?this._doChangeToForegroundTasks():this._doChangeToBackgroundTasks()},this._handleWindowBlur=()=>{let e=ob({category:"ui.blur"});this._doChangeToBackgroundTasks(e)},this._handleWindowFocus=()=>{let e=ob({category:"ui.focus"});this._doChangeToForegroundTasks(e)},this._handleKeyboardEvent=e=>{!function(e,t){if(!e.isEnabled())return;e.updateUserActivity();let r=function(e){var t;let{metaKey:r,shiftKey:n,ctrlKey:i,altKey:a,key:s,target:o}=e;if(!o||"INPUT"===(t=o).tagName||"TEXTAREA"===t.tagName||t.isContentEditable||!s)return null;let l=r||i||a,u=1===s.length;if(!l&&u)return null;let c=er(o,{maxStringLength:200})||"<unknown>",d=oR(o,c);return ob({category:"ui.keyDown",message:c,data:{...d.data,metaKey:r,shiftKey:n,ctrlKey:i,altKey:a,key:s}})}(t);r&&op(e,r)}(this,e)}}getContext(){return this._context}isEnabled(){return this._isEnabled}isPaused(){return this._isPaused}isRecordingCanvas(){return!!this._canvas}getOptions(){return this._options}handleException(e){this._options.onError&&this._options.onError(e)}initializeSampling(e){let{errorSampleRate:t,sessionSampleRate:r}=this._options,n=t<=0&&r<=0;if(this._requiresManualStart=n,!n)this._initializeSessionForSampling(e),this.session&&!1!==this.session.sampled&&(this.recordingMode="buffer"===this.session.sampled&&0===this.session.segmentId?"buffer":"session",this._initializeRecording())}start(){if(this._isEnabled&&"session"===this.recordingMode||this._isEnabled&&"buffer"===this.recordingMode)return;this._updateUserActivity();let e=oY({maxReplayDuration:this._options.maxReplayDuration,sessionIdleExpire:this.timeouts.sessionIdleExpire},{stickySession:this._options.stickySession,sessionSampleRate:1,allowBuffering:!1});this.session=e,this.recordingMode="session",this._initializeRecording()}startBuffering(){if(this._isEnabled)return;let e=oY({sessionIdleExpire:this.timeouts.sessionIdleExpire,maxReplayDuration:this._options.maxReplayDuration},{stickySession:this._options.stickySession,sessionSampleRate:0,allowBuffering:!0});this.session=e,this.recordingMode="buffer",this._initializeRecording()}startRecording(){try{var e;let t,r=this._canvas;this._stopRecording=ou({...this._recordingOptions,..."buffer"===this.recordingMode?{checkoutEveryNms:6e4}:this._options._experiments.continuousCheckout&&{checkoutEveryNms:Math.max(36e4,this._options._experiments.continuousCheckout)},emit:(e=this,t=!1,(r,n)=>{if(!e.checkAndHandleExpiredSession())return;let i=n||!t;t=!0,e.clickDetector&&function(e,t){try{var r;if(r=t,3!==r.type)return;let{source:n}=t.data;if(og.has(n)&&e.registerMutation(t.timestamp),n===sD.Scroll&&e.registerScroll(t.timestamp),t.data.source===sD.MouseInteraction){let{type:r,id:n}=t.data,i=ou.mirror.getNode(n);i instanceof HTMLElement&&r===sU.Click&&e.registerClick(i)}}catch{}}(e.clickDetector,r),e.addUpdate(()=>{var t;if("buffer"===e.recordingMode&&i&&e.setInitialState(),!oK(e,r,i))return!0;if(!i)return!1;let n=e.session;if(t=e,i&&t.session&&0===t.session.segmentId&&oK(t,function(e){let t=e.getOptions();return{type:sL.Custom,timestamp:Date.now(),data:{tag:"options",payload:{shouldRecordCanvas:e.isRecordingCanvas(),sessionSampleRate:t.sessionSampleRate,errorSampleRate:t.errorSampleRate,useCompressionOption:t.useCompression,blockAllMedia:t.blockAllMedia,maskAllText:t.maskAllText,maskAllInputs:t.maskAllInputs,useCompression:!!e.eventBuffer&&"worker"===e.eventBuffer.type,networkDetailHasUrls:t.networkDetailAllowUrls.length>0,networkCaptureBodies:t.networkCaptureBodies,networkRequestHasHeaders:t.networkRequestHeaders.length>0,networkResponseHasHeaders:t.networkResponseHeaders.length>0}}}}(t),!1),"buffer"===e.recordingMode&&n&&e.eventBuffer){let t=e.eventBuffer.getEarliestTimestamp();t&&(n.started=t,e.getOptions().stickySession&&oW(n))}return!!n?.previousSessionId||("session"===e.recordingMode&&e.flush(),!0)})}),.../iPhone|iPad|iPod/i.test(lp?.userAgent??"")||/Macintosh/i.test(lp?.userAgent??"")&&lp?.maxTouchPoints&&lp?.maxTouchPoints>1?{sampling:{mousemove:!1}}:{},onMutation:this._onMutationHandler.bind(this),...r?{recordCanvas:r.recordCanvas,getCanvasManager:r.getCanvasManager,sampling:r.sampling,dataURLOptions:r.dataURLOptions}:{}})}catch(e){this.handleException(e)}}stopRecording(){try{return this._stopRecording&&(this._stopRecording(),this._stopRecording=void 0),!0}catch(e){return this.handleException(e),!1}}async stop({forceFlush:e=!1,reason:t}={}){if(this._isEnabled){this._isEnabled=!1,this.recordingMode="buffer";try{o2(),this._removeListeners(),this.stopRecording(),this._debouncedFlush.cancel(),e&&await this._flush({force:!0}),this.eventBuffer?.destroy(),this.eventBuffer=null,function(){if(o$())try{C.sessionStorage.removeItem(aA)}catch{}}(),this.session=void 0}catch(e){this.handleException(e)}}}pause(){this._isPaused||(this._isPaused=!0,this.stopRecording())}resume(){this._isPaused&&this._checkSession()&&(this._isPaused=!1,this.startRecording())}async sendBufferedReplayOrFlush({continueRecording:e=!0}={}){if("session"===this.recordingMode)return this.flushImmediate();let t=Date.now();await this.flushImmediate();let r=this.stopRecording();e&&r&&"session"!==this.recordingMode&&(this.recordingMode="session",this.session&&(this._updateUserActivity(t),this._updateSessionActivity(t),this._maybeSaveSession()),this.startRecording())}addUpdate(e){let t=e();"buffer"!==this.recordingMode&&this._isEnabled&&!0!==t&&this._debouncedFlush()}triggerUserActivity(){if(this._updateUserActivity(),!this._stopRecording){if(!this._checkSession())return;this.resume();return}this.checkAndHandleExpiredSession(),this._updateSessionActivity()}updateUserActivity(){this._updateUserActivity(),this._updateSessionActivity()}conditionalFlush(){return"buffer"===this.recordingMode?Promise.resolve():this.flushImmediate()}flush(){return this._debouncedFlush()}flushImmediate(){return this._debouncedFlush(),this._debouncedFlush.flush()}cancelFlush(){this._debouncedFlush.cancel()}getSessionId(){return this.session?.id}checkAndHandleExpiredSession(){return this._lastActivity&&oX(this._lastActivity,this.timeouts.sessionIdlePause)&&this.session&&"session"===this.session.sampled?void this.pause():!!this._checkSession()}setInitialState(){let e=`${C.location.pathname}${C.location.hash}${C.location.search}`,t=`${C.location.origin}${e}`;this.performanceEntries=[],this.replayPerformanceEntries=[],this._clearContext(),this._context.initialUrl=t,this._context.initialTimestamp=Date.now(),this._context.urls.push(t)}throttledAddEvent(e,t){let r=this._throttledAddEvent(e,t);if(r===ly){let e=ob({category:"replay.throttled"});this.addUpdate(()=>!oK(this,{type:5,timestamp:e.timestamp||0,data:{tag:"breadcrumb",payload:e,metric:!0}}))}return r}getCurrentRoute(){let e=this.lastActiveSpan||tb(),t=e&&tv(e),r=(t&&tp(t).data||{})[eW];if(t&&r&&["route","custom"].includes(r))return tp(t).description}_initializeRecording(){this.setInitialState(),this._updateSessionActivity(),this.eventBuffer=function({useCompression:e,workerUrl:t}){if(e&&window.Worker){let e=function(e){try{let t=e||function(){if("undefined"==typeof __SENTRY_EXCLUDE_REPLAY_WORKER__||!__SENTRY_EXCLUDE_REPLAY_WORKER__){let e=new Blob(['var t=Uint8Array,n=Uint16Array,r=Int32Array,e=new t([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),i=new t([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),s=new t([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),a=function(t,e){for(var i=new n(31),s=0;s<31;++s)i[s]=e+=1<<t[s-1];var a=new r(i[30]);for(s=1;s<30;++s)for(var o=i[s];o<i[s+1];++o)a[o]=o-i[s]<<5|s;return{b:i,r:a}},o=a(e,2),h=o.b,f=o.r;h[28]=258,f[258]=28;for(var l=a(i,0).r,u=new n(32768),c=0;c<32768;++c){var v=(43690&c)>>1|(21845&c)<<1;v=(61680&(v=(52428&v)>>2|(13107&v)<<2))>>4|(3855&v)<<4,u[c]=((65280&v)>>8|(255&v)<<8)>>1}var d=function(t,r,e){for(var i=t.length,s=0,a=new n(r);s<i;++s)t[s]&&++a[t[s]-1];var o,h=new n(r);for(s=1;s<r;++s)h[s]=h[s-1]+a[s-1]<<1;if(e){o=new n(1<<r);var f=15-r;for(s=0;s<i;++s)if(t[s])for(var l=s<<4|t[s],c=r-t[s],v=h[t[s]-1]++<<c,d=v|(1<<c)-1;v<=d;++v)o[u[v]>>f]=l}else for(o=new n(i),s=0;s<i;++s)t[s]&&(o[s]=u[h[t[s]-1]++]>>15-t[s]);return o},p=new t(288);for(c=0;c<144;++c)p[c]=8;for(c=144;c<256;++c)p[c]=9;for(c=256;c<280;++c)p[c]=7;for(c=280;c<288;++c)p[c]=8;var g=new t(32);for(c=0;c<32;++c)g[c]=5;var w=d(p,9,0),y=d(g,5,0),m=function(t){return(t+7)/8|0},b=function(n,r,e){return(null==e||e>n.length)&&(e=n.length),new t(n.subarray(r,e))},M=["unexpected EOF","invalid block type","invalid length/literal","invalid distance","stream finished","no stream handler",,"no callback","invalid UTF-8 data","extra field too long","date not in range 1980-2099","filename too long","stream finishing","invalid zip data"],E=function(t,n,r){var e=new Error(n||M[t]);if(e.code=t,Error.captureStackTrace&&Error.captureStackTrace(e,E),!r)throw e;return e},z=function(t,n,r){r<<=7&n;var e=n/8|0;t[e]|=r,t[e+1]|=r>>8},_=function(t,n,r){r<<=7&n;var e=n/8|0;t[e]|=r,t[e+1]|=r>>8,t[e+2]|=r>>16},x=function(r,e){for(var i=[],s=0;s<r.length;++s)r[s]&&i.push({s:s,f:r[s]});var a=i.length,o=i.slice();if(!a)return{t:F,l:0};if(1==a){var h=new t(i[0].s+1);return h[i[0].s]=1,{t:h,l:1}}i.sort(function(t,n){return t.f-n.f}),i.push({s:-1,f:25001});var f=i[0],l=i[1],u=0,c=1,v=2;for(i[0]={s:-1,f:f.f+l.f,l:f,r:l};c!=a-1;)f=i[i[u].f<i[v].f?u++:v++],l=i[u!=c&&i[u].f<i[v].f?u++:v++],i[c++]={s:-1,f:f.f+l.f,l:f,r:l};var d=o[0].s;for(s=1;s<a;++s)o[s].s>d&&(d=o[s].s);var p=new n(d+1),g=A(i[c-1],p,0);if(g>e){s=0;var w=0,y=g-e,m=1<<y;for(o.sort(function(t,n){return p[n.s]-p[t.s]||t.f-n.f});s<a;++s){var b=o[s].s;if(!(p[b]>e))break;w+=m-(1<<g-p[b]),p[b]=e}for(w>>=y;w>0;){var M=o[s].s;p[M]<e?w-=1<<e-p[M]++-1:++s}for(;s>=0&&w;--s){var E=o[s].s;p[E]==e&&(--p[E],++w)}g=e}return{t:new t(p),l:g}},A=function(t,n,r){return-1==t.s?Math.max(A(t.l,n,r+1),A(t.r,n,r+1)):n[t.s]=r},D=function(t){for(var r=t.length;r&&!t[--r];);for(var e=new n(++r),i=0,s=t[0],a=1,o=function(t){e[i++]=t},h=1;h<=r;++h)if(t[h]==s&&h!=r)++a;else{if(!s&&a>2){for(;a>138;a-=138)o(32754);a>2&&(o(a>10?a-11<<5|28690:a-3<<5|12305),a=0)}else if(a>3){for(o(s),--a;a>6;a-=6)o(8304);a>2&&(o(a-3<<5|8208),a=0)}for(;a--;)o(s);a=1,s=t[h]}return{c:e.subarray(0,i),n:r}},T=function(t,n){for(var r=0,e=0;e<n.length;++e)r+=t[e]*n[e];return r},k=function(t,n,r){var e=r.length,i=m(n+2);t[i]=255&e,t[i+1]=e>>8,t[i+2]=255^t[i],t[i+3]=255^t[i+1];for(var s=0;s<e;++s)t[i+s+4]=r[s];return 8*(i+4+e)},U=function(t,r,a,o,h,f,l,u,c,v,m){z(r,m++,a),++h[256];for(var b=x(h,15),M=b.t,E=b.l,A=x(f,15),U=A.t,C=A.l,F=D(M),I=F.c,S=F.n,L=D(U),O=L.c,j=L.n,q=new n(19),B=0;B<I.length;++B)++q[31&I[B]];for(B=0;B<O.length;++B)++q[31&O[B]];for(var G=x(q,7),H=G.t,J=G.l,K=19;K>4&&!H[s[K-1]];--K);var N,P,Q,R,V=v+5<<3,W=T(h,p)+T(f,g)+l,X=T(h,M)+T(f,U)+l+14+3*K+T(q,H)+2*q[16]+3*q[17]+7*q[18];if(c>=0&&V<=W&&V<=X)return k(r,m,t.subarray(c,c+v));if(z(r,m,1+(X<W)),m+=2,X<W){N=d(M,E,0),P=M,Q=d(U,C,0),R=U;var Y=d(H,J,0);z(r,m,S-257),z(r,m+5,j-1),z(r,m+10,K-4),m+=14;for(B=0;B<K;++B)z(r,m+3*B,H[s[B]]);m+=3*K;for(var Z=[I,O],$=0;$<2;++$){var tt=Z[$];for(B=0;B<tt.length;++B){var nt=31&tt[B];z(r,m,Y[nt]),m+=H[nt],nt>15&&(z(r,m,tt[B]>>5&127),m+=tt[B]>>12)}}}else N=w,P=p,Q=y,R=g;for(B=0;B<u;++B){var rt=o[B];if(rt>255){_(r,m,N[(nt=rt>>18&31)+257]),m+=P[nt+257],nt>7&&(z(r,m,rt>>23&31),m+=e[nt]);var et=31&rt;_(r,m,Q[et]),m+=R[et],et>3&&(_(r,m,rt>>5&8191),m+=i[et])}else _(r,m,N[rt]),m+=P[rt]}return _(r,m,N[256]),m+P[256]},C=new r([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),F=new t(0),I=function(){for(var t=new Int32Array(256),n=0;n<256;++n){for(var r=n,e=9;--e;)r=(1&r&&-306674912)^r>>>1;t[n]=r}return t}(),S=function(){var t=1,n=0;return{p:function(r){for(var e=t,i=n,s=0|r.length,a=0;a!=s;){for(var o=Math.min(a+2655,s);a<o;++a)i+=e+=r[a];e=(65535&e)+15*(e>>16),i=(65535&i)+15*(i>>16)}t=e,n=i},d:function(){return(255&(t%=65521))<<24|(65280&t)<<8|(255&(n%=65521))<<8|n>>8}}},L=function(s,a,o,h,u){if(!u&&(u={l:1},a.dictionary)){var c=a.dictionary.subarray(-32768),v=new t(c.length+s.length);v.set(c),v.set(s,c.length),s=v,u.w=c.length}return function(s,a,o,h,u,c){var v=c.z||s.length,d=new t(h+v+5*(1+Math.ceil(v/7e3))+u),p=d.subarray(h,d.length-u),g=c.l,w=7&(c.r||0);if(a){w&&(p[0]=c.r>>3);for(var y=C[a-1],M=y>>13,E=8191&y,z=(1<<o)-1,_=c.p||new n(32768),x=c.h||new n(z+1),A=Math.ceil(o/3),D=2*A,T=function(t){return(s[t]^s[t+1]<<A^s[t+2]<<D)&z},F=new r(25e3),I=new n(288),S=new n(32),L=0,O=0,j=c.i||0,q=0,B=c.w||0,G=0;j+2<v;++j){var H=T(j),J=32767&j,K=x[H];if(_[J]=K,x[H]=J,B<=j){var N=v-j;if((L>7e3||q>24576)&&(N>423||!g)){w=U(s,p,0,F,I,S,O,q,G,j-G,w),q=L=O=0,G=j;for(var P=0;P<286;++P)I[P]=0;for(P=0;P<30;++P)S[P]=0}var Q=2,R=0,V=E,W=J-K&32767;if(N>2&&H==T(j-W))for(var X=Math.min(M,N)-1,Y=Math.min(32767,j),Z=Math.min(258,N);W<=Y&&--V&&J!=K;){if(s[j+Q]==s[j+Q-W]){for(var $=0;$<Z&&s[j+$]==s[j+$-W];++$);if($>Q){if(Q=$,R=W,$>X)break;var tt=Math.min(W,$-2),nt=0;for(P=0;P<tt;++P){var rt=j-W+P&32767,et=rt-_[rt]&32767;et>nt&&(nt=et,K=rt)}}}W+=(J=K)-(K=_[J])&32767}if(R){F[q++]=268435456|f[Q]<<18|l[R];var it=31&f[Q],st=31&l[R];O+=e[it]+i[st],++I[257+it],++S[st],B=j+Q,++L}else F[q++]=s[j],++I[s[j]]}}for(j=Math.max(j,B);j<v;++j)F[q++]=s[j],++I[s[j]];w=U(s,p,g,F,I,S,O,q,G,j-G,w),g||(c.r=7&w|p[w/8|0]<<3,w-=7,c.h=x,c.p=_,c.i=j,c.w=B)}else{for(j=c.w||0;j<v+g;j+=65535){var at=j+65535;at>=v&&(p[w/8|0]=g,at=v),w=k(p,w+1,s.subarray(j,at))}c.i=v}return b(d,0,h+m(w)+u)}(s,null==a.level?6:a.level,null==a.mem?u.l?Math.ceil(1.5*Math.max(8,Math.min(13,Math.log(s.length)))):20:12+a.mem,o,h,u)},O=function(t,n,r){for(;r;++n)t[n]=r,r>>>=8},j=function(){function n(n,r){if("function"==typeof n&&(r=n,n={}),this.ondata=r,this.o=n||{},this.s={l:0,i:32768,w:32768,z:32768},this.b=new t(98304),this.o.dictionary){var e=this.o.dictionary.subarray(-32768);this.b.set(e,32768-e.length),this.s.i=32768-e.length}}return n.prototype.p=function(t,n){this.ondata(L(t,this.o,0,0,this.s),n)},n.prototype.push=function(n,r){this.ondata||E(5),this.s.l&&E(4);var e=n.length+this.s.z;if(e>this.b.length){if(e>2*this.b.length-32768){var i=new t(-32768&e);i.set(this.b.subarray(0,this.s.z)),this.b=i}var s=this.b.length-this.s.z;this.b.set(n.subarray(0,s),this.s.z),this.s.z=this.b.length,this.p(this.b,!1),this.b.set(this.b.subarray(-32768)),this.b.set(n.subarray(s),32768),this.s.z=n.length-s+32768,this.s.i=32766,this.s.w=32768}else this.b.set(n,this.s.z),this.s.z+=n.length;this.s.l=1&r,(this.s.z>this.s.w+8191||r)&&(this.p(this.b,r||!1),this.s.w=this.s.i,this.s.i-=2)},n.prototype.flush=function(){this.ondata||E(5),this.s.l&&E(4),this.p(this.b,!1),this.s.w=this.s.i,this.s.i-=2},n}();function q(t,n){n||(n={});var r=function(){var t=-1;return{p:function(n){for(var r=t,e=0;e<n.length;++e)r=I[255&r^n[e]]^r>>>8;t=r},d:function(){return~t}}}(),e=t.length;r.p(t);var i,s=L(t,n,10+((i=n).filename?i.filename.length+1:0),8),a=s.length;return function(t,n){var r=n.filename;if(t[0]=31,t[1]=139,t[2]=8,t[8]=n.level<2?4:9==n.level?2:0,t[9]=3,0!=n.mtime&&O(t,4,Math.floor(new Date(n.mtime||Date.now())/1e3)),r){t[3]=8;for(var e=0;e<=r.length;++e)t[e+10]=r.charCodeAt(e)}}(s,n),O(s,a-8,r.d()),O(s,a-4,e),s}var B=function(){function t(t,n){this.c=S(),this.v=1,j.call(this,t,n)}return t.prototype.push=function(t,n){this.c.p(t),j.prototype.push.call(this,t,n)},t.prototype.p=function(t,n){var r=L(t,this.o,this.v&&(this.o.dictionary?6:2),n&&4,this.s);this.v&&(function(t,n){var r=n.level,e=0==r?0:r<6?1:9==r?3:2;if(t[0]=120,t[1]=e<<6|(n.dictionary&&32),t[1]|=31-(t[0]<<8|t[1])%31,n.dictionary){var i=S();i.p(n.dictionary),O(t,2,i.d())}}(r,this.o),this.v=0),n&&O(r,r.length-4,this.c.d()),this.ondata(r,n)},t.prototype.flush=function(){j.prototype.flush.call(this)},t}(),G="undefined"!=typeof TextEncoder&&new TextEncoder,H="undefined"!=typeof TextDecoder&&new TextDecoder;try{H.decode(F,{stream:!0})}catch(t){}var J=function(){function t(t){this.ondata=t}return t.prototype.push=function(t,n){this.ondata||E(5),this.d&&E(4),this.ondata(K(t),this.d=n||!1)},t}();function K(n,r){if(G)return G.encode(n);for(var e=n.length,i=new t(n.length+(n.length>>1)),s=0,a=function(t){i[s++]=t},o=0;o<e;++o){if(s+5>i.length){var h=new t(s+8+(e-o<<1));h.set(i),i=h}var f=n.charCodeAt(o);f<128||r?a(f):f<2048?(a(192|f>>6),a(128|63&f)):f>55295&&f<57344?(a(240|(f=65536+(1047552&f)|1023&n.charCodeAt(++o))>>18),a(128|f>>12&63),a(128|f>>6&63),a(128|63&f)):(a(224|f>>12),a(128|f>>6&63),a(128|63&f))}return b(i,0,s)}const N=new class{constructor(){this._init()}clear(){this._init()}addEvent(t){if(!t)throw new Error("Adding invalid event");const n=this._hasEvents?",":"";this.stream.push(n+t),this._hasEvents=!0}finish(){this.stream.push("]",!0);const t=function(t){let n=0;for(const r of t)n+=r.length;const r=new Uint8Array(n);for(let n=0,e=0,i=t.length;n<i;n++){const i=t[n];r.set(i,e),e+=i.length}return r}(this._deflatedData);return this._init(),t}_init(){this._hasEvents=!1,this._deflatedData=[],this.deflate=new B,this.deflate.ondata=(t,n)=>{this._deflatedData.push(t)},this.stream=new J((t,n)=>{this.deflate.push(t,n)}),this.stream.push("[")}},P={clear:()=>{N.clear()},addEvent:t=>N.addEvent(t),finish:()=>N.finish(),compress:t=>function(t){return q(K(t))}(t)};addEventListener("message",function(t){const n=t.data.method,r=t.data.id,e=t.data.arg;if(n in P&&"function"==typeof P[n])try{const t=P[n](e);postMessage({id:r,method:n,success:!0,response:t})}catch(t){postMessage({id:r,method:n,success:!1,response:t.message}),console.error(t)}}),postMessage({id:void 0,method:"init",success:!0,response:void 0});']);return URL.createObjectURL(e)}return""}();if(!t)return;let r=new Worker(t);return new oB(r)}catch(e){}}(t);if(e)return e}return new oD}({useCompression:this._options.useCompression,workerUrl:this._options.workerUrl}),this._removeListeners(),this._addListeners(),this._isEnabled=!0,this._isPaused=!1,this.startRecording()}_initializeSessionForSampling(e){let t=this._options.errorSampleRate>0,r=oY({sessionIdleExpire:this.timeouts.sessionIdleExpire,maxReplayDuration:this._options.maxReplayDuration,previousSessionId:e},{stickySession:this._options.stickySession,sessionSampleRate:this._options.sessionSampleRate,allowBuffering:t});this.session=r}_checkSession(){if(!this.session)return!1;let e=this.session;return!oV(e,{sessionIdleExpire:this.timeouts.sessionIdleExpire,maxReplayDuration:this._options.maxReplayDuration})||(this._refreshSession(e),!1)}async _refreshSession(e){this._isEnabled&&(await this.stop({reason:"refresh session"}),this.initializeSampling(e.id))}_addListeners(){try{C.document.addEventListener("visibilitychange",this._handleVisibilityChange),C.addEventListener("blur",this._handleWindowBlur),C.addEventListener("focus",this._handleWindowFocus),C.addEventListener("keydown",this._handleKeyboardEvent),this.clickDetector&&this.clickDetector.addListeners(),this._hasInitializedCoreListeners||(!function(e){let t=eU();rH(ow(e)),rV(t=>{if(!e.isEnabled())return;let r=function(e){let{from:t,to:r}=e,n=Date.now()/1e3;return{type:"navigation.push",start:n,end:n,name:r,data:{previous:t}}}(t);null!==r&&(e.getContext().urls.push(r.name),e.triggerUserActivity(),e.addUpdate(()=>(o3(e,[r]),!1)))});let r=eU();r&&r.on("beforeAddBreadcrumb",t=>(function(e,t){var r;if(!e.isEnabled()||!o1(t))return;let n=(r=t,!o1(r)||["fetch","xhr","sentry.event","sentry.transaction"].includes(r.category)||r.category.startsWith("ui.")?null:"console"===r.category?function(e){let t=e.data?.arguments;if(!Array.isArray(t)||0===t.length)return ob(e);let r=!1,n=t.map(e=>{if(!e)return e;if("string"==typeof e)return e.length>5e3?(r=!0,`${e.slice(0,5e3)}…`):e;if("object"==typeof e)try{let t=tj(e,7);if(JSON.stringify(t).length>5e3)return r=!0,`${JSON.stringify(t,null,2).slice(0,5e3)}…`;return t}catch{}return e});return ob({...e,data:{...e.data,arguments:n,...r?{_meta:{warnings:["CONSOLE_ARG_TRUNCATED"]}}:{}}})}(r):ob(r));n&&op(e,n)})(e,t));let n=eU();try{let{networkDetailAllowUrls:t,networkDetailDenyUrls:r,networkCaptureBodies:i,networkRequestHeaders:a,networkResponseHeaders:s}=e.getOptions(),o={replay:e,networkDetailAllowUrls:t,networkDetailDenyUrls:r,networkCaptureBodies:i,networkRequestHeaders:a,networkResponseHeaders:s};n&&n.on("beforeAddBreadcrumb",(e,t)=>(function(e,t,r){if(t.data)try{var n,i,a,s;if(n=t,"xhr"===n.category&&(i=r,i?.xhr)&&(!function(e,t){let{xhr:r,input:n}=t;if(!r)return;let i=o5(n),a=r.getResponseHeader("content-length")?o4(r.getResponseHeader("content-length")):function(e,t){try{let r="json"===t&&e&&"object"==typeof e?JSON.stringify(e):e;return o5(r)}catch{return}}(r.response,r.responseType);void 0!==i&&(e.data.request_body_size=i),void 0!==a&&(e.data.response_body_size=a)}(t,r),lc(t,r,e)),a=t,"fetch"===a.category&&(s=r,s?.response)){let{input:n,response:i}=r,a=o5(n?ak(n):void 0),s=i?o4(i.headers.get("content-length")):void 0;void 0!==a&&(t.data.request_body_size=a),void 0!==s&&(t.data.response_body_size=s),ln(t,r,e)}}catch(e){}})(o,e,t))}catch{}tB(Object.assign((t,r)=>!e.isEnabled()||e.isPaused()?t:"replay_event"===t.type?(delete t.breadcrumbs,t):!t.type||oZ(t)||o0(t)?e.checkAndHandleExpiredSession()?o0(t)?(e.flush(),t.contexts.feedback.replay_id=e.getSessionId(),e.triggerUserActivity(),e.addUpdate(()=>!t.timestamp||(e.throttledAddEvent({type:sL.Custom,timestamp:1e3*t.timestamp,data:{tag:"breadcrumb",payload:{timestamp:t.timestamp,type:"default",category:"sentry.feedback",data:{feedbackId:t.event_id}}}}),!1)),t):!t.type&&t.exception?.values?.length&&r.originalException?.__rrweb__&&!e.getOptions()._experiments.captureExceptions?null:(("buffer"===e.recordingMode&&t.message!==aN&&t.exception&&!t.type&&oH(e.getOptions().errorSampleRate)||"session"===e.recordingMode)&&(t.tags={...t.tags,replayId:e.getSessionId()}),t):(o2(),t):t,{id:"Replay"})),t&&(t.on("beforeSendEvent",t=>{e.isEnabled()&&!t.type&&function(e,t){let r=t.exception?.values?.[0]?.value;"string"==typeof r&&(r.match(/(reactjs\.org\/docs\/error-decoder\.html\?invariant=|react\.dev\/errors\/)(418|419|422|423|425)/)||r.match(/(does not match server-rendered HTML|Hydration failed because)/i))&&op(e,ob({category:"replay.hydrate-error",data:{url:en()}}))}(e,t)}),t.on("afterSendEvent",(t,r)=>{if(!e.isEnabled()||t.type&&!oZ(t))return;let n=r?.statusCode;if(n&&!(n<200)&&!(n>=300)){if(oZ(t))return void function(e,t){let r=e.getContext();t.contexts?.trace?.trace_id&&r.traceIds.size<100&&r.traceIds.add(t.contexts.trace.trace_id)}(e,t);!function(e,t){let r=e.getContext();if(t.event_id&&r.errorIds.size<100&&r.errorIds.add(t.event_id),"buffer"!==e.recordingMode||!t.tags||!t.tags.replayId)return;let{beforeErrorSampling:n}=e.getOptions();("function"!=typeof n||n(t))&&nv(async()=>{try{await e.sendBufferedReplayOrFlush()}catch(t){e.handleException(t)}})}(e,t)}}),t.on("createDsc",t=>{let r=e.getSessionId();r&&e.isEnabled()&&"session"===e.recordingMode&&e.checkAndHandleExpiredSession()&&(t.replay_id=r)}),t.on("spanStart",t=>{e.lastActiveSpan=t}),t.on("spanEnd",t=>{e.lastActiveSpan=t}),t.on("beforeSendFeedback",async(t,r)=>{let n=e.getSessionId();r?.includeReplay&&e.isEnabled()&&n&&t.contexts?.feedback&&("api"===t.contexts.feedback.source&&await e.flush(),t.contexts.feedback.replay_id=n)}),t.on("openFeedbackWidget",async()=>{await e.flush()}))}(this),this._hasInitializedCoreListeners=!0)}catch(e){this.handleException(e)}this._performanceCleanupCallback=function(e){function t(t){e.performanceEntries.includes(t)||e.performanceEntries.push(t)}function r({entries:e}){e.forEach(t)}let n=[];return["navigation","paint","resource"].forEach(e=>{n.push(iO(e,r))}),n.push(iP(oT(oI,e)),iR(oT(oC,e)),iT(oT(oM,e)),ix(oT(ok,e))),()=>{n.forEach(e=>e())}}(this)}_removeListeners(){try{C.document.removeEventListener("visibilitychange",this._handleVisibilityChange),C.removeEventListener("blur",this._handleWindowBlur),C.removeEventListener("focus",this._handleWindowFocus),C.removeEventListener("keydown",this._handleKeyboardEvent),this.clickDetector&&this.clickDetector.removeListeners(),this._performanceCleanupCallback&&this._performanceCleanupCallback()}catch(e){this.handleException(e)}}_doChangeToBackgroundTasks(e){this.session&&(oG(this.session,{maxReplayDuration:this._options.maxReplayDuration,sessionIdleExpire:this.timeouts.sessionIdleExpire})||(e&&this._createCustomBreadcrumb(e),this.conditionalFlush()))}_doChangeToForegroundTasks(e){if(this.session)this.checkAndHandleExpiredSession()&&e&&this._createCustomBreadcrumb(e)}_updateUserActivity(e=Date.now()){this._lastActivity=e}_updateSessionActivity(e=Date.now()){this.session&&(this.session.lastActivity=e,this._maybeSaveSession())}_createCustomBreadcrumb(e){this.addUpdate(()=>{this.throttledAddEvent({type:sL.Custom,timestamp:e.timestamp||0,data:{tag:"breadcrumb",payload:e}})})}_addPerformanceEntries(){let e=this.performanceEntries.map(ox).filter(Boolean).concat(this.replayPerformanceEntries);if(this.performanceEntries=[],this.replayPerformanceEntries=[],this._requiresManualStart){let t=this._context.initialTimestamp/1e3;e=e.filter(e=>e.start>=t)}return Promise.all(o3(this,e))}_clearContext(){this._context.errorIds.clear(),this._context.traceIds.clear(),this._context.urls=[]}_updateInitialTimestampFromEventBuffer(){let{session:e,eventBuffer:t}=this;if(!e||!t||this._requiresManualStart||e.segmentId)return;let r=t.getEarliestTimestamp();r&&r<this._context.initialTimestamp&&(this._context.initialTimestamp=r)}_popEventContext(){let e={initialTimestamp:this._context.initialTimestamp,initialUrl:this._context.initialUrl,errorIds:Array.from(this._context.errorIds),traceIds:Array.from(this._context.traceIds),urls:this._context.urls};return this._clearContext(),e}async _runFlush(){let e=this.getSessionId();if(this.session&&this.eventBuffer&&e&&(await this._addPerformanceEntries(),this.eventBuffer?.hasEvents)){if((await ld(this),this.eventBuffer)&&e===this.getSessionId())try{this._updateInitialTimestampFromEventBuffer();let t=Date.now();if(t-this._context.initialTimestamp>this._options.maxReplayDuration+3e4)throw Error("Session is too long, not sending replay");let r=this._popEventContext(),n=this.session.segmentId++;this._maybeSaveSession();let i=await this.eventBuffer.finish();await l_({replayId:e,recordingData:i,segmentId:n,eventContext:r,session:this.session,timestamp:t,onError:e=>this.handleException(e)})}catch(t){this.handleException(t),this.stop({reason:"sendReplay"});let e=eU();e&&e.recordDroppedEvent(t instanceof lg?"ratelimit_backoff":"send_error","replay")}}}async _flush({force:e=!1}={}){if(!this._isEnabled&&!e||!this.checkAndHandleExpiredSession()||!this.session)return;let t=this.session.started,r=Date.now()-t;this._debouncedFlush.cancel();let n=r<this._options.minReplayDuration,i=r>this._options.maxReplayDuration+5e3;if(n||i){n&&this._debouncedFlush();return}let a=this.eventBuffer;a&&0===this.session.segmentId&&a.hasCheckout;let s=!!this._flushLock;this._flushLock||(this._flushLock=this._runFlush());try{await this._flushLock}catch(e){this.handleException(e)}finally{this._flushLock=void 0,s&&this._debouncedFlush()}}_maybeSaveSession(){this.session&&this._options.stickySession&&oW(this.session)}_onMutationHandler(e){let{ignoreMutations:t}=this._options._experiments;if(t?.length&&e.some(e=>{let r=function(e){if(!e)return null;try{return e.nodeType===e.ELEMENT_NODE?e:e.parentElement}catch{return null}}(e.target),n=t.join(",");return r?.matches(n)}))return!1;let r=e.length,n=this._options.mutationLimit,i=this._options.mutationBreadcrumbLimit,a=n&&r>n;if(r>i||a){let e=ob({category:"replay.mutations",data:{count:r,limit:a}});this._createCustomBreadcrumb(e)}return!a||(this.stop({reason:"mutationLimit",forceFlush:"session"===this.recordingMode}),!1)}}function lb(e,t){return[...e,...t].join(",")}let lE='img,image,svg,video,object,picture,embed,map,audio,link[rel="icon"],link[rel="apple-touch-icon"]',lS=["content-length","content-type","accept"],lw=!1;class lR{constructor({flushMinDelay:e=5e3,flushMaxDelay:t=5500,minReplayDuration:r=4999,maxReplayDuration:n=36e5,stickySession:i=!0,useCompression:a=!0,workerUrl:s,_experiments:o={},maskAllText:l=!0,maskAllInputs:u=!0,blockAllMedia:c=!0,mutationBreadcrumbLimit:d=750,mutationLimit:p=1e4,slowClickTimeout:h=7e3,slowClickIgnoreSelectors:f=[],networkDetailAllowUrls:m=[],networkDetailDenyUrls:g=[],networkCaptureBodies:_=!0,networkRequestHeaders:y=[],networkResponseHeaders:v=[],mask:b=[],maskAttributes:E=["title","placeholder","aria-label"],unmask:S=[],block:w=[],unblock:R=[],ignore:P=[],maskFn:T,beforeAddRecordingEvent:x,beforeErrorSampling:O,onError:I}={}){this.name="Replay";let C=function({mask:e,unmask:t,block:r,unblock:n,ignore:i}){return{maskTextSelector:lb(e,[".sentry-mask","[data-sentry-mask]"]),unmaskTextSelector:lb(t,[]),blockSelector:lb(r,[".sentry-block","[data-sentry-block]","base","iframe[srcdoc]:not([src])"]),unblockSelector:lb(n,[]),ignoreSelector:lb(i,[".sentry-ignore","[data-sentry-ignore]",'input[type="file"]'])}}({mask:b,unmask:S,block:w,unblock:R,ignore:P});if(this._recordingOptions={maskAllInputs:u,maskAllText:l,maskInputOptions:{password:!0},maskTextFn:T,maskInputFn:T,maskAttributeFn:(e,t,r)=>(function({el:e,key:t,maskAttributes:r,maskAllText:n,privacyOptions:i,value:a}){return!n||i.unmaskTextSelector&&e.matches(i.unmaskTextSelector)?a:r.includes(t)||"value"===t&&"INPUT"===e.tagName&&["submit","button"].includes(e.getAttribute("type")||"")?a.replace(/[\S]/g,"*"):a})({maskAttributes:E,maskAllText:l,privacyOptions:C,key:e,value:t,el:r}),...C,slimDOMOptions:"all",inlineStylesheet:!0,inlineImages:!1,collectFonts:!0,errorHandler:e=>{try{e.__rrweb__=!0}catch{}},recordCrossOriginIframes:!!o.recordCrossOriginIframes},this._initialOptions={flushMinDelay:e,flushMaxDelay:t,minReplayDuration:Math.min(r,15e3),maxReplayDuration:Math.min(n,36e5),stickySession:i,useCompression:a,workerUrl:s,blockAllMedia:c,maskAllInputs:u,maskAllText:l,mutationBreadcrumbLimit:d,mutationLimit:p,slowClickTimeout:h,slowClickIgnoreSelectors:f,networkDetailAllowUrls:m,networkDetailDenyUrls:g,networkCaptureBodies:_,networkRequestHeaders:lP(y),networkResponseHeaders:lP(v),beforeAddRecordingEvent:x,beforeErrorSampling:O,onError:I,_experiments:o},this._initialOptions.blockAllMedia&&(this._recordingOptions.blockSelector=this._recordingOptions.blockSelector?`${this._recordingOptions.blockSelector},${lE}`:lE),this._isInitialized&&aI())throw Error("Multiple Sentry Session Replay instances are not supported");this._isInitialized=!0}get _isInitialized(){return lw}set _isInitialized(e){lw=e}afterAllSetup(e){aI()&&!this._replay&&(this._setup(e),this._initialize(e))}start(){this._replay&&this._replay.start()}startBuffering(){this._replay&&this._replay.startBuffering()}stop(){return this._replay?this._replay.stop({forceFlush:"session"===this._replay.recordingMode}):Promise.resolve()}flush(e){return this._replay?this._replay.isEnabled()?this._replay.sendBufferedReplayOrFlush(e):(this._replay.start(),Promise.resolve()):Promise.resolve()}getReplayId(){if(this._replay?.isEnabled())return this._replay.getSessionId()}getRecordingMode(){if(this._replay?.isEnabled())return this._replay.recordingMode}_initialize(e){this._replay&&(this._maybeLoadFromReplayCanvasIntegration(e),this._replay.initializeSampling())}_setup(e){let t=function(e,t){let r=t.getOptions(),n={sessionSampleRate:0,errorSampleRate:0,...e},i=ta(r.replaysSessionSampleRate),a=ta(r.replaysOnErrorSampleRate);return null==i&&null==a&&D(()=>{console.warn("Replay is disabled because neither `replaysSessionSampleRate` nor `replaysOnErrorSampleRate` are set.")}),null!=i&&(n.sessionSampleRate=i),null!=a&&(n.errorSampleRate=a),n}(this._initialOptions,e);this._replay=new lv({options:t,recordingOptions:this._recordingOptions})}_maybeLoadFromReplayCanvasIntegration(e){try{let t=e.getIntegrationByName("ReplayCanvas");if(!t)return;this._replay._canvas=t.getOptions()}catch{}}}function lP(e){return[...lS,...e.map(e=>e.toLowerCase())]}var lT=r(65364);globalThis._sentryRewritesTunnelPath="/monitoring",globalThis.SENTRY_RELEASE={id:"4f114066cfffc2184b89e81ba9495ab6e8e5cf06"},globalThis._sentryBasePath=void 0,globalThis._sentryRewriteFramesAssetPrefixPath="",globalThis._sentryAssetPrefix=void 0,globalThis._sentryExperimentalThirdPartyOriginStackFrames=void 0,globalThis._sentryRouteManifest='{"dynamicRoutes":[{"path":"/my-courses/:courseId","regex":"^/my-courses/([^/]+)$","paramNames":["courseId"]},{"path":"/my-courses/:courseId/exam","regex":"^/my-courses/([^/]+)/exam$","paramNames":["courseId"]},{"path":"/my-courses/:courseId/exam/results","regex":"^/my-courses/([^/]+)/exam/results$","paramNames":["courseId"]},{"path":"/auth/sign-in/:sign-in*?","regex":"^/auth/sign-in(?:/(.*))?$","paramNames":["sign-in"]},{"path":"/auth/sign-up/:sign-up*?","regex":"^/auth/sign-up(?:/(.*))?$","paramNames":["sign-up"]},{"path":"/dashboard/admin/institutions/:id","regex":"^/dashboard/admin/institutions/([^/]+)$","paramNames":["id"]},{"path":"/dashboard/admin/users/:id","regex":"^/dashboard/admin/users/([^/]+)$","paramNames":["id"]},{"path":"/dashboard/product/:productId","regex":"^/dashboard/product/([^/]+)$","paramNames":["productId"]},{"path":"/dashboard/profile/:profile*?","regex":"^/dashboard/profile(?:/(.*))?$","paramNames":["profile"]},{"path":"/dashboard/student/courses/:id","regex":"^/dashboard/student/courses/([^/]+)$","paramNames":["id"]},{"path":"/dashboard/teacher/classes/:id","regex":"^/dashboard/teacher/classes/([^/]+)$","paramNames":["id"]},{"path":"/dashboard/teacher/classes/:id/courses","regex":"^/dashboard/teacher/classes/([^/]+)/courses$","paramNames":["id"]},{"path":"/dashboard/teacher/classes/:id/students","regex":"^/dashboard/teacher/classes/([^/]+)/students$","paramNames":["id"]},{"path":"/dashboard/teacher/courses/:id","regex":"^/dashboard/teacher/courses/([^/]+)$","paramNames":["id"]}],"staticRoutes":[{"path":"/"},{"path":"/courses"},{"path":"/my-courses"},{"path":"/dashboard"},{"path":"/dashboard/admin"},{"path":"/dashboard/admin/institutions"},{"path":"/dashboard/admin/institutions/new"},{"path":"/dashboard/admin/subscriptions"},{"path":"/dashboard/admin/users"},{"path":"/dashboard/admin/users/new"},{"path":"/dashboard/kanban"},{"path":"/dashboard/overview/@area_stats"},{"path":"/dashboard/overview/@bar_stats"},{"path":"/dashboard/overview/@pie_stats"},{"path":"/dashboard/overview/@sales"},{"path":"/dashboard/product"},{"path":"/dashboard/student"},{"path":"/dashboard/student/certificates"},{"path":"/dashboard/student/courses"},{"path":"/dashboard/student/progress"},{"path":"/dashboard/teacher"},{"path":"/dashboard/teacher/classes"},{"path":"/dashboard/teacher/classes/new"},{"path":"/dashboard/teacher/courses"},{"path":"/dashboard/teacher/courses/generate"},{"path":"/dashboard/teacher/courses/new"},{"path":"/dashboard/teacher/reports"}]}',lT.env.NEXT_PUBLIC_SENTRY_DISABLED||function(e){aT&&D(()=>{console.warn("[@sentry/nextjs] You are calling `Sentry.init()` more than once on the client. This can happen if you have both a `sentry.client.config.ts` and a `instrumentation-client.ts` file with `Sentry.init()` calls. It is recommended to call `Sentry.init()` once in `instrumentation-client.ts`.")}),aT=!0;let t={environment:function(e){let t=e?nR.env.NEXT_PUBLIC_VERCEL_ENV:nR.env.VERCEL_ENV;return t?`vercel-${t}`:void 0}(!0)||"production",defaultIntegrations:function(e){let t=nE(e);("undefined"==typeof __SENTRY_TRACING__||__SENTRY_TRACING__)&&t.push(function(e={}){let t=ar({...e,instrumentNavigation:!1,instrumentPageLoad:!1,onRequestSpanStart(...t){let[r,{headers:n}]=t;return n?.get("next-router-prefetch")&&r?.setAttribute("http.request.prefetch",!0),e.onRequestSpanStart?.(...t)}}),{instrumentPageLoad:r=!0,instrumentNavigation:n=!0}=e;return{...t,afterAllSetup(e){n&&function(e){if(C.document.getElementById("__NEXT_DATA__"))av.events.on("routeChangeStart",t=>{let r,n,i=r$(t),a=function(e){let t=C.__BUILD_MANIFEST?.sortedPages;if(t)return t.find(t=>{let r=function(e){let t=e.split("/"),r="";t[t.length-1]?.match(/^\[\[\.\.\..+\]\]$/)&&(t.pop(),r="(?:/(.+?))?");let n=t.map(e=>e.replace(/^\[\.\.\..+\]$/,"(.+?)").replace(/^\[.*\]$/,"([^/]+?)")).join("/");return RegExp(`^${n}${r}(?:/)?$`)}(t);return e.match(r)})}(i);a?(r=a,n="route"):(r=i,n="url"),ai(e,{name:r,attributes:{[eX]:"navigation",[eG]:"auto.navigation.nextjs.pages_router_instrumentation",[eW]:n}})});else{O=(t,r)=>{let n=new URL(t,C.location.href).pathname,i=ad(n),a=i??n;"router-patch"===ah&&(ah="transition-start-hook");let s=af.current;s?(s.updateName(a),s.setAttributes({"navigation.type":`router.${r}`,[eW]:i?"route":"url"}),af.current=void 0):ai(e,{name:a,attributes:{[eX]:"navigation",[eG]:"auto.navigation.nextjs.app_router_instrumentation",[eW]:i?"route":"url","navigation.type":`router.${r}`}})},C.addEventListener("popstate",()=>{let t=ad(C.location.pathname);af.current?.isRecording()?(af.current.updateName(t??C.location.pathname),af.current.setAttribute(eW,t?"route":"url")):af.current=ai(e,{name:t??C.location.pathname,attributes:{[eG]:"auto.navigation.nextjs.app_router_instrumentation",[eW]:t?"route":"url","navigation.type":"browser.popstate"}})});let t=!1,r=0,n=setInterval(()=>{r++;let i=C?.next?.router??C?.nd?.router;t||r>500?clearInterval(n):i&&(clearInterval(n),t=!0,a_(e,i,af),["nd","next"].forEach(t=>{let r=C[t];r&&(C[t]=new Proxy(r,{set:(t,r,n)=>("router"===r&&"object"==typeof n&&null!==n&&a_(e,n,af),t[r]=n,!0)}))}))},20)}}(e),t.afterAllSetup(e),r&&function(e){if(C.document.getElementById("__NEXT_DATA__")){let{route:t,params:r,sentryTrace:n,baggage:i}=function(){let e,t=C.document.getElementById("__NEXT_DATA__");if(t?.innerHTML)try{e=JSON.parse(t.innerHTML)}catch{}if(!e)return{};let r={},{page:n,query:i,props:a}=e;return r.route=n,r.params=i,a?.pageProps&&(r.sentryTrace=a.pageProps._sentryTraceData,r.baggage=a.pageProps._sentryBaggage),r}(),a=e3(i),s=t||C.location.pathname;a?.["sentry-transaction"]&&"/_error"===s&&(s=(s=a["sentry-transaction"]).replace(/^(GET|POST|PUT|DELETE|PATCH|HEAD|OPTIONS|TRACE|CONNECT)\s+/i,""));let o=eS();an(e,{name:s,startTime:o?o/1e3:void 0,attributes:{[eX]:"pageload",[eG]:"auto.pageload.nextjs.pages_router_instrumentation",[eW]:t?"route":"url",...r&&e.getOptions().sendDefaultPii&&{...r}}},{sentryTrace:n,baggage:i})}else{let t=ad(C.location.pathname),r=eS();an(e,{name:t??C.location.pathname,startTime:r?r/1e3:void 0,attributes:{[eX]:"pageload",[eG]:"auto.pageload.nextjs.app_router_instrumentation",[eW]:t?"route":"url"}})}}(e)}}}());let r=C._sentryRewriteFramesAssetPrefixPath||"",n=aP.env._sentryAssetPrefix||C._sentryAssetPrefix,i=aP.env._sentryBasePath||C._sentryBasePath,a="true"===aP.env._experimentalThirdPartyOriginStackFrames||"true"===C._experimentalThirdPartyOriginStackFrames;return t.push(aR({assetPrefix:n,basePath:i,rewriteFramesAssetPrefixPath:r,experimentalThirdPartyOriginStackFrames:a})),t}(e),release:"4f114066cfffc2184b89e81ba9495ab6e8e5cf06",...e};!function(e){let t="/monitoring";if(t&&e.dsn){let r=e7(e.dsn);if(!r)return;let n=r.host.match(/^o(\d+)\.ingest(?:\.([a-z]{2}))?\.sentry\.io$/);if(n){let i=n[1],a=n[2],s=`${t}?o=${i}&p=${r.projectId}`;a&&(s+=`&r=${a}`),e.tunnel=s}}}(t),$(t,"nextjs",["nextjs","react"]),function(e){let t={...e};$(t,"react"),tF("react",{version:nw.version}),function(e={}){var t,r;let n=!e.skipBrowserExtensionCheck&&!!function(){if(void 0===C.window||C.nw)return!1;let e=C.chrome||C.browser;if(!e?.runtime?.id)return!1;let t=en();return!(C===C.top&&["chrome-extension","moz-extension","ms-browser-extension","safari-web-extension"].some(e=>t.startsWith(`${e}://`)))}(),i={...e,enabled:!n&&e.enabled,stackParser:Array.isArray(t=e.stackParser||np)?tC(...t):t,integrations:function(e){let t,r=e.defaultIntegrations||[],n=e.integrations;if(r.forEach(e=>{e.isDefaultInstance=!0}),Array.isArray(n))t=[...r,...n];else if("function"==typeof n){let e=n(r);t=Array.isArray(e)?e:[e]}else t=r;let i={};return t.forEach(e=>{let{name:t}=e,r=i[t];r&&!r.isDefaultInstance&&e.isDefaultInstance||(i[t]=e)}),Object.values(i)}({integrations:e.integrations,defaultIntegrations:null==e.defaultIntegrations?nE():e.defaultIntegrations}),transport:e.transport||nb};!0===i.debug&&D(()=>{console.warn("[Sentry] Cannot initialize SDK with `debug` option using a non-debug bundle.")}),ej().update(i.initialScope);let a=new rS(i);r=a,ej().setClient(r),a.init()}(t)}(t);let r=e=>"transaction"===e.type&&"/404"===e.transaction?null:e;r.id="NextClient404Filter",tB(r);let n=e=>"transaction"===e.type&&e.transaction===ap?null:e;n.id="IncompleteTransactionFilter",tB(n);let i=(e,t)=>{var r;return W(r=t?.originalException)&&"string"==typeof r.digest&&r.digest.startsWith("NEXT_REDIRECT;")||e.exception?.values?.[0]?.value==="NEXT_REDIRECT"?null:e};i.id="NextRedirectErrorFilter",tB(i)}({dsn:lT.env.NEXT_PUBLIC_SENTRY_DSN,integrations:[new lR(I)],sendDefaultPii:!0,tracesSampleRate:1,replaysSessionSampleRate:.1,replaysOnErrorSampleRate:1,debug:!1});let lx=function(e,t){O&&O(e,t)}},92860:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hasBasePath",{enumerable:!0,get:function(){return i}});let n=r(321);function i(e){return(0,n.pathHasPrefix)(e,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},93309:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{APP_BUILD_MANIFEST:function(){return v},APP_CLIENT_INTERNALS:function(){return J},APP_PATHS_MANIFEST:function(){return g},APP_PATH_ROUTES_MANIFEST:function(){return _},BARREL_OPTIMIZATION_PREFIX:function(){return H},BLOCKED_PAGES:function(){return D},BUILD_ID_FILE:function(){return L},BUILD_MANIFEST:function(){return y},CLIENT_PUBLIC_FILES_PATH:function(){return U},CLIENT_REFERENCE_MANIFEST:function(){return W},CLIENT_STATIC_FILES_PATH:function(){return F},CLIENT_STATIC_FILES_RUNTIME_AMP:function(){return Z},CLIENT_STATIC_FILES_RUNTIME_MAIN:function(){return Y},CLIENT_STATIC_FILES_RUNTIME_MAIN_APP:function(){return K},CLIENT_STATIC_FILES_RUNTIME_POLYFILLS:function(){return et},CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL:function(){return er},CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH:function(){return Q},CLIENT_STATIC_FILES_RUNTIME_WEBPACK:function(){return ee},COMPILER_INDEXES:function(){return a},COMPILER_NAMES:function(){return i},CONFIG_FILES:function(){return j},DEFAULT_RUNTIME_WEBPACK:function(){return en},DEFAULT_SANS_SERIF_FONT:function(){return el},DEFAULT_SERIF_FONT:function(){return eo},DEV_CLIENT_MIDDLEWARE_MANIFEST:function(){return k},DEV_CLIENT_PAGES_MANIFEST:function(){return I},DYNAMIC_CSS_MANIFEST:function(){return V},EDGE_RUNTIME_WEBPACK:function(){return ei},EDGE_UNSUPPORTED_NODE_APIS:function(){return eh},EXPORT_DETAIL:function(){return R},EXPORT_MARKER:function(){return w},FUNCTIONS_CONFIG_MANIFEST:function(){return b},IMAGES_MANIFEST:function(){return x},INTERCEPTION_ROUTE_REWRITE_MANIFEST:function(){return G},MIDDLEWARE_BUILD_MANIFEST:function(){return z},MIDDLEWARE_MANIFEST:function(){return C},MIDDLEWARE_REACT_LOADABLE_MANIFEST:function(){return X},MODERN_BROWSERSLIST_TARGET:function(){return n.default},NEXT_BUILTIN_DOCUMENT:function(){return $},NEXT_FONT_MANIFEST:function(){return S},PAGES_MANIFEST:function(){return f},PHASE_DEVELOPMENT_SERVER:function(){return d},PHASE_EXPORT:function(){return l},PHASE_INFO:function(){return h},PHASE_PRODUCTION_BUILD:function(){return u},PHASE_PRODUCTION_SERVER:function(){return c},PHASE_TEST:function(){return p},PRERENDER_MANIFEST:function(){return P},REACT_LOADABLE_MANIFEST:function(){return A},ROUTES_MANIFEST:function(){return T},RSC_MODULE_TYPES:function(){return ep},SERVER_DIRECTORY:function(){return N},SERVER_FILES_MANIFEST:function(){return O},SERVER_PROPS_ID:function(){return es},SERVER_REFERENCE_MANIFEST:function(){return q},STATIC_PROPS_ID:function(){return ea},STATIC_STATUS_PAGES:function(){return eu},STRING_LITERAL_DROP_BUNDLE:function(){return B},SUBRESOURCE_INTEGRITY_MANIFEST:function(){return E},SYSTEM_ENTRYPOINTS:function(){return ef},TRACE_OUTPUT_VERSION:function(){return ec},TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST:function(){return M},TURBO_TRACE_DEFAULT_MEMORY_LIMIT:function(){return ed},UNDERSCORE_NOT_FOUND_ROUTE:function(){return s},UNDERSCORE_NOT_FOUND_ROUTE_ENTRY:function(){return o},WEBPACK_STATS:function(){return m}});let n=r(72687)._(r(50563)),i={client:"client",server:"server",edgeServer:"edge-server"},a={[i.client]:0,[i.server]:1,[i.edgeServer]:2},s="/_not-found",o=""+s+"/page",l="phase-export",u="phase-production-build",c="phase-production-server",d="phase-development-server",p="phase-test",h="phase-info",f="pages-manifest.json",m="webpack-stats.json",g="app-paths-manifest.json",_="app-path-routes-manifest.json",y="build-manifest.json",v="app-build-manifest.json",b="functions-config-manifest.json",E="subresource-integrity-manifest",S="next-font-manifest",w="export-marker.json",R="export-detail.json",P="prerender-manifest.json",T="routes-manifest.json",x="images-manifest.json",O="required-server-files.json",I="_devPagesManifest.json",C="middleware-manifest.json",M="_clientMiddlewareManifest.json",k="_devMiddlewareManifest.json",A="react-loadable-manifest.json",N="server",j=["next.config.js","next.config.mjs","next.config.ts"],L="BUILD_ID",D=["/_document","/_app","/_error"],U="public",F="static",B="__NEXT_DROP_CLIENT_FILE__",$="__NEXT_BUILTIN_DOCUMENT__",H="__barrel_optimize__",W="client-reference-manifest",q="server-reference-manifest",z="middleware-build-manifest",X="middleware-react-loadable-manifest",G="interception-route-rewrite-manifest",V="dynamic-css-manifest",Y="main",K=""+Y+"-app",J="app-pages-internals",Q="react-refresh",Z="amp",ee="webpack",et="polyfills",er=Symbol(et),en="webpack-runtime",ei="edge-runtime-webpack",ea="__N_SSG",es="__N_SSP",eo={name:"Times New Roman",xAvgCharWidth:821,azAvgWidth:854.3953488372093,unitsPerEm:2048},el={name:"Arial",xAvgCharWidth:904,azAvgWidth:934.5116279069767,unitsPerEm:2048},eu=["/500"],ec=1,ed=6e3,ep={client:"client",server:"server"},eh=["clearImmediate","setImmediate","BroadcastChannel","ByteLengthQueuingStrategy","CompressionStream","CountQueuingStrategy","DecompressionStream","DomException","MessageChannel","MessageEvent","MessagePort","ReadableByteStreamController","ReadableStreamBYOBRequest","ReadableStreamDefaultController","TransformStreamDefaultController","WritableStreamDefaultController"],ef=new Set([Y,Q,Z,K]);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},94387:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseUrl",{enumerable:!0,get:function(){return a}});let n=r(67825),i=r(5402);function a(e){if(e.startsWith("/"))return(0,i.parseRelativeUrl)(e);let t=new URL(e);return{hash:t.hash,hostname:t.hostname,href:t.href,pathname:t.pathname,port:t.port,protocol:t.protocol,query:(0,n.searchParamsToUrlQuery)(t.searchParams),search:t.search}}},98091:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removePathPrefix",{enumerable:!0,get:function(){return i}});let n=r(321);function i(e,t){if(!(0,n.pathHasPrefix)(e,t))return e;let r=e.slice(t.length);return r.startsWith("/")?r:"/"+r}},99328:(e,t)=>{"use strict";function r(e,t){let r=Object.keys(e);if(r.length!==Object.keys(t).length)return!1;for(let n=r.length;n--;){let i=r[n];if("query"===i){let r=Object.keys(e.query);if(r.length!==Object.keys(t.query).length)return!1;for(let n=r.length;n--;){let i=r[n];if(!t.query.hasOwnProperty(i)||e.query[i]!==t.query[i])return!1}}else if(!t.hasOwnProperty(i)||e[i]!==t[i])return!1}return!0}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"compareRouterStates",{enumerable:!0,get:function(){return r}})}},e=>{var t=t=>e(e.s=t);e.O(0,[6593],()=>t(46175)),_N_E=e.O()}]);