try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="597c9132-27a5-4a5b-908e-e2b70152b24f",e._sentryDebugIdIdentifier="sentry-dbid-597c9132-27a5-4a5b-908e-e2b70152b24f")}catch(e){}exports.id=1481,exports.ids=[1481],exports.modules={339:function(e,t,n){"use strict";var r=(this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}})(n(4203)),i=n(35011);function l(e,t){var n={};return e&&"string"==typeof e&&(0,r.default)(e,function(e,r){e&&r&&(n[(0,i.camelCase)(e,t)]=r)}),n}l.default=l,e.exports=l},4203:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var n=null;if(!e||"string"!=typeof e)return n;var r=(0,i.default)(e),l="function"==typeof t;return r.forEach(function(e){if("declaration"===e.type){var r=e.property,i=e.value;l?t(r,i,e):i&&((n=n||{})[r]=i)}}),n};var i=r(n(14154))},14154:e=>{var t=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,n=/\n/g,r=/^\s*/,i=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,l=/^:\s*/,o=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,a=/^[;\s]*/,s=/^\s+|\s+$/g;function u(e){return e?e.replace(s,""):""}e.exports=function(e,s){if("string"!=typeof e)throw TypeError("First argument must be a string");if(!e)return[];s=s||{};var c=1,f=1;function p(e){var t=e.match(n);t&&(c+=t.length);var r=e.lastIndexOf("\n");f=~r?e.length-r:f+e.length}function d(){var e={line:c,column:f};return function(t){return t.position=new h(e),y(r),t}}function h(e){this.start=e,this.end={line:c,column:f},this.source=s.source}h.prototype.content=e;var m=[];function g(t){var n=Error(s.source+":"+c+":"+f+": "+t);if(n.reason=t,n.filename=s.source,n.line=c,n.column=f,n.source=e,s.silent)m.push(n);else throw n}function y(t){var n=t.exec(e);if(n){var r=n[0];return p(r),e=e.slice(r.length),n}}function b(e){var t;for(e=e||[];t=v();)!1!==t&&e.push(t);return e}function v(){var t=d();if("/"==e.charAt(0)&&"*"==e.charAt(1)){for(var n=2;""!=e.charAt(n)&&("*"!=e.charAt(n)||"/"!=e.charAt(n+1));)++n;if(n+=2,""===e.charAt(n-1))return g("End of comment missing");var r=e.slice(2,n-2);return f+=2,p(r),e=e.slice(n),f+=2,t({type:"comment",comment:r})}}y(r);var k,x=[];for(b(x);k=function(){var e=d(),n=y(i);if(n){if(v(),!y(l))return g("property missing ':'");var r=y(o),s=e({type:"declaration",property:u(n[0].replace(t,"")),value:r?u(r[0].replace(t,"")):""});return y(a),s}}();)!1!==k&&(x.push(k),b(x));return x}},27870:(e,t,n)=>{"use strict";n.d(t,{B:()=>l});var r=n(32146),i=n(64071);let l={partial:!0,tokenize:function(e,t,n){return function(t){return(0,i.On)(t)?(0,r.N)(e,l,"linePrefix")(t):l(t)};function l(e){return null===e||(0,i.HP)(e)?t(e):n(e)}}}},31535:(e,t,n)=>{"use strict";function r(e,t,n,r){let i,l=e.length,o=0;if(t=t<0?-t>l?0:l+t:t>l?l:t,n=n>0?n:0,r.length<1e4)(i=Array.from(r)).unshift(t,n),e.splice(...i);else for(n&&e.splice(t,n);o<r.length;)(i=r.slice(o,o+1e4)).unshift(t,0),e.splice(...i),o+=1e4,t+=1e4}function i(e,t){return e.length>0?(r(e,e.length,0,t),e):t}n.d(t,{V:()=>i,m:()=>r})},32146:(e,t,n)=>{"use strict";n.d(t,{N:()=>i});var r=n(64071);function i(e,t,n,i){let l=i?i-1:Number.POSITIVE_INFINITY,o=0;return function(i){return(0,r.On)(i)?(e.enter(n),function i(a){return(0,r.On)(a)&&o++<l?(e.consume(a),i):(e.exit(n),t(a))}(i)):t(i)}}},35011:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.camelCase=void 0;var n=/^--[a-zA-Z0-9_-]+$/,r=/-([a-z])/g,i=/^[^-]+$/,l=/^-(webkit|moz|ms|o|khtml)-/,o=/^-(ms)-/,a=function(e,t){return t.toUpperCase()},s=function(e,t){return"".concat(t,"-")};t.camelCase=function(e,t){var u;return(void 0===t&&(t={}),!(u=e)||i.test(u)||n.test(u))?e:(e=e.toLowerCase(),(e=t.reactCompat?e.replace(o,s):e.replace(l,s)).replace(r,a))}},35990:(e,t,n)=>{"use strict";function r(e,t,n){let r=[],i=-1;for(;++i<e.length;){let l=e[i].resolveAll;l&&!r.includes(l)&&(t=l(t,n),r.push(l))}return t}n.d(t,{W:()=>r})},36991:(e,t,n)=>{"use strict";n.d(t,{B8:()=>L,UC:()=>I,bL:()=>A,l9:()=>P});var r=n(93491),i=n(18682),l=n(10158),o=n(92023),a=n(55462),s=n(90604),u=n(78283),c=n(76322),f=n(62962),p=n(91754),d="Tabs",[h,m]=(0,l.A)(d,[o.RG]),g=(0,o.RG)(),[y,b]=h(d),v=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,onValueChange:i,defaultValue:l,orientation:o="horizontal",dir:a,activationMode:h="automatic",...m}=e,g=(0,u.jH)(a),[b,v]=(0,c.i)({prop:r,onChange:i,defaultProp:l??"",caller:d});return(0,p.jsx)(y,{scope:n,baseId:(0,f.B)(),value:b,onValueChange:v,orientation:o,dir:g,activationMode:h,children:(0,p.jsx)(s.sG.div,{dir:g,"data-orientation":o,...m,ref:t})})});v.displayName=d;var k="TabsList",x=r.forwardRef((e,t)=>{let{__scopeTabs:n,loop:r=!0,...i}=e,l=b(k,n),a=g(n);return(0,p.jsx)(o.bL,{asChild:!0,...a,orientation:l.orientation,dir:l.dir,loop:r,children:(0,p.jsx)(s.sG.div,{role:"tablist","aria-orientation":l.orientation,...i,ref:t})})});x.displayName=k;var w="TabsTrigger",S=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,disabled:l=!1,...a}=e,u=b(w,n),c=g(n),f=D(u.baseId,r),d=T(u.baseId,r),h=r===u.value;return(0,p.jsx)(o.q7,{asChild:!0,...c,focusable:!l,active:h,children:(0,p.jsx)(s.sG.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":d,"data-state":h?"active":"inactive","data-disabled":l?"":void 0,disabled:l,id:f,...a,ref:t,onMouseDown:(0,i.m)(e.onMouseDown,e=>{l||0!==e.button||!1!==e.ctrlKey?e.preventDefault():u.onValueChange(r)}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&u.onValueChange(r)}),onFocus:(0,i.m)(e.onFocus,()=>{let e="manual"!==u.activationMode;h||l||!e||u.onValueChange(r)})})})});S.displayName=w;var C="TabsContent",E=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:i,forceMount:l,children:o,...u}=e,c=b(C,n),f=D(c.baseId,i),d=T(c.baseId,i),h=i===c.value,m=r.useRef(h);return r.useEffect(()=>{let e=requestAnimationFrame(()=>m.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,p.jsx)(a.C,{present:l||h,children:({present:n})=>(0,p.jsx)(s.sG.div,{"data-state":h?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":f,hidden:!n,id:d,tabIndex:0,...u,ref:t,style:{...e.style,animationDuration:m.current?"0s":void 0},children:n&&o})})});function D(e,t){return`${e}-trigger-${t}`}function T(e,t){return`${e}-content-${t}`}E.displayName=C;var A=v,L=x,P=S,I=E},40515:(e,t,n)=>{"use strict";n.d(t,{oz:()=>nd});var r={};n.r(r),n.d(r,{boolean:()=>y,booleanish:()=>b,commaOrSpaceSeparated:()=>S,commaSeparated:()=>w,number:()=>k,overloadedBoolean:()=>v,spaceSeparated:()=>x});var i={};n.r(i),n.d(i,{attentionMarkers:()=>th,contentInitial:()=>ts,disable:()=>tm,document:()=>ta,flow:()=>tc,flowInitial:()=>tu,insideSpan:()=>td,string:()=>tf,text:()=>tp});var l=n(46418);let o=/^[$_\p{ID_Start}][$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,a=/^[$_\p{ID_Start}][-$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,s={};function u(e,t){return((t||s).jsx?a:o).test(e)}let c=/[ \t\n\f\r]/g;function f(e){return""===e.replace(c,"")}class p{constructor(e,t,n){this.normal=t,this.property=e,n&&(this.space=n)}}function d(e,t){let n={},r={};for(let t of e)Object.assign(n,t.property),Object.assign(r,t.normal);return new p(n,r,t)}function h(e){return e.toLowerCase()}p.prototype.normal={},p.prototype.property={},p.prototype.space=void 0;class m{constructor(e,t){this.attribute=t,this.property=e}}m.prototype.attribute="",m.prototype.booleanish=!1,m.prototype.boolean=!1,m.prototype.commaOrSpaceSeparated=!1,m.prototype.commaSeparated=!1,m.prototype.defined=!1,m.prototype.mustUseProperty=!1,m.prototype.number=!1,m.prototype.overloadedBoolean=!1,m.prototype.property="",m.prototype.spaceSeparated=!1,m.prototype.space=void 0;let g=0,y=C(),b=C(),v=C(),k=C(),x=C(),w=C(),S=C();function C(){return 2**++g}let E=Object.keys(r);class D extends m{constructor(e,t,n,i){let l=-1;if(super(e,t),function(e,t,n){n&&(e[t]=n)}(this,"space",i),"number"==typeof n)for(;++l<E.length;){let e=E[l];!function(e,t,n){n&&(e[t]=n)}(this,E[l],(n&r[e])===r[e])}}}function T(e){let t={},n={};for(let[r,i]of Object.entries(e.properties)){let l=new D(r,e.transform(e.attributes||{},r),i,e.space);e.mustUseProperty&&e.mustUseProperty.includes(r)&&(l.mustUseProperty=!0),t[r]=l,n[h(r)]=r,n[h(l.attribute)]=r}return new p(t,n,e.space)}D.prototype.defined=!0;let A=T({properties:{ariaActiveDescendant:null,ariaAtomic:b,ariaAutoComplete:null,ariaBusy:b,ariaChecked:b,ariaColCount:k,ariaColIndex:k,ariaColSpan:k,ariaControls:x,ariaCurrent:null,ariaDescribedBy:x,ariaDetails:null,ariaDisabled:b,ariaDropEffect:x,ariaErrorMessage:null,ariaExpanded:b,ariaFlowTo:x,ariaGrabbed:b,ariaHasPopup:null,ariaHidden:b,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:x,ariaLevel:k,ariaLive:null,ariaModal:b,ariaMultiLine:b,ariaMultiSelectable:b,ariaOrientation:null,ariaOwns:x,ariaPlaceholder:null,ariaPosInSet:k,ariaPressed:b,ariaReadOnly:b,ariaRelevant:null,ariaRequired:b,ariaRoleDescription:x,ariaRowCount:k,ariaRowIndex:k,ariaRowSpan:k,ariaSelected:b,ariaSetSize:k,ariaSort:null,ariaValueMax:k,ariaValueMin:k,ariaValueNow:k,ariaValueText:null,role:null},transform:(e,t)=>"role"===t?t:"aria-"+t.slice(4).toLowerCase()});function L(e,t){return t in e?e[t]:t}function P(e,t){return L(e,t.toLowerCase())}let I=T({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:w,acceptCharset:x,accessKey:x,action:null,allow:null,allowFullScreen:y,allowPaymentRequest:y,allowUserMedia:y,alt:null,as:null,async:y,autoCapitalize:null,autoComplete:x,autoFocus:y,autoPlay:y,blocking:x,capture:null,charSet:null,checked:y,cite:null,className:x,cols:k,colSpan:null,content:null,contentEditable:b,controls:y,controlsList:x,coords:k|w,crossOrigin:null,data:null,dateTime:null,decoding:null,default:y,defer:y,dir:null,dirName:null,disabled:y,download:v,draggable:b,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:y,formTarget:null,headers:x,height:k,hidden:v,high:k,href:null,hrefLang:null,htmlFor:x,httpEquiv:x,id:null,imageSizes:null,imageSrcSet:null,inert:y,inputMode:null,integrity:null,is:null,isMap:y,itemId:null,itemProp:x,itemRef:x,itemScope:y,itemType:x,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:y,low:k,manifest:null,max:null,maxLength:k,media:null,method:null,min:null,minLength:k,multiple:y,muted:y,name:null,nonce:null,noModule:y,noValidate:y,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:y,optimum:k,pattern:null,ping:x,placeholder:null,playsInline:y,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:y,referrerPolicy:null,rel:x,required:y,reversed:y,rows:k,rowSpan:k,sandbox:x,scope:null,scoped:y,seamless:y,selected:y,shadowRootClonable:y,shadowRootDelegatesFocus:y,shadowRootMode:null,shape:null,size:k,sizes:null,slot:null,span:k,spellCheck:b,src:null,srcDoc:null,srcLang:null,srcSet:null,start:k,step:null,style:null,tabIndex:k,target:null,title:null,translate:null,type:null,typeMustMatch:y,useMap:null,value:b,width:k,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:x,axis:null,background:null,bgColor:null,border:k,borderColor:null,bottomMargin:k,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:y,declare:y,event:null,face:null,frame:null,frameBorder:null,hSpace:k,leftMargin:k,link:null,longDesc:null,lowSrc:null,marginHeight:k,marginWidth:k,noResize:y,noHref:y,noShade:y,noWrap:y,object:null,profile:null,prompt:null,rev:null,rightMargin:k,rules:null,scheme:null,scrolling:b,standby:null,summary:null,text:null,topMargin:k,valueType:null,version:null,vAlign:null,vLink:null,vSpace:k,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:y,disableRemotePlayback:y,prefix:null,property:null,results:k,security:null,unselectable:null},space:"html",transform:P}),q=T({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:S,accentHeight:k,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:k,amplitude:k,arabicForm:null,ascent:k,attributeName:null,attributeType:null,azimuth:k,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:k,by:null,calcMode:null,capHeight:k,className:x,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:k,diffuseConstant:k,direction:null,display:null,dur:null,divisor:k,dominantBaseline:null,download:y,dx:null,dy:null,edgeMode:null,editable:null,elevation:k,enableBackground:null,end:null,event:null,exponent:k,externalResourcesRequired:null,fill:null,fillOpacity:k,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:w,g2:w,glyphName:w,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:k,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:k,horizOriginX:k,horizOriginY:k,id:null,ideographic:k,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:k,k:k,k1:k,k2:k,k3:k,k4:k,kernelMatrix:S,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:k,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:k,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:k,overlineThickness:k,paintOrder:null,panose1:null,path:null,pathLength:k,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:x,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:k,pointsAtY:k,pointsAtZ:k,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:S,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:S,rev:S,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:S,requiredFeatures:S,requiredFonts:S,requiredFormats:S,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:k,specularExponent:k,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:k,strikethroughThickness:k,string:null,stroke:null,strokeDashArray:S,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:k,strokeOpacity:k,strokeWidth:null,style:null,surfaceScale:k,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:S,tabIndex:k,tableValues:null,target:null,targetX:k,targetY:k,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:S,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:k,underlineThickness:k,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:k,values:null,vAlphabetic:k,vMathematical:k,vectorEffect:null,vHanging:k,vIdeographic:k,version:null,vertAdvY:k,vertOriginX:k,vertOriginY:k,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:k,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:L}),N=T({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform:(e,t)=>"xlink:"+t.slice(5).toLowerCase()}),O=T({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:P}),R=T({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform:(e,t)=>"xml:"+t.slice(3).toLowerCase()}),F=d([A,I,N,O,R],"html"),z=d([A,q,N,O,R],"svg"),M=/[A-Z]/g,H=/-[a-z]/g,B=/^data[-\w.:]+$/i;function V(e){return"-"+e.toLowerCase()}function j(e){return e.charAt(1).toUpperCase()}let _={classId:"classID",dataType:"datatype",itemId:"itemID",strokeDashArray:"strokeDasharray",strokeDashOffset:"strokeDashoffset",strokeLineCap:"strokeLinecap",strokeLineJoin:"strokeLinejoin",strokeMiterLimit:"strokeMiterlimit",typeOf:"typeof",xLinkActuate:"xlinkActuate",xLinkArcRole:"xlinkArcrole",xLinkHref:"xlinkHref",xLinkRole:"xlinkRole",xLinkShow:"xlinkShow",xLinkTitle:"xlinkTitle",xLinkType:"xlinkType",xmlnsXLink:"xmlnsXlink"};var U=n(339);let W=Y("end"),G=Y("start");function Y(e){return function(t){let n=t&&t.position&&t.position[e]||{};if("number"==typeof n.line&&n.line>0&&"number"==typeof n.column&&n.column>0)return{line:n.line,column:n.column,offset:"number"==typeof n.offset&&n.offset>-1?n.offset:void 0}}}function $(e){return e&&"object"==typeof e?"position"in e||"type"in e?J(e.position):"start"in e||"end"in e?J(e):"line"in e||"column"in e?Q(e):"":""}function Q(e){return K(e&&e.line)+":"+K(e&&e.column)}function J(e){return Q(e&&e.start)+"-"+Q(e&&e.end)}function K(e){return e&&"number"==typeof e?e:1}class Z extends Error{constructor(e,t,n){super(),"string"==typeof t&&(n=t,t=void 0);let r="",i={},l=!1;if(t&&(i="line"in t&&"column"in t||"start"in t&&"end"in t?{place:t}:"type"in t?{ancestors:[t],place:t.position}:{...t}),"string"==typeof e?r=e:!i.cause&&e&&(l=!0,r=e.message,i.cause=e),!i.ruleId&&!i.source&&"string"==typeof n){let e=n.indexOf(":");-1===e?i.ruleId=n:(i.source=n.slice(0,e),i.ruleId=n.slice(e+1))}if(!i.place&&i.ancestors&&i.ancestors){let e=i.ancestors[i.ancestors.length-1];e&&(i.place=e.position)}let o=i.place&&"start"in i.place?i.place.start:i.place;this.ancestors=i.ancestors||void 0,this.cause=i.cause||void 0,this.column=o?o.column:void 0,this.fatal=void 0,this.file="",this.message=r,this.line=o?o.line:void 0,this.name=$(i.place)||"1:1",this.place=i.place||void 0,this.reason=this.message,this.ruleId=i.ruleId||void 0,this.source=i.source||void 0,this.stack=l&&i.cause&&"string"==typeof i.cause.stack?i.cause.stack:"",this.actual=void 0,this.expected=void 0,this.note=void 0,this.url=void 0}}Z.prototype.file="",Z.prototype.name="",Z.prototype.reason="",Z.prototype.message="",Z.prototype.stack="",Z.prototype.column=void 0,Z.prototype.line=void 0,Z.prototype.ancestors=void 0,Z.prototype.cause=void 0,Z.prototype.fatal=void 0,Z.prototype.place=void 0,Z.prototype.ruleId=void 0,Z.prototype.source=void 0;let X={}.hasOwnProperty,ee=new Map,et=/[A-Z]/g,en=new Set(["table","tbody","thead","tfoot","tr"]),er=new Set(["td","th"]),ei="https://github.com/syntax-tree/hast-util-to-jsx-runtime";function el(e,t,n){var r;return"element"===t.type?function(e,t,n){let r=e.schema,i=r;"svg"===t.tagName.toLowerCase()&&"html"===r.space&&(e.schema=z),e.ancestors.push(t);let l=eu(e,t.tagName,!1),o=function(e,t){let n,r,i={};for(r in t.properties)if("children"!==r&&X.call(t.properties,r)){let l=function(e,t,n){let r=function(e,t){let n=h(t),r=t,i=m;if(n in e.normal)return e.property[e.normal[n]];if(n.length>4&&"data"===n.slice(0,4)&&B.test(t)){if("-"===t.charAt(4)){let e=t.slice(5).replace(H,j);r="data"+e.charAt(0).toUpperCase()+e.slice(1)}else{let e=t.slice(4);if(!H.test(e)){let n=e.replace(M,V);"-"!==n.charAt(0)&&(n="-"+n),t="data"+n}}i=D}return new i(r,t)}(e.schema,t);if(!(null==n||"number"==typeof n&&Number.isNaN(n))){if(Array.isArray(n)&&(n=r.commaSeparated?function(e,t){let n={};return(""===e[e.length-1]?[...e,""]:e).join((n.padRight?" ":"")+","+(!1===n.padLeft?"":" ")).trim()}(n):n.join(" ").trim()),"style"===r.property){let t="object"==typeof n?n:function(e,t){try{return U(t,{reactCompat:!0})}catch(n){if(e.ignoreInvalidStyle)return{};let t=new Z("Cannot parse `style` attribute",{ancestors:e.ancestors,cause:n,ruleId:"style",source:"hast-util-to-jsx-runtime"});throw t.file=e.filePath||void 0,t.url=ei+"#cannot-parse-style-attribute",t}}(e,String(n));return"css"===e.stylePropertyNameCase&&(t=function(e){let t,n={};for(t in e)X.call(e,t)&&(n[function(e){let t=e.replace(et,ef);return"ms-"===t.slice(0,3)&&(t="-"+t),t}(t)]=e[t]);return n}(t)),["style",t]}return["react"===e.elementAttributeNameCase&&r.space?_[r.property]||r.property:r.attribute,n]}}(e,r,t.properties[r]);if(l){let[r,o]=l;e.tableCellAlignToStyle&&"align"===r&&"string"==typeof o&&er.has(t.tagName)?n=o:i[r]=o}}return n&&((i.style||(i.style={}))["css"===e.stylePropertyNameCase?"text-align":"textAlign"]=n),i}(e,t),a=es(e,t);return en.has(t.tagName)&&(a=a.filter(function(e){return"string"!=typeof e||!("object"==typeof e?"text"===e.type&&f(e.value):f(e))})),eo(e,o,l,t),ea(o,a),e.ancestors.pop(),e.schema=r,e.create(t,l,o,n)}(e,t,n):"mdxFlowExpression"===t.type||"mdxTextExpression"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater){let n=t.data.estree.body[0];return(0,l.ok)("ExpressionStatement"===n.type),e.evaluater.evaluateExpression(n.expression)}ec(e,t.position)}(e,t):"mdxJsxFlowElement"===t.type||"mdxJsxTextElement"===t.type?function(e,t,n){let r=e.schema,i=r;"svg"===t.name&&"html"===r.space&&(e.schema=z),e.ancestors.push(t);let o=null===t.name?e.Fragment:eu(e,t.name,!0),a=function(e,t){let n={};for(let r of t.attributes)if("mdxJsxExpressionAttribute"===r.type)if(r.data&&r.data.estree&&e.evaluater){let t=r.data.estree.body[0];(0,l.ok)("ExpressionStatement"===t.type);let i=t.expression;(0,l.ok)("ObjectExpression"===i.type);let o=i.properties[0];(0,l.ok)("SpreadElement"===o.type),Object.assign(n,e.evaluater.evaluateExpression(o.argument))}else ec(e,t.position);else{let i,o=r.name;if(r.value&&"object"==typeof r.value)if(r.value.data&&r.value.data.estree&&e.evaluater){let t=r.value.data.estree.body[0];(0,l.ok)("ExpressionStatement"===t.type),i=e.evaluater.evaluateExpression(t.expression)}else ec(e,t.position);else i=null===r.value||r.value;n[o]=i}return n}(e,t),s=es(e,t);return eo(e,a,o,t),ea(a,s),e.ancestors.pop(),e.schema=r,e.create(t,o,a,n)}(e,t,n):"mdxjsEsm"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater)return e.evaluater.evaluateProgram(t.data.estree);ec(e,t.position)}(e,t):"root"===t.type?function(e,t,n){let r={};return ea(r,es(e,t)),e.create(t,e.Fragment,r,n)}(e,t,n):"text"===t.type?(r=0,t.value):void 0}function eo(e,t,n,r){"string"!=typeof n&&n!==e.Fragment&&e.passNode&&(t.node=r)}function ea(e,t){if(t.length>0){let n=t.length>1?t:t[0];n&&(e.children=n)}}function es(e,t){let n=[],r=-1,i=e.passKeys?new Map:ee;for(;++r<t.children.length;){let l,o=t.children[r];if(e.passKeys){let e="element"===o.type?o.tagName:"mdxJsxFlowElement"===o.type||"mdxJsxTextElement"===o.type?o.name:void 0;if(e){let t=i.get(e)||0;l=e+"-"+t,i.set(e,t+1)}}let a=el(e,o,l);void 0!==a&&n.push(a)}return n}function eu(e,t,n){let r;if(n)if(t.includes(".")){let e,n=t.split("."),i=-1;for(;++i<n.length;){let t=u(n[i])?{type:"Identifier",name:n[i]}:{type:"Literal",value:n[i]};e=e?{type:"MemberExpression",object:e,property:t,computed:!!(i&&"Literal"===t.type),optional:!1}:t}(0,l.ok)(e,"always a result"),r=e}else r=u(t)&&!/^[a-z]/.test(t)?{type:"Identifier",name:t}:{type:"Literal",value:t};else r={type:"Literal",value:t};if("Literal"===r.type){let t=r.value;return X.call(e.components,t)?e.components[t]:t}if(e.evaluater)return e.evaluater.evaluateExpression(r);ec(e)}function ec(e,t){let n=new Z("Cannot handle MDX estrees without `createEvaluater`",{ancestors:e.ancestors,place:t,ruleId:"mdx-estree",source:"hast-util-to-jsx-runtime"});throw n.file=e.filePath||void 0,n.url=ei+"#cannot-handle-mdx-estrees-without-createevaluater",n}function ef(e){return"-"+e.toLowerCase()}let ep={action:["form"],cite:["blockquote","del","ins","q"],data:["object"],formAction:["button","input"],href:["a","area","base","link"],icon:["menuitem"],itemId:null,manifest:["html"],ping:["a","area"],poster:["video"],src:["audio","embed","iframe","img","input","script","source","track","video"]};var ed=n(91754);n(93491);var eh=n(63015),em=n(31535);class eg{constructor(e){this.left=e?[...e]:[],this.right=[]}get(e){if(e<0||e>=this.left.length+this.right.length)throw RangeError("Cannot access index `"+e+"` in a splice buffer of size `"+(this.left.length+this.right.length)+"`");return e<this.left.length?this.left[e]:this.right[this.right.length-e+this.left.length-1]}get length(){return this.left.length+this.right.length}shift(){return this.setCursor(0),this.right.pop()}slice(e,t){let n=null==t?Number.POSITIVE_INFINITY:t;return n<this.left.length?this.left.slice(e,n):e>this.left.length?this.right.slice(this.right.length-n+this.left.length,this.right.length-e+this.left.length).reverse():this.left.slice(e).concat(this.right.slice(this.right.length-n+this.left.length).reverse())}splice(e,t,n){this.setCursor(Math.trunc(e));let r=this.right.splice(this.right.length-(t||0),Number.POSITIVE_INFINITY);return n&&ey(this.left,n),r.reverse()}pop(){return this.setCursor(Number.POSITIVE_INFINITY),this.left.pop()}push(e){this.setCursor(Number.POSITIVE_INFINITY),this.left.push(e)}pushMany(e){this.setCursor(Number.POSITIVE_INFINITY),ey(this.left,e)}unshift(e){this.setCursor(0),this.right.push(e)}unshiftMany(e){this.setCursor(0),ey(this.right,e.reverse())}setCursor(e){if(e!==this.left.length&&(!(e>this.left.length)||0!==this.right.length)&&(!(e<0)||0!==this.left.length))if(e<this.left.length){let t=this.left.splice(e,Number.POSITIVE_INFINITY);ey(this.right,t.reverse())}else{let t=this.right.splice(this.left.length+this.right.length-e,Number.POSITIVE_INFINITY);ey(this.left,t.reverse())}}}function ey(e,t){let n=0;if(t.length<1e4)e.push(...t);else for(;n<t.length;)e.push(...t.slice(n,n+1e4)),n+=1e4}function eb(e){let t,n,r,i,l,o,a,s={},u=-1,c=new eg(e);for(;++u<c.length;){for(;u in s;)u=s[u];if(t=c.get(u),u&&"chunkFlow"===t[1].type&&"listItemPrefix"===c.get(u-1)[1].type&&((r=0)<(o=t[1]._tokenizer.events).length&&"lineEndingBlank"===o[r][1].type&&(r+=2),r<o.length&&"content"===o[r][1].type))for(;++r<o.length&&"content"!==o[r][1].type;)"chunkText"===o[r][1].type&&(o[r][1]._isInFirstContentOfListItem=!0,r++);if("enter"===t[0])t[1].contentType&&(Object.assign(s,function(e,t){let n,r,i=e.get(t)[1],l=e.get(t)[2],o=t-1,a=[],s=i._tokenizer;!s&&(s=l.parser[i.contentType](i.start),i._contentTypeTextTrailing&&(s._contentTypeTextTrailing=!0));let u=s.events,c=[],f={},p=-1,d=i,h=0,m=0,g=[0];for(;d;){for(;e.get(++o)[1]!==d;);a.push(o),!d._tokenizer&&(n=l.sliceStream(d),d.next||n.push(null),r&&s.defineSkip(d.start),d._isInFirstContentOfListItem&&(s._gfmTasklistFirstContentOfListItem=!0),s.write(n),d._isInFirstContentOfListItem&&(s._gfmTasklistFirstContentOfListItem=void 0)),r=d,d=d.next}for(d=i;++p<u.length;)"exit"===u[p][0]&&"enter"===u[p-1][0]&&u[p][1].type===u[p-1][1].type&&u[p][1].start.line!==u[p][1].end.line&&(m=p+1,g.push(m),d._tokenizer=void 0,d.previous=void 0,d=d.next);for(s.events=[],d?(d._tokenizer=void 0,d.previous=void 0):g.pop(),p=g.length;p--;){let t=u.slice(g[p],g[p+1]),n=a.pop();c.push([n,n+t.length-1]),e.splice(n,2,t)}for(c.reverse(),p=-1;++p<c.length;)f[h+c[p][0]]=h+c[p][1],h+=c[p][1]-c[p][0]-1;return f}(c,u)),u=s[u],a=!0);else if(t[1]._container){for(r=u,n=void 0;r--;)if("lineEnding"===(i=c.get(r))[1].type||"lineEndingBlank"===i[1].type)"enter"===i[0]&&(n&&(c.get(n)[1].type="lineEndingBlank"),i[1].type="lineEnding",n=r);else if("linePrefix"===i[1].type||"listItemIndent"===i[1].type);else break;n&&(t[1].end={...c.get(n)[1].start},(l=c.slice(n,u)).unshift(t),c.splice(n,u-n+1,l))}}return(0,em.m)(e,0,Number.POSITIVE_INFINITY,c.slice(0)),!a}var ev=n(77126),ek=n(32146),ex=n(64071);let ew={tokenize:function(e){let t,n=e.attempt(this.parser.constructs.contentInitial,function(t){return null===t?void e.consume(t):(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),(0,ek.N)(e,n,"linePrefix"))},function(n){return e.enter("paragraph"),function n(r){let i=e.enter("chunkText",{contentType:"text",previous:t});return t&&(t.next=i),t=i,function t(r){if(null===r){e.exit("chunkText"),e.exit("paragraph"),e.consume(r);return}return(0,ex.HP)(r)?(e.consume(r),e.exit("chunkText"),n):(e.consume(r),t)}(r)}(n)});return n}},eS={tokenize:function(e){let t,n,r,i=this,l=[],o=0;return a;function a(t){if(o<l.length){let n=l[o];return i.containerState=n[1],e.attempt(n[0].continuation,s,u)(t)}return u(t)}function s(e){if(o++,i.containerState._closeFlow){let n;i.containerState._closeFlow=void 0,t&&y();let r=i.events.length,l=r;for(;l--;)if("exit"===i.events[l][0]&&"chunkFlow"===i.events[l][1].type){n=i.events[l][1].end;break}g(o);let a=r;for(;a<i.events.length;)i.events[a][1].end={...n},a++;return(0,em.m)(i.events,l+1,0,i.events.slice(r)),i.events.length=a,u(e)}return a(e)}function u(n){if(o===l.length){if(!t)return p(n);if(t.currentConstruct&&t.currentConstruct.concrete)return h(n);i.interrupt=!!(t.currentConstruct&&!t._gfmTableDynamicInterruptHack)}return i.containerState={},e.check(eC,c,f)(n)}function c(e){return t&&y(),g(o),p(e)}function f(e){return i.parser.lazy[i.now().line]=o!==l.length,r=i.now().offset,h(e)}function p(t){return i.containerState={},e.attempt(eC,d,h)(t)}function d(e){return o++,l.push([i.currentConstruct,i.containerState]),p(e)}function h(r){if(null===r){t&&y(),g(0),e.consume(r);return}return t=t||i.parser.flow(i.now()),e.enter("chunkFlow",{_tokenizer:t,contentType:"flow",previous:n}),function t(n){if(null===n){m(e.exit("chunkFlow"),!0),g(0),e.consume(n);return}return(0,ex.HP)(n)?(e.consume(n),m(e.exit("chunkFlow")),o=0,i.interrupt=void 0,a):(e.consume(n),t)}(r)}function m(e,l){let a=i.sliceStream(e);if(l&&a.push(null),e.previous=n,n&&(n.next=e),n=e,t.defineSkip(e.start),t.write(a),i.parser.lazy[e.start.line]){let e,n,l=t.events.length;for(;l--;)if(t.events[l][1].start.offset<r&&(!t.events[l][1].end||t.events[l][1].end.offset>r))return;let a=i.events.length,s=a;for(;s--;)if("exit"===i.events[s][0]&&"chunkFlow"===i.events[s][1].type){if(e){n=i.events[s][1].end;break}e=!0}for(g(o),l=a;l<i.events.length;)i.events[l][1].end={...n},l++;(0,em.m)(i.events,s+1,0,i.events.slice(a)),i.events.length=l}}function g(t){let n=l.length;for(;n-- >t;){let t=l[n];i.containerState=t[1],t[0].exit.call(i,e)}l.length=t}function y(){t.write([null]),n=void 0,t=void 0,i.containerState._closeFlow=void 0}}},eC={tokenize:function(e,t,n){return(0,ek.N)(e,e.attempt(this.parser.constructs.document,t,n),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}};var eE=n(27870);let eD={resolve:function(e){return eb(e),e},tokenize:function(e,t){let n;return function(t){return e.enter("content"),n=e.enter("chunkContent",{contentType:"content"}),r(t)};function r(t){return null===t?i(t):(0,ex.HP)(t)?e.check(eT,l,i)(t):(e.consume(t),r)}function i(n){return e.exit("chunkContent"),e.exit("content"),t(n)}function l(t){return e.consume(t),e.exit("chunkContent"),n.next=e.enter("chunkContent",{contentType:"content",previous:n}),n=n.next,r}}},eT={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return e.exit("chunkContent"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),(0,ek.N)(e,i,"linePrefix")};function i(i){if(null===i||(0,ex.HP)(i))return n(i);let l=r.events[r.events.length-1];return!r.parser.constructs.disable.null.includes("codeIndented")&&l&&"linePrefix"===l[1].type&&l[2].sliceSerialize(l[1],!0).length>=4?t(i):e.interrupt(r.parser.constructs.flow,n,t)(i)}}},eA={tokenize:function(e){let t=this,n=e.attempt(eE.B,function(r){return null===r?void e.consume(r):(e.enter("lineEndingBlank"),e.consume(r),e.exit("lineEndingBlank"),t.currentConstruct=void 0,n)},e.attempt(this.parser.constructs.flowInitial,r,(0,ek.N)(e,e.attempt(this.parser.constructs.flow,r,e.attempt(eD,r)),"linePrefix")));return n;function r(r){return null===r?void e.consume(r):(e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),t.currentConstruct=void 0,n)}}},eL={resolveAll:eN()},eP=eq("string"),eI=eq("text");function eq(e){return{resolveAll:eN("text"===e?eO:void 0),tokenize:function(t){let n=this,r=this.parser.constructs[e],i=t.attempt(r,l,o);return l;function l(e){return s(e)?i(e):o(e)}function o(e){return null===e?void t.consume(e):(t.enter("data"),t.consume(e),a)}function a(e){return s(e)?(t.exit("data"),i(e)):(t.consume(e),a)}function s(e){if(null===e)return!0;let t=r[e],i=-1;if(t)for(;++i<t.length;){let e=t[i];if(!e.previous||e.previous.call(n,n.previous))return!0}return!1}}}}function eN(e){return function(t,n){let r,i=-1;for(;++i<=t.length;)void 0===r?t[i]&&"data"===t[i][1].type&&(r=i,i++):t[i]&&"data"===t[i][1].type||(i!==r+2&&(t[r][1].end=t[i-1][1].end,t.splice(r+2,i-r-2),i=r+2),r=void 0);return e?e(t,n):t}}function eO(e,t){let n=0;for(;++n<=e.length;)if((n===e.length||"lineEnding"===e[n][1].type)&&"data"===e[n-1][1].type){let r,i=e[n-1][1],l=t.sliceStream(i),o=l.length,a=-1,s=0;for(;o--;){let e=l[o];if("string"==typeof e){for(a=e.length;32===e.charCodeAt(a-1);)s++,a--;if(a)break;a=-1}else if(-2===e)r=!0,s++;else if(-1===e);else{o++;break}}if(t._contentTypeTextTrailing&&n===e.length&&(s=0),s){let l={type:n===e.length||r||s<2?"lineSuffix":"hardBreakTrailing",start:{_bufferIndex:o?a:i.start._bufferIndex+a,_index:i.start._index+o,line:i.end.line,column:i.end.column-s,offset:i.end.offset-s},end:{...i.end}};i.end={...l.start},i.start.offset===i.end.offset?Object.assign(i,l):(e.splice(n,0,["enter",l,t],["exit",l,t]),n+=2)}n++}return e}let eR={name:"thematicBreak",tokenize:function(e,t,n){let r,i=0;return function(l){var o;return e.enter("thematicBreak"),r=o=l,function l(o){return o===r?(e.enter("thematicBreakSequence"),function t(n){return n===r?(e.consume(n),i++,t):(e.exit("thematicBreakSequence"),(0,ex.On)(n)?(0,ek.N)(e,l,"whitespace")(n):l(n))}(o)):i>=3&&(null===o||(0,ex.HP)(o))?(e.exit("thematicBreak"),t(o)):n(o)}(o)}}},eF={continuation:{tokenize:function(e,t,n){let r=this;return r.containerState._closeFlow=void 0,e.check(eE.B,function(n){return r.containerState.furtherBlankLines=r.containerState.furtherBlankLines||r.containerState.initialBlankLine,(0,ek.N)(e,t,"listItemIndent",r.containerState.size+1)(n)},function(n){return r.containerState.furtherBlankLines||!(0,ex.On)(n)?(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,i(n)):(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,e.attempt(eM,t,i)(n))});function i(i){return r.containerState._closeFlow=!0,r.interrupt=void 0,(0,ek.N)(e,e.attempt(eF,t,n),"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(i)}}},exit:function(e){e.exit(this.containerState.type)},name:"list",tokenize:function(e,t,n){let r=this,i=r.events[r.events.length-1],l=i&&"linePrefix"===i[1].type?i[2].sliceSerialize(i[1],!0).length:0,o=0;return function(t){let i=r.containerState.type||(42===t||43===t||45===t?"listUnordered":"listOrdered");if("listUnordered"===i?!r.containerState.marker||t===r.containerState.marker:(0,ex.BM)(t)){if(r.containerState.type||(r.containerState.type=i,e.enter(i,{_container:!0})),"listUnordered"===i)return e.enter("listItemPrefix"),42===t||45===t?e.check(eR,n,a)(t):a(t);if(!r.interrupt||49===t)return e.enter("listItemPrefix"),e.enter("listItemValue"),function t(i){return(0,ex.BM)(i)&&++o<10?(e.consume(i),t):(!r.interrupt||o<2)&&(r.containerState.marker?i===r.containerState.marker:41===i||46===i)?(e.exit("listItemValue"),a(i)):n(i)}(t)}return n(t)};function a(t){return e.enter("listItemMarker"),e.consume(t),e.exit("listItemMarker"),r.containerState.marker=r.containerState.marker||t,e.check(eE.B,r.interrupt?n:s,e.attempt(ez,c,u))}function s(e){return r.containerState.initialBlankLine=!0,l++,c(e)}function u(t){return(0,ex.On)(t)?(e.enter("listItemPrefixWhitespace"),e.consume(t),e.exit("listItemPrefixWhitespace"),c):n(t)}function c(n){return r.containerState.size=l+r.sliceSerialize(e.exit("listItemPrefix"),!0).length,t(n)}}},ez={partial:!0,tokenize:function(e,t,n){let r=this;return(0,ek.N)(e,function(e){let i=r.events[r.events.length-1];return!(0,ex.On)(e)&&i&&"listItemPrefixWhitespace"===i[1].type?t(e):n(e)},"listItemPrefixWhitespace",r.parser.constructs.disable.null.includes("codeIndented")?void 0:5)}},eM={partial:!0,tokenize:function(e,t,n){let r=this;return(0,ek.N)(e,function(e){let i=r.events[r.events.length-1];return i&&"listItemIndent"===i[1].type&&i[2].sliceSerialize(i[1],!0).length===r.containerState.size?t(e):n(e)},"listItemIndent",r.containerState.size+1)}},eH={continuation:{tokenize:function(e,t,n){let r=this;return function(t){return(0,ex.On)(t)?(0,ek.N)(e,i,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):i(t)};function i(r){return e.attempt(eH,t,n)(r)}}},exit:function(e){e.exit("blockQuote")},name:"blockQuote",tokenize:function(e,t,n){let r=this;return function(t){if(62===t){let n=r.containerState;return n.open||(e.enter("blockQuote",{_container:!0}),n.open=!0),e.enter("blockQuotePrefix"),e.enter("blockQuoteMarker"),e.consume(t),e.exit("blockQuoteMarker"),i}return n(t)};function i(n){return(0,ex.On)(n)?(e.enter("blockQuotePrefixWhitespace"),e.consume(n),e.exit("blockQuotePrefixWhitespace"),e.exit("blockQuotePrefix"),t):(e.exit("blockQuotePrefix"),t(n))}}};function eB(e,t,n,r,i,l,o,a,s){let u=s||Number.POSITIVE_INFINITY,c=0;return function(t){return 60===t?(e.enter(r),e.enter(i),e.enter(l),e.consume(t),e.exit(l),f):null===t||32===t||41===t||(0,ex.JQ)(t)?n(t):(e.enter(r),e.enter(o),e.enter(a),e.enter("chunkString",{contentType:"string"}),h(t))};function f(n){return 62===n?(e.enter(l),e.consume(n),e.exit(l),e.exit(i),e.exit(r),t):(e.enter(a),e.enter("chunkString",{contentType:"string"}),p(n))}function p(t){return 62===t?(e.exit("chunkString"),e.exit(a),f(t)):null===t||60===t||(0,ex.HP)(t)?n(t):(e.consume(t),92===t?d:p)}function d(t){return 60===t||62===t||92===t?(e.consume(t),p):p(t)}function h(i){return!c&&(null===i||41===i||(0,ex.Ee)(i))?(e.exit("chunkString"),e.exit(a),e.exit(o),e.exit(r),t(i)):c<u&&40===i?(e.consume(i),c++,h):41===i?(e.consume(i),c--,h):null===i||32===i||40===i||(0,ex.JQ)(i)?n(i):(e.consume(i),92===i?m:h)}function m(t){return 40===t||41===t||92===t?(e.consume(t),h):h(t)}}function eV(e,t,n,r,i,l){let o,a=this,s=0;return function(t){return e.enter(r),e.enter(i),e.consume(t),e.exit(i),e.enter(l),u};function u(f){return s>999||null===f||91===f||93===f&&!o||94===f&&!s&&"_hiddenFootnoteSupport"in a.parser.constructs?n(f):93===f?(e.exit(l),e.enter(i),e.consume(f),e.exit(i),e.exit(r),t):(0,ex.HP)(f)?(e.enter("lineEnding"),e.consume(f),e.exit("lineEnding"),u):(e.enter("chunkString",{contentType:"string"}),c(f))}function c(t){return null===t||91===t||93===t||(0,ex.HP)(t)||s++>999?(e.exit("chunkString"),u(t)):(e.consume(t),o||(o=!(0,ex.On)(t)),92===t?f:c)}function f(t){return 91===t||92===t||93===t?(e.consume(t),s++,c):c(t)}}function ej(e,t,n,r,i,l){let o;return function(t){return 34===t||39===t||40===t?(e.enter(r),e.enter(i),e.consume(t),e.exit(i),o=40===t?41:t,a):n(t)};function a(n){return n===o?(e.enter(i),e.consume(n),e.exit(i),e.exit(r),t):(e.enter(l),s(n))}function s(t){return t===o?(e.exit(l),a(o)):null===t?n(t):(0,ex.HP)(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),(0,ek.N)(e,s,"linePrefix")):(e.enter("chunkString",{contentType:"string"}),u(t))}function u(t){return t===o||null===t||(0,ex.HP)(t)?(e.exit("chunkString"),s(t)):(e.consume(t),92===t?c:u)}function c(t){return t===o||92===t?(e.consume(t),u):u(t)}}function e_(e,t){let n;return function r(i){return(0,ex.HP)(i)?(e.enter("lineEnding"),e.consume(i),e.exit("lineEnding"),n=!0,r):(0,ex.On)(i)?(0,ek.N)(e,r,n?"linePrefix":"lineSuffix")(i):t(i)}}var eU=n(89961);let eW={partial:!0,tokenize:function(e,t,n){return function(t){return(0,ex.Ee)(t)?e_(e,r)(t):n(t)};function r(t){return ej(e,i,n,"definitionTitle","definitionTitleMarker","definitionTitleString")(t)}function i(t){return(0,ex.On)(t)?(0,ek.N)(e,l,"whitespace")(t):l(t)}function l(e){return null===e||(0,ex.HP)(e)?t(e):n(e)}}},eG={name:"codeIndented",tokenize:function(e,t,n){let r=this;return function(t){return e.enter("codeIndented"),(0,ek.N)(e,i,"linePrefix",5)(t)};function i(t){let i=r.events[r.events.length-1];return i&&"linePrefix"===i[1].type&&i[2].sliceSerialize(i[1],!0).length>=4?function t(n){return null===n?l(n):(0,ex.HP)(n)?e.attempt(eY,t,l)(n):(e.enter("codeFlowValue"),function n(r){return null===r||(0,ex.HP)(r)?(e.exit("codeFlowValue"),t(r)):(e.consume(r),n)}(n))}(t):n(t)}function l(n){return e.exit("codeIndented"),t(n)}}},eY={partial:!0,tokenize:function(e,t,n){let r=this;return i;function i(t){return r.parser.lazy[r.now().line]?n(t):(0,ex.HP)(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),i):(0,ek.N)(e,l,"linePrefix",5)(t)}function l(e){let l=r.events[r.events.length-1];return l&&"linePrefix"===l[1].type&&l[2].sliceSerialize(l[1],!0).length>=4?t(e):(0,ex.HP)(e)?i(e):n(e)}}},e$={name:"setextUnderline",resolveTo:function(e,t){let n,r,i,l=e.length;for(;l--;)if("enter"===e[l][0]){if("content"===e[l][1].type){n=l;break}"paragraph"===e[l][1].type&&(r=l)}else"content"===e[l][1].type&&e.splice(l,1),i||"definition"!==e[l][1].type||(i=l);let o={type:"setextHeading",start:{...e[n][1].start},end:{...e[e.length-1][1].end}};return e[r][1].type="setextHeadingText",i?(e.splice(r,0,["enter",o,t]),e.splice(i+1,0,["exit",e[n][1],t]),e[n][1].end={...e[i][1].end}):e[n][1]=o,e.push(["exit",o,t]),e},tokenize:function(e,t,n){let r,i=this;return function(t){var o;let a,s=i.events.length;for(;s--;)if("lineEnding"!==i.events[s][1].type&&"linePrefix"!==i.events[s][1].type&&"content"!==i.events[s][1].type){a="paragraph"===i.events[s][1].type;break}return!i.parser.lazy[i.now().line]&&(i.interrupt||a)?(e.enter("setextHeadingLine"),r=t,o=t,e.enter("setextHeadingLineSequence"),function t(n){return n===r?(e.consume(n),t):(e.exit("setextHeadingLineSequence"),(0,ex.On)(n)?(0,ek.N)(e,l,"lineSuffix")(n):l(n))}(o)):n(t)};function l(r){return null===r||(0,ex.HP)(r)?(e.exit("setextHeadingLine"),t(r)):n(r)}}},eQ=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],eJ=["pre","script","style","textarea"],eK={partial:!0,tokenize:function(e,t,n){return function(r){return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),e.attempt(eE.B,t,n)}}},eZ={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return(0,ex.HP)(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),i):n(t)};function i(e){return r.parser.lazy[r.now().line]?n(e):t(e)}}},eX={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return null===t?n(t):(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),i)};function i(e){return r.parser.lazy[r.now().line]?n(e):t(e)}}},e0={concrete:!0,name:"codeFenced",tokenize:function(e,t,n){let r,i=this,l={partial:!0,tokenize:function(e,t,n){let l=0;return function(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),o};function o(t){return e.enter("codeFencedFence"),(0,ex.On)(t)?(0,ek.N)(e,s,"linePrefix",i.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):s(t)}function s(t){return t===r?(e.enter("codeFencedFenceSequence"),function t(i){return i===r?(l++,e.consume(i),t):l>=a?(e.exit("codeFencedFenceSequence"),(0,ex.On)(i)?(0,ek.N)(e,u,"whitespace")(i):u(i)):n(i)}(t)):n(t)}function u(r){return null===r||(0,ex.HP)(r)?(e.exit("codeFencedFence"),t(r)):n(r)}}},o=0,a=0;return function(t){var l=t;let u=i.events[i.events.length-1];return o=u&&"linePrefix"===u[1].type?u[2].sliceSerialize(u[1],!0).length:0,r=l,e.enter("codeFenced"),e.enter("codeFencedFence"),e.enter("codeFencedFenceSequence"),function t(i){return i===r?(a++,e.consume(i),t):a<3?n(i):(e.exit("codeFencedFenceSequence"),(0,ex.On)(i)?(0,ek.N)(e,s,"whitespace")(i):s(i))}(l)};function s(l){return null===l||(0,ex.HP)(l)?(e.exit("codeFencedFence"),i.interrupt?t(l):e.check(eX,c,h)(l)):(e.enter("codeFencedFenceInfo"),e.enter("chunkString",{contentType:"string"}),function t(i){return null===i||(0,ex.HP)(i)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),s(i)):(0,ex.On)(i)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),(0,ek.N)(e,u,"whitespace")(i)):96===i&&i===r?n(i):(e.consume(i),t)}(l))}function u(t){return null===t||(0,ex.HP)(t)?s(t):(e.enter("codeFencedFenceMeta"),e.enter("chunkString",{contentType:"string"}),function t(i){return null===i||(0,ex.HP)(i)?(e.exit("chunkString"),e.exit("codeFencedFenceMeta"),s(i)):96===i&&i===r?n(i):(e.consume(i),t)}(t))}function c(t){return e.attempt(l,h,f)(t)}function f(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),p}function p(t){return o>0&&(0,ex.On)(t)?(0,ek.N)(e,d,"linePrefix",o+1)(t):d(t)}function d(t){return null===t||(0,ex.HP)(t)?e.check(eX,c,h)(t):(e.enter("codeFlowValue"),function t(n){return null===n||(0,ex.HP)(n)?(e.exit("codeFlowValue"),d(n)):(e.consume(n),t)}(t))}function h(n){return e.exit("codeFenced"),t(n)}}},e1={AElig:"\xc6",AMP:"&",Aacute:"\xc1",Abreve:"Ă",Acirc:"\xc2",Acy:"А",Afr:"\uD835\uDD04",Agrave:"\xc0",Alpha:"Α",Amacr:"Ā",And:"⩓",Aogon:"Ą",Aopf:"\uD835\uDD38",ApplyFunction:"⁡",Aring:"\xc5",Ascr:"\uD835\uDC9C",Assign:"≔",Atilde:"\xc3",Auml:"\xc4",Backslash:"∖",Barv:"⫧",Barwed:"⌆",Bcy:"Б",Because:"∵",Bernoullis:"ℬ",Beta:"Β",Bfr:"\uD835\uDD05",Bopf:"\uD835\uDD39",Breve:"˘",Bscr:"ℬ",Bumpeq:"≎",CHcy:"Ч",COPY:"\xa9",Cacute:"Ć",Cap:"⋒",CapitalDifferentialD:"ⅅ",Cayleys:"ℭ",Ccaron:"Č",Ccedil:"\xc7",Ccirc:"Ĉ",Cconint:"∰",Cdot:"Ċ",Cedilla:"\xb8",CenterDot:"\xb7",Cfr:"ℭ",Chi:"Χ",CircleDot:"⊙",CircleMinus:"⊖",CirclePlus:"⊕",CircleTimes:"⊗",ClockwiseContourIntegral:"∲",CloseCurlyDoubleQuote:"”",CloseCurlyQuote:"’",Colon:"∷",Colone:"⩴",Congruent:"≡",Conint:"∯",ContourIntegral:"∮",Copf:"ℂ",Coproduct:"∐",CounterClockwiseContourIntegral:"∳",Cross:"⨯",Cscr:"\uD835\uDC9E",Cup:"⋓",CupCap:"≍",DD:"ⅅ",DDotrahd:"⤑",DJcy:"Ђ",DScy:"Ѕ",DZcy:"Џ",Dagger:"‡",Darr:"↡",Dashv:"⫤",Dcaron:"Ď",Dcy:"Д",Del:"∇",Delta:"Δ",Dfr:"\uD835\uDD07",DiacriticalAcute:"\xb4",DiacriticalDot:"˙",DiacriticalDoubleAcute:"˝",DiacriticalGrave:"`",DiacriticalTilde:"˜",Diamond:"⋄",DifferentialD:"ⅆ",Dopf:"\uD835\uDD3B",Dot:"\xa8",DotDot:"⃜",DotEqual:"≐",DoubleContourIntegral:"∯",DoubleDot:"\xa8",DoubleDownArrow:"⇓",DoubleLeftArrow:"⇐",DoubleLeftRightArrow:"⇔",DoubleLeftTee:"⫤",DoubleLongLeftArrow:"⟸",DoubleLongLeftRightArrow:"⟺",DoubleLongRightArrow:"⟹",DoubleRightArrow:"⇒",DoubleRightTee:"⊨",DoubleUpArrow:"⇑",DoubleUpDownArrow:"⇕",DoubleVerticalBar:"∥",DownArrow:"↓",DownArrowBar:"⤓",DownArrowUpArrow:"⇵",DownBreve:"̑",DownLeftRightVector:"⥐",DownLeftTeeVector:"⥞",DownLeftVector:"↽",DownLeftVectorBar:"⥖",DownRightTeeVector:"⥟",DownRightVector:"⇁",DownRightVectorBar:"⥗",DownTee:"⊤",DownTeeArrow:"↧",Downarrow:"⇓",Dscr:"\uD835\uDC9F",Dstrok:"Đ",ENG:"Ŋ",ETH:"\xd0",Eacute:"\xc9",Ecaron:"Ě",Ecirc:"\xca",Ecy:"Э",Edot:"Ė",Efr:"\uD835\uDD08",Egrave:"\xc8",Element:"∈",Emacr:"Ē",EmptySmallSquare:"◻",EmptyVerySmallSquare:"▫",Eogon:"Ę",Eopf:"\uD835\uDD3C",Epsilon:"Ε",Equal:"⩵",EqualTilde:"≂",Equilibrium:"⇌",Escr:"ℰ",Esim:"⩳",Eta:"Η",Euml:"\xcb",Exists:"∃",ExponentialE:"ⅇ",Fcy:"Ф",Ffr:"\uD835\uDD09",FilledSmallSquare:"◼",FilledVerySmallSquare:"▪",Fopf:"\uD835\uDD3D",ForAll:"∀",Fouriertrf:"ℱ",Fscr:"ℱ",GJcy:"Ѓ",GT:">",Gamma:"Γ",Gammad:"Ϝ",Gbreve:"Ğ",Gcedil:"Ģ",Gcirc:"Ĝ",Gcy:"Г",Gdot:"Ġ",Gfr:"\uD835\uDD0A",Gg:"⋙",Gopf:"\uD835\uDD3E",GreaterEqual:"≥",GreaterEqualLess:"⋛",GreaterFullEqual:"≧",GreaterGreater:"⪢",GreaterLess:"≷",GreaterSlantEqual:"⩾",GreaterTilde:"≳",Gscr:"\uD835\uDCA2",Gt:"≫",HARDcy:"Ъ",Hacek:"ˇ",Hat:"^",Hcirc:"Ĥ",Hfr:"ℌ",HilbertSpace:"ℋ",Hopf:"ℍ",HorizontalLine:"─",Hscr:"ℋ",Hstrok:"Ħ",HumpDownHump:"≎",HumpEqual:"≏",IEcy:"Е",IJlig:"Ĳ",IOcy:"Ё",Iacute:"\xcd",Icirc:"\xce",Icy:"И",Idot:"İ",Ifr:"ℑ",Igrave:"\xcc",Im:"ℑ",Imacr:"Ī",ImaginaryI:"ⅈ",Implies:"⇒",Int:"∬",Integral:"∫",Intersection:"⋂",InvisibleComma:"⁣",InvisibleTimes:"⁢",Iogon:"Į",Iopf:"\uD835\uDD40",Iota:"Ι",Iscr:"ℐ",Itilde:"Ĩ",Iukcy:"І",Iuml:"\xcf",Jcirc:"Ĵ",Jcy:"Й",Jfr:"\uD835\uDD0D",Jopf:"\uD835\uDD41",Jscr:"\uD835\uDCA5",Jsercy:"Ј",Jukcy:"Є",KHcy:"Х",KJcy:"Ќ",Kappa:"Κ",Kcedil:"Ķ",Kcy:"К",Kfr:"\uD835\uDD0E",Kopf:"\uD835\uDD42",Kscr:"\uD835\uDCA6",LJcy:"Љ",LT:"<",Lacute:"Ĺ",Lambda:"Λ",Lang:"⟪",Laplacetrf:"ℒ",Larr:"↞",Lcaron:"Ľ",Lcedil:"Ļ",Lcy:"Л",LeftAngleBracket:"⟨",LeftArrow:"←",LeftArrowBar:"⇤",LeftArrowRightArrow:"⇆",LeftCeiling:"⌈",LeftDoubleBracket:"⟦",LeftDownTeeVector:"⥡",LeftDownVector:"⇃",LeftDownVectorBar:"⥙",LeftFloor:"⌊",LeftRightArrow:"↔",LeftRightVector:"⥎",LeftTee:"⊣",LeftTeeArrow:"↤",LeftTeeVector:"⥚",LeftTriangle:"⊲",LeftTriangleBar:"⧏",LeftTriangleEqual:"⊴",LeftUpDownVector:"⥑",LeftUpTeeVector:"⥠",LeftUpVector:"↿",LeftUpVectorBar:"⥘",LeftVector:"↼",LeftVectorBar:"⥒",Leftarrow:"⇐",Leftrightarrow:"⇔",LessEqualGreater:"⋚",LessFullEqual:"≦",LessGreater:"≶",LessLess:"⪡",LessSlantEqual:"⩽",LessTilde:"≲",Lfr:"\uD835\uDD0F",Ll:"⋘",Lleftarrow:"⇚",Lmidot:"Ŀ",LongLeftArrow:"⟵",LongLeftRightArrow:"⟷",LongRightArrow:"⟶",Longleftarrow:"⟸",Longleftrightarrow:"⟺",Longrightarrow:"⟹",Lopf:"\uD835\uDD43",LowerLeftArrow:"↙",LowerRightArrow:"↘",Lscr:"ℒ",Lsh:"↰",Lstrok:"Ł",Lt:"≪",Map:"⤅",Mcy:"М",MediumSpace:" ",Mellintrf:"ℳ",Mfr:"\uD835\uDD10",MinusPlus:"∓",Mopf:"\uD835\uDD44",Mscr:"ℳ",Mu:"Μ",NJcy:"Њ",Nacute:"Ń",Ncaron:"Ň",Ncedil:"Ņ",Ncy:"Н",NegativeMediumSpace:"​",NegativeThickSpace:"​",NegativeThinSpace:"​",NegativeVeryThinSpace:"​",NestedGreaterGreater:"≫",NestedLessLess:"≪",NewLine:"\n",Nfr:"\uD835\uDD11",NoBreak:"⁠",NonBreakingSpace:"\xa0",Nopf:"ℕ",Not:"⫬",NotCongruent:"≢",NotCupCap:"≭",NotDoubleVerticalBar:"∦",NotElement:"∉",NotEqual:"≠",NotEqualTilde:"≂̸",NotExists:"∄",NotGreater:"≯",NotGreaterEqual:"≱",NotGreaterFullEqual:"≧̸",NotGreaterGreater:"≫̸",NotGreaterLess:"≹",NotGreaterSlantEqual:"⩾̸",NotGreaterTilde:"≵",NotHumpDownHump:"≎̸",NotHumpEqual:"≏̸",NotLeftTriangle:"⋪",NotLeftTriangleBar:"⧏̸",NotLeftTriangleEqual:"⋬",NotLess:"≮",NotLessEqual:"≰",NotLessGreater:"≸",NotLessLess:"≪̸",NotLessSlantEqual:"⩽̸",NotLessTilde:"≴",NotNestedGreaterGreater:"⪢̸",NotNestedLessLess:"⪡̸",NotPrecedes:"⊀",NotPrecedesEqual:"⪯̸",NotPrecedesSlantEqual:"⋠",NotReverseElement:"∌",NotRightTriangle:"⋫",NotRightTriangleBar:"⧐̸",NotRightTriangleEqual:"⋭",NotSquareSubset:"⊏̸",NotSquareSubsetEqual:"⋢",NotSquareSuperset:"⊐̸",NotSquareSupersetEqual:"⋣",NotSubset:"⊂⃒",NotSubsetEqual:"⊈",NotSucceeds:"⊁",NotSucceedsEqual:"⪰̸",NotSucceedsSlantEqual:"⋡",NotSucceedsTilde:"≿̸",NotSuperset:"⊃⃒",NotSupersetEqual:"⊉",NotTilde:"≁",NotTildeEqual:"≄",NotTildeFullEqual:"≇",NotTildeTilde:"≉",NotVerticalBar:"∤",Nscr:"\uD835\uDCA9",Ntilde:"\xd1",Nu:"Ν",OElig:"Œ",Oacute:"\xd3",Ocirc:"\xd4",Ocy:"О",Odblac:"Ő",Ofr:"\uD835\uDD12",Ograve:"\xd2",Omacr:"Ō",Omega:"Ω",Omicron:"Ο",Oopf:"\uD835\uDD46",OpenCurlyDoubleQuote:"“",OpenCurlyQuote:"‘",Or:"⩔",Oscr:"\uD835\uDCAA",Oslash:"\xd8",Otilde:"\xd5",Otimes:"⨷",Ouml:"\xd6",OverBar:"‾",OverBrace:"⏞",OverBracket:"⎴",OverParenthesis:"⏜",PartialD:"∂",Pcy:"П",Pfr:"\uD835\uDD13",Phi:"Φ",Pi:"Π",PlusMinus:"\xb1",Poincareplane:"ℌ",Popf:"ℙ",Pr:"⪻",Precedes:"≺",PrecedesEqual:"⪯",PrecedesSlantEqual:"≼",PrecedesTilde:"≾",Prime:"″",Product:"∏",Proportion:"∷",Proportional:"∝",Pscr:"\uD835\uDCAB",Psi:"Ψ",QUOT:'"',Qfr:"\uD835\uDD14",Qopf:"ℚ",Qscr:"\uD835\uDCAC",RBarr:"⤐",REG:"\xae",Racute:"Ŕ",Rang:"⟫",Rarr:"↠",Rarrtl:"⤖",Rcaron:"Ř",Rcedil:"Ŗ",Rcy:"Р",Re:"ℜ",ReverseElement:"∋",ReverseEquilibrium:"⇋",ReverseUpEquilibrium:"⥯",Rfr:"ℜ",Rho:"Ρ",RightAngleBracket:"⟩",RightArrow:"→",RightArrowBar:"⇥",RightArrowLeftArrow:"⇄",RightCeiling:"⌉",RightDoubleBracket:"⟧",RightDownTeeVector:"⥝",RightDownVector:"⇂",RightDownVectorBar:"⥕",RightFloor:"⌋",RightTee:"⊢",RightTeeArrow:"↦",RightTeeVector:"⥛",RightTriangle:"⊳",RightTriangleBar:"⧐",RightTriangleEqual:"⊵",RightUpDownVector:"⥏",RightUpTeeVector:"⥜",RightUpVector:"↾",RightUpVectorBar:"⥔",RightVector:"⇀",RightVectorBar:"⥓",Rightarrow:"⇒",Ropf:"ℝ",RoundImplies:"⥰",Rrightarrow:"⇛",Rscr:"ℛ",Rsh:"↱",RuleDelayed:"⧴",SHCHcy:"Щ",SHcy:"Ш",SOFTcy:"Ь",Sacute:"Ś",Sc:"⪼",Scaron:"Š",Scedil:"Ş",Scirc:"Ŝ",Scy:"С",Sfr:"\uD835\uDD16",ShortDownArrow:"↓",ShortLeftArrow:"←",ShortRightArrow:"→",ShortUpArrow:"↑",Sigma:"Σ",SmallCircle:"∘",Sopf:"\uD835\uDD4A",Sqrt:"√",Square:"□",SquareIntersection:"⊓",SquareSubset:"⊏",SquareSubsetEqual:"⊑",SquareSuperset:"⊐",SquareSupersetEqual:"⊒",SquareUnion:"⊔",Sscr:"\uD835\uDCAE",Star:"⋆",Sub:"⋐",Subset:"⋐",SubsetEqual:"⊆",Succeeds:"≻",SucceedsEqual:"⪰",SucceedsSlantEqual:"≽",SucceedsTilde:"≿",SuchThat:"∋",Sum:"∑",Sup:"⋑",Superset:"⊃",SupersetEqual:"⊇",Supset:"⋑",THORN:"\xde",TRADE:"™",TSHcy:"Ћ",TScy:"Ц",Tab:"	",Tau:"Τ",Tcaron:"Ť",Tcedil:"Ţ",Tcy:"Т",Tfr:"\uD835\uDD17",Therefore:"∴",Theta:"Θ",ThickSpace:"  ",ThinSpace:" ",Tilde:"∼",TildeEqual:"≃",TildeFullEqual:"≅",TildeTilde:"≈",Topf:"\uD835\uDD4B",TripleDot:"⃛",Tscr:"\uD835\uDCAF",Tstrok:"Ŧ",Uacute:"\xda",Uarr:"↟",Uarrocir:"⥉",Ubrcy:"Ў",Ubreve:"Ŭ",Ucirc:"\xdb",Ucy:"У",Udblac:"Ű",Ufr:"\uD835\uDD18",Ugrave:"\xd9",Umacr:"Ū",UnderBar:"_",UnderBrace:"⏟",UnderBracket:"⎵",UnderParenthesis:"⏝",Union:"⋃",UnionPlus:"⊎",Uogon:"Ų",Uopf:"\uD835\uDD4C",UpArrow:"↑",UpArrowBar:"⤒",UpArrowDownArrow:"⇅",UpDownArrow:"↕",UpEquilibrium:"⥮",UpTee:"⊥",UpTeeArrow:"↥",Uparrow:"⇑",Updownarrow:"⇕",UpperLeftArrow:"↖",UpperRightArrow:"↗",Upsi:"ϒ",Upsilon:"Υ",Uring:"Ů",Uscr:"\uD835\uDCB0",Utilde:"Ũ",Uuml:"\xdc",VDash:"⊫",Vbar:"⫫",Vcy:"В",Vdash:"⊩",Vdashl:"⫦",Vee:"⋁",Verbar:"‖",Vert:"‖",VerticalBar:"∣",VerticalLine:"|",VerticalSeparator:"❘",VerticalTilde:"≀",VeryThinSpace:" ",Vfr:"\uD835\uDD19",Vopf:"\uD835\uDD4D",Vscr:"\uD835\uDCB1",Vvdash:"⊪",Wcirc:"Ŵ",Wedge:"⋀",Wfr:"\uD835\uDD1A",Wopf:"\uD835\uDD4E",Wscr:"\uD835\uDCB2",Xfr:"\uD835\uDD1B",Xi:"Ξ",Xopf:"\uD835\uDD4F",Xscr:"\uD835\uDCB3",YAcy:"Я",YIcy:"Ї",YUcy:"Ю",Yacute:"\xdd",Ycirc:"Ŷ",Ycy:"Ы",Yfr:"\uD835\uDD1C",Yopf:"\uD835\uDD50",Yscr:"\uD835\uDCB4",Yuml:"Ÿ",ZHcy:"Ж",Zacute:"Ź",Zcaron:"Ž",Zcy:"З",Zdot:"Ż",ZeroWidthSpace:"​",Zeta:"Ζ",Zfr:"ℨ",Zopf:"ℤ",Zscr:"\uD835\uDCB5",aacute:"\xe1",abreve:"ă",ac:"∾",acE:"∾̳",acd:"∿",acirc:"\xe2",acute:"\xb4",acy:"а",aelig:"\xe6",af:"⁡",afr:"\uD835\uDD1E",agrave:"\xe0",alefsym:"ℵ",aleph:"ℵ",alpha:"α",amacr:"ā",amalg:"⨿",amp:"&",and:"∧",andand:"⩕",andd:"⩜",andslope:"⩘",andv:"⩚",ang:"∠",ange:"⦤",angle:"∠",angmsd:"∡",angmsdaa:"⦨",angmsdab:"⦩",angmsdac:"⦪",angmsdad:"⦫",angmsdae:"⦬",angmsdaf:"⦭",angmsdag:"⦮",angmsdah:"⦯",angrt:"∟",angrtvb:"⊾",angrtvbd:"⦝",angsph:"∢",angst:"\xc5",angzarr:"⍼",aogon:"ą",aopf:"\uD835\uDD52",ap:"≈",apE:"⩰",apacir:"⩯",ape:"≊",apid:"≋",apos:"'",approx:"≈",approxeq:"≊",aring:"\xe5",ascr:"\uD835\uDCB6",ast:"*",asymp:"≈",asympeq:"≍",atilde:"\xe3",auml:"\xe4",awconint:"∳",awint:"⨑",bNot:"⫭",backcong:"≌",backepsilon:"϶",backprime:"‵",backsim:"∽",backsimeq:"⋍",barvee:"⊽",barwed:"⌅",barwedge:"⌅",bbrk:"⎵",bbrktbrk:"⎶",bcong:"≌",bcy:"б",bdquo:"„",becaus:"∵",because:"∵",bemptyv:"⦰",bepsi:"϶",bernou:"ℬ",beta:"β",beth:"ℶ",between:"≬",bfr:"\uD835\uDD1F",bigcap:"⋂",bigcirc:"◯",bigcup:"⋃",bigodot:"⨀",bigoplus:"⨁",bigotimes:"⨂",bigsqcup:"⨆",bigstar:"★",bigtriangledown:"▽",bigtriangleup:"△",biguplus:"⨄",bigvee:"⋁",bigwedge:"⋀",bkarow:"⤍",blacklozenge:"⧫",blacksquare:"▪",blacktriangle:"▴",blacktriangledown:"▾",blacktriangleleft:"◂",blacktriangleright:"▸",blank:"␣",blk12:"▒",blk14:"░",blk34:"▓",block:"█",bne:"=⃥",bnequiv:"≡⃥",bnot:"⌐",bopf:"\uD835\uDD53",bot:"⊥",bottom:"⊥",bowtie:"⋈",boxDL:"╗",boxDR:"╔",boxDl:"╖",boxDr:"╓",boxH:"═",boxHD:"╦",boxHU:"╩",boxHd:"╤",boxHu:"╧",boxUL:"╝",boxUR:"╚",boxUl:"╜",boxUr:"╙",boxV:"║",boxVH:"╬",boxVL:"╣",boxVR:"╠",boxVh:"╫",boxVl:"╢",boxVr:"╟",boxbox:"⧉",boxdL:"╕",boxdR:"╒",boxdl:"┐",boxdr:"┌",boxh:"─",boxhD:"╥",boxhU:"╨",boxhd:"┬",boxhu:"┴",boxminus:"⊟",boxplus:"⊞",boxtimes:"⊠",boxuL:"╛",boxuR:"╘",boxul:"┘",boxur:"└",boxv:"│",boxvH:"╪",boxvL:"╡",boxvR:"╞",boxvh:"┼",boxvl:"┤",boxvr:"├",bprime:"‵",breve:"˘",brvbar:"\xa6",bscr:"\uD835\uDCB7",bsemi:"⁏",bsim:"∽",bsime:"⋍",bsol:"\\",bsolb:"⧅",bsolhsub:"⟈",bull:"•",bullet:"•",bump:"≎",bumpE:"⪮",bumpe:"≏",bumpeq:"≏",cacute:"ć",cap:"∩",capand:"⩄",capbrcup:"⩉",capcap:"⩋",capcup:"⩇",capdot:"⩀",caps:"∩︀",caret:"⁁",caron:"ˇ",ccaps:"⩍",ccaron:"č",ccedil:"\xe7",ccirc:"ĉ",ccups:"⩌",ccupssm:"⩐",cdot:"ċ",cedil:"\xb8",cemptyv:"⦲",cent:"\xa2",centerdot:"\xb7",cfr:"\uD835\uDD20",chcy:"ч",check:"✓",checkmark:"✓",chi:"χ",cir:"○",cirE:"⧃",circ:"ˆ",circeq:"≗",circlearrowleft:"↺",circlearrowright:"↻",circledR:"\xae",circledS:"Ⓢ",circledast:"⊛",circledcirc:"⊚",circleddash:"⊝",cire:"≗",cirfnint:"⨐",cirmid:"⫯",cirscir:"⧂",clubs:"♣",clubsuit:"♣",colon:":",colone:"≔",coloneq:"≔",comma:",",commat:"@",comp:"∁",compfn:"∘",complement:"∁",complexes:"ℂ",cong:"≅",congdot:"⩭",conint:"∮",copf:"\uD835\uDD54",coprod:"∐",copy:"\xa9",copysr:"℗",crarr:"↵",cross:"✗",cscr:"\uD835\uDCB8",csub:"⫏",csube:"⫑",csup:"⫐",csupe:"⫒",ctdot:"⋯",cudarrl:"⤸",cudarrr:"⤵",cuepr:"⋞",cuesc:"⋟",cularr:"↶",cularrp:"⤽",cup:"∪",cupbrcap:"⩈",cupcap:"⩆",cupcup:"⩊",cupdot:"⊍",cupor:"⩅",cups:"∪︀",curarr:"↷",curarrm:"⤼",curlyeqprec:"⋞",curlyeqsucc:"⋟",curlyvee:"⋎",curlywedge:"⋏",curren:"\xa4",curvearrowleft:"↶",curvearrowright:"↷",cuvee:"⋎",cuwed:"⋏",cwconint:"∲",cwint:"∱",cylcty:"⌭",dArr:"⇓",dHar:"⥥",dagger:"†",daleth:"ℸ",darr:"↓",dash:"‐",dashv:"⊣",dbkarow:"⤏",dblac:"˝",dcaron:"ď",dcy:"д",dd:"ⅆ",ddagger:"‡",ddarr:"⇊",ddotseq:"⩷",deg:"\xb0",delta:"δ",demptyv:"⦱",dfisht:"⥿",dfr:"\uD835\uDD21",dharl:"⇃",dharr:"⇂",diam:"⋄",diamond:"⋄",diamondsuit:"♦",diams:"♦",die:"\xa8",digamma:"ϝ",disin:"⋲",div:"\xf7",divide:"\xf7",divideontimes:"⋇",divonx:"⋇",djcy:"ђ",dlcorn:"⌞",dlcrop:"⌍",dollar:"$",dopf:"\uD835\uDD55",dot:"˙",doteq:"≐",doteqdot:"≑",dotminus:"∸",dotplus:"∔",dotsquare:"⊡",doublebarwedge:"⌆",downarrow:"↓",downdownarrows:"⇊",downharpoonleft:"⇃",downharpoonright:"⇂",drbkarow:"⤐",drcorn:"⌟",drcrop:"⌌",dscr:"\uD835\uDCB9",dscy:"ѕ",dsol:"⧶",dstrok:"đ",dtdot:"⋱",dtri:"▿",dtrif:"▾",duarr:"⇵",duhar:"⥯",dwangle:"⦦",dzcy:"џ",dzigrarr:"⟿",eDDot:"⩷",eDot:"≑",eacute:"\xe9",easter:"⩮",ecaron:"ě",ecir:"≖",ecirc:"\xea",ecolon:"≕",ecy:"э",edot:"ė",ee:"ⅇ",efDot:"≒",efr:"\uD835\uDD22",eg:"⪚",egrave:"\xe8",egs:"⪖",egsdot:"⪘",el:"⪙",elinters:"⏧",ell:"ℓ",els:"⪕",elsdot:"⪗",emacr:"ē",empty:"∅",emptyset:"∅",emptyv:"∅",emsp13:" ",emsp14:" ",emsp:" ",eng:"ŋ",ensp:" ",eogon:"ę",eopf:"\uD835\uDD56",epar:"⋕",eparsl:"⧣",eplus:"⩱",epsi:"ε",epsilon:"ε",epsiv:"ϵ",eqcirc:"≖",eqcolon:"≕",eqsim:"≂",eqslantgtr:"⪖",eqslantless:"⪕",equals:"=",equest:"≟",equiv:"≡",equivDD:"⩸",eqvparsl:"⧥",erDot:"≓",erarr:"⥱",escr:"ℯ",esdot:"≐",esim:"≂",eta:"η",eth:"\xf0",euml:"\xeb",euro:"€",excl:"!",exist:"∃",expectation:"ℰ",exponentiale:"ⅇ",fallingdotseq:"≒",fcy:"ф",female:"♀",ffilig:"ﬃ",fflig:"ﬀ",ffllig:"ﬄ",ffr:"\uD835\uDD23",filig:"ﬁ",fjlig:"fj",flat:"♭",fllig:"ﬂ",fltns:"▱",fnof:"ƒ",fopf:"\uD835\uDD57",forall:"∀",fork:"⋔",forkv:"⫙",fpartint:"⨍",frac12:"\xbd",frac13:"⅓",frac14:"\xbc",frac15:"⅕",frac16:"⅙",frac18:"⅛",frac23:"⅔",frac25:"⅖",frac34:"\xbe",frac35:"⅗",frac38:"⅜",frac45:"⅘",frac56:"⅚",frac58:"⅝",frac78:"⅞",frasl:"⁄",frown:"⌢",fscr:"\uD835\uDCBB",gE:"≧",gEl:"⪌",gacute:"ǵ",gamma:"γ",gammad:"ϝ",gap:"⪆",gbreve:"ğ",gcirc:"ĝ",gcy:"г",gdot:"ġ",ge:"≥",gel:"⋛",geq:"≥",geqq:"≧",geqslant:"⩾",ges:"⩾",gescc:"⪩",gesdot:"⪀",gesdoto:"⪂",gesdotol:"⪄",gesl:"⋛︀",gesles:"⪔",gfr:"\uD835\uDD24",gg:"≫",ggg:"⋙",gimel:"ℷ",gjcy:"ѓ",gl:"≷",glE:"⪒",gla:"⪥",glj:"⪤",gnE:"≩",gnap:"⪊",gnapprox:"⪊",gne:"⪈",gneq:"⪈",gneqq:"≩",gnsim:"⋧",gopf:"\uD835\uDD58",grave:"`",gscr:"ℊ",gsim:"≳",gsime:"⪎",gsiml:"⪐",gt:">",gtcc:"⪧",gtcir:"⩺",gtdot:"⋗",gtlPar:"⦕",gtquest:"⩼",gtrapprox:"⪆",gtrarr:"⥸",gtrdot:"⋗",gtreqless:"⋛",gtreqqless:"⪌",gtrless:"≷",gtrsim:"≳",gvertneqq:"≩︀",gvnE:"≩︀",hArr:"⇔",hairsp:" ",half:"\xbd",hamilt:"ℋ",hardcy:"ъ",harr:"↔",harrcir:"⥈",harrw:"↭",hbar:"ℏ",hcirc:"ĥ",hearts:"♥",heartsuit:"♥",hellip:"…",hercon:"⊹",hfr:"\uD835\uDD25",hksearow:"⤥",hkswarow:"⤦",hoarr:"⇿",homtht:"∻",hookleftarrow:"↩",hookrightarrow:"↪",hopf:"\uD835\uDD59",horbar:"―",hscr:"\uD835\uDCBD",hslash:"ℏ",hstrok:"ħ",hybull:"⁃",hyphen:"‐",iacute:"\xed",ic:"⁣",icirc:"\xee",icy:"и",iecy:"е",iexcl:"\xa1",iff:"⇔",ifr:"\uD835\uDD26",igrave:"\xec",ii:"ⅈ",iiiint:"⨌",iiint:"∭",iinfin:"⧜",iiota:"℩",ijlig:"ĳ",imacr:"ī",image:"ℑ",imagline:"ℐ",imagpart:"ℑ",imath:"ı",imof:"⊷",imped:"Ƶ",in:"∈",incare:"℅",infin:"∞",infintie:"⧝",inodot:"ı",int:"∫",intcal:"⊺",integers:"ℤ",intercal:"⊺",intlarhk:"⨗",intprod:"⨼",iocy:"ё",iogon:"į",iopf:"\uD835\uDD5A",iota:"ι",iprod:"⨼",iquest:"\xbf",iscr:"\uD835\uDCBE",isin:"∈",isinE:"⋹",isindot:"⋵",isins:"⋴",isinsv:"⋳",isinv:"∈",it:"⁢",itilde:"ĩ",iukcy:"і",iuml:"\xef",jcirc:"ĵ",jcy:"й",jfr:"\uD835\uDD27",jmath:"ȷ",jopf:"\uD835\uDD5B",jscr:"\uD835\uDCBF",jsercy:"ј",jukcy:"є",kappa:"κ",kappav:"ϰ",kcedil:"ķ",kcy:"к",kfr:"\uD835\uDD28",kgreen:"ĸ",khcy:"х",kjcy:"ќ",kopf:"\uD835\uDD5C",kscr:"\uD835\uDCC0",lAarr:"⇚",lArr:"⇐",lAtail:"⤛",lBarr:"⤎",lE:"≦",lEg:"⪋",lHar:"⥢",lacute:"ĺ",laemptyv:"⦴",lagran:"ℒ",lambda:"λ",lang:"⟨",langd:"⦑",langle:"⟨",lap:"⪅",laquo:"\xab",larr:"←",larrb:"⇤",larrbfs:"⤟",larrfs:"⤝",larrhk:"↩",larrlp:"↫",larrpl:"⤹",larrsim:"⥳",larrtl:"↢",lat:"⪫",latail:"⤙",late:"⪭",lates:"⪭︀",lbarr:"⤌",lbbrk:"❲",lbrace:"{",lbrack:"[",lbrke:"⦋",lbrksld:"⦏",lbrkslu:"⦍",lcaron:"ľ",lcedil:"ļ",lceil:"⌈",lcub:"{",lcy:"л",ldca:"⤶",ldquo:"“",ldquor:"„",ldrdhar:"⥧",ldrushar:"⥋",ldsh:"↲",le:"≤",leftarrow:"←",leftarrowtail:"↢",leftharpoondown:"↽",leftharpoonup:"↼",leftleftarrows:"⇇",leftrightarrow:"↔",leftrightarrows:"⇆",leftrightharpoons:"⇋",leftrightsquigarrow:"↭",leftthreetimes:"⋋",leg:"⋚",leq:"≤",leqq:"≦",leqslant:"⩽",les:"⩽",lescc:"⪨",lesdot:"⩿",lesdoto:"⪁",lesdotor:"⪃",lesg:"⋚︀",lesges:"⪓",lessapprox:"⪅",lessdot:"⋖",lesseqgtr:"⋚",lesseqqgtr:"⪋",lessgtr:"≶",lesssim:"≲",lfisht:"⥼",lfloor:"⌊",lfr:"\uD835\uDD29",lg:"≶",lgE:"⪑",lhard:"↽",lharu:"↼",lharul:"⥪",lhblk:"▄",ljcy:"љ",ll:"≪",llarr:"⇇",llcorner:"⌞",llhard:"⥫",lltri:"◺",lmidot:"ŀ",lmoust:"⎰",lmoustache:"⎰",lnE:"≨",lnap:"⪉",lnapprox:"⪉",lne:"⪇",lneq:"⪇",lneqq:"≨",lnsim:"⋦",loang:"⟬",loarr:"⇽",lobrk:"⟦",longleftarrow:"⟵",longleftrightarrow:"⟷",longmapsto:"⟼",longrightarrow:"⟶",looparrowleft:"↫",looparrowright:"↬",lopar:"⦅",lopf:"\uD835\uDD5D",loplus:"⨭",lotimes:"⨴",lowast:"∗",lowbar:"_",loz:"◊",lozenge:"◊",lozf:"⧫",lpar:"(",lparlt:"⦓",lrarr:"⇆",lrcorner:"⌟",lrhar:"⇋",lrhard:"⥭",lrm:"‎",lrtri:"⊿",lsaquo:"‹",lscr:"\uD835\uDCC1",lsh:"↰",lsim:"≲",lsime:"⪍",lsimg:"⪏",lsqb:"[",lsquo:"‘",lsquor:"‚",lstrok:"ł",lt:"<",ltcc:"⪦",ltcir:"⩹",ltdot:"⋖",lthree:"⋋",ltimes:"⋉",ltlarr:"⥶",ltquest:"⩻",ltrPar:"⦖",ltri:"◃",ltrie:"⊴",ltrif:"◂",lurdshar:"⥊",luruhar:"⥦",lvertneqq:"≨︀",lvnE:"≨︀",mDDot:"∺",macr:"\xaf",male:"♂",malt:"✠",maltese:"✠",map:"↦",mapsto:"↦",mapstodown:"↧",mapstoleft:"↤",mapstoup:"↥",marker:"▮",mcomma:"⨩",mcy:"м",mdash:"—",measuredangle:"∡",mfr:"\uD835\uDD2A",mho:"℧",micro:"\xb5",mid:"∣",midast:"*",midcir:"⫰",middot:"\xb7",minus:"−",minusb:"⊟",minusd:"∸",minusdu:"⨪",mlcp:"⫛",mldr:"…",mnplus:"∓",models:"⊧",mopf:"\uD835\uDD5E",mp:"∓",mscr:"\uD835\uDCC2",mstpos:"∾",mu:"μ",multimap:"⊸",mumap:"⊸",nGg:"⋙̸",nGt:"≫⃒",nGtv:"≫̸",nLeftarrow:"⇍",nLeftrightarrow:"⇎",nLl:"⋘̸",nLt:"≪⃒",nLtv:"≪̸",nRightarrow:"⇏",nVDash:"⊯",nVdash:"⊮",nabla:"∇",nacute:"ń",nang:"∠⃒",nap:"≉",napE:"⩰̸",napid:"≋̸",napos:"ŉ",napprox:"≉",natur:"♮",natural:"♮",naturals:"ℕ",nbsp:"\xa0",nbump:"≎̸",nbumpe:"≏̸",ncap:"⩃",ncaron:"ň",ncedil:"ņ",ncong:"≇",ncongdot:"⩭̸",ncup:"⩂",ncy:"н",ndash:"–",ne:"≠",neArr:"⇗",nearhk:"⤤",nearr:"↗",nearrow:"↗",nedot:"≐̸",nequiv:"≢",nesear:"⤨",nesim:"≂̸",nexist:"∄",nexists:"∄",nfr:"\uD835\uDD2B",ngE:"≧̸",nge:"≱",ngeq:"≱",ngeqq:"≧̸",ngeqslant:"⩾̸",nges:"⩾̸",ngsim:"≵",ngt:"≯",ngtr:"≯",nhArr:"⇎",nharr:"↮",nhpar:"⫲",ni:"∋",nis:"⋼",nisd:"⋺",niv:"∋",njcy:"њ",nlArr:"⇍",nlE:"≦̸",nlarr:"↚",nldr:"‥",nle:"≰",nleftarrow:"↚",nleftrightarrow:"↮",nleq:"≰",nleqq:"≦̸",nleqslant:"⩽̸",nles:"⩽̸",nless:"≮",nlsim:"≴",nlt:"≮",nltri:"⋪",nltrie:"⋬",nmid:"∤",nopf:"\uD835\uDD5F",not:"\xac",notin:"∉",notinE:"⋹̸",notindot:"⋵̸",notinva:"∉",notinvb:"⋷",notinvc:"⋶",notni:"∌",notniva:"∌",notnivb:"⋾",notnivc:"⋽",npar:"∦",nparallel:"∦",nparsl:"⫽⃥",npart:"∂̸",npolint:"⨔",npr:"⊀",nprcue:"⋠",npre:"⪯̸",nprec:"⊀",npreceq:"⪯̸",nrArr:"⇏",nrarr:"↛",nrarrc:"⤳̸",nrarrw:"↝̸",nrightarrow:"↛",nrtri:"⋫",nrtrie:"⋭",nsc:"⊁",nsccue:"⋡",nsce:"⪰̸",nscr:"\uD835\uDCC3",nshortmid:"∤",nshortparallel:"∦",nsim:"≁",nsime:"≄",nsimeq:"≄",nsmid:"∤",nspar:"∦",nsqsube:"⋢",nsqsupe:"⋣",nsub:"⊄",nsubE:"⫅̸",nsube:"⊈",nsubset:"⊂⃒",nsubseteq:"⊈",nsubseteqq:"⫅̸",nsucc:"⊁",nsucceq:"⪰̸",nsup:"⊅",nsupE:"⫆̸",nsupe:"⊉",nsupset:"⊃⃒",nsupseteq:"⊉",nsupseteqq:"⫆̸",ntgl:"≹",ntilde:"\xf1",ntlg:"≸",ntriangleleft:"⋪",ntrianglelefteq:"⋬",ntriangleright:"⋫",ntrianglerighteq:"⋭",nu:"ν",num:"#",numero:"№",numsp:" ",nvDash:"⊭",nvHarr:"⤄",nvap:"≍⃒",nvdash:"⊬",nvge:"≥⃒",nvgt:">⃒",nvinfin:"⧞",nvlArr:"⤂",nvle:"≤⃒",nvlt:"<⃒",nvltrie:"⊴⃒",nvrArr:"⤃",nvrtrie:"⊵⃒",nvsim:"∼⃒",nwArr:"⇖",nwarhk:"⤣",nwarr:"↖",nwarrow:"↖",nwnear:"⤧",oS:"Ⓢ",oacute:"\xf3",oast:"⊛",ocir:"⊚",ocirc:"\xf4",ocy:"о",odash:"⊝",odblac:"ő",odiv:"⨸",odot:"⊙",odsold:"⦼",oelig:"œ",ofcir:"⦿",ofr:"\uD835\uDD2C",ogon:"˛",ograve:"\xf2",ogt:"⧁",ohbar:"⦵",ohm:"Ω",oint:"∮",olarr:"↺",olcir:"⦾",olcross:"⦻",oline:"‾",olt:"⧀",omacr:"ō",omega:"ω",omicron:"ο",omid:"⦶",ominus:"⊖",oopf:"\uD835\uDD60",opar:"⦷",operp:"⦹",oplus:"⊕",or:"∨",orarr:"↻",ord:"⩝",order:"ℴ",orderof:"ℴ",ordf:"\xaa",ordm:"\xba",origof:"⊶",oror:"⩖",orslope:"⩗",orv:"⩛",oscr:"ℴ",oslash:"\xf8",osol:"⊘",otilde:"\xf5",otimes:"⊗",otimesas:"⨶",ouml:"\xf6",ovbar:"⌽",par:"∥",para:"\xb6",parallel:"∥",parsim:"⫳",parsl:"⫽",part:"∂",pcy:"п",percnt:"%",period:".",permil:"‰",perp:"⊥",pertenk:"‱",pfr:"\uD835\uDD2D",phi:"φ",phiv:"ϕ",phmmat:"ℳ",phone:"☎",pi:"π",pitchfork:"⋔",piv:"ϖ",planck:"ℏ",planckh:"ℎ",plankv:"ℏ",plus:"+",plusacir:"⨣",plusb:"⊞",pluscir:"⨢",plusdo:"∔",plusdu:"⨥",pluse:"⩲",plusmn:"\xb1",plussim:"⨦",plustwo:"⨧",pm:"\xb1",pointint:"⨕",popf:"\uD835\uDD61",pound:"\xa3",pr:"≺",prE:"⪳",prap:"⪷",prcue:"≼",pre:"⪯",prec:"≺",precapprox:"⪷",preccurlyeq:"≼",preceq:"⪯",precnapprox:"⪹",precneqq:"⪵",precnsim:"⋨",precsim:"≾",prime:"′",primes:"ℙ",prnE:"⪵",prnap:"⪹",prnsim:"⋨",prod:"∏",profalar:"⌮",profline:"⌒",profsurf:"⌓",prop:"∝",propto:"∝",prsim:"≾",prurel:"⊰",pscr:"\uD835\uDCC5",psi:"ψ",puncsp:" ",qfr:"\uD835\uDD2E",qint:"⨌",qopf:"\uD835\uDD62",qprime:"⁗",qscr:"\uD835\uDCC6",quaternions:"ℍ",quatint:"⨖",quest:"?",questeq:"≟",quot:'"',rAarr:"⇛",rArr:"⇒",rAtail:"⤜",rBarr:"⤏",rHar:"⥤",race:"∽̱",racute:"ŕ",radic:"√",raemptyv:"⦳",rang:"⟩",rangd:"⦒",range:"⦥",rangle:"⟩",raquo:"\xbb",rarr:"→",rarrap:"⥵",rarrb:"⇥",rarrbfs:"⤠",rarrc:"⤳",rarrfs:"⤞",rarrhk:"↪",rarrlp:"↬",rarrpl:"⥅",rarrsim:"⥴",rarrtl:"↣",rarrw:"↝",ratail:"⤚",ratio:"∶",rationals:"ℚ",rbarr:"⤍",rbbrk:"❳",rbrace:"}",rbrack:"]",rbrke:"⦌",rbrksld:"⦎",rbrkslu:"⦐",rcaron:"ř",rcedil:"ŗ",rceil:"⌉",rcub:"}",rcy:"р",rdca:"⤷",rdldhar:"⥩",rdquo:"”",rdquor:"”",rdsh:"↳",real:"ℜ",realine:"ℛ",realpart:"ℜ",reals:"ℝ",rect:"▭",reg:"\xae",rfisht:"⥽",rfloor:"⌋",rfr:"\uD835\uDD2F",rhard:"⇁",rharu:"⇀",rharul:"⥬",rho:"ρ",rhov:"ϱ",rightarrow:"→",rightarrowtail:"↣",rightharpoondown:"⇁",rightharpoonup:"⇀",rightleftarrows:"⇄",rightleftharpoons:"⇌",rightrightarrows:"⇉",rightsquigarrow:"↝",rightthreetimes:"⋌",ring:"˚",risingdotseq:"≓",rlarr:"⇄",rlhar:"⇌",rlm:"‏",rmoust:"⎱",rmoustache:"⎱",rnmid:"⫮",roang:"⟭",roarr:"⇾",robrk:"⟧",ropar:"⦆",ropf:"\uD835\uDD63",roplus:"⨮",rotimes:"⨵",rpar:")",rpargt:"⦔",rppolint:"⨒",rrarr:"⇉",rsaquo:"›",rscr:"\uD835\uDCC7",rsh:"↱",rsqb:"]",rsquo:"’",rsquor:"’",rthree:"⋌",rtimes:"⋊",rtri:"▹",rtrie:"⊵",rtrif:"▸",rtriltri:"⧎",ruluhar:"⥨",rx:"℞",sacute:"ś",sbquo:"‚",sc:"≻",scE:"⪴",scap:"⪸",scaron:"š",sccue:"≽",sce:"⪰",scedil:"ş",scirc:"ŝ",scnE:"⪶",scnap:"⪺",scnsim:"⋩",scpolint:"⨓",scsim:"≿",scy:"с",sdot:"⋅",sdotb:"⊡",sdote:"⩦",seArr:"⇘",searhk:"⤥",searr:"↘",searrow:"↘",sect:"\xa7",semi:";",seswar:"⤩",setminus:"∖",setmn:"∖",sext:"✶",sfr:"\uD835\uDD30",sfrown:"⌢",sharp:"♯",shchcy:"щ",shcy:"ш",shortmid:"∣",shortparallel:"∥",shy:"\xad",sigma:"σ",sigmaf:"ς",sigmav:"ς",sim:"∼",simdot:"⩪",sime:"≃",simeq:"≃",simg:"⪞",simgE:"⪠",siml:"⪝",simlE:"⪟",simne:"≆",simplus:"⨤",simrarr:"⥲",slarr:"←",smallsetminus:"∖",smashp:"⨳",smeparsl:"⧤",smid:"∣",smile:"⌣",smt:"⪪",smte:"⪬",smtes:"⪬︀",softcy:"ь",sol:"/",solb:"⧄",solbar:"⌿",sopf:"\uD835\uDD64",spades:"♠",spadesuit:"♠",spar:"∥",sqcap:"⊓",sqcaps:"⊓︀",sqcup:"⊔",sqcups:"⊔︀",sqsub:"⊏",sqsube:"⊑",sqsubset:"⊏",sqsubseteq:"⊑",sqsup:"⊐",sqsupe:"⊒",sqsupset:"⊐",sqsupseteq:"⊒",squ:"□",square:"□",squarf:"▪",squf:"▪",srarr:"→",sscr:"\uD835\uDCC8",ssetmn:"∖",ssmile:"⌣",sstarf:"⋆",star:"☆",starf:"★",straightepsilon:"ϵ",straightphi:"ϕ",strns:"\xaf",sub:"⊂",subE:"⫅",subdot:"⪽",sube:"⊆",subedot:"⫃",submult:"⫁",subnE:"⫋",subne:"⊊",subplus:"⪿",subrarr:"⥹",subset:"⊂",subseteq:"⊆",subseteqq:"⫅",subsetneq:"⊊",subsetneqq:"⫋",subsim:"⫇",subsub:"⫕",subsup:"⫓",succ:"≻",succapprox:"⪸",succcurlyeq:"≽",succeq:"⪰",succnapprox:"⪺",succneqq:"⪶",succnsim:"⋩",succsim:"≿",sum:"∑",sung:"♪",sup1:"\xb9",sup2:"\xb2",sup3:"\xb3",sup:"⊃",supE:"⫆",supdot:"⪾",supdsub:"⫘",supe:"⊇",supedot:"⫄",suphsol:"⟉",suphsub:"⫗",suplarr:"⥻",supmult:"⫂",supnE:"⫌",supne:"⊋",supplus:"⫀",supset:"⊃",supseteq:"⊇",supseteqq:"⫆",supsetneq:"⊋",supsetneqq:"⫌",supsim:"⫈",supsub:"⫔",supsup:"⫖",swArr:"⇙",swarhk:"⤦",swarr:"↙",swarrow:"↙",swnwar:"⤪",szlig:"\xdf",target:"⌖",tau:"τ",tbrk:"⎴",tcaron:"ť",tcedil:"ţ",tcy:"т",tdot:"⃛",telrec:"⌕",tfr:"\uD835\uDD31",there4:"∴",therefore:"∴",theta:"θ",thetasym:"ϑ",thetav:"ϑ",thickapprox:"≈",thicksim:"∼",thinsp:" ",thkap:"≈",thksim:"∼",thorn:"\xfe",tilde:"˜",times:"\xd7",timesb:"⊠",timesbar:"⨱",timesd:"⨰",tint:"∭",toea:"⤨",top:"⊤",topbot:"⌶",topcir:"⫱",topf:"\uD835\uDD65",topfork:"⫚",tosa:"⤩",tprime:"‴",trade:"™",triangle:"▵",triangledown:"▿",triangleleft:"◃",trianglelefteq:"⊴",triangleq:"≜",triangleright:"▹",trianglerighteq:"⊵",tridot:"◬",trie:"≜",triminus:"⨺",triplus:"⨹",trisb:"⧍",tritime:"⨻",trpezium:"⏢",tscr:"\uD835\uDCC9",tscy:"ц",tshcy:"ћ",tstrok:"ŧ",twixt:"≬",twoheadleftarrow:"↞",twoheadrightarrow:"↠",uArr:"⇑",uHar:"⥣",uacute:"\xfa",uarr:"↑",ubrcy:"ў",ubreve:"ŭ",ucirc:"\xfb",ucy:"у",udarr:"⇅",udblac:"ű",udhar:"⥮",ufisht:"⥾",ufr:"\uD835\uDD32",ugrave:"\xf9",uharl:"↿",uharr:"↾",uhblk:"▀",ulcorn:"⌜",ulcorner:"⌜",ulcrop:"⌏",ultri:"◸",umacr:"ū",uml:"\xa8",uogon:"ų",uopf:"\uD835\uDD66",uparrow:"↑",updownarrow:"↕",upharpoonleft:"↿",upharpoonright:"↾",uplus:"⊎",upsi:"υ",upsih:"ϒ",upsilon:"υ",upuparrows:"⇈",urcorn:"⌝",urcorner:"⌝",urcrop:"⌎",uring:"ů",urtri:"◹",uscr:"\uD835\uDCCA",utdot:"⋰",utilde:"ũ",utri:"▵",utrif:"▴",uuarr:"⇈",uuml:"\xfc",uwangle:"⦧",vArr:"⇕",vBar:"⫨",vBarv:"⫩",vDash:"⊨",vangrt:"⦜",varepsilon:"ϵ",varkappa:"ϰ",varnothing:"∅",varphi:"ϕ",varpi:"ϖ",varpropto:"∝",varr:"↕",varrho:"ϱ",varsigma:"ς",varsubsetneq:"⊊︀",varsubsetneqq:"⫋︀",varsupsetneq:"⊋︀",varsupsetneqq:"⫌︀",vartheta:"ϑ",vartriangleleft:"⊲",vartriangleright:"⊳",vcy:"в",vdash:"⊢",vee:"∨",veebar:"⊻",veeeq:"≚",vellip:"⋮",verbar:"|",vert:"|",vfr:"\uD835\uDD33",vltri:"⊲",vnsub:"⊂⃒",vnsup:"⊃⃒",vopf:"\uD835\uDD67",vprop:"∝",vrtri:"⊳",vscr:"\uD835\uDCCB",vsubnE:"⫋︀",vsubne:"⊊︀",vsupnE:"⫌︀",vsupne:"⊋︀",vzigzag:"⦚",wcirc:"ŵ",wedbar:"⩟",wedge:"∧",wedgeq:"≙",weierp:"℘",wfr:"\uD835\uDD34",wopf:"\uD835\uDD68",wp:"℘",wr:"≀",wreath:"≀",wscr:"\uD835\uDCCC",xcap:"⋂",xcirc:"◯",xcup:"⋃",xdtri:"▽",xfr:"\uD835\uDD35",xhArr:"⟺",xharr:"⟷",xi:"ξ",xlArr:"⟸",xlarr:"⟵",xmap:"⟼",xnis:"⋻",xodot:"⨀",xopf:"\uD835\uDD69",xoplus:"⨁",xotime:"⨂",xrArr:"⟹",xrarr:"⟶",xscr:"\uD835\uDCCD",xsqcup:"⨆",xuplus:"⨄",xutri:"△",xvee:"⋁",xwedge:"⋀",yacute:"\xfd",yacy:"я",ycirc:"ŷ",ycy:"ы",yen:"\xa5",yfr:"\uD835\uDD36",yicy:"ї",yopf:"\uD835\uDD6A",yscr:"\uD835\uDCCE",yucy:"ю",yuml:"\xff",zacute:"ź",zcaron:"ž",zcy:"з",zdot:"ż",zeetrf:"ℨ",zeta:"ζ",zfr:"\uD835\uDD37",zhcy:"ж",zigrarr:"⇝",zopf:"\uD835\uDD6B",zscr:"\uD835\uDCCF",zwj:"‍",zwnj:"‌"},e2={}.hasOwnProperty;function e4(e){return!!e2.call(e1,e)&&e1[e]}let e3={name:"characterReference",tokenize:function(e,t,n){let r,i,l=this,o=0;return function(t){return e.enter("characterReference"),e.enter("characterReferenceMarker"),e.consume(t),e.exit("characterReferenceMarker"),a};function a(t){return 35===t?(e.enter("characterReferenceMarkerNumeric"),e.consume(t),e.exit("characterReferenceMarkerNumeric"),s):(e.enter("characterReferenceValue"),r=31,i=ex.lV,u(t))}function s(t){return 88===t||120===t?(e.enter("characterReferenceMarkerHexadecimal"),e.consume(t),e.exit("characterReferenceMarkerHexadecimal"),e.enter("characterReferenceValue"),r=6,i=ex.ok,u):(e.enter("characterReferenceValue"),r=7,i=ex.BM,u(t))}function u(a){if(59===a&&o){let r=e.exit("characterReferenceValue");return i!==ex.lV||e4(l.sliceSerialize(r))?(e.enter("characterReferenceMarker"),e.consume(a),e.exit("characterReferenceMarker"),e.exit("characterReference"),t):n(a)}return i(a)&&o++<r?(e.consume(a),u):n(a)}}},e5={name:"characterEscape",tokenize:function(e,t,n){return function(t){return e.enter("characterEscape"),e.enter("escapeMarker"),e.consume(t),e.exit("escapeMarker"),r};function r(r){return(0,ex.ol)(r)?(e.enter("characterEscapeValue"),e.consume(r),e.exit("characterEscapeValue"),e.exit("characterEscape"),t):n(r)}}},e9={name:"lineEnding",tokenize:function(e,t){return function(n){return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),(0,ek.N)(e,t,"linePrefix")}}};var e6=n(35990);let e7={name:"labelEnd",resolveAll:function(e){let t=-1,n=[];for(;++t<e.length;){let r=e[t][1];if(n.push(e[t]),"labelImage"===r.type||"labelLink"===r.type||"labelEnd"===r.type){let e="labelImage"===r.type?4:2;r.type="data",t+=e}}return e.length!==n.length&&(0,em.m)(e,0,e.length,n),e},resolveTo:function(e,t){let n,r,i,l,o=e.length,a=0;for(;o--;)if(n=e[o][1],r){if("link"===n.type||"labelLink"===n.type&&n._inactive)break;"enter"===e[o][0]&&"labelLink"===n.type&&(n._inactive=!0)}else if(i){if("enter"===e[o][0]&&("labelImage"===n.type||"labelLink"===n.type)&&!n._balanced&&(r=o,"labelLink"!==n.type)){a=2;break}}else"labelEnd"===n.type&&(i=o);let s={type:"labelLink"===e[r][1].type?"link":"image",start:{...e[r][1].start},end:{...e[e.length-1][1].end}},u={type:"label",start:{...e[r][1].start},end:{...e[i][1].end}},c={type:"labelText",start:{...e[r+a+2][1].end},end:{...e[i-2][1].start}};return l=[["enter",s,t],["enter",u,t]],l=(0,em.V)(l,e.slice(r+1,r+a+3)),l=(0,em.V)(l,[["enter",c,t]]),l=(0,em.V)(l,(0,e6.W)(t.parser.constructs.insideSpan.null,e.slice(r+a+4,i-3),t)),l=(0,em.V)(l,[["exit",c,t],e[i-2],e[i-1],["exit",u,t]]),l=(0,em.V)(l,e.slice(i+1)),l=(0,em.V)(l,[["exit",s,t]]),(0,em.m)(e,r,e.length,l),e},tokenize:function(e,t,n){let r,i,l=this,o=l.events.length;for(;o--;)if(("labelImage"===l.events[o][1].type||"labelLink"===l.events[o][1].type)&&!l.events[o][1]._balanced){r=l.events[o][1];break}return function(t){return r?r._inactive?c(t):(i=l.parser.defined.includes((0,eU.B)(l.sliceSerialize({start:r.end,end:l.now()}))),e.enter("labelEnd"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelEnd"),a):n(t)};function a(t){return 40===t?e.attempt(e8,u,i?u:c)(t):91===t?e.attempt(te,u,i?s:c)(t):i?u(t):c(t)}function s(t){return e.attempt(tt,u,c)(t)}function u(e){return t(e)}function c(e){return r._balanced=!0,n(e)}}},e8={tokenize:function(e,t,n){return function(t){return e.enter("resource"),e.enter("resourceMarker"),e.consume(t),e.exit("resourceMarker"),r};function r(t){return(0,ex.Ee)(t)?e_(e,i)(t):i(t)}function i(t){return 41===t?u(t):eB(e,l,o,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(t)}function l(t){return(0,ex.Ee)(t)?e_(e,a)(t):u(t)}function o(e){return n(e)}function a(t){return 34===t||39===t||40===t?ej(e,s,n,"resourceTitle","resourceTitleMarker","resourceTitleString")(t):u(t)}function s(t){return(0,ex.Ee)(t)?e_(e,u)(t):u(t)}function u(r){return 41===r?(e.enter("resourceMarker"),e.consume(r),e.exit("resourceMarker"),e.exit("resource"),t):n(r)}}},te={tokenize:function(e,t,n){let r=this;return function(t){return eV.call(r,e,i,l,"reference","referenceMarker","referenceString")(t)};function i(e){return r.parser.defined.includes((0,eU.B)(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)))?t(e):n(e)}function l(e){return n(e)}}},tt={tokenize:function(e,t,n){return function(t){return e.enter("reference"),e.enter("referenceMarker"),e.consume(t),e.exit("referenceMarker"),r};function r(r){return 93===r?(e.enter("referenceMarker"),e.consume(r),e.exit("referenceMarker"),e.exit("reference"),t):n(r)}}},tn={name:"labelStartImage",resolveAll:e7.resolveAll,tokenize:function(e,t,n){let r=this;return function(t){return e.enter("labelImage"),e.enter("labelImageMarker"),e.consume(t),e.exit("labelImageMarker"),i};function i(t){return 91===t?(e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelImage"),l):n(t)}function l(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}}};var tr=n(57136);let ti={name:"attention",resolveAll:function(e,t){let n,r,i,l,o,a,s,u,c=-1;for(;++c<e.length;)if("enter"===e[c][0]&&"attentionSequence"===e[c][1].type&&e[c][1]._close){for(n=c;n--;)if("exit"===e[n][0]&&"attentionSequence"===e[n][1].type&&e[n][1]._open&&t.sliceSerialize(e[n][1]).charCodeAt(0)===t.sliceSerialize(e[c][1]).charCodeAt(0)){if((e[n][1]._close||e[c][1]._open)&&(e[c][1].end.offset-e[c][1].start.offset)%3&&!((e[n][1].end.offset-e[n][1].start.offset+e[c][1].end.offset-e[c][1].start.offset)%3))continue;a=e[n][1].end.offset-e[n][1].start.offset>1&&e[c][1].end.offset-e[c][1].start.offset>1?2:1;let f={...e[n][1].end},p={...e[c][1].start};tl(f,-a),tl(p,a),l={type:a>1?"strongSequence":"emphasisSequence",start:f,end:{...e[n][1].end}},o={type:a>1?"strongSequence":"emphasisSequence",start:{...e[c][1].start},end:p},i={type:a>1?"strongText":"emphasisText",start:{...e[n][1].end},end:{...e[c][1].start}},r={type:a>1?"strong":"emphasis",start:{...l.start},end:{...o.end}},e[n][1].end={...l.start},e[c][1].start={...o.end},s=[],e[n][1].end.offset-e[n][1].start.offset&&(s=(0,em.V)(s,[["enter",e[n][1],t],["exit",e[n][1],t]])),s=(0,em.V)(s,[["enter",r,t],["enter",l,t],["exit",l,t],["enter",i,t]]),s=(0,em.V)(s,(0,e6.W)(t.parser.constructs.insideSpan.null,e.slice(n+1,c),t)),s=(0,em.V)(s,[["exit",i,t],["enter",o,t],["exit",o,t],["exit",r,t]]),e[c][1].end.offset-e[c][1].start.offset?(u=2,s=(0,em.V)(s,[["enter",e[c][1],t],["exit",e[c][1],t]])):u=0,(0,em.m)(e,n-1,c-n+3,s),c=n+s.length-u-2;break}}for(c=-1;++c<e.length;)"attentionSequence"===e[c][1].type&&(e[c][1].type="data");return e},tokenize:function(e,t){let n,r=this.parser.constructs.attentionMarkers.null,i=this.previous,l=(0,tr.S)(i);return function(o){return n=o,e.enter("attentionSequence"),function o(a){if(a===n)return e.consume(a),o;let s=e.exit("attentionSequence"),u=(0,tr.S)(a),c=!u||2===u&&l||r.includes(a),f=!l||2===l&&u||r.includes(i);return s._open=!!(42===n?c:c&&(l||!f)),s._close=!!(42===n?f:f&&(u||!c)),t(a)}(o)}}};function tl(e,t){e.column+=t,e.offset+=t,e._bufferIndex+=t}let to={name:"labelStartLink",resolveAll:e7.resolveAll,tokenize:function(e,t,n){let r=this;return function(t){return e.enter("labelLink"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelLink"),i};function i(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}}},ta={42:eF,43:eF,45:eF,48:eF,49:eF,50:eF,51:eF,52:eF,53:eF,54:eF,55:eF,56:eF,57:eF,62:eH},ts={91:{name:"definition",tokenize:function(e,t,n){let r,i=this;return function(t){var r;return e.enter("definition"),r=t,eV.call(i,e,l,n,"definitionLabel","definitionLabelMarker","definitionLabelString")(r)};function l(t){return(r=(0,eU.B)(i.sliceSerialize(i.events[i.events.length-1][1]).slice(1,-1)),58===t)?(e.enter("definitionMarker"),e.consume(t),e.exit("definitionMarker"),o):n(t)}function o(t){return(0,ex.Ee)(t)?e_(e,a)(t):a(t)}function a(t){return eB(e,s,n,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(t)}function s(t){return e.attempt(eW,u,u)(t)}function u(t){return(0,ex.On)(t)?(0,ek.N)(e,c,"whitespace")(t):c(t)}function c(l){return null===l||(0,ex.HP)(l)?(e.exit("definition"),i.parser.defined.push(r),t(l)):n(l)}}}},tu={[-2]:eG,[-1]:eG,32:eG},tc={35:{name:"headingAtx",resolve:function(e,t){let n,r,i=e.length-2,l=3;return"whitespace"===e[3][1].type&&(l+=2),i-2>l&&"whitespace"===e[i][1].type&&(i-=2),"atxHeadingSequence"===e[i][1].type&&(l===i-1||i-4>l&&"whitespace"===e[i-2][1].type)&&(i-=l+1===i?2:4),i>l&&(n={type:"atxHeadingText",start:e[l][1].start,end:e[i][1].end},r={type:"chunkText",start:e[l][1].start,end:e[i][1].end,contentType:"text"},(0,em.m)(e,l,i-l+1,[["enter",n,t],["enter",r,t],["exit",r,t],["exit",n,t]])),e},tokenize:function(e,t,n){let r=0;return function(i){var l;return e.enter("atxHeading"),l=i,e.enter("atxHeadingSequence"),function i(l){return 35===l&&r++<6?(e.consume(l),i):null===l||(0,ex.Ee)(l)?(e.exit("atxHeadingSequence"),function n(r){return 35===r?(e.enter("atxHeadingSequence"),function t(r){return 35===r?(e.consume(r),t):(e.exit("atxHeadingSequence"),n(r))}(r)):null===r||(0,ex.HP)(r)?(e.exit("atxHeading"),t(r)):(0,ex.On)(r)?(0,ek.N)(e,n,"whitespace")(r):(e.enter("atxHeadingText"),function t(r){return null===r||35===r||(0,ex.Ee)(r)?(e.exit("atxHeadingText"),n(r)):(e.consume(r),t)}(r))}(l)):n(l)}(l)}}},42:eR,45:[e$,eR],60:{concrete:!0,name:"htmlFlow",resolveTo:function(e){let t=e.length;for(;t--&&("enter"!==e[t][0]||"htmlFlow"!==e[t][1].type););return t>1&&"linePrefix"===e[t-2][1].type&&(e[t][1].start=e[t-2][1].start,e[t+1][1].start=e[t-2][1].start,e.splice(t-2,2)),e},tokenize:function(e,t,n){let r,i,l,o,a,s=this;return function(t){var n;return n=t,e.enter("htmlFlow"),e.enter("htmlFlowData"),e.consume(n),u};function u(o){return 33===o?(e.consume(o),c):47===o?(e.consume(o),i=!0,d):63===o?(e.consume(o),r=3,s.interrupt?t:q):(0,ex.CW)(o)?(e.consume(o),l=String.fromCharCode(o),h):n(o)}function c(i){return 45===i?(e.consume(i),r=2,f):91===i?(e.consume(i),r=5,o=0,p):(0,ex.CW)(i)?(e.consume(i),r=4,s.interrupt?t:q):n(i)}function f(r){return 45===r?(e.consume(r),s.interrupt?t:q):n(r)}function p(r){let i="CDATA[";return r===i.charCodeAt(o++)?(e.consume(r),o===i.length)?s.interrupt?t:C:p:n(r)}function d(t){return(0,ex.CW)(t)?(e.consume(t),l=String.fromCharCode(t),h):n(t)}function h(o){if(null===o||47===o||62===o||(0,ex.Ee)(o)){let a=47===o,u=l.toLowerCase();return!a&&!i&&eJ.includes(u)?(r=1,s.interrupt?t(o):C(o)):eQ.includes(l.toLowerCase())?(r=6,a)?(e.consume(o),m):s.interrupt?t(o):C(o):(r=7,s.interrupt&&!s.parser.lazy[s.now().line]?n(o):i?function t(n){return(0,ex.On)(n)?(e.consume(n),t):w(n)}(o):g(o))}return 45===o||(0,ex.lV)(o)?(e.consume(o),l+=String.fromCharCode(o),h):n(o)}function m(r){return 62===r?(e.consume(r),s.interrupt?t:C):n(r)}function g(t){return 47===t?(e.consume(t),w):58===t||95===t||(0,ex.CW)(t)?(e.consume(t),y):(0,ex.On)(t)?(e.consume(t),g):w(t)}function y(t){return 45===t||46===t||58===t||95===t||(0,ex.lV)(t)?(e.consume(t),y):b(t)}function b(t){return 61===t?(e.consume(t),v):(0,ex.On)(t)?(e.consume(t),b):g(t)}function v(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),a=t,k):(0,ex.On)(t)?(e.consume(t),v):function t(n){return null===n||34===n||39===n||47===n||60===n||61===n||62===n||96===n||(0,ex.Ee)(n)?b(n):(e.consume(n),t)}(t)}function k(t){return t===a?(e.consume(t),a=null,x):null===t||(0,ex.HP)(t)?n(t):(e.consume(t),k)}function x(e){return 47===e||62===e||(0,ex.On)(e)?g(e):n(e)}function w(t){return 62===t?(e.consume(t),S):n(t)}function S(t){return null===t||(0,ex.HP)(t)?C(t):(0,ex.On)(t)?(e.consume(t),S):n(t)}function C(t){return 45===t&&2===r?(e.consume(t),A):60===t&&1===r?(e.consume(t),L):62===t&&4===r?(e.consume(t),N):63===t&&3===r?(e.consume(t),q):93===t&&5===r?(e.consume(t),I):(0,ex.HP)(t)&&(6===r||7===r)?(e.exit("htmlFlowData"),e.check(eK,O,E)(t)):null===t||(0,ex.HP)(t)?(e.exit("htmlFlowData"),E(t)):(e.consume(t),C)}function E(t){return e.check(eZ,D,O)(t)}function D(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),T}function T(t){return null===t||(0,ex.HP)(t)?E(t):(e.enter("htmlFlowData"),C(t))}function A(t){return 45===t?(e.consume(t),q):C(t)}function L(t){return 47===t?(e.consume(t),l="",P):C(t)}function P(t){if(62===t){let n=l.toLowerCase();return eJ.includes(n)?(e.consume(t),N):C(t)}return(0,ex.CW)(t)&&l.length<8?(e.consume(t),l+=String.fromCharCode(t),P):C(t)}function I(t){return 93===t?(e.consume(t),q):C(t)}function q(t){return 62===t?(e.consume(t),N):45===t&&2===r?(e.consume(t),q):C(t)}function N(t){return null===t||(0,ex.HP)(t)?(e.exit("htmlFlowData"),O(t)):(e.consume(t),N)}function O(n){return e.exit("htmlFlow"),t(n)}}},61:e$,95:eR,96:e0,126:e0},tf={38:e3,92:e5},tp={[-5]:e9,[-4]:e9,[-3]:e9,33:tn,38:e3,42:ti,60:[{name:"autolink",tokenize:function(e,t,n){let r=0;return function(t){return e.enter("autolink"),e.enter("autolinkMarker"),e.consume(t),e.exit("autolinkMarker"),e.enter("autolinkProtocol"),i};function i(t){return(0,ex.CW)(t)?(e.consume(t),l):64===t?n(t):a(t)}function l(t){return 43===t||45===t||46===t||(0,ex.lV)(t)?(r=1,function t(n){return 58===n?(e.consume(n),r=0,o):(43===n||45===n||46===n||(0,ex.lV)(n))&&r++<32?(e.consume(n),t):(r=0,a(n))}(t)):a(t)}function o(r){return 62===r?(e.exit("autolinkProtocol"),e.enter("autolinkMarker"),e.consume(r),e.exit("autolinkMarker"),e.exit("autolink"),t):null===r||32===r||60===r||(0,ex.JQ)(r)?n(r):(e.consume(r),o)}function a(t){return 64===t?(e.consume(t),s):(0,ex.cx)(t)?(e.consume(t),a):n(t)}function s(i){return(0,ex.lV)(i)?function i(l){return 46===l?(e.consume(l),r=0,s):62===l?(e.exit("autolinkProtocol").type="autolinkEmail",e.enter("autolinkMarker"),e.consume(l),e.exit("autolinkMarker"),e.exit("autolink"),t):function t(l){if((45===l||(0,ex.lV)(l))&&r++<63){let n=45===l?t:i;return e.consume(l),n}return n(l)}(l)}(i):n(i)}}},{name:"htmlText",tokenize:function(e,t,n){let r,i,l,o=this;return function(t){return e.enter("htmlText"),e.enter("htmlTextData"),e.consume(t),a};function a(t){return 33===t?(e.consume(t),s):47===t?(e.consume(t),k):63===t?(e.consume(t),b):(0,ex.CW)(t)?(e.consume(t),w):n(t)}function s(t){return 45===t?(e.consume(t),u):91===t?(e.consume(t),i=0,d):(0,ex.CW)(t)?(e.consume(t),y):n(t)}function u(t){return 45===t?(e.consume(t),p):n(t)}function c(t){return null===t?n(t):45===t?(e.consume(t),f):(0,ex.HP)(t)?(l=c,P(t)):(e.consume(t),c)}function f(t){return 45===t?(e.consume(t),p):c(t)}function p(e){return 62===e?L(e):45===e?f(e):c(e)}function d(t){let r="CDATA[";return t===r.charCodeAt(i++)?(e.consume(t),i===r.length?h:d):n(t)}function h(t){return null===t?n(t):93===t?(e.consume(t),m):(0,ex.HP)(t)?(l=h,P(t)):(e.consume(t),h)}function m(t){return 93===t?(e.consume(t),g):h(t)}function g(t){return 62===t?L(t):93===t?(e.consume(t),g):h(t)}function y(t){return null===t||62===t?L(t):(0,ex.HP)(t)?(l=y,P(t)):(e.consume(t),y)}function b(t){return null===t?n(t):63===t?(e.consume(t),v):(0,ex.HP)(t)?(l=b,P(t)):(e.consume(t),b)}function v(e){return 62===e?L(e):b(e)}function k(t){return(0,ex.CW)(t)?(e.consume(t),x):n(t)}function x(t){return 45===t||(0,ex.lV)(t)?(e.consume(t),x):function t(n){return(0,ex.HP)(n)?(l=t,P(n)):(0,ex.On)(n)?(e.consume(n),t):L(n)}(t)}function w(t){return 45===t||(0,ex.lV)(t)?(e.consume(t),w):47===t||62===t||(0,ex.Ee)(t)?S(t):n(t)}function S(t){return 47===t?(e.consume(t),L):58===t||95===t||(0,ex.CW)(t)?(e.consume(t),C):(0,ex.HP)(t)?(l=S,P(t)):(0,ex.On)(t)?(e.consume(t),S):L(t)}function C(t){return 45===t||46===t||58===t||95===t||(0,ex.lV)(t)?(e.consume(t),C):function t(n){return 61===n?(e.consume(n),E):(0,ex.HP)(n)?(l=t,P(n)):(0,ex.On)(n)?(e.consume(n),t):S(n)}(t)}function E(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),r=t,D):(0,ex.HP)(t)?(l=E,P(t)):(0,ex.On)(t)?(e.consume(t),E):(e.consume(t),T)}function D(t){return t===r?(e.consume(t),r=void 0,A):null===t?n(t):(0,ex.HP)(t)?(l=D,P(t)):(e.consume(t),D)}function T(t){return null===t||34===t||39===t||60===t||61===t||96===t?n(t):47===t||62===t||(0,ex.Ee)(t)?S(t):(e.consume(t),T)}function A(e){return 47===e||62===e||(0,ex.Ee)(e)?S(e):n(e)}function L(r){return 62===r?(e.consume(r),e.exit("htmlTextData"),e.exit("htmlText"),t):n(r)}function P(t){return e.exit("htmlTextData"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),I}function I(t){return(0,ex.On)(t)?(0,ek.N)(e,q,"linePrefix",o.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):q(t)}function q(t){return e.enter("htmlTextData"),l(t)}}}],91:to,92:[{name:"hardBreakEscape",tokenize:function(e,t,n){return function(t){return e.enter("hardBreakEscape"),e.consume(t),r};function r(r){return(0,ex.HP)(r)?(e.exit("hardBreakEscape"),t(r)):n(r)}}},e5],93:e7,95:ti,96:{name:"codeText",previous:function(e){return 96!==e||"characterEscape"===this.events[this.events.length-1][1].type},resolve:function(e){let t,n,r=e.length-4,i=3;if(("lineEnding"===e[3][1].type||"space"===e[i][1].type)&&("lineEnding"===e[r][1].type||"space"===e[r][1].type)){for(t=i;++t<r;)if("codeTextData"===e[t][1].type){e[i][1].type="codeTextPadding",e[r][1].type="codeTextPadding",i+=2,r-=2;break}}for(t=i-1,r++;++t<=r;)void 0===n?t!==r&&"lineEnding"!==e[t][1].type&&(n=t):(t===r||"lineEnding"===e[t][1].type)&&(e[n][1].type="codeTextData",t!==n+2&&(e[n][1].end=e[t-1][1].end,e.splice(n+2,t-n-2),r-=t-n-2,t=n+2),n=void 0);return e},tokenize:function(e,t,n){let r,i,l=0;return function(t){return e.enter("codeText"),e.enter("codeTextSequence"),function t(n){return 96===n?(e.consume(n),l++,t):(e.exit("codeTextSequence"),o(n))}(t)};function o(s){return null===s?n(s):32===s?(e.enter("space"),e.consume(s),e.exit("space"),o):96===s?(i=e.enter("codeTextSequence"),r=0,function n(o){return 96===o?(e.consume(o),r++,n):r===l?(e.exit("codeTextSequence"),e.exit("codeText"),t(o)):(i.type="codeTextData",a(o))}(s)):(0,ex.HP)(s)?(e.enter("lineEnding"),e.consume(s),e.exit("lineEnding"),o):(e.enter("codeTextData"),a(s))}function a(t){return null===t||32===t||96===t||(0,ex.HP)(t)?(e.exit("codeTextData"),o(t)):(e.consume(t),a)}}}},td={null:[ti,eL]},th={null:[42,95]},tm={null:[]},tg=/[\0\t\n\r]/g;function ty(e,t){let n=Number.parseInt(e,t);return n<9||11===n||n>13&&n<32||n>126&&n<160||n>55295&&n<57344||n>64975&&n<65008||(65535&n)==65535||(65535&n)==65534||n>1114111?"�":String.fromCodePoint(n)}let tb=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function tv(e,t,n){if(t)return t;if(35===n.charCodeAt(0)){let e=n.charCodeAt(1),t=120===e||88===e;return ty(n.slice(t?2:1),t?16:10)}return e4(n)||e}let tk={}.hasOwnProperty;function tx(e){return{line:e.line,column:e.column,offset:e.offset}}function tw(e,t){if(e)throw Error("Cannot close `"+e.type+"` ("+$({start:e.start,end:e.end})+"): a different token (`"+t.type+"`, "+$({start:t.start,end:t.end})+") is open");throw Error("Cannot close document, a token (`"+t.type+"`, "+$({start:t.start,end:t.end})+") is still open")}function tS(e){let t=this;t.parser=function(n){var r,l;let o,a,s,u;return"string"!=typeof(r={...t.data("settings"),...e,extensions:t.data("micromarkExtensions")||[],mdastExtensions:t.data("fromMarkdownExtensions")||[]})&&(l=r,r=void 0),(function(e){let t={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:r(y),autolinkProtocol:u,autolinkEmail:u,atxHeading:r(h),blockQuote:r(function(){return{type:"blockquote",children:[]}}),characterEscape:u,characterReference:u,codeFenced:r(d),codeFencedFenceInfo:i,codeFencedFenceMeta:i,codeIndented:r(d,i),codeText:r(function(){return{type:"inlineCode",value:""}},i),codeTextData:u,data:u,codeFlowValue:u,definition:r(function(){return{type:"definition",identifier:"",label:null,title:null,url:""}}),definitionDestinationString:i,definitionLabelString:i,definitionTitleString:i,emphasis:r(function(){return{type:"emphasis",children:[]}}),hardBreakEscape:r(m),hardBreakTrailing:r(m),htmlFlow:r(g,i),htmlFlowData:u,htmlText:r(g,i),htmlTextData:u,image:r(function(){return{type:"image",title:null,url:"",alt:null}}),label:i,link:r(y),listItem:r(function(e){return{type:"listItem",spread:e._spread,checked:null,children:[]}}),listItemValue:function(e){this.data.expectingFirstListItemValue&&(this.stack[this.stack.length-2].start=Number.parseInt(this.sliceSerialize(e),10),this.data.expectingFirstListItemValue=void 0)},listOrdered:r(b,function(){this.data.expectingFirstListItemValue=!0}),listUnordered:r(b),paragraph:r(function(){return{type:"paragraph",children:[]}}),reference:function(){this.data.referenceType="collapsed"},referenceString:i,resourceDestinationString:i,resourceTitleString:i,setextHeading:r(h),strong:r(function(){return{type:"strong",children:[]}}),thematicBreak:r(function(){return{type:"thematicBreak"}})},exit:{atxHeading:o(),atxHeadingSequence:function(e){let t=this.stack[this.stack.length-1];t.depth||(t.depth=this.sliceSerialize(e).length)},autolink:o(),autolinkEmail:function(e){c.call(this,e),this.stack[this.stack.length-1].url="mailto:"+this.sliceSerialize(e)},autolinkProtocol:function(e){c.call(this,e),this.stack[this.stack.length-1].url=this.sliceSerialize(e)},blockQuote:o(),characterEscapeValue:c,characterReferenceMarkerHexadecimal:p,characterReferenceMarkerNumeric:p,characterReferenceValue:function(e){let t,n=this.sliceSerialize(e),r=this.data.characterReferenceType;r?(t=ty(n,"characterReferenceMarkerNumeric"===r?10:16),this.data.characterReferenceType=void 0):t=e4(n);let i=this.stack[this.stack.length-1];i.value+=t},characterReference:function(e){this.stack.pop().position.end=tx(e.end)},codeFenced:o(function(){let e=this.resume();this.stack[this.stack.length-1].value=e.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0}),codeFencedFence:function(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)},codeFencedFenceInfo:function(){let e=this.resume();this.stack[this.stack.length-1].lang=e},codeFencedFenceMeta:function(){let e=this.resume();this.stack[this.stack.length-1].meta=e},codeFlowValue:c,codeIndented:o(function(){let e=this.resume();this.stack[this.stack.length-1].value=e.replace(/(\r?\n|\r)$/g,"")}),codeText:o(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),codeTextData:c,data:c,definition:o(),definitionDestinationString:function(){let e=this.resume();this.stack[this.stack.length-1].url=e},definitionLabelString:function(e){let t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=(0,eU.B)(this.sliceSerialize(e)).toLowerCase()},definitionTitleString:function(){let e=this.resume();this.stack[this.stack.length-1].title=e},emphasis:o(),hardBreakEscape:o(f),hardBreakTrailing:o(f),htmlFlow:o(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),htmlFlowData:c,htmlText:o(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),htmlTextData:c,image:o(function(){let e=this.stack[this.stack.length-1];if(this.data.inReference){let t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}),label:function(){let e=this.stack[this.stack.length-1],t=this.resume(),n=this.stack[this.stack.length-1];this.data.inReference=!0,"link"===n.type?n.children=e.children:n.alt=t},labelText:function(e){let t=this.sliceSerialize(e),n=this.stack[this.stack.length-2];n.label=t.replace(tb,tv),n.identifier=(0,eU.B)(t).toLowerCase()},lineEnding:function(e){let n=this.stack[this.stack.length-1];if(this.data.atHardBreak){n.children[n.children.length-1].position.end=tx(e.end),this.data.atHardBreak=void 0;return}!this.data.setextHeadingSlurpLineEnding&&t.canContainEols.includes(n.type)&&(u.call(this,e),c.call(this,e))},link:o(function(){let e=this.stack[this.stack.length-1];if(this.data.inReference){let t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}),listItem:o(),listOrdered:o(),listUnordered:o(),paragraph:o(),referenceString:function(e){let t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=(0,eU.B)(this.sliceSerialize(e)).toLowerCase(),this.data.referenceType="full"},resourceDestinationString:function(){let e=this.resume();this.stack[this.stack.length-1].url=e},resourceTitleString:function(){let e=this.resume();this.stack[this.stack.length-1].title=e},resource:function(){this.data.inReference=void 0},setextHeading:o(function(){this.data.setextHeadingSlurpLineEnding=void 0}),setextHeadingLineSequence:function(e){this.stack[this.stack.length-1].depth=61===this.sliceSerialize(e).codePointAt(0)?1:2},setextHeadingText:function(){this.data.setextHeadingSlurpLineEnding=!0},strong:o(),thematicBreak:o()}};!function e(t,n){let r=-1;for(;++r<n.length;){let i=n[r];Array.isArray(i)?e(t,i):function(e,t){let n;for(n in t)if(tk.call(t,n))switch(n){case"canContainEols":{let r=t[n];r&&e[n].push(...r);break}case"transforms":{let r=t[n];r&&e[n].push(...r);break}case"enter":case"exit":{let r=t[n];r&&Object.assign(e[n],r)}}}(t,i)}}(t,(e||{}).mdastExtensions||[]);let n={};return function(e){let r={type:"root",children:[]},o={stack:[r],tokenStack:[],config:t,enter:l,exit:a,buffer:i,resume:s,data:n},u=[],c=-1;for(;++c<e.length;)("listOrdered"===e[c][1].type||"listUnordered"===e[c][1].type)&&("enter"===e[c][0]?u.push(c):c=function(e,t,n){let r,i,l,o,a=t-1,s=-1,u=!1;for(;++a<=n;){let t=e[a];switch(t[1].type){case"listUnordered":case"listOrdered":case"blockQuote":"enter"===t[0]?s++:s--,o=void 0;break;case"lineEndingBlank":"enter"===t[0]&&(!r||o||s||l||(l=a),o=void 0);break;case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:o=void 0}if(!s&&"enter"===t[0]&&"listItemPrefix"===t[1].type||-1===s&&"exit"===t[0]&&("listUnordered"===t[1].type||"listOrdered"===t[1].type)){if(r){let o=a;for(i=void 0;o--;){let t=e[o];if("lineEnding"===t[1].type||"lineEndingBlank"===t[1].type){if("exit"===t[0])continue;i&&(e[i][1].type="lineEndingBlank",u=!0),t[1].type="lineEnding",i=o}else if("linePrefix"===t[1].type||"blockQuotePrefix"===t[1].type||"blockQuotePrefixWhitespace"===t[1].type||"blockQuoteMarker"===t[1].type||"listItemIndent"===t[1].type);else break}l&&(!i||l<i)&&(r._spread=!0),r.end=Object.assign({},i?e[i][1].start:t[1].end),e.splice(i||a,0,["exit",r,t[2]]),a++,n++}if("listItemPrefix"===t[1].type){let i={type:"listItem",_spread:!1,start:Object.assign({},t[1].start),end:void 0};r=i,e.splice(a,0,["enter",i,t[2]]),a++,n++,l=void 0,o=!0}}}return e[t][1]._spread=u,n}(e,u.pop(),c));for(c=-1;++c<e.length;){let n=t[e[c][0]];tk.call(n,e[c][1].type)&&n[e[c][1].type].call(Object.assign({sliceSerialize:e[c][2].sliceSerialize},o),e[c][1])}if(o.tokenStack.length>0){let e=o.tokenStack[o.tokenStack.length-1];(e[1]||tw).call(o,void 0,e[0])}for(r.position={start:tx(e.length>0?e[0][1].start:{line:1,column:1,offset:0}),end:tx(e.length>0?e[e.length-2][1].end:{line:1,column:1,offset:0})},c=-1;++c<t.transforms.length;)r=t.transforms[c](r)||r;return r};function r(e,t){return function(n){l.call(this,e(n),n),t&&t.call(this,n)}}function i(){this.stack.push({type:"fragment",children:[]})}function l(e,t,n){this.stack[this.stack.length-1].children.push(e),this.stack.push(e),this.tokenStack.push([t,n||void 0]),e.position={start:tx(t.start),end:void 0}}function o(e){return function(t){e&&e.call(this,t),a.call(this,t)}}function a(e,t){let n=this.stack.pop(),r=this.tokenStack.pop();if(r)r[0].type!==e.type&&(t?t.call(this,e,r[0]):(r[1]||tw).call(this,e,r[0]));else throw Error("Cannot close `"+e.type+"` ("+$({start:e.start,end:e.end})+"): it’s not open");n.position.end=tx(e.end)}function s(){return(0,eh.d)(this.stack.pop())}function u(e){let t=this.stack[this.stack.length-1].children,n=t[t.length-1];n&&"text"===n.type||((n={type:"text",value:""}).position={start:tx(e.start),end:void 0},t.push(n)),this.stack.push(n)}function c(e){let t=this.stack.pop();t.value+=this.sliceSerialize(e),t.position.end=tx(e.end)}function f(){this.data.atHardBreak=!0}function p(e){this.data.characterReferenceType=e.type}function d(){return{type:"code",lang:null,meta:null,value:""}}function h(){return{type:"heading",depth:0,children:[]}}function m(){return{type:"break"}}function g(){return{type:"html",value:""}}function y(){return{type:"link",title:null,url:"",children:[]}}function b(e){return{type:"list",ordered:"listOrdered"===e.type,start:null,spread:e._spread,children:[]}}})(l)(function(e){for(;!eb(e););return e}((function(e){let t={constructs:(0,ev.y)([i,...(e||{}).extensions||[]]),content:n(ew),defined:[],document:n(eS),flow:n(eA),lazy:{},string:n(eP),text:n(eI)};return t;function n(e){return function(n){return function(e,t,n){let r={_bufferIndex:-1,_index:0,line:n&&n.line||1,column:n&&n.column||1,offset:n&&n.offset||0},i={},l=[],o=[],a=[],s={attempt:h(function(e,t){m(e,t.from)}),check:h(d),consume:function(e){(0,ex.HP)(e)?(r.line++,r.column=1,r.offset+=-3===e?2:1,g()):-1!==e&&(r.column++,r.offset++),r._bufferIndex<0?r._index++:(r._bufferIndex++,r._bufferIndex===o[r._index].length&&(r._bufferIndex=-1,r._index++)),u.previous=e},enter:function(e,t){let n=t||{};return n.type=e,n.start=p(),u.events.push(["enter",n,u]),a.push(n),n},exit:function(e){let t=a.pop();return t.end=p(),u.events.push(["exit",t,u]),t},interrupt:h(d,{interrupt:!0})},u={code:null,containerState:{},defineSkip:function(e){i[e.line]=e.column,g()},events:[],now:p,parser:e,previous:null,sliceSerialize:function(e,t){return function(e,t){let n,r=-1,i=[];for(;++r<e.length;){let l,o=e[r];if("string"==typeof o)l=o;else switch(o){case -5:l="\r";break;case -4:l="\n";break;case -3:l="\r\n";break;case -2:l=t?" ":"	";break;case -1:if(!t&&n)continue;l=" ";break;default:l=String.fromCharCode(o)}n=-2===o,i.push(l)}return i.join("")}(f(e),t)},sliceStream:f,write:function(e){return(o=(0,em.V)(o,e),function(){let e;for(;r._index<o.length;){let n=o[r._index];if("string"==typeof n)for(e=r._index,r._bufferIndex<0&&(r._bufferIndex=0);r._index===e&&r._bufferIndex<n.length;){var t;t=n.charCodeAt(r._bufferIndex),c=c(t)}else c=c(n)}}(),null!==o[o.length-1])?[]:(m(t,0),u.events=(0,e6.W)(l,u.events,u),u.events)}},c=t.tokenize.call(u,s);return t.resolveAll&&l.push(t),u;function f(e){return function(e,t){let n,r=t.start._index,i=t.start._bufferIndex,l=t.end._index,o=t.end._bufferIndex;if(r===l)n=[e[r].slice(i,o)];else{if(n=e.slice(r,l),i>-1){let e=n[0];"string"==typeof e?n[0]=e.slice(i):n.shift()}o>0&&n.push(e[l].slice(0,o))}return n}(o,e)}function p(){let{_bufferIndex:e,_index:t,line:n,column:i,offset:l}=r;return{_bufferIndex:e,_index:t,line:n,column:i,offset:l}}function d(e,t){t.restore()}function h(e,t){return function(n,i,l){var o;let c,f,d,h;return Array.isArray(n)?m(n):"tokenize"in n?m([n]):(o=n,function(e){let t=null!==e&&o[e],n=null!==e&&o.null;return m([...Array.isArray(t)?t:t?[t]:[],...Array.isArray(n)?n:n?[n]:[]])(e)});function m(e){return(c=e,f=0,0===e.length)?l:y(e[f])}function y(e){return function(n){return(h=function(){let e=p(),t=u.previous,n=u.currentConstruct,i=u.events.length,l=Array.from(a);return{from:i,restore:function(){r=e,u.previous=t,u.currentConstruct=n,u.events.length=i,a=l,g()}}}(),d=e,e.partial||(u.currentConstruct=e),e.name&&u.parser.constructs.disable.null.includes(e.name))?v(n):e.tokenize.call(t?Object.assign(Object.create(u),t):u,s,b,v)(n)}}function b(t){return e(d,h),i}function v(e){return(h.restore(),++f<c.length)?y(c[f]):l}}}function m(e,t){e.resolveAll&&!l.includes(e)&&l.push(e),e.resolve&&(0,em.m)(u.events,t,u.events.length-t,e.resolve(u.events.slice(t),u)),e.resolveTo&&(u.events=e.resolveTo(u.events,u))}function g(){r.line in i&&r.column<2&&(r.column=i[r.line],r.offset+=i[r.line]-1)}}(t,e,n)}}})(l).document().write((a=1,s="",u=!0,function(e,t,n){let r,i,l,c,f,p=[];for(e=s+("string"==typeof e?e.toString():new TextDecoder(t||void 0).decode(e)),l=0,s="",u&&(65279===e.charCodeAt(0)&&l++,u=void 0);l<e.length;){if(tg.lastIndex=l,c=(r=tg.exec(e))&&void 0!==r.index?r.index:e.length,f=e.charCodeAt(c),!r){s=e.slice(l);break}if(10===f&&l===c&&o)p.push(-3),o=void 0;else switch(o&&(p.push(-5),o=void 0),l<c&&(p.push(e.slice(l,c)),a+=c-l),f){case 0:p.push(65533),a++;break;case 9:for(i=4*Math.ceil(a/4),p.push(-2);a++<i;)p.push(-1);break;case 10:p.push(-4),a=1;break;default:o=!0,a=1}l=c+1}return n&&(o&&p.push(-5),s&&p.push(s),p.push(null)),p})(n,r,!0))))}}let tC="object"==typeof self?self:globalThis,tE=(e,t)=>{let n=(t,n)=>(e.set(n,t),t),r=i=>{if(e.has(i))return e.get(i);let[l,o]=t[i];switch(l){case 0:case -1:return n(o,i);case 1:{let e=n([],i);for(let t of o)e.push(r(t));return e}case 2:{let e=n({},i);for(let[t,n]of o)e[r(t)]=r(n);return e}case 3:return n(new Date(o),i);case 4:{let{source:e,flags:t}=o;return n(new RegExp(e,t),i)}case 5:{let e=n(new Map,i);for(let[t,n]of o)e.set(r(t),r(n));return e}case 6:{let e=n(new Set,i);for(let t of o)e.add(r(t));return e}case 7:{let{name:e,message:t}=o;return n(new tC[e](t),i)}case 8:return n(BigInt(o),i);case"BigInt":return n(Object(BigInt(o)),i);case"ArrayBuffer":return n(new Uint8Array(o).buffer,o);case"DataView":{let{buffer:e}=new Uint8Array(o);return n(new DataView(e),o)}}return n(new tC[l](o),i)};return r},tD=e=>tE(new Map,e)(0),{toString:tT}={},{keys:tA}=Object,tL=e=>{let t=typeof e;if("object"!==t||!e)return[0,t];let n=tT.call(e).slice(8,-1);switch(n){case"Array":return[1,""];case"Object":return[2,""];case"Date":return[3,""];case"RegExp":return[4,""];case"Map":return[5,""];case"Set":return[6,""];case"DataView":return[1,n]}return n.includes("Array")?[1,n]:n.includes("Error")?[7,n]:[2,n]},tP=([e,t])=>0===e&&("function"===t||"symbol"===t),tI=(e,t,n,r)=>{let i=(e,t)=>{let i=r.push(e)-1;return n.set(t,i),i},l=r=>{if(n.has(r))return n.get(r);let[o,a]=tL(r);switch(o){case 0:{let t=r;switch(a){case"bigint":o=8,t=r.toString();break;case"function":case"symbol":if(e)throw TypeError("unable to serialize "+a);t=null;break;case"undefined":return i([-1],r)}return i([o,t],r)}case 1:{if(a){let e=r;return"DataView"===a?e=new Uint8Array(r.buffer):"ArrayBuffer"===a&&(e=new Uint8Array(r)),i([a,[...e]],r)}let e=[],t=i([o,e],r);for(let t of r)e.push(l(t));return t}case 2:{if(a)switch(a){case"BigInt":return i([a,r.toString()],r);case"Boolean":case"Number":case"String":return i([a,r.valueOf()],r)}if(t&&"toJSON"in r)return l(r.toJSON());let n=[],s=i([o,n],r);for(let t of tA(r))(e||!tP(tL(r[t])))&&n.push([l(t),l(r[t])]);return s}case 3:return i([o,r.toISOString()],r);case 4:{let{source:e,flags:t}=r;return i([o,{source:e,flags:t}],r)}case 5:{let t=[],n=i([o,t],r);for(let[n,i]of r)(e||!(tP(tL(n))||tP(tL(i))))&&t.push([l(n),l(i)]);return n}case 6:{let t=[],n=i([o,t],r);for(let n of r)(e||!tP(tL(n)))&&t.push(l(n));return n}}let{message:s}=r;return i([o,{name:a,message:s}],r)};return l},tq=(e,{json:t,lossy:n}={})=>{let r=[];return tI(!(t||n),!!t,new Map,r)(e),r},tN="function"==typeof structuredClone?(e,t)=>t&&("json"in t||"lossy"in t)?tD(tq(e,t)):structuredClone(e):(e,t)=>tD(tq(e,t));function tO(e){let t=[],n=-1,r=0,i=0;for(;++n<e.length;){let l=e.charCodeAt(n),o="";if(37===l&&(0,ex.lV)(e.charCodeAt(n+1))&&(0,ex.lV)(e.charCodeAt(n+2)))i=2;else if(l<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(l))||(o=String.fromCharCode(l));else if(l>55295&&l<57344){let t=e.charCodeAt(n+1);l<56320&&t>56319&&t<57344?(o=String.fromCharCode(l,t),i=1):o="�"}else o=String.fromCharCode(l);o&&(t.push(e.slice(r,n),encodeURIComponent(o)),r=n+i+1,o=""),i&&(n+=i,i=0)}return t.join("")+e.slice(r)}function tR(e,t){let n=[{type:"text",value:"↩"}];return t>1&&n.push({type:"element",tagName:"sup",properties:{},children:[{type:"text",value:String(t)}]}),n}function tF(e,t){return"Back to reference "+(e+1)+(t>1?"-"+t:"")}var tz=n(66755);function tM(e,t){let n=t.referenceType,r="]";if("collapsed"===n?r+="[]":"full"===n&&(r+="["+(t.label||t.identifier)+"]"),"imageReference"===t.type)return[{type:"text",value:"!["+t.alt+r}];let i=e.all(t),l=i[0];l&&"text"===l.type?l.value="["+l.value:i.unshift({type:"text",value:"["});let o=i[i.length-1];return o&&"text"===o.type?o.value+=r:i.push({type:"text",value:r}),i}function tH(e){let t=e.spread;return null==t?e.children.length>1:t}function tB(e,t,n){let r=0,i=e.length;if(t){let t=e.codePointAt(r);for(;9===t||32===t;)r++,t=e.codePointAt(r)}if(n){let t=e.codePointAt(i-1);for(;9===t||32===t;)i--,t=e.codePointAt(i-1)}return i>r?e.slice(r,i):""}let tV={blockquote:function(e,t){let n={type:"element",tagName:"blockquote",properties:{},children:e.wrap(e.all(t),!0)};return e.patch(t,n),e.applyData(t,n)},break:function(e,t){let n={type:"element",tagName:"br",properties:{},children:[]};return e.patch(t,n),[e.applyData(t,n),{type:"text",value:"\n"}]},code:function(e,t){let n=t.value?t.value+"\n":"",r={};t.lang&&(r.className=["language-"+t.lang]);let i={type:"element",tagName:"code",properties:r,children:[{type:"text",value:n}]};return t.meta&&(i.data={meta:t.meta}),e.patch(t,i),i={type:"element",tagName:"pre",properties:{},children:[i=e.applyData(t,i)]},e.patch(t,i),i},delete:function(e,t){let n={type:"element",tagName:"del",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},emphasis:function(e,t){let n={type:"element",tagName:"em",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},footnoteReference:function(e,t){let n,r="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",i=String(t.identifier).toUpperCase(),l=tO(i.toLowerCase()),o=e.footnoteOrder.indexOf(i),a=e.footnoteCounts.get(i);void 0===a?(a=0,e.footnoteOrder.push(i),n=e.footnoteOrder.length):n=o+1,a+=1,e.footnoteCounts.set(i,a);let s={type:"element",tagName:"a",properties:{href:"#"+r+"fn-"+l,id:r+"fnref-"+l+(a>1?"-"+a:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(n)}]};e.patch(t,s);let u={type:"element",tagName:"sup",properties:{},children:[s]};return e.patch(t,u),e.applyData(t,u)},heading:function(e,t){let n={type:"element",tagName:"h"+t.depth,properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},html:function(e,t){if(e.options.allowDangerousHtml){let n={type:"raw",value:t.value};return e.patch(t,n),e.applyData(t,n)}},imageReference:function(e,t){let n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return tM(e,t);let i={src:tO(r.url||""),alt:t.alt};null!==r.title&&void 0!==r.title&&(i.title=r.title);let l={type:"element",tagName:"img",properties:i,children:[]};return e.patch(t,l),e.applyData(t,l)},image:function(e,t){let n={src:tO(t.url)};null!==t.alt&&void 0!==t.alt&&(n.alt=t.alt),null!==t.title&&void 0!==t.title&&(n.title=t.title);let r={type:"element",tagName:"img",properties:n,children:[]};return e.patch(t,r),e.applyData(t,r)},inlineCode:function(e,t){let n={type:"text",value:t.value.replace(/\r?\n|\r/g," ")};e.patch(t,n);let r={type:"element",tagName:"code",properties:{},children:[n]};return e.patch(t,r),e.applyData(t,r)},linkReference:function(e,t){let n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return tM(e,t);let i={href:tO(r.url||"")};null!==r.title&&void 0!==r.title&&(i.title=r.title);let l={type:"element",tagName:"a",properties:i,children:e.all(t)};return e.patch(t,l),e.applyData(t,l)},link:function(e,t){let n={href:tO(t.url)};null!==t.title&&void 0!==t.title&&(n.title=t.title);let r={type:"element",tagName:"a",properties:n,children:e.all(t)};return e.patch(t,r),e.applyData(t,r)},listItem:function(e,t,n){let r=e.all(t),i=n?function(e){let t=!1;if("list"===e.type){t=e.spread||!1;let n=e.children,r=-1;for(;!t&&++r<n.length;)t=tH(n[r])}return t}(n):tH(t),l={},o=[];if("boolean"==typeof t.checked){let e,n=r[0];n&&"element"===n.type&&"p"===n.tagName?e=n:(e={type:"element",tagName:"p",properties:{},children:[]},r.unshift(e)),e.children.length>0&&e.children.unshift({type:"text",value:" "}),e.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:t.checked,disabled:!0},children:[]}),l.className=["task-list-item"]}let a=-1;for(;++a<r.length;){let e=r[a];(i||0!==a||"element"!==e.type||"p"!==e.tagName)&&o.push({type:"text",value:"\n"}),"element"!==e.type||"p"!==e.tagName||i?o.push(e):o.push(...e.children)}let s=r[r.length-1];s&&(i||"element"!==s.type||"p"!==s.tagName)&&o.push({type:"text",value:"\n"});let u={type:"element",tagName:"li",properties:l,children:o};return e.patch(t,u),e.applyData(t,u)},list:function(e,t){let n={},r=e.all(t),i=-1;for("number"==typeof t.start&&1!==t.start&&(n.start=t.start);++i<r.length;){let e=r[i];if("element"===e.type&&"li"===e.tagName&&e.properties&&Array.isArray(e.properties.className)&&e.properties.className.includes("task-list-item")){n.className=["contains-task-list"];break}}let l={type:"element",tagName:t.ordered?"ol":"ul",properties:n,children:e.wrap(r,!0)};return e.patch(t,l),e.applyData(t,l)},paragraph:function(e,t){let n={type:"element",tagName:"p",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},root:function(e,t){let n={type:"root",children:e.wrap(e.all(t))};return e.patch(t,n),e.applyData(t,n)},strong:function(e,t){let n={type:"element",tagName:"strong",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},table:function(e,t){let n=e.all(t),r=n.shift(),i=[];if(r){let n={type:"element",tagName:"thead",properties:{},children:e.wrap([r],!0)};e.patch(t.children[0],n),i.push(n)}if(n.length>0){let r={type:"element",tagName:"tbody",properties:{},children:e.wrap(n,!0)},l=G(t.children[1]),o=W(t.children[t.children.length-1]);l&&o&&(r.position={start:l,end:o}),i.push(r)}let l={type:"element",tagName:"table",properties:{},children:e.wrap(i,!0)};return e.patch(t,l),e.applyData(t,l)},tableCell:function(e,t){let n={type:"element",tagName:"td",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},tableRow:function(e,t,n){let r=n?n.children:void 0,i=0===(r?r.indexOf(t):1)?"th":"td",l=n&&"table"===n.type?n.align:void 0,o=l?l.length:t.children.length,a=-1,s=[];for(;++a<o;){let n=t.children[a],r={},o=l?l[a]:void 0;o&&(r.align=o);let u={type:"element",tagName:i,properties:r,children:[]};n&&(u.children=e.all(n),e.patch(n,u),u=e.applyData(n,u)),s.push(u)}let u={type:"element",tagName:"tr",properties:{},children:e.wrap(s,!0)};return e.patch(t,u),e.applyData(t,u)},text:function(e,t){let n={type:"text",value:function(e){let t=String(e),n=/\r?\n|\r/g,r=n.exec(t),i=0,l=[];for(;r;)l.push(tB(t.slice(i,r.index),i>0,!0),r[0]),i=r.index+r[0].length,r=n.exec(t);return l.push(tB(t.slice(i),i>0,!1)),l.join("")}(String(t.value))};return e.patch(t,n),e.applyData(t,n)},thematicBreak:function(e,t){let n={type:"element",tagName:"hr",properties:{},children:[]};return e.patch(t,n),e.applyData(t,n)},toml:tj,yaml:tj,definition:tj,footnoteDefinition:tj};function tj(){}let t_={}.hasOwnProperty,tU={};function tW(e,t){e.position&&(t.position=function(e){let t=G(e),n=W(e);if(t&&n)return{start:t,end:n}}(e))}function tG(e,t){let n=t;if(e&&e.data){let t=e.data.hName,r=e.data.hChildren,i=e.data.hProperties;"string"==typeof t&&("element"===n.type?n.tagName=t:n={type:"element",tagName:t,properties:{},children:"children"in n?n.children:[n]}),"element"===n.type&&i&&Object.assign(n.properties,tN(i)),"children"in n&&n.children&&null!=r&&(n.children=r)}return n}function tY(e,t){let n=[],r=-1;for(t&&n.push({type:"text",value:"\n"});++r<e.length;)r&&n.push({type:"text",value:"\n"}),n.push(e[r]);return t&&e.length>0&&n.push({type:"text",value:"\n"}),n}function t$(e){let t=0,n=e.charCodeAt(t);for(;9===n||32===n;)t++,n=e.charCodeAt(t);return e.slice(t)}function tQ(e,t){let n=function(e,t){let n=t||tU,r=new Map,i=new Map,l={all:function(e){let t=[];if("children"in e){let n=e.children,r=-1;for(;++r<n.length;){let i=l.one(n[r],e);if(i){if(r&&"break"===n[r-1].type&&(Array.isArray(i)||"text"!==i.type||(i.value=t$(i.value)),!Array.isArray(i)&&"element"===i.type)){let e=i.children[0];e&&"text"===e.type&&(e.value=t$(e.value))}Array.isArray(i)?t.push(...i):t.push(i)}}}return t},applyData:tG,definitionById:r,footnoteById:i,footnoteCounts:new Map,footnoteOrder:[],handlers:{...tV,...n.handlers},one:function(e,t){let n=e.type,r=l.handlers[n];if(t_.call(l.handlers,n)&&r)return r(l,e,t);if(l.options.passThrough&&l.options.passThrough.includes(n)){if("children"in e){let{children:t,...n}=e,r=tN(n);return r.children=l.all(e),r}return tN(e)}return(l.options.unknownHandler||function(e,t){let n=t.data||{},r="value"in t&&!(t_.call(n,"hProperties")||t_.call(n,"hChildren"))?{type:"text",value:t.value}:{type:"element",tagName:"div",properties:{},children:e.all(t)};return e.patch(t,r),e.applyData(t,r)})(l,e,t)},options:n,patch:tW,wrap:tY};return(0,tz.YR)(e,function(e){if("definition"===e.type||"footnoteDefinition"===e.type){let t="definition"===e.type?r:i,n=String(e.identifier).toUpperCase();t.has(n)||t.set(n,e)}}),l}(e,t),r=n.one(e,void 0),i=function(e){let t="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",n=e.options.footnoteBackContent||tR,r=e.options.footnoteBackLabel||tF,i=e.options.footnoteLabel||"Footnotes",l=e.options.footnoteLabelTagName||"h2",o=e.options.footnoteLabelProperties||{className:["sr-only"]},a=[],s=-1;for(;++s<e.footnoteOrder.length;){let i=e.footnoteById.get(e.footnoteOrder[s]);if(!i)continue;let l=e.all(i),o=String(i.identifier).toUpperCase(),u=tO(o.toLowerCase()),c=0,f=[],p=e.footnoteCounts.get(o);for(;void 0!==p&&++c<=p;){f.length>0&&f.push({type:"text",value:" "});let e="string"==typeof n?n:n(s,c);"string"==typeof e&&(e={type:"text",value:e}),f.push({type:"element",tagName:"a",properties:{href:"#"+t+"fnref-"+u+(c>1?"-"+c:""),dataFootnoteBackref:"",ariaLabel:"string"==typeof r?r:r(s,c),className:["data-footnote-backref"]},children:Array.isArray(e)?e:[e]})}let d=l[l.length-1];if(d&&"element"===d.type&&"p"===d.tagName){let e=d.children[d.children.length-1];e&&"text"===e.type?e.value+=" ":d.children.push({type:"text",value:" "}),d.children.push(...f)}else l.push(...f);let h={type:"element",tagName:"li",properties:{id:t+"fn-"+u},children:e.wrap(l,!0)};e.patch(i,h),a.push(h)}if(0!==a.length)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:l,properties:{...tN(o),id:"footnote-label"},children:[{type:"text",value:i}]},{type:"text",value:"\n"},{type:"element",tagName:"ol",properties:{},children:e.wrap(a,!0)},{type:"text",value:"\n"}]}}(n),o=Array.isArray(r)?{type:"root",children:r}:r||{type:"root",children:[]};return i&&((0,l.ok)("children"in o),o.children.push({type:"text",value:"\n"},i)),o}function tJ(e,t){return e&&"run"in e?async function(n,r){let i=tQ(n,{file:r,...t});await e.run(i,r)}:function(n,r){return tQ(n,{file:r,...e||t})}}function tK(e){if(e)throw e}var tZ=n(85519);function tX(e){if("object"!=typeof e||null===e)return!1;let t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}var t0=n(76760),t1=n(1708);function t2(e){return!!(null!==e&&"object"==typeof e&&"href"in e&&e.href&&"protocol"in e&&e.protocol&&void 0===e.auth)}var t4=n(73136);let t3=["history","path","basename","stem","extname","dirname"];class t5{constructor(e){let t,n;t=e?t2(e)?{path:e}:"string"==typeof e||function(e){return!!(e&&"object"==typeof e&&"byteLength"in e&&"byteOffset"in e)}(e)?{value:e}:e:{},this.cwd="cwd"in t?"":t1.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let r=-1;for(;++r<t3.length;){let e=t3[r];e in t&&void 0!==t[e]&&null!==t[e]&&(this[e]="history"===e?[...t[e]]:t[e])}for(n in t)t3.includes(n)||(this[n]=t[n])}get basename(){return"string"==typeof this.path?t0.basename(this.path):void 0}set basename(e){t6(e,"basename"),t9(e,"basename"),this.path=t0.join(this.dirname||"",e)}get dirname(){return"string"==typeof this.path?t0.dirname(this.path):void 0}set dirname(e){t7(this.basename,"dirname"),this.path=t0.join(e||"",this.basename)}get extname(){return"string"==typeof this.path?t0.extname(this.path):void 0}set extname(e){if(t9(e,"extname"),t7(this.dirname,"extname"),e){if(46!==e.codePointAt(0))throw Error("`extname` must start with `.`");if(e.includes(".",1))throw Error("`extname` cannot contain multiple dots")}this.path=t0.join(this.dirname,this.stem+(e||""))}get path(){return this.history[this.history.length-1]}set path(e){t2(e)&&(e=(0,t4.fileURLToPath)(e)),t6(e,"path"),this.path!==e&&this.history.push(e)}get stem(){return"string"==typeof this.path?t0.basename(this.path,this.extname):void 0}set stem(e){t6(e,"stem"),t9(e,"stem"),this.path=t0.join(this.dirname||"",e+(this.extname||""))}fail(e,t,n){let r=this.message(e,t,n);throw r.fatal=!0,r}info(e,t,n){let r=this.message(e,t,n);return r.fatal=void 0,r}message(e,t,n){let r=new Z(e,t,n);return this.path&&(r.name=this.path+":"+r.name,r.file=this.path),r.fatal=!1,this.messages.push(r),r}toString(e){return void 0===this.value?"":"string"==typeof this.value?this.value:new TextDecoder(e||void 0).decode(this.value)}}function t9(e,t){if(e&&e.includes(t0.sep))throw Error("`"+t+"` cannot be a path: did not expect `"+t0.sep+"`")}function t6(e,t){if(!e)throw Error("`"+t+"` cannot be empty")}function t7(e,t){if(!e)throw Error("Setting `"+t+"` requires `path` to be set too")}let t8=function(e){let t=this.constructor.prototype,n=t[e],r=function(){return n.apply(r,arguments)};return Object.setPrototypeOf(r,t),r},ne={}.hasOwnProperty;class nt extends t8{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=function(){let e=[],t={run:function(...t){let n=-1,r=t.pop();if("function"!=typeof r)throw TypeError("Expected function as last argument, not "+r);!function i(l,...o){let a=e[++n],s=-1;if(l)return void r(l);for(;++s<t.length;)(null===o[s]||void 0===o[s])&&(o[s]=t[s]);t=o,a?(function(e,t){let n;return function(...t){let l,o=e.length>t.length;o&&t.push(r);try{l=e.apply(this,t)}catch(e){if(o&&n)throw e;return r(e)}o||(l&&l.then&&"function"==typeof l.then?l.then(i,r):l instanceof Error?r(l):i(l))};function r(e,...i){n||(n=!0,t(e,...i))}function i(e){r(null,e)}})(a,i)(...o):r(null,...o)}(null,...t)},use:function(n){if("function"!=typeof n)throw TypeError("Expected `middelware` to be a function, not "+n);return e.push(n),t}};return t}()}copy(){let e=new nt,t=-1;for(;++t<this.attachers.length;){let n=this.attachers[t];e.use(...n)}return e.data(tZ(!0,{},this.namespace)),e}data(e,t){return"string"==typeof e?2==arguments.length?(nl("data",this.frozen),this.namespace[e]=t,this):ne.call(this.namespace,e)&&this.namespace[e]||void 0:e?(nl("data",this.frozen),this.namespace=e,this):this.namespace}freeze(){if(this.frozen)return this;for(;++this.freezeIndex<this.attachers.length;){let[e,...t]=this.attachers[this.freezeIndex];if(!1===t[0])continue;!0===t[0]&&(t[0]=void 0);let n=e.call(this,...t);"function"==typeof n&&this.transformers.use(n)}return this.frozen=!0,this.freezeIndex=Number.POSITIVE_INFINITY,this}parse(e){this.freeze();let t=ns(e),n=this.parser||this.Parser;return nr("parse",n),n(String(t),t)}process(e,t){let n=this;return this.freeze(),nr("process",this.parser||this.Parser),ni("process",this.compiler||this.Compiler),t?r(void 0,t):new Promise(r);function r(r,i){let o=ns(e),a=n.parse(o);function s(e,n){e||!n?i(e):r?r(n):((0,l.ok)(t,"`done` is defined if `resolve` is not"),t(void 0,n))}n.run(a,o,function(e,t,r){var i,l;if(e||!t||!r)return s(e);let o=n.stringify(t,r);"string"==typeof(i=o)||(l=i)&&"object"==typeof l&&"byteLength"in l&&"byteOffset"in l?r.value=o:r.result=o,s(e,r)})}}processSync(e){let t,n=!1;return this.freeze(),nr("processSync",this.parser||this.Parser),ni("processSync",this.compiler||this.Compiler),this.process(e,function(e,r){n=!0,tK(e),t=r}),na("processSync","process",n),(0,l.ok)(t,"we either bailed on an error or have a tree"),t}run(e,t,n){no(e),this.freeze();let r=this.transformers;return n||"function"!=typeof t||(n=t,t=void 0),n?i(void 0,n):new Promise(i);function i(i,o){(0,l.ok)("function"!=typeof t,"`file` can’t be a `done` anymore, we checked");let a=ns(t);r.run(e,a,function(t,r,a){let s=r||e;t?o(t):i?i(s):((0,l.ok)(n,"`done` is defined if `resolve` is not"),n(void 0,s,a))})}}runSync(e,t){let n,r=!1;return this.run(e,t,function(e,t){tK(e),n=t,r=!0}),na("runSync","run",r),(0,l.ok)(n,"we either bailed on an error or have a tree"),n}stringify(e,t){this.freeze();let n=ns(t),r=this.compiler||this.Compiler;return ni("stringify",r),no(e),r(e,n)}use(e,...t){let n=this.attachers,r=this.namespace;if(nl("use",this.frozen),null==e);else if("function"==typeof e)o(e,t);else if("object"==typeof e)Array.isArray(e)?l(e):i(e);else throw TypeError("Expected usable value, not `"+e+"`");return this;function i(e){if(!("plugins"in e)&&!("settings"in e))throw Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");l(e.plugins),e.settings&&(r.settings=tZ(!0,r.settings,e.settings))}function l(e){let t=-1;if(null==e);else if(Array.isArray(e))for(;++t<e.length;){var n=e[t];if("function"==typeof n)o(n,[]);else if("object"==typeof n)if(Array.isArray(n)){let[e,...t]=n;o(e,t)}else i(n);else throw TypeError("Expected usable value, not `"+n+"`")}else throw TypeError("Expected a list of plugins, not `"+e+"`")}function o(e,t){let r=-1,i=-1;for(;++r<n.length;)if(n[r][0]===e){i=r;break}if(-1===i)n.push([e,...t]);else if(t.length>0){let[r,...l]=t,o=n[i][1];tX(o)&&tX(r)&&(r=tZ(!0,o,r)),n[i]=[e,r,...l]}}}}let nn=new nt().freeze();function nr(e,t){if("function"!=typeof t)throw TypeError("Cannot `"+e+"` without `parser`")}function ni(e,t){if("function"!=typeof t)throw TypeError("Cannot `"+e+"` without `compiler`")}function nl(e,t){if(t)throw Error("Cannot call `"+e+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function no(e){if(!tX(e)||"string"!=typeof e.type)throw TypeError("Expected node, got `"+e+"`")}function na(e,t,n){if(!n)throw Error("`"+e+"` finished async. Use `"+t+"` instead")}function ns(e){var t;return(t=e)&&"object"==typeof t&&"message"in t&&"messages"in t?e:new t5(e)}let nu=[],nc={allowDangerousHtml:!0},nf=/^(https?|ircs?|mailto|xmpp)$/i,np=[{from:"astPlugins",id:"remove-buggy-html-in-markdown-parser"},{from:"allowDangerousHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"allowNode",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowElement"},{from:"allowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowedElements"},{from:"className",id:"remove-classname"},{from:"disallowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"disallowedElements"},{from:"escapeHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"includeElementIndex",id:"#remove-includeelementindex"},{from:"includeNodeIndex",id:"change-includenodeindex-to-includeelementindex"},{from:"linkTarget",id:"remove-linktarget"},{from:"plugins",id:"change-plugins-to-remarkplugins",to:"remarkPlugins"},{from:"rawSourcePos",id:"#remove-rawsourcepos"},{from:"renderers",id:"change-renderers-to-components",to:"components"},{from:"source",id:"change-source-to-children",to:"children"},{from:"sourcePos",id:"#remove-sourcepos"},{from:"transformImageUri",id:"#add-urltransform",to:"urlTransform"},{from:"transformLinkUri",id:"#add-urltransform",to:"urlTransform"}];function nd(e){let t=function(e){let t=e.rehypePlugins||nu,n=e.remarkPlugins||nu,r=e.remarkRehypeOptions?{...e.remarkRehypeOptions,...nc}:nc;return nn().use(tS).use(n).use(tJ,r).use(t)}(e),n=function(e){let t=e.children||"",n=new t5;return"string"==typeof t?n.value=t:(0,l.HB)("Unexpected value `"+t+"` for `children` prop, expected `string`"),n}(e);return function(e,t){let n=t.allowedElements,r=t.allowElement,i=t.components,o=t.disallowedElements,a=t.skipHtml,s=t.unwrapDisallowed,u=t.urlTransform||nh;for(let e of np)Object.hasOwn(t,e.from)&&(0,l.HB)("Unexpected `"+e.from+"` prop, "+(e.to?"use `"+e.to+"` instead":"remove it")+" (see <https://github.com/remarkjs/react-markdown/blob/main/changelog.md#"+e.id+"> for more info)");return n&&o&&(0,l.HB)("Unexpected combined `allowedElements` and `disallowedElements`, expected one or the other"),(0,tz.YR)(e,function(e,t,i){if("raw"===e.type&&i&&"number"==typeof t)return a?i.children.splice(t,1):i.children[t]={type:"text",value:e.value},t;if("element"===e.type){let t;for(t in ep)if(Object.hasOwn(ep,t)&&Object.hasOwn(e.properties,t)){let n=e.properties[t],r=ep[t];(null===r||r.includes(e.tagName))&&(e.properties[t]=u(String(n||""),t,e))}}if("element"===e.type){let l=n?!n.includes(e.tagName):!!o&&o.includes(e.tagName);if(!l&&r&&"number"==typeof t&&(l=!r(e,t,i)),l&&i&&"number"==typeof t)return s&&e.children?i.children.splice(t,1,...e.children):i.children.splice(t,1),t}}),function(e,t){var n,r,i,l,o;let a;if(!t||void 0===t.Fragment)throw TypeError("Expected `Fragment` in options");let s=t.filePath||void 0;if(t.development){if("function"!=typeof t.jsxDEV)throw TypeError("Expected `jsxDEV` in options when `development: true`");n=s,r=t.jsxDEV,a=function(e,t,i,l){let o=Array.isArray(i.children),a=G(e);return r(t,i,l,o,{columnNumber:a?a.column-1:void 0,fileName:n,lineNumber:a?a.line:void 0},void 0)}}else{if("function"!=typeof t.jsx)throw TypeError("Expected `jsx` in production options");if("function"!=typeof t.jsxs)throw TypeError("Expected `jsxs` in production options");i=0,l=t.jsx,o=t.jsxs,a=function(e,t,n,r){let i=Array.isArray(n.children)?o:l;return r?i(t,n,r):i(t,n)}}let u={Fragment:t.Fragment,ancestors:[],components:t.components||{},create:a,elementAttributeNameCase:t.elementAttributeNameCase||"react",evaluater:t.createEvaluater?t.createEvaluater():void 0,filePath:s,ignoreInvalidStyle:t.ignoreInvalidStyle||!1,passKeys:!1!==t.passKeys,passNode:t.passNode||!1,schema:"svg"===t.space?z:F,stylePropertyNameCase:t.stylePropertyNameCase||"dom",tableCellAlignToStyle:!1!==t.tableCellAlignToStyle},c=el(u,e,void 0);return c&&"string"!=typeof c?c:u.create(e,u.Fragment,{children:c||void 0},void 0)}(e,{Fragment:ed.Fragment,components:i,ignoreInvalidStyle:!0,jsx:ed.jsx,jsxs:ed.jsxs,passKeys:!0,passNode:!0})}(t.runSync(t.parse(n),n),e)}function nh(e){let t=e.indexOf(":"),n=e.indexOf("?"),r=e.indexOf("#"),i=e.indexOf("/");return -1===t||-1!==i&&t>i||-1!==n&&t>n||-1!==r&&t>r||nf.test(e.slice(0,t))?e:""}},43768:(e,t,n)=>{"use strict";n.d(t,{dc:()=>l,VG:()=>o});var r=n(82268);let i=[],l=!1;function o(e,t,n,o){let a;"function"==typeof t&&"function"!=typeof n?(o=n,n=t):a=t;let s=(0,r.C)(a),u=o?-1:1;(function e(r,a,c){let f=r&&"object"==typeof r?r:{};if("string"==typeof f.type){let e="string"==typeof f.tagName?f.tagName:"string"==typeof f.name?f.name:void 0;Object.defineProperty(p,"name",{value:"node (\x1b[33m"+r.type+(e?"<"+e+">":"")+"\x1b[39m)"})}return p;function p(){var f;let p,d,h,m=i;if((!t||s(r,a,c[c.length-1]||void 0))&&(m=Array.isArray(f=n(r,c))?f:"number"==typeof f?[!0,f]:null==f?i:[f])[0]===l)return m;if("children"in r&&r.children&&r.children&&"skip"!==m[0])for(d=(o?r.children.length:-1)+u,h=c.concat(r);d>-1&&d<r.children.length;){if((p=e(r.children[d],d,h)())[0]===l)return p;d="number"==typeof p[1]?p[1]:d+u}return m}})(e,void 0,[])()}},46418:(e,t,n)=>{"use strict";function r(){}function i(){}n.d(t,{HB:()=>i,ok:()=>r})},51897:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(55732).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},57136:(e,t,n)=>{"use strict";n.d(t,{S:()=>i});var r=n(64071);function i(e){return null===e||(0,r.Ee)(e)||(0,r.Ny)(e)?1:(0,r.es)(e)?2:void 0}},63015:(e,t,n)=>{"use strict";n.d(t,{d:()=>i});let r={};function i(e,t){let n=t||r;return l(e,"boolean"!=typeof n.includeImageAlt||n.includeImageAlt,"boolean"!=typeof n.includeHtml||n.includeHtml)}function l(e,t,n){var r;if((r=e)&&"object"==typeof r){if("value"in e)return"html"!==e.type||n?e.value:"";if(t&&"alt"in e&&e.alt)return e.alt;if("children"in e)return o(e.children,t,n)}return Array.isArray(e)?o(e,t,n):""}function o(e,t,n){let r=[],i=-1;for(;++i<e.length;)r[i]=l(e[i],t,n);return r.join("")}},64071:(e,t,n)=>{"use strict";n.d(t,{BM:()=>a,CW:()=>r,Ee:()=>f,HP:()=>c,JQ:()=>o,Ny:()=>h,On:()=>p,cx:()=>l,es:()=>d,lV:()=>i,ok:()=>s,ol:()=>u});let r=m(/[A-Za-z]/),i=m(/[\dA-Za-z]/),l=m(/[#-'*+\--9=?A-Z^-~]/);function o(e){return null!==e&&(e<32||127===e)}let a=m(/\d/),s=m(/[\dA-Fa-f]/),u=m(/[!-/:-@[-`{-~]/);function c(e){return null!==e&&e<-2}function f(e){return null!==e&&(e<0||32===e)}function p(e){return -2===e||-1===e||32===e}let d=m(/\p{P}|\p{S}/u),h=m(/\s/);function m(e){return function(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}},66536:(e,t,n)=>{"use strict";n.d(t,{C1:()=>x,bL:()=>k});var r=n(93491),i=n(10158),l=n(90604),o=n(91754),a="Progress",[s,u]=(0,i.A)(a),[c,f]=s(a),p=r.forwardRef((e,t)=>{var n,r;let{__scopeProgress:i,value:a=null,max:s,getValueLabel:u=m,...f}=e;(s||0===s)&&!b(s)&&console.error((n=`${s}`,`Invalid prop \`max\` of value \`${n}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let p=b(s)?s:100;null===a||v(a,p)||console.error((r=`${a}`,`Invalid prop \`value\` of value \`${r}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let d=v(a,p)?a:null,h=y(d)?u(d,p):void 0;return(0,o.jsx)(c,{scope:i,value:d,max:p,children:(0,o.jsx)(l.sG.div,{"aria-valuemax":p,"aria-valuemin":0,"aria-valuenow":y(d)?d:void 0,"aria-valuetext":h,role:"progressbar","data-state":g(d,p),"data-value":d??void 0,"data-max":p,...f,ref:t})})});p.displayName=a;var d="ProgressIndicator",h=r.forwardRef((e,t)=>{let{__scopeProgress:n,...r}=e,i=f(d,n);return(0,o.jsx)(l.sG.div,{"data-state":g(i.value,i.max),"data-value":i.value??void 0,"data-max":i.max,...r,ref:t})});function m(e,t){return`${Math.round(e/t*100)}%`}function g(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function y(e){return"number"==typeof e}function b(e){return y(e)&&!isNaN(e)&&e>0}function v(e,t){return y(e)&&!isNaN(e)&&e<=t&&e>=0}h.displayName=d;var k=p,x=h},66755:(e,t,n)=>{"use strict";n.d(t,{YR:()=>i});var r=n(43768);function i(e,t,n,i){let l,o,a;"function"==typeof t&&"function"!=typeof n?(o=void 0,a=t,l=n):(o=t,a=n,l=i),(0,r.VG)(e,o,function(e,t){let n=t[t.length-1],r=n?n.children.indexOf(e):void 0;return a(e,r,n)},l)}},77126:(e,t,n)=>{"use strict";n.d(t,{y:()=>l});var r=n(31535);let i={}.hasOwnProperty;function l(e){let t={},n=-1;for(;++n<e.length;)!function(e,t){let n;for(n in t){let l,o=(i.call(e,n)?e[n]:void 0)||(e[n]={}),a=t[n];if(a)for(l in a){i.call(o,l)||(o[l]=[]);let e=a[l];!function(e,t){let n=-1,i=[];for(;++n<t.length;)("after"===t[n].add?e:i).push(t[n]);(0,r.m)(e,0,0,i)}(o[l],Array.isArray(e)?e:e?[e]:[])}}}(t,e[n]);return t}},78918:(e,t,n)=>{"use strict";function r(e,t){let n=String(e);if("string"!=typeof t)throw TypeError("Expected character");let r=0,i=n.indexOf(t);for(;-1!==i;)r++,i=n.indexOf(t,i+t.length);return r}n.d(t,{A:()=>eH});var i=n(46418),l=n(64071),o=n(43768),a=n(82268);let s="phrasing",u=["autolink","link","image","label"];function c(e){this.enter({type:"link",title:null,url:"",children:[]},e)}function f(e){this.config.enter.autolinkProtocol.call(this,e)}function p(e){this.config.exit.autolinkProtocol.call(this,e)}function d(e){this.config.exit.data.call(this,e);let t=this.stack[this.stack.length-1];(0,i.ok)("link"===t.type),t.url="http://"+this.sliceSerialize(e)}function h(e){this.config.exit.autolinkEmail.call(this,e)}function m(e){this.exit(e)}function g(e){!function(e,t,n){let r=(0,a.C)((n||{}).ignore||[]),i=function(e){let t=[];if(!Array.isArray(e))throw TypeError("Expected find and replace tuple or list of tuples");let n=!e[0]||Array.isArray(e[0])?e:[e],r=-1;for(;++r<n.length;){var i;let e=n[r];t.push(["string"==typeof(i=e[0])?RegExp(function(e){if("string"!=typeof e)throw TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}(i),"g"):i,function(e){return"function"==typeof e?e:function(){return e}}(e[1])])}return t}(t),l=-1;for(;++l<i.length;)(0,o.VG)(e,"text",s);function s(e,t){let n,o=-1;for(;++o<t.length;){let e=t[o],i=n?n.children:void 0;if(r(e,i?i.indexOf(e):void 0,n))return;n=e}if(n)return function(e,t){let n=t[t.length-1],r=i[l][0],o=i[l][1],a=0,s=n.children.indexOf(e),u=!1,c=[];r.lastIndex=0;let f=r.exec(e.value);for(;f;){let n=f.index,i={index:f.index,input:f.input,stack:[...t,e]},l=o(...f,i);if("string"==typeof l&&(l=l.length>0?{type:"text",value:l}:void 0),!1===l?r.lastIndex=n+1:(a!==n&&c.push({type:"text",value:e.value.slice(a,n)}),Array.isArray(l)?c.push(...l):l&&c.push(l),a=n+f[0].length,u=!0),!r.global)break;f=r.exec(e.value)}return u?(a<e.value.length&&c.push({type:"text",value:e.value.slice(a)}),n.children.splice(s,1,...c)):c=[e],s+c.length}(e,t)}}(e,[[/(https?:\/\/|www(?=\.))([-.\w]+)([^ \t\r\n]*)/gi,y],[/(?<=^|\s|\p{P}|\p{S})([-.\w+]+)@([-\w]+(?:\.[-\w]+)+)/gu,b]],{ignore:["link","linkReference"]})}function y(e,t,n,i,l){let o="";if(!v(l)||(/^w/i.test(t)&&(n=t+n,t="",o="http://"),!function(e){let t=e.split(".");return!(t.length<2||t[t.length-1]&&(/_/.test(t[t.length-1])||!/[a-zA-Z\d]/.test(t[t.length-1]))||t[t.length-2]&&(/_/.test(t[t.length-2])||!/[a-zA-Z\d]/.test(t[t.length-2])))}(n)))return!1;let a=function(e){let t=/[!"&'),.:;<>?\]}]+$/.exec(e);if(!t)return[e,void 0];e=e.slice(0,t.index);let n=t[0],i=n.indexOf(")"),l=r(e,"("),o=r(e,")");for(;-1!==i&&l>o;)e+=n.slice(0,i+1),i=(n=n.slice(i+1)).indexOf(")"),o++;return[e,n]}(n+i);if(!a[0])return!1;let s={type:"link",title:null,url:o+t+a[0],children:[{type:"text",value:t+a[0]}]};return a[1]?[s,{type:"text",value:a[1]}]:s}function b(e,t,n,r){return!(!v(r,!0)||/[-\d_]$/.test(n))&&{type:"link",title:null,url:"mailto:"+t+"@"+n,children:[{type:"text",value:t+"@"+n}]}}function v(e,t){let n=e.input.charCodeAt(e.index-1);return(0===e.index||(0,l.Ny)(n)||(0,l.es)(n))&&(!t||47!==n)}var k=n(89961);function x(){this.buffer()}function w(e){this.enter({type:"footnoteReference",identifier:"",label:""},e)}function S(){this.buffer()}function C(e){this.enter({type:"footnoteDefinition",identifier:"",label:"",children:[]},e)}function E(e){let t=this.resume(),n=this.stack[this.stack.length-1];(0,i.ok)("footnoteReference"===n.type),n.identifier=(0,k.B)(this.sliceSerialize(e)).toLowerCase(),n.label=t}function D(e){this.exit(e)}function T(e){let t=this.resume(),n=this.stack[this.stack.length-1];(0,i.ok)("footnoteDefinition"===n.type),n.identifier=(0,k.B)(this.sliceSerialize(e)).toLowerCase(),n.label=t}function A(e){this.exit(e)}function L(e,t,n,r){let i=n.createTracker(r),l=i.move("[^"),o=n.enter("footnoteReference"),a=n.enter("reference");return l+=i.move(n.safe(n.associationId(e),{after:"]",before:l})),a(),o(),l+=i.move("]")}function P(e,t,n){return 0===t?e:I(e,t,n)}function I(e,t,n){return(n?"":"    ")+e}L.peek=function(){return"["};let q=["autolink","destinationLiteral","destinationRaw","reference","titleQuote","titleApostrophe"];function N(e){this.enter({type:"delete",children:[]},e)}function O(e){this.exit(e)}function R(e,t,n,r){let i=n.createTracker(r),l=n.enter("strikethrough"),o=i.move("~~");return o+=n.containerPhrasing(e,{...i.current(),before:o,after:"~"}),o+=i.move("~~"),l(),o}function F(e){return e.length}function z(e){let t="string"==typeof e?e.codePointAt(0):0;return 67===t||99===t?99:76===t||108===t?108:114*(82===t||114===t)}R.peek=function(){return"~"};function M(e,t,n){if("string"==typeof t&&(t=[t]),!t||0===t.length)return n;let r=-1;for(;++r<t.length;)if(e.includes(t[r]))return!0;return!1}function H(e){let t=e.options.quote||'"';if('"'!==t&&"'"!==t)throw Error("Cannot serialize title with `"+t+"` for `options.quote`, expected `\"`, or `'`");return t}function B(e){return"&#x"+e.toString(16).toUpperCase()+";"}var V=n(57136);function j(e,t,n){let r=(0,V.S)(e),i=(0,V.S)(t);return void 0===r?void 0===i?"_"===n?{inside:!0,outside:!0}:{inside:!1,outside:!1}:1===i?{inside:!0,outside:!0}:{inside:!1,outside:!0}:1===r?void 0===i?{inside:!1,outside:!1}:1===i?{inside:!0,outside:!0}:{inside:!1,outside:!1}:void 0===i?{inside:!1,outside:!1}:1===i?{inside:!0,outside:!1}:{inside:!1,outside:!1}}n(66755);var _=n(63015);function U(e,t,n){let r=e.value||"",i="`",l=-1;for(;RegExp("(^|[^`])"+i+"([^`]|$)").test(r);)i+="`";for(/[^ \r\n]/.test(r)&&(/^[ \r\n]/.test(r)&&/[ \r\n]$/.test(r)||/^`|`$/.test(r))&&(r=" "+r+" ");++l<n.unsafe.length;){let e,t=n.unsafe[l],i=n.compilePattern(t);if(t.atBreak)for(;e=i.exec(r);){let t=e.index;10===r.charCodeAt(t)&&13===r.charCodeAt(t-1)&&t--,r=r.slice(0,t)+" "+r.slice(e.index+1)}}return i+r+i}function W(e,t){let n=(0,_.d)(e);return!!(!t.options.resourceLink&&e.url&&!e.title&&e.children&&1===e.children.length&&"text"===e.children[0].type&&(n===e.url||"mailto:"+n===e.url)&&/^[a-z][a-z+.-]+:/i.test(e.url)&&!/[\0- <>\u007F]/.test(e.url))}U.peek=function(){return"`"};(0,a.C)(["break","delete","emphasis","footnote","footnoteReference","image","imageReference","inlineCode","inlineMath","link","linkReference","mdxJsxTextElement","mdxTextExpression","strong","text","textDirective"]);let G={inlineCode:U,listItem:function(e,t,n,r){let i=function(e){let t=e.options.listItemIndent||"one";if("tab"!==t&&"one"!==t&&"mixed"!==t)throw Error("Cannot serialize items with `"+t+"` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`");return t}(n),l=n.bulletCurrent||function(e){let t=e.options.bullet||"*";if("*"!==t&&"+"!==t&&"-"!==t)throw Error("Cannot serialize items with `"+t+"` for `options.bullet`, expected `*`, `+`, or `-`");return t}(n);t&&"list"===t.type&&t.ordered&&(l=("number"==typeof t.start&&t.start>-1?t.start:1)+(!1===n.options.incrementListMarker?0:t.children.indexOf(e))+l);let o=l.length+1;("tab"===i||"mixed"===i&&(t&&"list"===t.type&&t.spread||e.spread))&&(o=4*Math.ceil(o/4));let a=n.createTracker(r);a.move(l+" ".repeat(o-l.length)),a.shift(o);let s=n.enter("listItem"),u=n.indentLines(n.containerFlow(e,a.current()),function(e,t,n){return t?(n?"":" ".repeat(o))+e:(n?l:l+" ".repeat(o-l.length))+e});return s(),u}};function Y(e){let t=e._align;(0,i.ok)(t,"expected `_align` on table"),this.enter({type:"table",align:t.map(function(e){return"none"===e?null:e}),children:[]},e),this.data.inTable=!0}function $(e){this.exit(e),this.data.inTable=void 0}function Q(e){this.enter({type:"tableRow",children:[]},e)}function J(e){this.exit(e)}function K(e){this.enter({type:"tableCell",children:[]},e)}function Z(e){let t=this.resume();this.data.inTable&&(t=t.replace(/\\([\\|])/g,X));let n=this.stack[this.stack.length-1];(0,i.ok)("inlineCode"===n.type),n.value=t,this.exit(e)}function X(e,t){return"|"===t?t:e}function ee(e){let t=this.stack[this.stack.length-2];(0,i.ok)("listItem"===t.type),t.checked="taskListCheckValueChecked"===e.type}function et(e){let t=this.stack[this.stack.length-2];if(t&&"listItem"===t.type&&"boolean"==typeof t.checked){let e=this.stack[this.stack.length-1];(0,i.ok)("paragraph"===e.type);let n=e.children[0];if(n&&"text"===n.type){let r,i=t.children,l=-1;for(;++l<i.length;){let e=i[l];if("paragraph"===e.type){r=e;break}}r===e&&(n.value=n.value.slice(1),0===n.value.length?e.children.shift():e.position&&n.position&&"number"==typeof n.position.start.offset&&(n.position.start.column++,n.position.start.offset++,e.position.start=Object.assign({},n.position.start)))}}this.exit(e)}function en(e,t,n,r){let i=e.children[0],l="boolean"==typeof e.checked&&i&&"paragraph"===i.type,o="["+(e.checked?"x":" ")+"] ",a=n.createTracker(r);l&&a.move(o);let s=G.listItem(e,t,n,{...r,...a.current()});return l&&(s=s.replace(/^(?:[*+-]|\d+\.)([\r\n]| {1,3})/,function(e){return e+o})),s}var er=n(77126);let ei={tokenize:function(e,t,n){let r=0;return function t(l){return(87===l||119===l)&&r<3?(r++,e.consume(l),t):46===l&&3===r?(e.consume(l),i):n(l)};function i(e){return null===e?n(e):t(e)}},partial:!0},el={tokenize:function(e,t,n){let r,i,o;return a;function a(t){return 46===t||95===t?e.check(ea,u,s)(t):null===t||(0,l.Ee)(t)||(0,l.Ny)(t)||45!==t&&(0,l.es)(t)?u(t):(o=!0,e.consume(t),a)}function s(t){return 95===t?r=!0:(i=r,r=void 0),e.consume(t),a}function u(e){return i||r||!o?n(e):t(e)}},partial:!0},eo={tokenize:function(e,t){let n=0,r=0;return i;function i(a){return 40===a?(n++,e.consume(a),i):41===a&&r<n?o(a):33===a||34===a||38===a||39===a||41===a||42===a||44===a||46===a||58===a||59===a||60===a||63===a||93===a||95===a||126===a?e.check(ea,t,o)(a):null===a||(0,l.Ee)(a)||(0,l.Ny)(a)?t(a):(e.consume(a),i)}function o(t){return 41===t&&r++,e.consume(t),i}},partial:!0},ea={tokenize:function(e,t,n){return r;function r(a){return 33===a||34===a||39===a||41===a||42===a||44===a||46===a||58===a||59===a||63===a||95===a||126===a?(e.consume(a),r):38===a?(e.consume(a),o):93===a?(e.consume(a),i):60===a||null===a||(0,l.Ee)(a)||(0,l.Ny)(a)?t(a):n(a)}function i(e){return null===e||40===e||91===e||(0,l.Ee)(e)||(0,l.Ny)(e)?t(e):r(e)}function o(t){return(0,l.CW)(t)?function t(i){return 59===i?(e.consume(i),r):(0,l.CW)(i)?(e.consume(i),t):n(i)}(t):n(t)}},partial:!0},es={tokenize:function(e,t,n){return function(t){return e.consume(t),r};function r(e){return(0,l.lV)(e)?n(e):t(e)}},partial:!0},eu={name:"wwwAutolink",tokenize:function(e,t,n){let r=this;return function(t){return 87!==t&&119!==t||!eh.call(r,r.previous)||eb(r.events)?n(t):(e.enter("literalAutolink"),e.enter("literalAutolinkWww"),e.check(ei,e.attempt(el,e.attempt(eo,i),n),n)(t))};function i(n){return e.exit("literalAutolinkWww"),e.exit("literalAutolink"),t(n)}},previous:eh},ec={name:"protocolAutolink",tokenize:function(e,t,n){let r=this,i="",o=!1;return function(t){return(72===t||104===t)&&em.call(r,r.previous)&&!eb(r.events)?(e.enter("literalAutolink"),e.enter("literalAutolinkHttp"),i+=String.fromCodePoint(t),e.consume(t),a):n(t)};function a(t){if((0,l.CW)(t)&&i.length<5)return i+=String.fromCodePoint(t),e.consume(t),a;if(58===t){let n=i.toLowerCase();if("http"===n||"https"===n)return e.consume(t),s}return n(t)}function s(t){return 47===t?(e.consume(t),o)?u:(o=!0,s):n(t)}function u(t){return null===t||(0,l.JQ)(t)||(0,l.Ee)(t)||(0,l.Ny)(t)||(0,l.es)(t)?n(t):e.attempt(el,e.attempt(eo,c),n)(t)}function c(n){return e.exit("literalAutolinkHttp"),e.exit("literalAutolink"),t(n)}},previous:em},ef={name:"emailAutolink",tokenize:function(e,t,n){let r,i,o=this;return function(t){return!ey(t)||!eg.call(o,o.previous)||eb(o.events)?n(t):(e.enter("literalAutolink"),e.enter("literalAutolinkEmail"),function t(r){return ey(r)?(e.consume(r),t):64===r?(e.consume(r),a):n(r)}(t))};function a(t){return 46===t?e.check(es,u,s)(t):45===t||95===t||(0,l.lV)(t)?(i=!0,e.consume(t),a):u(t)}function s(t){return e.consume(t),r=!0,a}function u(a){return i&&r&&(0,l.CW)(o.previous)?(e.exit("literalAutolinkEmail"),e.exit("literalAutolink"),t(a)):n(a)}},previous:eg},ep={},ed=48;for(;ed<123;)ep[ed]=ef,58==++ed?ed=65:91===ed&&(ed=97);function eh(e){return null===e||40===e||42===e||95===e||91===e||93===e||126===e||(0,l.Ee)(e)}function em(e){return!(0,l.CW)(e)}function eg(e){return!(47===e||ey(e))}function ey(e){return 43===e||45===e||46===e||95===e||(0,l.lV)(e)}function eb(e){let t=e.length,n=!1;for(;t--;){let r=e[t][1];if(("labelLink"===r.type||"labelImage"===r.type)&&!r._balanced){n=!0;break}if(r._gfmAutolinkLiteralWalkedInto){n=!1;break}}return e.length>0&&!n&&(e[e.length-1][1]._gfmAutolinkLiteralWalkedInto=!0),n}ep[43]=ef,ep[45]=ef,ep[46]=ef,ep[95]=ef,ep[72]=[ef,ec],ep[104]=[ef,ec],ep[87]=[ef,eu],ep[119]=[ef,eu];var ev=n(27870),ek=n(32146);let ex={tokenize:function(e,t,n){let r=this;return(0,ek.N)(e,function(e){let i=r.events[r.events.length-1];return i&&"gfmFootnoteDefinitionIndent"===i[1].type&&4===i[2].sliceSerialize(i[1],!0).length?t(e):n(e)},"gfmFootnoteDefinitionIndent",5)},partial:!0};function ew(e,t,n){let r,i=this,l=i.events.length,o=i.parser.gfmFootnotes||(i.parser.gfmFootnotes=[]);for(;l--;){let e=i.events[l][1];if("labelImage"===e.type){r=e;break}if("gfmFootnoteCall"===e.type||"labelLink"===e.type||"label"===e.type||"image"===e.type||"link"===e.type)break}return function(l){if(!r||!r._balanced)return n(l);let a=(0,k.B)(i.sliceSerialize({start:r.end,end:i.now()}));return 94===a.codePointAt(0)&&o.includes(a.slice(1))?(e.enter("gfmFootnoteCallLabelMarker"),e.consume(l),e.exit("gfmFootnoteCallLabelMarker"),t(l)):n(l)}}function eS(e,t){let n=e.length;for(;n--;)if("labelImage"===e[n][1].type&&"enter"===e[n][0]){e[n][1];break}e[n+1][1].type="data",e[n+3][1].type="gfmFootnoteCallLabelMarker";let r={type:"gfmFootnoteCall",start:Object.assign({},e[n+3][1].start),end:Object.assign({},e[e.length-1][1].end)},i={type:"gfmFootnoteCallMarker",start:Object.assign({},e[n+3][1].end),end:Object.assign({},e[n+3][1].end)};i.end.column++,i.end.offset++,i.end._bufferIndex++;let l={type:"gfmFootnoteCallString",start:Object.assign({},i.end),end:Object.assign({},e[e.length-1][1].start)},o={type:"chunkString",contentType:"string",start:Object.assign({},l.start),end:Object.assign({},l.end)},a=[e[n+1],e[n+2],["enter",r,t],e[n+3],e[n+4],["enter",i,t],["exit",i,t],["enter",l,t],["enter",o,t],["exit",o,t],["exit",l,t],e[e.length-2],e[e.length-1],["exit",r,t]];return e.splice(n,e.length-n+1,...a),e}function eC(e,t,n){let r,i=this,o=i.parser.gfmFootnotes||(i.parser.gfmFootnotes=[]),a=0;return function(t){return e.enter("gfmFootnoteCall"),e.enter("gfmFootnoteCallLabelMarker"),e.consume(t),e.exit("gfmFootnoteCallLabelMarker"),s};function s(t){return 94!==t?n(t):(e.enter("gfmFootnoteCallMarker"),e.consume(t),e.exit("gfmFootnoteCallMarker"),e.enter("gfmFootnoteCallString"),e.enter("chunkString").contentType="string",u)}function u(s){if(a>999||93===s&&!r||null===s||91===s||(0,l.Ee)(s))return n(s);if(93===s){e.exit("chunkString");let r=e.exit("gfmFootnoteCallString");return o.includes((0,k.B)(i.sliceSerialize(r)))?(e.enter("gfmFootnoteCallLabelMarker"),e.consume(s),e.exit("gfmFootnoteCallLabelMarker"),e.exit("gfmFootnoteCall"),t):n(s)}return(0,l.Ee)(s)||(r=!0),a++,e.consume(s),92===s?c:u}function c(t){return 91===t||92===t||93===t?(e.consume(t),a++,u):u(t)}}function eE(e,t,n){let r,i,o=this,a=o.parser.gfmFootnotes||(o.parser.gfmFootnotes=[]),s=0;return function(t){return e.enter("gfmFootnoteDefinition")._container=!0,e.enter("gfmFootnoteDefinitionLabel"),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionLabelMarker"),u};function u(t){return 94===t?(e.enter("gfmFootnoteDefinitionMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionMarker"),e.enter("gfmFootnoteDefinitionLabelString"),e.enter("chunkString").contentType="string",c):n(t)}function c(t){if(s>999||93===t&&!i||null===t||91===t||(0,l.Ee)(t))return n(t);if(93===t){e.exit("chunkString");let n=e.exit("gfmFootnoteDefinitionLabelString");return r=(0,k.B)(o.sliceSerialize(n)),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionLabelMarker"),e.exit("gfmFootnoteDefinitionLabel"),p}return(0,l.Ee)(t)||(i=!0),s++,e.consume(t),92===t?f:c}function f(t){return 91===t||92===t||93===t?(e.consume(t),s++,c):c(t)}function p(t){return 58===t?(e.enter("definitionMarker"),e.consume(t),e.exit("definitionMarker"),a.includes(r)||a.push(r),(0,ek.N)(e,d,"gfmFootnoteDefinitionWhitespace")):n(t)}function d(e){return t(e)}}function eD(e,t,n){return e.check(ev.B,t,e.attempt(ex,t,n))}function eT(e){e.exit("gfmFootnoteDefinition")}var eA=n(31535),eL=n(35990);class eP{constructor(){this.map=[]}add(e,t,n){!function(e,t,n,r){let i=0;if(0!==n||0!==r.length){for(;i<e.map.length;){if(e.map[i][0]===t){e.map[i][1]+=n,e.map[i][2].push(...r);return}i+=1}e.map.push([t,n,r])}}(this,e,t,n)}consume(e){if(this.map.sort(function(e,t){return e[0]-t[0]}),0===this.map.length)return;let t=this.map.length,n=[];for(;t>0;)t-=1,n.push(e.slice(this.map[t][0]+this.map[t][1]),this.map[t][2]),e.length=this.map[t][0];n.push(e.slice()),e.length=0;let r=n.pop();for(;r;){for(let t of r)e.push(t);r=n.pop()}this.map.length=0}}function eI(e,t,n){let r,i=this,o=0,a=0;return function(e){let t=i.events.length-1;for(;t>-1;){let e=i.events[t][1].type;if("lineEnding"===e||"linePrefix"===e)t--;else break}let r=t>-1?i.events[t][1].type:null,l="tableHead"===r||"tableRow"===r?v:s;return l===v&&i.parser.lazy[i.now().line]?n(e):l(e)};function s(t){var n;return e.enter("tableHead"),e.enter("tableRow"),124===(n=t)||(r=!0,a+=1),u(n)}function u(t){return null===t?n(t):(0,l.HP)(t)?a>1?(a=0,i.interrupt=!0,e.exit("tableRow"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),p):n(t):(0,l.On)(t)?(0,ek.N)(e,u,"whitespace")(t):(a+=1,r&&(r=!1,o+=1),124===t)?(e.enter("tableCellDivider"),e.consume(t),e.exit("tableCellDivider"),r=!0,u):(e.enter("data"),c(t))}function c(t){return null===t||124===t||(0,l.Ee)(t)?(e.exit("data"),u(t)):(e.consume(t),92===t?f:c)}function f(t){return 92===t||124===t?(e.consume(t),c):c(t)}function p(t){return(i.interrupt=!1,i.parser.lazy[i.now().line])?n(t):(e.enter("tableDelimiterRow"),r=!1,(0,l.On)(t))?(0,ek.N)(e,d,"linePrefix",i.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):d(t)}function d(t){return 45===t||58===t?m(t):124===t?(r=!0,e.enter("tableCellDivider"),e.consume(t),e.exit("tableCellDivider"),h):n(t)}function h(t){return(0,l.On)(t)?(0,ek.N)(e,m,"whitespace")(t):m(t)}function m(t){return 58===t?(a+=1,r=!0,e.enter("tableDelimiterMarker"),e.consume(t),e.exit("tableDelimiterMarker"),g):45===t?(a+=1,g(t)):null===t||(0,l.HP)(t)?b(t):n(t)}function g(t){return 45===t?(e.enter("tableDelimiterFiller"),function t(n){return 45===n?(e.consume(n),t):58===n?(r=!0,e.exit("tableDelimiterFiller"),e.enter("tableDelimiterMarker"),e.consume(n),e.exit("tableDelimiterMarker"),y):(e.exit("tableDelimiterFiller"),y(n))}(t)):n(t)}function y(t){return(0,l.On)(t)?(0,ek.N)(e,b,"whitespace")(t):b(t)}function b(i){if(124===i)return d(i);if(null===i||(0,l.HP)(i))return r&&o===a?(e.exit("tableDelimiterRow"),e.exit("tableHead"),t(i)):n(i);return n(i)}function v(t){return e.enter("tableRow"),k(t)}function k(n){return 124===n?(e.enter("tableCellDivider"),e.consume(n),e.exit("tableCellDivider"),k):null===n||(0,l.HP)(n)?(e.exit("tableRow"),t(n)):(0,l.On)(n)?(0,ek.N)(e,k,"whitespace")(n):(e.enter("data"),x(n))}function x(t){return null===t||124===t||(0,l.Ee)(t)?(e.exit("data"),k(t)):(e.consume(t),92===t?w:x)}function w(t){return 92===t||124===t?(e.consume(t),x):x(t)}}function eq(e,t){let n,r,i,l=-1,o=!0,a=0,s=[0,0,0,0],u=[0,0,0,0],c=!1,f=0,p=new eP;for(;++l<e.length;){let d=e[l],h=d[1];"enter"===d[0]?"tableHead"===h.type?(c=!1,0!==f&&(eO(p,t,f,n,r),r=void 0,f=0),n={type:"table",start:Object.assign({},h.start),end:Object.assign({},h.end)},p.add(l,0,[["enter",n,t]])):"tableRow"===h.type||"tableDelimiterRow"===h.type?(o=!0,i=void 0,s=[0,0,0,0],u=[0,l+1,0,0],c&&(c=!1,r={type:"tableBody",start:Object.assign({},h.start),end:Object.assign({},h.end)},p.add(l,0,[["enter",r,t]])),a="tableDelimiterRow"===h.type?2:r?3:1):a&&("data"===h.type||"tableDelimiterMarker"===h.type||"tableDelimiterFiller"===h.type)?(o=!1,0===u[2]&&(0!==s[1]&&(u[0]=u[1],i=eN(p,t,s,a,void 0,i),s=[0,0,0,0]),u[2]=l)):"tableCellDivider"===h.type&&(o?o=!1:(0!==s[1]&&(u[0]=u[1],i=eN(p,t,s,a,void 0,i)),u=[(s=u)[1],l,0,0])):"tableHead"===h.type?(c=!0,f=l):"tableRow"===h.type||"tableDelimiterRow"===h.type?(f=l,0!==s[1]?(u[0]=u[1],i=eN(p,t,s,a,l,i)):0!==u[1]&&(i=eN(p,t,u,a,l,i)),a=0):a&&("data"===h.type||"tableDelimiterMarker"===h.type||"tableDelimiterFiller"===h.type)&&(u[3]=l)}for(0!==f&&eO(p,t,f,n,r),p.consume(t.events),l=-1;++l<t.events.length;){let e=t.events[l];"enter"===e[0]&&"table"===e[1].type&&(e[1]._align=function(e,t){let n=!1,r=[];for(;t<e.length;){let i=e[t];if(n){if("enter"===i[0])"tableContent"===i[1].type&&r.push("tableDelimiterMarker"===e[t+1][1].type?"left":"none");else if("tableContent"===i[1].type){if("tableDelimiterMarker"===e[t-1][1].type){let e=r.length-1;r[e]="left"===r[e]?"center":"right"}}else if("tableDelimiterRow"===i[1].type)break}else"enter"===i[0]&&"tableDelimiterRow"===i[1].type&&(n=!0);t+=1}return r}(t.events,l))}return e}function eN(e,t,n,r,i,l){0!==n[0]&&(l.end=Object.assign({},eR(t.events,n[0])),e.add(n[0],0,[["exit",l,t]]));let o=eR(t.events,n[1]);if(l={type:1===r?"tableHeader":2===r?"tableDelimiter":"tableData",start:Object.assign({},o),end:Object.assign({},o)},e.add(n[1],0,[["enter",l,t]]),0!==n[2]){let i=eR(t.events,n[2]),l=eR(t.events,n[3]),o={type:"tableContent",start:Object.assign({},i),end:Object.assign({},l)};if(e.add(n[2],0,[["enter",o,t]]),2!==r){let r=t.events[n[2]],i=t.events[n[3]];if(r[1].end=Object.assign({},i[1].end),r[1].type="chunkText",r[1].contentType="text",n[3]>n[2]+1){let t=n[2]+1,r=n[3]-n[2]-1;e.add(t,r,[])}}e.add(n[3]+1,0,[["exit",o,t]])}return void 0!==i&&(l.end=Object.assign({},eR(t.events,i)),e.add(i,0,[["exit",l,t]]),l=void 0),l}function eO(e,t,n,r,i){let l=[],o=eR(t.events,n);i&&(i.end=Object.assign({},o),l.push(["exit",i,t])),r.end=Object.assign({},o),l.push(["exit",r,t]),e.add(n+1,0,l)}function eR(e,t){let n=e[t],r="enter"===n[0]?"start":"end";return n[1][r]}let eF={name:"tasklistCheck",tokenize:function(e,t,n){let r=this;return function(t){return null===r.previous&&r._gfmTasklistFirstContentOfListItem?(e.enter("taskListCheck"),e.enter("taskListCheckMarker"),e.consume(t),e.exit("taskListCheckMarker"),i):n(t)};function i(t){return(0,l.Ee)(t)?(e.enter("taskListCheckValueUnchecked"),e.consume(t),e.exit("taskListCheckValueUnchecked"),o):88===t||120===t?(e.enter("taskListCheckValueChecked"),e.consume(t),e.exit("taskListCheckValueChecked"),o):n(t)}function o(t){return 93===t?(e.enter("taskListCheckMarker"),e.consume(t),e.exit("taskListCheckMarker"),e.exit("taskListCheck"),a):n(t)}function a(r){return(0,l.HP)(r)?t(r):(0,l.On)(r)?e.check({tokenize:ez},t,n)(r):n(r)}}};function ez(e,t,n){return(0,ek.N)(e,function(e){return null===e?n(e):t(e)},"whitespace")}let eM={};function eH(e){let t,n=e||eM,r=this.data(),i=r.micromarkExtensions||(r.micromarkExtensions=[]),l=r.fromMarkdownExtensions||(r.fromMarkdownExtensions=[]),o=r.toMarkdownExtensions||(r.toMarkdownExtensions=[]);i.push((0,er.y)([{text:ep},{document:{91:{name:"gfmFootnoteDefinition",tokenize:eE,continuation:{tokenize:eD},exit:eT}},text:{91:{name:"gfmFootnoteCall",tokenize:eC},93:{name:"gfmPotentialFootnoteCall",add:"after",tokenize:ew,resolveTo:eS}}},function(e){let t=(e||{}).singleTilde,n={name:"strikethrough",tokenize:function(e,n,r){let i=this.previous,l=this.events,o=0;return function(a){return 126===i&&"characterEscape"!==l[l.length-1][1].type?r(a):(e.enter("strikethroughSequenceTemporary"),function l(a){let s=(0,V.S)(i);if(126===a)return o>1?r(a):(e.consume(a),o++,l);if(o<2&&!t)return r(a);let u=e.exit("strikethroughSequenceTemporary"),c=(0,V.S)(a);return u._open=!c||2===c&&!!s,u._close=!s||2===s&&!!c,n(a)}(a))}},resolveAll:function(e,t){let n=-1;for(;++n<e.length;)if("enter"===e[n][0]&&"strikethroughSequenceTemporary"===e[n][1].type&&e[n][1]._close){let r=n;for(;r--;)if("exit"===e[r][0]&&"strikethroughSequenceTemporary"===e[r][1].type&&e[r][1]._open&&e[n][1].end.offset-e[n][1].start.offset==e[r][1].end.offset-e[r][1].start.offset){e[n][1].type="strikethroughSequence",e[r][1].type="strikethroughSequence";let i={type:"strikethrough",start:Object.assign({},e[r][1].start),end:Object.assign({},e[n][1].end)},l={type:"strikethroughText",start:Object.assign({},e[r][1].end),end:Object.assign({},e[n][1].start)},o=[["enter",i,t],["enter",e[r][1],t],["exit",e[r][1],t],["enter",l,t]],a=t.parser.constructs.insideSpan.null;a&&(0,eA.m)(o,o.length,0,(0,eL.W)(a,e.slice(r+1,n),t)),(0,eA.m)(o,o.length,0,[["exit",l,t],["enter",e[n][1],t],["exit",e[n][1],t],["exit",i,t]]),(0,eA.m)(e,r-1,n-r+3,o),n=r+o.length-2;break}}for(n=-1;++n<e.length;)"strikethroughSequenceTemporary"===e[n][1].type&&(e[n][1].type="data");return e}};return null==t&&(t=!0),{text:{126:n},insideSpan:{null:[n]},attentionMarkers:{null:[126]}}}(n),{flow:{null:{name:"table",tokenize:eI,resolveAll:eq}}},{text:{91:eF}}])),l.push([{transforms:[g],enter:{literalAutolink:c,literalAutolinkEmail:f,literalAutolinkHttp:f,literalAutolinkWww:f},exit:{literalAutolink:m,literalAutolinkEmail:h,literalAutolinkHttp:p,literalAutolinkWww:d}},{enter:{gfmFootnoteCallString:x,gfmFootnoteCall:w,gfmFootnoteDefinitionLabelString:S,gfmFootnoteDefinition:C},exit:{gfmFootnoteCallString:E,gfmFootnoteCall:D,gfmFootnoteDefinitionLabelString:T,gfmFootnoteDefinition:A}},{canContainEols:["delete"],enter:{strikethrough:N},exit:{strikethrough:O}},{enter:{table:Y,tableData:K,tableHeader:K,tableRow:Q},exit:{codeText:Z,table:$,tableData:J,tableHeader:J,tableRow:J}},{exit:{taskListCheckValueChecked:ee,taskListCheckValueUnchecked:ee,paragraph:et}}]),o.push({extensions:[{unsafe:[{character:"@",before:"[+\\-.\\w]",after:"[\\-.\\w]",inConstruct:s,notInConstruct:u},{character:".",before:"[Ww]",after:"[\\-.\\w]",inConstruct:s,notInConstruct:u},{character:":",before:"[ps]",after:"\\/",inConstruct:s,notInConstruct:u}]},(t=!1,n&&n.firstLineBlank&&(t=!0),{handlers:{footnoteDefinition:function(e,n,r,i){let l=r.createTracker(i),o=l.move("[^"),a=r.enter("footnoteDefinition"),s=r.enter("label");return o+=l.move(r.safe(r.associationId(e),{before:o,after:"]"})),s(),o+=l.move("]:"),e.children&&e.children.length>0&&(l.shift(4),o+=l.move((t?"\n":" ")+r.indentLines(r.containerFlow(e,l.current()),t?I:P))),a(),o},footnoteReference:L},unsafe:[{character:"[",inConstruct:["label","phrasing","reference"]}]}),{unsafe:[{character:"~",inConstruct:"phrasing",notInConstruct:q}],handlers:{delete:R}},function(e){let t=e||{},n=t.tableCellPadding,r=t.tablePipeAlign,i=t.stringLength,l=n?" ":"|";return{unsafe:[{character:"\r",inConstruct:"tableCell"},{character:"\n",inConstruct:"tableCell"},{atBreak:!0,character:"|",after:"[	 :-]"},{character:"|",inConstruct:"tableCell"},{atBreak:!0,character:":",after:"-"},{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{inlineCode:function(e,t,n){let r=G.inlineCode(e,t,n);return n.stack.includes("tableCell")&&(r=r.replace(/\|/g,"\\$&")),r},table:function(e,t,n,r){return a(function(e,t,n){let r=e.children,i=-1,l=[],o=t.enter("table");for(;++i<r.length;)l[i]=s(r[i],t,n);return o(),l}(e,n,r),e.align)},tableCell:o,tableRow:function(e,t,n,r){let i=a([s(e,n,r)]);return i.slice(0,i.indexOf("\n"))}}};function o(e,t,n,r){let i=n.enter("tableCell"),o=n.enter("phrasing"),a=n.containerPhrasing(e,{...r,before:l,after:l});return o(),i(),a}function a(e,t){return function(e,t){let n=t||{},r=(n.align||[]).concat(),i=n.stringLength||F,l=[],o=[],a=[],s=[],u=0,c=-1;for(;++c<e.length;){let t=[],r=[],l=-1;for(e[c].length>u&&(u=e[c].length);++l<e[c].length;){var f;let o=null==(f=e[c][l])?"":String(f);if(!1!==n.alignDelimiters){let e=i(o);r[l]=e,(void 0===s[l]||e>s[l])&&(s[l]=e)}t.push(o)}o[c]=t,a[c]=r}let p=-1;if("object"==typeof r&&"length"in r)for(;++p<u;)l[p]=z(r[p]);else{let e=z(r);for(;++p<u;)l[p]=e}p=-1;let d=[],h=[];for(;++p<u;){let e=l[p],t="",r="";99===e?(t=":",r=":"):108===e?t=":":114===e&&(r=":");let i=!1===n.alignDelimiters?1:Math.max(1,s[p]-t.length-r.length),o=t+"-".repeat(i)+r;!1!==n.alignDelimiters&&((i=t.length+i+r.length)>s[p]&&(s[p]=i),h[p]=i),d[p]=o}o.splice(1,0,d),a.splice(1,0,h),c=-1;let m=[];for(;++c<o.length;){let e=o[c],t=a[c];p=-1;let r=[];for(;++p<u;){let i=e[p]||"",o="",a="";if(!1!==n.alignDelimiters){let e=s[p]-(t[p]||0),n=l[p];114===n?o=" ".repeat(e):99===n?e%2?(o=" ".repeat(e/2+.5),a=" ".repeat(e/2-.5)):a=o=" ".repeat(e/2):a=" ".repeat(e)}!1===n.delimiterStart||p||r.push("|"),!1!==n.padding&&(!1!==n.alignDelimiters||""!==i)&&(!1!==n.delimiterStart||p)&&r.push(" "),!1!==n.alignDelimiters&&r.push(o),r.push(i),!1!==n.alignDelimiters&&r.push(a),!1!==n.padding&&r.push(" "),(!1!==n.delimiterEnd||p!==u-1)&&r.push("|")}m.push(!1===n.delimiterEnd?r.join("").replace(/ +$/,""):r.join(""))}return m.join("\n")}(e,{align:t,alignDelimiters:r,padding:n,stringLength:i})}function s(e,t,n){let r=e.children,i=-1,l=[],a=t.enter("tableRow");for(;++i<r.length;)l[i]=o(r[i],e,t,n);return a(),l}}(n),{unsafe:[{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{listItem:en}}]})}},80506:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(55732).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},82268:(e,t,n)=>{"use strict";n.d(t,{C:()=>r});let r=function(e){var t,n;if(null==e)return l;if("function"==typeof e)return i(e);if("object"==typeof e){return Array.isArray(e)?function(e){let t=[],n=-1;for(;++n<e.length;)t[n]=r(e[n]);return i(function(...e){let n=-1;for(;++n<t.length;)if(t[n].apply(this,e))return!0;return!1})}(e):(t=e,i(function(e){let n;for(n in t)if(e[n]!==t[n])return!1;return!0}))}if("string"==typeof e){return n=e,i(function(e){return e&&e.type===n})}throw Error("Expected function, string, or object as test")};function i(e){return function(t,n,r){return!!(function(e){return null!==e&&"object"==typeof e&&"type"in e}(t)&&e.call(this,t,"number"==typeof n?n:void 0,r||void 0))}}function l(){return!0}},85519:e=>{"use strict";var t=Object.prototype.hasOwnProperty,n=Object.prototype.toString,r=Object.defineProperty,i=Object.getOwnPropertyDescriptor,l=function(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===n.call(e)},o=function(e){if(!e||"[object Object]"!==n.call(e))return!1;var r,i=t.call(e,"constructor"),l=e.constructor&&e.constructor.prototype&&t.call(e.constructor.prototype,"isPrototypeOf");if(e.constructor&&!i&&!l)return!1;for(r in e);return void 0===r||t.call(e,r)},a=function(e,t){r&&"__proto__"===t.name?r(e,t.name,{enumerable:!0,configurable:!0,value:t.newValue,writable:!0}):e[t.name]=t.newValue},s=function(e,n){if("__proto__"===n){if(!t.call(e,n))return;else if(i)return i(e,n).value}return e[n]};e.exports=function e(){var t,n,r,i,u,c,f=arguments[0],p=1,d=arguments.length,h=!1;for("boolean"==typeof f&&(h=f,f=arguments[1]||{},p=2),(null==f||"object"!=typeof f&&"function"!=typeof f)&&(f={});p<d;++p)if(t=arguments[p],null!=t)for(n in t)r=s(f,n),f!==(i=s(t,n))&&(h&&i&&(o(i)||(u=l(i)))?(u?(u=!1,c=r&&l(r)?r:[]):c=r&&o(r)?r:{},a(f,{name:n,newValue:e(h,c,i)})):void 0!==i&&a(f,{name:n,newValue:i}));return f}},86857:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(55732).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},89961:(e,t,n)=>{"use strict";function r(e){return e.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}n.d(t,{B:()=>r})},96249:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(55732).A)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])}};
//# sourceMappingURL=1481.js.map