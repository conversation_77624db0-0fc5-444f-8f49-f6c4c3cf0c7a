try{let a="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},e=(new a.Error).stack;e&&(a._sentryDebugIds=a._sentryDebugIds||{},a._sentryDebugIds[e]="e4ff89ae-ca32-4743-b881-b9ac5d0a12c3",a._sentryDebugIdIdentifier="sentry-dbid-e4ff89ae-ca32-4743-b881-b9ac5d0a12c3")}catch(a){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7358],{60534:(a,e,s)=>{"use strict";s.r(e),s.d(e,{onRouterTransitionStart:()=>h});var r=s(76289),t=s(29695),d=s(21654),o=s(95704);globalThis._sentryRewritesTunnelPath="/monitoring",globalThis.SENTRY_RELEASE={id:"4f114066cfffc2184b89e81ba9495ab6e8e5cf06"},globalThis._sentryBasePath=void 0,globalThis._sentryRewriteFramesAssetPrefixPath="",globalThis._sentryAssetPrefix=void 0,globalThis._sentryExperimentalThirdPartyOriginStackFrames=void 0,globalThis._sentryRouteManifest='{"dynamicRoutes":[{"path":"/my-courses/:courseId","regex":"^/my-courses/([^/]+)$","paramNames":["courseId"]},{"path":"/my-courses/:courseId/exam","regex":"^/my-courses/([^/]+)/exam$","paramNames":["courseId"]},{"path":"/my-courses/:courseId/exam/results","regex":"^/my-courses/([^/]+)/exam/results$","paramNames":["courseId"]},{"path":"/auth/sign-in/:sign-in*?","regex":"^/auth/sign-in(?:/(.*))?$","paramNames":["sign-in"]},{"path":"/auth/sign-up/:sign-up*?","regex":"^/auth/sign-up(?:/(.*))?$","paramNames":["sign-up"]},{"path":"/dashboard/admin/institutions/:id","regex":"^/dashboard/admin/institutions/([^/]+)$","paramNames":["id"]},{"path":"/dashboard/admin/users/:id","regex":"^/dashboard/admin/users/([^/]+)$","paramNames":["id"]},{"path":"/dashboard/product/:productId","regex":"^/dashboard/product/([^/]+)$","paramNames":["productId"]},{"path":"/dashboard/profile/:profile*?","regex":"^/dashboard/profile(?:/(.*))?$","paramNames":["profile"]},{"path":"/dashboard/student/courses/:id","regex":"^/dashboard/student/courses/([^/]+)$","paramNames":["id"]},{"path":"/dashboard/teacher/classes/:id","regex":"^/dashboard/teacher/classes/([^/]+)$","paramNames":["id"]},{"path":"/dashboard/teacher/classes/:id/courses","regex":"^/dashboard/teacher/classes/([^/]+)/courses$","paramNames":["id"]},{"path":"/dashboard/teacher/classes/:id/students","regex":"^/dashboard/teacher/classes/([^/]+)/students$","paramNames":["id"]},{"path":"/dashboard/teacher/courses/:id","regex":"^/dashboard/teacher/courses/([^/]+)$","paramNames":["id"]}],"staticRoutes":[{"path":"/"},{"path":"/courses"},{"path":"/my-courses"},{"path":"/dashboard"},{"path":"/dashboard/admin"},{"path":"/dashboard/admin/institutions"},{"path":"/dashboard/admin/institutions/new"},{"path":"/dashboard/admin/subscriptions"},{"path":"/dashboard/admin/users"},{"path":"/dashboard/admin/users/new"},{"path":"/dashboard/kanban"},{"path":"/dashboard/overview/@area_stats"},{"path":"/dashboard/overview/@bar_stats"},{"path":"/dashboard/overview/@pie_stats"},{"path":"/dashboard/overview/@sales"},{"path":"/dashboard/product"},{"path":"/dashboard/student"},{"path":"/dashboard/student/certificates"},{"path":"/dashboard/student/courses"},{"path":"/dashboard/student/progress"},{"path":"/dashboard/teacher"},{"path":"/dashboard/teacher/classes"},{"path":"/dashboard/teacher/classes/new"},{"path":"/dashboard/teacher/courses"},{"path":"/dashboard/teacher/courses/generate"},{"path":"/dashboard/teacher/courses/new"},{"path":"/dashboard/teacher/reports"}]}',o.env.NEXT_PUBLIC_SENTRY_DISABLED||r.Ts({dsn:o.env.NEXT_PUBLIC_SENTRY_DSN,integrations:[t.w()],sendDefaultPii:!0,tracesSampleRate:1,replaysSessionSampleRate:.1,replaysOnErrorSampleRate:1,debug:!1});let h=d.Nc},85003:(a,e,s)=>{Promise.resolve().then(s.t.bind(s,81959,23)),Promise.resolve().then(s.t.bind(s,17989,23)),Promise.resolve().then(s.t.bind(s,88785,23)),Promise.resolve().then(s.t.bind(s,63886,23)),Promise.resolve().then(s.t.bind(s,9766,23)),Promise.resolve().then(s.t.bind(s,15278,23)),Promise.resolve().then(s.t.bind(s,19390,23)),Promise.resolve().then(s.t.bind(s,98924,23))}},a=>{var e=e=>a(a.s=e);a.O(0,[4850,8441,3840],()=>(e(60534),e(1666),e(85003))),_N_E=a.O()}]);