{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["turbopack:///[project]/node_modules/@sentry/vercel-edge/src/client.ts", "turbopack:///[project]/node_modules/node_modules/@opentelemetry/core/build/esm/trace/suppress-tracing.js", "turbopack:///[project]/node_modules/node_modules/@opentelemetry/core/build/esm/baggage/constants.js", "turbopack:///[project]/node_modules/node_modules/@opentelemetry/core/build/esm/baggage/utils.js", "turbopack:///[project]/node_modules/node_modules/@opentelemetry/core/build/esm/baggage/propagation/W3CBaggagePropagator.js", "turbopack:///[project]/node_modules/node_modules/@opentelemetry/core/build/esm/common/attributes.js", "turbopack:///[project]/node_modules/node_modules/@opentelemetry/core/build/esm/common/logging-error-handler.js", "turbopack:///[project]/node_modules/node_modules/@opentelemetry/core/build/esm/common/global-error-handler.js", "turbopack:///[project]/node_modules/node_modules/@opentelemetry/core/build/esm/utils/sampling.js", "turbopack:///[project]/node_modules/node_modules/@opentelemetry/core/build/esm/utils/environment.js", "turbopack:///[project]/node_modules/node_modules/@opentelemetry/core/build/esm/platform/node/environment.js", "turbopack:///[project]/node_modules/node_modules/@opentelemetry/core/build/esm/platform/node/performance.js", "turbopack:///[project]/node_modules/node_modules/@opentelemetry/core/build/esm/platform/node/timer-util.js", "turbopack:///[project]/node_modules/node_modules/@opentelemetry/core/build/esm/common/time.js", "turbopack:///[project]/node_modules/node_modules/@opentelemetry/core/build/esm/ExportResult.js", "turbopack:///[project]/node_modules/node_modules/@opentelemetry/core/build/esm/propagation/composite.js", "turbopack:///[project]/node_modules/node_modules/@opentelemetry/core/build/esm/internal/validators.js", "turbopack:///[project]/node_modules/node_modules/@opentelemetry/core/build/esm/trace/TraceState.js", "turbopack:///[project]/node_modules/node_modules/@opentelemetry/core/build/esm/trace/W3CTraceContextPropagator.js", "turbopack:///[project]/node_modules/node_modules/@opentelemetry/core/build/esm/utils/lodash.merge.js", "turbopack:///[project]/node_modules/node_modules/@opentelemetry/core/build/esm/utils/merge.js", "turbopack:///[project]/node_modules/node_modules/@opentelemetry/core/build/esm/utils/promise.js", "turbopack:///[project]/node_modules/node_modules/@opentelemetry/core/build/esm/utils/callback.js", "turbopack:///[project]/node_modules/node_modules/@opentelemetry/sdk-trace-base/build/esm/enums.js", "turbopack:///[project]/node_modules/node_modules/@opentelemetry/sdk-trace-base/build/esm/Span.js", "turbopack:///[project]/node_modules/node_modules/@opentelemetry/sdk-trace-base/build/esm/Sampler.js", "turbopack:///[project]/node_modules/node_modules/@opentelemetry/sdk-trace-base/build/esm/sampler/AlwaysOffSampler.js", "turbopack:///[project]/node_modules/node_modules/@opentelemetry/sdk-trace-base/build/esm/sampler/AlwaysOnSampler.js", "turbopack:///[project]/node_modules/node_modules/@opentelemetry/sdk-trace-base/build/esm/sampler/ParentBasedSampler.js", "turbopack:///[project]/node_modules/node_modules/@opentelemetry/sdk-trace-base/build/esm/sampler/TraceIdRatioBasedSampler.js", "turbopack:///[project]/node_modules/node_modules/@opentelemetry/sdk-trace-base/build/esm/config.js", "turbopack:///[project]/node_modules/node_modules/@opentelemetry/sdk-trace-base/build/esm/utility.js", "turbopack:///[project]/node_modules/node_modules/@opentelemetry/sdk-trace-base/build/esm/export/BatchSpanProcessorBase.js", "turbopack:///[project]/node_modules/node_modules/@opentelemetry/sdk-trace-base/build/esm/platform/node/export/BatchSpanProcessor.js", "turbopack:///[project]/node_modules/node_modules/@opentelemetry/sdk-trace-base/build/esm/platform/node/RandomIdGenerator.js", "turbopack:///[project]/node_modules/node_modules/@opentelemetry/sdk-trace-base/build/esm/Tracer.js", "turbopack:///[project]/node_modules/node_modules/@opentelemetry/sdk-trace-base/build/esm/MultiSpanProcessor.js", "turbopack:///[project]/node_modules/node_modules/@opentelemetry/sdk-trace-base/build/esm/export/NoopSpanProcessor.js", "turbopack:///[project]/node_modules/node_modules/@opentelemetry/sdk-trace-base/build/esm/BasicTracerProvider.js", "turbopack:///[project]/node_modules/@sentry/vercel-edge/src/debug-build.ts", "turbopack:///[project]/node_modules/@sentry/vercel-edge/src/integrations/wintercg-fetch.ts", "turbopack:///[project]/node_modules/@sentry/vercel-edge/src/transports/index.ts", "turbopack:///[project]/node_modules/@sentry/vercel-edge/src/utils/vercel.ts", "turbopack:///[project]/node_modules/@sentry/vercel-edge/src/vendored/abstract-async-hooks-context-manager.ts", "turbopack:///[project]/node_modules/@sentry/vercel-edge/src/vendored/async-local-storage-context-manager.ts", "turbopack:///[project]/node_modules/@sentry/vercel-edge/src/sdk.ts", "turbopack:///[project]/node_modules/@sentry/vercel-edge/src/integrations/tracing/vercelai.ts", "turbopack:///[project]/node_modules/@sentry/vercel-edge/src/logs/exports.ts"], "sourcesContent": ["import type { BasicTracerProvider } from '@opentelemetry/sdk-trace-base';\nimport type { ServerRuntimeClientOptions } from '@sentry/core';\nimport { applySdkMetadata, ServerRuntimeClient } from '@sentry/core';\nimport type { VercelEdgeClientOptions } from './types';\n\ndeclare const process: {\n  env: Record<string, string>;\n};\n\n/**\n * The Sentry Vercel Edge Runtime SDK Client.\n *\n * @see VercelEdgeClientOptions for documentation on configuration options.\n * @see ServerRuntimeClient for usage documentation.\n */\nexport class VercelEdgeClient extends ServerRuntimeClient<VercelEdgeClientOptions> {\n  public traceProvider: BasicTracerProvider | undefined;\n\n  /**\n   * Creates a new Vercel Edge Runtime SDK instance.\n   * @param options Configuration options for this SDK.\n   */\n  public constructor(options: VercelEdgeClientOptions) {\n    applySdkMetadata(options, 'vercel-edge');\n    options._metadata = options._metadata || {};\n\n    const clientOptions: ServerRuntimeClientOptions = {\n      ...options,\n      platform: 'javascript',\n      // TODO: Grab version information\n      runtime: { name: 'vercel-edge' },\n      serverName: options.serverName || process.env.SENTRY_NAME,\n    };\n\n    super(clientOptions);\n  }\n\n  // Eslint ignore explanation: This is already documented in super.\n  // eslint-disable-next-line jsdoc/require-jsdoc\n  public async flush(timeout?: number): Promise<boolean> {\n    const provider = this.traceProvider;\n    const spanProcessor = provider?.activeSpanProcessor;\n\n    if (spanProcessor) {\n      await spanProcessor.forceFlush();\n    }\n\n    if (this.getOptions().sendClientReports) {\n      this._flushOutcomes();\n    }\n\n    return super.flush(timeout);\n  }\n}\n", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { createContextKey } from '@opentelemetry/api';\nvar SUPPRESS_TRACING_KEY = createContextKey('OpenTelemetry SDK Context Key SUPPRESS_TRACING');\nexport function suppressTracing(context) {\n    return context.setValue(SUPPRESS_TRACING_KEY, true);\n}\nexport function unsuppressTracing(context) {\n    return context.deleteValue(SUPPRESS_TRACING_KEY);\n}\nexport function isTracingSuppressed(context) {\n    return context.getValue(SUPPRESS_TRACING_KEY) === true;\n}\n//# sourceMappingURL=suppress-tracing.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport var BAGGAGE_KEY_PAIR_SEPARATOR = '=';\nexport var BAGGAGE_PROPERTIES_SEPARATOR = ';';\nexport var BAGGAGE_ITEMS_SEPARATOR = ',';\n// Name of the http header used to propagate the baggage\nexport var BAGGAGE_HEADER = 'baggage';\n// Maximum number of name-value pairs allowed by w3c spec\nexport var BAGGAGE_MAX_NAME_VALUE_PAIRS = 180;\n// Maximum number of bytes per a single name-value pair allowed by w3c spec\nexport var BAGGAGE_MAX_PER_NAME_VALUE_PAIRS = 4096;\n// Maximum total length of all name-value pairs allowed by w3c spec\nexport var BAGGAGE_MAX_TOTAL_LENGTH = 8192;\n//# sourceMappingURL=constants.js.map", "var __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { baggageEntryMetadataFromString, } from '@opentelemetry/api';\nimport { BAGGAGE_ITEMS_SEPARATOR, BAGGAGE_PROPERTIES_SEPARATOR, BAGGAGE_KEY_PAIR_SEPARATOR, BAGGAGE_MAX_TOTAL_LENGTH, } from './constants';\nexport function serializeKeyPairs(keyPairs) {\n    return keyPairs.reduce(function (hValue, current) {\n        var value = \"\" + hValue + (hValue !== '' ? BAGGAGE_ITEMS_SEPARATOR : '') + current;\n        return value.length > BAGGAGE_MAX_TOTAL_LENGTH ? hValue : value;\n    }, '');\n}\nexport function getKeyPairs(baggage) {\n    return baggage.getAllEntries().map(function (_a) {\n        var _b = __read(_a, 2), key = _b[0], value = _b[1];\n        var entry = encodeURIComponent(key) + \"=\" + encodeURIComponent(value.value);\n        // include opaque metadata if provided\n        // NOTE: we intentionally don't URI-encode the metadata - that responsibility falls on the metadata implementation\n        if (value.metadata !== undefined) {\n            entry += BAGGAGE_PROPERTIES_SEPARATOR + value.metadata.toString();\n        }\n        return entry;\n    });\n}\nexport function parsePairKeyValue(entry) {\n    var valueProps = entry.split(BAGGAGE_PROPERTIES_SEPARATOR);\n    if (valueProps.length <= 0)\n        return;\n    var keyPairPart = valueProps.shift();\n    if (!keyPairPart)\n        return;\n    var separatorIndex = keyPairPart.indexOf(BAGGAGE_KEY_PAIR_SEPARATOR);\n    if (separatorIndex <= 0)\n        return;\n    var key = decodeURIComponent(keyPairPart.substring(0, separatorIndex).trim());\n    var value = decodeURIComponent(keyPairPart.substring(separatorIndex + 1).trim());\n    var metadata;\n    if (valueProps.length > 0) {\n        metadata = baggageEntryMetadataFromString(valueProps.join(BAGGAGE_PROPERTIES_SEPARATOR));\n    }\n    return { key: key, value: value, metadata: metadata };\n}\n/**\n * Parse a string serialized in the baggage HTTP Format (without metadata):\n * https://github.com/w3c/baggage/blob/master/baggage/HTTP_HEADER_FORMAT.md\n */\nexport function parseKeyPairsIntoRecord(value) {\n    if (typeof value !== 'string' || value.length === 0)\n        return {};\n    return value\n        .split(BAGGAGE_ITEMS_SEPARATOR)\n        .map(function (entry) {\n        return parsePairKeyValue(entry);\n    })\n        .filter(function (keyPair) { return keyPair !== undefined && keyPair.value.length > 0; })\n        .reduce(function (headers, keyPair) {\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        headers[keyPair.key] = keyPair.value;\n        return headers;\n    }, {});\n}\n//# sourceMappingURL=utils.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { propagation, } from '@opentelemetry/api';\nimport { isTracingSuppressed } from '../../trace/suppress-tracing';\nimport { BAGGAGE_HEADER, BAGGAGE_ITEMS_SEPARATOR, BAGGAGE_MAX_NAME_VALUE_PAIRS, BAGGAGE_MAX_PER_NAME_VALUE_PAIRS, } from '../constants';\nimport { getKeyPairs, parsePairKeyValue, serializeKeyPairs } from '../utils';\n/**\n * Propagates {@link Baggage} through Context format propagation.\n *\n * Based on the Baggage specification:\n * https://w3c.github.io/baggage/\n */\nvar W3CBaggagePropagator = /** @class */ (function () {\n    function W3CBaggagePropagator() {\n    }\n    W3CBaggagePropagator.prototype.inject = function (context, carrier, setter) {\n        var baggage = propagation.getBaggage(context);\n        if (!baggage || isTracingSuppressed(context))\n            return;\n        var keyPairs = getKeyPairs(baggage)\n            .filter(function (pair) {\n            return pair.length <= BAGGAGE_MAX_PER_NAME_VALUE_PAIRS;\n        })\n            .slice(0, BAGGAGE_MAX_NAME_VALUE_PAIRS);\n        var headerValue = serializeKeyPairs(keyPairs);\n        if (headerValue.length > 0) {\n            setter.set(carrier, BAGGAGE_HEADER, headerValue);\n        }\n    };\n    W3CBaggagePropagator.prototype.extract = function (context, carrier, getter) {\n        var headerValue = getter.get(carrier, BAGGAGE_HEADER);\n        var baggageString = Array.isArray(headerValue)\n            ? headerValue.join(BAGGAGE_ITEMS_SEPARATOR)\n            : headerValue;\n        if (!baggageString)\n            return context;\n        var baggage = {};\n        if (baggageString.length === 0) {\n            return context;\n        }\n        var pairs = baggageString.split(BAGGAGE_ITEMS_SEPARATOR);\n        pairs.forEach(function (entry) {\n            var keyPair = parsePairKeyValue(entry);\n            if (keyPair) {\n                var baggageEntry = { value: keyPair.value };\n                if (keyPair.metadata) {\n                    baggageEntry.metadata = keyPair.metadata;\n                }\n                baggage[keyPair.key] = baggageEntry;\n            }\n        });\n        if (Object.entries(baggage).length === 0) {\n            return context;\n        }\n        return propagation.setBaggage(context, propagation.createBaggage(baggage));\n    };\n    W3CBaggagePropagator.prototype.fields = function () {\n        return [BAGGAGE_HEADER];\n    };\n    return W3CBaggagePropagator;\n}());\nexport { W3CBaggagePropagator };\n//# sourceMappingURL=W3CBaggagePropagator.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nimport { diag } from '@opentelemetry/api';\nexport function sanitizeAttributes(attributes) {\n    var e_1, _a;\n    var out = {};\n    if (typeof attributes !== 'object' || attributes == null) {\n        return out;\n    }\n    try {\n        for (var _b = __values(Object.entries(attributes)), _c = _b.next(); !_c.done; _c = _b.next()) {\n            var _d = __read(_c.value, 2), key = _d[0], val = _d[1];\n            if (!isAttributeKey(key)) {\n                diag.warn(\"Invalid attribute key: \" + key);\n                continue;\n            }\n            if (!isAttributeValue(val)) {\n                diag.warn(\"Invalid attribute value set for key: \" + key);\n                continue;\n            }\n            if (Array.isArray(val)) {\n                out[key] = val.slice();\n            }\n            else {\n                out[key] = val;\n            }\n        }\n    }\n    catch (e_1_1) { e_1 = { error: e_1_1 }; }\n    finally {\n        try {\n            if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n        }\n        finally { if (e_1) throw e_1.error; }\n    }\n    return out;\n}\nexport function isAttributeKey(key) {\n    return typeof key === 'string' && key.length > 0;\n}\nexport function isAttributeValue(val) {\n    if (val == null) {\n        return true;\n    }\n    if (Array.isArray(val)) {\n        return isHomogeneousAttributeValueArray(val);\n    }\n    return isValidPrimitiveAttributeValue(val);\n}\nfunction isHomogeneousAttributeValueArray(arr) {\n    var e_2, _a;\n    var type;\n    try {\n        for (var arr_1 = __values(arr), arr_1_1 = arr_1.next(); !arr_1_1.done; arr_1_1 = arr_1.next()) {\n            var element = arr_1_1.value;\n            // null/undefined elements are allowed\n            if (element == null)\n                continue;\n            if (!type) {\n                if (isValidPrimitiveAttributeValue(element)) {\n                    type = typeof element;\n                    continue;\n                }\n                // encountered an invalid primitive\n                return false;\n            }\n            if (typeof element === type) {\n                continue;\n            }\n            return false;\n        }\n    }\n    catch (e_2_1) { e_2 = { error: e_2_1 }; }\n    finally {\n        try {\n            if (arr_1_1 && !arr_1_1.done && (_a = arr_1.return)) _a.call(arr_1);\n        }\n        finally { if (e_2) throw e_2.error; }\n    }\n    return true;\n}\nfunction isValidPrimitiveAttributeValue(val) {\n    switch (typeof val) {\n        case 'number':\n        case 'boolean':\n        case 'string':\n            return true;\n    }\n    return false;\n}\n//# sourceMappingURL=attributes.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { diag } from '@opentelemetry/api';\n/**\n * Returns a function that logs an error using the provided logger, or a\n * console logger if one was not provided.\n */\nexport function loggingErrorHandler() {\n    return function (ex) {\n        diag.error(stringifyException(ex));\n    };\n}\n/**\n * Converts an exception into a string representation\n * @param {Exception} ex\n */\nfunction stringifyException(ex) {\n    if (typeof ex === 'string') {\n        return ex;\n    }\n    else {\n        return JSON.stringify(flattenException(ex));\n    }\n}\n/**\n * Flattens an exception into key-value pairs by traversing the prototype chain\n * and coercing values to strings. Duplicate properties will not be overwritten;\n * the first insert wins.\n */\nfunction flattenException(ex) {\n    var result = {};\n    var current = ex;\n    while (current !== null) {\n        Object.getOwnPropertyNames(current).forEach(function (propertyName) {\n            if (result[propertyName])\n                return;\n            var value = current[propertyName];\n            if (value) {\n                result[propertyName] = String(value);\n            }\n        });\n        current = Object.getPrototypeOf(current);\n    }\n    return result;\n}\n//# sourceMappingURL=logging-error-handler.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { loggingErrorHandler } from './logging-error-handler';\n/** The global error handler delegate */\nvar delegateHandler = loggingErrorHandler();\n/**\n * Set the global error handler\n * @param {ErrorHandler} handler\n */\nexport function setGlobalErrorHandler(handler) {\n    delegateHandler = handler;\n}\n/**\n * Return the global error handler\n * @param {Exception} ex\n */\nexport function globalErrorHandler(ex) {\n    try {\n        delegateHandler(ex);\n    }\n    catch (_a) { } // eslint-disable-line no-empty\n}\n//# sourceMappingURL=global-error-handler.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport var TracesSamplerValues;\n(function (TracesSamplerValues) {\n    TracesSamplerValues[\"AlwaysOff\"] = \"always_off\";\n    TracesSamplerValues[\"AlwaysOn\"] = \"always_on\";\n    TracesSamplerValues[\"ParentBasedAlwaysOff\"] = \"parentbased_always_off\";\n    TracesSamplerValues[\"ParentBasedAlwaysOn\"] = \"parentbased_always_on\";\n    TracesSamplerValues[\"ParentBasedTraceIdRatio\"] = \"parentbased_traceidratio\";\n    TracesSamplerValues[\"TraceIdRatio\"] = \"traceidratio\";\n})(TracesSamplerValues || (TracesSamplerValues = {}));\n//# sourceMappingURL=sampling.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { DiagLogLevel } from '@opentelemetry/api';\nimport { TracesSamplerValues } from './sampling';\nvar DEFAULT_LIST_SEPARATOR = ',';\n/**\n * Environment interface to define all names\n */\nvar ENVIRONMENT_BOOLEAN_KEYS = ['OTEL_SDK_DISABLED'];\nfunction isEnvVarABoolean(key) {\n    return (ENVIRONMENT_BOOLEAN_KEYS.indexOf(key) > -1);\n}\nvar ENVIRONMENT_NUMBERS_KEYS = [\n    'OTEL_BSP_EXPORT_TIMEOUT',\n    'OTEL_BSP_MAX_EXPORT_BATCH_SIZE',\n    'OTEL_BSP_MAX_QUEUE_SIZE',\n    'OTEL_BSP_SCHEDULE_DELAY',\n    'OTEL_BLRP_EXPORT_TIMEOUT',\n    'OTEL_BLRP_MAX_EXPORT_BATCH_SIZE',\n    'OTEL_BLRP_MAX_QUEUE_SIZE',\n    'OTEL_BLRP_SCHEDULE_DELAY',\n    'OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT',\n    'OTEL_ATTRIBUTE_COUNT_LIMIT',\n    'OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT',\n    'OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT',\n    'OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT',\n    'OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT',\n    'OTEL_SPAN_EVENT_COUNT_LIMIT',\n    'OTEL_SPAN_LINK_COUNT_LIMIT',\n    'OTEL_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT',\n    'OTEL_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT',\n    'OTEL_EXPORTER_OTLP_TIMEOUT',\n    'OTEL_EXPORTER_OTLP_TRACES_TIMEOUT',\n    'OTEL_EXPORTER_OTLP_METRICS_TIMEOUT',\n    'OTEL_EXPORTER_OTLP_LOGS_TIMEOUT',\n    'OTEL_EXPORTER_JAEGER_AGENT_PORT',\n];\nfunction isEnvVarANumber(key) {\n    return (ENVIRONMENT_NUMBERS_KEYS.indexOf(key) > -1);\n}\nvar ENVIRONMENT_LISTS_KEYS = [\n    'OTEL_NO_PATCH_MODULES',\n    'OTEL_PROPAGATORS',\n    'OTEL_SEMCONV_STABILITY_OPT_IN',\n];\nfunction isEnvVarAList(key) {\n    return ENVIRONMENT_LISTS_KEYS.indexOf(key) > -1;\n}\nexport var DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT = Infinity;\nexport var DEFAULT_ATTRIBUTE_COUNT_LIMIT = 128;\nexport var DEFAULT_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT = 128;\nexport var DEFAULT_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT = 128;\n/**\n * Default environment variables\n */\nexport var DEFAULT_ENVIRONMENT = {\n    OTEL_SDK_DISABLED: false,\n    CONTAINER_NAME: '',\n    ECS_CONTAINER_METADATA_URI_V4: '',\n    ECS_CONTAINER_METADATA_URI: '',\n    HOSTNAME: '',\n    KUBERNETES_SERVICE_HOST: '',\n    NAMESPACE: '',\n    OTEL_BSP_EXPORT_TIMEOUT: 30000,\n    OTEL_BSP_MAX_EXPORT_BATCH_SIZE: 512,\n    OTEL_BSP_MAX_QUEUE_SIZE: 2048,\n    OTEL_BSP_SCHEDULE_DELAY: 5000,\n    OTEL_BLRP_EXPORT_TIMEOUT: 30000,\n    OTEL_BLRP_MAX_EXPORT_BATCH_SIZE: 512,\n    OTEL_BLRP_MAX_QUEUE_SIZE: 2048,\n    OTEL_BLRP_SCHEDULE_DELAY: 5000,\n    OTEL_EXPORTER_JAEGER_AGENT_HOST: '',\n    OTEL_EXPORTER_JAEGER_AGENT_PORT: 6832,\n    OTEL_EXPORTER_JAEGER_ENDPOINT: '',\n    OTEL_EXPORTER_JAEGER_PASSWORD: '',\n    OTEL_EXPORTER_JAEGER_USER: '',\n    OTEL_EXPORTER_OTLP_ENDPOINT: '',\n    OTEL_EXPORTER_OTLP_TRACES_ENDPOINT: '',\n    OTEL_EXPORTER_OTLP_METRICS_ENDPOINT: '',\n    OTEL_EXPORTER_OTLP_LOGS_ENDPOINT: '',\n    OTEL_EXPORTER_OTLP_HEADERS: '',\n    OTEL_EXPORTER_OTLP_TRACES_HEADERS: '',\n    OTEL_EXPORTER_OTLP_METRICS_HEADERS: '',\n    OTEL_EXPORTER_OTLP_LOGS_HEADERS: '',\n    OTEL_EXPORTER_OTLP_TIMEOUT: 10000,\n    OTEL_EXPORTER_OTLP_TRACES_TIMEOUT: 10000,\n    OTEL_EXPORTER_OTLP_METRICS_TIMEOUT: 10000,\n    OTEL_EXPORTER_OTLP_LOGS_TIMEOUT: 10000,\n    OTEL_EXPORTER_ZIPKIN_ENDPOINT: 'http://localhost:9411/api/v2/spans',\n    OTEL_LOG_LEVEL: DiagLogLevel.INFO,\n    OTEL_NO_PATCH_MODULES: [],\n    OTEL_PROPAGATORS: ['tracecontext', 'baggage'],\n    OTEL_RESOURCE_ATTRIBUTES: '',\n    OTEL_SERVICE_NAME: '',\n    OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT: DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT,\n    OTEL_ATTRIBUTE_COUNT_LIMIT: DEFAULT_ATTRIBUTE_COUNT_LIMIT,\n    OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT: DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT,\n    OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT: DEFAULT_ATTRIBUTE_COUNT_LIMIT,\n    OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT: DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT,\n    OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT: DEFAULT_ATTRIBUTE_COUNT_LIMIT,\n    OTEL_SPAN_EVENT_COUNT_LIMIT: 128,\n    OTEL_SPAN_LINK_COUNT_LIMIT: 128,\n    OTEL_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT: DEFAULT_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT,\n    OTEL_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT: DEFAULT_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT,\n    OTEL_TRACES_EXPORTER: '',\n    OTEL_TRACES_SAMPLER: TracesSamplerValues.ParentBasedAlwaysOn,\n    OTEL_TRACES_SAMPLER_ARG: '',\n    OTEL_LOGS_EXPORTER: '',\n    OTEL_EXPORTER_OTLP_INSECURE: '',\n    OTEL_EXPORTER_OTLP_TRACES_INSECURE: '',\n    OTEL_EXPORTER_OTLP_METRICS_INSECURE: '',\n    OTEL_EXPORTER_OTLP_LOGS_INSECURE: '',\n    OTEL_EXPORTER_OTLP_CERTIFICATE: '',\n    OTEL_EXPORTER_OTLP_TRACES_CERTIFICATE: '',\n    OTEL_EXPORTER_OTLP_METRICS_CERTIFICATE: '',\n    OTEL_EXPORTER_OTLP_LOGS_CERTIFICATE: '',\n    OTEL_EXPORTER_OTLP_COMPRESSION: '',\n    OTEL_EXPORTER_OTLP_TRACES_COMPRESSION: '',\n    OTEL_EXPORTER_OTLP_METRICS_COMPRESSION: '',\n    OTEL_EXPORTER_OTLP_LOGS_COMPRESSION: '',\n    OTEL_EXPORTER_OTLP_CLIENT_KEY: '',\n    OTEL_EXPORTER_OTLP_TRACES_CLIENT_KEY: '',\n    OTEL_EXPORTER_OTLP_METRICS_CLIENT_KEY: '',\n    OTEL_EXPORTER_OTLP_LOGS_CLIENT_KEY: '',\n    OTEL_EXPORTER_OTLP_CLIENT_CERTIFICATE: '',\n    OTEL_EXPORTER_OTLP_TRACES_CLIENT_CERTIFICATE: '',\n    OTEL_EXPORTER_OTLP_METRICS_CLIENT_CERTIFICATE: '',\n    OTEL_EXPORTER_OTLP_LOGS_CLIENT_CERTIFICATE: '',\n    OTEL_EXPORTER_OTLP_PROTOCOL: 'http/protobuf',\n    OTEL_EXPORTER_OTLP_TRACES_PROTOCOL: 'http/protobuf',\n    OTEL_EXPORTER_OTLP_METRICS_PROTOCOL: 'http/protobuf',\n    OTEL_EXPORTER_OTLP_LOGS_PROTOCOL: 'http/protobuf',\n    OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE: 'cumulative',\n    OTEL_SEMCONV_STABILITY_OPT_IN: [],\n};\n/**\n * @param key\n * @param environment\n * @param values\n */\nfunction parseBoolean(key, environment, values) {\n    if (typeof values[key] === 'undefined') {\n        return;\n    }\n    var value = String(values[key]);\n    // support case-insensitive \"true\"\n    environment[key] = value.toLowerCase() === 'true';\n}\n/**\n * Parses a variable as number with number validation\n * @param name\n * @param environment\n * @param values\n * @param min\n * @param max\n */\nfunction parseNumber(name, environment, values, min, max) {\n    if (min === void 0) { min = -Infinity; }\n    if (max === void 0) { max = Infinity; }\n    if (typeof values[name] !== 'undefined') {\n        var value = Number(values[name]);\n        if (!isNaN(value)) {\n            if (value < min) {\n                environment[name] = min;\n            }\n            else if (value > max) {\n                environment[name] = max;\n            }\n            else {\n                environment[name] = value;\n            }\n        }\n    }\n}\n/**\n * Parses list-like strings from input into output.\n * @param name\n * @param environment\n * @param values\n * @param separator\n */\nfunction parseStringList(name, output, input, separator) {\n    if (separator === void 0) { separator = DEFAULT_LIST_SEPARATOR; }\n    var givenValue = input[name];\n    if (typeof givenValue === 'string') {\n        output[name] = givenValue.split(separator).map(function (v) { return v.trim(); });\n    }\n}\n// The support string -> DiagLogLevel mappings\nvar logLevelMap = {\n    ALL: DiagLogLevel.ALL,\n    VERBOSE: DiagLogLevel.VERBOSE,\n    DEBUG: DiagLogLevel.DEBUG,\n    INFO: DiagLogLevel.INFO,\n    WARN: DiagLogLevel.WARN,\n    ERROR: DiagLogLevel.ERROR,\n    NONE: DiagLogLevel.NONE,\n};\n/**\n * Environmentally sets log level if valid log level string is provided\n * @param key\n * @param environment\n * @param values\n */\nfunction setLogLevelFromEnv(key, environment, values) {\n    var value = values[key];\n    if (typeof value === 'string') {\n        var theLevel = logLevelMap[value.toUpperCase()];\n        if (theLevel != null) {\n            environment[key] = theLevel;\n        }\n    }\n}\n/**\n * Parses environment values\n * @param values\n */\nexport function parseEnvironment(values) {\n    var environment = {};\n    for (var env in DEFAULT_ENVIRONMENT) {\n        var key = env;\n        switch (key) {\n            case 'OTEL_LOG_LEVEL':\n                setLogLevelFromEnv(key, environment, values);\n                break;\n            default:\n                if (isEnvVarABoolean(key)) {\n                    parseBoolean(key, environment, values);\n                }\n                else if (isEnvVarANumber(key)) {\n                    parseNumber(key, environment, values);\n                }\n                else if (isEnvVarAList(key)) {\n                    parseStringList(key, environment, values);\n                }\n                else {\n                    var value = values[key];\n                    if (typeof value !== 'undefined' && value !== null) {\n                        environment[key] = String(value);\n                    }\n                }\n        }\n    }\n    return environment;\n}\n//# sourceMappingURL=environment.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { DEFAULT_ENVIRONMENT, parseEnvironment, } from '../../utils/environment';\n/**\n * Gets the environment variables\n */\nexport function getEnv() {\n    var processEnv = parseEnvironment(process.env);\n    return Object.assign({}, DEFAULT_ENVIRONMENT, processEnv);\n}\nexport function getEnvWithoutDefaults() {\n    return parseEnvironment(process.env);\n}\n//# sourceMappingURL=environment.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { performance } from 'perf_hooks';\nexport var otperformance = performance;\n//# sourceMappingURL=performance.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport function unrefTimer(timer) {\n    timer.unref();\n}\n//# sourceMappingURL=timer-util.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { otperformance as performance } from '../platform';\nvar NANOSECOND_DIGITS = 9;\nvar NANOSECOND_DIGITS_IN_MILLIS = 6;\nvar MILLISECONDS_TO_NANOSECONDS = Math.pow(10, NANOSECOND_DIGITS_IN_MILLIS);\nvar SECOND_TO_NANOSECONDS = Math.pow(10, NANOSECOND_DIGITS);\n/**\n * Converts a number of milliseconds from epoch to HrTime([seconds, remainder in nanoseconds]).\n * @param epochMillis\n */\nexport function millisToHrTime(epochMillis) {\n    var epochSeconds = epochMillis / 1000;\n    // Decimals only.\n    var seconds = Math.trunc(epochSeconds);\n    // Round sub-nanosecond accuracy to nanosecond.\n    var nanos = Math.round((epochMillis % 1000) * MILLISECONDS_TO_NANOSECONDS);\n    return [seconds, nanos];\n}\nexport function getTimeOrigin() {\n    var timeOrigin = performance.timeOrigin;\n    if (typeof timeOrigin !== 'number') {\n        var perf = performance;\n        timeOrigin = perf.timing && perf.timing.fetchStart;\n    }\n    return timeOrigin;\n}\n/**\n * Returns an hrtime calculated via performance component.\n * @param performanceNow\n */\nexport function hrTime(performanceNow) {\n    var timeOrigin = millisToHrTime(getTimeOrigin());\n    var now = millisToHrTime(typeof performanceNow === 'number' ? performanceNow : performance.now());\n    return addHrTimes(timeOrigin, now);\n}\n/**\n *\n * Converts a TimeInput to an HrTime, defaults to _hrtime().\n * @param time\n */\nexport function timeInputToHrTime(time) {\n    // process.hrtime\n    if (isTimeInputHrTime(time)) {\n        return time;\n    }\n    else if (typeof time === 'number') {\n        // Must be a performance.now() if it's smaller than process start time.\n        if (time < getTimeOrigin()) {\n            return hrTime(time);\n        }\n        else {\n            // epoch milliseconds or performance.timeOrigin\n            return millisToHrTime(time);\n        }\n    }\n    else if (time instanceof Date) {\n        return millisToHrTime(time.getTime());\n    }\n    else {\n        throw TypeError('Invalid input type');\n    }\n}\n/**\n * Returns a duration of two hrTime.\n * @param startTime\n * @param endTime\n */\nexport function hrTimeDuration(startTime, endTime) {\n    var seconds = endTime[0] - startTime[0];\n    var nanos = endTime[1] - startTime[1];\n    // overflow\n    if (nanos < 0) {\n        seconds -= 1;\n        // negate\n        nanos += SECOND_TO_NANOSECONDS;\n    }\n    return [seconds, nanos];\n}\n/**\n * Convert hrTime to timestamp, for example \"2019-05-14T17:00:00.000123456Z\"\n * @param time\n */\nexport function hrTimeToTimeStamp(time) {\n    var precision = NANOSECOND_DIGITS;\n    var tmp = \"\" + '0'.repeat(precision) + time[1] + \"Z\";\n    var nanoString = tmp.substring(tmp.length - precision - 1);\n    var date = new Date(time[0] * 1000).toISOString();\n    return date.replace('000Z', nanoString);\n}\n/**\n * Convert hrTime to nanoseconds.\n * @param time\n */\nexport function hrTimeToNanoseconds(time) {\n    return time[0] * SECOND_TO_NANOSECONDS + time[1];\n}\n/**\n * Convert hrTime to milliseconds.\n * @param time\n */\nexport function hrTimeToMilliseconds(time) {\n    return time[0] * 1e3 + time[1] / 1e6;\n}\n/**\n * Convert hrTime to microseconds.\n * @param time\n */\nexport function hrTimeToMicroseconds(time) {\n    return time[0] * 1e6 + time[1] / 1e3;\n}\n/**\n * check if time is HrTime\n * @param value\n */\nexport function isTimeInputHrTime(value) {\n    return (Array.isArray(value) &&\n        value.length === 2 &&\n        typeof value[0] === 'number' &&\n        typeof value[1] === 'number');\n}\n/**\n * check if input value is a correct types.TimeInput\n * @param value\n */\nexport function isTimeInput(value) {\n    return (isTimeInputHrTime(value) ||\n        typeof value === 'number' ||\n        value instanceof Date);\n}\n/**\n * Given 2 HrTime formatted times, return their sum as an HrTime.\n */\nexport function addHrTimes(time1, time2) {\n    var out = [time1[0] + time2[0], time1[1] + time2[1]];\n    // Nanoseconds\n    if (out[1] >= SECOND_TO_NANOSECONDS) {\n        out[1] -= SECOND_TO_NANOSECONDS;\n        out[0] += 1;\n    }\n    return out;\n}\n//# sourceMappingURL=time.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport var ExportResultCode;\n(function (ExportResultCode) {\n    ExportResultCode[ExportResultCode[\"SUCCESS\"] = 0] = \"SUCCESS\";\n    ExportResultCode[ExportResultCode[\"FAILED\"] = 1] = \"FAILED\";\n})(ExportResultCode || (ExportResultCode = {}));\n//# sourceMappingURL=ExportResult.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport { diag, } from '@opentelemetry/api';\n/** Combines multiple propagators into a single propagator. */\nvar CompositePropagator = /** @class */ (function () {\n    /**\n     * Construct a composite propagator from a list of propagators.\n     *\n     * @param [config] Configuration object for composite propagator\n     */\n    function CompositePropagator(config) {\n        if (config === void 0) { config = {}; }\n        var _a;\n        this._propagators = (_a = config.propagators) !== null && _a !== void 0 ? _a : [];\n        this._fields = Array.from(new Set(this._propagators\n            // older propagators may not have fields function, null check to be sure\n            .map(function (p) { return (typeof p.fields === 'function' ? p.fields() : []); })\n            .reduce(function (x, y) { return x.concat(y); }, [])));\n    }\n    /**\n     * Run each of the configured propagators with the given context and carrier.\n     * Propagators are run in the order they are configured, so if multiple\n     * propagators write the same carrier key, the propagator later in the list\n     * will \"win\".\n     *\n     * @param context Context to inject\n     * @param carrier Carrier into which context will be injected\n     */\n    CompositePropagator.prototype.inject = function (context, carrier, setter) {\n        var e_1, _a;\n        try {\n            for (var _b = __values(this._propagators), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var propagator = _c.value;\n                try {\n                    propagator.inject(context, carrier, setter);\n                }\n                catch (err) {\n                    diag.warn(\"Failed to inject with \" + propagator.constructor.name + \". Err: \" + err.message);\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n    };\n    /**\n     * Run each of the configured propagators with the given context and carrier.\n     * Propagators are run in the order they are configured, so if multiple\n     * propagators write the same context key, the propagator later in the list\n     * will \"win\".\n     *\n     * @param context Context to add values to\n     * @param carrier Carrier from which to extract context\n     */\n    CompositePropagator.prototype.extract = function (context, carrier, getter) {\n        return this._propagators.reduce(function (ctx, propagator) {\n            try {\n                return propagator.extract(ctx, carrier, getter);\n            }\n            catch (err) {\n                diag.warn(\"Failed to extract with \" + propagator.constructor.name + \". Err: \" + err.message);\n            }\n            return ctx;\n        }, context);\n    };\n    CompositePropagator.prototype.fields = function () {\n        // return a new array so our fields cannot be modified\n        return this._fields.slice();\n    };\n    return CompositePropagator;\n}());\nexport { CompositePropagator };\n//# sourceMappingURL=composite.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar VALID_KEY_CHAR_RANGE = '[_0-9a-z-*/]';\nvar VALID_KEY = \"[a-z]\" + VALID_KEY_CHAR_RANGE + \"{0,255}\";\nvar VALID_VENDOR_KEY = \"[a-z0-9]\" + VALID_KEY_CHAR_RANGE + \"{0,240}@[a-z]\" + VALID_KEY_CHAR_RANGE + \"{0,13}\";\nvar VALID_KEY_REGEX = new RegExp(\"^(?:\" + VALID_KEY + \"|\" + VALID_VENDOR_KEY + \")$\");\nvar VALID_VALUE_BASE_REGEX = /^[ -~]{0,255}[!-~]$/;\nvar INVALID_VALUE_COMMA_EQUAL_REGEX = /,|=/;\n/**\n * Key is opaque string up to 256 characters printable. It MUST begin with a\n * lowercase letter, and can only contain lowercase letters a-z, digits 0-9,\n * underscores _, dashes -, asterisks *, and forward slashes /.\n * For multi-tenant vendor scenarios, an at sign (@) can be used to prefix the\n * vendor name. Vendors SHOULD set the tenant ID at the beginning of the key.\n * see https://www.w3.org/TR/trace-context/#key\n */\nexport function validateKey(key) {\n    return VALID_KEY_REGEX.test(key);\n}\n/**\n * Value is opaque string up to 256 characters printable ASCII RFC0020\n * characters (i.e., the range 0x20 to 0x7E) except comma , and =.\n */\nexport function validateValue(value) {\n    return (VALID_VALUE_BASE_REGEX.test(value) &&\n        !INVALID_VALUE_COMMA_EQUAL_REGEX.test(value));\n}\n//# sourceMappingURL=validators.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { validateKey, validateValue } from '../internal/validators';\nvar MAX_TRACE_STATE_ITEMS = 32;\nvar MAX_TRACE_STATE_LEN = 512;\nvar LIST_MEMBERS_SEPARATOR = ',';\nvar LIST_MEMBER_KEY_VALUE_SPLITTER = '=';\n/**\n * TraceState must be a class and not a simple object type because of the spec\n * requirement (https://www.w3.org/TR/trace-context/#tracestate-field).\n *\n * Here is the list of allowed mutations:\n * - New key-value pair should be added into the beginning of the list\n * - The value of any key can be updated. Modified keys MUST be moved to the\n * beginning of the list.\n */\nvar TraceState = /** @class */ (function () {\n    function TraceState(rawTraceState) {\n        this._internalState = new Map();\n        if (rawTraceState)\n            this._parse(rawTraceState);\n    }\n    TraceState.prototype.set = function (key, value) {\n        // TODO: Benchmark the different approaches(map vs list) and\n        // use the faster one.\n        var traceState = this._clone();\n        if (traceState._internalState.has(key)) {\n            traceState._internalState.delete(key);\n        }\n        traceState._internalState.set(key, value);\n        return traceState;\n    };\n    TraceState.prototype.unset = function (key) {\n        var traceState = this._clone();\n        traceState._internalState.delete(key);\n        return traceState;\n    };\n    TraceState.prototype.get = function (key) {\n        return this._internalState.get(key);\n    };\n    TraceState.prototype.serialize = function () {\n        var _this = this;\n        return this._keys()\n            .reduce(function (agg, key) {\n            agg.push(key + LIST_MEMBER_KEY_VALUE_SPLITTER + _this.get(key));\n            return agg;\n        }, [])\n            .join(LIST_MEMBERS_SEPARATOR);\n    };\n    TraceState.prototype._parse = function (rawTraceState) {\n        if (rawTraceState.length > MAX_TRACE_STATE_LEN)\n            return;\n        this._internalState = rawTraceState\n            .split(LIST_MEMBERS_SEPARATOR)\n            .reverse() // Store in reverse so new keys (.set(...)) will be placed at the beginning\n            .reduce(function (agg, part) {\n            var listMember = part.trim(); // Optional Whitespace (OWS) handling\n            var i = listMember.indexOf(LIST_MEMBER_KEY_VALUE_SPLITTER);\n            if (i !== -1) {\n                var key = listMember.slice(0, i);\n                var value = listMember.slice(i + 1, part.length);\n                if (validateKey(key) && validateValue(value)) {\n                    agg.set(key, value);\n                }\n                else {\n                    // TODO: Consider to add warning log\n                }\n            }\n            return agg;\n        }, new Map());\n        // Because of the reverse() requirement, trunc must be done after map is created\n        if (this._internalState.size > MAX_TRACE_STATE_ITEMS) {\n            this._internalState = new Map(Array.from(this._internalState.entries())\n                .reverse() // Use reverse same as original tracestate parse chain\n                .slice(0, MAX_TRACE_STATE_ITEMS));\n        }\n    };\n    TraceState.prototype._keys = function () {\n        return Array.from(this._internalState.keys()).reverse();\n    };\n    TraceState.prototype._clone = function () {\n        var traceState = new TraceState();\n        traceState._internalState = new Map(this._internalState);\n        return traceState;\n    };\n    return TraceState;\n}());\nexport { TraceState };\n//# sourceMappingURL=TraceState.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { isSpanContextValid, trace, TraceFlags, } from '@opentelemetry/api';\nimport { isTracingSuppressed } from './suppress-tracing';\nimport { TraceState } from './TraceState';\nexport var TRACE_PARENT_HEADER = 'traceparent';\nexport var TRACE_STATE_HEADER = 'tracestate';\nvar VERSION = '00';\nvar VERSION_PART = '(?!ff)[\\\\da-f]{2}';\nvar TRACE_ID_PART = '(?![0]{32})[\\\\da-f]{32}';\nvar PARENT_ID_PART = '(?![0]{16})[\\\\da-f]{16}';\nvar FLAGS_PART = '[\\\\da-f]{2}';\nvar TRACE_PARENT_REGEX = new RegExp(\"^\\\\s?(\" + VERSION_PART + \")-(\" + TRACE_ID_PART + \")-(\" + PARENT_ID_PART + \")-(\" + FLAGS_PART + \")(-.*)?\\\\s?$\");\n/**\n * Parses information from the [traceparent] span tag and converts it into {@link SpanContext}\n * @param traceParent - A meta property that comes from server.\n *     It should be dynamically generated server side to have the server's request trace Id,\n *     a parent span Id that was set on the server's request span,\n *     and the trace flags to indicate the server's sampling decision\n *     (01 = sampled, 00 = not sampled).\n *     for example: '{version}-{traceId}-{spanId}-{sampleDecision}'\n *     For more information see {@link https://www.w3.org/TR/trace-context/}\n */\nexport function parseTraceParent(traceParent) {\n    var match = TRACE_PARENT_REGEX.exec(traceParent);\n    if (!match)\n        return null;\n    // According to the specification the implementation should be compatible\n    // with future versions. If there are more parts, we only reject it if it's using version 00\n    // See https://www.w3.org/TR/trace-context/#versioning-of-traceparent\n    if (match[1] === '00' && match[5])\n        return null;\n    return {\n        traceId: match[2],\n        spanId: match[3],\n        traceFlags: parseInt(match[4], 16),\n    };\n}\n/**\n * Propagates {@link SpanContext} through Trace Context format propagation.\n *\n * Based on the Trace Context specification:\n * https://www.w3.org/TR/trace-context/\n */\nvar W3CTraceContextPropagator = /** @class */ (function () {\n    function W3CTraceContextPropagator() {\n    }\n    W3CTraceContextPropagator.prototype.inject = function (context, carrier, setter) {\n        var spanContext = trace.getSpanContext(context);\n        if (!spanContext ||\n            isTracingSuppressed(context) ||\n            !isSpanContextValid(spanContext))\n            return;\n        var traceParent = VERSION + \"-\" + spanContext.traceId + \"-\" + spanContext.spanId + \"-0\" + Number(spanContext.traceFlags || TraceFlags.NONE).toString(16);\n        setter.set(carrier, TRACE_PARENT_HEADER, traceParent);\n        if (spanContext.traceState) {\n            setter.set(carrier, TRACE_STATE_HEADER, spanContext.traceState.serialize());\n        }\n    };\n    W3CTraceContextPropagator.prototype.extract = function (context, carrier, getter) {\n        var traceParentHeader = getter.get(carrier, TRACE_PARENT_HEADER);\n        if (!traceParentHeader)\n            return context;\n        var traceParent = Array.isArray(traceParentHeader)\n            ? traceParentHeader[0]\n            : traceParentHeader;\n        if (typeof traceParent !== 'string')\n            return context;\n        var spanContext = parseTraceParent(traceParent);\n        if (!spanContext)\n            return context;\n        spanContext.isRemote = true;\n        var traceStateHeader = getter.get(carrier, TRACE_STATE_HEADER);\n        if (traceStateHeader) {\n            // If more than one `tracestate` header is found, we merge them into a\n            // single header.\n            var state = Array.isArray(traceStateHeader)\n                ? traceStateHeader.join(',')\n                : traceStateHeader;\n            spanContext.traceState = new TraceState(typeof state === 'string' ? state : undefined);\n        }\n        return trace.setSpanContext(context, spanContext);\n    };\n    W3CTraceContextPropagator.prototype.fields = function () {\n        return [TRACE_PARENT_HEADER, TRACE_STATE_HEADER];\n    };\n    return W3CTraceContextPropagator;\n}());\nexport { W3CTraceContextPropagator };\n//# sourceMappingURL=W3CTraceContextPropagator.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/**\n * based on lodash in order to support esm builds without esModuleInterop.\n * lodash is using MIT License.\n **/\nvar objectTag = '[object Object]';\nvar nullTag = '[object Null]';\nvar undefinedTag = '[object Undefined]';\nvar funcProto = Function.prototype;\nvar funcToString = funcProto.toString;\nvar objectCtorString = funcToString.call(Object);\nvar getPrototype = overArg(Object.getPrototypeOf, Object);\nvar objectProto = Object.prototype;\nvar hasOwnProperty = objectProto.hasOwnProperty;\nvar symToStringTag = Symbol ? Symbol.toStringTag : undefined;\nvar nativeObjectToString = objectProto.toString;\n/**\n * Creates a unary function that invokes `func` with its argument transformed.\n *\n * @private\n * @param {Function} func The function to wrap.\n * @param {Function} transform The argument transform.\n * @returns {Function} Returns the new function.\n */\nfunction overArg(func, transform) {\n    return function (arg) {\n        return func(transform(arg));\n    };\n}\n/**\n * Checks if `value` is a plain object, that is, an object created by the\n * `Object` constructor or one with a `[[Prototype]]` of `null`.\n *\n * @static\n * @memberOf _\n * @since 0.8.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a plain object, else `false`.\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n * }\n *\n * _.isPlainObject(new Foo);\n * // => false\n *\n * _.isPlainObject([1, 2, 3]);\n * // => false\n *\n * _.isPlainObject({ 'x': 0, 'y': 0 });\n * // => true\n *\n * _.isPlainObject(Object.create(null));\n * // => true\n */\nexport function isPlainObject(value) {\n    if (!isObjectLike(value) || baseGetTag(value) !== objectTag) {\n        return false;\n    }\n    var proto = getPrototype(value);\n    if (proto === null) {\n        return true;\n    }\n    var Ctor = hasOwnProperty.call(proto, 'constructor') && proto.constructor;\n    return (typeof Ctor == 'function' &&\n        Ctor instanceof Ctor &&\n        funcToString.call(Ctor) === objectCtorString);\n}\n/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */\nfunction isObjectLike(value) {\n    return value != null && typeof value == 'object';\n}\n/**\n * The base implementation of `getTag` without fallbacks for buggy environments.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the `toStringTag`.\n */\nfunction baseGetTag(value) {\n    if (value == null) {\n        return value === undefined ? undefinedTag : nullTag;\n    }\n    return symToStringTag && symToStringTag in Object(value)\n        ? getRawTag(value)\n        : objectToString(value);\n}\n/**\n * A specialized version of `baseGetTag` which ignores `Symbol.toStringTag` values.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the raw `toStringTag`.\n */\nfunction getRawTag(value) {\n    var isOwn = hasOwnProperty.call(value, symToStringTag), tag = value[symToStringTag];\n    var unmasked = false;\n    try {\n        value[symToStringTag] = undefined;\n        unmasked = true;\n    }\n    catch (e) {\n        // silence\n    }\n    var result = nativeObjectToString.call(value);\n    if (unmasked) {\n        if (isOwn) {\n            value[symToStringTag] = tag;\n        }\n        else {\n            delete value[symToStringTag];\n        }\n    }\n    return result;\n}\n/**\n * Converts `value` to a string using `Object.prototype.toString`.\n *\n * @private\n * @param {*} value The value to convert.\n * @returns {string} Returns the converted string.\n */\nfunction objectToString(value) {\n    return nativeObjectToString.call(value);\n}\n//# sourceMappingURL=lodash.merge.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/* eslint-disable @typescript-eslint/no-explicit-any */\nimport { isPlainObject } from './lodash.merge';\nvar MAX_LEVEL = 20;\n/**\n * Merges objects together\n * @param args - objects / values to be merged\n */\nexport function merge() {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n    }\n    var result = args.shift();\n    var objects = new WeakMap();\n    while (args.length > 0) {\n        result = mergeTwoObjects(result, args.shift(), 0, objects);\n    }\n    return result;\n}\nfunction takeValue(value) {\n    if (isArray(value)) {\n        return value.slice();\n    }\n    return value;\n}\n/**\n * Merges two objects\n * @param one - first object\n * @param two - second object\n * @param level - current deep level\n * @param objects - objects holder that has been already referenced - to prevent\n * cyclic dependency\n */\nfunction mergeTwoObjects(one, two, level, objects) {\n    if (level === void 0) { level = 0; }\n    var result;\n    if (level > MAX_LEVEL) {\n        return undefined;\n    }\n    level++;\n    if (isPrimitive(one) || isPrimitive(two) || isFunction(two)) {\n        result = takeValue(two);\n    }\n    else if (isArray(one)) {\n        result = one.slice();\n        if (isArray(two)) {\n            for (var i = 0, j = two.length; i < j; i++) {\n                result.push(takeValue(two[i]));\n            }\n        }\n        else if (isObject(two)) {\n            var keys = Object.keys(two);\n            for (var i = 0, j = keys.length; i < j; i++) {\n                var key = keys[i];\n                result[key] = takeValue(two[key]);\n            }\n        }\n    }\n    else if (isObject(one)) {\n        if (isObject(two)) {\n            if (!shouldMerge(one, two)) {\n                return two;\n            }\n            result = Object.assign({}, one);\n            var keys = Object.keys(two);\n            for (var i = 0, j = keys.length; i < j; i++) {\n                var key = keys[i];\n                var twoValue = two[key];\n                if (isPrimitive(twoValue)) {\n                    if (typeof twoValue === 'undefined') {\n                        delete result[key];\n                    }\n                    else {\n                        // result[key] = takeValue(twoValue);\n                        result[key] = twoValue;\n                    }\n                }\n                else {\n                    var obj1 = result[key];\n                    var obj2 = twoValue;\n                    if (wasObjectReferenced(one, key, objects) ||\n                        wasObjectReferenced(two, key, objects)) {\n                        delete result[key];\n                    }\n                    else {\n                        if (isObject(obj1) && isObject(obj2)) {\n                            var arr1 = objects.get(obj1) || [];\n                            var arr2 = objects.get(obj2) || [];\n                            arr1.push({ obj: one, key: key });\n                            arr2.push({ obj: two, key: key });\n                            objects.set(obj1, arr1);\n                            objects.set(obj2, arr2);\n                        }\n                        result[key] = mergeTwoObjects(result[key], twoValue, level, objects);\n                    }\n                }\n            }\n        }\n        else {\n            result = two;\n        }\n    }\n    return result;\n}\n/**\n * Function to check if object has been already reference\n * @param obj\n * @param key\n * @param objects\n */\nfunction wasObjectReferenced(obj, key, objects) {\n    var arr = objects.get(obj[key]) || [];\n    for (var i = 0, j = arr.length; i < j; i++) {\n        var info = arr[i];\n        if (info.key === key && info.obj === obj) {\n            return true;\n        }\n    }\n    return false;\n}\nfunction isArray(value) {\n    return Array.isArray(value);\n}\nfunction isFunction(value) {\n    return typeof value === 'function';\n}\nfunction isObject(value) {\n    return (!isPrimitive(value) &&\n        !isArray(value) &&\n        !isFunction(value) &&\n        typeof value === 'object');\n}\nfunction isPrimitive(value) {\n    return (typeof value === 'string' ||\n        typeof value === 'number' ||\n        typeof value === 'boolean' ||\n        typeof value === 'undefined' ||\n        value instanceof Date ||\n        value instanceof RegExp ||\n        value === null);\n}\nfunction shouldMerge(one, two) {\n    if (!isPlainObject(one) || !isPlainObject(two)) {\n        return false;\n    }\n    return true;\n}\n//# sourceMappingURL=merge.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar Deferred = /** @class */ (function () {\n    function Deferred() {\n        var _this = this;\n        this._promise = new Promise(function (resolve, reject) {\n            _this._resolve = resolve;\n            _this._reject = reject;\n        });\n    }\n    Object.defineProperty(Deferred.prototype, \"promise\", {\n        get: function () {\n            return this._promise;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Deferred.prototype.resolve = function (val) {\n        this._resolve(val);\n    };\n    Deferred.prototype.reject = function (err) {\n        this._reject(err);\n    };\n    return Deferred;\n}());\nexport { Deferred };\n//# sourceMappingURL=promise.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nimport { Deferred } from './promise';\n/**\n * Bind the callback and only invoke the callback once regardless how many times `BindOnceFuture.call` is invoked.\n */\nvar BindOnceFuture = /** @class */ (function () {\n    function BindOnceFuture(_callback, _that) {\n        this._callback = _callback;\n        this._that = _that;\n        this._isCalled = false;\n        this._deferred = new Deferred();\n    }\n    Object.defineProperty(BindOnceFuture.prototype, \"isCalled\", {\n        get: function () {\n            return this._isCalled;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(BindOnceFuture.prototype, \"promise\", {\n        get: function () {\n            return this._deferred.promise;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    BindOnceFuture.prototype.call = function () {\n        var _a;\n        var _this = this;\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        if (!this._isCalled) {\n            this._isCalled = true;\n            try {\n                Promise.resolve((_a = this._callback).call.apply(_a, __spreadArray([this._that], __read(args), false))).then(function (val) { return _this._deferred.resolve(val); }, function (err) { return _this._deferred.reject(err); });\n            }\n            catch (err) {\n                this._deferred.reject(err);\n            }\n        }\n        return this._deferred.promise;\n    };\n    return BindOnceFuture;\n}());\nexport { BindOnceFuture };\n//# sourceMappingURL=callback.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Event name definitions\nexport var ExceptionEventName = 'exception';\n//# sourceMappingURL=enums.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nimport { diag, SpanStatusCode, } from '@opentelemetry/api';\nimport { addHrTimes, millisToHrTime, getTimeOrigin, hrTime, hrTimeDuration, isAttributeValue, isTimeInput, isTimeInputHrTime, otperformance, sanitizeAttributes, } from '@opentelemetry/core';\nimport { SEMATTRS_EXCEPTION_MESSAGE, SEMATTRS_EXCEPTION_STACKTRACE, SEMATTRS_EXCEPTION_TYPE, } from '@opentelemetry/semantic-conventions';\nimport { ExceptionEventName } from './enums';\n/**\n * This class represents a span.\n */\nvar Span = /** @class */ (function () {\n    /**\n     * Constructs a new Span instance.\n     *\n     * @deprecated calling Span constructor directly is not supported. Please use tracer.startSpan.\n     * */\n    function Span(parentTracer, context, spanName, spanContext, kind, parentSpanId, links, startTime, _deprecatedClock, // keeping this argument even though it is unused to ensure backwards compatibility\n    attributes) {\n        if (links === void 0) { links = []; }\n        this.attributes = {};\n        this.links = [];\n        this.events = [];\n        this._droppedAttributesCount = 0;\n        this._droppedEventsCount = 0;\n        this._droppedLinksCount = 0;\n        this.status = {\n            code: SpanStatusCode.UNSET,\n        };\n        this.endTime = [0, 0];\n        this._ended = false;\n        this._duration = [-1, -1];\n        this.name = spanName;\n        this._spanContext = spanContext;\n        this.parentSpanId = parentSpanId;\n        this.kind = kind;\n        this.links = links;\n        var now = Date.now();\n        this._performanceStartTime = otperformance.now();\n        this._performanceOffset =\n            now - (this._performanceStartTime + getTimeOrigin());\n        this._startTimeProvided = startTime != null;\n        this.startTime = this._getTime(startTime !== null && startTime !== void 0 ? startTime : now);\n        this.resource = parentTracer.resource;\n        this.instrumentationLibrary = parentTracer.instrumentationLibrary;\n        this._spanLimits = parentTracer.getSpanLimits();\n        this._attributeValueLengthLimit =\n            this._spanLimits.attributeValueLengthLimit || 0;\n        if (attributes != null) {\n            this.setAttributes(attributes);\n        }\n        this._spanProcessor = parentTracer.getActiveSpanProcessor();\n        this._spanProcessor.onStart(this, context);\n    }\n    Span.prototype.spanContext = function () {\n        return this._spanContext;\n    };\n    Span.prototype.setAttribute = function (key, value) {\n        if (value == null || this._isSpanEnded())\n            return this;\n        if (key.length === 0) {\n            diag.warn(\"Invalid attribute key: \" + key);\n            return this;\n        }\n        if (!isAttributeValue(value)) {\n            diag.warn(\"Invalid attribute value set for key: \" + key);\n            return this;\n        }\n        if (Object.keys(this.attributes).length >=\n            this._spanLimits.attributeCountLimit &&\n            !Object.prototype.hasOwnProperty.call(this.attributes, key)) {\n            this._droppedAttributesCount++;\n            return this;\n        }\n        this.attributes[key] = this._truncateToSize(value);\n        return this;\n    };\n    Span.prototype.setAttributes = function (attributes) {\n        var e_1, _a;\n        try {\n            for (var _b = __values(Object.entries(attributes)), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var _d = __read(_c.value, 2), k = _d[0], v = _d[1];\n                this.setAttribute(k, v);\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        return this;\n    };\n    /**\n     *\n     * @param name Span Name\n     * @param [attributesOrStartTime] Span attributes or start time\n     *     if type is {@type TimeInput} and 3rd param is undefined\n     * @param [timeStamp] Specified time stamp for the event\n     */\n    Span.prototype.addEvent = function (name, attributesOrStartTime, timeStamp) {\n        if (this._isSpanEnded())\n            return this;\n        if (this._spanLimits.eventCountLimit === 0) {\n            diag.warn('No events allowed.');\n            this._droppedEventsCount++;\n            return this;\n        }\n        if (this.events.length >= this._spanLimits.eventCountLimit) {\n            if (this._droppedEventsCount === 0) {\n                diag.debug('Dropping extra events.');\n            }\n            this.events.shift();\n            this._droppedEventsCount++;\n        }\n        if (isTimeInput(attributesOrStartTime)) {\n            if (!isTimeInput(timeStamp)) {\n                timeStamp = attributesOrStartTime;\n            }\n            attributesOrStartTime = undefined;\n        }\n        var attributes = sanitizeAttributes(attributesOrStartTime);\n        this.events.push({\n            name: name,\n            attributes: attributes,\n            time: this._getTime(timeStamp),\n            droppedAttributesCount: 0,\n        });\n        return this;\n    };\n    Span.prototype.addLink = function (link) {\n        this.links.push(link);\n        return this;\n    };\n    Span.prototype.addLinks = function (links) {\n        var _a;\n        (_a = this.links).push.apply(_a, __spreadArray([], __read(links), false));\n        return this;\n    };\n    Span.prototype.setStatus = function (status) {\n        if (this._isSpanEnded())\n            return this;\n        this.status = __assign({}, status);\n        // When using try-catch, the caught \"error\" is of type `any`. When then assigning `any` to `status.message`,\n        // TypeScript will not error. While this can happen during use of any API, it is more common on Span#setStatus()\n        // as it's likely used in a catch-block. Therefore, we validate if `status.message` is actually a string, null, or\n        // undefined to avoid an incorrect type causing issues downstream.\n        if (this.status.message != null && typeof status.message !== 'string') {\n            diag.warn(\"Dropping invalid status.message of type '\" + typeof status.message + \"', expected 'string'\");\n            delete this.status.message;\n        }\n        return this;\n    };\n    Span.prototype.updateName = function (name) {\n        if (this._isSpanEnded())\n            return this;\n        this.name = name;\n        return this;\n    };\n    Span.prototype.end = function (endTime) {\n        if (this._isSpanEnded()) {\n            diag.error(this.name + \" \" + this._spanContext.traceId + \"-\" + this._spanContext.spanId + \" - You can only call end() on a span once.\");\n            return;\n        }\n        this._ended = true;\n        this.endTime = this._getTime(endTime);\n        this._duration = hrTimeDuration(this.startTime, this.endTime);\n        if (this._duration[0] < 0) {\n            diag.warn('Inconsistent start and end time, startTime > endTime. Setting span duration to 0ms.', this.startTime, this.endTime);\n            this.endTime = this.startTime.slice();\n            this._duration = [0, 0];\n        }\n        if (this._droppedEventsCount > 0) {\n            diag.warn(\"Dropped \" + this._droppedEventsCount + \" events because eventCountLimit reached\");\n        }\n        this._spanProcessor.onEnd(this);\n    };\n    Span.prototype._getTime = function (inp) {\n        if (typeof inp === 'number' && inp <= otperformance.now()) {\n            // must be a performance timestamp\n            // apply correction and convert to hrtime\n            return hrTime(inp + this._performanceOffset);\n        }\n        if (typeof inp === 'number') {\n            return millisToHrTime(inp);\n        }\n        if (inp instanceof Date) {\n            return millisToHrTime(inp.getTime());\n        }\n        if (isTimeInputHrTime(inp)) {\n            return inp;\n        }\n        if (this._startTimeProvided) {\n            // if user provided a time for the start manually\n            // we can't use duration to calculate event/end times\n            return millisToHrTime(Date.now());\n        }\n        var msDuration = otperformance.now() - this._performanceStartTime;\n        return addHrTimes(this.startTime, millisToHrTime(msDuration));\n    };\n    Span.prototype.isRecording = function () {\n        return this._ended === false;\n    };\n    Span.prototype.recordException = function (exception, time) {\n        var attributes = {};\n        if (typeof exception === 'string') {\n            attributes[SEMATTRS_EXCEPTION_MESSAGE] = exception;\n        }\n        else if (exception) {\n            if (exception.code) {\n                attributes[SEMATTRS_EXCEPTION_TYPE] = exception.code.toString();\n            }\n            else if (exception.name) {\n                attributes[SEMATTRS_EXCEPTION_TYPE] = exception.name;\n            }\n            if (exception.message) {\n                attributes[SEMATTRS_EXCEPTION_MESSAGE] = exception.message;\n            }\n            if (exception.stack) {\n                attributes[SEMATTRS_EXCEPTION_STACKTRACE] = exception.stack;\n            }\n        }\n        // these are minimum requirements from spec\n        if (attributes[SEMATTRS_EXCEPTION_TYPE] ||\n            attributes[SEMATTRS_EXCEPTION_MESSAGE]) {\n            this.addEvent(ExceptionEventName, attributes, time);\n        }\n        else {\n            diag.warn(\"Failed to record an exception \" + exception);\n        }\n    };\n    Object.defineProperty(Span.prototype, \"duration\", {\n        get: function () {\n            return this._duration;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Span.prototype, \"ended\", {\n        get: function () {\n            return this._ended;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Span.prototype, \"droppedAttributesCount\", {\n        get: function () {\n            return this._droppedAttributesCount;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Span.prototype, \"droppedEventsCount\", {\n        get: function () {\n            return this._droppedEventsCount;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Span.prototype, \"droppedLinksCount\", {\n        get: function () {\n            return this._droppedLinksCount;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Span.prototype._isSpanEnded = function () {\n        if (this._ended) {\n            diag.warn(\"Can not execute the operation on ended Span {traceId: \" + this._spanContext.traceId + \", spanId: \" + this._spanContext.spanId + \"}\");\n        }\n        return this._ended;\n    };\n    // Utility function to truncate given value within size\n    // for value type of string, will truncate to given limit\n    // for type of non-string, will return same value\n    Span.prototype._truncateToLimitUtil = function (value, limit) {\n        if (value.length <= limit) {\n            return value;\n        }\n        return value.substring(0, limit);\n    };\n    /**\n     * If the given attribute value is of type string and has more characters than given {@code attributeValueLengthLimit} then\n     * return string with truncated to {@code attributeValueLengthLimit} characters\n     *\n     * If the given attribute value is array of strings then\n     * return new array of strings with each element truncated to {@code attributeValueLengthLimit} characters\n     *\n     * Otherwise return same Attribute {@code value}\n     *\n     * @param value Attribute value\n     * @returns truncated attribute value if required, otherwise same value\n     */\n    Span.prototype._truncateToSize = function (value) {\n        var _this = this;\n        var limit = this._attributeValueLengthLimit;\n        // Check limit\n        if (limit <= 0) {\n            // Negative values are invalid, so do not truncate\n            diag.warn(\"Attribute value limit must be positive, got \" + limit);\n            return value;\n        }\n        // String\n        if (typeof value === 'string') {\n            return this._truncateToLimitUtil(value, limit);\n        }\n        // Array of strings\n        if (Array.isArray(value)) {\n            return value.map(function (val) {\n                return typeof val === 'string' ? _this._truncateToLimitUtil(val, limit) : val;\n            });\n        }\n        // Other types, no need to apply value length limit\n        return value;\n    };\n    return Span;\n}());\nexport { Span };\n//# sourceMappingURL=Span.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * A sampling decision that determines how a {@link Span} will be recorded\n * and collected.\n */\nexport var SamplingDecision;\n(function (SamplingDecision) {\n    /**\n     * `Span.isRecording() === false`, span will not be recorded and all events\n     * and attributes will be dropped.\n     */\n    SamplingDecision[SamplingDecision[\"NOT_RECORD\"] = 0] = \"NOT_RECORD\";\n    /**\n     * `Span.isRecording() === true`, but `Sampled` flag in {@link TraceFlags}\n     * MUST NOT be set.\n     */\n    SamplingDecision[SamplingDecision[\"RECORD\"] = 1] = \"RECORD\";\n    /**\n     * `Span.isRecording() === true` AND `Sampled` flag in {@link TraceFlags}\n     * MUST be set.\n     */\n    SamplingDecision[SamplingDecision[\"RECORD_AND_SAMPLED\"] = 2] = \"RECORD_AND_SAMPLED\";\n})(SamplingDecision || (SamplingDecision = {}));\n//# sourceMappingURL=Sampler.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { SamplingDecision } from '../Sampler';\n/** Sampler that samples no traces. */\nvar AlwaysOffSampler = /** @class */ (function () {\n    function AlwaysOffSampler() {\n    }\n    AlwaysOffSampler.prototype.shouldSample = function () {\n        return {\n            decision: SamplingDecision.NOT_RECORD,\n        };\n    };\n    AlwaysOffSampler.prototype.toString = function () {\n        return 'AlwaysOffSampler';\n    };\n    return AlwaysOffSampler;\n}());\nexport { AlwaysOffSampler };\n//# sourceMappingURL=AlwaysOffSampler.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { SamplingDecision } from '../Sampler';\n/** Sampler that samples all traces. */\nvar AlwaysOnSampler = /** @class */ (function () {\n    function AlwaysOnSampler() {\n    }\n    AlwaysOnSampler.prototype.shouldSample = function () {\n        return {\n            decision: SamplingDecision.RECORD_AND_SAMPLED,\n        };\n    };\n    AlwaysOnSampler.prototype.toString = function () {\n        return 'AlwaysOnSampler';\n    };\n    return AlwaysOnSampler;\n}());\nexport { AlwaysOnSampler };\n//# sourceMappingURL=AlwaysOnSampler.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { isSpanContextValid, TraceFlags, trace, } from '@opentelemetry/api';\nimport { globalErrorHandler } from '@opentelemetry/core';\nimport { AlwaysOffSampler } from './AlwaysOffSampler';\nimport { AlwaysOnSampler } from './AlwaysOnSampler';\n/**\n * A composite sampler that either respects the parent span's sampling decision\n * or delegates to `delegateSampler` for root spans.\n */\nvar ParentBasedSampler = /** @class */ (function () {\n    function ParentBasedSampler(config) {\n        var _a, _b, _c, _d;\n        this._root = config.root;\n        if (!this._root) {\n            globalErrorHandler(new Error('ParentBasedSampler must have a root sampler configured'));\n            this._root = new AlwaysOnSampler();\n        }\n        this._remoteParentSampled =\n            (_a = config.remoteParentSampled) !== null && _a !== void 0 ? _a : new AlwaysOnSampler();\n        this._remoteParentNotSampled =\n            (_b = config.remoteParentNotSampled) !== null && _b !== void 0 ? _b : new AlwaysOffSampler();\n        this._localParentSampled =\n            (_c = config.localParentSampled) !== null && _c !== void 0 ? _c : new AlwaysOnSampler();\n        this._localParentNotSampled =\n            (_d = config.localParentNotSampled) !== null && _d !== void 0 ? _d : new AlwaysOffSampler();\n    }\n    ParentBasedSampler.prototype.shouldSample = function (context, traceId, spanName, spanKind, attributes, links) {\n        var parentContext = trace.getSpanContext(context);\n        if (!parentContext || !isSpanContextValid(parentContext)) {\n            return this._root.shouldSample(context, traceId, spanName, spanKind, attributes, links);\n        }\n        if (parentContext.isRemote) {\n            if (parentContext.traceFlags & TraceFlags.SAMPLED) {\n                return this._remoteParentSampled.shouldSample(context, traceId, spanName, spanKind, attributes, links);\n            }\n            return this._remoteParentNotSampled.shouldSample(context, traceId, spanName, spanKind, attributes, links);\n        }\n        if (parentContext.traceFlags & TraceFlags.SAMPLED) {\n            return this._localParentSampled.shouldSample(context, traceId, spanName, spanKind, attributes, links);\n        }\n        return this._localParentNotSampled.shouldSample(context, traceId, spanName, spanKind, attributes, links);\n    };\n    ParentBasedSampler.prototype.toString = function () {\n        return \"ParentBased{root=\" + this._root.toString() + \", remoteParentSampled=\" + this._remoteParentSampled.toString() + \", remoteParentNotSampled=\" + this._remoteParentNotSampled.toString() + \", localParentSampled=\" + this._localParentSampled.toString() + \", localParentNotSampled=\" + this._localParentNotSampled.toString() + \"}\";\n    };\n    return ParentBasedSampler;\n}());\nexport { ParentBasedSampler };\n//# sourceMappingURL=ParentBasedSampler.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { isValidTraceId } from '@opentelemetry/api';\nimport { SamplingDecision } from '../Sampler';\n/** Sam<PERSON> that samples a given fraction of traces based of trace id deterministically. */\nvar TraceIdRatioBasedSampler = /** @class */ (function () {\n    function TraceIdRatioBasedSampler(_ratio) {\n        if (_ratio === void 0) { _ratio = 0; }\n        this._ratio = _ratio;\n        this._ratio = this._normalize(_ratio);\n        this._upperBound = Math.floor(this._ratio * 0xffffffff);\n    }\n    TraceIdRatioBasedSampler.prototype.shouldSample = function (context, traceId) {\n        return {\n            decision: isValidTraceId(traceId) && this._accumulate(traceId) < this._upperBound\n                ? SamplingDecision.RECORD_AND_SAMPLED\n                : SamplingDecision.NOT_RECORD,\n        };\n    };\n    TraceIdRatioBasedSampler.prototype.toString = function () {\n        return \"TraceIdRatioBased{\" + this._ratio + \"}\";\n    };\n    TraceIdRatioBasedSampler.prototype._normalize = function (ratio) {\n        if (typeof ratio !== 'number' || isNaN(ratio))\n            return 0;\n        return ratio >= 1 ? 1 : ratio <= 0 ? 0 : ratio;\n    };\n    TraceIdRatioBasedSampler.prototype._accumulate = function (traceId) {\n        var accumulation = 0;\n        for (var i = 0; i < traceId.length / 8; i++) {\n            var pos = i * 8;\n            var part = parseInt(traceId.slice(pos, pos + 8), 16);\n            accumulation = (accumulation ^ part) >>> 0;\n        }\n        return accumulation;\n    };\n    return TraceIdRatioBasedSampler;\n}());\nexport { TraceIdRatioBasedSampler };\n//# sourceMappingURL=TraceIdRatioBasedSampler.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { diag } from '@opentelemetry/api';\nimport { getEnv, TracesSamplerValues } from '@opentelemetry/core';\nimport { AlwaysOffSampler } from './sampler/AlwaysOffSampler';\nimport { AlwaysOnSampler } from './sampler/AlwaysOnSampler';\nimport { ParentBasedSampler } from './sampler/ParentBasedSampler';\nimport { TraceIdRatioBasedSampler } from './sampler/TraceIdRatioBasedSampler';\nvar FALLBACK_OTEL_TRACES_SAMPLER = TracesSamplerValues.AlwaysOn;\nvar DEFAULT_RATIO = 1;\n/**\n * Load default configuration. For fields with primitive values, any user-provided\n * value will override the corresponding default value. For fields with\n * non-primitive values (like `spanLimits`), the user-provided value will be\n * used to extend the default value.\n */\n// object needs to be wrapped in this function and called when needed otherwise\n// envs are parsed before tests are ran - causes tests using these envs to fail\nexport function loadDefaultConfig() {\n    var env = getEnv();\n    return {\n        sampler: buildSamplerFromEnv(env),\n        forceFlushTimeoutMillis: 30000,\n        generalLimits: {\n            attributeValueLengthLimit: env.OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT,\n            attributeCountLimit: env.OTEL_ATTRIBUTE_COUNT_LIMIT,\n        },\n        spanLimits: {\n            attributeValueLengthLimit: env.OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT,\n            attributeCountLimit: env.OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT,\n            linkCountLimit: env.OTEL_SPAN_LINK_COUNT_LIMIT,\n            eventCountLimit: env.OTEL_SPAN_EVENT_COUNT_LIMIT,\n            attributePerEventCountLimit: env.OTEL_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT,\n            attributePerLinkCountLimit: env.OTEL_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT,\n        },\n        mergeResourceWithDefaults: true,\n    };\n}\n/**\n * Based on environment, builds a sampler, complies with specification.\n * @param environment optional, by default uses getEnv(), but allows passing a value to reuse parsed environment\n */\nexport function buildSamplerFromEnv(environment) {\n    if (environment === void 0) { environment = getEnv(); }\n    switch (environment.OTEL_TRACES_SAMPLER) {\n        case TracesSamplerValues.AlwaysOn:\n            return new AlwaysOnSampler();\n        case TracesSamplerValues.AlwaysOff:\n            return new AlwaysOffSampler();\n        case TracesSamplerValues.ParentBasedAlwaysOn:\n            return new ParentBasedSampler({\n                root: new AlwaysOnSampler(),\n            });\n        case TracesSamplerValues.ParentBasedAlwaysOff:\n            return new ParentBasedSampler({\n                root: new AlwaysOffSampler(),\n            });\n        case TracesSamplerValues.TraceIdRatio:\n            return new TraceIdRatioBasedSampler(getSamplerProbabilityFromEnv(environment));\n        case TracesSamplerValues.ParentBasedTraceIdRatio:\n            return new ParentBasedSampler({\n                root: new TraceIdRatioBasedSampler(getSamplerProbabilityFromEnv(environment)),\n            });\n        default:\n            diag.error(\"OTEL_TRACES_SAMPLER value \\\"\" + environment.OTEL_TRACES_SAMPLER + \" invalid, defaulting to \" + FALLBACK_OTEL_TRACES_SAMPLER + \"\\\".\");\n            return new AlwaysOnSampler();\n    }\n}\nfunction getSamplerProbabilityFromEnv(environment) {\n    if (environment.OTEL_TRACES_SAMPLER_ARG === undefined ||\n        environment.OTEL_TRACES_SAMPLER_ARG === '') {\n        diag.error(\"OTEL_TRACES_SAMPLER_ARG is blank, defaulting to \" + DEFAULT_RATIO + \".\");\n        return DEFAULT_RATIO;\n    }\n    var probability = Number(environment.OTEL_TRACES_SAMPLER_ARG);\n    if (isNaN(probability)) {\n        diag.error(\"OTEL_TRACES_SAMPLER_ARG=\" + environment.OTEL_TRACES_SAMPLER_ARG + \" was given, but it is invalid, defaulting to \" + DEFAULT_RATIO + \".\");\n        return DEFAULT_RATIO;\n    }\n    if (probability < 0 || probability > 1) {\n        diag.error(\"OTEL_TRACES_SAMPLER_ARG=\" + environment.OTEL_TRACES_SAMPLER_ARG + \" was given, but it is out of range ([0..1]), defaulting to \" + DEFAULT_RATIO + \".\");\n        return DEFAULT_RATIO;\n    }\n    return probability;\n}\n//# sourceMappingURL=config.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { buildSamplerFromEnv, loadDefaultConfig } from './config';\nimport { DEFAULT_ATTRIBUTE_COUNT_LIMIT, DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT, getEnvWithoutDefaults, } from '@opentelemetry/core';\n/**\n * Function to merge Default configuration (as specified in './config') with\n * user provided configurations.\n */\nexport function mergeConfig(userConfig) {\n    var perInstanceDefaults = {\n        sampler: buildSamplerFromEnv(),\n    };\n    var DEFAULT_CONFIG = loadDefaultConfig();\n    var target = Object.assign({}, DEFAULT_CONFIG, perInstanceDefaults, userConfig);\n    target.generalLimits = Object.assign({}, DEFAULT_CONFIG.generalLimits, userConfig.generalLimits || {});\n    target.spanLimits = Object.assign({}, DEFAULT_CONFIG.spanLimits, userConfig.spanLimits || {});\n    return target;\n}\n/**\n * When general limits are provided and model specific limits are not,\n * configures the model specific limits by using the values from the general ones.\n * @param userConfig User provided tracer configuration\n */\nexport function reconfigureLimits(userConfig) {\n    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m;\n    var spanLimits = Object.assign({}, userConfig.spanLimits);\n    var parsedEnvConfig = getEnvWithoutDefaults();\n    /**\n     * Reassign span attribute count limit to use first non null value defined by user or use default value\n     */\n    spanLimits.attributeCountLimit =\n        (_f = (_e = (_d = (_b = (_a = userConfig.spanLimits) === null || _a === void 0 ? void 0 : _a.attributeCountLimit) !== null && _b !== void 0 ? _b : (_c = userConfig.generalLimits) === null || _c === void 0 ? void 0 : _c.attributeCountLimit) !== null && _d !== void 0 ? _d : parsedEnvConfig.OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT) !== null && _e !== void 0 ? _e : parsedEnvConfig.OTEL_ATTRIBUTE_COUNT_LIMIT) !== null && _f !== void 0 ? _f : DEFAULT_ATTRIBUTE_COUNT_LIMIT;\n    /**\n     * Reassign span attribute value length limit to use first non null value defined by user or use default value\n     */\n    spanLimits.attributeValueLengthLimit =\n        (_m = (_l = (_k = (_h = (_g = userConfig.spanLimits) === null || _g === void 0 ? void 0 : _g.attributeValueLengthLimit) !== null && _h !== void 0 ? _h : (_j = userConfig.generalLimits) === null || _j === void 0 ? void 0 : _j.attributeValueLengthLimit) !== null && _k !== void 0 ? _k : parsedEnvConfig.OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT) !== null && _l !== void 0 ? _l : parsedEnvConfig.OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT) !== null && _m !== void 0 ? _m : DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT;\n    return Object.assign({}, userConfig, { spanLimits: spanLimits });\n}\n//# sourceMappingURL=utility.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { context, diag, TraceFlags } from '@opentelemetry/api';\nimport { BindOnceFuture, ExportResultCode, getEnv, globalErrorHandler, suppressTracing, unrefTimer, } from '@opentelemetry/core';\n/**\n * Implementation of the {@link SpanProcessor} that batches spans exported by\n * the SDK then pushes them to the exporter pipeline.\n */\nvar BatchSpanProcessorBase = /** @class */ (function () {\n    function BatchSpanProcessorBase(_exporter, config) {\n        this._exporter = _exporter;\n        this._isExporting = false;\n        this._finishedSpans = [];\n        this._droppedSpansCount = 0;\n        var env = getEnv();\n        this._maxExportBatchSize =\n            typeof (config === null || config === void 0 ? void 0 : config.maxExportBatchSize) === 'number'\n                ? config.maxExportBatchSize\n                : env.OTEL_BSP_MAX_EXPORT_BATCH_SIZE;\n        this._maxQueueSize =\n            typeof (config === null || config === void 0 ? void 0 : config.maxQueueSize) === 'number'\n                ? config.maxQueueSize\n                : env.OTEL_BSP_MAX_QUEUE_SIZE;\n        this._scheduledDelayMillis =\n            typeof (config === null || config === void 0 ? void 0 : config.scheduledDelayMillis) === 'number'\n                ? config.scheduledDelayMillis\n                : env.OTEL_BSP_SCHEDULE_DELAY;\n        this._exportTimeoutMillis =\n            typeof (config === null || config === void 0 ? void 0 : config.exportTimeoutMillis) === 'number'\n                ? config.exportTimeoutMillis\n                : env.OTEL_BSP_EXPORT_TIMEOUT;\n        this._shutdownOnce = new BindOnceFuture(this._shutdown, this);\n        if (this._maxExportBatchSize > this._maxQueueSize) {\n            diag.warn('BatchSpanProcessor: maxExportBatchSize must be smaller or equal to maxQueueSize, setting maxExportBatchSize to match maxQueueSize');\n            this._maxExportBatchSize = this._maxQueueSize;\n        }\n    }\n    BatchSpanProcessorBase.prototype.forceFlush = function () {\n        if (this._shutdownOnce.isCalled) {\n            return this._shutdownOnce.promise;\n        }\n        return this._flushAll();\n    };\n    // does nothing.\n    BatchSpanProcessorBase.prototype.onStart = function (_span, _parentContext) { };\n    BatchSpanProcessorBase.prototype.onEnd = function (span) {\n        if (this._shutdownOnce.isCalled) {\n            return;\n        }\n        if ((span.spanContext().traceFlags & TraceFlags.SAMPLED) === 0) {\n            return;\n        }\n        this._addToBuffer(span);\n    };\n    BatchSpanProcessorBase.prototype.shutdown = function () {\n        return this._shutdownOnce.call();\n    };\n    BatchSpanProcessorBase.prototype._shutdown = function () {\n        var _this = this;\n        return Promise.resolve()\n            .then(function () {\n            return _this.onShutdown();\n        })\n            .then(function () {\n            return _this._flushAll();\n        })\n            .then(function () {\n            return _this._exporter.shutdown();\n        });\n    };\n    /** Add a span in the buffer. */\n    BatchSpanProcessorBase.prototype._addToBuffer = function (span) {\n        if (this._finishedSpans.length >= this._maxQueueSize) {\n            // limit reached, drop span\n            if (this._droppedSpansCount === 0) {\n                diag.debug('maxQueueSize reached, dropping spans');\n            }\n            this._droppedSpansCount++;\n            return;\n        }\n        if (this._droppedSpansCount > 0) {\n            // some spans were dropped, log once with count of spans dropped\n            diag.warn(\"Dropped \" + this._droppedSpansCount + \" spans because maxQueueSize reached\");\n            this._droppedSpansCount = 0;\n        }\n        this._finishedSpans.push(span);\n        this._maybeStartTimer();\n    };\n    /**\n     * Send all spans to the exporter respecting the batch size limit\n     * This function is used only on forceFlush or shutdown,\n     * for all other cases _flush should be used\n     * */\n    BatchSpanProcessorBase.prototype._flushAll = function () {\n        var _this = this;\n        return new Promise(function (resolve, reject) {\n            var promises = [];\n            // calculate number of batches\n            var count = Math.ceil(_this._finishedSpans.length / _this._maxExportBatchSize);\n            for (var i = 0, j = count; i < j; i++) {\n                promises.push(_this._flushOneBatch());\n            }\n            Promise.all(promises)\n                .then(function () {\n                resolve();\n            })\n                .catch(reject);\n        });\n    };\n    BatchSpanProcessorBase.prototype._flushOneBatch = function () {\n        var _this = this;\n        this._clearTimer();\n        if (this._finishedSpans.length === 0) {\n            return Promise.resolve();\n        }\n        return new Promise(function (resolve, reject) {\n            var timer = setTimeout(function () {\n                // don't wait anymore for export, this way the next batch can start\n                reject(new Error('Timeout'));\n            }, _this._exportTimeoutMillis);\n            // prevent downstream exporter calls from generating spans\n            context.with(suppressTracing(context.active()), function () {\n                // Reset the finished spans buffer here because the next invocations of the _flush method\n                // could pass the same finished spans to the exporter if the buffer is cleared\n                // outside the execution of this callback.\n                var spans;\n                if (_this._finishedSpans.length <= _this._maxExportBatchSize) {\n                    spans = _this._finishedSpans;\n                    _this._finishedSpans = [];\n                }\n                else {\n                    spans = _this._finishedSpans.splice(0, _this._maxExportBatchSize);\n                }\n                var doExport = function () {\n                    return _this._exporter.export(spans, function (result) {\n                        var _a;\n                        clearTimeout(timer);\n                        if (result.code === ExportResultCode.SUCCESS) {\n                            resolve();\n                        }\n                        else {\n                            reject((_a = result.error) !== null && _a !== void 0 ? _a : new Error('BatchSpanProcessor: span export failed'));\n                        }\n                    });\n                };\n                var pendingResources = null;\n                for (var i = 0, len = spans.length; i < len; i++) {\n                    var span = spans[i];\n                    if (span.resource.asyncAttributesPending &&\n                        span.resource.waitForAsyncAttributes) {\n                        pendingResources !== null && pendingResources !== void 0 ? pendingResources : (pendingResources = []);\n                        pendingResources.push(span.resource.waitForAsyncAttributes());\n                    }\n                }\n                // Avoid scheduling a promise to make the behavior more predictable and easier to test\n                if (pendingResources === null) {\n                    doExport();\n                }\n                else {\n                    Promise.all(pendingResources).then(doExport, function (err) {\n                        globalErrorHandler(err);\n                        reject(err);\n                    });\n                }\n            });\n        });\n    };\n    BatchSpanProcessorBase.prototype._maybeStartTimer = function () {\n        var _this = this;\n        if (this._isExporting)\n            return;\n        var flush = function () {\n            _this._isExporting = true;\n            _this._flushOneBatch()\n                .finally(function () {\n                _this._isExporting = false;\n                if (_this._finishedSpans.length > 0) {\n                    _this._clearTimer();\n                    _this._maybeStartTimer();\n                }\n            })\n                .catch(function (e) {\n                _this._isExporting = false;\n                globalErrorHandler(e);\n            });\n        };\n        // we only wait if the queue doesn't have enough elements yet\n        if (this._finishedSpans.length >= this._maxExportBatchSize) {\n            return flush();\n        }\n        if (this._timer !== undefined)\n            return;\n        this._timer = setTimeout(function () { return flush(); }, this._scheduledDelayMillis);\n        unrefTimer(this._timer);\n    };\n    BatchSpanProcessorBase.prototype._clearTimer = function () {\n        if (this._timer !== undefined) {\n            clearTimeout(this._timer);\n            this._timer = undefined;\n        }\n    };\n    return BatchSpanProcessorBase;\n}());\nexport { BatchSpanProcessorBase };\n//# sourceMappingURL=BatchSpanProcessorBase.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport { BatchSpanProcessorBase } from '../../../export/BatchSpanProcessorBase';\nvar BatchSpanProcessor = /** @class */ (function (_super) {\n    __extends(BatchSpanProcessor, _super);\n    function BatchSpanProcessor() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    BatchSpanProcessor.prototype.onShutdown = function () { };\n    return BatchSpanProcessor;\n}(BatchSpanProcessorBase));\nexport { BatchSpanProcessor };\n//# sourceMappingURL=BatchSpanProcessor.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar SPAN_ID_BYTES = 8;\nvar TRACE_ID_BYTES = 16;\nvar RandomIdGenerator = /** @class */ (function () {\n    function RandomIdGenerator() {\n        /**\n         * Returns a random 16-byte trace ID formatted/encoded as a 32 lowercase hex\n         * characters corresponding to 128 bits.\n         */\n        this.generateTraceId = getIdGenerator(TRACE_ID_BYTES);\n        /**\n         * Returns a random 8-byte span ID formatted/encoded as a 16 lowercase hex\n         * characters corresponding to 64 bits.\n         */\n        this.generateSpanId = getIdGenerator(SPAN_ID_BYTES);\n    }\n    return RandomIdGenerator;\n}());\nexport { RandomIdGenerator };\nvar SHARED_BUFFER = Buffer.allocUnsafe(TRACE_ID_BYTES);\nfunction getIdGenerator(bytes) {\n    return function generateId() {\n        for (var i = 0; i < bytes / 4; i++) {\n            // unsigned right shift drops decimal part of the number\n            // it is required because if a number between 2**32 and 2**32 - 1 is generated, an out of range error is thrown by writeUInt32BE\n            SHARED_BUFFER.writeUInt32BE((Math.random() * Math.pow(2, 32)) >>> 0, i * 4);\n        }\n        // If buffer is all 0, set the last byte to 1 to guarantee a valid w3c id is generated\n        for (var i = 0; i < bytes; i++) {\n            if (SHARED_BUFFER[i] > 0) {\n                break;\n            }\n            else if (i === bytes - 1) {\n                SHARED_BUFFER[bytes - 1] = 1;\n            }\n        }\n        return SHARED_BUFFER.toString('hex', 0, bytes);\n    };\n}\n//# sourceMappingURL=RandomIdGenerator.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport * as api from '@opentelemetry/api';\nimport { sanitizeAttributes, isTracingSuppressed, } from '@opentelemetry/core';\nimport { Span } from './Span';\nimport { mergeConfig } from './utility';\nimport { RandomIdGenerator } from './platform';\n/**\n * This class represents a basic tracer.\n */\nvar Tracer = /** @class */ (function () {\n    /**\n     * Constructs a new Tracer instance.\n     */\n    function Tracer(instrumentationLibrary, config, _tracerProvider) {\n        this._tracerProvider = _tracerProvider;\n        var localConfig = mergeConfig(config);\n        this._sampler = localConfig.sampler;\n        this._generalLimits = localConfig.generalLimits;\n        this._spanLimits = localConfig.spanLimits;\n        this._idGenerator = config.idGenerator || new RandomIdGenerator();\n        this.resource = _tracerProvider.resource;\n        this.instrumentationLibrary = instrumentationLibrary;\n    }\n    /**\n     * Starts a new Span or returns the default NoopSpan based on the sampling\n     * decision.\n     */\n    Tracer.prototype.startSpan = function (name, options, context) {\n        var _a, _b, _c;\n        if (options === void 0) { options = {}; }\n        if (context === void 0) { context = api.context.active(); }\n        // remove span from context in case a root span is requested via options\n        if (options.root) {\n            context = api.trace.deleteSpan(context);\n        }\n        var parentSpan = api.trace.getSpan(context);\n        if (isTracingSuppressed(context)) {\n            api.diag.debug('Instrumentation suppressed, returning Noop Span');\n            var nonRecordingSpan = api.trace.wrapSpanContext(api.INVALID_SPAN_CONTEXT);\n            return nonRecordingSpan;\n        }\n        var parentSpanContext = parentSpan === null || parentSpan === void 0 ? void 0 : parentSpan.spanContext();\n        var spanId = this._idGenerator.generateSpanId();\n        var traceId;\n        var traceState;\n        var parentSpanId;\n        if (!parentSpanContext ||\n            !api.trace.isSpanContextValid(parentSpanContext)) {\n            // New root span.\n            traceId = this._idGenerator.generateTraceId();\n        }\n        else {\n            // New child span.\n            traceId = parentSpanContext.traceId;\n            traceState = parentSpanContext.traceState;\n            parentSpanId = parentSpanContext.spanId;\n        }\n        var spanKind = (_a = options.kind) !== null && _a !== void 0 ? _a : api.SpanKind.INTERNAL;\n        var links = ((_b = options.links) !== null && _b !== void 0 ? _b : []).map(function (link) {\n            return {\n                context: link.context,\n                attributes: sanitizeAttributes(link.attributes),\n            };\n        });\n        var attributes = sanitizeAttributes(options.attributes);\n        // make sampling decision\n        var samplingResult = this._sampler.shouldSample(context, traceId, name, spanKind, attributes, links);\n        traceState = (_c = samplingResult.traceState) !== null && _c !== void 0 ? _c : traceState;\n        var traceFlags = samplingResult.decision === api.SamplingDecision.RECORD_AND_SAMPLED\n            ? api.TraceFlags.SAMPLED\n            : api.TraceFlags.NONE;\n        var spanContext = { traceId: traceId, spanId: spanId, traceFlags: traceFlags, traceState: traceState };\n        if (samplingResult.decision === api.SamplingDecision.NOT_RECORD) {\n            api.diag.debug('Recording is off, propagating context in a non-recording span');\n            var nonRecordingSpan = api.trace.wrapSpanContext(spanContext);\n            return nonRecordingSpan;\n        }\n        // Set initial span attributes. The attributes object may have been mutated\n        // by the sampler, so we sanitize the merged attributes before setting them.\n        var initAttributes = sanitizeAttributes(Object.assign(attributes, samplingResult.attributes));\n        var span = new Span(this, context, name, spanContext, spanKind, parentSpanId, links, options.startTime, undefined, initAttributes);\n        return span;\n    };\n    Tracer.prototype.startActiveSpan = function (name, arg2, arg3, arg4) {\n        var opts;\n        var ctx;\n        var fn;\n        if (arguments.length < 2) {\n            return;\n        }\n        else if (arguments.length === 2) {\n            fn = arg2;\n        }\n        else if (arguments.length === 3) {\n            opts = arg2;\n            fn = arg3;\n        }\n        else {\n            opts = arg2;\n            ctx = arg3;\n            fn = arg4;\n        }\n        var parentContext = ctx !== null && ctx !== void 0 ? ctx : api.context.active();\n        var span = this.startSpan(name, opts, parentContext);\n        var contextWithSpanSet = api.trace.setSpan(parentContext, span);\n        return api.context.with(contextWithSpanSet, fn, undefined, span);\n    };\n    /** Returns the active {@link GeneralLimits}. */\n    Tracer.prototype.getGeneralLimits = function () {\n        return this._generalLimits;\n    };\n    /** Returns the active {@link SpanLimits}. */\n    Tracer.prototype.getSpanLimits = function () {\n        return this._spanLimits;\n    };\n    Tracer.prototype.getActiveSpanProcessor = function () {\n        return this._tracerProvider.getActiveSpanProcessor();\n    };\n    return Tracer;\n}());\nexport { Tracer };\n//# sourceMappingURL=Tracer.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport { globalErrorHandler } from '@opentelemetry/core';\n/**\n * Implementation of the {@link SpanProcessor} that simply forwards all\n * received events to a list of {@link SpanProcessor}s.\n */\nvar MultiSpanProcessor = /** @class */ (function () {\n    function MultiSpanProcessor(_spanProcessors) {\n        this._spanProcessors = _spanProcessors;\n    }\n    MultiSpanProcessor.prototype.forceFlush = function () {\n        var e_1, _a;\n        var promises = [];\n        try {\n            for (var _b = __values(this._spanProcessors), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var spanProcessor = _c.value;\n                promises.push(spanProcessor.forceFlush());\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        return new Promise(function (resolve) {\n            Promise.all(promises)\n                .then(function () {\n                resolve();\n            })\n                .catch(function (error) {\n                globalErrorHandler(error || new Error('MultiSpanProcessor: forceFlush failed'));\n                resolve();\n            });\n        });\n    };\n    MultiSpanProcessor.prototype.onStart = function (span, context) {\n        var e_2, _a;\n        try {\n            for (var _b = __values(this._spanProcessors), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var spanProcessor = _c.value;\n                spanProcessor.onStart(span, context);\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n    };\n    MultiSpanProcessor.prototype.onEnd = function (span) {\n        var e_3, _a;\n        try {\n            for (var _b = __values(this._spanProcessors), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var spanProcessor = _c.value;\n                spanProcessor.onEnd(span);\n            }\n        }\n        catch (e_3_1) { e_3 = { error: e_3_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_3) throw e_3.error; }\n        }\n    };\n    MultiSpanProcessor.prototype.shutdown = function () {\n        var e_4, _a;\n        var promises = [];\n        try {\n            for (var _b = __values(this._spanProcessors), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var spanProcessor = _c.value;\n                promises.push(spanProcessor.shutdown());\n            }\n        }\n        catch (e_4_1) { e_4 = { error: e_4_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_4) throw e_4.error; }\n        }\n        return new Promise(function (resolve, reject) {\n            Promise.all(promises).then(function () {\n                resolve();\n            }, reject);\n        });\n    };\n    return MultiSpanProcessor;\n}());\nexport { MultiSpanProcessor };\n//# sourceMappingURL=MultiSpanProcessor.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/** No-op implementation of SpanProcessor */\nvar NoopSpanProcessor = /** @class */ (function () {\n    function NoopSpanProcessor() {\n    }\n    NoopSpanProcessor.prototype.onStart = function (_span, _context) { };\n    NoopSpanProcessor.prototype.onEnd = function (_span) { };\n    NoopSpanProcessor.prototype.shutdown = function () {\n        return Promise.resolve();\n    };\n    NoopSpanProcessor.prototype.forceFlush = function () {\n        return Promise.resolve();\n    };\n    return NoopSpanProcessor;\n}());\nexport { NoopSpanProcessor };\n//# sourceMappingURL=NoopSpanProcessor.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nimport { context, diag, propagation, trace, } from '@opentelemetry/api';\nimport { CompositePropagator, W3CBaggagePropagator, W3CTraceContextPropagator, getEnv, merge, } from '@opentelemetry/core';\nimport { Resource } from '@opentelemetry/resources';\nimport { Tracer } from './Tracer';\nimport { loadDefaultConfig } from './config';\nimport { MultiSpanProcessor } from './MultiSpanProcessor';\nimport { NoopSpanProcessor } from './export/NoopSpanProcessor';\nimport { BatchSpanProcessor } from './platform';\nimport { reconfigureLimits } from './utility';\nexport var ForceFlushState;\n(function (ForceFlushState) {\n    ForceFlushState[ForceFlushState[\"resolved\"] = 0] = \"resolved\";\n    ForceFlushState[ForceFlushState[\"timeout\"] = 1] = \"timeout\";\n    ForceFlushState[ForceFlushState[\"error\"] = 2] = \"error\";\n    ForceFlushState[ForceFlushState[\"unresolved\"] = 3] = \"unresolved\";\n})(ForceFlushState || (ForceFlushState = {}));\n/**\n * This class represents a basic tracer provider which platform libraries can extend\n */\nvar BasicTracerProvider = /** @class */ (function () {\n    function BasicTracerProvider(config) {\n        if (config === void 0) { config = {}; }\n        var _a, _b;\n        this._registeredSpanProcessors = [];\n        this._tracers = new Map();\n        var mergedConfig = merge({}, loadDefaultConfig(), reconfigureLimits(config));\n        this.resource = (_a = mergedConfig.resource) !== null && _a !== void 0 ? _a : Resource.empty();\n        if (mergedConfig.mergeResourceWithDefaults) {\n            this.resource = Resource.default().merge(this.resource);\n        }\n        this._config = Object.assign({}, mergedConfig, {\n            resource: this.resource,\n        });\n        if ((_b = config.spanProcessors) === null || _b === void 0 ? void 0 : _b.length) {\n            this._registeredSpanProcessors = __spreadArray([], __read(config.spanProcessors), false);\n            this.activeSpanProcessor = new MultiSpanProcessor(this._registeredSpanProcessors);\n        }\n        else {\n            var defaultExporter = this._buildExporterFromEnv();\n            if (defaultExporter !== undefined) {\n                var batchProcessor = new BatchSpanProcessor(defaultExporter);\n                this.activeSpanProcessor = batchProcessor;\n            }\n            else {\n                this.activeSpanProcessor = new NoopSpanProcessor();\n            }\n        }\n    }\n    BasicTracerProvider.prototype.getTracer = function (name, version, options) {\n        var key = name + \"@\" + (version || '') + \":\" + ((options === null || options === void 0 ? void 0 : options.schemaUrl) || '');\n        if (!this._tracers.has(key)) {\n            this._tracers.set(key, new Tracer({ name: name, version: version, schemaUrl: options === null || options === void 0 ? void 0 : options.schemaUrl }, this._config, this));\n        }\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        return this._tracers.get(key);\n    };\n    /**\n     * @deprecated please use {@link TracerConfig} spanProcessors property\n     * Adds a new {@link SpanProcessor} to this tracer.\n     * @param spanProcessor the new SpanProcessor to be added.\n     */\n    BasicTracerProvider.prototype.addSpanProcessor = function (spanProcessor) {\n        if (this._registeredSpanProcessors.length === 0) {\n            // since we might have enabled by default a batchProcessor, we disable it\n            // before adding the new one\n            this.activeSpanProcessor\n                .shutdown()\n                .catch(function (err) {\n                return diag.error('Error while trying to shutdown current span processor', err);\n            });\n        }\n        this._registeredSpanProcessors.push(spanProcessor);\n        this.activeSpanProcessor = new MultiSpanProcessor(this._registeredSpanProcessors);\n    };\n    BasicTracerProvider.prototype.getActiveSpanProcessor = function () {\n        return this.activeSpanProcessor;\n    };\n    /**\n     * Register this TracerProvider for use with the OpenTelemetry API.\n     * Undefined values may be replaced with defaults, and\n     * null values will be skipped.\n     *\n     * @param config Configuration object for SDK registration\n     */\n    BasicTracerProvider.prototype.register = function (config) {\n        if (config === void 0) { config = {}; }\n        trace.setGlobalTracerProvider(this);\n        if (config.propagator === undefined) {\n            config.propagator = this._buildPropagatorFromEnv();\n        }\n        if (config.contextManager) {\n            context.setGlobalContextManager(config.contextManager);\n        }\n        if (config.propagator) {\n            propagation.setGlobalPropagator(config.propagator);\n        }\n    };\n    BasicTracerProvider.prototype.forceFlush = function () {\n        var timeout = this._config.forceFlushTimeoutMillis;\n        var promises = this._registeredSpanProcessors.map(function (spanProcessor) {\n            return new Promise(function (resolve) {\n                var state;\n                var timeoutInterval = setTimeout(function () {\n                    resolve(new Error(\"Span processor did not completed within timeout period of \" + timeout + \" ms\"));\n                    state = ForceFlushState.timeout;\n                }, timeout);\n                spanProcessor\n                    .forceFlush()\n                    .then(function () {\n                    clearTimeout(timeoutInterval);\n                    if (state !== ForceFlushState.timeout) {\n                        state = ForceFlushState.resolved;\n                        resolve(state);\n                    }\n                })\n                    .catch(function (error) {\n                    clearTimeout(timeoutInterval);\n                    state = ForceFlushState.error;\n                    resolve(error);\n                });\n            });\n        });\n        return new Promise(function (resolve, reject) {\n            Promise.all(promises)\n                .then(function (results) {\n                var errors = results.filter(function (result) { return result !== ForceFlushState.resolved; });\n                if (errors.length > 0) {\n                    reject(errors);\n                }\n                else {\n                    resolve();\n                }\n            })\n                .catch(function (error) { return reject([error]); });\n        });\n    };\n    BasicTracerProvider.prototype.shutdown = function () {\n        return this.activeSpanProcessor.shutdown();\n    };\n    /**\n     * TS cannot yet infer the type of this.constructor:\n     * https://github.com/Microsoft/TypeScript/issues/3841#issuecomment-337560146\n     * There is no need to override either of the getters in your child class.\n     * The type of the registered component maps should be the same across all\n     * classes in the inheritance tree.\n     */\n    BasicTracerProvider.prototype._getPropagator = function (name) {\n        var _a;\n        return (_a = this.constructor._registeredPropagators.get(name)) === null || _a === void 0 ? void 0 : _a();\n    };\n    BasicTracerProvider.prototype._getSpanExporter = function (name) {\n        var _a;\n        return (_a = this.constructor._registeredExporters.get(name)) === null || _a === void 0 ? void 0 : _a();\n    };\n    BasicTracerProvider.prototype._buildPropagatorFromEnv = function () {\n        var _this = this;\n        // per spec, propagators from env must be deduplicated\n        var uniquePropagatorNames = Array.from(new Set(getEnv().OTEL_PROPAGATORS));\n        var propagators = uniquePropagatorNames.map(function (name) {\n            var propagator = _this._getPropagator(name);\n            if (!propagator) {\n                diag.warn(\"Propagator \\\"\" + name + \"\\\" requested through environment variable is unavailable.\");\n            }\n            return propagator;\n        });\n        var validPropagators = propagators.reduce(function (list, item) {\n            if (item) {\n                list.push(item);\n            }\n            return list;\n        }, []);\n        if (validPropagators.length === 0) {\n            return;\n        }\n        else if (uniquePropagatorNames.length === 1) {\n            return validPropagators[0];\n        }\n        else {\n            return new CompositePropagator({\n                propagators: validPropagators,\n            });\n        }\n    };\n    BasicTracerProvider.prototype._buildExporterFromEnv = function () {\n        var exporterName = getEnv().OTEL_TRACES_EXPORTER;\n        if (exporterName === 'none' || exporterName === '')\n            return;\n        var exporter = this._getSpanExporter(exporterName);\n        if (!exporter) {\n            diag.error(\"Exporter \\\"\" + exporterName + \"\\\" requested through environment variable is unavailable.\");\n        }\n        return exporter;\n    };\n    BasicTracerProvider._registeredPropagators = new Map([\n        ['tracecontext', function () { return new W3CTraceContextPropagator(); }],\n        ['baggage', function () { return new W3CBaggagePropagator(); }],\n    ]);\n    BasicTracerProvider._registeredExporters = new Map();\n    return BasicTracerProvider;\n}());\nexport { BasicTracerProvider };\n//# sourceMappingURL=BasicTracerProvider.js.map", "declare const __DEBUG_BUILD__: boolean;\n\n/**\n * This serves as a build time flag that will be true by default, but false in non-debug builds or if users replace `__SENTRY_DEBUG__` in their generated code.\n *\n * ATTENTION: This constant must never cross package boundaries (i.e. be exported) to guarantee that it can be used for tree shaking.\n */\nexport const DEBUG_BUILD = __DEBUG_BUILD__;\n", "import type {\n  Client,\n  FetchBreadcrumbData,\n  FetchBreadcrumbHint,\n  HandlerDataFetch,\n  IntegrationFn,\n  Span,\n} from '@sentry/core';\nimport {\n  addBreadcrumb,\n  addFetchInstrumentationHandler,\n  defineIntegration,\n  getBreadcrumbLogLevelFromHttpStatusCode,\n  getClient,\n  instrumentFetchRequest,\n  isSentryRequestUrl,\n  LRUMap,\n  stringMatchesSomePattern,\n} from '@sentry/core';\n\nconst INTEGRATION_NAME = 'WinterCGFetch';\n\nconst HAS_CLIENT_MAP = new WeakMap<Client, boolean>();\n\nexport interface Options {\n  /**\n   * Whether breadcrumbs should be recorded for requests\n   * Defaults to true\n   */\n  breadcrumbs: boolean;\n\n  /**\n   * Function determining whether or not to create spans to track outgoing requests to the given URL.\n   * By default, spans will be created for all outgoing requests.\n   */\n  shouldCreateSpanForRequest?: (url: string) => boolean;\n}\n\nconst _winterCGFetch = ((options: Partial<Options> = {}) => {\n  const breadcrumbs = options.breadcrumbs === undefined ? true : options.breadcrumbs;\n  const shouldCreateSpanForRequest = options.shouldCreateSpanForRequest;\n\n  const _createSpanUrlMap = new LRUMap<string, boolean>(100);\n  const _headersUrlMap = new LRUMap<string, boolean>(100);\n\n  const spans: Record<string, Span> = {};\n\n  /** Decides whether to attach trace data to the outgoing fetch request */\n  function _shouldAttachTraceData(url: string): boolean {\n    const client = getClient();\n\n    if (!client) {\n      return false;\n    }\n\n    const clientOptions = client.getOptions();\n\n    if (clientOptions.tracePropagationTargets === undefined) {\n      return true;\n    }\n\n    const cachedDecision = _headersUrlMap.get(url);\n    if (cachedDecision !== undefined) {\n      return cachedDecision;\n    }\n\n    const decision = stringMatchesSomePattern(url, clientOptions.tracePropagationTargets);\n    _headersUrlMap.set(url, decision);\n    return decision;\n  }\n\n  /** Helper that wraps shouldCreateSpanForRequest option */\n  function _shouldCreateSpan(url: string): boolean {\n    if (shouldCreateSpanForRequest === undefined) {\n      return true;\n    }\n\n    const cachedDecision = _createSpanUrlMap.get(url);\n    if (cachedDecision !== undefined) {\n      return cachedDecision;\n    }\n\n    const decision = shouldCreateSpanForRequest(url);\n    _createSpanUrlMap.set(url, decision);\n    return decision;\n  }\n\n  return {\n    name: INTEGRATION_NAME,\n    setupOnce() {\n      addFetchInstrumentationHandler(handlerData => {\n        const client = getClient();\n        if (!client || !HAS_CLIENT_MAP.get(client)) {\n          return;\n        }\n\n        if (isSentryRequestUrl(handlerData.fetchData.url, client)) {\n          return;\n        }\n\n        instrumentFetchRequest(\n          handlerData,\n          _shouldCreateSpan,\n          _shouldAttachTraceData,\n          spans,\n          'auto.http.wintercg_fetch',\n        );\n\n        if (breadcrumbs) {\n          createBreadcrumb(handlerData);\n        }\n      });\n    },\n    setup(client) {\n      HAS_CLIENT_MAP.set(client, true);\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Creates spans and attaches tracing headers to fetch requests on WinterCG runtimes.\n */\nexport const winterCGFetchIntegration = defineIntegration(_winterCGFetch);\n\nfunction createBreadcrumb(handlerData: HandlerDataFetch): void {\n  const { startTimestamp, endTimestamp } = handlerData;\n\n  // We only capture complete fetch requests\n  if (!endTimestamp) {\n    return;\n  }\n\n  const breadcrumbData: FetchBreadcrumbData = {\n    method: handlerData.fetchData.method,\n    url: handlerData.fetchData.url,\n  };\n\n  if (handlerData.error) {\n    const hint: FetchBreadcrumbHint = {\n      data: handlerData.error,\n      input: handlerData.args,\n      startTimestamp,\n      endTimestamp,\n    };\n\n    addBreadcrumb(\n      {\n        category: 'fetch',\n        data: breadcrumbData,\n        level: 'error',\n        type: 'http',\n      },\n      hint,\n    );\n  } else {\n    const response = handlerData.response as Response | undefined;\n\n    breadcrumbData.request_body_size = handlerData.fetchData.request_body_size;\n    breadcrumbData.response_body_size = handlerData.fetchData.response_body_size;\n    breadcrumbData.status_code = response?.status;\n\n    const hint: FetchBreadcrumbHint = {\n      input: handlerData.args,\n      response,\n      startTimestamp,\n      endTimestamp,\n    };\n    const level = getBreadcrumbLogLevelFromHttpStatusCode(breadcrumbData.status_code);\n\n    addBreadcrumb(\n      {\n        category: 'fetch',\n        data: breadcrumbData,\n        type: 'http',\n        level,\n      },\n      hint,\n    );\n  }\n}\n", "import type { BaseTransportOptions, Transport, TransportMakeRequestResponse, TransportRequest } from '@sentry/core';\nimport { createTransport, SENTRY_BUFFER_FULL_ERROR, suppressTracing } from '@sentry/core';\n\nexport interface VercelEdgeTransportOptions extends BaseTransportOptions {\n  /** Fetch API init parameters. */\n  fetchOptions?: RequestInit;\n  /** Custom headers for the transport. */\n  headers?: { [key: string]: string };\n}\n\nconst DEFAULT_TRANSPORT_BUFFER_SIZE = 30;\n\n/**\n * This is a modified promise buffer that collects tasks until drain is called.\n * We need this in the edge runtime because edge function invocations may not share I/O objects, like fetch requests\n * and responses, and the normal PromiseBuffer inherently buffers stuff inbetween incoming requests.\n *\n * A limitation we need to be aware of is that DEFAULT_TRANSPORT_BUFFER_SIZE is the maximum amount of payloads the\n * SDK can send for a given edge function invocation.\n */\nexport class IsolatedPromiseBuffer {\n  // We just have this field because the promise buffer interface requires it.\n  // If we ever remove it from the interface we should also remove it here.\n  public $: Array<PromiseLike<TransportMakeRequestResponse>>;\n\n  private _taskProducers: (() => PromiseLike<TransportMakeRequestResponse>)[];\n\n  private readonly _bufferSize: number;\n\n  public constructor(_bufferSize = DEFAULT_TRANSPORT_BUFFER_SIZE) {\n    this.$ = [];\n    this._taskProducers = [];\n    this._bufferSize = _bufferSize;\n  }\n\n  /**\n   * @inheritdoc\n   */\n  public add(taskProducer: () => PromiseLike<TransportMakeRequestResponse>): PromiseLike<TransportMakeRequestResponse> {\n    if (this._taskProducers.length >= this._bufferSize) {\n      return Promise.reject(SENTRY_BUFFER_FULL_ERROR);\n    }\n\n    this._taskProducers.push(taskProducer);\n    return Promise.resolve({});\n  }\n\n  /**\n   * @inheritdoc\n   */\n  public drain(timeout?: number): PromiseLike<boolean> {\n    const oldTaskProducers = [...this._taskProducers];\n    this._taskProducers = [];\n\n    return new Promise(resolve => {\n      const timer = setTimeout(() => {\n        if (timeout && timeout > 0) {\n          resolve(false);\n        }\n      }, timeout);\n\n      // This cannot reject\n      // eslint-disable-next-line @typescript-eslint/no-floating-promises\n      Promise.all(\n        oldTaskProducers.map(taskProducer =>\n          taskProducer().then(null, () => {\n            // catch all failed requests\n          }),\n        ),\n      ).then(() => {\n        // resolve to true if all fetch requests settled\n        clearTimeout(timer);\n        resolve(true);\n      });\n    });\n  }\n}\n\n/**\n * Creates a Transport that uses the Edge Runtimes native fetch API to send events to Sentry.\n */\nexport function makeEdgeTransport(options: VercelEdgeTransportOptions): Transport {\n  function makeRequest(request: TransportRequest): PromiseLike<TransportMakeRequestResponse> {\n    const requestOptions: RequestInit = {\n      body: request.body,\n      method: 'POST',\n      headers: options.headers,\n      ...options.fetchOptions,\n    };\n\n    return suppressTracing(() => {\n      return fetch(options.url, requestOptions).then(response => {\n        return {\n          statusCode: response.status,\n          headers: {\n            'x-sentry-rate-limits': response.headers.get('X-Sentry-Rate-Limits'),\n            'retry-after': response.headers.get('Retry-After'),\n          },\n        };\n      });\n    });\n  }\n\n  return createTransport(options, makeRequest, new IsolatedPromiseBuffer(options.bufferSize));\n}\n", "declare const process: {\n  env: Record<string, string>;\n};\n\n/**\n * Returns an environment setting value determined by Vercel's `VERCEL_ENV` environment variable.\n *\n * @param isClient Flag to indicate whether to use the `NEXT_PUBLIC_` prefixed version of the environment variable.\n */\nexport function getVercelEnv(isClient: boolean): string | undefined {\n  const vercelEnvVar = isClient ? process.env.NEXT_PUBLIC_VERCEL_ENV : process.env.VERCEL_ENV;\n  return vercelEnvVar ? `vercel-${vercelEnvVar}` : undefined;\n}\n", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * NOTICE from the Sentry authors:\n * - Code vendored from: https://github.com/open-telemetry/opentelemetry-js/blob/6515ed8098333646a63a74a8c0150cc2daf520db/packages/opentelemetry-context-async-hooks/src/AbstractAsyncHooksContextManager.ts\n * - Modifications:\n *   - Added lint rules\n *   - Modified bind() method not to rely on Node.js specific APIs\n */\n\n/* eslint-disable @typescript-eslint/explicit-member-accessibility */\n/* eslint-disable @typescript-eslint/member-ordering */\n/* eslint-disable jsdoc/require-jsdoc */\n/* eslint-disable @typescript-eslint/ban-types */\n/* eslint-disable @typescript-eslint/explicit-function-return-type */\n/* eslint-disable @typescript-eslint/no-unsafe-member-access */\n/* eslint-disable prefer-rest-params */\n/* eslint-disable @typescript-eslint/no-dynamic-delete */\n/* eslint-disable @typescript-eslint/unbound-method */\n/* eslint-disable @typescript-eslint/no-this-alias */\n\nimport type { Context, ContextManager } from '@opentelemetry/api';\nimport type { EventEmitter } from 'events';\n\ntype Func<T> = (...args: unknown[]) => T;\n\n/**\n * Store a map for each event of all original listeners and their \"patched\"\n * version. So when a listener is removed by the user, the corresponding\n * patched function will be also removed.\n */\ninterface PatchMap {\n  [name: string]: WeakMap<Func<void>, Func<void>>;\n}\n\nconst ADD_LISTENER_METHODS = [\n  'addListener' as const,\n  'on' as const,\n  'once' as const,\n  'prependListener' as const,\n  'prependOnceListener' as const,\n];\n\nexport abstract class AbstractAsyncHooksContextManager implements ContextManager {\n  abstract active(): Context;\n\n  abstract with<A extends unknown[], F extends (...args: A) => ReturnType<F>>(\n    context: Context,\n    fn: F,\n    thisArg?: ThisParameterType<F>,\n    ...args: A\n  ): ReturnType<F>;\n\n  abstract enable(): this;\n\n  abstract disable(): this;\n\n  /**\n   * Binds a the certain context or the active one to the target function and then returns the target\n   * @param context A context (span) to be bind to target\n   * @param target a function or event emitter. When target or one of its callbacks is called,\n   *  the provided context will be used as the active context for the duration of the call.\n   */\n  bind<T>(context: Context, target: T): T {\n    if (typeof target === 'object' && target !== null && 'on' in target) {\n      return this._bindEventEmitter(context, target as unknown as EventEmitter) as T;\n    }\n\n    if (typeof target === 'function') {\n      return this._bindFunction(context, target);\n    }\n    return target;\n  }\n\n  private _bindFunction<T extends Function>(context: Context, target: T): T {\n    const manager = this;\n    const contextWrapper = function (this: never, ...args: unknown[]) {\n      return manager.with(context, () => target.apply(this, args));\n    };\n    Object.defineProperty(contextWrapper, 'length', {\n      enumerable: false,\n      configurable: true,\n      writable: false,\n      value: target.length,\n    });\n    /**\n     * It isn't possible to tell Typescript that contextWrapper is the same as T\n     * so we forced to cast as any here.\n     */\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    return contextWrapper as any;\n  }\n\n  /**\n   * By default, EventEmitter call their callback with their context, which we do\n   * not want, instead we will bind a specific context to all callbacks that\n   * go through it.\n   * @param context the context we want to bind\n   * @param ee EventEmitter an instance of EventEmitter to patch\n   */\n  private _bindEventEmitter<T extends EventEmitter>(context: Context, ee: T): T {\n    const map = this._getPatchMap(ee);\n    if (map !== undefined) return ee;\n    this._createPatchMap(ee);\n\n    // patch methods that add a listener to propagate context\n    ADD_LISTENER_METHODS.forEach(methodName => {\n      if (ee[methodName] === undefined) return;\n      ee[methodName] = this._patchAddListener(ee, ee[methodName], context);\n    });\n    // patch methods that remove a listener\n    if (typeof ee.removeListener === 'function') {\n      ee.removeListener = this._patchRemoveListener(ee, ee.removeListener);\n    }\n    if (typeof ee.off === 'function') {\n      ee.off = this._patchRemoveListener(ee, ee.off);\n    }\n    // patch method that remove all listeners\n    if (typeof ee.removeAllListeners === 'function') {\n      ee.removeAllListeners = this._patchRemoveAllListeners(ee, ee.removeAllListeners);\n    }\n    return ee;\n  }\n\n  /**\n   * Patch methods that remove a given listener so that we match the \"patched\"\n   * version of that listener (the one that propagate context).\n   * @param ee EventEmitter instance\n   * @param original reference to the patched method\n   */\n  private _patchRemoveListener(ee: EventEmitter, original: Function) {\n    const contextManager = this;\n    return function (this: never, event: string, listener: Func<void>) {\n      const events = contextManager._getPatchMap(ee)?.[event];\n      if (events === undefined) {\n        return original.call(this, event, listener);\n      }\n      const patchedListener = events.get(listener);\n      return original.call(this, event, patchedListener || listener);\n    };\n  }\n\n  /**\n   * Patch methods that remove all listeners so we remove our\n   * internal references for a given event.\n   * @param ee EventEmitter instance\n   * @param original reference to the patched method\n   */\n  private _patchRemoveAllListeners(ee: EventEmitter, original: Function) {\n    const contextManager = this;\n    return function (this: never, event: string) {\n      const map = contextManager._getPatchMap(ee);\n      if (map !== undefined) {\n        if (arguments.length === 0) {\n          contextManager._createPatchMap(ee);\n        } else if (map[event] !== undefined) {\n          delete map[event];\n        }\n      }\n      return original.apply(this, arguments);\n    };\n  }\n\n  /**\n   * Patch methods on an event emitter instance that can add listeners so we\n   * can force them to propagate a given context.\n   * @param ee EventEmitter instance\n   * @param original reference to the patched method\n   * @param [context] context to propagate when calling listeners\n   */\n  private _patchAddListener(ee: EventEmitter, original: Function, context: Context) {\n    const contextManager = this;\n    return function (this: never, event: string, listener: Func<void>) {\n      /**\n       * This check is required to prevent double-wrapping the listener.\n       * The implementation for ee.once wraps the listener and calls ee.on.\n       * Without this check, we would wrap that wrapped listener.\n       * This causes an issue because ee.removeListener depends on the onceWrapper\n       * to properly remove the listener. If we wrap their wrapper, we break\n       * that detection.\n       */\n      if (contextManager._wrapped) {\n        return original.call(this, event, listener);\n      }\n      let map = contextManager._getPatchMap(ee);\n      if (map === undefined) {\n        map = contextManager._createPatchMap(ee);\n      }\n      let listeners = map[event];\n      if (listeners === undefined) {\n        listeners = new WeakMap();\n        map[event] = listeners;\n      }\n      const patchedListener = contextManager.bind(context, listener);\n      // store a weak reference of the user listener to ours\n      listeners.set(listener, patchedListener);\n\n      /**\n       * See comment at the start of this function for the explanation of this property.\n       */\n      contextManager._wrapped = true;\n      try {\n        return original.call(this, event, patchedListener);\n      } finally {\n        contextManager._wrapped = false;\n      }\n    };\n  }\n\n  private _createPatchMap(ee: EventEmitter): PatchMap {\n    const map = Object.create(null);\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    (ee as any)[this._kOtListeners] = map;\n    return map;\n  }\n  private _getPatchMap(ee: EventEmitter): PatchMap | undefined {\n    return (ee as never)[this._kOtListeners];\n  }\n\n  private readonly _kOtListeners = Symbol('OtListeners');\n  private _wrapped = false;\n}\n", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * NOTICE from the Sentry authors:\n * - Code vendored from: https://github.com/open-telemetry/opentelemetry-js/blob/6515ed8098333646a63a74a8c0150cc2daf520db/packages/opentelemetry-context-async-hooks/src/AbstractAsyncHooksContextManager.ts\n * - Modifications:\n *   - Added lint rules\n *   - Modified import path to AbstractAsyncHooksContextManager\n *   - Added Sentry logging\n *   - Modified constructor to access AsyncLocalStorage class from global object instead of the Node.js API\n */\n\n/* eslint-disable @typescript-eslint/explicit-member-accessibility */\n/* eslint-disable jsdoc/require-jsdoc */\n\nimport type { Context } from '@opentelemetry/api';\nimport { ROOT_CONTEXT } from '@opentelemetry/api';\nimport { debug, GLOBAL_OBJ } from '@sentry/core';\nimport type { AsyncLocalStorage } from 'async_hooks';\nimport { DEBUG_BUILD } from '../debug-build';\nimport { AbstractAsyncHooksContextManager } from './abstract-async-hooks-context-manager';\n\nexport class AsyncLocalStorageContextManager extends AbstractAsyncHooksContextManager {\n  private _asyncLocalStorage: AsyncLocalStorage<Context>;\n\n  constructor() {\n    super();\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-explicit-any\n    const MaybeGlobalAsyncLocalStorageConstructor = (GLOBAL_OBJ as any).AsyncLocalStorage;\n\n    if (!MaybeGlobalAsyncLocalStorageConstructor) {\n      DEBUG_BUILD &&\n        debug.warn(\n          \"Tried to register AsyncLocalStorage async context strategy in a runtime that doesn't support AsyncLocalStorage.\",\n        );\n\n      // @ts-expect-error Vendored type shenanigans\n      this._asyncLocalStorage = {\n        getStore() {\n          return undefined;\n        },\n        run(_store: unknown, callback: () => Context, ...args: unknown[]) {\n          return callback.apply(this, args);\n        },\n        disable() {\n          // noop\n        },\n      };\n    } else {\n      this._asyncLocalStorage = new MaybeGlobalAsyncLocalStorageConstructor();\n    }\n  }\n\n  active(): Context {\n    return this._asyncLocalStorage.getStore() ?? ROOT_CONTEXT;\n  }\n\n  with<A extends unknown[], F extends (...args: A) => ReturnType<F>>(\n    context: Context,\n    fn: F,\n    thisArg?: ThisParameterType<F>,\n    ...args: A\n  ): ReturnType<F> {\n    const cb = thisArg == null ? fn : fn.bind(thisArg);\n    return this._asyncLocalStorage.run(context, cb as never, ...args);\n  }\n\n  enable(): this {\n    return this;\n  }\n\n  disable(): this {\n    this._asyncLocalStorage.disable();\n    return this;\n  }\n}\n", "import { context, diag, DiagLogLevel, propagation, trace } from '@opentelemetry/api';\nimport { Resource } from '@opentelemetry/resources';\nimport { BasicTracerProvider } from '@opentelemetry/sdk-trace-base';\nimport {\n  ATTR_SERVICE_NAME,\n  ATTR_SERVICE_VERSION,\n  SEMRESATTRS_SERVICE_NAMESPACE,\n} from '@opentelemetry/semantic-conventions';\nimport type { Client, Integration, Options } from '@sentry/core';\nimport {\n  consoleIntegration,\n  createStackParser,\n  debug,\n  dedupeIntegration,\n  functionToStringIntegration,\n  getCurrentScope,\n  getIntegrationsToSetup,\n  GLOBAL_OBJ,\n  hasSpansEnabled,\n  inboundFiltersIntegration,\n  linkedErrorsIntegration,\n  nodeStackLineParser,\n  requestDataIntegration,\n  SDK_VERSION,\n  stackParserFromStackParserOptions,\n} from '@sentry/core';\nimport {\n  enhanceDscWithOpenTelemetryRootSpanName,\n  openTelemetrySetupCheck,\n  SentryPropagator,\n  SentrySampler,\n  SentrySpanProcessor,\n  setOpenTelemetryContextAsyncContextStrategy,\n  setupEventContextTrace,\n  wrapContextManagerClass,\n} from '@sentry/opentelemetry';\nimport { VercelEdgeClient } from './client';\nimport { DEBUG_BUILD } from './debug-build';\nimport { winterCGFetchIntegration } from './integrations/wintercg-fetch';\nimport { makeEdgeTransport } from './transports';\nimport type { VercelEdgeOptions } from './types';\nimport { getVercelEnv } from './utils/vercel';\nimport { AsyncLocalStorageContextManager } from './vendored/async-local-storage-context-manager';\n\ndeclare const process: {\n  env: Record<string, string>;\n};\n\nconst nodeStackParser = createStackParser(nodeStackLineParser());\n\n/** Get the default integrations for the browser SDK. */\nexport function getDefaultIntegrations(options: Options): Integration[] {\n  return [\n    dedupeIntegration(),\n    // TODO(v10): Replace with `eventFiltersIntegration` once we remove the deprecated `inboundFiltersIntegration`\n    // eslint-disable-next-line deprecation/deprecation\n    inboundFiltersIntegration(),\n    functionToStringIntegration(),\n    linkedErrorsIntegration(),\n    winterCGFetchIntegration(),\n    consoleIntegration(),\n    // TODO(v10): integration can be included - but integration should not add IP address etc\n    ...(options.sendDefaultPii ? [requestDataIntegration()] : []),\n  ];\n}\n\n/** Inits the Sentry NextJS SDK on the Edge Runtime. */\nexport function init(options: VercelEdgeOptions = {}): Client | undefined {\n  setOpenTelemetryContextAsyncContextStrategy();\n\n  const scope = getCurrentScope();\n  scope.update(options.initialScope);\n\n  if (options.defaultIntegrations === undefined) {\n    options.defaultIntegrations = getDefaultIntegrations(options);\n  }\n\n  if (options.dsn === undefined && process.env.SENTRY_DSN) {\n    options.dsn = process.env.SENTRY_DSN;\n  }\n\n  if (options.tracesSampleRate === undefined && process.env.SENTRY_TRACES_SAMPLE_RATE) {\n    const tracesSampleRate = parseFloat(process.env.SENTRY_TRACES_SAMPLE_RATE);\n    if (isFinite(tracesSampleRate)) {\n      options.tracesSampleRate = tracesSampleRate;\n    }\n  }\n\n  if (options.release === undefined) {\n    const detectedRelease = getSentryRelease();\n    if (detectedRelease !== undefined) {\n      options.release = detectedRelease;\n    }\n  }\n\n  options.environment =\n    options.environment || process.env.SENTRY_ENVIRONMENT || getVercelEnv(false) || process.env.NODE_ENV;\n\n  const client = new VercelEdgeClient({\n    ...options,\n    stackParser: stackParserFromStackParserOptions(options.stackParser || nodeStackParser),\n    integrations: getIntegrationsToSetup(options),\n    transport: options.transport || makeEdgeTransport,\n  });\n  // The client is on the current scope, from where it generally is inherited\n  getCurrentScope().setClient(client);\n\n  client.init();\n\n  // If users opt-out of this, they _have_ to set up OpenTelemetry themselves\n  // There is no way to use this SDK without OpenTelemetry!\n  if (!options.skipOpenTelemetrySetup) {\n    setupOtel(client);\n    validateOpenTelemetrySetup();\n  }\n\n  enhanceDscWithOpenTelemetryRootSpanName(client);\n  setupEventContextTrace(client);\n\n  return client;\n}\n\nfunction validateOpenTelemetrySetup(): void {\n  if (!DEBUG_BUILD) {\n    return;\n  }\n\n  const setup = openTelemetrySetupCheck();\n\n  const required: ReturnType<typeof openTelemetrySetupCheck> = ['SentryContextManager', 'SentryPropagator'];\n\n  if (hasSpansEnabled()) {\n    required.push('SentrySpanProcessor');\n  }\n\n  for (const k of required) {\n    if (!setup.includes(k)) {\n      debug.error(\n        `You have to set up the ${k}. Without this, the OpenTelemetry & Sentry integration will not work properly.`,\n      );\n    }\n  }\n\n  if (!setup.includes('SentrySampler')) {\n    debug.warn(\n      'You have to set up the SentrySampler. Without this, the OpenTelemetry & Sentry integration may still work, but sample rates set for the Sentry SDK will not be respected. If you use a custom sampler, make sure to use `wrapSamplingDecision`.',\n    );\n  }\n}\n\n// exported for tests\n// eslint-disable-next-line jsdoc/require-jsdoc\nexport function setupOtel(client: VercelEdgeClient): void {\n  if (client.getOptions().debug) {\n    setupOpenTelemetryLogger();\n  }\n\n  // Create and configure NodeTracerProvider\n  const provider = new BasicTracerProvider({\n    sampler: new SentrySampler(client),\n    resource: new Resource({\n      [ATTR_SERVICE_NAME]: 'edge',\n      // eslint-disable-next-line deprecation/deprecation\n      [SEMRESATTRS_SERVICE_NAMESPACE]: 'sentry',\n      [ATTR_SERVICE_VERSION]: SDK_VERSION,\n    }),\n    forceFlushTimeoutMillis: 500,\n    spanProcessors: [\n      new SentrySpanProcessor({\n        timeout: client.getOptions().maxSpanWaitDuration,\n      }),\n    ],\n  });\n\n  const SentryContextManager = wrapContextManagerClass(AsyncLocalStorageContextManager);\n\n  trace.setGlobalTracerProvider(provider);\n  propagation.setGlobalPropagator(new SentryPropagator());\n  context.setGlobalContextManager(new SentryContextManager());\n\n  client.traceProvider = provider;\n}\n\n/**\n * Setup the OTEL logger to use our own debug logger.\n */\nfunction setupOpenTelemetryLogger(): void {\n  // Disable diag, to ensure this works even if called multiple times\n  diag.disable();\n  diag.setLogger(\n    {\n      error: debug.error,\n      warn: debug.warn,\n      info: debug.log,\n      debug: debug.log,\n      verbose: debug.log,\n    },\n    DiagLogLevel.DEBUG,\n  );\n}\n\n/**\n * Returns a release dynamically from environment variables.\n */\n// eslint-disable-next-line complexity\nexport function getSentryRelease(fallback?: string): string | undefined {\n  // Always read first as Sentry takes this as precedence\n  if (process.env.SENTRY_RELEASE) {\n    return process.env.SENTRY_RELEASE;\n  }\n\n  // This supports the variable that sentry-webpack-plugin injects\n  if (GLOBAL_OBJ.SENTRY_RELEASE?.id) {\n    return GLOBAL_OBJ.SENTRY_RELEASE.id;\n  }\n\n  // This list is in approximate alpha order, separated into 3 categories:\n  // 1. Git providers\n  // 2. CI providers with specific environment variables (has the provider name in the variable name)\n  // 3. CI providers with generic environment variables (checked for last to prevent possible false positives)\n\n  const possibleReleaseNameOfGitProvider =\n    // GitHub Actions - https://help.github.com/en/actions/configuring-and-managing-workflows/using-environment-variables#default-environment-variables\n    process.env['GITHUB_SHA'] ||\n    // GitLab CI - https://docs.gitlab.com/ee/ci/variables/predefined_variables.html\n    process.env['CI_MERGE_REQUEST_SOURCE_BRANCH_SHA'] ||\n    process.env['CI_BUILD_REF'] ||\n    process.env['CI_COMMIT_SHA'] ||\n    // Bitbucket - https://support.atlassian.com/bitbucket-cloud/docs/variables-and-secrets/\n    process.env['BITBUCKET_COMMIT'];\n\n  const possibleReleaseNameOfCiProvidersWithSpecificEnvVar =\n    // AppVeyor - https://www.appveyor.com/docs/environment-variables/\n    process.env['APPVEYOR_PULL_REQUEST_HEAD_COMMIT'] ||\n    process.env['APPVEYOR_REPO_COMMIT'] ||\n    // AWS CodeBuild - https://docs.aws.amazon.com/codebuild/latest/userguide/build-env-ref-env-vars.html\n    process.env['CODEBUILD_RESOLVED_SOURCE_VERSION'] ||\n    // AWS Amplify - https://docs.aws.amazon.com/amplify/latest/userguide/environment-variables.html\n    process.env['AWS_COMMIT_ID'] ||\n    // Azure Pipelines - https://docs.microsoft.com/en-us/azure/devops/pipelines/build/variables?view=azure-devops&tabs=yaml\n    process.env['BUILD_SOURCEVERSION'] ||\n    // Bitrise - https://devcenter.bitrise.io/builds/available-environment-variables/\n    process.env['GIT_CLONE_COMMIT_HASH'] ||\n    // Buddy CI - https://buddy.works/docs/pipelines/environment-variables#default-environment-variables\n    process.env['BUDDY_EXECUTION_REVISION'] ||\n    // Builtkite - https://buildkite.com/docs/pipelines/environment-variables\n    process.env['BUILDKITE_COMMIT'] ||\n    // CircleCI - https://circleci.com/docs/variables/\n    process.env['CIRCLE_SHA1'] ||\n    // Cirrus CI - https://cirrus-ci.org/guide/writing-tasks/#environment-variables\n    process.env['CIRRUS_CHANGE_IN_REPO'] ||\n    // Codefresh - https://codefresh.io/docs/docs/codefresh-yaml/variables/\n    process.env['CF_REVISION'] ||\n    // Codemagic - https://docs.codemagic.io/yaml-basic-configuration/environment-variables/\n    process.env['CM_COMMIT'] ||\n    // Cloudflare Pages - https://developers.cloudflare.com/pages/platform/build-configuration/#environment-variables\n    process.env['CF_PAGES_COMMIT_SHA'] ||\n    // Drone - https://docs.drone.io/pipeline/environment/reference/\n    process.env['DRONE_COMMIT_SHA'] ||\n    // Flightcontrol - https://www.flightcontrol.dev/docs/guides/flightcontrol/environment-variables#built-in-environment-variables\n    process.env['FC_GIT_COMMIT_SHA'] ||\n    // Heroku #1 https://devcenter.heroku.com/articles/heroku-ci\n    process.env['HEROKU_TEST_RUN_COMMIT_VERSION'] ||\n    // Heroku #2 https://docs.sentry.io/product/integrations/deployment/heroku/#configure-releases\n    process.env['HEROKU_SLUG_COMMIT'] ||\n    // Railway - https://docs.railway.app/reference/variables#git-variables\n    process.env['RAILWAY_GIT_COMMIT_SHA'] ||\n    // Render - https://render.com/docs/environment-variables\n    process.env['RENDER_GIT_COMMIT'] ||\n    // Semaphore CI - https://docs.semaphoreci.com/ci-cd-environment/environment-variables\n    process.env['SEMAPHORE_GIT_SHA'] ||\n    // TravisCI - https://docs.travis-ci.com/user/environment-variables/#default-environment-variables\n    process.env['TRAVIS_PULL_REQUEST_SHA'] ||\n    // Vercel - https://vercel.com/docs/v2/build-step#system-environment-variables\n    process.env['VERCEL_GIT_COMMIT_SHA'] ||\n    process.env['VERCEL_GITHUB_COMMIT_SHA'] ||\n    process.env['VERCEL_GITLAB_COMMIT_SHA'] ||\n    process.env['VERCEL_BITBUCKET_COMMIT_SHA'] ||\n    // Zeit (now known as Vercel)\n    process.env['ZEIT_GITHUB_COMMIT_SHA'] ||\n    process.env['ZEIT_GITLAB_COMMIT_SHA'] ||\n    process.env['ZEIT_BITBUCKET_COMMIT_SHA'];\n\n  const possibleReleaseNameOfCiProvidersWithGenericEnvVar =\n    // CloudBees CodeShip - https://docs.cloudbees.com/docs/cloudbees-codeship/latest/pro-builds-and-configuration/environment-variables\n    process.env['CI_COMMIT_ID'] ||\n    // Coolify - https://coolify.io/docs/knowledge-base/environment-variables\n    process.env['SOURCE_COMMIT'] ||\n    // Heroku #3 https://devcenter.heroku.com/changelog-items/630\n    process.env['SOURCE_VERSION'] ||\n    // Jenkins - https://plugins.jenkins.io/git/#environment-variables\n    process.env['GIT_COMMIT'] ||\n    // Netlify - https://docs.netlify.com/configure-builds/environment-variables/#build-metadata\n    process.env['COMMIT_REF'] ||\n    // TeamCity - https://www.jetbrains.com/help/teamcity/predefined-build-parameters.html\n    process.env['BUILD_VCS_NUMBER'] ||\n    // Woodpecker CI - https://woodpecker-ci.org/docs/usage/environment\n    process.env['CI_COMMIT_SHA'];\n\n  return (\n    possibleReleaseNameOfGitProvider ||\n    possibleReleaseNameOfCiProvidersWithSpecificEnvVar ||\n    possibleReleaseNameOfCiProvidersWithGenericEnvVar ||\n    fallback\n  );\n}\n", "/**\n * This is a copy of the Vercel AI integration from the node SDK.\n *\n * The only difference is that it does not use `@opentelemetry/instrumentation`\n * because Cloudflare Workers do not support it.\n *\n * Therefore, we cannot automatically patch setting `experimental_telemetry: { isEnabled: true }`\n * and users have to manually set this to get spans.\n */\n\nimport type { IntegrationFn } from '@sentry/core';\nimport { addVercelAiProcessors, defineIntegration } from '@sentry/core';\n\nconst INTEGRATION_NAME = 'VercelAI';\n\nconst _vercelAIIntegration = (() => {\n  return {\n    name: INTEGRATION_NAME,\n    setup(client) {\n      addVercelAiProcessors(client);\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Adds Sentry tracing instrumentation for the [ai](https://www.npmjs.com/package/ai) library.\n * This integration is not enabled by default, you need to manually add it.\n *\n * For more information, see the [`ai` documentation](https://sdk.vercel.ai/docs/ai-sdk-core/telemetry).\n *\n *  You need to enable collecting spans for a specific call by setting\n * `experimental_telemetry.isEnabled` to `true` in the first argument of the function call.\n *\n * ```javascript\n * const result = await generateText({\n *   model: openai('gpt-4-turbo'),\n *   experimental_telemetry: { isEnabled: true },\n * });\n * ```\n *\n * If you want to collect inputs and outputs for a specific call, you must specifically opt-in to each\n * function call by setting `experimental_telemetry.recordInputs` and `experimental_telemetry.recordOutputs`\n * to `true`.\n *\n * ```javascript\n * const result = await generateText({\n *  model: openai('gpt-4-turbo'),\n *  experimental_telemetry: { isEnabled: true, recordInputs: true, recordOutputs: true },\n * });\n */\nexport const vercelAIIntegration = defineIntegration(_vercelAIIntegration);\n", "import type { Log, LogSeverityLevel, ParameterizedString } from '@sentry/core';\nimport { _INTERNAL_captureLog } from '@sentry/core';\n\n/**\n * Capture a log with the given level.\n *\n * @param level - The level of the log.\n * @param message - The message to log.\n * @param attributes - Arbitrary structured data that stores information about the log - e.g., userId: 100.\n * @param severityNumber - The severity number of the log.\n */\nfunction captureLog(\n  level: LogSeverityLevel,\n  message: ParameterizedString,\n  attributes?: Log['attributes'],\n  severityNumber?: Log['severityNumber'],\n): void {\n  _INTERNAL_captureLog({ level, message, attributes, severityNumber });\n}\n\n/**\n * @summary Capture a log with the `trace` level. Requires the `enableLogs` option to be enabled.\n *\n * @param message - The message to log.\n * @param attributes - Arbitrary structured data that stores information about the log - e.g., { userId: 100, route: '/dashboard' }.\n *\n * @example\n *\n * ```\n * Sentry.logger.trace('User clicked submit button', {\n *   buttonId: 'submit-form',\n *   formId: 'user-profile',\n *   timestamp: Date.now()\n * });\n * ```\n *\n * @example With template strings\n *\n * ```\n * Sentry.logger.trace(Sentry.logger.fmt`User ${user} navigated to ${page}`, {\n *   userId: '123',\n *   sessionId: 'abc-xyz'\n * });\n * ```\n */\nexport function trace(message: ParameterizedString, attributes?: Log['attributes']): void {\n  captureLog('trace', message, attributes);\n}\n\n/**\n * @summary Capture a log with the `debug` level. Requires the `enableLogs` option to be enabled.\n *\n * @param message - The message to log.\n * @param attributes - Arbitrary structured data that stores information about the log - e.g., { component: 'Header', state: 'loading' }.\n *\n * @example\n *\n * ```\n * Sentry.logger.debug('Component mounted', {\n *   component: 'UserProfile',\n *   props: { userId: 123 },\n *   renderTime: 150\n * });\n * ```\n *\n * @example With template strings\n *\n * ```\n * Sentry.logger.debug(Sentry.logger.fmt`API request to ${endpoint} failed`, {\n *   statusCode: 404,\n *   requestId: 'req-123',\n *   duration: 250\n * });\n * ```\n */\nexport function debug(message: ParameterizedString, attributes?: Log['attributes']): void {\n  captureLog('debug', message, attributes);\n}\n\n/**\n * @summary Capture a log with the `info` level. Requires the `enableLogs` option to be enabled.\n *\n * @param message - The message to log.\n * @param attributes - Arbitrary structured data that stores information about the log - e.g., { feature: 'checkout', status: 'completed' }.\n *\n * @example\n *\n * ```\n * Sentry.logger.info('User completed checkout', {\n *   orderId: 'order-123',\n *   amount: 99.99,\n *   paymentMethod: 'credit_card'\n * });\n * ```\n *\n * @example With template strings\n *\n * ```\n * Sentry.logger.info(Sentry.logger.fmt`User ${user} updated profile picture`, {\n *   userId: 'user-123',\n *   imageSize: '2.5MB',\n *   timestamp: Date.now()\n * });\n * ```\n */\nexport function info(message: ParameterizedString, attributes?: Log['attributes']): void {\n  captureLog('info', message, attributes);\n}\n\n/**\n * @summary Capture a log with the `warn` level. Requires the `enableLogs` option to be enabled.\n *\n * @param message - The message to log.\n * @param attributes - Arbitrary structured data that stores information about the log - e.g., { browser: 'Chrome', version: '91.0' }.\n *\n * @example\n *\n * ```\n * Sentry.logger.warn('Browser compatibility issue detected', {\n *   browser: 'Safari',\n *   version: '14.0',\n *   feature: 'WebRTC',\n *   fallback: 'enabled'\n * });\n * ```\n *\n * @example With template strings\n *\n * ```\n * Sentry.logger.warn(Sentry.logger.fmt`API endpoint ${endpoint} is deprecated`, {\n *   recommendedEndpoint: '/api/v2/users',\n *   sunsetDate: '2024-12-31',\n *   clientVersion: '1.2.3'\n * });\n * ```\n */\nexport function warn(message: ParameterizedString, attributes?: Log['attributes']): void {\n  captureLog('warn', message, attributes);\n}\n\n/**\n * @summary Capture a log with the `error` level. Requires the `enableLogs` option to be enabled.\n *\n * @param message - The message to log.\n * @param attributes - Arbitrary structured data that stores information about the log - e.g., { error: 'NetworkError', url: '/api/data' }.\n *\n * @example\n *\n * ```\n * Sentry.logger.error('Failed to load user data', {\n *   error: 'NetworkError',\n *   url: '/api/users/123',\n *   statusCode: 500,\n *   retryCount: 3\n * });\n * ```\n *\n * @example With template strings\n *\n * ```\n * Sentry.logger.error(Sentry.logger.fmt`Payment processing failed for order ${orderId}`, {\n *   error: 'InsufficientFunds',\n *   amount: 100.00,\n *   currency: 'USD',\n *   userId: 'user-456'\n * });\n * ```\n */\nexport function error(message: ParameterizedString, attributes?: Log['attributes']): void {\n  captureLog('error', message, attributes);\n}\n\n/**\n * @summary Capture a log with the `fatal` level. Requires the `enableLogs` option to be enabled.\n *\n * @param message - The message to log.\n * @param attributes - Arbitrary structured data that stores information about the log - e.g., { appState: 'corrupted', sessionId: 'abc-123' }.\n *\n * @example\n *\n * ```\n * Sentry.logger.fatal('Application state corrupted', {\n *   lastKnownState: 'authenticated',\n *   sessionId: 'session-123',\n *   timestamp: Date.now(),\n *   recoveryAttempted: true\n * });\n * ```\n *\n * @example With template strings\n *\n * ```\n * Sentry.logger.fatal(Sentry.logger.fmt`Critical system failure in ${service}`, {\n *   service: 'payment-processor',\n *   errorCode: 'CRITICAL_FAILURE',\n *   affectedUsers: 150,\n *   timestamp: Date.now()\n * });\n * ```\n */\nexport function fatal(message: ParameterizedString, attributes?: Log['attributes']): void {\n  captureLog('fatal', message, attributes);\n}\n\nexport { fmt } from '@sentry/core';\n"], "names": ["__read", "this", "__values", "performance", "trace", "__spread<PERSON><PERSON>y", "INTEGRATION_NAME", "suppressTracing", "debug"], "mappings": ";;;;;;;;AkCiCoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AlCxBpB;;;;;CAKA,GACO,MAAM,gBAAA,sMAAyB,sBAAmB,CAA0B;IAGnF;;;GAGA,GACS,WAAW,CAAC,OAAO,CAA2B;kMACnD,mBAAA,AAAgB,EAAC,OAAO,EAAE,aAAa,CAAC;QACxC,OAAO,CAAC,SAAA,GAAY,OAAO,CAAC,SAAA,IAAa,CAAA,CAAE;QAE3C,MAAM,aAAa,GAA+B;YAChD,GAAG,OAAO;YACV,QAAQ,EAAE,YAAY;YAC5B,iCAAA;YACM,OAAO,EAAE;gBAAE,IAAI,EAAE;YAAA,CAAe;YAChC,UAAU,EAAE,OAAO,CAAC,UAAA,IAAc,OAAO,CAAC,GAAG,CAAC,WAAW;QAC/D,CAAK;QAED,KAAK,CAAC,aAAa,CAAC;IACxB;IAEA,kEAAA;IACA,+CAAA;IACS,MAAM,KAAK,CAAC,OAAO,EAA6B;QACrD,MAAM,QAAA,GAAW,IAAI,CAAC,aAAa;QACnC,MAAM,aAAA,GAAgB,QAAQ,EAAE,mBAAmB;QAEnD,IAAI,aAAa,EAAE;YACjB,MAAM,aAAa,CAAC,UAAU,EAAE;QACtC;QAEI,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC,iBAAiB,EAAE;YACvC,IAAI,CAAC,cAAc,EAAE;QAC3B;QAEI,OAAO,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC;IAC/B;AACA;ACrDA;;;;;;;;;;;;;;CAcA,GAEA,IAAI,oBAAA,gMAAuB,mBAAgB,AAAhB,EAAiB,gDAAgD,CAAC;AACtF,SAAS,eAAe,CAAC,OAAO,EAAE;IACrC,OAAO,OAAO,CAAC,QAAQ,CAAC,oBAAoB,EAAE,IAAI,CAAC;AACvD;AAIO,SAAS,mBAAmB,CAAC,OAAO,EAAE;IACzC,OAAO,OAAO,CAAC,QAAQ,CAAC,oBAAoB,CAAA,KAAM,IAAI;AAC1D;ACzBA;;;;;;;;;;;;;;CAcA,GACO,IAAI,0BAAA,GAA6B,GAAG;AACpC,IAAI,4BAAA,GAA+B,GAAG;AACtC,IAAI,uBAAA,GAA0B,GAAG;AACxC,wDAAA;AACO,IAAI,cAAA,GAAiB,SAAS;AACrC,yDAAA;AACO,IAAI,4BAAA,GAA+B,GAAG;AAC7C,2EAAA;AACO,IAAI,gCAAA,GAAmC,IAAI;AAClD,mEAAA;AACO,IAAI,wBAAA,GAA2B,IAAI;ACzB1C,IAAIA,QAAA,GAAS,AAACC,UAAA,IAAQA,UAAI,CAAC,MAAM,IAAK,SAAU,CAAC,EAAE,CAAC,EAAE;IAClD,IAAI,CAAA,GAAI,OAAO,MAAA,KAAW,UAAA,IAAc,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC;IAC1D,IAAI,CAAC,CAAC,EAAE,OAAO,CAAC;IAChB,IAAI,CAAA,GAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAA,GAAK,EAAE,EAAE,CAAC;IAChC,IAAI;QACA,MAAO,CAAC,CAAA,KAAM,KAAK,CAAA,IAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAA,GAAI,CAAC,CAAC,IAAI,EAAE,EAAE,IAAI,CAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;IAClF,EACI,OAAO,KAAK,EAAE;QAAE,CAAA,GAAI;YAAE,KAAK,EAAE,KAAA;QAAA,CAAO,CAAA;IAAA,SAC5B;QACJ,IAAI;YACA,IAAI,CAAA,IAAK,CAAC,CAAC,CAAC,IAAA,IAAA,CAAS,CAAA,GAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QAC5D,SACgB;YAAE,IAAI,CAAC,EAAE,MAAM,CAAC,CAAC,KAAK,CAAA;QAAA;IACtC;IACI,OAAO,EAAE;AACb,CAAC;AAkBM,SAAS,iBAAiB,CAAC,QAAQ,EAAE;IACxC,OAAO,QAAQ,CAAC,MAAM,CAAC,SAAU,MAAM,EAAE,OAAO,EAAE;QAC9C,IAAI,KAAA,GAAQ,EAAA,GAAK,SAAA,CAAU,MAAA,KAAW,KAAK,uBAAA,GAA0B,EAAE,CAAA,GAAI,OAAO;QAClF,OAAO,KAAK,CAAC,MAAA,GAAS,wBAAA,GAA2B,MAAA,GAAS,KAAK;IACvE,CAAK,EAAE,EAAE,CAAC;AACV;AACO,SAAS,WAAW,CAAC,OAAO,EAAE;IACjC,OAAO,OAAO,CAAC,aAAa,EAAE,CAAC,GAAG,CAAC,SAAU,EAAE,EAAE;QAC7C,IAAI,KAAKD,QAAM,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,GAAA,GAAM,EAAE,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC;QAClD,IAAI,KAAA,GAAQ,kBAAkB,CAAC,GAAG,CAAA,GAAI,GAAA,GAAM,kBAAkB,CAAC,KAAK,CAAC,KAAK,CAAC;QACnF,sCAAA;QACA,kHAAA;QACQ,IAAI,KAAK,CAAC,QAAA,KAAa,SAAS,EAAE;YAC9B,KAAA,IAAS,4BAAA,GAA+B,KAAK,CAAC,QAAQ,CAAC,QAAQ,EAAE;QAC7E;QACQ,OAAO,KAAK;IACpB,CAAK,CAAC;AACN;AACO,SAAS,iBAAiB,CAAC,KAAK,EAAE;IACrC,IAAI,aAAa,KAAK,CAAC,KAAK,CAAC,4BAA4B,CAAC;IAC1D,IAAI,UAAU,CAAC,MAAA,IAAU,CAAC,EACtB;IACJ,IAAI,WAAA,GAAc,UAAU,CAAC,KAAK,EAAE;IACpC,IAAI,CAAC,WAAW,EACZ;IACJ,IAAI,iBAAiB,WAAW,CAAC,OAAO,CAAC,0BAA0B,CAAC;IACpE,IAAI,cAAA,IAAkB,CAAC,EACnB;IACJ,IAAI,GAAA,GAAM,kBAAkB,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,IAAI,EAAE,CAAC;IAC7E,IAAI,KAAA,GAAQ,kBAAkB,CAAC,WAAW,CAAC,SAAS,CAAC,cAAA,GAAiB,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;IAChF,IAAI,QAAQ;IACZ,IAAI,UAAU,CAAC,MAAA,GAAS,CAAC,EAAE;QACvB,QAAA,gMAAW,iCAAA,AAA8B,EAAC,UAAU,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;IAChG;IACI,OAAO;QAAE,GAAG,EAAE,GAAG;QAAE,KAAK,EAAE,KAAK;QAAE,QAAQ,EAAE;IAAA,CAAU;AACzD;ACpEA;;;;;;;;;;;;;;CAcA,GAKA;;;;;CAKA,GACA,IAAI,oBAAA,GAAsC,YAAY;IAClD,SAAS,oBAAoB,GAAG,CACpC;IACI,oBAAoB,CAAC,SAAS,CAAC,MAAA,GAAS,SAAU,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE;QACxE,IAAI,mMAAU,cAAW,CAAC,UAAU,CAAC,OAAO,CAAC;QAC7C,IAAI,CAAC,OAAA,IAAW,mBAAmB,CAAC,OAAO,CAAC,EACxC;QACJ,IAAI,QAAA,GAAW,WAAW,CAAC,OAAO,EAC7B,MAAM,CAAC,SAAU,IAAI,EAAE;YACxB,OAAO,IAAI,CAAC,MAAA,IAAU,gCAAgC;QAClE,CAAS,EACI,KAAK,CAAC,CAAC,EAAE,4BAA4B,CAAC;QAC3C,IAAI,WAAA,GAAc,iBAAiB,CAAC,QAAQ,CAAC;QAC7C,IAAI,WAAW,CAAC,MAAA,GAAS,CAAC,EAAE;YACxB,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,cAAc,EAAE,WAAW,CAAC;QAC5D;IACA,CAAK;IACD,oBAAoB,CAAC,SAAS,CAAC,OAAA,GAAU,SAAU,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE;QACzE,IAAI,WAAA,GAAc,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,cAAc,CAAC;QACrD,IAAI,aAAA,GAAgB,KAAK,CAAC,OAAO,CAAC,WAAW,IACvC,WAAW,CAAC,IAAI,CAAC,uBAAuB,IACxC,WAAW;QACjB,IAAI,CAAC,aAAa,EACd,OAAO,OAAO;QAClB,IAAI,OAAA,GAAU,CAAA,CAAE;QAChB,IAAI,aAAa,CAAC,MAAA,KAAW,CAAC,EAAE;YAC5B,OAAO,OAAO;QAC1B;QACQ,IAAI,QAAQ,aAAa,CAAC,KAAK,CAAC,uBAAuB,CAAC;QACxD,KAAK,CAAC,OAAO,CAAC,SAAU,KAAK,EAAE;YAC3B,IAAI,OAAA,GAAU,iBAAiB,CAAC,KAAK,CAAC;YACtC,IAAI,OAAO,EAAE;gBACT,IAAI,eAAe;oBAAE,KAAK,EAAE,OAAO,CAAC,KAAA;gBAAA,CAAO;gBAC3C,IAAI,OAAO,CAAC,QAAQ,EAAE;oBAClB,YAAY,CAAC,QAAA,GAAW,OAAO,CAAC,QAAQ;gBAC5D;gBACgB,OAAO,CAAC,OAAO,CAAC,GAAG,CAAA,GAAI,YAAY;YACnD;QACA,CAAS,CAAC;QACF,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAA,KAAW,CAAC,EAAE;YACtC,OAAO,OAAO;QAC1B;QACQ,gMAAO,cAAW,CAAC,UAAU,CAAC,OAAO,EAAE,uMAAW,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;IAClF,CAAK;IACD,oBAAoB,CAAC,SAAS,CAAC,MAAA,GAAS,YAAY;QAChD,OAAO;YAAC,cAAc;SAAC;IAC/B,CAAK;IACD,OAAO,oBAAoB;AAC/B,CAAC,EAAE,CAAC;ACzEJ;;;;;;;;;;;;;;CAcA,GACA,IAAIE,UAAA,GAAW,AAACD,UAAA,IAAQA,UAAI,CAAC,QAAQ,IAAK,SAAS,CAAC,EAAE;IAClD,IAAI,IAAI,OAAO,WAAW,UAAA,IAAc,MAAM,CAAC,QAAQ,EAAE,CAAA,GAAI,CAAA,IAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA,GAAI,CAAC;IAC7E,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;IACvB,IAAI,CAAA,IAAK,OAAO,CAAC,CAAC,MAAA,KAAW,QAAQ,EAAE,OAAO;QAC1C,IAAI,EAAE,YAAY;YACd,IAAI,CAAA,IAAK,CAAA,IAAK,CAAC,CAAC,MAAM,EAAE,CAAA,GAAI,KAAA,CAAM;YAClC,OAAO;gBAAE,KAAK,EAAE,CAAA,IAAK,CAAC,CAAC,CAAC,EAAE,CAAC;gBAAE,IAAI,EAAE,CAAC;YAAA,CAAG;QACnD;IACA,CAAK;IACD,MAAM,IAAI,SAAS,CAAC,IAAI,yBAAA,GAA4B,iCAAiC,CAAC;AAC1F,CAAC;AACD,IAAID,QAAA,GAAS,AAACC,UAAA,IAAQA,UAAI,CAAC,MAAM,IAAK,SAAU,CAAC,EAAE,CAAC,EAAE;IAClD,IAAI,CAAA,GAAI,OAAO,MAAA,KAAW,UAAA,IAAc,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC;IAC1D,IAAI,CAAC,CAAC,EAAE,OAAO,CAAC;IAChB,IAAI,CAAA,GAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAA,GAAK,EAAE,EAAE,CAAC;IAChC,IAAI;QACA,MAAO,CAAC,CAAA,KAAM,KAAK,CAAA,IAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAA,GAAI,CAAC,CAAC,IAAI,EAAE,EAAE,IAAI,CAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;IAClF,EACI,OAAO,KAAK,EAAE;QAAE,CAAA,GAAI;YAAE,KAAK,EAAE,KAAA;QAAA,CAAO,CAAA;IAAA,SAC5B;QACJ,IAAI;YACA,IAAI,CAAA,IAAK,CAAC,CAAC,CAAC,IAAA,IAAA,CAAS,CAAA,GAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QAC5D,SACgB;YAAE,IAAI,CAAC,EAAE,MAAM,CAAC,CAAC,KAAK,CAAA;QAAA;IACtC;IACI,OAAO,EAAE;AACb,CAAC;AAEM,SAAS,kBAAkB,CAAC,UAAU,EAAE;IAC3C,IAAI,GAAG,EAAE,EAAE;IACX,IAAI,GAAA,GAAM,CAAA,CAAE;IACZ,IAAI,OAAO,UAAA,KAAe,YAAY,UAAA,IAAc,IAAI,EAAE;QACtD,OAAO,GAAG;IAClB;IACI,IAAI;QACA,IAAK,IAAI,EAAA,GAAKC,UAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,EAAE,EAAA,GAAK,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,EAAA,GAAK,EAAE,CAAC,IAAI,EAAE,CAAE;YAC1F,IAAI,EAAA,GAAKF,QAAM,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;YACtD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;gBACtB,gMAAI,CAAC,IAAI,CAAC,yBAAA,GAA4B,GAAG,CAAC;gBAC1C;YAChB;YACY,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE;yMACxB,OAAI,CAAC,IAAI,CAAC,uCAAA,GAA0C,GAAG,CAAC;gBACxD;YAChB;YACY,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gBACpB,GAAG,CAAC,GAAG,CAAA,GAAI,GAAG,CAAC,KAAK,EAAE;YACtC,OACiB;gBACD,GAAG,CAAC,GAAG,CAAA,GAAI,GAAG;YAC9B;QACA;IACA,EACI,OAAO,KAAK,EAAE;QAAE,GAAA,GAAM;YAAE,KAAK,EAAE,KAAA;QAAA,CAAO,CAAA;IAAA,SAC9B;QACJ,IAAI;YACA,IAAI,EAAA,IAAM,CAAC,EAAE,CAAC,IAAA,IAAA,CAAS,EAAA,GAAK,EAAE,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;QAC/D,SACgB;YAAE,IAAI,GAAG,EAAE,MAAM,GAAG,CAAC,KAAK,CAAA;QAAA;IAC1C;IACI,OAAO,GAAG;AACd;AACO,SAAS,cAAc,CAAC,GAAG,EAAE;IAChC,OAAO,OAAO,GAAA,KAAQ,QAAA,IAAY,GAAG,CAAC,MAAA,GAAS,CAAC;AACpD;AACO,SAAS,gBAAgB,CAAC,GAAG,EAAE;IAClC,IAAI,GAAA,IAAO,IAAI,EAAE;QACb,OAAO,IAAI;IACnB;IACI,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QACpB,OAAO,gCAAgC,CAAC,GAAG,CAAC;IACpD;IACI,OAAO,8BAA8B,CAAC,GAAG,CAAC;AAC9C;AACA,SAAS,gCAAgC,CAAC,GAAG,EAAE;IAC3C,IAAI,GAAG,EAAE,EAAE;IACX,IAAI,IAAI;IACR,IAAI;QACA,IAAK,IAAI,KAAA,GAAQE,UAAQ,CAAC,GAAG,CAAC,EAAE,OAAA,GAAU,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE,OAAA,GAAU,KAAK,CAAC,IAAI,EAAE,CAAE;YAC3F,IAAI,OAAA,GAAU,OAAO,CAAC,KAAK;YACvC,sCAAA;YACY,IAAI,OAAA,IAAW,IAAI,EACf;YACJ,IAAI,CAAC,IAAI,EAAE;gBACP,IAAI,8BAA8B,CAAC,OAAO,CAAC,EAAE;oBACzC,IAAA,GAAO,OAAO,OAAO;oBACrB;gBACpB;gBACA,mCAAA;gBACgB,OAAO,KAAK;YAC5B;YACY,IAAI,OAAO,OAAA,KAAY,IAAI,EAAE;gBACzB;YAChB;YACY,OAAO,KAAK;QACxB;IACA,EACI,OAAO,KAAK,EAAE;QAAE,GAAA,GAAM;YAAE,KAAK,EAAE,KAAA;QAAA,CAAO,CAAA;IAAA,SAC9B;QACJ,IAAI;YACA,IAAI,OAAA,IAAW,CAAC,OAAO,CAAC,IAAA,IAAA,CAAS,EAAA,GAAK,KAAK,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;QAC/E,SACgB;YAAE,IAAI,GAAG,EAAE,MAAM,GAAG,CAAC,KAAK,CAAA;QAAA;IAC1C;IACI,OAAO,IAAI;AACf;AACA,SAAS,8BAA8B,CAAC,GAAG,EAAE;IACzC,OAAQ,OAAO,GAAG;QACd,KAAK,QAAQ;QACb,KAAK,SAAS;QACd,KAAK,QAAQ;YACT,OAAO,IAAI;IACvB;IACI,OAAO,KAAK;AAChB;ACjIA;;;;;;;;;;;;;;CAcA,GAEA;;;CAGA,GACO,SAAS,mBAAmB,GAAG;IAClC,OAAO,SAAU,EAAE,EAAE;iMACjB,OAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC;IAC1C,CAAK;AACL;AACA;;;CAGA,GACA,SAAS,kBAAkB,CAAC,EAAE,EAAE;IAC5B,IAAI,OAAO,EAAA,KAAO,QAAQ,EAAE;QACxB,OAAO,EAAE;IACjB,OACS;QACD,OAAO,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;IACnD;AACA;AACA;;;;CAIA,GACA,SAAS,gBAAgB,CAAC,EAAE,EAAE;IAC1B,IAAI,MAAA,GAAS,CAAA,CAAE;IACf,IAAI,OAAA,GAAU,EAAE;IAChB,MAAO,OAAA,KAAY,IAAI,CAAE;QACrB,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,SAAU,YAAY,EAAE;YAChE,IAAI,MAAM,CAAC,YAAY,CAAC,EACpB;YACJ,IAAI,KAAA,GAAQ,OAAO,CAAC,YAAY,CAAC;YACjC,IAAI,KAAK,EAAE;gBACP,MAAM,CAAC,YAAY,CAAA,GAAI,MAAM,CAAC,KAAK,CAAC;YACpD;QACA,CAAS,CAAC;QACF,UAAU,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC;IAChD;IACI,OAAO,MAAM;AACjB;ACzDA;;;;;;;;;;;;;;CAcA,GAEA,sCAAA,GACA,IAAI,eAAA,GAAkB,mBAAmB,EAAE;AAQ3C;;;CAGA,GACO,SAAS,kBAAkB,CAAC,EAAE,EAAE;IACnC,IAAI;QACA,eAAe,CAAC,EAAE,CAAC;IAC3B,EACI,OAAO,EAAE,EAAE,CAAA,CAAE,CAAA,+BAAA;AACjB;AClCA;;;;;;;;;;;;;;CAcA,GACO,IAAI,mBAAmB;AAC9B,CAAC,SAAU,mBAAmB,EAAE;IAC5B,mBAAmB,CAAC,WAAW,CAAA,GAAI,YAAY;IAC/C,mBAAmB,CAAC,UAAU,CAAA,GAAI,WAAW;IAC7C,mBAAmB,CAAC,sBAAsB,CAAA,GAAI,wBAAwB;IACtE,mBAAmB,CAAC,qBAAqB,CAAA,GAAI,uBAAuB;IACpE,mBAAmB,CAAC,yBAAyB,CAAA,GAAI,0BAA0B;IAC3E,mBAAmB,CAAC,cAAc,CAAA,GAAI,cAAc;AACxD,CAAC,EAAE,mBAAA,IAAA,CAAwB,sBAAsB,CAAA,CAAE,CAAC,CAAC;ACvBrD;;;;;;;;;;;;;;CAcA,GAGA,IAAI,sBAAA,GAAyB,GAAG;AAChC;;CAEA,GACA,IAAI,wBAAA,GAA2B;IAAC,mBAAmB;CAAC;AACpD,SAAS,gBAAgB,CAAC,GAAG,EAAE;IAC3B,OAAQ,wBAAwB,CAAC,OAAO,CAAC,GAAG,CAAA,GAAI,CAAA,CAAE;AACtD;AACA,IAAI,2BAA2B;IAC3B,yBAAyB;IACzB,gCAAgC;IAChC,yBAAyB;IACzB,yBAAyB;IACzB,0BAA0B;IAC1B,iCAAiC;IACjC,0BAA0B;IAC1B,0BAA0B;IAC1B,mCAAmC;IACnC,4BAA4B;IAC5B,wCAAwC;IACxC,iCAAiC;IACjC,6CAA6C;IAC7C,sCAAsC;IACtC,6BAA6B;IAC7B,4BAA4B;IAC5B,2CAA2C;IAC3C,0CAA0C;IAC1C,4BAA4B;IAC5B,mCAAmC;IACnC,oCAAoC;IACpC,iCAAiC;IACjC,iCAAiC;CACpC;AACD,SAAS,eAAe,CAAC,GAAG,EAAE;IAC1B,OAAQ,wBAAwB,CAAC,OAAO,CAAC,GAAG,CAAA,GAAI,CAAA,CAAE;AACtD;AACA,IAAI,yBAAyB;IACzB,uBAAuB;IACvB,kBAAkB;IAClB,+BAA+B;CAClC;AACD,SAAS,aAAa,CAAC,GAAG,EAAE;IACxB,OAAO,sBAAsB,CAAC,OAAO,CAAC,GAAG,CAAA,GAAI,CAAA,CAAE;AACnD;AACO,IAAI,oCAAA,GAAuC,QAAQ;AACnD,IAAI,6BAAA,GAAgC,GAAG;AACvC,IAAI,4CAAA,GAA+C,GAAG;AACtD,IAAI,2CAAA,GAA8C,GAAG;AAC5D;;CAEA,GACO,IAAI,sBAAsB;IAC7B,iBAAiB,EAAE,KAAK;IACxB,cAAc,EAAE,EAAE;IAClB,6BAA6B,EAAE,EAAE;IACjC,0BAA0B,EAAE,EAAE;IAC9B,QAAQ,EAAE,EAAE;IACZ,uBAAuB,EAAE,EAAE;IAC3B,SAAS,EAAE,EAAE;IACb,uBAAuB,EAAE,KAAK;IAC9B,8BAA8B,EAAE,GAAG;IACnC,uBAAuB,EAAE,IAAI;IAC7B,uBAAuB,EAAE,IAAI;IAC7B,wBAAwB,EAAE,KAAK;IAC/B,+BAA+B,EAAE,GAAG;IACpC,wBAAwB,EAAE,IAAI;IAC9B,wBAAwB,EAAE,IAAI;IAC9B,+BAA+B,EAAE,EAAE;IACnC,+BAA+B,EAAE,IAAI;IACrC,6BAA6B,EAAE,EAAE;IACjC,6BAA6B,EAAE,EAAE;IACjC,yBAAyB,EAAE,EAAE;IAC7B,2BAA2B,EAAE,EAAE;IAC/B,kCAAkC,EAAE,EAAE;IACtC,mCAAmC,EAAE,EAAE;IACvC,gCAAgC,EAAE,EAAE;IACpC,0BAA0B,EAAE,EAAE;IAC9B,iCAAiC,EAAE,EAAE;IACrC,kCAAkC,EAAE,EAAE;IACtC,+BAA+B,EAAE,EAAE;IACnC,0BAA0B,EAAE,KAAK;IACjC,iCAAiC,EAAE,KAAK;IACxC,kCAAkC,EAAE,KAAK;IACzC,+BAA+B,EAAE,KAAK;IACtC,6BAA6B,EAAE,oCAAoC;IACnE,cAAc,2LAAE,eAAY,CAAC,IAAI;IACjC,qBAAqB,EAAE,EAAE;IACzB,gBAAgB,EAAE;QAAC,cAAc;QAAE,SAAS;KAAC;IAC7C,wBAAwB,EAAE,EAAE;IAC5B,iBAAiB,EAAE,EAAE;IACrB,iCAAiC,EAAE,oCAAoC;IACvE,0BAA0B,EAAE,6BAA6B;IACzD,sCAAsC,EAAE,oCAAoC;IAC5E,+BAA+B,EAAE,6BAA6B;IAC9D,2CAA2C,EAAE,oCAAoC;IACjF,oCAAoC,EAAE,6BAA6B;IACnE,2BAA2B,EAAE,GAAG;IAChC,0BAA0B,EAAE,GAAG;IAC/B,yCAAyC,EAAE,4CAA4C;IACvF,wCAAwC,EAAE,2CAA2C;IACrF,oBAAoB,EAAE,EAAE;IACxB,mBAAmB,EAAE,mBAAmB,CAAC,mBAAmB;IAC5D,uBAAuB,EAAE,EAAE;IAC3B,kBAAkB,EAAE,EAAE;IACtB,2BAA2B,EAAE,EAAE;IAC/B,kCAAkC,EAAE,EAAE;IACtC,mCAAmC,EAAE,EAAE;IACvC,gCAAgC,EAAE,EAAE;IACpC,8BAA8B,EAAE,EAAE;IAClC,qCAAqC,EAAE,EAAE;IACzC,sCAAsC,EAAE,EAAE;IAC1C,mCAAmC,EAAE,EAAE;IACvC,8BAA8B,EAAE,EAAE;IAClC,qCAAqC,EAAE,EAAE;IACzC,sCAAsC,EAAE,EAAE;IAC1C,mCAAmC,EAAE,EAAE;IACvC,6BAA6B,EAAE,EAAE;IACjC,oCAAoC,EAAE,EAAE;IACxC,qCAAqC,EAAE,EAAE;IACzC,kCAAkC,EAAE,EAAE;IACtC,qCAAqC,EAAE,EAAE;IACzC,4CAA4C,EAAE,EAAE;IAChD,6CAA6C,EAAE,EAAE;IACjD,0CAA0C,EAAE,EAAE;IAC9C,2BAA2B,EAAE,eAAe;IAC5C,kCAAkC,EAAE,eAAe;IACnD,mCAAmC,EAAE,eAAe;IACpD,gCAAgC,EAAE,eAAe;IACjD,iDAAiD,EAAE,YAAY;IAC/D,6BAA6B,EAAE,EAAE;AACrC,CAAC;AACD;;;;CAIA,GACA,SAAS,YAAY,CAAC,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE;IAC5C,IAAI,OAAO,MAAM,CAAC,GAAG,CAAA,KAAM,WAAW,EAAE;QACpC;IACR;IACI,IAAI,QAAQ,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IACnC,kCAAA;IACI,WAAW,CAAC,GAAG,CAAA,GAAI,KAAK,CAAC,WAAW,EAAC,KAAM,MAAM;AACrD;AACA;;;;;;;CAOA,GACA,SAAS,WAAW,CAAC,IAAI,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE;IACtD,IAAI,GAAA,KAAQ,KAAA,CAAM,EAAE;QAAE,GAAA,GAAM,CAAC,QAAQ,CAAA;IAAA;IACrC,IAAI,GAAA,KAAQ,KAAA,CAAM,EAAE;QAAE,GAAA,GAAM,QAAQ,CAAA;IAAA;IACpC,IAAI,OAAO,MAAM,CAAC,IAAI,CAAA,KAAM,WAAW,EAAE;QACrC,IAAI,QAAQ,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAChC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YACf,IAAI,KAAA,GAAQ,GAAG,EAAE;gBACb,WAAW,CAAC,IAAI,CAAA,GAAI,GAAG;YACvC,OACiB,IAAI,KAAA,GAAQ,GAAG,EAAE;gBAClB,WAAW,CAAC,IAAI,CAAA,GAAI,GAAG;YACvC,OACiB;gBACD,WAAW,CAAC,IAAI,CAAA,GAAI,KAAK;YACzC;QACA;IACA;AACA;AACA;;;;;;CAMA,GACA,SAAS,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE;IACrD,IAAI,SAAA,KAAc,KAAA,CAAM,EAAE;QAAE,SAAA,GAAY,sBAAsB,CAAA;IAAA;IAC9D,IAAI,UAAA,GAAa,KAAK,CAAC,IAAI,CAAC;IAC5B,IAAI,OAAO,UAAA,KAAe,QAAQ,EAAE;QAChC,MAAM,CAAC,IAAI,CAAA,GAAI,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,SAAU,CAAC,EAAE;YAAE,OAAO,CAAC,CAAC,IAAI,EAAE,CAAA;QAAA,CAAG,CAAC;IACzF;AACA;AACA,8CAAA;AACA,IAAI,cAAc;IACd,GAAG,2LAAE,eAAY,CAAC,GAAG;IACrB,OAAO,2LAAE,eAAY,CAAC,OAAO;IAC7B,KAAK,2LAAE,eAAY,CAAC,KAAK;IACzB,IAAI,2LAAE,eAAY,CAAC,IAAI;IACvB,IAAI,2LAAE,eAAY,CAAC,IAAI;IACvB,KAAK,2LAAE,eAAY,CAAC,KAAK;IACzB,IAAI,2LAAE,eAAY,CAAC,IAAI;AAC3B,CAAC;AACD;;;;;CAKA,GACA,SAAS,kBAAkB,CAAC,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE;IAClD,IAAI,KAAA,GAAQ,MAAM,CAAC,GAAG,CAAC;IACvB,IAAI,OAAO,KAAA,KAAU,QAAQ,EAAE;QAC3B,IAAI,QAAA,GAAW,WAAW,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;QAC/C,IAAI,QAAA,IAAY,IAAI,EAAE;YAClB,WAAW,CAAC,GAAG,CAAA,GAAI,QAAQ;QACvC;IACA;AACA;AACA;;;CAGA,GACO,SAAS,gBAAgB,CAAC,MAAM,EAAE;IACrC,IAAI,WAAA,GAAc,CAAA,CAAE;IACpB,IAAK,IAAI,GAAA,IAAO,mBAAmB,CAAE;QACjC,IAAI,GAAA,GAAM,GAAG;QACb,OAAQ,GAAG;YACP,KAAK,gBAAgB;gBACjB,kBAAkB,CAAC,GAAG,EAAE,WAAW,EAAE,MAAM,CAAC;gBAC5C;YACJ;gBACI,IAAI,gBAAgB,CAAC,GAAG,CAAC,EAAE;oBACvB,YAAY,CAAC,GAAG,EAAE,WAAW,EAAE,MAAM,CAAC;gBAC1D,OACqB,IAAI,eAAe,CAAC,GAAG,CAAC,EAAE;oBAC3B,WAAW,CAAC,GAAG,EAAE,WAAW,EAAE,MAAM,CAAC;gBACzD,OACqB,IAAI,aAAa,CAAC,GAAG,CAAC,EAAE;oBACzB,eAAe,CAAC,GAAG,EAAE,WAAW,EAAE,MAAM,CAAC;gBAC7D,OACqB;oBACD,IAAI,KAAA,GAAQ,MAAM,CAAC,GAAG,CAAC;oBACvB,IAAI,OAAO,KAAA,KAAU,eAAe,KAAA,KAAU,IAAI,EAAE;wBAChD,WAAW,CAAC,GAAG,CAAA,GAAI,MAAM,CAAC,KAAK,CAAC;oBACxD;gBACA;QACA;IACA;IACI,OAAO,WAAW;AACtB;ACjQA;;;;;;;;;;;;;;CAcA,GAEA;;CAEA,GACO,SAAS,MAAM,GAAG;IACrB,IAAI,aAAa,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC;IAC9C,OAAO,MAAM,CAAC,MAAM,CAAC,CAAA,CAAE,EAAE,mBAAmB,EAAE,UAAU,CAAC;AAC7D;AACO,SAAS,qBAAqB,GAAG;IACpC,OAAO,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC;AACxC;;;;;ACzBA;;;;;;;;;;;;;;CAcA,GAEO,IAAI,aAAA,GAAgB,WAAW;AChBtC;;;;;;;;;;;;;;CAcA,GACO,SAAS,UAAU,CAAC,KAAK,EAAE;IAC9B,KAAK,CAAC,KAAK,EAAE;AACjB;ACjBA;;;;;;;;;;;;;;CAcA,GAEA,IAAI,iBAAA,GAAoB,CAAC;AACzB,IAAI,2BAAA,GAA8B,CAAC;AACnC,IAAI,2BAAA,GAA8B,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,2BAA2B,CAAC;AAC3E,IAAI,qBAAA,GAAwB,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,iBAAiB,CAAC;AAC3D;;;CAGA,GACO,SAAS,cAAc,CAAC,WAAW,EAAE;IACxC,IAAI,YAAA,GAAe,WAAA,GAAc,IAAI;IACzC,iBAAA;IACI,IAAI,UAAU,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC;IAC1C,+CAAA;IACI,IAAI,KAAA,GAAQ,IAAI,CAAC,KAAK,CAAC,AAAC,WAAA,GAAc,IAAI,GAAI,2BAA2B,CAAC;IAC1E,OAAO;QAAC,OAAO;QAAE,KAAK;KAAC;AAC3B;AACO,SAAS,aAAa,GAAG;IAC5B,IAAI,UAAA,GAAaC,aAAW,CAAC,UAAU;IAKvC,OAAO,UAAU;AACrB;AACA;;;CAGA,GACO,SAAS,MAAM,CAAC,cAAc,EAAE;IACnC,IAAI,aAAa,cAAc,CAAC,aAAa,EAAE,CAAC;IAChD,IAAI,GAAA,GAAM,cAAc,CAAC,OAAO,cAAA,KAAmB,QAAA,GAAW,iBAAiBA,aAAW,CAAC,GAAG,EAAE,CAAC;IACjG,OAAO,UAAU,CAAC,UAAU,EAAE,GAAG,CAAC;AACtC;AA4BA;;;;CAIA,GACO,SAAS,cAAc,CAAC,SAAS,EAAE,OAAO,EAAE;IAC/C,IAAI,OAAA,GAAU,OAAO,CAAC,CAAC,CAAA,GAAI,SAAS,CAAC,CAAC,CAAC;IACvC,IAAI,KAAA,GAAQ,OAAO,CAAC,CAAC,CAAA,GAAI,SAAS,CAAC,CAAC,CAAC;IACzC,WAAA;IACI,IAAI,KAAA,GAAQ,CAAC,EAAE;QACX,OAAA,IAAW,CAAC;QACpB,SAAA;QACQ,KAAA,IAAS,qBAAqB;IACtC;IACI,OAAO;QAAC,OAAO;QAAE,KAAK;KAAC;AAC3B;AAiCA;;;CAGA,GACO,SAAS,iBAAiB,CAAC,KAAK,EAAE;IACrC,OAAQ,KAAK,CAAC,OAAO,CAAC,KAAK,CAAA,IACvB,KAAK,CAAC,MAAA,KAAW,CAAA,IACjB,OAAO,KAAK,CAAC,CAAC,CAAA,KAAM,QAAA,IACpB,OAAO,KAAK,CAAC,CAAC,CAAA,KAAM,QAAQ;AACpC;AACA;;;CAGA,GACO,SAAS,WAAW,CAAC,KAAK,EAAE;IAC/B,OAAQ,iBAAiB,CAAC,KAAK,CAAA,IAC3B,OAAO,KAAA,KAAU,QAAA,IACjB,KAAA,YAAiB,IAAI;AAC7B;AACA;;CAEA,GACO,SAAS,UAAU,CAAC,KAAK,EAAE,KAAK,EAAE;IACrC,IAAI,MAAM;QAAC,KAAK,CAAC,CAAC,CAAA,GAAI,KAAK,CAAC,CAAC,CAAC;QAAE,KAAK,CAAC,CAAC,CAAA,GAAI,KAAK,CAAC,CAAC,CAAC;KAAC;IACxD,cAAA;IACI,IAAI,GAAG,CAAC,CAAC,CAAA,IAAK,qBAAqB,EAAE;QACjC,GAAG,CAAC,CAAC,CAAA,IAAK,qBAAqB;QAC/B,GAAG,CAAC,CAAC,CAAA,IAAK,CAAC;IACnB;IACI,OAAO,GAAG;AACd;AC1JA;;;;;;;;;;;;;;CAcA,GACO,IAAI,gBAAgB;AAC3B,CAAC,SAAU,gBAAgB,EAAE;IACzB,gBAAgB,CAAC,gBAAgB,CAAC,SAAS,CAAA,GAAI,CAAC,CAAA,GAAI,SAAS;IAC7D,gBAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAA,GAAI,CAAC,CAAA,GAAI,QAAQ;AAC/D,CAAC,EAAE,gBAAA,IAAA,CAAqB,mBAAmB,CAAA,CAAE,CAAC,CAAC;ACnB/C;;;;;;;;;;;;;;CAcA,GACA,IAAID,UAAA,GAAW,AAACD,UAAA,IAAQA,UAAI,CAAC,QAAQ,IAAK,SAAS,CAAC,EAAE;IAClD,IAAI,IAAI,OAAO,WAAW,UAAA,IAAc,MAAM,CAAC,QAAQ,EAAE,CAAA,GAAI,CAAA,IAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA,GAAI,CAAC;IAC7E,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;IACvB,IAAI,CAAA,IAAK,OAAO,CAAC,CAAC,MAAA,KAAW,QAAQ,EAAE,OAAO;QAC1C,IAAI,EAAE,YAAY;YACd,IAAI,CAAA,IAAK,CAAA,IAAK,CAAC,CAAC,MAAM,EAAE,CAAA,GAAI,KAAA,CAAM;YAClC,OAAO;gBAAE,KAAK,EAAE,CAAA,IAAK,CAAC,CAAC,CAAC,EAAE,CAAC;gBAAE,IAAI,EAAE,CAAC;YAAA,CAAG;QACnD;IACA,CAAK;IACD,MAAM,IAAI,SAAS,CAAC,IAAI,yBAAA,GAA4B,iCAAiC,CAAC;AAC1F,CAAC;AAED,4DAAA,GACA,IAAI,mBAAA,GAAqC,YAAY;IACrD;;;;KAIA,GACI,SAAS,mBAAmB,CAAC,MAAM,EAAE;QACjC,IAAI,MAAA,KAAW,KAAA,CAAM,EAAE;YAAE,MAAA,GAAS,CAAA,CAAE,CAAA;QAAA;QACpC,IAAI,EAAE;QACN,IAAI,CAAC,YAAA,GAAe,CAAC,EAAA,GAAK,MAAM,CAAC,WAAW,MAAM,IAAA,IAAQ,OAAO,KAAA,IAAS,EAAA,GAAK,EAAE;QACjF,IAAI,CAAC,OAAA,GAAU,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,YAAA,AAC/C,wEAAA;SACa,GAAG,CAAC,SAAU,CAAC,EAAE;YAAE,OAAQ,OAAO,CAAC,CAAC,MAAA,KAAW,UAAA,GAAa,CAAC,CAAC,MAAM,EAAC,GAAI,EAAE,EAAC;QAAA,CAAG,EAC/E,MAAM,CAAC,SAAU,CAAC,EAAE,CAAC,EAAE;YAAE,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;QAAA,CAAG,EAAE,EAAE,CAAC,CAAC,CAAC;IAClE;IACA;;;;;;;;KAQA,GACI,mBAAmB,CAAC,SAAS,CAAC,MAAA,GAAS,SAAU,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE;QACvE,IAAI,GAAG,EAAE,EAAE;QACX,IAAI;YACA,IAAK,IAAI,EAAA,GAAKC,UAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,EAAA,GAAK,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,EAAA,GAAK,EAAE,CAAC,IAAI,EAAE,CAAE;gBACjF,IAAI,UAAA,GAAa,EAAE,CAAC,KAAK;gBACzB,IAAI;oBACA,UAAU,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC;gBAC/D,EACgB,OAAO,GAAG,EAAE;oBACR,gMAAI,CAAC,IAAI,CAAC,wBAAA,GAA2B,UAAU,CAAC,WAAW,CAAC,IAAA,GAAO,SAAA,GAAY,GAAG,CAAC,OAAO,CAAC;gBAC/G;YACA;QACA,EACQ,OAAO,KAAK,EAAE;YAAE,GAAA,GAAM;gBAAE,KAAK,EAAE,KAAA;YAAA,CAAO,CAAA;QAAA,SAC9B;YACJ,IAAI;gBACA,IAAI,EAAA,IAAM,CAAC,EAAE,CAAC,IAAA,IAAA,CAAS,EAAA,GAAK,EAAE,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACnE,SACoB;gBAAE,IAAI,GAAG,EAAE,MAAM,GAAG,CAAC,KAAK,CAAA;YAAA;QAC9C;IACA,CAAK;IACL;;;;;;;;KAQA,GACI,mBAAmB,CAAC,SAAS,CAAC,OAAA,GAAU,SAAU,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE;QACxE,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,SAAU,GAAG,EAAE,UAAU,EAAE;YACvD,IAAI;gBACA,OAAO,UAAU,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,EAAE,MAAM,CAAC;YAC/D,EACY,OAAO,GAAG,EAAE;yMACR,OAAI,CAAC,IAAI,CAAC,yBAAA,GAA4B,UAAU,CAAC,WAAW,CAAC,IAAA,GAAO,SAAA,GAAY,GAAG,CAAC,OAAO,CAAC;YAC5G;YACY,OAAO,GAAG;QACtB,CAAS,EAAE,OAAO,CAAC;IACnB,CAAK;IACD,mBAAmB,CAAC,SAAS,CAAC,MAAA,GAAS,YAAY;QACvD,sDAAA;QACQ,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;IACnC,CAAK;IACD,OAAO,mBAAmB;AAC9B,CAAC,EAAE,CAAC;AClGJ;;;;;;;;;;;;;;CAcA,GACA,IAAI,oBAAA,GAAuB,cAAc;AACzC,IAAI,YAAY,OAAA,GAAU,oBAAA,GAAuB,SAAS;AAC1D,IAAI,gBAAA,GAAmB,UAAA,GAAa,oBAAA,GAAuB,eAAA,GAAkB,oBAAA,GAAuB,QAAQ;AAC5G,IAAI,eAAA,GAAkB,IAAI,MAAM,CAAC,MAAA,GAAS,SAAA,GAAY,GAAA,GAAM,gBAAA,GAAmB,IAAI,CAAC;AACpF,IAAI,sBAAA,GAAyB,qBAAqB;AAClD,IAAI,+BAAA,GAAkC,KAAK;AAC3C;;;;;;;CAOA,GACO,SAAS,WAAW,CAAC,GAAG,EAAE;IAC7B,OAAO,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC;AACpC;AACA;;;CAGA,GACO,SAAS,aAAa,CAAC,KAAK,EAAE;IACjC,OAAQ,sBAAsB,CAAC,IAAI,CAAC,KAAK,CAAA,IACrC,CAAC,+BAA+B,CAAC,IAAI,CAAC,KAAK,CAAC;AACpD;ACvCA;;;;;;;;;;;;;;CAcA,GAEA,IAAI,qBAAA,GAAwB,EAAE;AAC9B,IAAI,mBAAA,GAAsB,GAAG;AAC7B,IAAI,sBAAA,GAAyB,GAAG;AAChC,IAAI,8BAAA,GAAiC,GAAG;AACxC;;;;;;;;CAQA,GACA,IAAI,UAAA,GAA4B,YAAY;IACxC,SAAS,UAAU,CAAC,aAAa,EAAE;QAC/B,IAAI,CAAC,cAAA,GAAiB,IAAI,GAAG,EAAE;QAC/B,IAAI,aAAa,EACb,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;IACtC;IACI,UAAU,CAAC,SAAS,CAAC,GAAA,GAAM,SAAU,GAAG,EAAE,KAAK,EAAE;QACrD,4DAAA;QACA,sBAAA;QACQ,IAAI,UAAA,GAAa,IAAI,CAAC,MAAM,EAAE;QAC9B,IAAI,UAAU,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YACpC,UAAU,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC;QACjD;QACQ,UAAU,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC;QACzC,OAAO,UAAU;IACzB,CAAK;IACD,UAAU,CAAC,SAAS,CAAC,KAAA,GAAQ,SAAU,GAAG,EAAE;QACxC,IAAI,UAAA,GAAa,IAAI,CAAC,MAAM,EAAE;QAC9B,UAAU,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC;QACrC,OAAO,UAAU;IACzB,CAAK;IACD,UAAU,CAAC,SAAS,CAAC,GAAA,GAAM,SAAU,GAAG,EAAE;QACtC,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC;IAC3C,CAAK;IACD,UAAU,CAAC,SAAS,CAAC,SAAA,GAAY,YAAY;QACzC,IAAI,KAAA,GAAQ,IAAI;QAChB,OAAO,IAAI,CAAC,KAAK,GACZ,MAAM,CAAC,SAAU,GAAG,EAAE,GAAG,EAAE;YAC5B,GAAG,CAAC,IAAI,CAAC,MAAM,8BAAA,GAAiC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAC/D,OAAO,GAAG;QACtB,CAAS,EAAE,EAAE,EACA,IAAI,CAAC,sBAAsB,CAAC;IACzC,CAAK;IACD,UAAU,CAAC,SAAS,CAAC,MAAA,GAAS,SAAU,aAAa,EAAE;QACnD,IAAI,aAAa,CAAC,MAAA,GAAS,mBAAmB,EAC1C;QACJ,IAAI,CAAC,cAAA,GAAiB,cACjB,KAAK,CAAC,sBAAsB,EAC5B,OAAO,EAAC,CAAA,2EAAA;SACR,MAAM,CAAC,SAAU,GAAG,EAAE,IAAI,EAAE;YAC7B,IAAI,UAAA,GAAa,IAAI,CAAC,IAAI,EAAE,CAAA,CAAA,qCAAA;YAC5B,IAAI,IAAI,UAAU,CAAC,OAAO,CAAC,8BAA8B,CAAC;YAC1D,IAAI,CAAA,KAAM,CAAA,CAAE,EAAE;gBACV,IAAI,GAAA,GAAM,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;gBAChC,IAAI,KAAA,GAAQ,UAAU,CAAC,KAAK,CAAC,CAAA,GAAI,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC;gBAChD,IAAI,WAAW,CAAC,GAAG,CAAA,IAAK,aAAa,CAAC,KAAK,CAAC,EAAE;oBAC1C,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC;gBACvC;YAIA;YACY,OAAO,GAAG;QACtB,CAAS,EAAE,IAAI,GAAG,EAAE,CAAC;QACrB,gFAAA;QACQ,IAAI,IAAI,CAAC,cAAc,CAAC,IAAA,GAAO,qBAAqB,EAAE;YAClD,IAAI,CAAC,cAAA,GAAiB,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,EACjE,OAAO,EAAC,CAAA,sDAAA;aACR,KAAK,CAAC,CAAC,EAAE,qBAAqB,CAAC,CAAC;QACjD;IACA,CAAK;IACD,UAAU,CAAC,SAAS,CAAC,KAAA,GAAQ,YAAY;QACrC,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,EAAE;IAC/D,CAAK;IACD,UAAU,CAAC,SAAS,CAAC,MAAA,GAAS,YAAY;QACtC,IAAI,UAAA,GAAa,IAAI,UAAU,EAAE;QACjC,UAAU,CAAC,cAAA,GAAiB,IAAI,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC;QACxD,OAAO,UAAU;IACzB,CAAK;IACD,OAAO,UAAU;AACrB,CAAC,EAAE,CAAC;ACnGJ;;;;;;;;;;;;;;CAcA,GAIO,IAAI,mBAAA,GAAsB,aAAa;AACvC,IAAI,kBAAA,GAAqB,YAAY;AAC5C,IAAI,OAAA,GAAU,IAAI;AAClB,IAAI,YAAA,GAAe,mBAAmB;AACtC,IAAI,aAAA,GAAgB,yBAAyB;AAC7C,IAAI,cAAA,GAAiB,yBAAyB;AAC9C,IAAI,UAAA,GAAa,aAAa;AAC9B,IAAI,kBAAA,GAAqB,IAAI,MAAM,CAAC,QAAA,GAAW,YAAA,GAAe,KAAA,GAAQ,gBAAgB,KAAA,GAAQ,cAAA,GAAiB,QAAQ,UAAA,GAAa,cAAc,CAAC;AACnJ;;;;;;;;;CASA,GACO,SAAS,gBAAgB,CAAC,WAAW,EAAE;IAC1C,IAAI,QAAQ,kBAAkB,CAAC,IAAI,CAAC,WAAW,CAAC;IAChD,IAAI,CAAC,KAAK,EACN,OAAO,IAAI;IACnB,yEAAA;IACA,4FAAA;IACA,qEAAA;IACI,IAAI,KAAK,CAAC,CAAC,CAAA,KAAM,IAAA,IAAQ,KAAK,CAAC,CAAC,CAAC,EAC7B,OAAO,IAAI;IACf,OAAO;QACH,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;QACjB,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;QAChB,UAAU,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IAC1C,CAAK;AACL;AACA;;;;;CAKA,GACA,IAAI,yBAAA,GAA2C,YAAY;IACvD,SAAS,yBAAyB,GAAG,CACzC;IACI,yBAAyB,CAAC,SAAS,CAAC,MAAA,GAAS,SAAU,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE;QAC7E,IAAI,uMAAcE,QAAK,CAAC,cAAc,CAAC,OAAO,CAAC;QAC/C,IAAI,CAAC,WAAA,IACD,mBAAmB,CAAC,OAAO,CAAA,IAC3B,8LAAC,qBAAA,AAAkB,EAAC,WAAW,CAAC,EAChC;QACJ,IAAI,WAAA,GAAc,OAAA,GAAU,GAAA,GAAM,WAAW,CAAC,OAAA,GAAU,GAAA,GAAM,WAAW,CAAC,MAAA,GAAS,IAAA,GAAO,MAAM,CAAC,WAAW,CAAC,UAAA,6LAAc,aAAU,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC;QACxJ,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,mBAAmB,EAAE,WAAW,CAAC;QACrD,IAAI,WAAW,CAAC,UAAU,EAAE;YACxB,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,kBAAkB,EAAE,WAAW,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;QACvF;IACA,CAAK;IACD,yBAAyB,CAAC,SAAS,CAAC,OAAA,GAAU,SAAU,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE;QAC9E,IAAI,iBAAA,GAAoB,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,mBAAmB,CAAC;QAChE,IAAI,CAAC,iBAAiB,EAClB,OAAO,OAAO;QAClB,IAAI,WAAA,GAAc,KAAK,CAAC,OAAO,CAAC,iBAAiB,IAC3C,iBAAiB,CAAC,CAAC,CAAA,GACnB,iBAAiB;QACvB,IAAI,OAAO,WAAA,KAAgB,QAAQ,EAC/B,OAAO,OAAO;QAClB,IAAI,WAAA,GAAc,gBAAgB,CAAC,WAAW,CAAC;QAC/C,IAAI,CAAC,WAAW,EACZ,OAAO,OAAO;QAClB,WAAW,CAAC,QAAA,GAAW,IAAI;QAC3B,IAAI,gBAAA,GAAmB,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,kBAAkB,CAAC;QAC9D,IAAI,gBAAgB,EAAE;YAC9B,sEAAA;YACA,iBAAA;YACY,IAAI,KAAA,GAAQ,KAAK,CAAC,OAAO,CAAC,gBAAgB,IACpC,gBAAgB,CAAC,IAAI,CAAC,GAAG,IACzB,gBAAgB;YACtB,WAAW,CAAC,UAAA,GAAa,IAAI,UAAU,CAAC,OAAO,KAAA,KAAU,QAAA,GAAW,KAAA,GAAQ,SAAS,CAAC;QAClG;QACQ,gMAAOA,QAAK,CAAC,cAAc,CAAC,OAAO,EAAE,WAAW,CAAC;IACzD,CAAK;IACD,yBAAyB,CAAC,SAAS,CAAC,MAAA,GAAS,YAAY;QACrD,OAAO;YAAC,mBAAmB;YAAE,kBAAkB;SAAC;IACxD,CAAK;IACD,OAAO,yBAAyB;AACpC,CAAC,EAAE,CAAC;ACpGJ;;;;;;;;;;;;;;CAcA,GACA,qDAAA,GACA;;;EAGA,GACA,IAAI,SAAA,GAAY,iBAAiB;AACjC,IAAI,OAAA,GAAU,eAAe;AAC7B,IAAI,YAAA,GAAe,oBAAoB;AACvC,IAAI,SAAA,GAAY,QAAQ,CAAC,SAAS;AAClC,IAAI,YAAA,GAAe,SAAS,CAAC,QAAQ;AACrC,IAAI,mBAAmB,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC;AAChD,IAAI,YAAA,GAAe,OAAO,CAAC,MAAM,CAAC,cAAc,EAAE,MAAM,CAAC;AACzD,IAAI,WAAA,GAAc,MAAM,CAAC,SAAS;AAClC,IAAI,cAAA,GAAiB,WAAW,CAAC,cAAc;AAC/C,IAAI,cAAA,GAAiB,MAAA,GAAS,MAAM,CAAC,WAAA,GAAc,SAAS;AAC5D,IAAI,oBAAA,GAAuB,WAAW,CAAC,QAAQ;AAC/C;;;;;;;CAOA,GACA,SAAS,OAAO,CAAC,IAAI,EAAE,SAAS,EAAE;IAC9B,OAAO,SAAU,GAAG,EAAE;QAClB,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;IACnC,CAAK;AACL;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;CA2BA,GACO,SAAS,aAAa,CAAC,KAAK,EAAE;IACjC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAA,IAAK,UAAU,CAAC,KAAK,CAAA,KAAM,SAAS,EAAE;QACzD,OAAO,KAAK;IACpB;IACI,IAAI,KAAA,GAAQ,YAAY,CAAC,KAAK,CAAC;IAC/B,IAAI,KAAA,KAAU,IAAI,EAAE;QAChB,OAAO,IAAI;IACnB;IACI,IAAI,IAAA,GAAO,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,aAAa,CAAA,IAAK,KAAK,CAAC,WAAW;IACzE,OAAQ,OAAO,IAAA,IAAQ,UAAA,IACnB,IAAA,YAAgB,IAAA,IAChB,YAAY,CAAC,IAAI,CAAC,IAAI,CAAA,KAAM,gBAAgB;AACpD;AACA;;;;;;;;;;;;;;;;;;;;;;;CAuBA,GACA,SAAS,YAAY,CAAC,KAAK,EAAE;IACzB,OAAO,SAAS,IAAA,IAAQ,OAAO,KAAA,IAAS,QAAQ;AACpD;AACA;;;;;;CAMA,GACA,SAAS,UAAU,CAAC,KAAK,EAAE;IACvB,IAAI,KAAA,IAAS,IAAI,EAAE;QACf,OAAO,KAAA,KAAU,YAAY,YAAA,GAAe,OAAO;IAC3D;IACI,OAAO,cAAA,IAAkB,kBAAkB,MAAM,CAAC,KAAK,IACjD,SAAS,CAAC,KAAK,IACf,cAAc,CAAC,KAAK,CAAC;AAC/B;AACA;;;;;;CAMA,GACA,SAAS,SAAS,CAAC,KAAK,EAAE;IACtB,IAAI,KAAA,GAAQ,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,cAAc,CAAC,EAAE,GAAA,GAAM,KAAK,CAAC,cAAc,CAAC;IACnF,IAAI,QAAA,GAAW,KAAK;IACpB,IAAI;QACA,KAAK,CAAC,cAAc,CAAA,GAAI,SAAS;QACjC,QAAA,GAAW,IAAI;IACvB,EACI,OAAO,CAAC,EAAE;IACd,UAAA;IACA;IACI,IAAI,SAAS,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC;IAC7C,IAAI,QAAQ,EAAE;QACV,IAAI,KAAK,EAAE;YACP,KAAK,CAAC,cAAc,CAAA,GAAI,GAAG;QACvC,OACa;YACD,OAAO,KAAK,CAAC,cAAc,CAAC;QACxC;IACA;IACI,OAAO,MAAM;AACjB;AACA;;;;;;CAMA,GACA,SAAS,cAAc,CAAC,KAAK,EAAE;IAC3B,OAAO,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC;AAC3C;ACpKA;;;;;;;;;;;;;;CAcA,GACA,qDAAA,GAEA,IAAI,SAAA,GAAY,EAAE;AAClB;;;CAGA,GACO,SAAS,KAAK,GAAG;IACpB,IAAI,IAAA,GAAO,EAAE;IACb,IAAK,IAAI,EAAA,GAAK,CAAC,EAAE,EAAA,GAAK,SAAS,CAAC,MAAM,EAAE,EAAE,EAAE,CAAE;QAC1C,IAAI,CAAC,EAAE,CAAA,GAAI,SAAS,CAAC,EAAE,CAAC;IAChC;IACI,IAAI,MAAA,GAAS,IAAI,CAAC,KAAK,EAAE;IACzB,IAAI,OAAA,GAAU,IAAI,OAAO,EAAE;IAC3B,MAAO,IAAI,CAAC,MAAA,GAAS,CAAC,CAAE;QACpB,MAAA,GAAS,eAAe,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE,OAAO,CAAC;IAClE;IACI,OAAO,MAAM;AACjB;AACA,SAAS,SAAS,CAAC,KAAK,EAAE;IACtB,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE;QAChB,OAAO,KAAK,CAAC,KAAK,EAAE;IAC5B;IACI,OAAO,KAAK;AAChB;AACA;;;;;;;CAOA,GACA,SAAS,eAAe,CAAC,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE;IAC/C,IAAI,KAAA,KAAU,KAAA,CAAM,EAAE;QAAE,KAAA,GAAQ,CAAC,CAAA;IAAA;IACjC,IAAI,MAAM;IACV,IAAI,KAAA,GAAQ,SAAS,EAAE;QACnB,OAAO,SAAS;IACxB;IACI,KAAK,EAAE;IACP,IAAI,WAAW,CAAC,GAAG,KAAK,WAAW,CAAC,GAAG,KAAK,UAAU,CAAC,GAAG,CAAC,EAAE;QACzD,MAAA,GAAS,SAAS,CAAC,GAAG,CAAC;IAC/B,OACS,IAAI,OAAO,CAAC,GAAG,CAAC,EAAE;QACnB,SAAS,GAAG,CAAC,KAAK,EAAE;QACpB,IAAI,OAAO,CAAC,GAAG,CAAC,EAAE;YACd,IAAK,IAAI,IAAI,CAAC,EAAE,CAAA,GAAI,GAAG,CAAC,MAAM,EAAE,CAAA,GAAI,CAAC,EAAE,CAAC,EAAE,CAAE;gBACxC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9C;QACA,OACa,IAAI,QAAQ,CAAC,GAAG,CAAC,EAAE;YACpB,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC;YAC3B,IAAK,IAAI,IAAI,CAAC,EAAE,CAAA,GAAI,IAAI,CAAC,MAAM,EAAE,CAAA,GAAI,CAAC,EAAE,CAAC,EAAE,CAAE;gBACzC,IAAI,GAAA,GAAM,IAAI,CAAC,CAAC,CAAC;gBACjB,MAAM,CAAC,GAAG,CAAA,GAAI,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACjD;QACA;IACA,OACS,IAAI,QAAQ,CAAC,GAAG,CAAC,EAAE;QACpB,IAAI,QAAQ,CAAC,GAAG,CAAC,EAAE;YACf,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE;gBACxB,OAAO,GAAG;YAC1B;YACY,MAAA,GAAS,MAAM,CAAC,MAAM,CAAC,CAAA,CAAE,EAAE,GAAG,CAAC;YAC/B,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC;YAC3B,IAAK,IAAI,IAAI,CAAC,EAAE,CAAA,GAAI,IAAI,CAAC,MAAM,EAAE,CAAA,GAAI,CAAC,EAAE,CAAC,EAAE,CAAE;gBACzC,IAAI,GAAA,GAAM,IAAI,CAAC,CAAC,CAAC;gBACjB,IAAI,QAAA,GAAW,GAAG,CAAC,GAAG,CAAC;gBACvB,IAAI,WAAW,CAAC,QAAQ,CAAC,EAAE;oBACvB,IAAI,OAAO,QAAA,KAAa,WAAW,EAAE;wBACjC,OAAO,MAAM,CAAC,GAAG,CAAC;oBAC1C,OACyB;wBACzB,qCAAA;wBACwB,MAAM,CAAC,GAAG,CAAA,GAAI,QAAQ;oBAC9C;gBACA,OACqB;oBACD,IAAI,IAAA,GAAO,MAAM,CAAC,GAAG,CAAC;oBACtB,IAAI,IAAA,GAAO,QAAQ;oBACnB,IAAI,mBAAmB,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAA,IACrC,mBAAmB,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,EAAE;wBACxC,OAAO,MAAM,CAAC,GAAG,CAAC;oBAC1C,OACyB;wBACD,IAAI,QAAQ,CAAC,IAAI,CAAA,IAAK,QAAQ,CAAC,IAAI,CAAC,EAAE;4BAClC,IAAI,IAAA,GAAO,OAAO,CAAC,GAAG,CAAC,IAAI,CAAA,IAAK,EAAE;4BAClC,IAAI,IAAA,GAAO,OAAO,CAAC,GAAG,CAAC,IAAI,CAAA,IAAK,EAAE;4BAClC,IAAI,CAAC,IAAI,CAAC;gCAAE,GAAG,EAAE,GAAG;gCAAE,GAAG,EAAE,GAAA;4BAAA,CAAK,CAAC;4BACjC,IAAI,CAAC,IAAI,CAAC;gCAAE,GAAG,EAAE,GAAG;gCAAE,GAAG,EAAE,GAAA;4BAAA,CAAK,CAAC;4BACjC,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;4BACvB,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;wBACnD;wBACwB,MAAM,CAAC,GAAG,CAAA,GAAI,eAAe,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,CAAC;oBAC5F;gBACA;YACA;QACA,OACa;YACD,MAAA,GAAS,GAAG;QACxB;IACA;IACI,OAAO,MAAM;AACjB;AACA;;;;;CAKA,GACA,SAAS,mBAAmB,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,EAAE;IAC5C,IAAI,GAAA,GAAM,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE;IACrC,IAAK,IAAI,IAAI,CAAC,EAAE,CAAA,GAAI,GAAG,CAAC,MAAM,EAAE,CAAA,GAAI,CAAC,EAAE,CAAC,EAAE,CAAE;QACxC,IAAI,IAAA,GAAO,GAAG,CAAC,CAAC,CAAC;QACjB,IAAI,IAAI,CAAC,GAAA,KAAQ,GAAA,IAAO,IAAI,CAAC,GAAA,KAAQ,GAAG,EAAE;YACtC,OAAO,IAAI;QACvB;IACA;IACI,OAAO,KAAK;AAChB;AACA,SAAS,OAAO,CAAC,KAAK,EAAE;IACpB,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;AAC/B;AACA,SAAS,UAAU,CAAC,KAAK,EAAE;IACvB,OAAO,OAAO,KAAA,KAAU,UAAU;AACtC;AACA,SAAS,QAAQ,CAAC,KAAK,EAAE;IACrB,OAAQ,CAAC,WAAW,CAAC,KAAK,CAAA,IACtB,CAAC,OAAO,CAAC,KAAK,CAAA,IACd,CAAC,UAAU,CAAC,KAAK,CAAA,IACjB,OAAO,KAAA,KAAU,QAAQ;AACjC;AACA,SAAS,WAAW,CAAC,KAAK,EAAE;IACxB,OAAQ,OAAO,KAAA,KAAU,QAAA,IACrB,OAAO,KAAA,KAAU,QAAA,IACjB,OAAO,KAAA,KAAU,SAAA,IACjB,OAAO,KAAA,KAAU,WAAA,IACjB,KAAA,YAAiB,IAAA,IACjB,KAAA,YAAiB,MAAA,IACjB,KAAA,KAAU,IAAI;AACtB;AACA,SAAS,WAAW,CAAC,GAAG,EAAE,GAAG,EAAE;IAC3B,IAAI,CAAC,aAAa,CAAC,GAAG,CAAA,IAAK,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE;QAC5C,OAAO,KAAK;IACpB;IACI,OAAO,IAAI;AACf;ACjKA;;;;;;;;;;;;;;CAcA,GACA,IAAI,QAAA,GAA0B,YAAY;IACtC,SAAS,QAAQ,GAAG;QAChB,IAAI,KAAA,GAAQ,IAAI;QAChB,IAAI,CAAC,QAAA,GAAW,IAAI,OAAO,CAAC,SAAU,OAAO,EAAE,MAAM,EAAE;YACnD,KAAK,CAAC,QAAA,GAAW,OAAO;YACxB,KAAK,CAAC,OAAA,GAAU,MAAM;QAClC,CAAS,CAAC;IACV;IACI,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,SAAS,EAAE,SAAS,EAAE;QACjD,GAAG,EAAE,YAAY;YACb,OAAO,IAAI,CAAC,QAAQ;QAChC,CAAS;QACD,UAAU,EAAE,KAAK;QACjB,YAAY,EAAE;IACtB,CAAK,CAAC;IACF,QAAQ,CAAC,SAAS,CAAC,OAAA,GAAU,SAAU,GAAG,EAAE;QACxC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;IAC1B,CAAK;IACD,QAAQ,CAAC,SAAS,CAAC,MAAA,GAAS,SAAU,GAAG,EAAE;QACvC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;IACzB,CAAK;IACD,OAAO,QAAQ;AACnB,CAAC,EAAE,CAAC;ACrCJ;;;;;;;;;;;;;;CAcA,GACA,IAAIJ,QAAA,GAAS,AAACC,UAAA,IAAQA,UAAI,CAAC,MAAM,IAAK,SAAU,CAAC,EAAE,CAAC,EAAE;IAClD,IAAI,CAAA,GAAI,OAAO,MAAA,KAAW,UAAA,IAAc,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC;IAC1D,IAAI,CAAC,CAAC,EAAE,OAAO,CAAC;IAChB,IAAI,CAAA,GAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAA,GAAK,EAAE,EAAE,CAAC;IAChC,IAAI;QACA,MAAO,CAAC,CAAA,KAAM,KAAK,CAAA,IAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAA,GAAI,CAAC,CAAC,IAAI,EAAE,EAAE,IAAI,CAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;IAClF,EACI,OAAO,KAAK,EAAE;QAAE,CAAA,GAAI;YAAE,KAAK,EAAE,KAAA;QAAA,CAAO,CAAA;IAAA,SAC5B;QACJ,IAAI;YACA,IAAI,CAAA,IAAK,CAAC,CAAC,CAAC,IAAA,IAAA,CAAS,CAAA,GAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QAC5D,SACgB;YAAE,IAAI,CAAC,EAAE,MAAM,CAAC,CAAC,KAAK,CAAA;QAAA;IACtC;IACI,OAAO,EAAE;AACb,CAAC;AACD,IAAII,kBAAgB,AAACJ,UAAA,IAAQA,UAAI,CAAC,aAAa,IAAK,SAAU,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;IAC1E,IAAI,IAAA,IAAQ,SAAS,CAAC,MAAA,KAAW,CAAC,EAAE,IAAK,IAAI,CAAA,GAAI,CAAC,EAAE,CAAA,GAAI,IAAI,CAAC,MAAM,EAAE,EAAE,EAAE,CAAA,GAAI,CAAC,EAAE,CAAC,EAAE,CAAE;QACjF,IAAI,EAAA,IAAM,CAAA,CAAE,CAAA,IAAK,IAAI,CAAC,EAAE;YACpB,IAAI,CAAC,EAAE,EAAE,EAAA,GAAK,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;YACpD,EAAE,CAAC,CAAC,CAAA,GAAI,IAAI,CAAC,CAAC,CAAC;QAC3B;IACA;IACI,OAAO,EAAE,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC5D,CAAC;AAED;;CAEA,GACA,IAAI,cAAA,GAAgC,YAAY;IAC5C,SAAS,cAAc,CAAC,SAAS,EAAE,KAAK,EAAE;QACtC,IAAI,CAAC,SAAA,GAAY,SAAS;QAC1B,IAAI,CAAC,KAAA,GAAQ,KAAK;QAClB,IAAI,CAAC,SAAA,GAAY,KAAK;QACtB,IAAI,CAAC,SAAA,GAAY,IAAI,QAAQ,EAAE;IACvC;IACI,MAAM,CAAC,cAAc,CAAC,cAAc,CAAC,SAAS,EAAE,UAAU,EAAE;QACxD,GAAG,EAAE,YAAY;YACb,OAAO,IAAI,CAAC,SAAS;QACjC,CAAS;QACD,UAAU,EAAE,KAAK;QACjB,YAAY,EAAE;IACtB,CAAK,CAAC;IACF,MAAM,CAAC,cAAc,CAAC,cAAc,CAAC,SAAS,EAAE,SAAS,EAAE;QACvD,GAAG,EAAE,YAAY;YACb,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO;QACzC,CAAS;QACD,UAAU,EAAE,KAAK;QACjB,YAAY,EAAE;IACtB,CAAK,CAAC;IACF,cAAc,CAAC,SAAS,CAAC,IAAA,GAAO,YAAY;QACxC,IAAI,EAAE;QACN,IAAI,KAAA,GAAQ,IAAI;QAChB,IAAI,IAAA,GAAO,EAAE;QACb,IAAK,IAAI,EAAA,GAAK,CAAC,EAAE,EAAA,GAAK,SAAS,CAAC,MAAM,EAAE,EAAE,EAAE,CAAE;YAC1C,IAAI,CAAC,EAAE,CAAA,GAAI,SAAS,CAAC,EAAE,CAAC;QACpC;QACQ,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,IAAI,CAAC,SAAA,GAAY,IAAI;YACrB,IAAI;gBACA,OAAO,CAAC,OAAO,CAAC,CAAC,EAAA,GAAK,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,EAAEI,eAAa,CAAC;oBAAC,IAAI,CAAC,KAAK;iBAAC,EAAEL,QAAM,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAU,GAAG,EAAE;oBAAE,OAAO,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA;gBAAA,CAAG,EAAE,SAAU,GAAG,EAAE;oBAAE,OAAO,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;gBAAA,CAAG,CAAC;YAC7O,EACY,OAAO,GAAG,EAAE;gBACR,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC;YAC1C;QACA;QACQ,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO;IACrC,CAAK;IACD,OAAO,cAAc;AACzB,CAAC,EAAE,CAAC;ACpFJ;;;;;;;;;;;;;;CAcA,GACA,yBAAA;AACO,IAAI,kBAAA,GAAqB,WAAW;AChB3C;;;;;;;;;;;;;;CAcA,GACA,IAAI,QAAA,GAAW,AAACC,UAAA,IAAQA,UAAI,CAAC,QAAQ,IAAK,YAAY;IAClD,QAAA,GAAW,MAAM,CAAC,MAAA,IAAU,SAAS,CAAC,EAAE;QACpC,IAAK,IAAI,CAAC,EAAE,CAAA,GAAI,CAAC,EAAE,CAAA,GAAI,SAAS,CAAC,MAAM,EAAE,CAAA,GAAI,CAAC,EAAE,CAAC,EAAE,CAAE;YACjD,CAAA,GAAI,SAAS,CAAC,CAAC,CAAC;YAChB,IAAK,IAAI,CAAA,IAAK,CAAC,CAAE,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAC3D,CAAC,CAAC,CAAC,CAAA,GAAI,CAAC,CAAC,CAAC,CAAC;QAC3B;QACQ,OAAO,CAAC;IAChB,CAAK;IACD,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC;AAC1C,CAAC;AACD,IAAIC,UAAA,GAAW,AAACD,UAAA,IAAQA,UAAI,CAAC,QAAQ,IAAK,SAAS,CAAC,EAAE;IAClD,IAAI,IAAI,OAAO,WAAW,UAAA,IAAc,MAAM,CAAC,QAAQ,EAAE,CAAA,GAAI,CAAA,IAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA,GAAI,CAAC;IAC7E,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;IACvB,IAAI,CAAA,IAAK,OAAO,CAAC,CAAC,MAAA,KAAW,QAAQ,EAAE,OAAO;QAC1C,IAAI,EAAE,YAAY;YACd,IAAI,CAAA,IAAK,CAAA,IAAK,CAAC,CAAC,MAAM,EAAE,CAAA,GAAI,KAAA,CAAM;YAClC,OAAO;gBAAE,KAAK,EAAE,CAAA,IAAK,CAAC,CAAC,CAAC,EAAE,CAAC;gBAAE,IAAI,EAAE,CAAC;YAAA,CAAG;QACnD;IACA,CAAK;IACD,MAAM,IAAI,SAAS,CAAC,IAAI,yBAAA,GAA4B,iCAAiC,CAAC;AAC1F,CAAC;AACD,IAAID,QAAA,GAAS,AAACC,UAAA,IAAQA,UAAI,CAAC,MAAM,IAAK,SAAU,CAAC,EAAE,CAAC,EAAE;IAClD,IAAI,CAAA,GAAI,OAAO,MAAA,KAAW,UAAA,IAAc,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC;IAC1D,IAAI,CAAC,CAAC,EAAE,OAAO,CAAC;IAChB,IAAI,CAAA,GAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAA,GAAK,EAAE,EAAE,CAAC;IAChC,IAAI;QACA,MAAO,CAAC,CAAA,KAAM,KAAK,CAAA,IAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAA,GAAI,CAAC,CAAC,IAAI,EAAE,EAAE,IAAI,CAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;IAClF,EACI,OAAO,KAAK,EAAE;QAAE,CAAA,GAAI;YAAE,KAAK,EAAE,KAAA;QAAA,CAAO,CAAA;IAAA,SAC5B;QACJ,IAAI;YACA,IAAI,CAAA,IAAK,CAAC,CAAC,CAAC,IAAA,IAAA,CAAS,CAAA,GAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QAC5D,SACgB;YAAE,IAAI,CAAC,EAAE,MAAM,CAAC,CAAC,KAAK,CAAA;QAAA;IACtC;IACI,OAAO,EAAE;AACb,CAAC;AACD,IAAII,kBAAgB,AAACJ,UAAA,IAAQA,UAAI,CAAC,aAAa,IAAK,SAAU,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;IAC1E,IAAI,IAAA,IAAQ,SAAS,CAAC,MAAA,KAAW,CAAC,EAAE,IAAK,IAAI,CAAA,GAAI,CAAC,EAAE,CAAA,GAAI,IAAI,CAAC,MAAM,EAAE,EAAE,EAAE,CAAA,GAAI,CAAC,EAAE,CAAC,EAAE,CAAE;QACjF,IAAI,EAAA,IAAM,CAAA,CAAE,CAAA,IAAK,IAAI,CAAC,EAAE;YACpB,IAAI,CAAC,EAAE,EAAE,EAAA,GAAK,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;YACpD,EAAE,CAAC,CAAC,CAAA,GAAI,IAAI,CAAC,CAAC,CAAC;QAC3B;IACA;IACI,OAAO,EAAE,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC5D,CAAC;AAKD;;CAEA,GACA,IAAI,IAAA,GAAsB,YAAY;IACtC;;;;OAIA,GACI,SAAS,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,SAAS,EAAE,gBAAgB,EAClH,UAAU,EAAE;QACR,IAAI,KAAA,KAAU,KAAA,CAAM,EAAE;YAAE,KAAA,GAAQ,EAAE,CAAA;QAAA;QAClC,IAAI,CAAC,UAAA,GAAa,CAAA,CAAE;QACpB,IAAI,CAAC,KAAA,GAAQ,EAAE;QACf,IAAI,CAAC,MAAA,GAAS,EAAE;QAChB,IAAI,CAAC,uBAAA,GAA0B,CAAC;QAChC,IAAI,CAAC,mBAAA,GAAsB,CAAC;QAC5B,IAAI,CAAC,kBAAA,GAAqB,CAAC;QAC3B,IAAI,CAAC,MAAA,GAAS;YACV,IAAI,EAAE,0MAAc,CAAC,KAAK;QACtC,CAAS;QACD,IAAI,CAAC,OAAA,GAAU;YAAC,CAAC;YAAE,CAAC;SAAC;QACrB,IAAI,CAAC,MAAA,GAAS,KAAK;QACnB,IAAI,CAAC,SAAA,GAAY;YAAC,CAAA,CAAE;YAAE,CAAA,CAAE;SAAC;QACzB,IAAI,CAAC,IAAA,GAAO,QAAQ;QACpB,IAAI,CAAC,YAAA,GAAe,WAAW;QAC/B,IAAI,CAAC,YAAA,GAAe,YAAY;QAChC,IAAI,CAAC,IAAA,GAAO,IAAI;QAChB,IAAI,CAAC,KAAA,GAAQ,KAAK;QAClB,IAAI,GAAA,GAAM,IAAI,CAAC,GAAG,EAAE;QACpB,IAAI,CAAC,qBAAA,GAAwB,aAAa,CAAC,GAAG,EAAE;QAChD,IAAI,CAAC,kBAAA,GACD,GAAA,GAAA,CAAO,IAAI,CAAC,qBAAA,GAAwB,aAAa,EAAE,CAAC;QACxD,IAAI,CAAC,kBAAA,GAAqB,SAAA,IAAa,IAAI;QAC3C,IAAI,CAAC,SAAA,GAAY,IAAI,CAAC,QAAQ,CAAC,SAAA,KAAc,QAAQ,SAAA,KAAc,KAAA,CAAK,GAAI,SAAA,GAAY,GAAG,CAAC;QAC5F,IAAI,CAAC,QAAA,GAAW,YAAY,CAAC,QAAQ;QACrC,IAAI,CAAC,sBAAA,GAAyB,YAAY,CAAC,sBAAsB;QACjE,IAAI,CAAC,WAAA,GAAc,YAAY,CAAC,aAAa,EAAE;QAC/C,IAAI,CAAC,0BAAA,GACD,IAAI,CAAC,WAAW,CAAC,yBAAA,IAA6B,CAAC;QACnD,IAAI,UAAA,IAAc,IAAI,EAAE;YACpB,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC;QAC1C;QACQ,IAAI,CAAC,cAAA,GAAiB,YAAY,CAAC,sBAAsB,EAAE;QAC3D,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC;IAClD;IACI,IAAI,CAAC,SAAS,CAAC,WAAA,GAAc,YAAY;QACrC,OAAO,IAAI,CAAC,YAAY;IAChC,CAAK;IACD,IAAI,CAAC,SAAS,CAAC,YAAA,GAAe,SAAU,GAAG,EAAE,KAAK,EAAE;QAChD,IAAI,KAAA,IAAS,IAAA,IAAQ,IAAI,CAAC,YAAY,EAAE,EACpC,OAAO,IAAI;QACf,IAAI,GAAG,CAAC,MAAA,KAAW,CAAC,EAAE;qMAClB,OAAI,CAAC,IAAI,CAAC,yBAAA,GAA4B,GAAG,CAAC;YAC1C,OAAO,IAAI;QACvB;QACQ,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE;qMAC1B,OAAI,CAAC,IAAI,CAAC,uCAAA,GAA0C,GAAG,CAAC;YACxD,OAAO,IAAI;QACvB;QACQ,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAA,IAC7B,IAAI,CAAC,WAAW,CAAC,mBAAA,IACjB,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,GAAG,CAAC,EAAE;YAC7D,IAAI,CAAC,uBAAuB,EAAE;YAC9B,OAAO,IAAI;QACvB;QACQ,IAAI,CAAC,UAAU,CAAC,GAAG,CAAA,GAAI,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC;QAClD,OAAO,IAAI;IACnB,CAAK;IACD,IAAI,CAAC,SAAS,CAAC,aAAA,GAAgB,SAAU,UAAU,EAAE;QACjD,IAAI,GAAG,EAAE,EAAE;QACX,IAAI;YACA,IAAK,IAAI,EAAA,GAAKC,UAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,EAAE,EAAA,GAAK,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,EAAA,GAAK,EAAE,CAAC,IAAI,EAAE,CAAE;gBAC1F,IAAI,EAAA,GAAKF,QAAM,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;gBAClD,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC;YACvC;QACA,EACQ,OAAO,KAAK,EAAE;YAAE,GAAA,GAAM;gBAAE,KAAK,EAAE,KAAA;YAAA,CAAO,CAAA;QAAA,SAC9B;YACJ,IAAI;gBACA,IAAI,EAAA,IAAM,CAAC,EAAE,CAAC,IAAA,IAAA,CAAS,EAAA,GAAK,EAAE,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACnE,SACoB;gBAAE,IAAI,GAAG,EAAE,MAAM,GAAG,CAAC,KAAK,CAAA;YAAA;QAC9C;QACQ,OAAO,IAAI;IACnB,CAAK;IACL;;;;;;KAMA,GACI,IAAI,CAAC,SAAS,CAAC,QAAA,GAAW,SAAU,IAAI,EAAE,qBAAqB,EAAE,SAAS,EAAE;QACxE,IAAI,IAAI,CAAC,YAAY,EAAE,EACnB,OAAO,IAAI;QACf,IAAI,IAAI,CAAC,WAAW,CAAC,eAAA,KAAoB,CAAC,EAAE;qMACxC,OAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC;YAC/B,IAAI,CAAC,mBAAmB,EAAE;YAC1B,OAAO,IAAI;QACvB;QACQ,IAAI,IAAI,CAAC,MAAM,CAAC,MAAA,IAAU,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE;YACxD,IAAI,IAAI,CAAC,mBAAA,KAAwB,CAAC,EAAE;yMAChC,OAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC;YACpD;YACY,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;YACnB,IAAI,CAAC,mBAAmB,EAAE;QACtC;QACQ,IAAI,WAAW,CAAC,qBAAqB,CAAC,EAAE;YACpC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,EAAE;gBACzB,SAAA,GAAY,qBAAqB;YACjD;YACY,qBAAA,GAAwB,SAAS;QAC7C;QACQ,IAAI,UAAA,GAAa,kBAAkB,CAAC,qBAAqB,CAAC;QAC1D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACb,IAAI,EAAE,IAAI;YACV,UAAU,EAAE,UAAU;YACtB,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;YAC9B,sBAAsB,EAAE,CAAC;QACrC,CAAS,CAAC;QACF,OAAO,IAAI;IACnB,CAAK;IACD,IAAI,CAAC,SAAS,CAAC,OAAA,GAAU,SAAU,IAAI,EAAE;QACrC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;QACrB,OAAO,IAAI;IACnB,CAAK;IACD,IAAI,CAAC,SAAS,CAAC,QAAA,GAAW,SAAU,KAAK,EAAE;QACvC,IAAI,EAAE;QACN,CAAC,EAAA,GAAK,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,EAAEK,eAAa,CAAC,EAAE,EAAEL,QAAM,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,CAAC;QACzE,OAAO,IAAI;IACnB,CAAK;IACD,IAAI,CAAC,SAAS,CAAC,SAAA,GAAY,SAAU,MAAM,EAAE;QACzC,IAAI,IAAI,CAAC,YAAY,EAAE,EACnB,OAAO,IAAI;QACf,IAAI,CAAC,MAAA,GAAS,QAAQ,CAAC,CAAA,CAAE,EAAE,MAAM,CAAC;QAC1C,4GAAA;QACA,gHAAA;QACA,kHAAA;QACA,kEAAA;QACQ,IAAI,IAAI,CAAC,MAAM,CAAC,OAAA,IAAW,IAAA,IAAQ,OAAO,MAAM,CAAC,OAAA,KAAY,QAAQ,EAAE;YACnE,gMAAI,CAAC,IAAI,CAAC,2CAAA,GAA8C,OAAO,MAAM,CAAC,OAAA,GAAU,sBAAsB,CAAC;YACvG,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO;QACtC;QACQ,OAAO,IAAI;IACnB,CAAK;IACD,IAAI,CAAC,SAAS,CAAC,UAAA,GAAa,SAAU,IAAI,EAAE;QACxC,IAAI,IAAI,CAAC,YAAY,EAAE,EACnB,OAAO,IAAI;QACf,IAAI,CAAC,IAAA,GAAO,IAAI;QAChB,OAAO,IAAI;IACnB,CAAK;IACD,IAAI,CAAC,SAAS,CAAC,GAAA,GAAM,SAAU,OAAO,EAAE;QACpC,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE;qMACrB,OAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAA,GAAO,GAAA,GAAM,IAAI,CAAC,YAAY,CAAC,OAAA,GAAU,GAAA,GAAM,IAAI,CAAC,YAAY,CAAC,MAAA,GAAS,4CAA4C,CAAC;YACvI;QACZ;QACQ,IAAI,CAAC,MAAA,GAAS,IAAI;QAClB,IAAI,CAAC,OAAA,GAAU,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;QACrC,IAAI,CAAC,SAAA,GAAY,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC;QAC7D,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAA,GAAI,CAAC,EAAE;qMACvB,OAAI,CAAC,IAAI,CAAC,qFAAqF,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC;YAC9H,IAAI,CAAC,OAAA,GAAU,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;YACrC,IAAI,CAAC,SAAA,GAAY;gBAAC,CAAC;gBAAE,CAAC;aAAC;QACnC;QACQ,IAAI,IAAI,CAAC,mBAAA,GAAsB,CAAC,EAAE;qMAC9B,OAAI,CAAC,IAAI,CAAC,UAAA,GAAa,IAAI,CAAC,mBAAA,GAAsB,yCAAyC,CAAC;QACxG;QACQ,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC;IACvC,CAAK;IACD,IAAI,CAAC,SAAS,CAAC,QAAA,GAAW,SAAU,GAAG,EAAE;QACrC,IAAI,OAAO,GAAA,KAAQ,QAAA,IAAY,GAAA,IAAO,aAAa,CAAC,GAAG,EAAE,EAAE;YACnE,kCAAA;YACA,yCAAA;YACY,OAAO,MAAM,CAAC,GAAA,GAAM,IAAI,CAAC,kBAAkB,CAAC;QACxD;QACQ,IAAI,OAAO,GAAA,KAAQ,QAAQ,EAAE;YACzB,OAAO,cAAc,CAAC,GAAG,CAAC;QACtC;QACQ,IAAI,GAAA,YAAe,IAAI,EAAE;YACrB,OAAO,cAAc,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;QAChD;QACQ,IAAI,iBAAiB,CAAC,GAAG,CAAC,EAAE;YACxB,OAAO,GAAG;QACtB;QACQ,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACrC,iDAAA;YACA,qDAAA;YACY,OAAO,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7C;QACQ,IAAI,UAAA,GAAa,aAAa,CAAC,GAAG,EAAC,GAAI,IAAI,CAAC,qBAAqB;QACjE,OAAO,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE,cAAc,CAAC,UAAU,CAAC,CAAC;IACrE,CAAK;IACD,IAAI,CAAC,SAAS,CAAC,WAAA,GAAc,YAAY;QACrC,OAAO,IAAI,CAAC,MAAA,KAAW,KAAK;IACpC,CAAK;IACD,IAAI,CAAC,SAAS,CAAC,eAAA,GAAkB,SAAU,SAAS,EAAE,IAAI,EAAE;QACxD,IAAI,UAAA,GAAa,CAAA,CAAE;QACnB,IAAI,OAAO,SAAA,KAAc,QAAQ,EAAE;YAC/B,UAAU,wNAAC,6BAA0B,CAAA,GAAI,SAAS;QAC9D,OACa,IAAI,SAAS,EAAE;YAChB,IAAI,SAAS,CAAC,IAAI,EAAE;gBAChB,UAAU,wNAAC,0BAAuB,CAAA,GAAI,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE;YAC/E,OACiB,IAAI,SAAS,CAAC,IAAI,EAAE;gBACrB,UAAU,wNAAC,0BAAuB,CAAA,GAAI,SAAS,CAAC,IAAI;YACpE;YACY,IAAI,SAAS,CAAC,OAAO,EAAE;gBACnB,UAAU,wNAAC,6BAA0B,CAAA,GAAI,SAAS,CAAC,OAAO;YAC1E;YACY,IAAI,SAAS,CAAC,KAAK,EAAE;gBACjB,UAAU,wNAAC,gCAA6B,CAAA,GAAI,SAAS,CAAC,KAAK;YAC3E;QACA;QACA,2CAAA;QACQ,IAAI,UAAU,wNAAC,0BAAuB,CAAA,IAClC,UAAU,wNAAC,6BAA0B,CAAC,EAAE;YACxC,IAAI,CAAC,QAAQ,CAAC,kBAAkB,EAAE,UAAU,EAAE,IAAI,CAAC;QAC/D,OACa;qMACD,OAAI,CAAC,IAAI,CAAC,gCAAA,GAAmC,SAAS,CAAC;QACnE;IACA,CAAK;IACD,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,UAAU,EAAE;QAC9C,GAAG,EAAE,YAAY;YACb,OAAO,IAAI,CAAC,SAAS;QACjC,CAAS;QACD,UAAU,EAAE,KAAK;QACjB,YAAY,EAAE;IACtB,CAAK,CAAC;IACF,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE;QAC3C,GAAG,EAAE,YAAY;YACb,OAAO,IAAI,CAAC,MAAM;QAC9B,CAAS;QACD,UAAU,EAAE,KAAK;QACjB,YAAY,EAAE;IACtB,CAAK,CAAC;IACF,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,wBAAwB,EAAE;QAC5D,GAAG,EAAE,YAAY;YACb,OAAO,IAAI,CAAC,uBAAuB;QAC/C,CAAS;QACD,UAAU,EAAE,KAAK;QACjB,YAAY,EAAE;IACtB,CAAK,CAAC;IACF,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,oBAAoB,EAAE;QACxD,GAAG,EAAE,YAAY;YACb,OAAO,IAAI,CAAC,mBAAmB;QAC3C,CAAS;QACD,UAAU,EAAE,KAAK;QACjB,YAAY,EAAE;IACtB,CAAK,CAAC;IACF,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,mBAAmB,EAAE;QACvD,GAAG,EAAE,YAAY;YACb,OAAO,IAAI,CAAC,kBAAkB;QAC1C,CAAS;QACD,UAAU,EAAE,KAAK;QACjB,YAAY,EAAE;IACtB,CAAK,CAAC;IACF,IAAI,CAAC,SAAS,CAAC,YAAA,GAAe,YAAY;QACtC,IAAI,IAAI,CAAC,MAAM,EAAE;qMACb,OAAI,CAAC,IAAI,CAAC,2DAA2D,IAAI,CAAC,YAAY,CAAC,OAAA,GAAU,YAAA,GAAe,IAAI,CAAC,YAAY,CAAC,MAAA,GAAS,GAAG,CAAC;QAC3J;QACQ,OAAO,IAAI,CAAC,MAAM;IAC1B,CAAK;IACL,uDAAA;IACA,yDAAA;IACA,iDAAA;IACI,IAAI,CAAC,SAAS,CAAC,oBAAA,GAAuB,SAAU,KAAK,EAAE,KAAK,EAAE;QAC1D,IAAI,KAAK,CAAC,MAAA,IAAU,KAAK,EAAE;YACvB,OAAO,KAAK;QACxB;QACQ,OAAO,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC;IACxC,CAAK;IACL;;;;;;;;;;;KAWA,GACI,IAAI,CAAC,SAAS,CAAC,eAAA,GAAkB,SAAU,KAAK,EAAE;QAC9C,IAAI,KAAA,GAAQ,IAAI;QAChB,IAAI,KAAA,GAAQ,IAAI,CAAC,0BAA0B;QACnD,cAAA;QACQ,IAAI,KAAA,IAAS,CAAC,EAAE;YACxB,kDAAA;qMACY,OAAI,CAAC,IAAI,CAAC,8CAAA,GAAiD,KAAK,CAAC;YACjE,OAAO,KAAK;QACxB;QACA,SAAA;QACQ,IAAI,OAAO,KAAA,KAAU,QAAQ,EAAE;YAC3B,OAAO,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,KAAK,CAAC;QAC1D;QACA,mBAAA;QACQ,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACtB,OAAO,KAAK,CAAC,GAAG,CAAC,SAAU,GAAG,EAAE;gBAC5B,OAAO,OAAO,GAAA,KAAQ,WAAW,KAAK,CAAC,oBAAoB,CAAC,GAAG,EAAE,KAAK,CAAA,GAAI,GAAG;YAC7F,CAAa,CAAC;QACd;QACA,mDAAA;QACQ,OAAO,KAAK;IACpB,CAAK;IACD,OAAO,IAAI;AACf,CAAC,EAAE,CAAC;ACvXJ;;;;;;;;;;;;;;CAcA,GACA;;;CAGA,GACO,IAAI,gBAAgB;AAC3B,CAAC,SAAU,gBAAgB,EAAE;IAC7B;;;KAGA,GACI,gBAAgB,CAAC,gBAAgB,CAAC,YAAY,CAAA,GAAI,CAAC,CAAA,GAAI,YAAY;IACvE;;;KAGA,GACI,gBAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAA,GAAI,CAAC,CAAA,GAAI,QAAQ;IAC/D;;;KAGA,GACI,gBAAgB,CAAC,gBAAgB,CAAC,oBAAoB,CAAA,GAAI,CAAC,CAAA,GAAI,oBAAoB;AACvF,CAAC,EAAE,gBAAA,IAAA,CAAqB,mBAAmB,CAAA,CAAE,CAAC,CAAC;ACpC/C;;;;;;;;;;;;;;CAcA,GAEA,oCAAA,GACA,IAAI,gBAAA,GAAkC,YAAY;IAC9C,SAAS,gBAAgB,GAAG,CAChC;IACI,gBAAgB,CAAC,SAAS,CAAC,YAAA,GAAe,YAAY;QAClD,OAAO;YACH,QAAQ,EAAE,gBAAgB,CAAC,UAAU;QACjD,CAAS;IACT,CAAK;IACD,gBAAgB,CAAC,SAAS,CAAC,QAAA,GAAW,YAAY;QAC9C,OAAO,kBAAkB;IACjC,CAAK;IACD,OAAO,gBAAgB;AAC3B,CAAC,EAAE,CAAC;AC7BJ;;;;;;;;;;;;;;CAcA,GAEA,qCAAA,GACA,IAAI,eAAA,GAAiC,YAAY;IAC7C,SAAS,eAAe,GAAG,CAC/B;IACI,eAAe,CAAC,SAAS,CAAC,YAAA,GAAe,YAAY;QACjD,OAAO;YACH,QAAQ,EAAE,gBAAgB,CAAC,kBAAkB;QACzD,CAAS;IACT,CAAK;IACD,eAAe,CAAC,SAAS,CAAC,QAAA,GAAW,YAAY;QAC7C,OAAO,iBAAiB;IAChC,CAAK;IACD,OAAO,eAAe;AAC1B,CAAC,EAAE,CAAC;AC7BJ;;;;;;;;;;;;;;CAcA,GAKA;;;CAGA,GACA,IAAI,kBAAA,GAAoC,YAAY;IAChD,SAAS,kBAAkB,CAAC,MAAM,EAAE;QAChC,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;QAClB,IAAI,CAAC,KAAA,GAAQ,MAAM,CAAC,IAAI;QACxB,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YACb,kBAAkB,CAAC,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;YACvF,IAAI,CAAC,KAAA,GAAQ,IAAI,eAAe,EAAE;QAC9C;QACQ,IAAI,CAAC,oBAAA,GACD,CAAC,KAAK,MAAM,CAAC,mBAAmB,MAAM,IAAA,IAAQ,OAAO,KAAA,IAAS,EAAA,GAAK,IAAI,eAAe,EAAE;QAC5F,IAAI,CAAC,uBAAA,GACD,CAAC,KAAK,MAAM,CAAC,sBAAsB,MAAM,IAAA,IAAQ,OAAO,KAAA,IAAS,EAAA,GAAK,IAAI,gBAAgB,EAAE;QAChG,IAAI,CAAC,mBAAA,GACD,CAAC,KAAK,MAAM,CAAC,kBAAkB,MAAM,IAAA,IAAQ,OAAO,KAAA,IAAS,EAAA,GAAK,IAAI,eAAe,EAAE;QAC3F,IAAI,CAAC,sBAAA,GACD,CAAC,KAAK,MAAM,CAAC,qBAAqB,MAAM,IAAA,IAAQ,OAAO,KAAA,IAAS,EAAA,GAAK,IAAI,gBAAgB,EAAE;IACvG;IACI,kBAAkB,CAAC,SAAS,CAAC,YAAA,GAAe,SAAU,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,KAAK,EAAE;QAC3G,IAAI,yMAAgBI,QAAK,CAAC,cAAc,CAAC,OAAO,CAAC;QACjD,IAAI,CAAC,aAAA,IAAiB,8LAAC,qBAAA,AAAkB,EAAC,aAAa,CAAC,EAAE;YACtD,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,KAAK,CAAC;QACnG;QACQ,IAAI,aAAa,CAAC,QAAQ,EAAE;YACxB,IAAI,aAAa,CAAC,UAAA,4LAAa,aAAU,CAAC,OAAO,EAAE;gBAC/C,OAAO,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,KAAK,CAAC;YACtH;YACY,OAAO,IAAI,CAAC,uBAAuB,CAAC,YAAY,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,KAAK,CAAC;QACrH;QACQ,IAAI,aAAa,CAAC,UAAA,4LAAa,aAAU,CAAC,OAAO,EAAE;YAC/C,OAAO,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,KAAK,CAAC;QACjH;QACQ,OAAO,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,KAAK,CAAC;IAChH,CAAK;IACD,kBAAkB,CAAC,SAAS,CAAC,QAAA,GAAW,YAAY;QAChD,OAAO,sBAAsB,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAC,GAAI,2BAA2B,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAC,GAAI,2BAAA,GAA8B,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAC,GAAI,uBAAA,GAA0B,IAAI,CAAC,mBAAmB,CAAC,QAAQ,KAAK,0BAAA,GAA6B,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAC,GAAI,GAAG;IAChV,CAAK;IACD,OAAO,kBAAkB;AAC7B,CAAC,EAAE,CAAC;AC5DJ;;;;;;;;;;;;;;CAcA,GAGA,yFAAA,GACA,IAAI,wBAAA,GAA0C,YAAY;IACtD,SAAS,wBAAwB,CAAC,MAAM,EAAE;QACtC,IAAI,MAAA,KAAW,KAAA,CAAM,EAAE;YAAE,MAAA,GAAS,CAAC,CAAA;QAAA;QACnC,IAAI,CAAC,MAAA,GAAS,MAAM;QACpB,IAAI,CAAC,MAAA,GAAS,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;QACrC,IAAI,CAAC,WAAA,GAAc,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAA,GAAS,UAAU,CAAC;IAC/D;IACI,wBAAwB,CAAC,SAAS,CAAC,YAAA,GAAe,SAAU,OAAO,EAAE,OAAO,EAAE;QAC1E,OAAO;YACH,QAAQ,+LAAE,iBAAA,AAAc,EAAC,OAAO,CAAA,IAAK,IAAI,CAAC,WAAW,CAAC,OAAO,CAAA,GAAI,IAAI,CAAC,WAAA,GAChE,gBAAgB,CAAC,kBAAA,GACjB,gBAAgB,CAAC,UAAU;QAC7C,CAAS;IACT,CAAK;IACD,wBAAwB,CAAC,SAAS,CAAC,QAAA,GAAW,YAAY;QACtD,OAAO,oBAAA,GAAuB,IAAI,CAAC,MAAA,GAAS,GAAG;IACvD,CAAK;IACD,wBAAwB,CAAC,SAAS,CAAC,UAAA,GAAa,SAAU,KAAK,EAAE;QAC7D,IAAI,OAAO,KAAA,KAAU,YAAY,KAAK,CAAC,KAAK,CAAC,EACzC,OAAO,CAAC;QACZ,OAAO,KAAA,IAAS,CAAA,GAAI,CAAA,GAAI,KAAA,IAAS,CAAA,GAAI,CAAA,GAAI,KAAK;IACtD,CAAK;IACD,wBAAwB,CAAC,SAAS,CAAC,WAAA,GAAc,SAAU,OAAO,EAAE;QAChE,IAAI,YAAA,GAAe,CAAC;QACpB,IAAK,IAAI,CAAA,GAAI,CAAC,EAAE,CAAA,GAAI,OAAO,CAAC,MAAA,GAAS,CAAC,EAAE,CAAC,EAAE,CAAE;YACzC,IAAI,GAAA,GAAM,CAAA,GAAI,CAAC;YACf,IAAI,IAAA,GAAO,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE,GAAA,GAAM,CAAC,CAAC,EAAE,EAAE,CAAC;YACpD,YAAA,GAAe,CAAC,YAAA,GAAe,IAAI,MAAM,CAAC;QACtD;QACQ,OAAO,YAAY;IAC3B,CAAK;IACD,OAAO,wBAAwB;AACnC,CAAC,EAAE,CAAC;AClDJ;;;;;;;;;;;;;;CAcA,GAOA,IAAI,4BAAA,GAA+B,mBAAmB,CAAC,QAAQ;AAC/D,IAAI,aAAA,GAAgB,CAAC;AACrB;;;;;CAKA,GACA,+EAAA;AACA,+EAAA;AACO,SAAS,iBAAiB,GAAG;IAChC,IAAI,GAAA,GAAM,MAAM,EAAE;IAClB,OAAO;QACH,OAAO,EAAE,mBAAmB,CAAC,GAAG,CAAC;QACjC,uBAAuB,EAAE,KAAK;QAC9B,aAAa,EAAE;YACX,yBAAyB,EAAE,GAAG,CAAC,iCAAiC;YAChE,mBAAmB,EAAE,GAAG,CAAC,0BAA0B;QAC/D,CAAS;QACD,UAAU,EAAE;YACR,yBAAyB,EAAE,GAAG,CAAC,sCAAsC;YACrE,mBAAmB,EAAE,GAAG,CAAC,+BAA+B;YACxD,cAAc,EAAE,GAAG,CAAC,0BAA0B;YAC9C,eAAe,EAAE,GAAG,CAAC,2BAA2B;YAChD,2BAA2B,EAAE,GAAG,CAAC,yCAAyC;YAC1E,0BAA0B,EAAE,GAAG,CAAC,wCAAwC;QACpF,CAAS;QACD,yBAAyB,EAAE,IAAI;IACvC,CAAK;AACL;AACA;;;CAGA,GACO,SAAS,mBAAmB,CAAC,WAAW,EAAE;IAC7C,IAAI,WAAA,KAAgB,KAAA,CAAM,EAAE;QAAE,WAAA,GAAc,MAAM,EAAE,CAAA;IAAA;IACpD,OAAQ,WAAW,CAAC,mBAAmB;QACnC,KAAK,mBAAmB,CAAC,QAAQ;YAC7B,OAAO,IAAI,eAAe,EAAE;QAChC,KAAK,mBAAmB,CAAC,SAAS;YAC9B,OAAO,IAAI,gBAAgB,EAAE;QACjC,KAAK,mBAAmB,CAAC,mBAAmB;YACxC,OAAO,IAAI,kBAAkB,CAAC;gBAC1B,IAAI,EAAE,IAAI,eAAe,EAAE;YAC3C,CAAa,CAAC;QACN,KAAK,mBAAmB,CAAC,oBAAoB;YACzC,OAAO,IAAI,kBAAkB,CAAC;gBAC1B,IAAI,EAAE,IAAI,gBAAgB,EAAE;YAC5C,CAAa,CAAC;QACN,KAAK,mBAAmB,CAAC,YAAY;YACjC,OAAO,IAAI,wBAAwB,CAAC,4BAA4B,CAAC,WAAW,CAAC,CAAC;QAClF,KAAK,mBAAmB,CAAC,uBAAuB;YAC5C,OAAO,IAAI,kBAAkB,CAAC;gBAC1B,IAAI,EAAE,IAAI,wBAAwB,CAAC,4BAA4B,CAAC,WAAW,CAAC,CAAC;YAC7F,CAAa,CAAC;QACN;qMACI,OAAI,CAAC,KAAK,CAAC,iCAAiC,WAAW,CAAC,mBAAA,GAAsB,0BAAA,GAA6B,4BAAA,GAA+B,KAAK,CAAC;YAChJ,OAAO,IAAI,eAAe,EAAE;IACxC;AACA;AACA,SAAS,4BAA4B,CAAC,WAAW,EAAE;IAC/C,IAAI,WAAW,CAAC,uBAAA,KAA4B,SAAA,IACxC,WAAW,CAAC,uBAAA,KAA4B,EAAE,EAAE;iMAC5C,OAAI,CAAC,KAAK,CAAC,qDAAqD,aAAA,GAAgB,GAAG,CAAC;QACpF,OAAO,aAAa;IAC5B;IACI,IAAI,cAAc,MAAM,CAAC,WAAW,CAAC,uBAAuB,CAAC;IAC7D,IAAI,KAAK,CAAC,WAAW,CAAC,EAAE;iMACpB,OAAI,CAAC,KAAK,CAAC,6BAA6B,WAAW,CAAC,uBAAA,GAA0B,+CAAA,GAAkD,aAAA,GAAgB,GAAG,CAAC;QACpJ,OAAO,aAAa;IAC5B;IACI,IAAI,WAAA,GAAc,KAAK,WAAA,GAAc,CAAC,EAAE;iMACpC,OAAI,CAAC,KAAK,CAAC,6BAA6B,WAAW,CAAC,uBAAA,GAA0B,6DAAA,GAAgE,aAAA,GAAgB,GAAG,CAAC;QAClK,OAAO,aAAa;IAC5B;IACI,OAAO,WAAW;AACtB;ACjGA;;;;;;;;;;;;;;CAcA,GAGA;;;CAGA,GACO,SAAS,WAAW,CAAC,UAAU,EAAE;IACpC,IAAI,sBAAsB;QACtB,OAAO,EAAE,mBAAmB,EAAE;IACtC,CAAK;IACD,IAAI,cAAA,GAAiB,iBAAiB,EAAE;IACxC,IAAI,MAAA,GAAS,MAAM,CAAC,MAAM,CAAC,CAAA,CAAE,EAAE,cAAc,EAAE,mBAAmB,EAAE,UAAU,CAAC;IAC/E,MAAM,CAAC,aAAA,GAAgB,MAAM,CAAC,MAAM,CAAC,CAAA,CAAE,EAAE,cAAc,CAAC,aAAa,EAAE,UAAU,CAAC,aAAA,IAAiB,CAAA,CAAE,CAAC;IACtG,MAAM,CAAC,UAAA,GAAa,MAAM,CAAC,MAAM,CAAC,CAAA,CAAE,EAAE,cAAc,CAAC,UAAU,EAAE,UAAU,CAAC,UAAA,IAAc,CAAA,CAAE,CAAC;IAC7F,OAAO,MAAM;AACjB;AACA;;;;CAIA,GACO,SAAS,iBAAiB,CAAC,UAAU,EAAE;IAC1C,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;IAClD,IAAI,UAAA,GAAa,MAAM,CAAC,MAAM,CAAC,CAAA,CAAE,EAAE,UAAU,CAAC,UAAU,CAAC;IACzD,IAAI,eAAA,GAAkB,qBAAqB,EAAE;IACjD;;KAEA,GACI,UAAU,CAAC,mBAAA,GACP,CAAC,KAAK,CAAC,KAAK,CAAC,EAAA,GAAK,CAAC,EAAA,GAAK,CAAC,EAAA,GAAK,UAAU,CAAC,UAAU,MAAM,IAAA,IAAQ,EAAA,KAAO,KAAA,IAAS,KAAA,IAAS,EAAE,CAAC,mBAAmB,MAAM,IAAA,IAAQ,EAAA,KAAO,KAAA,CAAK,GAAI,EAAA,GAAK,CAAC,KAAK,UAAU,CAAC,aAAa,MAAM,QAAQ,EAAA,KAAO,KAAA,CAAK,GAAI,KAAA,CAAK,GAAI,EAAE,CAAC,mBAAmB,MAAM,IAAA,IAAQ,EAAA,KAAO,KAAA,IAAS,EAAA,GAAK,eAAe,CAAC,+BAA+B,MAAM,IAAA,IAAQ,EAAA,KAAO,KAAA,CAAK,GAAI,EAAA,GAAK,eAAe,CAAC,0BAA0B,MAAM,IAAA,IAAQ,EAAA,KAAO,KAAA,CAAK,GAAI,EAAA,GAAK,6BAA6B;IACrd;;KAEA,GACI,UAAU,CAAC,yBAAA,GACP,CAAC,KAAK,CAAC,KAAK,CAAC,EAAA,GAAK,CAAC,EAAA,GAAK,CAAC,EAAA,GAAK,UAAU,CAAC,UAAU,MAAM,IAAA,IAAQ,EAAA,KAAO,KAAA,IAAS,KAAA,IAAS,EAAE,CAAC,yBAAyB,MAAM,IAAA,IAAQ,EAAA,KAAO,KAAA,CAAK,GAAI,EAAA,GAAK,CAAC,KAAK,UAAU,CAAC,aAAa,MAAM,QAAQ,EAAA,KAAO,KAAA,CAAK,GAAI,KAAA,CAAK,GAAI,EAAE,CAAC,yBAAyB,MAAM,IAAA,IAAQ,EAAA,KAAO,KAAA,IAAS,EAAA,GAAK,eAAe,CAAC,sCAAsC,MAAM,IAAA,IAAQ,EAAA,KAAO,KAAA,CAAK,GAAI,EAAA,GAAK,eAAe,CAAC,iCAAiC,MAAM,IAAA,IAAQ,EAAA,KAAO,KAAA,CAAK,GAAI,EAAA,GAAK,oCAAoC;IAClf,OAAO,MAAM,CAAC,MAAM,CAAC,CAAA,CAAE,EAAE,UAAU,EAAE;QAAE,UAAU,EAAE,UAAA;IAAA,CAAY,CAAC;AACpE;ACnDA;;;;;;;;;;;;;;CAcA,GAGA;;;CAGA,GACA,IAAI,sBAAA,GAAwC,YAAY;IACpD,SAAS,sBAAsB,CAAC,SAAS,EAAE,MAAM,EAAE;QAC/C,IAAI,CAAC,SAAA,GAAY,SAAS;QAC1B,IAAI,CAAC,YAAA,GAAe,KAAK;QACzB,IAAI,CAAC,cAAA,GAAiB,EAAE;QACxB,IAAI,CAAC,kBAAA,GAAqB,CAAC;QAC3B,IAAI,GAAA,GAAM,MAAM,EAAE;QAClB,IAAI,CAAC,mBAAA,GACD,OAAA,CAAQ,MAAA,KAAW,IAAA,IAAQ,WAAW,KAAA,CAAK,GAAI,KAAA,CAAK,GAAI,MAAM,CAAC,kBAAkB,MAAM,WACjF,MAAM,CAAC,kBAAA,GACP,GAAG,CAAC,8BAA8B;QAC5C,IAAI,CAAC,aAAA,GACD,OAAA,CAAQ,MAAA,KAAW,IAAA,IAAQ,WAAW,KAAA,CAAK,GAAI,KAAA,CAAK,GAAI,MAAM,CAAC,YAAY,MAAM,WAC3E,MAAM,CAAC,YAAA,GACP,GAAG,CAAC,uBAAuB;QACrC,IAAI,CAAC,qBAAA,GACD,OAAA,CAAQ,MAAA,KAAW,IAAA,IAAQ,WAAW,KAAA,CAAK,GAAI,KAAA,CAAK,GAAI,MAAM,CAAC,oBAAoB,MAAM,WACnF,MAAM,CAAC,oBAAA,GACP,GAAG,CAAC,uBAAuB;QACrC,IAAI,CAAC,oBAAA,GACD,OAAA,CAAQ,MAAA,KAAW,IAAA,IAAQ,WAAW,KAAA,CAAK,GAAI,KAAA,CAAK,GAAI,MAAM,CAAC,mBAAmB,MAAM,WAClF,MAAM,CAAC,mBAAA,GACP,GAAG,CAAC,uBAAuB;QACrC,IAAI,CAAC,aAAA,GAAgB,IAAI,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC;QAC7D,IAAI,IAAI,CAAC,mBAAA,GAAsB,IAAI,CAAC,aAAa,EAAE;qMAC/C,OAAI,CAAC,IAAI,CAAC,mIAAmI,CAAC;YAC9I,IAAI,CAAC,mBAAA,GAAsB,IAAI,CAAC,aAAa;QACzD;IACA;IACI,sBAAsB,CAAC,SAAS,CAAC,UAAA,GAAa,YAAY;QACtD,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YAC7B,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO;QAC7C;QACQ,OAAO,IAAI,CAAC,SAAS,EAAE;IAC/B,CAAK;IACL,gBAAA;IACI,sBAAsB,CAAC,SAAS,CAAC,OAAA,GAAU,SAAU,KAAK,EAAE,cAAc,EAAE,CAAA,CAAG;IAC/E,sBAAsB,CAAC,SAAS,CAAC,KAAA,GAAQ,SAAU,IAAI,EAAE;QACrD,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YAC7B;QACZ;QACQ,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,UAAA,4LAAa,aAAU,CAAC,OAAO,MAAM,CAAC,EAAE;YAC5D;QACZ;QACQ,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;IAC/B,CAAK;IACD,sBAAsB,CAAC,SAAS,CAAC,QAAA,GAAW,YAAY;QACpD,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE;IACxC,CAAK;IACD,sBAAsB,CAAC,SAAS,CAAC,SAAA,GAAY,YAAY;QACrD,IAAI,KAAA,GAAQ,IAAI;QAChB,OAAO,OAAO,CAAC,OAAO,GACjB,IAAI,CAAC,YAAY;YAClB,OAAO,KAAK,CAAC,UAAU,EAAE;QACrC,CAAS,EACI,IAAI,CAAC,YAAY;YAClB,OAAO,KAAK,CAAC,SAAS,EAAE;QACpC,CAAS,EACI,IAAI,CAAC,YAAY;YAClB,OAAO,KAAK,CAAC,SAAS,CAAC,QAAQ,EAAE;QAC7C,CAAS,CAAC;IACV,CAAK;IACL,8BAAA,GACI,sBAAsB,CAAC,SAAS,CAAC,YAAA,GAAe,SAAU,IAAI,EAAE;QAC5D,IAAI,IAAI,CAAC,cAAc,CAAC,MAAA,IAAU,IAAI,CAAC,aAAa,EAAE;YAC9D,2BAAA;YACY,IAAI,IAAI,CAAC,kBAAA,KAAuB,CAAC,EAAE;yMAC/B,OAAI,CAAC,KAAK,CAAC,sCAAsC,CAAC;YAClE;YACY,IAAI,CAAC,kBAAkB,EAAE;YACzB;QACZ;QACQ,IAAI,IAAI,CAAC,kBAAA,GAAqB,CAAC,EAAE;YACzC,gEAAA;qMACY,OAAI,CAAC,IAAI,CAAC,UAAA,GAAa,IAAI,CAAC,kBAAA,GAAqB,qCAAqC,CAAC;YACvF,IAAI,CAAC,kBAAA,GAAqB,CAAC;QACvC;QACQ,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC;QAC9B,IAAI,CAAC,gBAAgB,EAAE;IAC/B,CAAK;IACL;;;;OAIA,GACI,sBAAsB,CAAC,SAAS,CAAC,SAAA,GAAY,YAAY;QACrD,IAAI,KAAA,GAAQ,IAAI;QAChB,OAAO,IAAI,OAAO,CAAC,SAAU,OAAO,EAAE,MAAM,EAAE;YAC1C,IAAI,QAAA,GAAW,EAAE;YAC7B,8BAAA;YACY,IAAI,KAAA,GAAQ,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,MAAA,GAAS,KAAK,CAAC,mBAAmB,CAAC;YAC9E,IAAK,IAAI,CAAA,GAAI,CAAC,EAAE,CAAA,GAAI,KAAK,EAAE,CAAA,GAAI,CAAC,EAAE,CAAC,EAAE,CAAE;gBACnC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC;YACrD;YACY,OAAO,CAAC,GAAG,CAAC,QAAQ,EACf,IAAI,CAAC,YAAY;gBAClB,OAAO,EAAE;YACzB,CAAa,EACI,KAAK,CAAC,MAAM,CAAC;QAC9B,CAAS,CAAC;IACV,CAAK;IACD,sBAAsB,CAAC,SAAS,CAAC,cAAA,GAAiB,YAAY;QAC1D,IAAI,KAAA,GAAQ,IAAI;QAChB,IAAI,CAAC,WAAW,EAAE;QAClB,IAAI,IAAI,CAAC,cAAc,CAAC,MAAA,KAAW,CAAC,EAAE;YAClC,OAAO,OAAO,CAAC,OAAO,EAAE;QACpC;QACQ,OAAO,IAAI,OAAO,CAAC,SAAU,OAAO,EAAE,MAAM,EAAE;YAC1C,IAAI,KAAA,GAAQ,UAAU,CAAC,YAAY;gBAC/C,mEAAA;gBACgB,MAAM,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC;YAC5C,CAAa,EAAE,KAAK,CAAC,oBAAoB,CAAC;YAC1C,0DAAA;YACY,mMAAO,CAAC,IAAI,CAAC,eAAe,0LAAC,UAAO,CAAC,MAAM,EAAE,CAAC,EAAE,YAAY;gBACxE,yFAAA;gBACA,8EAAA;gBACA,0CAAA;gBACgB,IAAI,KAAK;gBACT,IAAI,KAAK,CAAC,cAAc,CAAC,MAAA,IAAU,KAAK,CAAC,mBAAmB,EAAE;oBAC1D,KAAA,GAAQ,KAAK,CAAC,cAAc;oBAC5B,KAAK,CAAC,cAAA,GAAiB,EAAE;gBAC7C,OACqB;oBACD,KAAA,GAAQ,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,mBAAmB,CAAC;gBACrF;gBACgB,IAAI,QAAA,GAAW,YAAY;oBACvB,OAAO,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,SAAU,MAAM,EAAE;wBACnD,IAAI,EAAE;wBACN,YAAY,CAAC,KAAK,CAAC;wBACnB,IAAI,MAAM,CAAC,IAAA,KAAS,gBAAgB,CAAC,OAAO,EAAE;4BAC1C,OAAO,EAAE;wBACrC,OAC6B;4BACD,MAAM,CAAC,CAAC,EAAA,GAAK,MAAM,CAAC,KAAK,MAAM,IAAA,IAAQ,OAAO,KAAA,CAAK,GAAI,EAAA,GAAK,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;wBAC5I;oBACA,CAAqB,CAAC;gBACtB,CAAiB;gBACD,IAAI,gBAAA,GAAmB,IAAI;gBAC3B,IAAK,IAAI,IAAI,CAAC,EAAE,GAAA,GAAM,KAAK,CAAC,MAAM,EAAE,CAAA,GAAI,GAAG,EAAE,CAAC,EAAE,CAAE;oBAC9C,IAAI,IAAA,GAAO,KAAK,CAAC,CAAC,CAAC;oBACnB,IAAI,IAAI,CAAC,QAAQ,CAAC,sBAAA,IACd,IAAI,CAAC,QAAQ,CAAC,sBAAsB,EAAE;wBACtC,qBAAqB,IAAA,IAAQ,gBAAA,KAAqB,KAAA,CAAK,GAAI,gBAAA,GAAoB,gBAAA,GAAmB,EAAE,CAAC;wBACrG,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,sBAAsB,EAAE,CAAC;oBACrF;gBACA;gBACA,sFAAA;gBACgB,IAAI,gBAAA,KAAqB,IAAI,EAAE;oBAC3B,QAAQ,EAAE;gBAC9B,OACqB;oBACD,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAU,GAAG,EAAE;wBACxD,kBAAkB,CAAC,GAAG,CAAC;wBACvB,MAAM,CAAC,GAAG,CAAC;oBACnC,CAAqB,CAAC;gBACtB;YACA,CAAa,CAAC;QACd,CAAS,CAAC;IACV,CAAK;IACD,sBAAsB,CAAC,SAAS,CAAC,gBAAA,GAAmB,YAAY;QAC5D,IAAI,KAAA,GAAQ,IAAI;QAChB,IAAI,IAAI,CAAC,YAAY,EACjB;QACJ,IAAI,KAAA,GAAQ,YAAY;YACpB,KAAK,CAAC,YAAA,GAAe,IAAI;YACzB,KAAK,CAAC,cAAc,GACf,OAAO,CAAC,YAAY;gBACrB,KAAK,CAAC,YAAA,GAAe,KAAK;gBAC1B,IAAI,KAAK,CAAC,cAAc,CAAC,MAAA,GAAS,CAAC,EAAE;oBACjC,KAAK,CAAC,WAAW,EAAE;oBACnB,KAAK,CAAC,gBAAgB,EAAE;gBAC5C;YACA,CAAa,EACI,KAAK,CAAC,SAAU,CAAC,EAAE;gBACpB,KAAK,CAAC,YAAA,GAAe,KAAK;gBAC1B,kBAAkB,CAAC,CAAC,CAAC;YACrC,CAAa,CAAC;QACd,CAAS;QACT,6DAAA;QACQ,IAAI,IAAI,CAAC,cAAc,CAAC,MAAA,IAAU,IAAI,CAAC,mBAAmB,EAAE;YACxD,OAAO,KAAK,EAAE;QAC1B;QACQ,IAAI,IAAI,CAAC,MAAA,KAAW,SAAS,EACzB;QACJ,IAAI,CAAC,MAAA,GAAS,UAAU,CAAC,YAAY;YAAE,OAAO,KAAK,EAAE,CAAA;QAAA,CAAG,EAAE,IAAI,CAAC,qBAAqB,CAAC;QACrF,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC;IAC/B,CAAK;IACD,sBAAsB,CAAC,SAAS,CAAC,WAAA,GAAc,YAAY;QACvD,IAAI,IAAI,CAAC,MAAA,KAAW,SAAS,EAAE;YAC3B,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC;YACzB,IAAI,CAAC,MAAA,GAAS,SAAS;QACnC;IACA,CAAK;IACD,OAAO,sBAAsB;AACjC,CAAC,EAAE,CAAC;ACvNJ;;;;;;;;;;;;;;CAcA,GACA,IAAI,SAAA,GAAY,AAACH,UAAA,IAAQA,UAAI,CAAC,SAAS,IAAK,AAAC,YAAY;IACrD,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC,EAAE;QAChC,aAAA,GAAgB,MAAM,CAAC,cAAA,KAClB;YAAE,SAAS,EAAE,EAAA;QAAA,CAAA,CAAG,YAAa,KAAA,IAAS,SAAU,CAAC,EAAE,CAAC,EAAE;YAAE,CAAC,CAAC,SAAA,GAAY,CAAC,CAAA;QAAA,CAAG,CAAA,GAC3E,SAAU,CAAC,EAAE,CAAC,EAAE;YAAE,IAAK,IAAI,KAAK,CAAC,CAAE,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA,GAAI,CAAC,CAAC,CAAC,CAAC;QAAA,CAAG;QACrG,OAAO,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC;IAClC,CAAK;IACD,OAAO,SAAU,CAAC,EAAE,CAAC,EAAE;QACnB,IAAI,OAAO,CAAA,KAAM,UAAA,IAAc,CAAA,KAAM,IAAI,EACrC,MAAM,IAAI,SAAS,CAAC,sBAAA,GAAyB,MAAM,CAAC,CAAC,CAAA,GAAI,+BAA+B,CAAC;QAC7F,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC;QACnB,SAAS,EAAE,GAAG;YAAE,IAAI,CAAC,WAAA,GAAc,CAAC,CAAA;QAAA;QACpC,CAAC,CAAC,SAAA,GAAY,CAAA,KAAM,IAAA,GAAO,MAAM,CAAC,MAAM,CAAC,CAAC,CAAA,GAAA,CAAK,EAAE,CAAC,SAAA,GAAY,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC;IAC5F,CAAK;AACL,CAAC,GAAG;AAEJ,IAAI,qBAAoC,SAAU,MAAM,EAAE;IACtD,SAAS,CAAC,kBAAkB,EAAE,MAAM,CAAC;IACrC,SAAS,kBAAkB,GAAG;QAC1B,OAAO,MAAA,KAAW,IAAA,IAAQ,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAA,IAAK,IAAI;IACvE;IACI,kBAAkB,CAAC,SAAS,CAAC,UAAA,GAAa,YAAY,CAAG;IACzD,OAAO,kBAAkB;AAC7B,CAAC,CAAC,sBAAsB,CAAC,CAAC;ACtC1B;;;;;;;;;;;;;;CAcA,GACA,IAAI,aAAA,GAAgB,CAAC;AACrB,IAAI,cAAA,GAAiB,EAAE;AACvB,IAAI,iBAAA,GAAmC,YAAY;IAC/C,SAAS,iBAAiB,GAAG;QACjC;;;SAGA,GACQ,IAAI,CAAC,eAAA,GAAkB,cAAc,CAAC,cAAc,CAAC;QAC7D;;;SAGA,GACQ,IAAI,CAAC,cAAA,GAAiB,cAAc,CAAC,aAAa,CAAC;IAC3D;IACI,OAAO,iBAAiB;AAC5B,CAAC,EAAE,CAAC;AAEJ,IAAI,+IAAsB,CAAC,WAAW,CAAC,cAAc,CAAC;AACtD,SAAS,cAAc,CAAC,KAAK,EAAE;IAC3B,OAAO,SAAS,UAAU,GAAG;QACzB,IAAK,IAAI,CAAA,GAAI,CAAC,EAAE,CAAA,GAAI,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAE;YAC5C,wDAAA;YACA,gIAAA;YACY,aAAa,CAAC,aAAa,CAAC,AAAC,IAAI,CAAC,MAAM,EAAC,GAAI,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,KAAM,CAAC,EAAE,CAAA,GAAI,CAAC,CAAC;QACvF;QACA,sFAAA;QACQ,IAAK,IAAI,CAAA,GAAI,CAAC,EAAE,CAAA,GAAI,KAAK,EAAE,CAAC,EAAE,CAAE;YAC5B,IAAI,aAAa,CAAC,CAAC,CAAA,GAAI,CAAC,EAAE;gBACtB;YAChB,OACiB,IAAI,CAAA,KAAM,KAAA,GAAQ,CAAC,EAAE;gBACtB,aAAa,CAAC,KAAA,GAAQ,CAAC,CAAA,GAAI,CAAC;YAC5C;QACA;QACQ,OAAO,aAAa,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC;IACtD,CAAK;AACL;ACpDA;;;;;;;;;;;;;;CAcA,GAMA;;CAEA,GACA,IAAI,MAAA,GAAwB,YAAY;IACxC;;KAEA,GACI,SAAS,MAAM,CAAC,sBAAsB,EAAE,MAAM,EAAE,eAAe,EAAE;QAC7D,IAAI,CAAC,eAAA,GAAkB,eAAe;QACtC,IAAI,WAAA,GAAc,WAAW,CAAC,MAAM,CAAC;QACrC,IAAI,CAAC,QAAA,GAAW,WAAW,CAAC,OAAO;QACnC,IAAI,CAAC,cAAA,GAAiB,WAAW,CAAC,aAAa;QAC/C,IAAI,CAAC,WAAA,GAAc,WAAW,CAAC,UAAU;QACzC,IAAI,CAAC,YAAA,GAAe,MAAM,CAAC,WAAA,IAAe,IAAI,iBAAiB,EAAE;QACjE,IAAI,CAAC,QAAA,GAAW,eAAe,CAAC,QAAQ;QACxC,IAAI,CAAC,sBAAA,GAAyB,sBAAsB;IAC5D;IACA;;;KAGA,GACI,MAAM,CAAC,SAAS,CAAC,SAAA,GAAY,SAAU,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE;QAC3D,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE;QACd,IAAI,OAAA,KAAY,KAAA,CAAM,EAAE;YAAE,OAAA,GAAU,CAAA,CAAE,CAAA;QAAA;QACtC,IAAI,OAAA,KAAY,KAAA,CAAM,EAAE;YAAE,OAAA,4LAAU,GAAG,CAAC,MAAO,CAAC,MAAM,EAAE,CAAA;QAAA;QAChE,wEAAA;QACQ,IAAI,OAAO,CAAC,IAAI,EAAE;YACd,OAAA,4LAAU,GAAG,CAAC,IAAK,CAAC,UAAU,CAAC,OAAO,CAAC;QACnD;QACQ,IAAI,UAAA,4LAAa,GAAG,CAAC,IAAK,CAAC,OAAO,CAAC,OAAO,CAAC;QAC3C,IAAI,mBAAmB,CAAC,OAAO,CAAC,EAAE;qMAC9B,GAAG,CAAC,GAAI,CAAC,KAAK,CAAC,iDAAiD,CAAC;YACjE,IAAI,gBAAA,4LAAmB,GAAG,CAAC,IAAK,CAAC,eAAe,0LAAC,GAAG,CAAC,mBAAoB,CAAC;YAC1E,OAAO,gBAAgB;QACnC;QACQ,IAAI,iBAAA,GAAoB,eAAe,IAAA,IAAQ,UAAA,KAAe,KAAA,CAAK,GAAI,KAAA,CAAK,GAAI,UAAU,CAAC,WAAW,EAAE;QACxG,IAAI,SAAS,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE;QAC/C,IAAI,OAAO;QACX,IAAI,UAAU;QACd,IAAI,YAAY;QAChB,IAAI,CAAC,iBAAA,IACD,0LAAC,GAAG,CAAC,IAAK,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,EAAE;YAC9D,iBAAA;YACY,OAAA,GAAU,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE;QACzD,OACa;YACb,kBAAA;YACY,OAAA,GAAU,iBAAiB,CAAC,OAAO;YACnC,UAAA,GAAa,iBAAiB,CAAC,UAAU;YACzC,YAAA,GAAe,iBAAiB,CAAC,MAAM;QACnD;QACQ,IAAI,QAAA,GAAW,CAAC,EAAA,GAAK,OAAO,CAAC,IAAI,MAAM,IAAA,IAAQ,EAAA,KAAO,KAAA,CAAK,GAAI,EAAA,4LAAK,GAAG,CAAC,OAAQ,CAAC,QAAQ;QACzF,IAAI,KAAA,GAAQ,CAAC,CAAC,EAAA,GAAK,OAAO,CAAC,KAAK,MAAM,QAAQ,EAAA,KAAO,KAAA,CAAK,GAAI,EAAA,GAAK,EAAE,EAAE,GAAG,CAAC,SAAU,IAAI,EAAE;YACvF,OAAO;gBACH,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,UAAU,EAAE,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC;YAC/D,CAAa;QACb,CAAS,CAAC;QACF,IAAI,aAAa,kBAAkB,CAAC,OAAO,CAAC,UAAU,CAAC;QAC/D,yBAAA;QACQ,IAAI,iBAAiB,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,KAAK,CAAC;QACpG,aAAa,CAAC,KAAK,cAAc,CAAC,UAAU,MAAM,IAAA,IAAQ,OAAO,KAAA,IAAS,EAAA,GAAK,UAAU;QACzF,IAAI,UAAA,GAAa,cAAc,CAAC,QAAA,8LAAa,GAAG,CAAC,eAAgB,CAAC,kBAAA,4LAC5D,GAAG,CAAC,SAAU,CAAC,OAAA,GACf,GAAG,CAAC,kMAAU,CAAC,IAAI;QACzB,IAAI,WAAA,GAAc;YAAE,OAAO,EAAE,OAAO;YAAE,MAAM,EAAE,MAAM;YAAE,UAAU,EAAE,UAAU;YAAE,UAAU,EAAE;QAAA,CAAY;QACtG,IAAI,cAAc,CAAC,QAAA,8LAAa,GAAG,CAAC,eAAgB,CAAC,UAAU,EAAE;YAC7D,GAAG,CAAC,4LAAI,CAAC,KAAK,CAAC,+DAA+D,CAAC;YAC/E,IAAI,gBAAA,4LAAmB,GAAG,CAAC,IAAK,CAAC,eAAe,CAAC,WAAW,CAAC;YAC7D,OAAO,gBAAgB;QACnC;QACA,2EAAA;QACA,4EAAA;QACQ,IAAI,cAAA,GAAiB,kBAAkB,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,cAAc,CAAC,UAAU,CAAC,CAAC;QAC7F,IAAI,IAAA,GAAO,IAAI,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,EAAE,OAAO,CAAC,SAAS,EAAE,SAAS,EAAE,cAAc,CAAC;QAClI,OAAO,IAAI;IACnB,CAAK;IACD,MAAM,CAAC,SAAS,CAAC,eAAA,GAAkB,SAAU,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;QACjE,IAAI,IAAI;QACR,IAAI,GAAG;QACP,IAAI,EAAE;QACN,IAAI,SAAS,CAAC,MAAA,GAAS,CAAC,EAAE;YACtB;QACZ,OACa,IAAI,SAAS,CAAC,MAAA,KAAW,CAAC,EAAE;YAC7B,EAAA,GAAK,IAAI;QACrB,OACa,IAAI,SAAS,CAAC,MAAA,KAAW,CAAC,EAAE;YAC7B,IAAA,GAAO,IAAI;YACX,EAAA,GAAK,IAAI;QACrB,OACa;YACD,IAAA,GAAO,IAAI;YACX,GAAA,GAAM,IAAI;YACV,EAAA,GAAK,IAAI;QACrB;QACQ,IAAI,gBAAgB,GAAA,KAAQ,IAAA,IAAQ,QAAQ,KAAA,IAAS,GAAA,GAAM,GAAG,CAAC,+LAAO,CAAC,MAAM,EAAE;QAC/E,IAAI,IAAA,GAAO,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,aAAa,CAAC;QACpD,IAAI,kBAAA,4LAAqB,GAAG,CAAC,IAAK,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC;QAC/D,gMAAO,GAAG,CAAC,MAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC;IACxE,CAAK;IACL,8CAAA,GACI,MAAM,CAAC,SAAS,CAAC,gBAAA,GAAmB,YAAY;QAC5C,OAAO,IAAI,CAAC,cAAc;IAClC,CAAK;IACL,2CAAA,GACI,MAAM,CAAC,SAAS,CAAC,aAAA,GAAgB,YAAY;QACzC,OAAO,IAAI,CAAC,WAAW;IAC/B,CAAK;IACD,MAAM,CAAC,SAAS,CAAC,sBAAA,GAAyB,YAAY;QAClD,OAAO,IAAI,CAAC,eAAe,CAAC,sBAAsB,EAAE;IAC5D,CAAK;IACD,OAAO,MAAM;AACjB,CAAC,EAAE,CAAC;ACrIJ;;;;;;;;;;;;;;CAcA,GACA,IAAI,QAAA,GAAW,AAACA,UAAA,IAAQA,UAAI,CAAC,QAAQ,IAAK,SAAS,CAAC,EAAE;IAClD,IAAI,IAAI,OAAO,WAAW,UAAA,IAAc,MAAM,CAAC,QAAQ,EAAE,CAAA,GAAI,CAAA,IAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA,GAAI,CAAC;IAC7E,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;IACvB,IAAI,CAAA,IAAK,OAAO,CAAC,CAAC,MAAA,KAAW,QAAQ,EAAE,OAAO;QAC1C,IAAI,EAAE,YAAY;YACd,IAAI,CAAA,IAAK,CAAA,IAAK,CAAC,CAAC,MAAM,EAAE,CAAA,GAAI,KAAA,CAAM;YAClC,OAAO;gBAAE,KAAK,EAAE,CAAA,IAAK,CAAC,CAAC,CAAC,EAAE,CAAC;gBAAE,IAAI,EAAE,CAAC;YAAA,CAAG;QACnD;IACA,CAAK;IACD,MAAM,IAAI,SAAS,CAAC,IAAI,yBAAA,GAA4B,iCAAiC,CAAC;AAC1F,CAAC;AAED;;;CAGA,GACA,IAAI,kBAAA,GAAoC,YAAY;IAChD,SAAS,kBAAkB,CAAC,eAAe,EAAE;QACzC,IAAI,CAAC,eAAA,GAAkB,eAAe;IAC9C;IACI,kBAAkB,CAAC,SAAS,CAAC,UAAA,GAAa,YAAY;QAClD,IAAI,GAAG,EAAE,EAAE;QACX,IAAI,QAAA,GAAW,EAAE;QACjB,IAAI;YACA,IAAK,IAAI,EAAA,GAAK,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,EAAA,GAAK,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,EAAA,GAAK,EAAE,CAAC,IAAI,EAAE,CAAE;gBACpF,IAAI,aAAA,GAAgB,EAAE,CAAC,KAAK;gBAC5B,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC;YACzD;QACA,EACQ,OAAO,KAAK,EAAE;YAAE,GAAA,GAAM;gBAAE,KAAK,EAAE,KAAA;YAAA,CAAO,CAAA;QAAA,SAC9B;YACJ,IAAI;gBACA,IAAI,EAAA,IAAM,CAAC,EAAE,CAAC,IAAA,IAAA,CAAS,EAAA,GAAK,EAAE,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACnE,SACoB;gBAAE,IAAI,GAAG,EAAE,MAAM,GAAG,CAAC,KAAK,CAAA;YAAA;QAC9C;QACQ,OAAO,IAAI,OAAO,CAAC,SAAU,OAAO,EAAE;YAClC,OAAO,CAAC,GAAG,CAAC,QAAQ,EACf,IAAI,CAAC,YAAY;gBAClB,OAAO,EAAE;YACzB,CAAa,EACI,KAAK,CAAC,SAAU,KAAK,EAAE;gBACxB,kBAAkB,CAAC,KAAA,IAAS,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;gBAC/E,OAAO,EAAE;YACzB,CAAa,CAAC;QACd,CAAS,CAAC;IACV,CAAK;IACD,kBAAkB,CAAC,SAAS,CAAC,OAAA,GAAU,SAAU,IAAI,EAAE,OAAO,EAAE;QAC5D,IAAI,GAAG,EAAE,EAAE;QACX,IAAI;YACA,IAAK,IAAI,EAAA,GAAK,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,EAAA,GAAK,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,EAAA,GAAK,EAAE,CAAC,IAAI,EAAE,CAAE;gBACpF,IAAI,aAAA,GAAgB,EAAE,CAAC,KAAK;gBAC5B,aAAa,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC;YACpD;QACA,EACQ,OAAO,KAAK,EAAE;YAAE,GAAA,GAAM;gBAAE,KAAK,EAAE,KAAA;YAAA,CAAO,CAAA;QAAA,SAC9B;YACJ,IAAI;gBACA,IAAI,EAAA,IAAM,CAAC,EAAE,CAAC,IAAA,IAAA,CAAS,EAAA,GAAK,EAAE,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACnE,SACoB;gBAAE,IAAI,GAAG,EAAE,MAAM,GAAG,CAAC,KAAK,CAAA;YAAA;QAC9C;IACA,CAAK;IACD,kBAAkB,CAAC,SAAS,CAAC,KAAA,GAAQ,SAAU,IAAI,EAAE;QACjD,IAAI,GAAG,EAAE,EAAE;QACX,IAAI;YACA,IAAK,IAAI,EAAA,GAAK,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,EAAA,GAAK,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,EAAA,GAAK,EAAE,CAAC,IAAI,EAAE,CAAE;gBACpF,IAAI,aAAA,GAAgB,EAAE,CAAC,KAAK;gBAC5B,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC;YACzC;QACA,EACQ,OAAO,KAAK,EAAE;YAAE,GAAA,GAAM;gBAAE,KAAK,EAAE,KAAA;YAAA,CAAO,CAAA;QAAA,SAC9B;YACJ,IAAI;gBACA,IAAI,EAAA,IAAM,CAAC,EAAE,CAAC,IAAA,IAAA,CAAS,EAAA,GAAK,EAAE,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACnE,SACoB;gBAAE,IAAI,GAAG,EAAE,MAAM,GAAG,CAAC,KAAK,CAAA;YAAA;QAC9C;IACA,CAAK;IACD,kBAAkB,CAAC,SAAS,CAAC,QAAA,GAAW,YAAY;QAChD,IAAI,GAAG,EAAE,EAAE;QACX,IAAI,QAAA,GAAW,EAAE;QACjB,IAAI;YACA,IAAK,IAAI,EAAA,GAAK,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,EAAA,GAAK,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,EAAA,GAAK,EAAE,CAAC,IAAI,EAAE,CAAE;gBACpF,IAAI,aAAA,GAAgB,EAAE,CAAC,KAAK;gBAC5B,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;YACvD;QACA,EACQ,OAAO,KAAK,EAAE;YAAE,GAAA,GAAM;gBAAE,KAAK,EAAE,KAAA;YAAA,CAAO,CAAA;QAAA,SAC9B;YACJ,IAAI;gBACA,IAAI,EAAA,IAAM,CAAC,EAAE,CAAC,IAAA,IAAA,CAAS,EAAA,GAAK,EAAE,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACnE,SACoB;gBAAE,IAAI,GAAG,EAAE,MAAM,GAAG,CAAC,KAAK,CAAA;YAAA;QAC9C;QACQ,OAAO,IAAI,OAAO,CAAC,SAAU,OAAO,EAAE,MAAM,EAAE;YAC1C,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,YAAY;gBACnC,OAAO,EAAE;YACzB,CAAa,EAAE,MAAM,CAAC;QACtB,CAAS,CAAC;IACV,CAAK;IACD,OAAO,kBAAkB;AAC7B,CAAC,EAAE,CAAC;ACrHJ;;;;;;;;;;;;;;CAcA,GACA,0CAAA,GACA,IAAI,iBAAA,GAAmC,YAAY;IAC/C,SAAS,iBAAiB,GAAG,CACjC;IACI,iBAAiB,CAAC,SAAS,CAAC,OAAA,GAAU,SAAU,KAAK,EAAE,QAAQ,EAAE,CAAA,CAAG;IACpE,iBAAiB,CAAC,SAAS,CAAC,KAAA,GAAQ,SAAU,KAAK,EAAE,CAAA,CAAG;IACxD,iBAAiB,CAAC,SAAS,CAAC,QAAA,GAAW,YAAY;QAC/C,OAAO,OAAO,CAAC,OAAO,EAAE;IAChC,CAAK;IACD,iBAAiB,CAAC,SAAS,CAAC,UAAA,GAAa,YAAY;QACjD,OAAO,OAAO,CAAC,OAAO,EAAE;IAChC,CAAK;IACD,OAAO,iBAAiB;AAC5B,CAAC,EAAE,CAAC;AC5BJ;;;;;;;;;;;;;;CAcA,GACA,IAAI,MAAA,GAAS,AAACA,UAAA,IAAQA,UAAI,CAAC,MAAM,IAAK,SAAU,CAAC,EAAE,CAAC,EAAE;IAClD,IAAI,CAAA,GAAI,OAAO,MAAA,KAAW,UAAA,IAAc,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC;IAC1D,IAAI,CAAC,CAAC,EAAE,OAAO,CAAC;IAChB,IAAI,CAAA,GAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAA,GAAK,EAAE,EAAE,CAAC;IAChC,IAAI;QACA,MAAO,CAAC,CAAA,KAAM,KAAK,CAAA,IAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAA,GAAI,CAAC,CAAC,IAAI,EAAE,EAAE,IAAI,CAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;IAClF,EACI,OAAO,KAAK,EAAE;QAAE,CAAA,GAAI;YAAE,KAAK,EAAE,KAAA;QAAA,CAAO,CAAA;IAAA,SAC5B;QACJ,IAAI;YACA,IAAI,CAAA,IAAK,CAAC,CAAC,CAAC,IAAA,IAAA,CAAS,CAAA,GAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QAC5D,SACgB;YAAE,IAAI,CAAC,EAAE,MAAM,CAAC,CAAC,KAAK,CAAA;QAAA;IACtC;IACI,OAAO,EAAE;AACb,CAAC;AACD,IAAI,gBAAiBA,AAAD,UAAC,IAAQA,UAAI,CAAC,aAAa,IAAK,SAAU,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;IAC1E,IAAI,IAAA,IAAQ,SAAS,CAAC,MAAA,KAAW,CAAC,EAAE,IAAK,IAAI,CAAA,GAAI,CAAC,EAAE,CAAA,GAAI,IAAI,CAAC,MAAM,EAAE,EAAE,EAAE,CAAA,GAAI,CAAC,EAAE,CAAC,EAAE,CAAE;QACjF,IAAI,EAAA,IAAM,CAAA,CAAE,CAAA,IAAK,IAAI,CAAC,EAAE;YACpB,IAAI,CAAC,EAAE,EAAE,EAAA,GAAK,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;YACpD,EAAE,CAAC,CAAC,CAAA,GAAI,IAAI,CAAC,CAAC,CAAC;QAC3B;IACA;IACI,OAAO,EAAE,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC5D,CAAC;AAUM,IAAI,eAAe;AAC1B,CAAC,SAAU,eAAe,EAAE;IACxB,eAAe,CAAC,eAAe,CAAC,UAAU,CAAA,GAAI,CAAC,CAAA,GAAI,UAAU;IAC7D,eAAe,CAAC,eAAe,CAAC,SAAS,CAAA,GAAI,CAAC,CAAA,GAAI,SAAS;IAC3D,eAAe,CAAC,eAAe,CAAC,OAAO,CAAA,GAAI,CAAC,CAAA,GAAI,OAAO;IACvD,eAAe,CAAC,eAAe,CAAC,YAAY,CAAA,GAAI,CAAC,CAAA,GAAI,YAAY;AACrE,CAAC,EAAE,eAAA,IAAA,CAAoB,kBAAkB,CAAA,CAAE,CAAC,CAAC;AAC7C;;CAEA,GACA,IAAI,mBAAA,GAAqC,YAAY;IACjD,SAAS,mBAAmB,CAAC,MAAM,EAAE;QACjC,IAAI,MAAA,KAAW,KAAA,CAAM,EAAE;YAAE,MAAA,GAAS,CAAA,CAAE,CAAA;QAAA;QACpC,IAAI,EAAE,EAAE,EAAE;QACV,IAAI,CAAC,yBAAA,GAA4B,EAAE;QACnC,IAAI,CAAC,QAAA,GAAW,IAAI,GAAG,EAAE;QACzB,IAAI,YAAA,GAAe,KAAK,CAAC,CAAA,CAAE,EAAE,iBAAiB,EAAE,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAC;QAC5E,IAAI,CAAC,QAAA,GAAW,CAAC,KAAK,YAAY,CAAC,QAAQ,MAAM,QAAQ,EAAA,KAAO,KAAA,CAAK,GAAI,EAAA,yLAAK,WAAQ,CAAC,KAAK,EAAE;QAC9F,IAAI,YAAY,CAAC,yBAAyB,EAAE;YACxC,IAAI,CAAC,QAAA,GAAW,iMAAQ,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC;QACnE;QACQ,IAAI,CAAC,OAAA,GAAU,MAAM,CAAC,MAAM,CAAC,CAAA,CAAE,EAAE,YAAY,EAAE;YAC3C,QAAQ,EAAE,IAAI,CAAC,QAAQ;QACnC,CAAS,CAAC;QACF,IAAI,CAAC,EAAA,GAAK,MAAM,CAAC,cAAc,MAAM,IAAA,IAAQ,EAAA,KAAO,KAAA,CAAK,GAAI,KAAA,IAAS,EAAE,CAAC,MAAM,EAAE;YAC7E,IAAI,CAAC,yBAAA,GAA4B,aAAa,CAAC,EAAE,EAAE,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,EAAE,KAAK,CAAC;YACxF,IAAI,CAAC,mBAAA,GAAsB,IAAI,kBAAkB,CAAC,IAAI,CAAC,yBAAyB,CAAC;QAC7F,OACa;YACD,IAAI,eAAA,GAAkB,IAAI,CAAC,qBAAqB,EAAE;YAClD,IAAI,eAAA,KAAoB,SAAS,EAAE;gBAC/B,IAAI,cAAA,GAAiB,IAAI,kBAAkB,CAAC,eAAe,CAAC;gBAC5D,IAAI,CAAC,mBAAA,GAAsB,cAAc;YACzD,OACiB;gBACD,IAAI,CAAC,mBAAA,GAAsB,IAAI,iBAAiB,EAAE;YAClE;QACA;IACA;IACI,mBAAmB,CAAC,SAAS,CAAC,SAAA,GAAY,SAAU,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE;QACxE,IAAI,GAAA,GAAM,IAAA,GAAO,GAAA,GAAA,CAAO,OAAA,IAAW,EAAE,CAAA,GAAI,GAAA,GAAA,CAAO,CAAC,OAAA,KAAY,QAAQ,OAAA,KAAY,KAAA,IAAS,KAAA,CAAK,GAAI,OAAO,CAAC,SAAS,KAAK,EAAE,CAAC;QAC5H,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YACzB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,MAAM,CAAC;gBAAE,IAAI,EAAE,IAAI;gBAAE,OAAO,EAAE,OAAO;gBAAE,SAAS,EAAE,YAAY,IAAA,IAAQ,OAAA,KAAY,KAAA,CAAK,GAAI,KAAA,CAAK,GAAI,OAAO,CAAC,SAAA;YAAA,CAAW,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACpL;QACA,oEAAA;QACQ,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC;IACrC,CAAK;IACL;;;;KAIA,GACI,mBAAmB,CAAC,SAAS,CAAC,gBAAA,GAAmB,SAAU,aAAa,EAAE;QACtE,IAAI,IAAI,CAAC,yBAAyB,CAAC,MAAA,KAAW,CAAC,EAAE;YACzD,yEAAA;YACA,4BAAA;YACY,IAAI,CAAC,mBAAA,CACA,QAAQ,GACR,KAAK,CAAC,SAAU,GAAG,EAAE;gBACtB,gMAAO,OAAI,CAAC,KAAK,CAAC,uDAAuD,EAAE,GAAG,CAAC;YAC/F,CAAa,CAAC;QACd;QACQ,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,aAAa,CAAC;QAClD,IAAI,CAAC,mBAAA,GAAsB,IAAI,kBAAkB,CAAC,IAAI,CAAC,yBAAyB,CAAC;IACzF,CAAK;IACD,mBAAmB,CAAC,SAAS,CAAC,sBAAA,GAAyB,YAAY;QAC/D,OAAO,IAAI,CAAC,mBAAmB;IACvC,CAAK;IACL;;;;;;KAMA,GACI,mBAAmB,CAAC,SAAS,CAAC,QAAA,GAAW,SAAU,MAAM,EAAE;QACvD,IAAI,MAAA,KAAW,KAAA,CAAM,EAAE;YAAE,MAAA,GAAS,CAAA,CAAE,CAAA;QAAA;iMACpCG,QAAK,CAAC,uBAAuB,CAAC,IAAI,CAAC;QACnC,IAAI,MAAM,CAAC,UAAA,KAAe,SAAS,EAAE;YACjC,MAAM,CAAC,UAAA,GAAa,IAAI,CAAC,uBAAuB,EAAE;QAC9D;QACQ,IAAI,MAAM,CAAC,cAAc,EAAE;qMACvB,UAAO,CAAC,uBAAuB,CAAC,MAAM,CAAC,cAAc,CAAC;QAClE;QACQ,IAAI,MAAM,CAAC,UAAU,EAAE;qMACnB,cAAW,CAAC,mBAAmB,CAAC,MAAM,CAAC,UAAU,CAAC;QAC9D;IACA,CAAK;IACD,mBAAmB,CAAC,SAAS,CAAC,UAAA,GAAa,YAAY;QACnD,IAAI,OAAA,GAAU,IAAI,CAAC,OAAO,CAAC,uBAAuB;QAClD,IAAI,QAAA,GAAW,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,SAAU,aAAa,EAAE;YACvE,OAAO,IAAI,OAAO,CAAC,SAAU,OAAO,EAAE;gBAClC,IAAI,KAAK;gBACT,IAAI,eAAA,GAAkB,UAAU,CAAC,YAAY;oBACzC,OAAO,CAAC,IAAI,KAAK,CAAC,4DAAA,GAA+D,OAAA,GAAU,KAAK,CAAC,CAAC;oBAClG,KAAA,GAAQ,eAAe,CAAC,OAAO;gBACnD,CAAiB,EAAE,OAAO,CAAC;gBACX,cACK,UAAU,GACV,IAAI,CAAC,YAAY;oBAClB,YAAY,CAAC,eAAe,CAAC;oBAC7B,IAAI,KAAA,KAAU,eAAe,CAAC,OAAO,EAAE;wBACnC,KAAA,GAAQ,eAAe,CAAC,QAAQ;wBAChC,OAAO,CAAC,KAAK,CAAC;oBACtC;gBACA,CAAiB,EACI,KAAK,CAAC,SAAU,KAAK,EAAE;oBACxB,YAAY,CAAC,eAAe,CAAC;oBAC7B,KAAA,GAAQ,eAAe,CAAC,KAAK;oBAC7B,OAAO,CAAC,KAAK,CAAC;gBAClC,CAAiB,CAAC;YAClB,CAAa,CAAC;QACd,CAAS,CAAC;QACF,OAAO,IAAI,OAAO,CAAC,SAAU,OAAO,EAAE,MAAM,EAAE;YAC1C,OAAO,CAAC,GAAG,CAAC,QAAQ,EACf,IAAI,CAAC,SAAU,OAAO,EAAE;gBACzB,IAAI,SAAS,OAAO,CAAC,MAAM,CAAC,SAAU,MAAM,EAAE;oBAAE,OAAO,WAAW,eAAe,CAAC,QAAQ,CAAA;gBAAA,CAAG,CAAC;gBAC9F,IAAI,MAAM,CAAC,MAAA,GAAS,CAAC,EAAE;oBACnB,MAAM,CAAC,MAAM,CAAC;gBAClC,OACqB;oBACD,OAAO,EAAE;gBAC7B;YACA,CAAa,EACI,KAAK,CAAC,SAAU,KAAK,EAAE;gBAAE,OAAO,MAAM,CAAC;oBAAC,KAAK;iBAAC,CAAC,CAAA;YAAA,CAAG,CAAC;QACpE,CAAS,CAAC;IACV,CAAK;IACD,mBAAmB,CAAC,SAAS,CAAC,QAAA,GAAW,YAAY;QACjD,OAAO,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE;IAClD,CAAK;IACL;;;;;;KAMA,GACI,mBAAmB,CAAC,SAAS,CAAC,cAAA,GAAiB,SAAU,IAAI,EAAE;QAC3D,IAAI,EAAE;QACN,OAAO,CAAC,EAAA,GAAK,IAAI,CAAC,WAAW,CAAC,sBAAsB,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,IAAA,IAAQ,EAAA,KAAO,KAAA,CAAK,GAAI,KAAA,CAAK,GAAI,EAAE,EAAE;IACjH,CAAK;IACD,mBAAmB,CAAC,SAAS,CAAC,gBAAA,GAAmB,SAAU,IAAI,EAAE;QAC7D,IAAI,EAAE;QACN,OAAO,CAAC,EAAA,GAAK,IAAI,CAAC,WAAW,CAAC,oBAAoB,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,IAAA,IAAQ,EAAA,KAAO,KAAA,CAAK,GAAI,KAAA,CAAK,GAAI,EAAE,EAAE;IAC/G,CAAK;IACD,mBAAmB,CAAC,SAAS,CAAC,uBAAA,GAA0B,YAAY;QAChE,IAAI,KAAA,GAAQ,IAAI;QACxB,sDAAA;QACQ,IAAI,qBAAA,GAAwB,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC,gBAAgB,CAAC,CAAC;QAC1E,IAAI,WAAA,GAAc,qBAAqB,CAAC,GAAG,CAAC,SAAU,IAAI,EAAE;YACxD,IAAI,aAAa,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC;YAC3C,IAAI,CAAC,UAAU,EAAE;wMACb,QAAI,CAAC,IAAI,CAAC,kBAAkB,IAAA,GAAO,2DAA2D,CAAC;YAC/G;YACY,OAAO,UAAU;QAC7B,CAAS,CAAC;QACF,IAAI,gBAAA,GAAmB,WAAW,CAAC,MAAM,CAAC,SAAU,IAAI,EAAE,IAAI,EAAE;YAC5D,IAAI,IAAI,EAAE;gBACN,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;YAC/B;YACY,OAAO,IAAI;QACvB,CAAS,EAAE,EAAE,CAAC;QACN,IAAI,gBAAgB,CAAC,MAAA,KAAW,CAAC,EAAE;YAC/B;QACZ,OACa,IAAI,qBAAqB,CAAC,MAAA,KAAW,CAAC,EAAE;YACzC,OAAO,gBAAgB,CAAC,CAAC,CAAC;QACtC,OACa;YACD,OAAO,IAAI,mBAAmB,CAAC;gBAC3B,WAAW,EAAE,gBAAgB;YAC7C,CAAa,CAAC;QACd;IACA,CAAK;IACD,mBAAmB,CAAC,SAAS,CAAC,qBAAA,GAAwB,YAAY;QAC9D,IAAI,YAAA,GAAe,MAAM,EAAE,CAAC,oBAAoB;QAChD,IAAI,YAAA,KAAiB,UAAU,YAAA,KAAiB,EAAE,EAC9C;QACJ,IAAI,WAAW,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC;QAClD,IAAI,CAAC,QAAQ,EAAE;qMACX,OAAI,CAAC,KAAK,CAAC,gBAAgB,YAAA,GAAe,2DAA2D,CAAC;QAClH;QACQ,OAAO,QAAQ;IACvB,CAAK;IACD,mBAAmB,CAAC,sBAAA,GAAyB,IAAI,GAAG,CAAC;QACjD;YAAC,cAAc;YAAE,YAAY;gBAAE,OAAO,IAAI,yBAAyB,EAAE,CAAA;YAAA,CAAG;SAAC;QACzE;YAAC,SAAS;YAAE,YAAY;gBAAE,OAAO,IAAI,oBAAoB,EAAE,CAAA;YAAA,CAAG;SAAC;KAClE,CAAC;IACF,mBAAmB,CAAC,oBAAA,GAAuB,IAAI,GAAG,EAAE;IACpD,OAAO,mBAAmB;AAC9B,CAAC,EAAE,CAAC;AC7OJ;;;;CAIA,GACO,MAAM,WAAA,GAAc,OAAA,gBAAA,KAAA,WAAA,IAAA,gBAAA,CAAe;ACa1C,MAAME,kBAAA,GAAmB,eAAe;AAExC,MAAM,cAAA,GAAiB,IAAI,OAAO,EAAmB;AAgBrD,MAAM,cAAA,GAAkB,CAAC,OAAO,GAAqB,CAAA,CAAE,KAAK;IAC1D,MAAM,WAAA,GAAc,OAAO,CAAC,WAAA,KAAgB,SAAA,GAAY,IAAA,GAAO,OAAO,CAAC,WAAW;IAClF,MAAM,0BAAA,GAA6B,OAAO,CAAC,0BAA0B;IAErE,MAAM,iBAAA,GAAoB,kLAAI,SAAM,CAAkB,GAAG,CAAC;IAC1D,MAAM,cAAA,GAAiB,kLAAI,SAAM,CAAkB,GAAG,CAAC;IAEvD,MAAM,KAAK,GAAyB,CAAA,CAAE;IAExC,uEAAA,GACE,SAAS,sBAAsB,CAAC,GAAG,EAAmB;QACpD,MAAM,MAAA,qLAAS,aAAA,AAAS,EAAE;QAE1B,IAAI,CAAC,MAAM,EAAE;YACX,OAAO,KAAK;QAClB;QAEI,MAAM,aAAA,GAAgB,MAAM,CAAC,UAAU,EAAE;QAEzC,IAAI,aAAa,CAAC,uBAAA,KAA4B,SAAS,EAAE;YACvD,OAAO,IAAI;QACjB;QAEI,MAAM,iBAAiB,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC;QAC9C,IAAI,cAAA,KAAmB,SAAS,EAAE;YAChC,OAAO,cAAc;QAC3B;QAEI,MAAM,QAAA,wLAAW,2BAAA,AAAwB,EAAC,GAAG,EAAE,aAAa,CAAC,uBAAuB,CAAC;QACrF,cAAc,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC;QACjC,OAAO,QAAQ;IACnB;IAEA,wDAAA,GACE,SAAS,iBAAiB,CAAC,GAAG,EAAmB;QAC/C,IAAI,0BAAA,KAA+B,SAAS,EAAE;YAC5C,OAAO,IAAI;QACjB;QAEI,MAAM,iBAAiB,iBAAiB,CAAC,GAAG,CAAC,GAAG,CAAC;QACjD,IAAI,cAAA,KAAmB,SAAS,EAAE;YAChC,OAAO,cAAc;QAC3B;QAEI,MAAM,QAAA,GAAW,0BAA0B,CAAC,GAAG,CAAC;QAChD,iBAAiB,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC;QACpC,OAAO,QAAQ;IACnB;IAEE,OAAO;QACL,IAAI,EAAEA,kBAAgB;QACtB,SAAS,GAAG;qMACV,iCAAA,AAA8B,GAAC,WAAA,IAAe;gBAC5C,MAAM,MAAA,OAAS,2LAAA,AAAS,EAAE;gBAC1B,IAAI,CAAC,MAAA,IAAU,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;oBAC1C;gBACV;gBAEQ,IAAI,sNAAA,AAAkB,EAAC,WAAW,CAAC,SAAS,CAAC,GAAG,EAAE,MAAM,CAAC,EAAE;oBACzD;gBACV;2LAEQ,yBAAA,AAAsB,EACpB,WAAW,EACX,iBAAiB,EACjB,sBAAsB,EACtB,KAAK,EACL,0BAA0B;gBAG5B,IAAI,WAAW,EAAE;oBACf,gBAAgB,CAAC,WAAW,CAAC;gBACvC;YACA,CAAO,CAAC;QACR,CAAK;QACD,KAAK,EAAC,MAAM,EAAE;YACZ,cAAc,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC;QACtC,CAAK;IACL,CAAG;AACH,CAAC,CAAA;AAED;;CAEA,SACa,wBAAA,oLAA2B,oBAAA,AAAiB,EAAC,cAAc;AAExE,SAAS,gBAAgB,CAAC,WAAW,EAA0B;IAC7D,MAAM,EAAE,cAAc,EAAE,YAAA,EAAa,GAAI,WAAW;IAEtD,0CAAA;IACE,IAAI,CAAC,YAAY,EAAE;QACjB;IACJ;IAEE,MAAM,cAAc,GAAwB;QAC1C,MAAM,EAAE,WAAW,CAAC,SAAS,CAAC,MAAM;QACpC,GAAG,EAAE,WAAW,CAAC,SAAS,CAAC,GAAG;IAClC,CAAG;IAED,IAAI,WAAW,CAAC,KAAK,EAAE;QACrB,MAAM,IAAI,GAAwB;YAChC,IAAI,EAAE,WAAW,CAAC,KAAK;YACvB,KAAK,EAAE,WAAW,CAAC,IAAI;YACvB,cAAc;YACd,YAAY;QAClB,CAAK;yLAED,gBAAA,AAAa,EACX;YACE,QAAQ,EAAE,OAAO;YACjB,IAAI,EAAE,cAAc;YACpB,KAAK,EAAE,OAAO;YACd,IAAI,EAAE,MAAM;QACpB,CAAO,EACD,IAAI;IAEV,OAAS;QACL,MAAM,QAAA,GAAW,WAAW,CAAC,QAAA;QAE7B,cAAc,CAAC,iBAAA,GAAoB,WAAW,CAAC,SAAS,CAAC,iBAAiB;QAC1E,cAAc,CAAC,kBAAA,GAAqB,WAAW,CAAC,SAAS,CAAC,kBAAkB;QAC5E,cAAc,CAAC,WAAA,GAAc,QAAQ,EAAE,MAAM;QAE7C,MAAM,IAAI,GAAwB;YAChC,KAAK,EAAE,WAAW,CAAC,IAAI;YACvB,QAAQ;YACR,cAAc;YACd,YAAY;QAClB,CAAK;QACD,MAAM,iNAAQ,0CAAA,AAAuC,EAAC,cAAc,CAAC,WAAW,CAAC;wLAEjF,iBAAA,AAAa,EACX;YACE,QAAQ,EAAE,OAAO;YACjB,IAAI,EAAE,cAAc;YACpB,IAAI,EAAE,MAAM;YACZ,KAAK;QACb,CAAO,EACD,IAAI;IAEV;AACA;ACzKA,MAAM,6BAAA,GAAgC,EAAE;AAExC;;;;;;;CAOA,GACO,MAAM,qBAAA,CAAsB;IACnC,4EAAA;IACA,yEAAA;IAOS,WAAW,CAAC,WAAA,GAAc,6BAA6B,CAAE;QAC9D,IAAI,CAAC,CAAA,GAAI,EAAE;QACX,IAAI,CAAC,cAAA,GAAiB,EAAE;QACxB,IAAI,CAAC,WAAA,GAAc,WAAW;IAClC;IAEA;;GAEA,GACS,GAAG,CAAC,YAAY,EAA8F;QACnH,IAAI,IAAI,CAAC,cAAc,CAAC,MAAA,IAAU,IAAI,CAAC,WAAW,EAAE;YAClD,OAAO,OAAO,CAAC,MAAM,wLAAC,4BAAwB,CAAC;QACrD;QAEI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC;QACtC,OAAO,OAAO,CAAC,OAAO,CAAC,CAAA,CAAE,CAAC;IAC9B;IAEA;;GAEA,GACS,KAAK,CAAC,OAAO,EAAiC;QACnD,MAAM,mBAAmB,CAAC;eAAG,IAAI,CAAC,cAAc;SAAC;QACjD,IAAI,CAAC,cAAA,GAAiB,EAAE;QAExB,OAAO,IAAI,OAAO,EAAC,WAAW;YAC5B,MAAM,KAAA,GAAQ,UAAU,CAAC,MAAM;gBAC7B,IAAI,OAAA,IAAW,OAAA,GAAU,CAAC,EAAE;oBAC1B,OAAO,CAAC,KAAK,CAAC;gBACxB;YACA,CAAO,EAAE,OAAO,CAAC;YAEjB,qBAAA;YACA,mEAAA;YACM,OAAO,CAAC,GAAG,CACT,gBAAgB,CAAC,GAAG,EAAC,YAAA,GACnB,YAAY,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM;gBAC1C,4BAAA;gBACA,CAAW,CAAC,GAEJ,IAAI,CAAC,MAAM;gBACnB,gDAAA;gBACQ,YAAY,CAAC,KAAK,CAAC;gBACnB,OAAO,CAAC,IAAI,CAAC;YACrB,CAAO,CAAC;QACR,CAAK,CAAC;IACN;AACA;AAEA;;CAEA,GACO,SAAS,iBAAiB,CAAC,OAAO,EAAyC;IAChF,SAAS,WAAW,CAAC,OAAO,EAA+D;QACzF,MAAM,cAAc,GAAgB;YAClC,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,MAAM,EAAE,MAAM;YACd,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,GAAG,OAAO,CAAC,YAAY;QAC7B,CAAK;QAED,6LAAOC,kBAAAA,AAAe,EAAC,MAAM;YAC3B,OAAO,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC,IAAI,EAAC,YAAY;gBACzD,OAAO;oBACL,UAAU,EAAE,QAAQ,CAAC,MAAM;oBAC3B,OAAO,EAAE;wBACP,sBAAsB,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC;wBACpE,aAAa,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC;oBAC9D,CAAW;gBACX,CAAS;YACT,CAAO,CAAC;QACR,CAAK,CAAC;IACN;IAEE,+LAAO,kBAAA,AAAe,EAAC,OAAO,EAAE,WAAW,EAAE,IAAI,qBAAqB,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AAC7F;ACpGA;;;;CAIA,GACO,SAAS,YAAY,CAAC,QAAQ,EAA+B;IAClE,MAAM,YAAA,GAA+D,OAAO,CAAC,GAAG,CAAC,UAAU;IAC3F,OAAO,eAAe,CAAC,OAAO,EAAE,YAAY,CAAC,CAAA,GAAA,SAAA;AACA;ACmC/C,MAAM,uBAAuB;IAC3B,aAAA;IACA,IAAA;IACA,MAAA;IACA,iBAAA;IACA,qBAAA;CACD;AAEe,MAAM,gCAAA,EAA2D;IAAA,WAAA,EAAA;QAAA,gCAAA,CAAA,SAAA,CAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA;QAAA,gCAAA,CAAA,SAAA,CAAA,OAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;IAAA;IAcjF;;;;;GAKA,GACE,IAAI,CAAI,OAAO,EAAW,MAAM,EAAQ;QACtC,IAAI,OAAO,MAAA,KAAW,QAAA,IAAY,MAAA,KAAW,IAAA,IAAQ,IAAA,IAAQ,MAAM,EAAE;YACnE,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,QAAO;QACpD;QAEI,IAAI,OAAO,MAAA,KAAW,UAAU,EAAE;YAChC,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,MAAM,CAAC;QAChD;QACI,OAAO,MAAM;IACjB;IAEU,aAAa,CAAqB,OAAO,EAAW,MAAM,EAAQ;QACxE,MAAM,OAAA,GAAU,IAAI;QACpB,MAAM,iBAAiB,SAAuB,GAAG,IAAI,EAAa;YAChE,OAAO,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,IAAM,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAClE,CAAK;QACD,MAAM,CAAC,cAAc,CAAC,cAAc,EAAE,QAAQ,EAAE;YAC9C,UAAU,EAAE,KAAK;YACjB,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,KAAK;YACf,KAAK,EAAE,MAAM,CAAC,MAAM;QAC1B,CAAK,CAAC;QACN;;;KAGA,GACA,8DAAA;QACI,OAAO,cAAA;IACX;IAEA;;;;;;GAMA,GACU,iBAAiB,CAAyB,OAAO,EAAW,EAAE,EAAQ;QAC5E,MAAM,MAAM,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;QACjC,IAAI,GAAA,KAAQ,SAAS,EAAE,OAAO,EAAE;QAChC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC;QAE5B,yDAAA;QACI,oBAAoB,CAAC,OAAO,CAAC,cAAc;YACzC,IAAI,EAAE,CAAC,UAAU,CAAA,KAAM,SAAS,EAAE;YAClC,EAAE,CAAC,UAAU,CAAA,GAAI,IAAI,CAAC,iBAAiB,CAAC,EAAE,EAAE,EAAE,CAAC,UAAU,CAAC,EAAE,OAAO,CAAC;QAC1E,CAAK,CAAC;QACN,uCAAA;QACI,IAAI,OAAO,EAAE,CAAC,cAAA,KAAmB,UAAU,EAAE;YAC3C,EAAE,CAAC,cAAA,GAAiB,IAAI,CAAC,oBAAoB,CAAC,EAAE,EAAE,EAAE,CAAC,cAAc,CAAC;QAC1E;QACI,IAAI,OAAO,EAAE,CAAC,GAAA,KAAQ,UAAU,EAAE;YAChC,EAAE,CAAC,GAAA,GAAM,IAAI,CAAC,oBAAoB,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC;QACpD;QACA,yCAAA;QACI,IAAI,OAAO,EAAE,CAAC,kBAAA,KAAuB,UAAU,EAAE;YAC/C,EAAE,CAAC,kBAAA,GAAqB,IAAI,CAAC,wBAAwB,CAAC,EAAE,EAAE,EAAE,CAAC,kBAAkB,CAAC;QACtF;QACI,OAAO,EAAE;IACb;IAEA;;;;;GAKA,GACU,oBAAoB,CAAC,EAAE,EAAgB,QAAQ,EAAY;QACjE,MAAM,cAAA,GAAiB,IAAI;QAC3B,OAAO,SAAuB,KAAK,EAAU,QAAQ,EAAc;YACjE,MAAM,MAAA,GAAS,cAAc,CAAC,YAAY,CAAC,EAAE,CAAC,EAAA,CAAG,KAAK,CAAC;YACvD,IAAI,MAAA,KAAW,SAAS,EAAE;gBACxB,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC;YACnD;YACM,MAAM,kBAAkB,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC;YAC5C,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,eAAA,IAAmB,QAAQ,CAAC;QACpE,CAAK;IACL;IAEA;;;;;GAKA,GACU,wBAAwB,CAAC,EAAE,EAAgB,QAAQ,EAAY;QACrE,MAAM,cAAA,GAAiB,IAAI;QAC3B,OAAO,SAAuB,KAAK,EAAU;YAC3C,MAAM,MAAM,cAAc,CAAC,YAAY,CAAC,EAAE,CAAC;YAC3C,IAAI,GAAA,KAAQ,SAAS,EAAE;gBACrB,IAAI,SAAS,CAAC,MAAA,KAAW,CAAC,EAAE;oBAC1B,cAAc,CAAC,eAAe,CAAC,EAAE,CAAC;gBAC5C,CAAQ,MAAO,IAAI,GAAG,CAAC,KAAK,CAAA,KAAM,SAAS,EAAE;oBACnC,OAAO,GAAG,CAAC,KAAK,CAAC;gBAC3B;YACA;YACM,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC;QAC5C,CAAK;IACL;IAEA;;;;;;GAMA,GACU,iBAAiB,CAAC,EAAE,EAAgB,QAAQ,EAAY,OAAO,EAAW;QAChF,MAAM,cAAA,GAAiB,IAAI;QAC3B,OAAO,SAAuB,KAAK,EAAU,QAAQ,EAAc;YACvE;;;;;;;OAOA,GACM,IAAI,cAAc,CAAC,QAAQ,EAAE;gBAC3B,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC;YACnD;YACM,IAAI,MAAM,cAAc,CAAC,YAAY,CAAC,EAAE,CAAC;YACzC,IAAI,GAAA,KAAQ,SAAS,EAAE;gBACrB,MAAM,cAAc,CAAC,eAAe,CAAC,EAAE,CAAC;YAChD;YACM,IAAI,SAAA,GAAY,GAAG,CAAC,KAAK,CAAC;YAC1B,IAAI,SAAA,KAAc,SAAS,EAAE;gBAC3B,SAAA,GAAY,IAAI,OAAO,EAAE;gBACzB,GAAG,CAAC,KAAK,CAAA,GAAI,SAAS;YAC9B;YACM,MAAM,eAAA,GAAkB,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC;YACpE,sDAAA;YACM,SAAS,CAAC,GAAG,CAAC,QAAQ,EAAE,eAAe,CAAC;YAE9C;;OAEA,GACM,cAAc,CAAC,QAAA,GAAW,IAAI;YAC9B,IAAI;gBACF,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,eAAe,CAAC;YAC1D,SAAgB;gBACR,cAAc,CAAC,QAAA,GAAW,KAAK;YACvC;QACA,CAAK;IACL;IAEU,eAAe,CAAC,EAAE,EAA0B;QAClD,MAAM,MAAM,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;QACnC,8DAAA;QACK,EAAA,CAAW,IAAI,CAAC,aAAa,CAAA,GAAI,GAAG;QACrC,OAAO,GAAG;IACd;IACU,YAAY,CAAC,EAAE,EAAsC;QAC3D,OAAO,AAAC,EAAA,CAAa,IAAI,CAAC,aAAa,CAAC;IAC5C;IAEU,MAAA,GAAA;QAAA,IAAA,CAAS,aAAA,GAAgB,MAAM,CAAC,aAAa;IAAA;IACrD,OAAA,GAAA;QAAA,IAAA,CAAQ,QAAA,GAAW;IAAA;AACrB;ACvMO,MAAM,+BAAA,SAAwC,gCAAA,CAAiC;IAGpF,WAAW,EAAG;QACZ,KAAK,EAAE;QACX,0GAAA;QACI,MAAM,uCAAA,GAA0C,oLAAC,aAAA,CAAmB,iBAAiB;QAErF,IAAI,CAAC,uCAAuC,EAAE;YAC5C,WAAA,8LACEC,QAAK,CAAC,IAAI,CACR,iHAAiH;YAG3H,6CAAA;YACM,IAAI,CAAC,kBAAA,GAAqB;gBACxB,QAAQ,GAAG;oBACT,OAAO,SAAS;gBAC1B,CAAS;gBACD,GAAG,EAAC,MAAM,EAAW,QAAQ,EAAiB,GAAG,IAAI,EAAa;oBAChE,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC;gBAC3C,CAAS;gBACD,OAAO,GAAG;gBAClB,OAAA;gBACA,CAAS;YACT,CAAO;QACP,OAAW;YACL,IAAI,CAAC,kBAAA,GAAqB,IAAI,uCAAuC,EAAE;QAC7E;IACA;IAEE,MAAM,GAAY;QAChB,OAAO,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAC,6LAAK,eAAY;IAC7D;IAEE,IAAI,CACF,OAAO,EACP,EAAE,EACF,OAAO,EACP,GAAG,IAAA,EACY;QACf,MAAM,EAAA,GAAK,OAAA,IAAW,IAAA,GAAO,EAAA,GAAK,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC;QAClD,OAAO,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,OAAO,EAAE,EAAA,GAAa,EAAG,IAAI,CAAC;IACrE;IAEE,MAAM,GAAS;QACb,OAAO,IAAI;IACf;IAEE,OAAO,GAAS;QACd,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE;QACjC,OAAO,IAAI;IACf;AACA;ACvCA,MAAM,2MAAkB,oBAAiB,AAAjB,uMAAkB,sBAAmB,AAAnB,EAAqB,CAAC;AAEhE,sDAAA,GACO,SAAS,sBAAsB,CAAC,OAAO,EAA0B;IACtE,OAAO;QACL,gNAAA,AAAiB,EAAE;QACvB,8GAAA;QACA,mDAAA;0MACI,4BAAA,AAAyB,EAAE;8MAC3B,8BAAA,AAA2B,EAAE;0MAC7B,0BAAA,AAAuB,EAAE;QACzB,wBAAwB,EAAE;qMAC1B,qBAAA,AAAkB,EAAE;QACxB,yFAAA;WACQ,OAAO,CAAC,cAAA,GAAiB;6MAAC,yBAAA,AAAsB,EAAE;SAAA,GAAI,EAAE,CAAC;KAC9D;AACH;AAEA,qDAAA,GACO,SAAS,IAAI,CAAC,OAAO,GAAsB,CAAA,CAAE,EAAsB;wMACxE,8CAAA,AAA2C,EAAE;IAE7C,MAAM,KAAA,sLAAQ,kBAAA,AAAe,EAAE;IAC/B,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC;IAElC,IAAI,OAAO,CAAC,mBAAA,KAAwB,SAAS,EAAE;QAC7C,OAAO,CAAC,mBAAA,GAAsB,sBAAsB,CAAC,OAAO,CAAC;IACjE;IAEE,IAAI,OAAO,CAAC,GAAA,KAAQ,SAAA,IAAa,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE;QACvD,OAAO,CAAC,GAAA,GAAM,OAAO,CAAC,GAAG,CAAC,UAAU;IACxC;IAEE,IAAI,OAAO,CAAC,gBAAA,KAAqB,SAAA,IAAa,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE;QACnF,MAAM,gBAAA,GAAmB,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC;QAC1E,IAAI,QAAQ,CAAC,gBAAgB,CAAC,EAAE;YAC9B,OAAO,CAAC,gBAAA,GAAmB,gBAAgB;QACjD;IACA;IAEE,IAAI,OAAO,CAAC,OAAA,KAAY,SAAS,EAAE;QACjC,MAAM,eAAA,GAAkB,gBAAgB,EAAE;QAC1C,IAAI,eAAA,KAAoB,SAAS,EAAE;YACjC,OAAO,CAAC,OAAA,GAAU,eAAe;QACvC;IACA;IAEE,OAAO,CAAC,WAAA,GACN,OAAO,CAAC,WAAA,IAAe,OAAO,CAAC,GAAG,CAAC,kBAAA,IAAsB,YAAY,CAAM,CAAA,IAAK,OAAO,CAAC,GAAG,CAAC,QAAQ;IAEtG,MAAM,MAAA,GAAS,IAAI,gBAAgB,CAAC;QAClC,GAAG,OAAO;QACV,WAAW,2LAAE,oCAAA,AAAiC,EAAC,OAAO,CAAC,WAAA,IAAe,eAAe,CAAC;QACtF,YAAY,mLAAE,yBAAsB,AAAtB,EAAuB,OAAO,CAAC;QAC7C,SAAS,EAAE,OAAO,CAAC,SAAA,IAAa,iBAAiB;IACrD,CAAG,CAAC;IACJ,2EAAA;uLACE,kBAAA,AAAe,EAAE,EAAC,SAAS,CAAC,MAAM,CAAC;IAEnC,MAAM,CAAC,IAAI,EAAE;IAEf,2EAAA;IACA,yDAAA;IACE,IAAI,CAAC,OAAO,CAAC,sBAAsB,EAAE;QACnC,SAAS,CAAC,MAAM,CAAC;QACjB,0BAA0B,EAAE;IAChC;wMAEE,0CAAA,AAAuC,EAAC,MAAM,CAAC;IAC/C,6NAAA,AAAsB,EAAC,MAAM,CAAC;IAE9B,OAAO,MAAM;AACf;AAEA,SAAS,0BAA0B,GAAS;IAC1C,IAAI,CAAC,WAAW,EAAE;QAChB;IACJ;IAEE,MAAM,KAAA,uMAAQ,0BAAA,AAAuB,EAAE;IAEvC,MAAM,QAAQ,GAA+C;QAAC,sBAAsB;QAAE,kBAAkB;KAAC;IAEzG,kMAAI,kBAAA,AAAe,EAAE,GAAE;QACrB,QAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC;IACxC;IAEE,KAAK,MAAM,CAAA,IAAK,QAAQ,CAAE;QACxB,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;sMACtBA,QAAK,CAAC,KAAK,CACT,CAAC,uBAAuB,EAAE,CAAC,CAAC,8EAA8E,CAAC;QAEnH;IACA;IAEE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE;QACpCA,kMAAK,CAAC,IAAI,CACR,iPAAiP;IAEvP;AACA;AAEA,qBAAA;AACA,+CAAA;AACO,SAAS,SAAS,CAAC,MAAM,EAA0B;IACxD,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC,KAAK,EAAE;QAC7B,wBAAwB,EAAE;IAC9B;IAEA,0CAAA;IACE,MAAM,QAAA,GAAW,IAAI,mBAAmB,CAAC;QACvC,OAAO,EAAE,oMAAI,gBAAa,CAAC,MAAM,CAAC;QAClC,QAAQ,EAAE,IAAI,iMAAQ,CAAC;YACrB,8MAAC,oBAAiB,CAAA,EAAG,MAAM;YACjC,mDAAA;YACM,CAAC,kQAA6B,CAAA,EAAG,QAAQ;YACzC,8MAAC,uBAAoB,CAAA,oLAAG,cAAW;QACzC,CAAK,CAAC;QACF,uBAAuB,EAAE,GAAG;QAC5B,cAAc,EAAE;YACd,oMAAI,sBAAmB,CAAC;gBACtB,OAAO,EAAE,MAAM,CAAC,UAAU,EAAE,CAAC,mBAAmB;YACxD,CAAO,CAAC;SACH;IACL,CAAG,CAAC;IAEF,MAAM,oBAAA,uMAAuB,0BAAA,AAAuB,EAAC,+BAA+B,CAAC;IAErFJ,iMAAK,CAAC,uBAAuB,CAAC,QAAQ,CAAC;6LACvC,cAAW,CAAC,mBAAmB,CAAC,oMAAI,mBAAgB,EAAE,CAAC;6LACvD,UAAO,CAAC,uBAAuB,CAAC,IAAI,oBAAoB,EAAE,CAAC;IAE3D,MAAM,CAAC,aAAA,GAAgB,QAAQ;AACjC;AAEA;;CAEA,GACA,SAAS,wBAAwB,GAAS;IAC1C,mEAAA;6LACE,OAAI,CAAC,OAAO,EAAE;6LACd,OAAI,CAAC,SAAS,CACZ;QACE,KAAK,4LAAEI,QAAK,CAAC,KAAK;QAClB,IAAI,4LAAEA,QAAK,CAAC,IAAI;QAChB,IAAI,4LAAEA,QAAK,CAAC,GAAG;QACf,KAAK,4LAAEA,QAAK,CAAC,GAAG;QAChB,OAAO,4LAAEA,QAAK,CAAC,GAAG;IACxB,CAAK,2LACD,eAAY,CAAC,KAAK;AAEtB;AAEA;;CAEA,GACA,sCAAA;AACO,SAAS,gBAAgB,CAAC,QAAQ,EAA+B;IACxE,uDAAA;IACE,IAAI,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE;QAC9B,OAAO,OAAO,CAAC,GAAG,CAAC,cAAc;IACrC;IAEA,gEAAA;IACE,wLAAI,aAAU,CAAC,cAAc,EAAE,EAAE,EAAE;QACjC,2LAAO,aAAU,CAAC,cAAc,CAAC,EAAE;IACvC;IAEA,wEAAA;IACA,mBAAA;IACA,mGAAA;IACA,4GAAA;IAEE,MAAM,gCAAA,GACR,mJAAA;IACI,OAAO,CAAC,GAAG,CAAC,YAAY,CAAA,IAC5B,gFAAA;IACI,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAA,IAChD,OAAO,CAAC,GAAG,CAAC,cAAc,CAAA,IAC1B,OAAO,CAAC,GAAG,CAAC,eAAe,CAAA,IAC/B,wFAAA;IACI,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC;IAEjC,MAAM,kDAAA,GACR,kEAAA;IACI,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAA,IAC/C,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAA,IACtC,qGAAA;IACI,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAA,IACnD,gGAAA;IACI,OAAO,CAAC,GAAG,CAAC,eAAe,CAAA,IAC/B,wHAAA;IACI,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAA,IACrC,iFAAA;IACI,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAA,IACvC,oGAAA;IACI,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAA,IAC1C,yEAAA;IACI,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAA,IAClC,kDAAA;IACI,OAAO,CAAC,GAAG,CAAC,aAAa,CAAA,IAC7B,+EAAA;IACI,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAA,IACvC,uEAAA;IACI,OAAO,CAAC,GAAG,CAAC,aAAa,CAAA,IAC7B,wFAAA;IACI,OAAO,CAAC,GAAG,CAAC,WAAW,CAAA,IAC3B,iHAAA;IACI,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAA,IACrC,gEAAA;IACI,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAA,IAClC,+HAAA;IACI,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAA,IACnC,4DAAA;IACI,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAA,IAChD,8FAAA;IACI,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAA,IACpC,uEAAA;IACI,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAA,IACxC,yDAAA;IACI,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAA,IACnC,sFAAA;IACI,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAA,IACnC,kGAAA;IACI,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAA,IACzC,8EAAA;IACI,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAA,IACnC,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAA,IACtC,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAA,IACtC,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAA,IAC7C,6BAAA;IACI,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAA,IACpC,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAA,IACpC,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC;IAE1C,MAAM,iDAAA,GACR,oIAAA;IACI,OAAO,CAAC,GAAG,CAAC,cAAc,CAAA,IAC9B,yEAAA;IACI,OAAO,CAAC,GAAG,CAAC,eAAe,CAAA,IAC/B,6DAAA;IACI,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAA,IAChC,kEAAA;IACI,OAAO,CAAC,GAAG,CAAC,YAAY,CAAA,IAC5B,4FAAA;IACI,OAAO,CAAC,GAAG,CAAC,YAAY,CAAA,IAC5B,sFAAA;IACI,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAA,IAClC,mEAAA;IACI,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC;IAE9B,OACE,gCAAA,IACA,kDAAA,IACA,iDAAA,IACA;AAEJ;ACpSA,MAAM,gBAAA,GAAmB,UAAU;AAEnC,MAAM,oBAAA,GAAwB,MAAM;IAClC,OAAO;QACL,IAAI,EAAE,gBAAgB;QACtB,KAAK,EAAC,MAAM,EAAE;uMACZ,wBAAA,AAAqB,EAAC,MAAM,CAAC;QACnC,CAAK;IACL,CAAG;AACH,CAAC,CAAA;AAED;;;;;;;;;;;;;;;;;;;;;;;;;CAyBA,SACa,mBAAA,IAAsB,oMAAA,AAAiB,EAAC,oBAAoB;AC/CzE;;;;;;;CAOA,GACA,SAAS,UAAU,CACjB,KAAK,EACL,OAAO,EACP,UAAU,EACV,cAAc;yLAEd,uBAAA,AAAoB,EAAC;QAAE,KAAK;QAAE,OAAO;QAAE,UAAU;QAAE,cAAA;IAAA,CAAgB,CAAC;AACtE;AAEA;;;;;;;;;;;;;;;;;;;;;;;;CAwBA,GACO,SAAS,KAAK,CAAC,OAAO,EAAuB,UAAU,EAA4B;IACxF,UAAU,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,CAAC;AAC1C;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;CAyBA,GACO,SAAS,KAAK,CAAC,OAAO,EAAuB,UAAU,EAA4B;IACxF,UAAU,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,CAAC;AAC1C;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;CAyBA,GACO,SAAS,IAAI,CAAC,OAAO,EAAuB,UAAU,EAA4B;IACvF,UAAU,CAAC,MAAM,EAAE,OAAO,EAAE,UAAU,CAAC;AACzC;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;CA0BA,GACO,SAAS,IAAI,CAAC,OAAO,EAAuB,UAAU,EAA4B;IACvF,UAAU,CAAC,MAAM,EAAE,OAAO,EAAE,UAAU,CAAC;AACzC;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;CA2BA,GACO,SAAS,KAAK,CAAC,OAAO,EAAuB,UAAU,EAA4B;IACxF,UAAU,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,CAAC;AAC1C;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;CA2BA,GACO,SAAS,KAAK,CAAC,OAAO,EAAuB,UAAU,EAA4B;IACxF,UAAU,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,CAAC;AAC1C", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47]}}]}