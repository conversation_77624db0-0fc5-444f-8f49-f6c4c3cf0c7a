{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/constants/data.ts"], "sourcesContent": ["import { NavItem } from '@/types';\r\n\r\nexport type Product = {\r\n  photo_url: string;\r\n  name: string;\r\n  description: string;\r\n  created_at: string;\r\n  price: number;\r\n  id: number;\r\n  category: string;\r\n  updated_at: string;\r\n};\r\n\r\n//Info: The following data is used for the sidebar navigation and Cmd K bar.\r\nexport const getNavItems = (pathname: string): NavItem[] => [\r\n  {\r\n    title: 'Available Courses',\r\n    url: '/courses',\r\n    icon: 'searchList',\r\n    isActive: pathname.startsWith('/courses'),\r\n    shortcut: ['a', 'c'],\r\n    items: [] // Empty array as there are no child items\r\n  },\r\n  {\r\n    title: 'My Courses',\r\n    url: '/my-courses',\r\n    icon: 'graduationCap',\r\n    isActive: pathname.startsWith('/my-courses'),\r\n    shortcut: ['m', 'c'],\r\n    items: [] // Empty array as there are no child items\r\n  }\r\n];\r\n\r\n// Legacy export for backward compatibility\r\nexport const navItems: NavItem[] = getNavItems('');\r\n\r\nexport interface SaleUser {\r\n  id: number;\r\n  name: string;\r\n  email: string;\r\n  amount: string;\r\n  image: string;\r\n  initials: string;\r\n}\r\n\r\nexport const recentSalesData: SaleUser[] = [\r\n  {\r\n    id: 1,\r\n    name: '<PERSON>',\r\n    email: '<EMAIL>',\r\n    amount: '+$1,999.00',\r\n    image: 'https://api.slingacademy.com/public/sample-users/1.png',\r\n    initials: 'OM'\r\n  },\r\n  {\r\n    id: 2,\r\n    name: 'Jackson Lee',\r\n    email: '<EMAIL>',\r\n    amount: '+$39.00',\r\n    image: 'https://api.slingacademy.com/public/sample-users/2.png',\r\n    initials: 'JL'\r\n  },\r\n  {\r\n    id: 3,\r\n    name: 'Isabella Nguyen',\r\n    email: '<EMAIL>',\r\n    amount: '+$299.00',\r\n    image: 'https://api.slingacademy.com/public/sample-users/3.png',\r\n    initials: 'IN'\r\n  },\r\n  {\r\n    id: 4,\r\n    name: 'William Kim',\r\n    email: '<EMAIL>',\r\n    amount: '+$99.00',\r\n    image: 'https://api.slingacademy.com/public/sample-users/4.png',\r\n    initials: 'WK'\r\n  },\r\n  {\r\n    id: 5,\r\n    name: 'Sofia Davis',\r\n    email: '<EMAIL>',\r\n    amount: '+$39.00',\r\n    image: 'https://api.slingacademy.com/public/sample-users/5.png',\r\n    initials: 'SD'\r\n  }\r\n];\r\n"], "names": [], "mappings": ";;;;;AAcO,MAAM,cAAc,CAAC,WAAgC;QAC1D;YACE,OAAO;YACP,KAAK;YACL,MAAM;YACN,UAAU,SAAS,UAAU,CAAC;YAC9B,UAAU;gBAAC;gBAAK;aAAI;YACpB,OAAO,EAAE,CAAC,0CAA0C;QACtD;QACA;YACE,OAAO;YACP,KAAK;YACL,MAAM;YACN,UAAU,SAAS,UAAU,CAAC;YAC9B,UAAU;gBAAC;gBAAK;aAAI;YACpB,OAAO,EAAE,CAAC,0CAA0C;QACtD;KACD;AAGM,MAAM,WAAsB,YAAY;AAWxC,MAAM,kBAA8B;IACzC;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,OAAO;QACP,UAAU;IACZ;CACD", "debugId": null}}, {"offset": {"line": 85, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/kbar/result-item.tsx"], "sourcesContent": ["import type { ActionId, ActionImpl } from 'kbar';\r\nimport * as React from 'react';\r\n\r\nconst ResultItem = React.forwardRef(\r\n  (\r\n    {\r\n      action,\r\n      active,\r\n      currentRootActionId\r\n    }: {\r\n      action: ActionImpl;\r\n      active: boolean;\r\n      currentRootActionId: ActionId;\r\n    },\r\n    ref: React.Ref<HTMLDivElement>\r\n  ) => {\r\n    const ancestors = React.useMemo(() => {\r\n      if (!currentRootActionId) return action.ancestors;\r\n      const index = action.ancestors.findIndex(\r\n        (ancestor) => ancestor.id === currentRootActionId\r\n      );\r\n      return action.ancestors.slice(index + 1);\r\n    }, [action.ancestors, currentRootActionId]);\r\n\r\n    return (\r\n      <div\r\n        ref={ref}\r\n        className={`relative z-10 flex cursor-pointer items-center justify-between px-4 py-3`}\r\n      >\r\n        {active && (\r\n          <div\r\n            id='kbar-result-item'\r\n            className='border-primary bg-accent/50 absolute inset-0 z-[-1]! border-l-4'\r\n          ></div>\r\n        )}\r\n        <div className='relative z-10 flex items-center gap-2'>\r\n          {action.icon && action.icon}\r\n          <div className='flex flex-col'>\r\n            <div>\r\n              {ancestors.length > 0 &&\r\n                ancestors.map((ancestor) => (\r\n                  <React.Fragment key={ancestor.id}>\r\n                    <span className='text-muted-foreground mr-2'>\r\n                      {ancestor.name}\r\n                    </span>\r\n                    <span className='mr-2'>&rsaquo;</span>\r\n                  </React.Fragment>\r\n                ))}\r\n              <span>{action.name}</span>\r\n            </div>\r\n            {action.subtitle && (\r\n              <span className='text-muted-foreground text-sm'>\r\n                {action.subtitle}\r\n              </span>\r\n            )}\r\n          </div>\r\n        </div>\r\n        {action.shortcut?.length ? (\r\n          <div className='relative z-10 grid grid-flow-col gap-1'>\r\n            {action.shortcut.map((sc, i) => (\r\n              <kbd\r\n                key={sc + i}\r\n                className='bg-muted flex h-5 items-center gap-1 rounded-md border px-1.5 text-[10px] font-medium'\r\n              >\r\n                {sc}\r\n              </kbd>\r\n            ))}\r\n          </div>\r\n        ) : null}\r\n      </div>\r\n    );\r\n  }\r\n);\r\n\r\nResultItem.displayName = 'KBarResultItem';\r\n\r\nexport default ResultItem;\r\n"], "names": [], "mappings": ";;;;AACA;;;AAEA,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAChC,CACE,EACE,MAAM,EACN,MAAM,EACN,mBAAmB,EAKpB,EACD;IAEA,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE;QAC9B,IAAI,CAAC,qBAAqB,OAAO,OAAO,SAAS;QACjD,MAAM,QAAQ,OAAO,SAAS,CAAC,SAAS,CACtC,CAAC,WAAa,SAAS,EAAE,KAAK;QAEhC,OAAO,OAAO,SAAS,CAAC,KAAK,CAAC,QAAQ;IACxC,GAAG;QAAC,OAAO,SAAS;QAAE;KAAoB;IAE1C,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAC,wEAAwE,CAAC;;YAEpF,wBACC,8OAAC;gBACC,IAAG;gBACH,WAAU;;;;;;0BAGd,8OAAC;gBAAI,WAAU;;oBACZ,OAAO,IAAI,IAAI,OAAO,IAAI;kCAC3B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;oCACE,UAAU,MAAM,GAAG,KAClB,UAAU,GAAG,CAAC,CAAC,yBACb,8OAAC,qMAAA,CAAA,WAAc;;8DACb,8OAAC;oDAAK,WAAU;8DACb,SAAS,IAAI;;;;;;8DAEhB,8OAAC;oDAAK,WAAU;8DAAO;;;;;;;2CAJJ,SAAS,EAAE;;;;;kDAOpC,8OAAC;kDAAM,OAAO,IAAI;;;;;;;;;;;;4BAEnB,OAAO,QAAQ,kBACd,8OAAC;gCAAK,WAAU;0CACb,OAAO,QAAQ;;;;;;;;;;;;;;;;;;YAKvB,OAAO,QAAQ,EAAE,uBAChB,8OAAC;gBAAI,WAAU;0BACZ,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,kBACxB,8OAAC;wBAEC,WAAU;kCAET;uBAHI,KAAK;;;;;;;;;uBAOd;;;;;;;AAGV;AAGF,WAAW,WAAW,GAAG;uCAEV", "debugId": null}}, {"offset": {"line": 209, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/kbar/render-result.tsx"], "sourcesContent": ["import { KBarResults, useMatches } from 'kbar';\r\nimport ResultItem from './result-item';\r\n\r\nexport default function RenderResults() {\r\n  const { results, rootActionId } = useMatches();\r\n\r\n  return (\r\n    <KBarResults\r\n      items={results}\r\n      onRender={({ item, active }) =>\r\n        typeof item === 'string' ? (\r\n          <div className='text-primary-foreground px-4 py-2 text-sm uppercase opacity-50'>\r\n            {item}\r\n          </div>\r\n        ) : (\r\n          <ResultItem\r\n            action={item}\r\n            active={active}\r\n            currentRootActionId={rootActionId ?? ''}\r\n          />\r\n        )\r\n      }\r\n    />\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS;IACtB,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,aAAU,AAAD;IAE3C,qBACE,8OAAC,oIAAA,CAAA,cAAW;QACV,OAAO;QACP,UAAU,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,GACzB,OAAO,SAAS,yBACd,8OAAC;gBAAI,WAAU;0BACZ;;;;;uCAGH,8OAAC,4IAAA,CAAA,UAAU;gBACT,QAAQ;gBACR,QAAQ;gBACR,qBAAqB,gBAAgB;;;;;;;;;;;AAMjD", "debugId": null}}, {"offset": {"line": 250, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/kbar/use-theme-switching.tsx"], "sourcesContent": ["import { useRegisterActions } from 'kbar';\r\nimport { useTheme } from 'next-themes';\r\n\r\nconst useThemeSwitching = () => {\r\n  const { theme, setTheme } = useTheme();\r\n\r\n  const toggleTheme = () => {\r\n    setTheme(theme === 'light' ? 'dark' : 'light');\r\n  };\r\n\r\n  const themeAction = [\r\n    {\r\n      id: 'toggleTheme',\r\n      name: 'Toggle Theme',\r\n      shortcut: ['t', 't'],\r\n      section: 'Theme',\r\n      perform: toggleTheme\r\n    },\r\n    {\r\n      id: 'setLightTheme',\r\n      name: 'Set Light Theme',\r\n      section: 'Theme',\r\n      perform: () => setTheme('light')\r\n    },\r\n    {\r\n      id: 'setDarkTheme',\r\n      name: 'Set Dark Theme',\r\n      section: 'Theme',\r\n      perform: () => setTheme('dark')\r\n    }\r\n  ];\r\n\r\n  useRegisterActions(themeAction, [theme]);\r\n};\r\n\r\nexport default useThemeSwitching;\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,oBAAoB;IACxB,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAEnC,MAAM,cAAc;QAClB,SAAS,UAAU,UAAU,SAAS;IACxC;IAEA,MAAM,cAAc;QAClB;YACE,IAAI;YACJ,MAAM;YACN,UAAU;gBAAC;gBAAK;aAAI;YACpB,SAAS;YACT,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,SAAS;YACT,SAAS,IAAM,SAAS;QAC1B;QACA;YACE,IAAI;YACJ,MAAM;YACN,SAAS;YACT,SAAS,IAAM,SAAS;QAC1B;KACD;IAED,CAAA,GAAA,oIAAA,CAAA,qBAAkB,AAAD,EAAE,aAAa;QAAC;KAAM;AACzC;uCAEe", "debugId": null}}, {"offset": {"line": 297, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/kbar/index.tsx"], "sourcesContent": ["'use client';\r\nimport { navItems } from '@/constants/data';\r\nimport {\r\n  KBarAnimator,\r\n  KBarPortal,\r\n  KBarPositioner,\r\n  KBarProvider,\r\n  KBarSearch\r\n} from 'kbar';\r\nimport { useRouter } from 'next/navigation';\r\nimport { useMemo } from 'react';\r\nimport RenderResults from './render-result';\r\nimport useThemeSwitching from './use-theme-switching';\r\n\r\nexport default function KBar({ children }: { children: React.ReactNode }) {\r\n  const router = useRouter();\r\n\r\n  // These action are for the navigation\r\n  const actions = useMemo(() => {\r\n    // Define navigateTo inside the useMemo callback to avoid dependency array issues\r\n    const navigateTo = (url: string) => {\r\n      router.push(url);\r\n    };\r\n\r\n    return navItems.flatMap((navItem) => {\r\n      // Only include base action if the navItem has a real URL and is not just a container\r\n      const baseAction =\r\n        navItem.url !== '#'\r\n          ? {\r\n              id: `${navItem.title.toLowerCase()}Action`,\r\n              name: navItem.title,\r\n              shortcut: navItem.shortcut,\r\n              keywords: navItem.title.toLowerCase(),\r\n              section: 'Navigation',\r\n              subtitle: `Go to ${navItem.title}`,\r\n              perform: () => navigateTo(navItem.url)\r\n            }\r\n          : null;\r\n\r\n      // Map child items into actions\r\n      const childActions =\r\n        navItem.items?.map((childItem) => ({\r\n          id: `${childItem.title.toLowerCase()}Action`,\r\n          name: childItem.title,\r\n          shortcut: childItem.shortcut,\r\n          keywords: childItem.title.toLowerCase(),\r\n          section: navItem.title,\r\n          subtitle: `Go to ${childItem.title}`,\r\n          perform: () => navigateTo(childItem.url)\r\n        })) ?? [];\r\n\r\n      // Return only valid actions (ignoring null base actions for containers)\r\n      return baseAction ? [baseAction, ...childActions] : childActions;\r\n    });\r\n  }, [router]);\r\n\r\n  return (\r\n    <KBarProvider actions={actions}>\r\n      <KBarComponent>{children}</KBarComponent>\r\n    </KBarProvider>\r\n  );\r\n}\r\nconst KBarComponent = ({ children }: { children: React.ReactNode }) => {\r\n  useThemeSwitching();\r\n\r\n  return (\r\n    <>\r\n      <KBarPortal>\r\n        <KBarPositioner className='bg-background/80 fixed inset-0 z-99999 p-0! backdrop-blur-sm'>\r\n          <KBarAnimator className='bg-card text-card-foreground relative mt-64! w-full max-w-[600px] -translate-y-12! overflow-hidden rounded-lg border shadow-lg'>\r\n            <div className='bg-card border-border sticky top-0 z-10 border-b'>\r\n              <KBarSearch className='bg-card w-full border-none px-6 py-4 text-lg outline-hidden focus:ring-0 focus:ring-offset-0 focus:outline-hidden' />\r\n            </div>\r\n            <div className='max-h-[400px]'>\r\n              <RenderResults />\r\n            </div>\r\n          </KBarAnimator>\r\n        </KBarPositioner>\r\n      </KBarPortal>\r\n      {children}\r\n    </>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAOA;AACA;AACA;AACA;AAZA;;;;;;;;AAce,SAAS,KAAK,EAAE,QAAQ,EAAiC;IACtE,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,sCAAsC;IACtC,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACtB,iFAAiF;QACjF,MAAM,aAAa,CAAC;YAClB,OAAO,IAAI,CAAC;QACd;QAEA,OAAO,wHAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,CAAC;YACvB,qFAAqF;YACrF,MAAM,aACJ,QAAQ,GAAG,KAAK,MACZ;gBACE,IAAI,GAAG,QAAQ,KAAK,CAAC,WAAW,GAAG,MAAM,CAAC;gBAC1C,MAAM,QAAQ,KAAK;gBACnB,UAAU,QAAQ,QAAQ;gBAC1B,UAAU,QAAQ,KAAK,CAAC,WAAW;gBACnC,SAAS;gBACT,UAAU,CAAC,MAAM,EAAE,QAAQ,KAAK,EAAE;gBAClC,SAAS,IAAM,WAAW,QAAQ,GAAG;YACvC,IACA;YAEN,+BAA+B;YAC/B,MAAM,eACJ,QAAQ,KAAK,EAAE,IAAI,CAAC,YAAc,CAAC;oBACjC,IAAI,GAAG,UAAU,KAAK,CAAC,WAAW,GAAG,MAAM,CAAC;oBAC5C,MAAM,UAAU,KAAK;oBACrB,UAAU,UAAU,QAAQ;oBAC5B,UAAU,UAAU,KAAK,CAAC,WAAW;oBACrC,SAAS,QAAQ,KAAK;oBACtB,UAAU,CAAC,MAAM,EAAE,UAAU,KAAK,EAAE;oBACpC,SAAS,IAAM,WAAW,UAAU,GAAG;gBACzC,CAAC,MAAM,EAAE;YAEX,wEAAwE;YACxE,OAAO,aAAa;gBAAC;mBAAe;aAAa,GAAG;QACtD;IACF,GAAG;QAAC;KAAO;IAEX,qBACE,8OAAC,oIAAA,CAAA,eAAY;QAAC,SAAS;kBACrB,cAAA,8OAAC;sBAAe;;;;;;;;;;;AAGtB;AACA,MAAM,gBAAgB,CAAC,EAAE,QAAQ,EAAiC;IAChE,CAAA,GAAA,uJAAA,CAAA,UAAiB,AAAD;IAEhB,qBACE;;0BACE,8OAAC,oIAAA,CAAA,aAAU;0BACT,cAAA,8OAAC,oIAAA,CAAA,iBAAc;oBAAC,WAAU;8BACxB,cAAA,8OAAC,oIAAA,CAAA,eAAY;wBAAC,WAAU;;0CACtB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,oIAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;0CAExB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,8IAAA,CAAA,UAAa;;;;;;;;;;;;;;;;;;;;;;;;;;YAKrB;;;AAGP", "debugId": null}}, {"offset": {"line": 430, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/collapsible.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as CollapsiblePrimitive from '@radix-ui/react-collapsible';\r\n\r\nfunction Collapsible({\r\n  ...props\r\n}: React.ComponentProps<typeof CollapsiblePrimitive.Root>) {\r\n  return <CollapsiblePrimitive.Root data-slot='collapsible' {...props} />;\r\n}\r\n\r\nfunction CollapsibleTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof CollapsiblePrimitive.CollapsibleTrigger>) {\r\n  return (\r\n    <CollapsiblePrimitive.CollapsibleTrigger\r\n      data-slot='collapsible-trigger'\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CollapsibleContent({\r\n  ...props\r\n}: React.ComponentProps<typeof CollapsiblePrimitive.CollapsibleContent>) {\r\n  return (\r\n    <CollapsiblePrimitive.CollapsibleContent\r\n      data-slot='collapsible-content'\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Collapsible, CollapsibleTrigger, CollapsibleContent };\r\n"], "names": [], "mappings": ";;;;;;AAEA;AAFA;;;AAIA,SAAS,YAAY,EACnB,GAAG,OACoD;IACvD,qBAAO,8OAAC,uKAAA,CAAA,OAAyB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AACrE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACkE;IACrE,qBACE,8OAAC,uKAAA,CAAA,qBAAuC;QACtC,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACkE;IACrE,qBACE,8OAAC,uKAAA,CAAA,qBAAuC;QACtC,aAAU;QACT,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 477, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\r\nimport { twMerge } from 'tailwind-merge';\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\nexport function formatBytes(\r\n  bytes: number,\r\n  opts: {\r\n    decimals?: number;\r\n    sizeType?: 'accurate' | 'normal';\r\n  } = {}\r\n) {\r\n  const { decimals = 0, sizeType = 'normal' } = opts;\r\n\r\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\r\n  const accurateSizes = ['Bytes', 'KiB', 'MiB', 'GiB', 'TiB'];\r\n  if (bytes === 0) return '0 Byte';\r\n  const i = Math.floor(Math.log(bytes) / Math.log(1024));\r\n  return `${(bytes / Math.pow(1024, i)).toFixed(decimals)} ${\r\n    sizeType === 'accurate'\r\n      ? (accurateSizes[i] ?? 'Bytest')\r\n      : (sizes[i] ?? 'Bytes')\r\n  }`;\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YACd,KAAa,EACb,OAGI,CAAC,CAAC;IAEN,MAAM,EAAE,WAAW,CAAC,EAAE,WAAW,QAAQ,EAAE,GAAG;IAE9C,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;KAAK;IAC/C,MAAM,gBAAgB;QAAC;QAAS;QAAO;QAAO;QAAO;KAAM;IAC3D,IAAI,UAAU,GAAG,OAAO;IACxB,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAChD,OAAO,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,EAAE,EAAE,OAAO,CAAC,UAAU,CAAC,EACvD,aAAa,aACR,aAAa,CAAC,EAAE,IAAI,WACpB,KAAK,CAAC,EAAE,IAAI,SACjB;AACJ", "debugId": null}}, {"offset": {"line": 514, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as DropdownMenuPrimitive from '@radix-ui/react-dropdown-menu';\r\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from 'lucide-react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction DropdownMenu({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\r\n  return <DropdownMenuPrimitive.Root data-slot='dropdown-menu' {...props} />;\r\n}\r\n\r\nfunction DropdownMenuPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal data-slot='dropdown-menu-portal' {...props} />\r\n  );\r\n}\r\n\r\nfunction DropdownMenuTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Trigger\r\n      data-slot='dropdown-menu-trigger'\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DropdownMenuContent({\r\n  className,\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal>\r\n      <DropdownMenuPrimitive.Content\r\n        data-slot='dropdown-menu-content'\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          'bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md',\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </DropdownMenuPrimitive.Portal>\r\n  );\r\n}\r\n\r\nfunction DropdownMenuGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Group data-slot='dropdown-menu-group' {...props} />\r\n  );\r\n}\r\n\r\nfunction DropdownMenuItem({\r\n  className,\r\n  inset,\r\n  variant = 'default',\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\r\n  inset?: boolean;\r\n  variant?: 'default' | 'destructive';\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Item\r\n      data-slot='dropdown-menu-item'\r\n      data-inset={inset}\r\n      data-variant={variant}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground focus:[&_svg:not([class*='text-'])]:text-accent-foreground relative flex cursor-pointer items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DropdownMenuCheckboxItem({\r\n  className,\r\n  children,\r\n  checked,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.CheckboxItem\r\n      data-slot='dropdown-menu-checkbox-item'\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      checked={checked}\r\n      {...props}\r\n    >\r\n      <span className='pointer-events-none absolute left-2 flex size-3.5 items-center justify-center'>\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CheckIcon className='size-4' />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.CheckboxItem>\r\n  );\r\n}\r\n\r\nfunction DropdownMenuRadioGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioGroup\r\n      data-slot='dropdown-menu-radio-group'\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DropdownMenuRadioItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioItem\r\n      data-slot='dropdown-menu-radio-item'\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className='pointer-events-none absolute left-2 flex size-3.5 items-center justify-center'>\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CircleIcon className='size-2 fill-current' />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.RadioItem>\r\n  );\r\n}\r\n\r\nfunction DropdownMenuLabel({\r\n  className,\r\n  inset,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\r\n  inset?: boolean;\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Label\r\n      data-slot='dropdown-menu-label'\r\n      data-inset={inset}\r\n      className={cn(\r\n        'px-2 py-1.5 text-sm font-medium data-[inset]:pl-8',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DropdownMenuSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Separator\r\n      data-slot='dropdown-menu-separator'\r\n      className={cn('bg-border -mx-1 my-1 h-px', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DropdownMenuShortcut({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'span'>) {\r\n  return (\r\n    <span\r\n      data-slot='dropdown-menu-shortcut'\r\n      className={cn(\r\n        'text-muted-foreground ml-auto text-xs tracking-widest',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DropdownMenuSub({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\r\n  return <DropdownMenuPrimitive.Sub data-slot='dropdown-menu-sub' {...props} />;\r\n}\r\n\r\nfunction DropdownMenuSubTrigger({\r\n  className,\r\n  inset,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n  inset?: boolean;\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubTrigger\r\n      data-slot='dropdown-menu-sub-trigger'\r\n      data-inset={inset}\r\n      className={cn(\r\n        'focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8',\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <ChevronRightIcon className='ml-auto size-4' />\r\n    </DropdownMenuPrimitive.SubTrigger>\r\n  );\r\n}\r\n\r\nfunction DropdownMenuSubContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubContent\r\n      data-slot='dropdown-menu-sub-content'\r\n      className={cn(\r\n        'bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuPortal,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuLabel,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioGroup,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuSubContent\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0qBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 776, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/hooks/use-mobile.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nconst MOBILE_BREAKPOINT = 768;\r\n\r\nexport function useIsMobile() {\r\n  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(\r\n    undefined\r\n  );\r\n\r\n  React.useEffect(() => {\r\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`);\r\n    const onChange = () => {\r\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);\r\n    };\r\n    mql.addEventListener('change', onChange);\r\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);\r\n    return () => mql.removeEventListener('change', onChange);\r\n  }, []);\r\n\r\n  return !!isMobile;\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,oBAAoB;AAEnB,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAC3C;IAGF,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,MAAM,OAAO,UAAU,CAAC,CAAC,YAAY,EAAE,oBAAoB,EAAE,GAAG,CAAC;QACvE,MAAM,WAAW;YACf,YAAY,OAAO,UAAU,GAAG;QAClC;QACA,IAAI,gBAAgB,CAAC,UAAU;QAC/B,YAAY,OAAO,UAAU,GAAG;QAChC,OAAO,IAAM,IAAI,mBAAmB,CAAC,UAAU;IACjD,GAAG,EAAE;IAEL,OAAO,CAAC,CAAC;AACX", "debugId": null}}, {"offset": {"line": 801, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { cva, type VariantProps } from 'class-variance-authority';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',\r\n        destructive:\r\n          'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\r\n        outline:\r\n          'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50',\r\n        secondary:\r\n          'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',\r\n        ghost:\r\n          'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',\r\n        link: 'text-primary underline-offset-4 hover:underline',\r\n        iai: 'bg-[var(--iai-primary)] text-white shadow-xs hover:bg-[var(--iai-secondary)] focus-visible:ring-[var(--iai-primary)]/20',\r\n        'iai-outline': 'border border-[var(--iai-primary)] text-[var(--iai-primary)] bg-background shadow-xs hover:bg-[var(--iai-primary)] hover:text-white'\r\n      },\r\n      size: {\r\n        default: 'h-9 px-4 py-2 has-[>svg]:px-3',\r\n        sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',\r\n        lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',\r\n        icon: 'size-9'\r\n      }\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default',\r\n      size: 'default'\r\n    }\r\n  }\r\n);\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<'button'> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean;\r\n  }) {\r\n  const Comp = asChild ? Slot : 'button';\r\n\r\n  return (\r\n    <Comp\r\n      data-slot='button'\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,8cACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;YACN,KAAK;YACL,eAAe;QACjB;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 860, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<'input'>) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot='input'\r\n      className={cn(\r\n        'file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',\r\n        'focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]',\r\n        'aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Input };\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 886, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Separator = React.forwardRef<\r\n  React.ElementRef<typeof SeparatorPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>\r\n>((\r\n  { className, orientation = \"horizontal\", decorative = true, ...props },\r\n  ref\r\n) => (\r\n  <SeparatorPrimitive.Root\r\n    ref={ref}\r\n    decorative={decorative}\r\n    orientation={orientation}\r\n    className={cn(\r\n      \"shrink-0 bg-border\",\r\n      orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nSeparator.displayName = SeparatorPrimitive.Root.displayName\r\n\r\nexport { Separator }"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CACA,EAAE,SAAS,EAAE,cAAc,YAAY,EAAE,aAAa,IAAI,EAAE,GAAG,OAAO,EACtE,oBAEA,8OAAC,qKAAA,CAAA,OAAuB;QACtB,KAAK;QACL,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sBACA,gBAAgB,eAAe,mBAAmB,kBAClD;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG,qKAAA,CAAA,OAAuB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 917, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/sheet.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as SheetPrimitive from '@radix-ui/react-dialog';\r\nimport { XIcon } from 'lucide-react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\r\n  return <SheetPrimitive.Root data-slot='sheet' {...props} />;\r\n}\r\n\r\nfunction SheetTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\r\n  return <SheetPrimitive.Trigger data-slot='sheet-trigger' {...props} />;\r\n}\r\n\r\nfunction SheetClose({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\r\n  return <SheetPrimitive.Close data-slot='sheet-close' {...props} />;\r\n}\r\n\r\nfunction SheetPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\r\n  return <SheetPrimitive.Portal data-slot='sheet-portal' {...props} />;\r\n}\r\n\r\nfunction SheetOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\r\n  return (\r\n    <SheetPrimitive.Overlay\r\n      data-slot='sheet-overlay'\r\n      className={cn(\r\n        'data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SheetContent({\r\n  className,\r\n  children,\r\n  side = 'right',\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\r\n  side?: 'top' | 'right' | 'bottom' | 'left';\r\n}) {\r\n  return (\r\n    <SheetPortal>\r\n      <SheetOverlay />\r\n      <SheetPrimitive.Content\r\n        data-slot='sheet-content'\r\n        className={cn(\r\n          'bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500',\r\n          side === 'right' &&\r\n            'data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm',\r\n          side === 'left' &&\r\n            'data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm',\r\n          side === 'top' &&\r\n            'data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b',\r\n          side === 'bottom' &&\r\n            'data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t',\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <SheetPrimitive.Close className='ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none'>\r\n          <XIcon className='size-4' />\r\n          <span className='sr-only'>Close</span>\r\n        </SheetPrimitive.Close>\r\n      </SheetPrimitive.Content>\r\n    </SheetPortal>\r\n  );\r\n}\r\n\r\nfunction SheetHeader({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='sheet-header'\r\n      className={cn('flex flex-col gap-1.5 p-4', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SheetFooter({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='sheet-footer'\r\n      className={cn('mt-auto flex flex-col gap-2 p-4', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SheetTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\r\n  return (\r\n    <SheetPrimitive.Title\r\n      data-slot='sheet-title'\r\n      className={cn('text-foreground font-semibold', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SheetDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\r\n  return (\r\n    <SheetPrimitive.Description\r\n      data-slot='sheet-description'\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport {\r\n  Sheet,\r\n  SheetTrigger,\r\n  SheetClose,\r\n  SheetContent,\r\n  SheetHeader,\r\n  SheetFooter,\r\n  SheetTitle,\r\n  SheetDescription\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,8OAAC,kKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,kKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,8OAAC,gMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,8OAAC,kKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1089, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from '@/lib/utils';\r\n\r\nfunction Skeleton({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='skeleton'\r\n      className={cn('bg-accent animate-pulse rounded-md', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Skeleton };\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/tooltip.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as TooltipPrimitive from '@radix-ui/react-tooltip';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction TooltipProvider({\r\n  delayDuration = 0,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {\r\n  return (\r\n    <TooltipPrimitive.Provider\r\n      data-slot='tooltip-provider'\r\n      delayDuration={delayDuration}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction Tooltip({\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Root>) {\r\n  return (\r\n    <TooltipProvider>\r\n      <TooltipPrimitive.Root data-slot='tooltip' {...props} />\r\n    </TooltipProvider>\r\n  );\r\n}\r\n\r\nfunction TooltipTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {\r\n  return <TooltipPrimitive.Trigger data-slot='tooltip-trigger' {...props} />;\r\n}\r\n\r\nfunction TooltipContent({\r\n  className,\r\n  sideOffset = 0,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Content>) {\r\n  return (\r\n    <TooltipPrimitive.Portal>\r\n      <TooltipPrimitive.Content\r\n        data-slot='tooltip-content'\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          'bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance',\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <TooltipPrimitive.Arrow className='bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]' />\r\n      </TooltipPrimitive.Content>\r\n    </TooltipPrimitive.Portal>\r\n  );\r\n}\r\n\r\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider };\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,gBAAgB,EACvB,gBAAgB,CAAC,EACjB,GAAG,OACoD;IACvD,qBACE,8OAAC,mKAAA,CAAA,WAAyB;QACxB,aAAU;QACV,eAAe;QACd,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBACE,8OAAC;kBACC,cAAA,8OAAC,mKAAA,CAAA,OAAqB;YAAC,aAAU;YAAW,GAAG,KAAK;;;;;;;;;;;AAG1D;AAEA,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,8OAAC,mKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,aAAa,CAAC,EACd,QAAQ,EACR,GAAG,OACmD;IACtD,qBACE,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0aACA;YAED,GAAG,KAAK;;gBAER;8BACD,8OAAC,mKAAA,CAAA,QAAsB;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI1C", "debugId": null}}, {"offset": {"line": 1199, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/sidebar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { VariantProps, cva } from 'class-variance-authority';\r\nimport { PanelLeftIcon } from 'lucide-react';\r\nimport { useIsMobile } from '@/hooks/use-mobile';\r\nimport { cn } from '@/lib/utils';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Separator } from '@/components/ui/separator';\r\nimport {\r\n  Sheet,\r\n  SheetContent,\r\n  SheetDescription,\r\n  SheetHeader,\r\n  SheetTitle\r\n} from '@/components/ui/sheet';\r\nimport { Skeleton } from '@/components/ui/skeleton';\r\nimport {\r\n  Tooltip,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger\r\n} from '@/components/ui/tooltip';\r\n\r\nconst SIDEBAR_COOKIE_NAME = 'sidebar_state';\r\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7;\r\nconst SIDEBAR_WIDTH = '16rem';\r\nconst SIDEBAR_WIDTH_MOBILE = '18rem';\r\nconst SIDEBAR_WIDTH_ICON = '3rem';\r\nconst SIDEBAR_KEYBOARD_SHORTCUT = 'b';\r\n\r\ntype SidebarContextProps = {\r\n  state: 'expanded' | 'collapsed';\r\n  open: boolean;\r\n  setOpen: (open: boolean) => void;\r\n  openMobile: boolean;\r\n  setOpenMobile: (open: boolean) => void;\r\n  isMobile: boolean;\r\n  toggleSidebar: () => void;\r\n};\r\n\r\nconst SidebarContext = React.createContext<SidebarContextProps | null>(null);\r\n\r\nfunction useSidebar() {\r\n  const context = React.useContext(SidebarContext);\r\n  if (!context) {\r\n    throw new Error('useSidebar must be used within a SidebarProvider.');\r\n  }\r\n\r\n  return context;\r\n}\r\n\r\nfunction SidebarProvider({\r\n  defaultOpen = true,\r\n  open: openProp,\r\n  onOpenChange: setOpenProp,\r\n  className,\r\n  style,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<'div'> & {\r\n  defaultOpen?: boolean;\r\n  open?: boolean;\r\n  onOpenChange?: (open: boolean) => void;\r\n}) {\r\n  const isMobile = useIsMobile();\r\n  const [openMobile, setOpenMobile] = React.useState(false);\r\n\r\n  // This is the internal state of the sidebar.\r\n  // We use openProp and setOpenProp for control from outside the component.\r\n  const [_open, _setOpen] = React.useState(defaultOpen);\r\n  const open = openProp ?? _open;\r\n  const setOpen = React.useCallback(\r\n    (value: boolean | ((value: boolean) => boolean)) => {\r\n      const openState = typeof value === 'function' ? value(open) : value;\r\n      if (setOpenProp) {\r\n        setOpenProp(openState);\r\n      } else {\r\n        _setOpen(openState);\r\n      }\r\n\r\n      // This sets the cookie to keep the sidebar state.\r\n      document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`;\r\n    },\r\n    [setOpenProp, open]\r\n  );\r\n\r\n  // Helper to toggle the sidebar.\r\n  const toggleSidebar = React.useCallback(() => {\r\n    return isMobile ? setOpenMobile((open) => !open) : setOpen((open) => !open);\r\n  }, [isMobile, setOpen, setOpenMobile]);\r\n\r\n  // Adds a keyboard shortcut to toggle the sidebar.\r\n  React.useEffect(() => {\r\n    const handleKeyDown = (event: KeyboardEvent) => {\r\n      if (\r\n        event.key === SIDEBAR_KEYBOARD_SHORTCUT &&\r\n        (event.metaKey || event.ctrlKey)\r\n      ) {\r\n        event.preventDefault();\r\n        toggleSidebar();\r\n      }\r\n    };\r\n\r\n    window.addEventListener('keydown', handleKeyDown);\r\n    return () => window.removeEventListener('keydown', handleKeyDown);\r\n  }, [toggleSidebar]);\r\n\r\n  // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\r\n  // This makes it easier to style the sidebar with Tailwind classes.\r\n  const state = open ? 'expanded' : 'collapsed';\r\n\r\n  const contextValue = React.useMemo<SidebarContextProps>(\r\n    () => ({\r\n      state,\r\n      open,\r\n      setOpen,\r\n      isMobile,\r\n      openMobile,\r\n      setOpenMobile,\r\n      toggleSidebar\r\n    }),\r\n    [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]\r\n  );\r\n\r\n  return (\r\n    <SidebarContext.Provider value={contextValue}>\r\n      <TooltipProvider delayDuration={0}>\r\n        <div\r\n          data-slot='sidebar-wrapper'\r\n          style={\r\n            {\r\n              '--sidebar-width': SIDEBAR_WIDTH,\r\n              '--sidebar-width-icon': SIDEBAR_WIDTH_ICON,\r\n              ...style\r\n            } as React.CSSProperties\r\n          }\r\n          className={cn(\r\n            'group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full',\r\n            className\r\n          )}\r\n          {...props}\r\n        >\r\n          {children}\r\n        </div>\r\n      </TooltipProvider>\r\n    </SidebarContext.Provider>\r\n  );\r\n}\r\n\r\nfunction Sidebar({\r\n  side = 'left',\r\n  variant = 'sidebar',\r\n  collapsible = 'offcanvas',\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<'div'> & {\r\n  side?: 'left' | 'right';\r\n  variant?: 'sidebar' | 'floating' | 'inset';\r\n  collapsible?: 'offcanvas' | 'icon' | 'none';\r\n}) {\r\n  const { isMobile, state, openMobile, setOpenMobile } = useSidebar();\r\n\r\n  if (collapsible === 'none') {\r\n    return (\r\n      <div\r\n        data-slot='sidebar'\r\n        className={cn(\r\n          'bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col',\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (isMobile) {\r\n    return (\r\n      <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>\r\n        <SheetContent\r\n          data-sidebar='sidebar'\r\n          data-slot='sidebar'\r\n          data-mobile='true'\r\n          className='bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden'\r\n          style={\r\n            {\r\n              '--sidebar-width': SIDEBAR_WIDTH_MOBILE\r\n            } as React.CSSProperties\r\n          }\r\n          side={side}\r\n        >\r\n          <SheetHeader className='sr-only'>\r\n            <SheetTitle>Sidebar</SheetTitle>\r\n            <SheetDescription>Displays the mobile sidebar.</SheetDescription>\r\n          </SheetHeader>\r\n          <div className='flex h-full w-full flex-col'>{children}</div>\r\n        </SheetContent>\r\n      </Sheet>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className='group peer text-sidebar-foreground hidden md:block'\r\n      data-state={state}\r\n      data-collapsible={state === 'collapsed' ? collapsible : ''}\r\n      data-variant={variant}\r\n      data-side={side}\r\n      data-slot='sidebar'\r\n    >\r\n      {/* This is what handles the sidebar gap on desktop */}\r\n      <div\r\n        data-slot='sidebar-gap'\r\n        className={cn(\r\n          'relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear',\r\n          'group-data-[collapsible=offcanvas]:w-0',\r\n          'group-data-[side=right]:rotate-180',\r\n          variant === 'floating' || variant === 'inset'\r\n            ? 'group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]'\r\n            : 'group-data-[collapsible=icon]:w-(--sidebar-width-icon)'\r\n        )}\r\n      />\r\n      <div\r\n        data-slot='sidebar-container'\r\n        className={cn(\r\n          'fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex',\r\n          side === 'left'\r\n            ? 'left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]'\r\n            : 'right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]',\r\n          // Adjust the padding for floating and inset variants.\r\n          variant === 'floating' || variant === 'inset'\r\n            ? 'p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]'\r\n            : 'group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l',\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        <div\r\n          data-sidebar='sidebar'\r\n          data-slot='sidebar-inner'\r\n          className='bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm'\r\n        >\r\n          {children}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction SidebarTrigger({\r\n  className,\r\n  onClick,\r\n  ...props\r\n}: React.ComponentProps<typeof Button>) {\r\n  const { toggleSidebar } = useSidebar();\r\n\r\n  return (\r\n    <Button\r\n      data-sidebar='trigger'\r\n      data-slot='sidebar-trigger'\r\n      variant='ghost'\r\n      size='icon'\r\n      className={cn('size-7', className)}\r\n      onClick={(event) => {\r\n        onClick?.(event);\r\n        toggleSidebar();\r\n      }}\r\n      {...props}\r\n    >\r\n      <PanelLeftIcon />\r\n      <span className='sr-only'>Toggle Sidebar</span>\r\n    </Button>\r\n  );\r\n}\r\n\r\nfunction SidebarRail({ className, ...props }: React.ComponentProps<'button'>) {\r\n  const { toggleSidebar } = useSidebar();\r\n\r\n  return (\r\n    <button\r\n      data-sidebar='rail'\r\n      data-slot='sidebar-rail'\r\n      aria-label='Toggle Sidebar'\r\n      tabIndex={-1}\r\n      onClick={toggleSidebar}\r\n      title='Toggle Sidebar'\r\n      className={cn(\r\n        'hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex',\r\n        'in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize',\r\n        '[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize',\r\n        'hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full',\r\n        '[[data-side=left][data-collapsible=offcanvas]_&]:-right-2',\r\n        '[[data-side=right][data-collapsible=offcanvas]_&]:-left-2',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarInset({ className, ...props }: React.ComponentProps<'main'>) {\r\n  return (\r\n    <main\r\n      data-slot='sidebar-inset'\r\n      className={cn(\r\n        'bg-background relative flex w-full flex-1 flex-col',\r\n        'md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarInput({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof Input>) {\r\n  return (\r\n    <Input\r\n      data-slot='sidebar-input'\r\n      data-sidebar='input'\r\n      className={cn('bg-background h-8 w-full shadow-none', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarHeader({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='sidebar-header'\r\n      data-sidebar='header'\r\n      className={cn('flex flex-col gap-2 p-2', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarFooter({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='sidebar-footer'\r\n      data-sidebar='footer'\r\n      className={cn('flex flex-col gap-2 p-2', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof Separator>) {\r\n  return (\r\n    <Separator\r\n      data-slot='sidebar-separator'\r\n      data-sidebar='separator'\r\n      className={cn('bg-sidebar-border mx-2 w-auto', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarContent({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='sidebar-content'\r\n      data-sidebar='content'\r\n      className={cn(\r\n        'flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroup({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='sidebar-group'\r\n      data-sidebar='group'\r\n      className={cn('relative flex w-full min-w-0 flex-col p-2', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroupLabel({\r\n  className,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<'div'> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : 'div';\r\n\r\n  return (\r\n    <Comp\r\n      data-slot='sidebar-group-label'\r\n      data-sidebar='group-label'\r\n      className={cn(\r\n        'text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0',\r\n        'group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroupAction({\r\n  className,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<'button'> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : 'button';\r\n\r\n  return (\r\n    <Comp\r\n      data-slot='sidebar-group-action'\r\n      data-sidebar='group-action'\r\n      className={cn(\r\n        'text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground absolute top-3.5 right-3 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0',\r\n        // Increases the hit area of the button on mobile.\r\n        'after:absolute after:-inset-2 md:after:hidden',\r\n        'group-data-[collapsible=icon]:hidden',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroupContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='sidebar-group-content'\r\n      data-sidebar='group-content'\r\n      className={cn('w-full text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenu({ className, ...props }: React.ComponentProps<'ul'>) {\r\n  return (\r\n    <ul\r\n      data-slot='sidebar-menu'\r\n      data-sidebar='menu'\r\n      className={cn('flex w-full min-w-0 flex-col gap-1', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenuItem({ className, ...props }: React.ComponentProps<'li'>) {\r\n  return (\r\n    <li\r\n      data-slot='sidebar-menu-item'\r\n      data-sidebar='menu-item'\r\n      className={cn('group/menu-item relative', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nconst sidebarMenuButtonVariants = cva(\r\n  'peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0 cursor-pointer',\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: 'hover:bg-sidebar-accent hover:text-sidebar-accent-foreground',\r\n        outline:\r\n          'bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]'\r\n      },\r\n      size: {\r\n        default: 'h-8 text-sm',\r\n        sm: 'h-7 text-xs',\r\n        lg: 'h-12 text-sm group-data-[collapsible=icon]:p-0!'\r\n      }\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default',\r\n      size: 'default'\r\n    }\r\n  }\r\n);\r\n\r\nfunction SidebarMenuButton({\r\n  asChild = false,\r\n  isActive = false,\r\n  variant = 'default',\r\n  size = 'default',\r\n  tooltip,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'button'> & {\r\n  asChild?: boolean;\r\n  isActive?: boolean;\r\n  tooltip?: string | React.ComponentProps<typeof TooltipContent>;\r\n} & VariantProps<typeof sidebarMenuButtonVariants>) {\r\n  const Comp = asChild ? Slot : 'button';\r\n  const { isMobile, state } = useSidebar();\r\n\r\n  const button = (\r\n    <Comp\r\n      data-slot='sidebar-menu-button'\r\n      data-sidebar='menu-button'\r\n      data-size={size}\r\n      data-active={isActive}\r\n      className={cn(sidebarMenuButtonVariants({ variant, size }), className)}\r\n      {...props}\r\n    />\r\n  );\r\n\r\n  if (!tooltip) {\r\n    return button;\r\n  }\r\n\r\n  if (typeof tooltip === 'string') {\r\n    tooltip = {\r\n      children: tooltip\r\n    };\r\n  }\r\n\r\n  return (\r\n    <Tooltip>\r\n      <TooltipTrigger asChild>{button}</TooltipTrigger>\r\n      <TooltipContent\r\n        side='right'\r\n        align='center'\r\n        hidden={state !== 'collapsed' || isMobile}\r\n        {...tooltip}\r\n      />\r\n    </Tooltip>\r\n  );\r\n}\r\n\r\nfunction SidebarMenuAction({\r\n  className,\r\n  asChild = false,\r\n  showOnHover = false,\r\n  ...props\r\n}: React.ComponentProps<'button'> & {\r\n  asChild?: boolean;\r\n  showOnHover?: boolean;\r\n}) {\r\n  const Comp = asChild ? Slot : 'button';\r\n\r\n  return (\r\n    <Comp\r\n      data-slot='sidebar-menu-action'\r\n      data-sidebar='menu-action'\r\n      className={cn(\r\n        'text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground peer-hover/menu-button:text-sidebar-accent-foreground absolute top-1.5 right-1 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0',\r\n        // Increases the hit area of the button on mobile.\r\n        'after:absolute after:-inset-2 md:after:hidden',\r\n        'peer-data-[size=sm]/menu-button:top-1',\r\n        'peer-data-[size=default]/menu-button:top-1.5',\r\n        'peer-data-[size=lg]/menu-button:top-2.5',\r\n        'group-data-[collapsible=icon]:hidden',\r\n        showOnHover &&\r\n          'peer-data-[active=true]/menu-button:text-sidebar-accent-foreground group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 md:opacity-0',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenuBadge({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='sidebar-menu-badge'\r\n      data-sidebar='menu-badge'\r\n      className={cn(\r\n        'text-sidebar-foreground pointer-events-none absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums select-none',\r\n        'peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground',\r\n        'peer-data-[size=sm]/menu-button:top-1',\r\n        'peer-data-[size=default]/menu-button:top-1.5',\r\n        'peer-data-[size=lg]/menu-button:top-2.5',\r\n        'group-data-[collapsible=icon]:hidden',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenuSkeleton({\r\n  className,\r\n  showIcon = false,\r\n  ...props\r\n}: React.ComponentProps<'div'> & {\r\n  showIcon?: boolean;\r\n}) {\r\n  // Random width between 50 to 90%.\r\n  const width = React.useMemo(() => {\r\n    return `${Math.floor(Math.random() * 40) + 50}%`;\r\n  }, []);\r\n\r\n  return (\r\n    <div\r\n      data-slot='sidebar-menu-skeleton'\r\n      data-sidebar='menu-skeleton'\r\n      className={cn('flex h-8 items-center gap-2 rounded-md px-2', className)}\r\n      {...props}\r\n    >\r\n      {showIcon && (\r\n        <Skeleton\r\n          className='size-4 rounded-md'\r\n          data-sidebar='menu-skeleton-icon'\r\n        />\r\n      )}\r\n      <Skeleton\r\n        className='h-4 max-w-(--skeleton-width) flex-1'\r\n        data-sidebar='menu-skeleton-text'\r\n        style={\r\n          {\r\n            '--skeleton-width': width\r\n          } as React.CSSProperties\r\n        }\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction SidebarMenuSub({ className, ...props }: React.ComponentProps<'ul'>) {\r\n  return (\r\n    <ul\r\n      data-slot='sidebar-menu-sub'\r\n      data-sidebar='menu-sub'\r\n      className={cn(\r\n        'border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5',\r\n        'group-data-[collapsible=icon]:hidden',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenuSubItem({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'li'>) {\r\n  return (\r\n    <li\r\n      data-slot='sidebar-menu-sub-item'\r\n      data-sidebar='menu-sub-item'\r\n      className={cn('group/menu-sub-item relative', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenuSubButton({\r\n  asChild = false,\r\n  size = 'md',\r\n  isActive = false,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'a'> & {\r\n  asChild?: boolean;\r\n  size?: 'sm' | 'md';\r\n  isActive?: boolean;\r\n}) {\r\n  const Comp = asChild ? Slot : 'a';\r\n\r\n  return (\r\n    <Comp\r\n      data-slot='sidebar-menu-sub-button'\r\n      data-sidebar='menu-sub-button'\r\n      data-size={size}\r\n      data-active={isActive}\r\n      className={cn(\r\n        'text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0',\r\n        'data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground',\r\n        size === 'sm' && 'text-xs',\r\n        size === 'md' && 'text-sm',\r\n        'group-data-[collapsible=icon]:hidden',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport {\r\n  Sidebar,\r\n  SidebarContent,\r\n  SidebarFooter,\r\n  SidebarGroup,\r\n  SidebarGroupAction,\r\n  SidebarGroupContent,\r\n  SidebarGroupLabel,\r\n  SidebarHeader,\r\n  SidebarInput,\r\n  SidebarInset,\r\n  SidebarMenu,\r\n  SidebarMenuAction,\r\n  SidebarMenuBadge,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  SidebarMenuSkeleton,\r\n  SidebarMenuSub,\r\n  SidebarMenuSubButton,\r\n  SidebarMenuSubItem,\r\n  SidebarProvider,\r\n  SidebarRail,\r\n  SidebarSeparator,\r\n  SidebarTrigger,\r\n  useSidebar\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AAnBA;;;;;;;;;;;;;;AA0BA,MAAM,sBAAsB;AAC5B,MAAM,yBAAyB,KAAK,KAAK,KAAK;AAC9C,MAAM,gBAAgB;AACtB,MAAM,uBAAuB;AAC7B,MAAM,qBAAqB;AAC3B,MAAM,4BAA4B;AAYlC,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAA8B;AAEvE,SAAS;IACP,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;AAEA,SAAS,gBAAgB,EACvB,cAAc,IAAI,EAClB,MAAM,QAAQ,EACd,cAAc,WAAW,EACzB,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAKJ;IACC,MAAM,WAAW,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAEnD,6CAA6C;IAC7C,0EAA0E;IAC1E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IACzC,MAAM,OAAO,YAAY;IACzB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAC9B,CAAC;QACC,MAAM,YAAY,OAAO,UAAU,aAAa,MAAM,QAAQ;QAC9D,IAAI,aAAa;YACf,YAAY;QACd,OAAO;YACL,SAAS;QACX;QAEA,kDAAkD;QAClD,SAAS,MAAM,GAAG,GAAG,oBAAoB,CAAC,EAAE,UAAU,kBAAkB,EAAE,wBAAwB;IACpG,GACA;QAAC;QAAa;KAAK;IAGrB,gCAAgC;IAChC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE;QACtC,OAAO,WAAW,cAAc,CAAC,OAAS,CAAC,QAAQ,QAAQ,CAAC,OAAS,CAAC;IACxE,GAAG;QAAC;QAAU;QAAS;KAAc;IAErC,kDAAkD;IAClD,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,gBAAgB,CAAC;YACrB,IACE,MAAM,GAAG,KAAK,6BACd,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,GAC/B;gBACA,MAAM,cAAc;gBACpB;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;IACrD,GAAG;QAAC;KAAc;IAElB,yEAAyE;IACzE,mEAAmE;IACnE,MAAM,QAAQ,OAAO,aAAa;IAElC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAC/B,IAAM,CAAC;YACL;YACA;YACA;YACA;YACA;YACA;YACA;QACF,CAAC,GACD;QAAC;QAAO;QAAM;QAAS;QAAU;QAAY;QAAe;KAAc;IAG5E,qBACE,8OAAC,eAAe,QAAQ;QAAC,OAAO;kBAC9B,cAAA,8OAAC,mIAAA,CAAA,kBAAe;YAAC,eAAe;sBAC9B,cAAA,8OAAC;gBACC,aAAU;gBACV,OACE;oBACE,mBAAmB;oBACnB,wBAAwB;oBACxB,GAAG,KAAK;gBACV;gBAEF,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mFACA;gBAED,GAAG,KAAK;0BAER;;;;;;;;;;;;;;;;AAKX;AAEA,SAAS,QAAQ,EACf,OAAO,MAAM,EACb,UAAU,SAAS,EACnB,cAAc,WAAW,EACzB,SAAS,EACT,QAAQ,EACR,GAAG,OAKJ;IACC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEvD,IAAI,gBAAgB,QAAQ;QAC1B,qBACE,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;YAED,GAAG,KAAK;sBAER;;;;;;IAGP;IAEA,IAAI,UAAU;QACZ,qBACE,8OAAC,iIAAA,CAAA,QAAK;YAAC,MAAM;YAAY,cAAc;YAAgB,GAAG,KAAK;sBAC7D,cAAA,8OAAC,iIAAA,CAAA,eAAY;gBACX,gBAAa;gBACb,aAAU;gBACV,eAAY;gBACZ,WAAU;gBACV,OACE;oBACE,mBAAmB;gBACrB;gBAEF,MAAM;;kCAEN,8OAAC,iIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC,iIAAA,CAAA,aAAU;0CAAC;;;;;;0CACZ,8OAAC,iIAAA,CAAA,mBAAgB;0CAAC;;;;;;;;;;;;kCAEpB,8OAAC;wBAAI,WAAU;kCAA+B;;;;;;;;;;;;;;;;;IAItD;IAEA,qBACE,8OAAC;QACC,WAAU;QACV,cAAY;QACZ,oBAAkB,UAAU,cAAc,cAAc;QACxD,gBAAc;QACd,aAAW;QACX,aAAU;;0BAGV,8OAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2FACA,0CACA,sCACA,YAAY,cAAc,YAAY,UAClC,qFACA;;;;;;0BAGR,8OAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wHACA,SAAS,SACL,mFACA,oFACJ,sDAAsD;gBACtD,YAAY,cAAc,YAAY,UAClC,6FACA,2HACJ;gBAED,GAAG,KAAK;0BAET,cAAA,8OAAC;oBACC,gBAAa;oBACb,aAAU;oBACV,WAAU;8BAET;;;;;;;;;;;;;;;;;AAKX;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,OAAO,EACP,GAAG,OACiC;IACpC,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,gBAAa;QACb,aAAU;QACV,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACxB,SAAS,CAAC;YACR,UAAU;YACV;QACF;QACC,GAAG,KAAK;;0BAET,8OAAC,oNAAA,CAAA,gBAAa;;;;;0BACd,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAuC;IAC1E,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,8OAAC;QACC,gBAAa;QACb,aAAU;QACV,cAAW;QACX,UAAU,CAAC;QACX,SAAS;QACT,OAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mPACA,4EACA,0HACA,2JACA,6DACA,6DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAqC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA,mNACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACgC;IACnC,qBACE,8OAAC,iIAAA,CAAA,QAAK;QACJ,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;QACrD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACoC;IACvC,qBACE,8OAAC,qIAAA,CAAA,YAAS;QACR,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC1E,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACiD;IACpD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4OACA,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACoD;IACvD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8RACA,kDAAkD;QAClD,iDACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;QAC/B,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAmC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAmC;IAC1E,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,MAAM,4BAA4B,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EAClC,o0BACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,kBAAkB,EACzB,UAAU,KAAK,EACf,WAAW,KAAK,EAChB,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,OAAO,EACP,SAAS,EACT,GAAG,OAK6C;IAChD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG;IAE5B,MAAM,uBACJ,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;YAAE;YAAS;QAAK,IAAI;QAC3D,GAAG,KAAK;;;;;;IAIb,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,IAAI,OAAO,YAAY,UAAU;QAC/B,UAAU;YACR,UAAU;QACZ;IACF;IAEA,qBACE,8OAAC,mIAAA,CAAA,UAAO;;0BACN,8OAAC,mIAAA,CAAA,iBAAc;gBAAC,OAAO;0BAAE;;;;;;0BACzB,8OAAC,mIAAA,CAAA,iBAAc;gBACb,MAAK;gBACL,OAAM;gBACN,QAAQ,UAAU,eAAe;gBAChC,GAAG,OAAO;;;;;;;;;;;;AAInB;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,UAAU,KAAK,EACf,cAAc,KAAK,EACnB,GAAG,OAIJ;IACC,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oVACA,kDAAkD;QAClD,iDACA,yCACA,gDACA,2CACA,wCACA,eACE,4LACF;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0KACA,4HACA,yCACA,gDACA,2CACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,WAAW,KAAK,EAChB,GAAG,OAGJ;IACC,kCAAkC;IAClC,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE;QAC1B,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG,CAAC,CAAC;IAClD,GAAG,EAAE;IAEL,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+CAA+C;QAC5D,GAAG,KAAK;;YAER,0BACC,8OAAC,oIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;;;;;;0BAGjB,8OAAC,oIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;gBACb,OACE;oBACE,oBAAoB;gBACtB;;;;;;;;;;;;AAKV;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACwB;IAC3B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gCAAgC;QAC7C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,UAAU,KAAK,EACf,OAAO,IAAI,EACX,WAAW,KAAK,EAChB,SAAS,EACT,GAAG,OAKJ;IACC,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA,0FACA,SAAS,QAAQ,WACjB,SAAS,QAAQ,WACjB,wCACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1848, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/avatar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as AvatarPrimitive from '@radix-ui/react-avatar';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Avatar({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\r\n  return (\r\n    <AvatarPrimitive.Root\r\n      data-slot='avatar'\r\n      className={cn(\r\n        'relative flex size-8 shrink-0 overflow-hidden rounded-full',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AvatarImage({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\r\n  return (\r\n    <AvatarPrimitive.Image\r\n      data-slot='avatar-image'\r\n      className={cn('aspect-square size-full', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AvatarFallback({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\r\n  return (\r\n    <AvatarPrimitive.Fallback\r\n      data-slot='avatar-fallback'\r\n      className={cn(\r\n        'bg-muted flex size-full items-center justify-center rounded-full',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback };\r\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1900, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/lib/auth.ts"], "sourcesContent": ["import { AuthUser } from '@/types/database';\r\n\r\n// Client-side auth utilities\r\nexport const authStorage = {\r\n  setUser: (user: AuthUser) => {\r\n    if (typeof window !== 'undefined') {\r\n      localStorage.setItem('auth_user', JSON.stringify(user));\r\n    }\r\n  },\r\n\r\n  getUser: (): AuthUser | null => {\r\n    if (typeof window !== 'undefined') {\r\n      const stored = localStorage.getItem('auth_user');\r\n      return stored ? JSON.parse(stored) : null;\r\n    }\r\n    return null;\r\n  },\r\n\r\n  removeUser: () => {\r\n    if (typeof window !== 'undefined') {\r\n      localStorage.removeItem('auth_user');\r\n    }\r\n  },\r\n\r\n  isAuthenticated: (): boolean => {\r\n    return authStorage.getUser() !== null;\r\n  },\r\n\r\n  hasRole: (role: string): boolean => {\r\n    const user = authStorage.getUser();\r\n    return user?.role === role;\r\n  },\r\n\r\n  isSuperAdmin: (): boolean => {\r\n    return authStorage.hasRole('super_admin');\r\n  },\r\n\r\n  isTeacher: (): boolean => {\r\n    return authStorage.hasRole('teacher');\r\n  },\r\n\r\n  isStudent: (): boolean => {\r\n    return authStorage.hasRole('student');\r\n  }\r\n};\r\n\r\n// Role-based redirect logic\r\nexport const getRedirectPath = (user: AuthUser): string => {\r\n  switch (user.role) {\r\n    case 'super_admin':\r\n      return '/dashboard/admin';\r\n    case 'teacher':\r\n      return '/dashboard/teacher';\r\n    case 'student':\r\n      return '/courses';\r\n    default:\r\n      return '/dashboard';\r\n  }\r\n};\r\n\r\n// Protected route checker\r\nexport const checkAuth = (): AuthUser | null => {\r\n  const user = authStorage.getUser();\r\n  if (!user) {\r\n    // Redirect to sign in if not authenticated\r\n    if (typeof window !== 'undefined') {\r\n      window.location.href = '/auth/sign-in';\r\n    }\r\n    return null;\r\n  }\r\n  return user;\r\n};\r\n\r\n// Role-based access control\r\nexport const requireRole = (requiredRole: string): AuthUser | null => {\r\n  const user = checkAuth();\r\n  if (!user) return null;\r\n\r\n  if (user.role !== requiredRole) {\r\n    // Redirect to appropriate dashboard if wrong role\r\n    if (typeof window !== 'undefined') {\r\n      window.location.href = getRedirectPath(user);\r\n    }\r\n    return null;\r\n  }\r\n  return user;\r\n};\r\n\r\n// Multiple roles checker\r\nexport const requireAnyRole = (roles: string[]): AuthUser | null => {\r\n  const user = checkAuth();\r\n  if (!user) return null;\r\n\r\n  if (!roles.includes(user.role)) {\r\n    // Redirect to appropriate dashboard if wrong role\r\n    if (typeof window !== 'undefined') {\r\n      window.location.href = getRedirectPath(user);\r\n    }\r\n    return null;\r\n  }\r\n  return user;\r\n};\r\n"], "names": [], "mappings": ";;;;;;;AAGO,MAAM,cAAc;IACzB,SAAS,CAAC;QACR,uCAAmC;;QAEnC;IACF;IAEA,SAAS;QACP,uCAAmC;;QAGnC;QACA,OAAO;IACT;IAEA,YAAY;QACV,uCAAmC;;QAEnC;IACF;IAEA,iBAAiB;QACf,OAAO,YAAY,OAAO,OAAO;IACnC;IAEA,SAAS,CAAC;QACR,MAAM,OAAO,YAAY,OAAO;QAChC,OAAO,MAAM,SAAS;IACxB;IAEA,cAAc;QACZ,OAAO,YAAY,OAAO,CAAC;IAC7B;IAEA,WAAW;QACT,OAAO,YAAY,OAAO,CAAC;IAC7B;IAEA,WAAW;QACT,OAAO,YAAY,OAAO,CAAC;IAC7B;AACF;AAGO,MAAM,kBAAkB,CAAC;IAC9B,OAAQ,KAAK,IAAI;QACf,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAGO,MAAM,YAAY;IACvB,MAAM,OAAO,YAAY,OAAO;IAChC,IAAI,CAAC,MAAM;QACT,2CAA2C;QAC3C,uCAAmC;;QAEnC;QACA,OAAO;IACT;IACA,OAAO;AACT;AAGO,MAAM,cAAc,CAAC;IAC1B,MAAM,OAAO;IACb,IAAI,CAAC,MAAM,OAAO;IAElB,IAAI,KAAK,IAAI,KAAK,cAAc;QAC9B,kDAAkD;QAClD,uCAAmC;;QAEnC;QACA,OAAO;IACT;IACA,OAAO;AACT;AAGO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,OAAO;IACb,IAAI,CAAC,MAAM,OAAO;IAElB,IAAI,CAAC,MAAM,QAAQ,CAAC,KAAK,IAAI,GAAG;QAC9B,kDAAkD;QAClD,uCAAmC;;QAEnC;QACA,OAAO;IACT;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1994, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/hooks/use-auth.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { AuthUser } from '@/types/database';\r\nimport { authStorage } from '@/lib/auth';\r\nimport { useRouter } from 'next/navigation';\r\n\r\nexport function useAuth() {\r\n  const [user, setUser] = useState<AuthUser | null>(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const router = useRouter();\r\n\r\n  useEffect(() => {\r\n    const storedUser = authStorage.getUser();\r\n    if (storedUser) {\r\n      setUser(storedUser);\r\n    }\r\n    setLoading(false);\r\n  }, []);\r\n\r\n  const signOut = () => {\r\n    authStorage.removeUser();\r\n    setUser(null);\r\n    router.push('/auth/sign-in');\r\n  };\r\n\r\n  return { user, loading, signOut };\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;AAEA;AACA;AALA;;;;AAOO,SAAS;IACd,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa,kHAAA,CAAA,cAAW,CAAC,OAAO;QACtC,IAAI,YAAY;YACd,QAAQ;QACV;QACA,WAAW;IACb,GAAG,EAAE;IAEL,MAAM,UAAU;QACd,kHAAA,CAAA,cAAW,CAAC,UAAU;QACtB,QAAQ;QACR,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;QAAE;QAAM;QAAS;IAAQ;AAClC", "debugId": null}}, {"offset": {"line": 2032, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/user-avatar-profile.tsx"], "sourcesContent": ["import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\r\nimport { useAuth } from '@/hooks/use-auth';\r\n\r\ninterface UserAvatarProfileProps {\r\n  className?: string;\r\n  showInfo?: boolean;\r\n}\r\n\r\nexport function UserAvatarProfile({\r\n  className,\r\n  showInfo = false\r\n}: UserAvatarProfileProps) {\r\n  const { user } = useAuth();\r\n\r\n  if (!user) {\r\n    return (\r\n      <Avatar className={className}>\r\n        <AvatarFallback>G</AvatarFallback>\r\n      </Avatar>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className='flex items-center gap-2'>\r\n      <Avatar className={className}>\r\n        <AvatarImage\r\n          src={`https://ui-avatars.com/api/?name=${user.name}&background=random`}\r\n          alt={user.name}\r\n        />\r\n        <AvatarFallback className='rounded-lg'>\r\n          {user.name?.slice(0, 2)?.toUpperCase() || 'U'}\r\n        </AvatarFallback>\r\n      </Avatar>\r\n\r\n      {showInfo && (\r\n        <div className='grid flex-1 text-left text-sm leading-tight'>\r\n          <span className='truncate font-semibold'>{user.name}</span>\r\n          <span className='truncate text-xs'>{user.email}</span>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOO,SAAS,kBAAkB,EAChC,SAAS,EACT,WAAW,KAAK,EACO;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IAEvB,IAAI,CAAC,MAAM;QACT,qBACE,8OAAC,kIAAA,CAAA,SAAM;YAAC,WAAW;sBACjB,cAAA,8OAAC,kIAAA,CAAA,iBAAc;0BAAC;;;;;;;;;;;IAGtB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,kIAAA,CAAA,SAAM;gBAAC,WAAW;;kCACjB,8OAAC,kIAAA,CAAA,cAAW;wBACV,KAAK,CAAC,iCAAiC,EAAE,KAAK,IAAI,CAAC,kBAAkB,CAAC;wBACtE,KAAK,KAAK,IAAI;;;;;;kCAEhB,8OAAC,kIAAA,CAAA,iBAAc;wBAAC,WAAU;kCACvB,KAAK,IAAI,EAAE,MAAM,GAAG,IAAI,iBAAiB;;;;;;;;;;;;YAI7C,0BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;kCAA0B,KAAK,IAAI;;;;;;kCACnD,8OAAC;wBAAK,WAAU;kCAAoB,KAAK,KAAK;;;;;;;;;;;;;;;;;;AAKxD", "debugId": null}}, {"offset": {"line": 2125, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/config/navigation.ts"], "sourcesContent": ["import { NavItem } from '@/types';\r\n\r\n// Super Admin Navigation\r\nexport const superAdminNavItems: NavItem[] = [\r\n  {\r\n    title: 'Dashboard',\r\n    url: '/dashboard/admin',\r\n    icon: 'dashboard',\r\n    isActive: false,\r\n    shortcut: ['d', 'd'],\r\n    items: []\r\n  },\r\n  {\r\n    title: 'Institutions',\r\n    url: '/dashboard/admin/institutions',\r\n    icon: 'building',\r\n    isActive: false,\r\n    shortcut: ['i', 'i'],\r\n    items: [\r\n      {\r\n        title: 'All Institutions',\r\n        url: '/dashboard/admin/institutions',\r\n        icon: 'building'\r\n      },\r\n      {\r\n        title: 'Add Institution',\r\n        url: '/dashboard/admin/institutions/new',\r\n        icon: 'plus'\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    title: 'Users',\r\n    url: '/dashboard/admin/users',\r\n    icon: 'users',\r\n    isActive: false,\r\n    shortcut: ['u', 'u'],\r\n    items: [\r\n      {\r\n        title: 'All Users',\r\n        url: '/dashboard/admin/users',\r\n        icon: 'users'\r\n      },\r\n      {\r\n        title: 'Add User',\r\n        url: '/dashboard/admin/users/new',\r\n        icon: 'userPlus'\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    title: 'Subscriptions',\r\n    url: '/dashboard/admin/subscriptions',\r\n    icon: 'creditCard',\r\n    isActive: false,\r\n    shortcut: ['s', 's'],\r\n    items: []\r\n  },\r\n  {\r\n    title: 'Analytics',\r\n    url: '/dashboard/admin/analytics',\r\n    icon: 'barChart',\r\n    isActive: false,\r\n    shortcut: ['a', 'a'],\r\n    items: []\r\n  },\r\n  {\r\n    title: 'Account',\r\n    url: '#',\r\n    icon: 'billing',\r\n    isActive: false,\r\n    items: [\r\n      {\r\n        title: 'Profile',\r\n        url: '/dashboard/profile',\r\n        icon: 'userPen',\r\n        shortcut: ['p', 'p']\r\n      },\r\n      {\r\n        title: 'Settings',\r\n        url: '/dashboard/settings',\r\n        icon: 'settings'\r\n      }\r\n    ]\r\n  }\r\n];\r\n\r\n// Teacher Navigation\r\nexport const teacherNavItems: NavItem[] = [\r\n  {\r\n    title: 'Dashboard',\r\n    url: '/dashboard/teacher',\r\n    icon: 'dashboard',\r\n    isActive: false,\r\n    shortcut: ['d', 'd'],\r\n    items: []\r\n  },\r\n  /*\r\n  {\r\n    title: 'Classes',\r\n    url: '/dashboard/teacher/classes',\r\n    icon: 'users',\r\n    isActive: false,\r\n    shortcut: ['c', 'c'],\r\n    items: [\r\n      {\r\n        title: 'My Classes',\r\n        url: '/dashboard/teacher/classes',\r\n        icon: 'users'\r\n      },\r\n      {\r\n        title: 'Create Class',\r\n        url: '/dashboard/teacher/classes/new',\r\n        icon: 'plus'\r\n      }\r\n    ]\r\n  },\r\n  */\r\n  {\r\n    title: 'Courses',\r\n    url: '/dashboard/teacher/courses',\r\n    icon: 'bookOpen',\r\n    isActive: false,\r\n    shortcut: ['o', 'o'],\r\n    items: [\r\n      {\r\n        title: 'My Courses',\r\n        url: '/dashboard/teacher/courses',\r\n        icon: 'bookOpen'\r\n      },\r\n      {\r\n        title: 'Create Course',\r\n        url: '/dashboard/teacher/courses/new',\r\n        icon: 'plus'\r\n      }\r\n    ]\r\n  },\r\n\r\n  /*\r\n  {\r\n    title: 'Quizzes',\r\n    url: '/dashboard/teacher/quizzes',\r\n    icon: 'post',\r\n    isActive: false,\r\n    shortcut: ['q', 'q'],\r\n    items: [\r\n      {\r\n        title: 'All Quizzes',\r\n        url: '/dashboard/teacher/quizzes',\r\n        icon: 'post'\r\n      },\r\n      {\r\n        title: 'Create Quiz',\r\n        url: '/dashboard/teacher/quizzes/new',\r\n        icon: 'plus'\r\n      }\r\n    ]\r\n  },\r\n  */\r\n  /*\r\n  {\r\n    title: 'Reports',\r\n    url: '/dashboard/teacher/reports',\r\n    icon: 'barChart',\r\n    isActive: false,\r\n    shortcut: ['r', 'r'],\r\n    items: [\r\n      {\r\n        title: 'Student Progress',\r\n        url: '/dashboard/teacher/reports/progress',\r\n        icon: 'trendingUp'\r\n      },\r\n      {\r\n        title: 'Quiz Results',\r\n        url: '/dashboard/teacher/reports/quizzes',\r\n        icon: 'post'\r\n      },\r\n      {\r\n        title: 'Certificates',\r\n        url: '/dashboard/teacher/reports/certificates',\r\n        icon: 'award'\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    title: 'Account',\r\n    url: '#',\r\n    icon: 'billing',\r\n    isActive: false,\r\n    items: [\r\n      {\r\n        title: 'Profile',\r\n        url: '/dashboard/profile',\r\n        icon: 'userPen',\r\n        shortcut: ['p', 'p']\r\n      },\r\n      {\r\n        title: 'Settings',\r\n        url: '/dashboard/settings',\r\n        icon: 'settings'\r\n      }\r\n    ]\r\n  }\r\n  */\r\n];\r\n\r\n// Student Navigation\r\nexport const studentNavItems: NavItem[] = [\r\n  {\r\n    title: 'Dashboard',\r\n    url: '/dashboard/student',\r\n    icon: 'dashboard',\r\n    isActive: false,\r\n    shortcut: ['d', 'd'],\r\n    items: []\r\n  },\r\n  {\r\n    title: 'My Courses',\r\n    url: '/dashboard/student/courses',\r\n    icon: 'bookOpen',\r\n    isActive: false,\r\n    shortcut: ['c', 'c'],\r\n    items: []\r\n  },\r\n  {\r\n    title: 'Progress',\r\n    url: '/dashboard/student/progress',\r\n    icon: 'trendingUp',\r\n    isActive: false,\r\n    shortcut: ['p', 'p'],\r\n    items: []\r\n  },\r\n  {\r\n    title: 'Certificates',\r\n    url: '/dashboard/student/certificates',\r\n    icon: 'award',\r\n    isActive: false,\r\n    shortcut: ['e', 'e'],\r\n    items: []\r\n  },\r\n  {\r\n    title: 'Account',\r\n    url: '#',\r\n    icon: 'billing',\r\n    isActive: false,\r\n    items: [\r\n      {\r\n        title: 'Profile',\r\n        url: '/dashboard/profile',\r\n        icon: 'userPen',\r\n        shortcut: ['r', 'r']\r\n      },\r\n      {\r\n        title: 'Settings',\r\n        url: '/dashboard/settings',\r\n        icon: 'settings'\r\n      }\r\n    ]\r\n  }\r\n];\r\n\r\n// Function to get navigation items based on user role\r\nexport const getNavigationItems = (role: string): NavItem[] => {\r\n  switch (role) {\r\n    case 'super_admin':\r\n      return superAdminNavItems;\r\n    case 'teacher':\r\n      return teacherNavItems;\r\n    case 'student':\r\n      return studentNavItems;\r\n    default:\r\n      return [];\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;;;AAGO,MAAM,qBAAgC;IAC3C;QACE,OAAO;QACP,KAAK;QACL,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAK;SAAI;QACpB,OAAO,EAAE;IACX;IACA;QACE,OAAO;QACP,KAAK;QACL,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAK;SAAI;QACpB,OAAO;YACL;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM;YACR;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM;YACR;SACD;IACH;IACA;QACE,OAAO;QACP,KAAK;QACL,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAK;SAAI;QACpB,OAAO;YACL;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM;YACR;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM;YACR;SACD;IACH;IACA;QACE,OAAO;QACP,KAAK;QACL,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAK;SAAI;QACpB,OAAO,EAAE;IACX;IACA;QACE,OAAO;QACP,KAAK;QACL,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAK;SAAI;QACpB,OAAO,EAAE;IACX;IACA;QACE,OAAO;QACP,KAAK;QACL,MAAM;QACN,UAAU;QACV,OAAO;YACL;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM;gBACN,UAAU;oBAAC;oBAAK;iBAAI;YACtB;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM;YACR;SACD;IACH;CACD;AAGM,MAAM,kBAA6B;IACxC;QACE,OAAO;QACP,KAAK;QACL,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAK;SAAI;QACpB,OAAO,EAAE;IACX;IACA;;;;;;;;;;;;;;;;;;;;EAoBA,GACA;QACE,OAAO;QACP,KAAK;QACL,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAK;SAAI;QACpB,OAAO;YACL;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM;YACR;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM;YACR;SACD;IACH;CAoED;AAGM,MAAM,kBAA6B;IACxC;QACE,OAAO;QACP,KAAK;QACL,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAK;SAAI;QACpB,OAAO,EAAE;IACX;IACA;QACE,OAAO;QACP,KAAK;QACL,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAK;SAAI;QACpB,OAAO,EAAE;IACX;IACA;QACE,OAAO;QACP,KAAK;QACL,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAK;SAAI;QACpB,OAAO,EAAE;IACX;IACA;QACE,OAAO;QACP,KAAK;QACL,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAK;SAAI;QACpB,OAAO,EAAE;IACX;IACA;QACE,OAAO;QACP,KAAK;QACL,MAAM;QACN,UAAU;QACV,OAAO;YACL;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM;gBACN,UAAU;oBAAC;oBAAK;iBAAI;YACtB;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM;YACR;SACD;IACH;CACD;AAGM,MAAM,qBAAqB,CAAC;IACjC,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO,EAAE;IACb;AACF", "debugId": null}}, {"offset": {"line": 2373, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/hooks/use-media-query.ts"], "sourcesContent": ["import { useEffect, useState } from 'react';\r\n\r\nexport function useMediaQuery() {\r\n  const [isOpen, setIsOpen] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const mediaQuery = window.matchMedia('(max-width: 768px)');\r\n    setIsOpen(mediaQuery.matches);\r\n\r\n    const handler = (e: MediaQueryListEvent) => {\r\n      setIsOpen(e.matches);\r\n    };\r\n\r\n    mediaQuery.addEventListener('change', handler);\r\n    return () => mediaQuery.removeEventListener('change', handler);\r\n  }, []);\r\n\r\n  return { isOpen };\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAEO,SAAS;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa,OAAO,UAAU,CAAC;QACrC,UAAU,WAAW,OAAO;QAE5B,MAAM,UAAU,CAAC;YACf,UAAU,EAAE,OAAO;QACrB;QAEA,WAAW,gBAAgB,CAAC,UAAU;QACtC,OAAO,IAAM,WAAW,mBAAmB,CAAC,UAAU;IACxD,GAAG,EAAE;IAEL,OAAO;QAAE;IAAO;AAClB", "debugId": null}}, {"offset": {"line": 2399, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/icons.tsx"], "sourcesContent": ["import {\r\n  Alert<PERSON>riangle,\r\n  <PERSON>R<PERSON>,\r\n  Award,\r\n  BarChart3,\r\n  BookOpen,\r\n  Book,\r\n  Bot,\r\n  Building2,\r\n  Check,\r\n  ChevronLeft,\r\n  ChevronRight,\r\n  Command,\r\n  CreditCard,\r\n  File,\r\n  FileText,\r\n  HelpCircle,\r\n  Image,\r\n  Laptop,\r\n  LayoutDashboard,\r\n  Loader2,\r\n  LogIn,\r\n  ShoppingBag,\r\n  Moon,\r\n  MoreHorizontal,\r\n  Pizza,\r\n  Plus,\r\n  Settings,\r\n  Sun,\r\n  Trash2,\r\n  TrendingUp,\r\n  User,\r\n  UserCheck,\r\n  UserCircle,\r\n  UserCog,\r\n  UserPlus,\r\n  Users,\r\n  UserX,\r\n  X,\r\n  Square,\r\n  ExternalLink,\r\n  Github,\r\n  Twitter,\r\n  type LucideProps\r\n} from 'lucide-react';\r\n\r\nimport {\r\n  Search01Icon as SearchIcon,\r\n  GraduateMaleIcon as GraduationCapIcon\r\n} from 'hugeicons-react';\r\n\r\nexport type Icon = React.ComponentType<LucideProps>;\r\n\r\nexport const Icons = {\r\n  dashboard: LayoutDashboard,\r\n  logo: Command,\r\n  login: LogIn,\r\n  close: X,\r\n  product: ShoppingBag,\r\n  spinner: Loader2,\r\n  kanban: Square,\r\n  chevronLeft: ChevronLeft,\r\n  chevronRight: ChevronRight,\r\n  trash: Trash2,\r\n  employee: User<PERSON>,\r\n  post: FileText,\r\n  page: File,\r\n  userPen: UserCog,\r\n  user2: UserCircle,\r\n  media: Image,\r\n  settings: Settings,\r\n  billing: CreditCard,\r\n  ellipsis: MoreHorizontal,\r\n  add: Plus,\r\n  warning: AlertTriangle,\r\n  user: User,\r\n  arrowRight: ArrowRight,\r\n  help: HelpCircle,\r\n  pizza: Pizza,\r\n  sun: Sun,\r\n  moon: Moon,\r\n  laptop: Laptop,\r\n  github: Github,\r\n  twitter: Twitter,\r\n  check: Check,\r\n  // LMS specific icons - migrated from Tabler to Lucide equivalents\r\n  building: Building2,\r\n  users: Users,\r\n  userPlus: UserPlus,\r\n  userCheck: UserCheck,\r\n  creditCard: CreditCard,\r\n  barChart: BarChart3,\r\n  bookOpen: BookOpen,\r\n  book: Book,\r\n  bot: Bot,\r\n  trendingUp: TrendingUp,\r\n  award: Award,\r\n  plus: Plus,\r\n  // Additional LMS icons\r\n  school: BookOpen,\r\n  certificate: Award,\r\n  enrollment: UserPlus,\r\n  searchList: SearchIcon,\r\n  graduationCap: GraduationCapIcon\r\n};"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA8CA;AAAA;;;AAOO,MAAM,QAAQ;IACnB,WAAW,4NAAA,CAAA,kBAAe;IAC1B,MAAM,wMAAA,CAAA,UAAO;IACb,OAAO,wMAAA,CAAA,QAAK;IACZ,OAAO,4LAAA,CAAA,IAAC;IACR,SAAS,oNAAA,CAAA,cAAW;IACpB,SAAS,iNAAA,CAAA,UAAO;IAChB,QAAQ,sMAAA,CAAA,SAAM;IACd,aAAa,oNAAA,CAAA,cAAW;IACxB,cAAc,sNAAA,CAAA,eAAY;IAC1B,OAAO,0MAAA,CAAA,SAAM;IACb,UAAU,wMAAA,CAAA,QAAK;IACf,MAAM,8MAAA,CAAA,WAAQ;IACd,MAAM,kMAAA,CAAA,OAAI;IACV,SAAS,4MAAA,CAAA,UAAO;IAChB,OAAO,kNAAA,CAAA,aAAU;IACjB,OAAO,oMAAA,CAAA,QAAK;IACZ,UAAU,0MAAA,CAAA,WAAQ;IAClB,SAAS,kNAAA,CAAA,aAAU;IACnB,UAAU,gNAAA,CAAA,iBAAc;IACxB,KAAK,kMAAA,CAAA,OAAI;IACT,SAAS,wNAAA,CAAA,gBAAa;IACtB,MAAM,kMAAA,CAAA,OAAI;IACV,YAAY,kNAAA,CAAA,aAAU;IACtB,MAAM,kNAAA,CAAA,aAAU;IAChB,OAAO,oMAAA,CAAA,QAAK;IACZ,KAAK,gMAAA,CAAA,MAAG;IACR,MAAM,kMAAA,CAAA,OAAI;IACV,QAAQ,sMAAA,CAAA,SAAM;IACd,QAAQ,sMAAA,CAAA,SAAM;IACd,SAAS,wMAAA,CAAA,UAAO;IAChB,OAAO,oMAAA,CAAA,QAAK;IACZ,kEAAkE;IAClE,UAAU,gNAAA,CAAA,YAAS;IACnB,OAAO,oMAAA,CAAA,QAAK;IACZ,UAAU,8MAAA,CAAA,WAAQ;IAClB,WAAW,gNAAA,CAAA,YAAS;IACpB,YAAY,kNAAA,CAAA,aAAU;IACtB,UAAU,kNAAA,CAAA,YAAS;IACnB,UAAU,8MAAA,CAAA,WAAQ;IAClB,MAAM,kMAAA,CAAA,OAAI;IACV,KAAK,gMAAA,CAAA,MAAG;IACR,YAAY,kNAAA,CAAA,aAAU;IACtB,OAAO,oMAAA,CAAA,QAAK;IACZ,MAAM,kMAAA,CAAA,OAAI;IACV,uBAAuB;IACvB,QAAQ,8MAAA,CAAA,WAAQ;IAChB,aAAa,oMAAA,CAAA,QAAK;IAClB,YAAY,8MAAA,CAAA,WAAQ;IACpB,YAAY,uNAAA,CAAA,eAAU;IACtB,eAAe,+NAAA,CAAA,mBAAiB;AAClC", "debugId": null}}, {"offset": {"line": 2505, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/role-indicator.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { GraduationCap, BookOpen, Building2 } from 'lucide-react';\r\nimport * as React from 'react';\r\nimport { authStorage } from '@/lib/auth';\r\nimport {\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem\r\n} from '@/components/ui/sidebar';\r\n\r\nconst getRoleDisplay = (role: string) => {\r\n  switch (role) {\r\n    case 'teacher':\r\n      return {\r\n        label: 'Teacher',\r\n        icon: GraduationCap,\r\n        bgColor: 'bg-blue-600'\r\n      };\r\n    case 'student':\r\n      return {\r\n        label: 'Student',\r\n        icon: BookOpen,\r\n        bgColor: 'bg-green-600'\r\n      };\r\n    case 'super_admin':\r\n      return {\r\n        label: 'Institution Manager',\r\n        icon: Building2,\r\n        bgColor: 'bg-purple-600'\r\n      };\r\n    default:\r\n      return {\r\n        label: 'User',\r\n        icon: BookOpen,\r\n        bgColor: 'bg-gray-600'\r\n      };\r\n  }\r\n};\r\n\r\nexport function RoleIndicator() {\r\n  const [user, setUser] = React.useState(authStorage.getUser());\r\n\r\n  React.useEffect(() => {\r\n    const currentUser = authStorage.getUser();\r\n    setUser(currentUser);\r\n  }, []);\r\n\r\n  if (!user) {\r\n    return null;\r\n  }\r\n\r\n  const roleInfo = getRoleDisplay(user.role);\r\n  const Icon = roleInfo.icon;\r\n\r\n  return (\r\n    <SidebarMenu>\r\n      <SidebarMenuItem>\r\n        <SidebarMenuButton\r\n          size='lg'\r\n          className='cursor-default hover:bg-transparent'\r\n        >\r\n          <div className={`${roleInfo.bgColor} text-white flex aspect-square size-8 items-center justify-center rounded-lg`}>\r\n            <Icon className='size-4' />\r\n          </div>\r\n          <div className='flex flex-col gap-0.5 leading-none'>\r\n            <span className='font-semibold'>{user.name}</span>\r\n            <span className='text-sm text-muted-foreground'>Account Type: {roleInfo.label}</span>\r\n          </div>\r\n        </SidebarMenuButton>\r\n      </SidebarMenuItem>\r\n    </SidebarMenu>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AACA;AACA;AALA;;;;;;AAWA,MAAM,iBAAiB,CAAC;IACtB,OAAQ;QACN,KAAK;YACH,OAAO;gBACL,OAAO;gBACP,MAAM,wNAAA,CAAA,gBAAa;gBACnB,SAAS;YACX;QACF,KAAK;YACH,OAAO;gBACL,OAAO;gBACP,MAAM,8MAAA,CAAA,WAAQ;gBACd,SAAS;YACX;QACF,KAAK;YACH,OAAO;gBACL,OAAO;gBACP,MAAM,gNAAA,CAAA,YAAS;gBACf,SAAS;YACX;QACF;YACE,OAAO;gBACL,OAAO;gBACP,MAAM,8MAAA,CAAA,WAAQ;gBACd,SAAS;YACX;IACJ;AACF;AAEO,SAAS;IACd,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE,kHAAA,CAAA,cAAW,CAAC,OAAO;IAE1D,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,cAAc,kHAAA,CAAA,cAAW,CAAC,OAAO;QACvC,QAAQ;IACV,GAAG,EAAE;IAEL,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,MAAM,WAAW,eAAe,KAAK,IAAI;IACzC,MAAM,OAAO,SAAS,IAAI;IAE1B,qBACE,8OAAC,mIAAA,CAAA,cAAW;kBACV,cAAA,8OAAC,mIAAA,CAAA,kBAAe;sBACd,cAAA,8OAAC,mIAAA,CAAA,oBAAiB;gBAChB,MAAK;gBACL,WAAU;;kCAEV,8OAAC;wBAAI,WAAW,GAAG,SAAS,OAAO,CAAC,4EAA4E,CAAC;kCAC/G,cAAA,8OAAC;4BAAK,WAAU;;;;;;;;;;;kCAElB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAAiB,KAAK,IAAI;;;;;;0CAC1C,8OAAC;gCAAK,WAAU;;oCAAgC;oCAAe,SAAS,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzF", "debugId": null}}, {"offset": {"line": 2631, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/layout/app-sidebar.tsx"], "sourcesContent": ["'use client';\r\nimport {\r\n  Collapsible,\r\n  CollapsibleContent,\r\n  CollapsibleTrigger\r\n} from '@/components/ui/collapsible';\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger\r\n} from '@/components/ui/dropdown-menu';\r\nimport {\r\n  Sidebar,\r\n  SidebarContent,\r\n  SidebarFooter,\r\n  SidebarGroup,\r\n  SidebarGroupLabel,\r\n  SidebarHeader,\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  SidebarMenuSub,\r\n  SidebarMenuSubButton,\r\n  SidebarMenuSubItem,\r\n  SidebarRail,\r\n  useSidebar\r\n} from '@/components/ui/sidebar';\r\nimport { UserAvatarProfile } from '@/components/user-avatar-profile';\r\nimport { getNavigationItems } from '@/config/navigation';\r\nimport { authStorage } from '@/lib/auth';\r\nimport { getNavItems } from '@/constants/data';\r\nimport { useMediaQuery } from '@/hooks/use-media-query';\r\nimport { ChevronRight, LogOut, Building2 } from 'lucide-react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { usePathname, useRouter } from 'next/navigation';\r\nimport * as React from 'react';\r\nimport { NavItem } from '@/types';\r\nimport { Icons } from '../icons';\r\nimport { RoleIndicator } from '../role-indicator';\r\n\r\nexport const company = {\r\n  name: 'Acme Inc',\r\n  logo: Building2,\r\n  plan: 'Enterprise'\r\n};\r\n\r\nexport default function AppSidebar() {\r\n  const pathname = usePathname();\r\n  const { isOpen } = useMediaQuery();\r\n  const router = useRouter();\r\n  const { state } = useSidebar();\r\n  const [navItems, setNavItems] = React.useState<NavItem[]>([]);\r\n\r\n  React.useEffect(() => {\r\n    // Get user role and set appropriate navigation items\r\n    const user = authStorage.getUser();\r\n    if (user) {\r\n      // Temporary: If user is student, use fallback navigation items\r\n      // This is for demonstration or specific testing purposes.\r\n      // In a production scenario, you would typically use role-based navigation.\r\n      if (user.role === 'student') {\r\n        const fallbackNavItems = getNavItems(pathname); // (Temporary)\r\n        setNavItems(fallbackNavItems); // (Temporary)\r\n      } else {\r\n        const roleNavItems = getNavigationItems(user.role);\r\n        setNavItems(roleNavItems);\r\n      }\r\n    } else {\r\n      // Use fallback navigation items if no user is found\r\n      const fallbackNavItems = getNavItems(pathname);\r\n      setNavItems(fallbackNavItems);\r\n    }\r\n  }, [pathname]);\r\n\r\n  const displayNavItems = navItems.length > 0 ? navItems : getNavItems(pathname);\r\n\r\n  return (\r\n    <Sidebar collapsible='icon'>\r\n      <SidebarHeader className=\"p-4\">\r\n        <div className=\"flex items-center justify-center\">\r\n          <Link href=\"/\">\r\n            <Image\r\n              src=\"/assets/logo-iai.png\"\r\n              alt=\"IAI Logo\"\r\n              width={state === 'collapsed' ? 48 : 160}\r\n              height={state === 'collapsed' ? 48 : 60}\r\n              className=\"object-contain transition-all duration-200 cursor-pointer hover:opacity-80\"\r\n              priority\r\n            />\r\n          </Link>\r\n        </div>\r\n        <div className={`mt-3 ${state === 'collapsed' ? 'hidden' : 'block'}`}>\r\n          <RoleIndicator />\r\n        </div>\r\n      </SidebarHeader>\r\n      <SidebarContent className='overflow-x-hidden'>\r\n        <SidebarGroup>\r\n          <SidebarGroupLabel>Overview</SidebarGroupLabel>\r\n          <SidebarMenu className=\"gap-0\">\r\n            {displayNavItems.map((item: NavItem) => {\r\n              const Icon = item.icon ? Icons[item.icon] : Icons.logo;\r\n              return item?.items && item?.items?.length > 0 ? (\r\n                <Collapsible\r\n                  key={item.title}\r\n                  asChild\r\n                  defaultOpen={item.isActive}\r\n                  className='group/collapsible'\r\n                >\r\n                  <SidebarMenuItem>\r\n                    <CollapsibleTrigger asChild>\r\n                      <SidebarMenuButton\r\n                        tooltip={item.title}\r\n                        isActive={item.items?.some(\r\n                          (subItem) => pathname === subItem.url\r\n                        )}\r\n                        className=\"py-3 px-4 text-base font-medium h-auto\"\r\n                      >\r\n                        {item.icon && <Icon />}\r\n                        <span>{item.title}</span>\r\n                        <ChevronRight className='ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90' />\r\n                      </SidebarMenuButton>\r\n                    </CollapsibleTrigger>\r\n                    <CollapsibleContent>\r\n                      <SidebarMenuSub>\r\n                        {item.items?.map((subItem: NavItem) => (\r\n                          <SidebarMenuSubItem key={subItem.title}>\r\n                            <SidebarMenuSubButton\r\n                              asChild\r\n                              isActive={pathname === subItem.url}\r\n                              className=\"py-2 px-6 text-sm font-medium\"\r\n                            >\r\n                              <Link href={subItem.url}>\r\n                                <span>{subItem.title}</span>\r\n                              </Link>\r\n                            </SidebarMenuSubButton>\r\n                          </SidebarMenuSubItem>\r\n                        ))}\r\n                      </SidebarMenuSub>\r\n                    </CollapsibleContent>\r\n                  </SidebarMenuItem>\r\n                </Collapsible>\r\n              ) : (\r\n                <SidebarMenuItem key={item.title}>\r\n                  <SidebarMenuButton\r\n                    asChild\r\n                    tooltip={item.title}\r\n                    isActive={pathname === item.url}\r\n                    className=\"py-3 px-4 text-base font-medium h-auto\"\r\n                  >\r\n                    <Link href={item.url}>\r\n                      <Icon />\r\n                      <span>{item.title}</span>\r\n                    </Link>\r\n                  </SidebarMenuButton>\r\n                </SidebarMenuItem>\r\n              );\r\n            })}\r\n          </SidebarMenu>\r\n        </SidebarGroup>\r\n      </SidebarContent>\r\n      <SidebarFooter>\r\n        <SidebarMenu>\r\n          <SidebarMenuItem>\r\n            <DropdownMenu>\r\n              <DropdownMenuTrigger asChild>\r\n                <SidebarMenuButton\r\n                  size='lg'\r\n                  className='data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground py-3 px-4 text-base font-medium h-auto'\r\n                >\r\n                  <UserAvatarProfile className='h-8 w-8 rounded-lg' showInfo />\r\n                  <Icons.chevronRight className='ml-auto size-4' />\r\n                </SidebarMenuButton>\r\n              </DropdownMenuTrigger>\r\n              <DropdownMenuContent\r\n                className='w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg'\r\n                side='bottom'\r\n                align='end'\r\n                sideOffset={4}\r\n              >\r\n                <DropdownMenuLabel className='p-0 font-normal'>\r\n                  <div className='px-1 py-1.5'>\r\n                    <UserAvatarProfile\r\n                      className='h-8 w-8 rounded-lg'\r\n                      showInfo\r\n                    />\r\n                  </div>\r\n                </DropdownMenuLabel>\r\n                <DropdownMenuSeparator />\r\n\r\n                <DropdownMenuGroup>\r\n                  <DropdownMenuItem\r\n                    onClick={() => router.push('/dashboard/profile')}\r\n                  >\r\n                    <Icons.user className='mr-2 h-4 w-4' />\r\n                    Profile\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem>\r\n                    <Icons.billing className='mr-2 h-4 w-4' />\r\n                    Billing\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem>\r\n                    <Icons.help className='mr-2 h-4 w-4' />\r\n                    Notifications\r\n                  </DropdownMenuItem>\r\n                </DropdownMenuGroup>\r\n                <DropdownMenuSeparator />\r\n                <DropdownMenuItem\r\n                  onClick={() => {\r\n                    authStorage.removeUser();\r\n                    window.location.href = '/auth/sign-in';\r\n                  }}\r\n                >\r\n                  <LogOut className='mr-2 h-4 w-4' />\r\n                  <span>Logout</span>\r\n                </DropdownMenuItem>\r\n              </DropdownMenuContent>\r\n            </DropdownMenu>\r\n          </SidebarMenuItem>\r\n        </SidebarMenu>\r\n      </SidebarFooter>\r\n      <SidebarRail />\r\n    </Sidebar>\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AACA;AAKA;AASA;AAgBA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAEA;AACA;AA3CA;;;;;;;;;;;;;;;;;AA6CO,MAAM,UAAU;IACrB,MAAM;IACN,MAAM,gNAAA,CAAA,YAAS;IACf,MAAM;AACR;AAEe,SAAS;IACtB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,gBAAa,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD;IAC3B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAa,EAAE;IAE5D,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,qDAAqD;QACrD,MAAM,OAAO,kHAAA,CAAA,cAAW,CAAC,OAAO;QAChC,IAAI,MAAM;YACR,+DAA+D;YAC/D,0DAA0D;YAC1D,2EAA2E;YAC3E,IAAI,KAAK,IAAI,KAAK,WAAW;gBAC3B,MAAM,mBAAmB,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD,EAAE,WAAW,cAAc;gBAC9D,YAAY,mBAAmB,cAAc;YAC/C,OAAO;gBACL,MAAM,eAAe,CAAA,GAAA,2HAAA,CAAA,qBAAkB,AAAD,EAAE,KAAK,IAAI;gBACjD,YAAY;YACd;QACF,OAAO;YACL,oDAAoD;YACpD,MAAM,mBAAmB,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD,EAAE;YACrC,YAAY;QACd;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,kBAAkB,SAAS,MAAM,GAAG,IAAI,WAAW,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD,EAAE;IAErE,qBACE,8OAAC,mIAAA,CAAA,UAAO;QAAC,aAAY;;0BACnB,8OAAC,mIAAA,CAAA,gBAAa;gBAAC,WAAU;;kCACvB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO,UAAU,cAAc,KAAK;gCACpC,QAAQ,UAAU,cAAc,KAAK;gCACrC,WAAU;gCACV,QAAQ;;;;;;;;;;;;;;;;kCAId,8OAAC;wBAAI,WAAW,CAAC,KAAK,EAAE,UAAU,cAAc,WAAW,SAAS;kCAClE,cAAA,8OAAC,uIAAA,CAAA,gBAAa;;;;;;;;;;;;;;;;0BAGlB,8OAAC,mIAAA,CAAA,iBAAc;gBAAC,WAAU;0BACxB,cAAA,8OAAC,mIAAA,CAAA,eAAY;;sCACX,8OAAC,mIAAA,CAAA,oBAAiB;sCAAC;;;;;;sCACnB,8OAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACpB,gBAAgB,GAAG,CAAC,CAAC;gCACpB,MAAM,OAAO,KAAK,IAAI,GAAG,2HAAA,CAAA,QAAK,CAAC,KAAK,IAAI,CAAC,GAAG,2HAAA,CAAA,QAAK,CAAC,IAAI;gCACtD,OAAO,MAAM,SAAS,MAAM,OAAO,SAAS,kBAC1C,8OAAC,uIAAA,CAAA,cAAW;oCAEV,OAAO;oCACP,aAAa,KAAK,QAAQ;oCAC1B,WAAU;8CAEV,cAAA,8OAAC,mIAAA,CAAA,kBAAe;;0DACd,8OAAC,uIAAA,CAAA,qBAAkB;gDAAC,OAAO;0DACzB,cAAA,8OAAC,mIAAA,CAAA,oBAAiB;oDAChB,SAAS,KAAK,KAAK;oDACnB,UAAU,KAAK,KAAK,EAAE,KACpB,CAAC,UAAY,aAAa,QAAQ,GAAG;oDAEvC,WAAU;;wDAET,KAAK,IAAI,kBAAI,8OAAC;;;;;sEACf,8OAAC;sEAAM,KAAK,KAAK;;;;;;sEACjB,8OAAC,sNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAG5B,8OAAC,uIAAA,CAAA,qBAAkB;0DACjB,cAAA,8OAAC,mIAAA,CAAA,iBAAc;8DACZ,KAAK,KAAK,EAAE,IAAI,CAAC,wBAChB,8OAAC,mIAAA,CAAA,qBAAkB;sEACjB,cAAA,8OAAC,mIAAA,CAAA,uBAAoB;gEACnB,OAAO;gEACP,UAAU,aAAa,QAAQ,GAAG;gEAClC,WAAU;0EAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;oEAAC,MAAM,QAAQ,GAAG;8EACrB,cAAA,8OAAC;kFAAM,QAAQ,KAAK;;;;;;;;;;;;;;;;2DAPD,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;mCAtBzC,KAAK,KAAK;;;;yDAuCjB,8OAAC,mIAAA,CAAA,kBAAe;8CACd,cAAA,8OAAC,mIAAA,CAAA,oBAAiB;wCAChB,OAAO;wCACP,SAAS,KAAK,KAAK;wCACnB,UAAU,aAAa,KAAK,GAAG;wCAC/B,WAAU;kDAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAM,KAAK,GAAG;;8DAClB,8OAAC;;;;;8DACD,8OAAC;8DAAM,KAAK,KAAK;;;;;;;;;;;;;;;;;mCATD,KAAK,KAAK;;;;;4BAcpC;;;;;;;;;;;;;;;;;0BAIN,8OAAC,mIAAA,CAAA,gBAAa;0BACZ,cAAA,8OAAC,mIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC,mIAAA,CAAA,kBAAe;kCACd,cAAA,8OAAC,4IAAA,CAAA,eAAY;;8CACX,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,8OAAC,mIAAA,CAAA,oBAAiB;wCAChB,MAAK;wCACL,WAAU;;0DAEV,8OAAC,+IAAA,CAAA,oBAAiB;gDAAC,WAAU;gDAAqB,QAAQ;;;;;;0DAC1D,8OAAC,2HAAA,CAAA,QAAK,CAAC,YAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAGlC,8OAAC,4IAAA,CAAA,sBAAmB;oCAClB,WAAU;oCACV,MAAK;oCACL,OAAM;oCACN,YAAY;;sDAEZ,8OAAC,4IAAA,CAAA,oBAAiB;4CAAC,WAAU;sDAC3B,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,+IAAA,CAAA,oBAAiB;oDAChB,WAAU;oDACV,QAAQ;;;;;;;;;;;;;;;;sDAId,8OAAC,4IAAA,CAAA,wBAAqB;;;;;sDAEtB,8OAAC,4IAAA,CAAA,oBAAiB;;8DAChB,8OAAC,4IAAA,CAAA,mBAAgB;oDACf,SAAS,IAAM,OAAO,IAAI,CAAC;;sEAE3B,8OAAC,2HAAA,CAAA,QAAK,CAAC,IAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGzC,8OAAC,4IAAA,CAAA,mBAAgB;;sEACf,8OAAC,2HAAA,CAAA,QAAK,CAAC,OAAO;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAG5C,8OAAC,4IAAA,CAAA,mBAAgB;;sEACf,8OAAC,2HAAA,CAAA,QAAK,CAAC,IAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;sDAI3C,8OAAC,4IAAA,CAAA,wBAAqB;;;;;sDACtB,8OAAC,4IAAA,CAAA,mBAAgB;4CACf,SAAS;gDACP,kHAAA,CAAA,cAAW,CAAC,UAAU;gDACtB,OAAO,QAAQ,CAAC,IAAI,GAAG;4CACzB;;8DAEA,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOlB,8OAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;AAGlB", "debugId": null}}, {"offset": {"line": 3120, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/layout/user-nav.tsx"], "sourcesContent": ["'use client';\r\nimport { But<PERSON> } from '@/components/ui/button';\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger\r\n} from '@/components/ui/dropdown-menu';\r\nimport { UserAvatarProfile } from '@/components/user-avatar-profile';\r\nimport { useRouter } from 'next/navigation';\r\nimport { useAuth } from '@/hooks/use-auth';\r\n\r\nexport function UserNav() {\r\n  const router = useRouter();\r\n  const { user, signOut } = useAuth();\r\n\r\n  if (!user) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <DropdownMenu>\r\n      <DropdownMenuTrigger asChild>\r\n        <Button variant='ghost' className='relative h-8 w-8 rounded-full cursor-pointer'>\r\n          <UserAvatarProfile />\r\n        </Button>\r\n      </DropdownMenuTrigger>\r\n      <DropdownMenuContent\r\n        className='w-56'\r\n        align='end'\r\n        sideOffset={10}\r\n        forceMount\r\n      >\r\n        <DropdownMenuLabel className='font-normal'>\r\n          <div className='flex flex-col space-y-1'>\r\n            <p className='text-sm leading-none font-medium'>{user.name}</p>\r\n            <p className='text-muted-foreground text-xs leading-none'>\r\n              {user.email}\r\n            </p>\r\n          </div>\r\n        </DropdownMenuLabel>\r\n        <DropdownMenuSeparator />\r\n        <DropdownMenuGroup>\r\n          <DropdownMenuItem onClick={() => router.push('/dashboard/profile')}>\r\n            Profile\r\n          </DropdownMenuItem>\r\n          <DropdownMenuItem>Billing</DropdownMenuItem>\r\n        </DropdownMenuGroup>\r\n        <DropdownMenuSeparator />\r\n        <DropdownMenuItem onClick={signOut}>Sign Out</DropdownMenuItem>\r\n      </DropdownMenuContent>\r\n    </DropdownMenu>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AASA;AACA;AACA;AAbA;;;;;;;AAeO,SAAS;IACd,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IAEhC,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,8OAAC,4IAAA,CAAA,eAAY;;0BACX,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAQ,WAAU;8BAChC,cAAA,8OAAC,+IAAA,CAAA,oBAAiB;;;;;;;;;;;;;;;0BAGtB,8OAAC,4IAAA,CAAA,sBAAmB;gBAClB,WAAU;gBACV,OAAM;gBACN,YAAY;gBACZ,UAAU;;kCAEV,8OAAC,4IAAA,CAAA,oBAAiB;wBAAC,WAAU;kCAC3B,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAoC,KAAK,IAAI;;;;;;8CAC1D,8OAAC;oCAAE,WAAU;8CACV,KAAK,KAAK;;;;;;;;;;;;;;;;;kCAIjB,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kCACtB,8OAAC,4IAAA,CAAA,oBAAiB;;0CAChB,8OAAC,4IAAA,CAAA,mBAAgB;gCAAC,SAAS,IAAM,OAAO,IAAI,CAAC;0CAAuB;;;;;;0CAGpE,8OAAC,4IAAA,CAAA,mBAAgB;0CAAC;;;;;;;;;;;;kCAEpB,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kCACtB,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,SAAS;kCAAS;;;;;;;;;;;;;;;;;;AAI5C", "debugId": null}}, {"offset": {"line": 3262, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/layout/header.tsx"], "sourcesContent": ["'use client';\r\nimport React from 'react';\r\nimport { SidebarTrigger } from '../ui/sidebar';\r\nimport { Separator } from '../ui/separator';\r\nimport SearchInput from '../search-input';\r\nimport { UserNav } from './user-nav';\r\nimport { Button } from '../ui/button';\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger\r\n} from '../ui/dropdown-menu';\r\nimport { User, Settings, HelpCircle, FileText } from 'lucide-react';\r\nimport { useRouter } from 'next/navigation';\r\nimport CtaGithub from './cta-github';\r\n\r\nexport default function Header() {\r\n  const router = useRouter();\r\n\r\n  return (\r\n    <header className='flex h-16 shrink-0 items-center justify-between gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12'>\r\n      <div className='flex items-center gap-2 px-4'>\r\n        <SidebarTrigger className='-ml-1' />\r\n      </div>\r\n\r\n      <div className='flex items-center gap-2 px-4'>\r\n        <DropdownMenu>\r\n          <DropdownMenuTrigger asChild>\r\n            <Button\r\n              variant='ghost'\r\n              size='icon'\r\n              className='relative h-8 w-8 rounded-full cursor-pointer'\r\n            >\r\n              <User className='h-4 w-4' />\r\n              <span className='sr-only'>Profile Menu</span>\r\n            </Button>\r\n          </DropdownMenuTrigger>\r\n          <DropdownMenuContent\r\n            className='w-56'\r\n            align='end'\r\n            sideOffset={10}\r\n          >\r\n            <DropdownMenuLabel>Profile & Settings</DropdownMenuLabel>\r\n            <DropdownMenuSeparator />\r\n            <DropdownMenuGroup>\r\n              <DropdownMenuItem onClick={() => router.push('/dashboard/profile')}>\r\n                <User className='mr-2 h-4 w-4' />\r\n                View Profile\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem onClick={() => router.push('/dashboard/settings')}>\r\n                <Settings className='mr-2 h-4 w-4' />\r\n                Settings\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem onClick={() => router.push('/dashboard/help')}>\r\n                <HelpCircle className='mr-2 h-4 w-4' />\r\n                Help & Support\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem onClick={() => router.push('/dashboard/documentation')}>\r\n                <FileText className='mr-2 h-4 w-4' />\r\n                Documentation\r\n              </DropdownMenuItem>\r\n            </DropdownMenuGroup>\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n        <UserNav />\r\n      </div>\r\n    </header>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AAGA;AACA;AACA;AASA;AAAA;AAAA;AAAA;AACA;AAjBA;;;;;;;;AAoBe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,mIAAA,CAAA,iBAAc;oBAAC,WAAU;;;;;;;;;;;0BAG5B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,4IAAA,CAAA,eAAY;;0CACX,8OAAC,4IAAA,CAAA,sBAAmB;gCAAC,OAAO;0CAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;;sDAEV,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;0CAG9B,8OAAC,4IAAA,CAAA,sBAAmB;gCAClB,WAAU;gCACV,OAAM;gCACN,YAAY;;kDAEZ,8OAAC,4IAAA,CAAA,oBAAiB;kDAAC;;;;;;kDACnB,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kDACtB,8OAAC,4IAAA,CAAA,oBAAiB;;0DAChB,8OAAC,4IAAA,CAAA,mBAAgB;gDAAC,SAAS,IAAM,OAAO,IAAI,CAAC;;kEAC3C,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGnC,8OAAC,4IAAA,CAAA,mBAAgB;gDAAC,SAAS,IAAM,OAAO,IAAI,CAAC;;kEAC3C,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGvC,8OAAC,4IAAA,CAAA,mBAAgB;gDAAC,SAAS,IAAM,OAAO,IAAI,CAAC;;kEAC3C,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGzC,8OAAC,4IAAA,CAAA,mBAAgB;gDAAC,SAAS,IAAM,OAAO,IAAI,CAAC;;kEAC3C,8OAAC,8MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;kCAM7C,8OAAC,2IAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;AAIhB", "debugId": null}}, {"offset": {"line": 3469, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/constants/shared-course-data.ts"], "sourcesContent": ["import { Course } from '@/types/lms';\r\n\r\n// Shared Architecture course data for both enroll and modules pages\r\nexport const architectureCourse: Course = {\r\n  id: 'arch-cert-001',\r\n  name: 'Sertifikasi Arsitek Profesional IAI',\r\n  code: 'IAI-CERT-001',\r\n  description:\r\n    'Program sertifikasi profesional untuk arsitek yang ingin memperoleh sertifikat Ikatan Arsitek Indonesia (IAI) dan meningkatkan kompetensi di bidang perencanaan dan pengawasan bangunan',\r\n  instructor: 'Ar. <PERSON>, IAI, AA',\r\n  startDate: '2024-08-01',\r\n  endDate: '2024-12-31',\r\n  enrollmentType: 'both',\r\n  enrollmentCode: 'IAI-CERT-2024',\r\n  minPassingScore: 80,\r\n  totalProgress: 0,\r\n  status: 'not-started',\r\n  modules: [\r\n    {\r\n      id: 'mod1',\r\n      title: 'Modul 1: Dasar-Dasar Arsitektur',\r\n      description: 'Memahami prinsip-prinsip dasar arsitektur dan sejarahnya',\r\n      order: 1,\r\n      isUnlocked: true,\r\n      completionPercentage: 0,\r\n      chapters: [\r\n        {\r\n          id: 'ch1',\r\n          title: 'Bab 1: Sejarah Arsitektur',\r\n          order: 1,\r\n          isUnlocked: true,\r\n          completionPercentage: 0,\r\n          contents: [\r\n            {\r\n              id: 'content1',\r\n              title: 'Prinsip-Prinsip Arsitektur Kuno',\r\n              type: 'text',\r\n              content: `# Ancient Architecture Principles\r\n\r\n## Introduction\r\n\r\nArchitecture has been a **fundamental aspect** of human civilization since ancient times. From the pyramids of Egypt to the temples of Greece, architectural principles have evolved to reflect cultural values, technological advances, and environmental considerations.\r\n\r\n## Key Civilizations and Their Contributions\r\n\r\n### 1. Egyptian Architecture\r\n- **Pyramids**: Monumental structures demonstrating advanced engineering\r\n- **Materials**: Limestone, granite, and sandstone\r\n- **Key Features**:\r\n  - Massive scale\r\n  - Precise geometric proportions\r\n  - Durability across millennia\r\n\r\n### 2. Greek Architecture\r\nThe Greeks developed the classical orders that influence architecture to this day:\r\n\r\n1. **Doric Order** - Simple and sturdy\r\n2. **Ionic Order** - Elegant with scroll-like capitals\r\n3. **Corinthian Order** - Ornate with acanthus leaf decorations\r\n\r\n### 3. Roman Architecture\r\nRomans revolutionized construction with:\r\n- **Concrete technology**\r\n- **Arches and vaults**\r\n- **Aqueducts and infrastructure**\r\n\r\n## Fundamental Principles\r\n\r\n> \"Architecture is the learned game, correct and magnificent, of forms assembled in the light.\" - Le Corbusier\r\n\r\n### Proportion and Scale\r\n- **Golden Ratio**: φ ≈ 1.618\r\n- **Human scale**: Buildings designed for human comfort\r\n- **Visual balance**: Creating harmony through proportional relationships\r\n\r\n### Materials and Structure\r\n\\`\\`\\`\r\nLoad-bearing elements:\r\n├── Walls\r\n├── Columns\r\n├── Beams\r\n└── Foundations\r\n\\`\\`\\`\r\n\r\n## Learning Objectives\r\nBy the end of this section, you will understand:\r\n- [ ] The evolution of architectural styles\r\n- [ ] Key principles of proportion and scale\r\n- [ ] Material properties and their applications\r\n- [ ] Cultural influences on architectural design\r\n\r\n---\r\n\r\n*Next: We'll explore how these ancient principles influenced Renaissance and modern architecture.*`,\r\n              isCompleted: false,\r\n              createdAt: '2024-01-15'\r\n            },\r\n            {\r\n              id: 'content2',\r\n              title: 'Tata Orde Klasik dalam Arsitektur',\r\n              type: 'video',\r\n              content: 'https://example.com/classical-orders-video',\r\n              duration: 25,\r\n              isCompleted: false,\r\n              createdAt: '2024-01-16'\r\n            },\r\n            {\r\n              id: 'content3',\r\n              title: 'Dasar-Dasar Gambar Arsitektur',\r\n              type: 'pdf',\r\n              content: 'https://example.com/drawing-fundamentals.pdf',\r\n              isCompleted: false,\r\n              createdAt: '2024-01-17'\r\n            }\r\n          ],\r\n          quiz: {\r\n            id: 'quiz-ch1',\r\n            title: 'Kuis Bab 1: Sejarah Arsitektur',\r\n            type: 'chapter',\r\n            questions: [\r\n              {\r\n                id: 'q1',\r\n                question:\r\n                  'Which architectural order is characterized by simple, sturdy columns?',\r\n                type: 'multiple-choice',\r\n                options: ['Doric', 'Ionic', 'Corinthian', 'Composite'],\r\n                correctAnswer: 0,\r\n                explanation:\r\n                  'The Doric order is the simplest and most robust of the classical orders.'\r\n              }\r\n            ],\r\n            minimumScore: 70,\r\n            timeLimit: 15,\r\n            attempts: 0,\r\n            maxAttempts: 3,\r\n            isPassed: false\r\n          }\r\n        },\r\n        {\r\n          id: 'ch2',\r\n          title: 'Bab 2: Prinsip-Prinsip Desain',\r\n          order: 2,\r\n          isUnlocked: false,\r\n          completionPercentage: 0,\r\n          contents: [\r\n            {\r\n              id: 'content4',\r\n              title: 'Proporsi dan Skala',\r\n              type: 'text',\r\n              content: `# Proportion and Scale in Architecture\r\n\r\n## What is Proportion?\r\n\r\n**Proportion** refers to the relationship between different elements in architectural design. It's the foundation of creating visually pleasing and harmonious structures.\r\n\r\n## The Golden Ratio (φ = 1.618)\r\n\r\nThe **Golden Ratio** has been used in architecture for millennia:\r\n\r\n| Building | Golden Ratio Application |\r\n|----------|-------------------------|\r\n| Parthenon | Façade proportions |\r\n| Notre-Dame | Rose window design |\r\n| Villa Savoye | Room dimensions |\r\n\r\n### Mathematical Expression\r\n\\`\\`\\`\r\nφ = (1 + √5) / 2 ≈ 1.618033988...\r\n\\`\\`\\`\r\n\r\n## Types of Scale\r\n\r\n### 1. Human Scale\r\nBuildings should relate to human dimensions:\r\n- **Door heights**: ~2.1m (7 feet)\r\n- **Ceiling heights**: 2.4-3.0m (8-10 feet)\r\n- **Stair risers**: 15-18cm (6-7 inches)\r\n\r\n### 2. Architectural Scale\r\nHow building elements relate to each other:\r\n- Window to wall ratios\r\n- Column spacing\r\n- Room proportions\r\n\r\n### 3. Urban Scale\r\nHow buildings relate to their context:\r\n- Street width to building height\r\n- Public space dimensions\r\n- Neighborhood character\r\n\r\n## Classical Proportional Systems\r\n\r\n### Vitruvian Proportions\r\nBased on the human body:\r\n> \"The human body is a model of proportion because with arms or legs extended it fits into those 'perfect' geometrical forms, the square and the circle.\"\r\n\r\n### Modular Systems\r\n- **Le Corbusier's Modulor**: Based on human measurements\r\n- **Japanese Ken**: Traditional proportional system\r\n- **Classical Orders**: Column to entablature ratios\r\n\r\n## Design Exercise\r\n\r\nTry this proportional analysis:\r\n1. Find a building you admire\r\n2. Measure key dimensions\r\n3. Calculate the ratios\r\n4. Compare to golden ratio or other systems\r\n\r\n## Key Takeaways\r\n\r\n✅ **Good proportion creates visual harmony**  \r\n✅ **Human scale ensures comfort**  \r\n✅ **Mathematical ratios provide guidelines**  \r\n✅ **Context matters in scale decisions**  \r\n\r\n---\r\n\r\n*Remember: Proportion is felt, not calculated. Trust your eye, but understand the principles.*`,\r\n              isCompleted: false,\r\n              createdAt: '2024-01-18'\r\n            },\r\n            {\r\n              id: 'content5',\r\n              title: 'Rasio Emas dalam Arsitektur',\r\n              type: 'zoom-recording',\r\n              content: 'https://zoom.us/rec/golden-ratio-lecture',\r\n              duration: 45,\r\n              isCompleted: false,\r\n              createdAt: '2024-01-19'\r\n            }\r\n          ],\r\n          quiz: {\r\n            id: 'quiz-ch2',\r\n            title: 'Kuis Bab 2: Prinsip-Prinsip Desain',\r\n            type: 'chapter',\r\n            questions: [\r\n              {\r\n                id: 'q2',\r\n                question: 'What is the Golden Ratio approximately equal to?',\r\n                type: 'multiple-choice',\r\n                options: ['1.414', '1.618', '1.732', '2.000'],\r\n                correctAnswer: 1,\r\n                explanation:\r\n                  'The Golden Ratio is approximately 1.618 and is commonly used in architectural proportions.'\r\n              },\r\n              {\r\n                id: 'q3',\r\n                question:\r\n                  'Proportion in architecture refers to the relationship between different elements.',\r\n                type: 'true-false',\r\n                correctAnswer: 'true',\r\n                explanation:\r\n                  'Proportion is indeed about the harmonious relationship between different architectural elements.'\r\n              }\r\n            ],\r\n            minimumScore: 70,\r\n            attempts: 0,\r\n            maxAttempts: 3,\r\n            isPassed: false\r\n          }\r\n        }\r\n      ],\r\n      moduleQuiz: {\r\n        id: 'quiz-mod1',\r\n        title: 'Kuis Akhir Modul 1',\r\n        type: 'module',\r\n        questions: [\r\n          {\r\n            id: 'qm1',\r\n            question:\r\n              'Which of the following are classical architectural orders? (Select all that apply)',\r\n            type: 'multiple-choice',\r\n            options: ['Doric', 'Ionic', 'Corinthian', 'All of the above'],\r\n            correctAnswer: 3,\r\n            explanation:\r\n              'Doric, Ionic, and Corinthian are the three main classical architectural orders.'\r\n          }\r\n        ],\r\n        minimumScore: 75,\r\n        attempts: 0,\r\n        maxAttempts: 2,\r\n        isPassed: false\r\n      }\r\n    },\r\n    {\r\n      id: 'mod2',\r\n      title: 'Modul 2: Arsitektur Kontemporer',\r\n      description: 'Gerakan arsitektur modern dan desain berkelanjutan',\r\n      order: 2,\r\n      isUnlocked: false,\r\n      completionPercentage: 0,\r\n      chapters: [\r\n        {\r\n          id: 'ch3',\r\n          title: 'Bab 3: Gerakan Modern',\r\n          order: 1,\r\n          isUnlocked: false,\r\n          completionPercentage: 0,\r\n          contents: [\r\n            {\r\n              id: 'content6',\r\n              title: 'Gerakan Bauhaus',\r\n              type: 'text',\r\n              content: `# The Bauhaus Movement: Form Follows Function\r\n\r\n## Overview\r\n\r\nThe **Bauhaus** (1919-1933) was a revolutionary design school that fundamentally changed architecture, art, and design. Founded by Walter Gropius in Weimar, Germany, it promoted the integration of art, craft, and industrial technology.\r\n\r\n## Core Principles\r\n\r\n### 1. **Form Follows Function**\r\n- Design should be determined by purpose\r\n- No unnecessary ornamentation\r\n- Efficiency in both form and construction\r\n\r\n### 2. **Gesamtkunstwerk** (Total Work of Art)\r\n- Integration of all arts and crafts\r\n- Architecture as the master art\r\n- Unified design philosophy\r\n\r\n### 3. **Machine Aesthetic**\r\n- Embrace industrial production methods\r\n- Clean lines and geometric forms\r\n- Mass production capabilities\r\n\r\n## Key Figures\r\n\r\n| Name | Role | Contribution |\r\n|------|------|-------------|\r\n| **Walter Gropius** | Founder & Director | Established core philosophy |\r\n| **Ludwig Mies van der Rohe** | Director (1930-33) | \"Less is more\" principle |\r\n| **Marcel Breuer** | Faculty | Furniture design innovation |\r\n| **László Moholy-Nagy** | Faculty | Photography and typography |\r\n\r\n## Architectural Characteristics\r\n\r\n### Visual Elements\r\n- ✅ **Clean geometric forms**\r\n- ✅ **Flat roofs**\r\n- ✅ **Large windows**\r\n- ✅ **Open floor plans**\r\n- ❌ Historical references\r\n- ❌ Decorative elements\r\n\r\n### Materials\r\n\\`\\`\\`\r\nPrimary materials:\r\n├── Steel frame construction\r\n├── Glass curtain walls\r\n├── Reinforced concrete\r\n└── Industrial materials\r\n\\`\\`\\`\r\n\r\n## Famous Bauhaus Buildings\r\n\r\n### 1. Fagus Factory (1911-1913)\r\n- **Architect**: Walter Gropius\r\n- **Innovation**: Glass curtain wall\r\n- **Significance**: Proto-Bauhaus design\r\n\r\n### 2. Bauhaus Dessau (1925-1926)\r\n- **Architect**: Walter Gropius\r\n- **Features**: Modular design, industrial materials\r\n- **Legacy**: Became the school's permanent home\r\n\r\n### 3. Barcelona Pavilion (1929)\r\n- **Architect**: Mies van der Rohe\r\n- **Concept**: Flowing space, minimal structure\r\n- **Impact**: Redefined modern architecture\r\n\r\n## Impact on Modern Architecture\r\n\r\n> \"The ultimate goal of all visual arts is the complete building!\" - Walter Gropius\r\n\r\n### International Style\r\nThe Bauhaus influenced what became known as the **International Style**:\r\n- Emphasis on volume over mass\r\n- Regularity rather than symmetry\r\n- No applied ornamentation\r\n\r\n### Urban Planning\r\n- **Functional zoning**\r\n- **Housing for the masses**\r\n- **Integration with landscape**\r\n\r\n## Legacy and Criticism\r\n\r\n### Positive Impact\r\n- **Democratized design**: Good design for everyone\r\n- **Functional solutions**: Buildings that work efficiently  \r\n- **Modern materials**: Pioneered new construction techniques\r\n\r\n### Criticisms\r\n- **Cultural homogenization**: Loss of regional character\r\n- **Human scale**: Sometimes overlooked human needs\r\n- **Context insensitivity**: Universal solutions vs. local conditions\r\n\r\n## Contemporary Relevance\r\n\r\nThe Bauhaus principles remain relevant today:\r\n- **Sustainable design**: Efficiency and functionality\r\n- **Digital integration**: Technology serving human needs\r\n- **Social responsibility**: Design for collective benefit\r\n\r\n---\r\n\r\n*\"The Bauhaus is not a style but a principle - the principle that the artistic process and the industrial process are one.\"* - Walter Gropius`,\r\n              isCompleted: false,\r\n              createdAt: '2024-01-20'\r\n            }\r\n          ],\r\n          quiz: {\r\n            id: 'quiz-ch3',\r\n            title: 'Kuis Bab 3',\r\n            type: 'chapter',\r\n            questions: [\r\n              {\r\n                id: 'q4',\r\n                question:\r\n                  'The Bauhaus movement emphasized functionality over ornamentation.',\r\n                type: 'true-false',\r\n                correctAnswer: 'true',\r\n                explanation:\r\n                  'The Bauhaus movement was known for its emphasis on functionality and minimalism.'\r\n              }\r\n            ],\r\n            minimumScore: 70,\r\n            attempts: 0,\r\n            maxAttempts: 3,\r\n            isPassed: false\r\n          }\r\n        }\r\n      ],\r\n      moduleQuiz: {\r\n        id: 'quiz-mod2',\r\n        title: 'Kuis Akhir Modul 2',\r\n        type: 'module',\r\n        questions: [\r\n          {\r\n            id: 'qm2',\r\n            question: 'What are the key principles of modern architecture?',\r\n            type: 'essay',\r\n            correctAnswer:\r\n              'Sample answer about functionality, simplicity, and form follows function',\r\n            explanation:\r\n              'Modern architecture emphasizes functionality, simplicity, and the principle that form should follow function.'\r\n          }\r\n        ],\r\n        minimumScore: 75,\r\n        attempts: 0,\r\n        maxAttempts: 2,\r\n        isPassed: false\r\n      }\r\n    }\r\n  ],\r\n  finalExam: {\r\n    id: 'final-exam',\r\n    title: 'Ujian Akhir Sertifikasi Arsitektur',\r\n    type: 'final',\r\n    questions: [\r\n      {\r\n        id: 'qf1',\r\n        question:\r\n          'Which architectural order features ornate capitals with acanthus leaves?',\r\n        type: 'multiple-choice',\r\n        options: ['Doric', 'Ionic', 'Corinthian', 'Tuscan'],\r\n        correctAnswer: 2,\r\n        explanation:\r\n          'The Corinthian order is distinguished by its elaborate capitals decorated with acanthus leaves.'\r\n      },\r\n      {\r\n        id: 'qf2',\r\n        question:\r\n          'The Golden Ratio is frequently used in architectural design.',\r\n        type: 'true-false',\r\n        correctAnswer: 'true',\r\n        explanation:\r\n          'The Golden Ratio (approximately 1.618) has been used in architecture for centuries to create pleasing proportions.'\r\n      },\r\n      {\r\n        id: 'qf3',\r\n        question:\r\n          'Describe the influence of the Bauhaus movement on modern architecture.',\r\n        type: 'essay',\r\n        correctAnswer:\r\n          'The Bauhaus movement emphasized functionality, simplicity, and the integration of art and technology, leading to modern minimalist design principles.',\r\n        explanation:\r\n          'The Bauhaus movement revolutionized architecture by promoting functional design, clean lines, and the principle that form should follow function.'\r\n      }\r\n    ],\r\n    minimumScore: 80,\r\n    timeLimit: 120,\r\n    attempts: 0,\r\n    maxAttempts: 2,\r\n    isPassed: false\r\n  },\r\n  certificate: {\r\n    isEligible: true,\r\n    isGenerated: false,\r\n    type: 'professional',\r\n    issuedBy: 'Ikatan Arsitek Indonesia (IAI)',\r\n    validityPeriod: '5 years',\r\n    credentialId: 'IAI-CERT-2024',\r\n    description: 'Sertifikat Arsitek Profesional yang diakui secara nasional untuk praktik arsitektur di Indonesia'\r\n  }\r\n};\r\n\r\n// For enroll page compatibility, convert to class format\r\nexport interface ClassData {\r\n  id: number;\r\n  name: string;\r\n  teacher: string;\r\n  schedule: string;\r\n  enrollmentCode: string;\r\n  materials: any[];\r\n  thumbnail?: string;\r\n  description?: string;\r\n  startDate?: string;\r\n  endDate?: string;\r\n}\r\n\r\nexport const architectureClass: ClassData = {\r\n  id: 1,\r\n  name: architectureCourse.name,\r\n  teacher: architectureCourse.instructor,\r\n  schedule: 'Sen, Rab 10:00 WIB',\r\n  enrollmentCode: architectureCourse.enrollmentCode || 'ARCH2024-001',\r\n  description: architectureCourse.description,\r\n  startDate: architectureCourse.startDate,\r\n  endDate: architectureCourse.endDate,\r\n  materials: [] // Will be populated from modules when enrolled\r\n};\r\n\r\n// Institution data\r\nexport const architectureInstitution = {\r\n  id: 'iai-indonesia',\r\n  name: 'Ikatan Arsitek Indonesia',\r\n  shortName: 'IAI',\r\n  website: 'https://iai.or.id',\r\n  certificateTemplate: {\r\n    primaryColor: '#1e40af',\r\n    secondaryColor: '#f59e0b',\r\n    signatoryName: 'Ar. Georgius Budi Yulianto, IAI, AA',\r\n    signatoryTitle: 'Ketua Umum IAI 2024-2027'\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;;AAGO,MAAM,qBAA6B;IACxC,IAAI;IACJ,MAAM;IACN,MAAM;IACN,aACE;IACF,YAAY;IACZ,WAAW;IACX,SAAS;IACT,gBAAgB;IAChB,gBAAgB;IAChB,iBAAiB;IACjB,eAAe;IACf,QAAQ;IACR,SAAS;QACP;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,YAAY;YACZ,sBAAsB;YACtB,UAAU;gBACR;oBACE,IAAI;oBACJ,OAAO;oBACP,OAAO;oBACP,YAAY;oBACZ,sBAAsB;oBACtB,UAAU;wBACR;4BACE,IAAI;4BACJ,OAAO;4BACP,MAAM;4BACN,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kGAwD0E,CAAC;4BACrF,aAAa;4BACb,WAAW;wBACb;wBACA;4BACE,IAAI;4BACJ,OAAO;4BACP,MAAM;4BACN,SAAS;4BACT,UAAU;4BACV,aAAa;4BACb,WAAW;wBACb;wBACA;4BACE,IAAI;4BACJ,OAAO;4BACP,MAAM;4BACN,SAAS;4BACT,aAAa;4BACb,WAAW;wBACb;qBACD;oBACD,MAAM;wBACJ,IAAI;wBACJ,OAAO;wBACP,MAAM;wBACN,WAAW;4BACT;gCACE,IAAI;gCACJ,UACE;gCACF,MAAM;gCACN,SAAS;oCAAC;oCAAS;oCAAS;oCAAc;iCAAY;gCACtD,eAAe;gCACf,aACE;4BACJ;yBACD;wBACD,cAAc;wBACd,WAAW;wBACX,UAAU;wBACV,aAAa;wBACb,UAAU;oBACZ;gBACF;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,OAAO;oBACP,YAAY;oBACZ,sBAAsB;oBACtB,UAAU;wBACR;4BACE,IAAI;4BACJ,OAAO;4BACP,MAAM;4BACN,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8FAqEsE,CAAC;4BACjF,aAAa;4BACb,WAAW;wBACb;wBACA;4BACE,IAAI;4BACJ,OAAO;4BACP,MAAM;4BACN,SAAS;4BACT,UAAU;4BACV,aAAa;4BACb,WAAW;wBACb;qBACD;oBACD,MAAM;wBACJ,IAAI;wBACJ,OAAO;wBACP,MAAM;wBACN,WAAW;4BACT;gCACE,IAAI;gCACJ,UAAU;gCACV,MAAM;gCACN,SAAS;oCAAC;oCAAS;oCAAS;oCAAS;iCAAQ;gCAC7C,eAAe;gCACf,aACE;4BACJ;4BACA;gCACE,IAAI;gCACJ,UACE;gCACF,MAAM;gCACN,eAAe;gCACf,aACE;4BACJ;yBACD;wBACD,cAAc;wBACd,UAAU;wBACV,aAAa;wBACb,UAAU;oBACZ;gBACF;aACD;YACD,YAAY;gBACV,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,WAAW;oBACT;wBACE,IAAI;wBACJ,UACE;wBACF,MAAM;wBACN,SAAS;4BAAC;4BAAS;4BAAS;4BAAc;yBAAmB;wBAC7D,eAAe;wBACf,aACE;oBACJ;iBACD;gBACD,cAAc;gBACd,UAAU;gBACV,aAAa;gBACb,UAAU;YACZ;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,YAAY;YACZ,sBAAsB;YACtB,UAAU;gBACR;oBACE,IAAI;oBACJ,OAAO;oBACP,OAAO;oBACP,YAAY;oBACZ,sBAAsB;oBACtB,UAAU;wBACR;4BACE,IAAI;4BACJ,OAAO;4BACP,MAAM;4BACN,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6IAwGqH,CAAC;4BAChI,aAAa;4BACb,WAAW;wBACb;qBACD;oBACD,MAAM;wBACJ,IAAI;wBACJ,OAAO;wBACP,MAAM;wBACN,WAAW;4BACT;gCACE,IAAI;gCACJ,UACE;gCACF,MAAM;gCACN,eAAe;gCACf,aACE;4BACJ;yBACD;wBACD,cAAc;wBACd,UAAU;wBACV,aAAa;wBACb,UAAU;oBACZ;gBACF;aACD;YACD,YAAY;gBACV,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,WAAW;oBACT;wBACE,IAAI;wBACJ,UAAU;wBACV,MAAM;wBACN,eACE;wBACF,aACE;oBACJ;iBACD;gBACD,cAAc;gBACd,UAAU;gBACV,aAAa;gBACb,UAAU;YACZ;QACF;KACD;IACD,WAAW;QACT,IAAI;QACJ,OAAO;QACP,MAAM;QACN,WAAW;YACT;gBACE,IAAI;gBACJ,UACE;gBACF,MAAM;gBACN,SAAS;oBAAC;oBAAS;oBAAS;oBAAc;iBAAS;gBACnD,eAAe;gBACf,aACE;YACJ;YACA;gBACE,IAAI;gBACJ,UACE;gBACF,MAAM;gBACN,eAAe;gBACf,aACE;YACJ;YACA;gBACE,IAAI;gBACJ,UACE;gBACF,MAAM;gBACN,eACE;gBACF,aACE;YACJ;SACD;QACD,cAAc;QACd,WAAW;QACX,UAAU;QACV,aAAa;QACb,UAAU;IACZ;IACA,aAAa;QACX,YAAY;QACZ,aAAa;QACb,MAAM;QACN,UAAU;QACV,gBAAgB;QAChB,cAAc;QACd,aAAa;IACf;AACF;AAgBO,MAAM,oBAA+B;IAC1C,IAAI;IACJ,MAAM,mBAAmB,IAAI;IAC7B,SAAS,mBAAmB,UAAU;IACtC,UAAU;IACV,gBAAgB,mBAAmB,cAAc,IAAI;IACrD,aAAa,mBAAmB,WAAW;IAC3C,WAAW,mBAAmB,SAAS;IACvC,SAAS,mBAAmB,OAAO;IACnC,WAAW,EAAE,CAAC,+CAA+C;AAC/D;AAGO,MAAM,0BAA0B;IACrC,IAAI;IACJ,MAAM;IACN,WAAW;IACX,SAAS;IACT,qBAAqB;QACnB,cAAc;QACd,gBAAgB;QAChB,eAAe;QACf,gBAAgB;IAClB;AACF", "debugId": null}}, {"offset": {"line": 4009, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/contexts/enrollment-context.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\r\nimport { architectureCourse } from '@/constants/shared-course-data';\r\nimport { Course } from '@/types/lms';\r\n\r\ninterface EnrollmentData {\r\n  isEnrolled: boolean;\r\n  courseData: Course;\r\n  enrollmentTimestamp: number;\r\n  expirationTime: number; // 10 minutes in milliseconds\r\n}\r\n\r\ninterface MultipleEnrollmentData {\r\n  enrolledCourses: Course[];\r\n  enrollmentTimestamp: number;\r\n  expirationTime: number; // 10 minutes in milliseconds\r\n}\r\n\r\ninterface EnrollmentContextType {\r\n  isEnrolled: boolean;\r\n  courseData: Course;\r\n  enrollInCourse: () => void;\r\n  enrollInCourseWithPurchase: (course: Course) => void;\r\n  updateCourseProgress: (updatedCourse: Course) => void;\r\n  enrolledCourses: Course[];\r\n  isEnrolledInCourse: (courseId: string) => boolean;\r\n  getCourseById: (courseId: string) => Course | undefined;\r\n}\r\n\r\nconst EnrollmentContext = createContext<EnrollmentContextType | undefined>(\r\n  undefined\r\n);\r\n\r\nexport const useEnrollment = () => {\r\n  const context = useContext(EnrollmentContext);\r\n  if (!context) {\r\n    throw new Error('useEnrollment must be used within an EnrollmentProvider');\r\n  }\r\n  return context;\r\n};\r\n\r\ninterface EnrollmentProviderProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport const EnrollmentProvider: React.FC<EnrollmentProviderProps> = ({\r\n  children\r\n}) => {\r\n  const [isEnrolled, setIsEnrolled] = useState(false);\r\n  const [courseData, setCourseData] = useState<Course>(architectureCourse);\r\n  const [enrolledCourses, setEnrolledCourses] = useState<Course[]>([]);\r\n\r\n  const STORAGE_KEY = 'lms-enrollment-data';\r\n  const MULTIPLE_STORAGE_KEY = 'lms-multiple-enrollment-data';\r\n  const EXPIRATION_TIME = 10 * 60 * 1000; // 10 minutes in milliseconds\r\n\r\n  // Load persisted data on component mount\r\n  useEffect(() => {\r\n    const loadPersistedData = () => {\r\n      try {\r\n        // Try to load multiple enrollment data first\r\n        const multipleStored = localStorage.getItem(MULTIPLE_STORAGE_KEY);\r\n        if (multipleStored) {\r\n          const multipleData: MultipleEnrollmentData = JSON.parse(multipleStored);\r\n          const now = Date.now();\r\n\r\n          // Check if enrollment has expired\r\n          if (now < multipleData.expirationTime) {\r\n            setEnrolledCourses(multipleData.enrolledCourses);\r\n            setIsEnrolled(multipleData.enrolledCourses.length > 0);\r\n            if (multipleData.enrolledCourses.length > 0) {\r\n              setCourseData(multipleData.enrolledCourses[0]); // Set first course as primary\r\n            }\r\n          } else {\r\n            // Clear expired data\r\n            localStorage.removeItem(MULTIPLE_STORAGE_KEY);\r\n          }\r\n          return;\r\n        }\r\n\r\n        // Fallback to old single enrollment data for backward compatibility\r\n        const stored = localStorage.getItem(STORAGE_KEY);\r\n        if (stored) {\r\n          const enrollmentData: EnrollmentData = JSON.parse(stored);\r\n          const now = Date.now();\r\n\r\n          // Check if enrollment has expired\r\n          if (now < enrollmentData.expirationTime) {\r\n            setIsEnrolled(enrollmentData.isEnrolled);\r\n            setCourseData(enrollmentData.courseData);\r\n            setEnrolledCourses([enrollmentData.courseData]);\r\n\r\n            // Migrate to new format\r\n            const multipleData: MultipleEnrollmentData = {\r\n              enrolledCourses: [enrollmentData.courseData],\r\n              enrollmentTimestamp: enrollmentData.enrollmentTimestamp,\r\n              expirationTime: enrollmentData.expirationTime\r\n            };\r\n            localStorage.setItem(MULTIPLE_STORAGE_KEY, JSON.stringify(multipleData));\r\n            localStorage.removeItem(STORAGE_KEY); // Remove old format\r\n          } else {\r\n            // Clear expired data\r\n            localStorage.removeItem(STORAGE_KEY);\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('Failed to load enrollment data:', error);\r\n        localStorage.removeItem(STORAGE_KEY);\r\n        localStorage.removeItem(MULTIPLE_STORAGE_KEY);\r\n      }\r\n    };\r\n\r\n    loadPersistedData();\r\n  }, []);\r\n\r\n  // Persist enrollment data to localStorage\r\n  const persistEnrollmentData = (course: Course) => {\r\n    const now = Date.now();\r\n\r\n    try {\r\n      // Update enrolled courses state\r\n      setEnrolledCourses(prev => {\r\n        const isAlreadyEnrolled = prev.some(c => c.id === course.id);\r\n        let updatedCourses;\r\n\r\n        if (isAlreadyEnrolled) {\r\n          // Update existing course\r\n          updatedCourses = prev.map(c => c.id === course.id ? course : c);\r\n        } else {\r\n          // Add new course\r\n          updatedCourses = [...prev, course];\r\n        }\r\n\r\n        // Save to localStorage with new format\r\n        const multipleData: MultipleEnrollmentData = {\r\n          enrolledCourses: updatedCourses,\r\n          enrollmentTimestamp: now,\r\n          expirationTime: now + EXPIRATION_TIME\r\n        };\r\n        localStorage.setItem(MULTIPLE_STORAGE_KEY, JSON.stringify(multipleData));\r\n\r\n        return updatedCourses;\r\n      });\r\n\r\n      // Set up automatic cleanup after expiration\r\n      setTimeout(() => {\r\n        localStorage.removeItem(MULTIPLE_STORAGE_KEY);\r\n        setIsEnrolled(false);\r\n        setEnrolledCourses([]);\r\n        setCourseData(architectureCourse);\r\n      }, EXPIRATION_TIME);\r\n    } catch (error) {\r\n      console.error('Failed to persist enrollment data:', error);\r\n    }\r\n  };\r\n\r\n  const enrollInCourse = () => {\r\n    setIsEnrolled(true);\r\n    const updatedCourse = {\r\n      ...architectureCourse,\r\n      status: 'in-progress' as const\r\n    };\r\n    setCourseData(updatedCourse);\r\n    persistEnrollmentData(updatedCourse);\r\n  };\r\n\r\n  const enrollInCourseWithPurchase = (course: Course) => {\r\n    setIsEnrolled(true);\r\n    const updatedCourse = {\r\n      ...course,\r\n      status: 'in-progress' as const,\r\n      totalProgress: 0\r\n    };\r\n    setCourseData(updatedCourse);\r\n    persistEnrollmentData(updatedCourse);\r\n  };\r\n\r\n  const updateCourseProgress = (updatedCourse: Course) => {\r\n    // Update the primary courseData if it's the same course\r\n    if (courseData.id === updatedCourse.id) {\r\n      setCourseData(updatedCourse);\r\n    }\r\n\r\n    // Update the course in enrolledCourses array\r\n    setEnrolledCourses(prev =>\r\n      prev.map(course =>\r\n        course.id === updatedCourse.id ? updatedCourse : course\r\n      )\r\n    );\r\n\r\n    // Update persisted data with new progress\r\n    if (isEnrolled) {\r\n      persistEnrollmentData(updatedCourse);\r\n    }\r\n  };\r\n\r\n  // Check if user is enrolled in a specific course\r\n  const isEnrolledInCourse = (courseId: string): boolean => {\r\n    return enrolledCourses.some(course => course.id === courseId);\r\n  };\r\n\r\n  // Get a specific course by ID\r\n  const getCourseById = (courseId: string): Course | undefined => {\r\n    return enrolledCourses.find(course => course.id === courseId);\r\n  };\r\n\r\n  const value = {\r\n    isEnrolled,\r\n    courseData,\r\n    enrollInCourse,\r\n    enrollInCourseWithPurchase,\r\n    updateCourseProgress,\r\n    enrolledCourses,\r\n    isEnrolledInCourse,\r\n    getCourseById\r\n  };\r\n\r\n  return (\r\n    <EnrollmentContext.Provider value={value}>\r\n      {children}\r\n    </EnrollmentContext.Provider>\r\n  );\r\n};"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AA8BA,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EACpC;AAGK,MAAM,gBAAgB;IAC3B,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAMO,MAAM,qBAAwD,CAAC,EACpE,QAAQ,EACT;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,4IAAA,CAAA,qBAAkB;IACvE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAEnE,MAAM,cAAc;IACpB,MAAM,uBAAuB;IAC7B,MAAM,kBAAkB,KAAK,KAAK,MAAM,6BAA6B;IAErE,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,oBAAoB;YACxB,IAAI;gBACF,6CAA6C;gBAC7C,MAAM,iBAAiB,aAAa,OAAO,CAAC;gBAC5C,IAAI,gBAAgB;oBAClB,MAAM,eAAuC,KAAK,KAAK,CAAC;oBACxD,MAAM,MAAM,KAAK,GAAG;oBAEpB,kCAAkC;oBAClC,IAAI,MAAM,aAAa,cAAc,EAAE;wBACrC,mBAAmB,aAAa,eAAe;wBAC/C,cAAc,aAAa,eAAe,CAAC,MAAM,GAAG;wBACpD,IAAI,aAAa,eAAe,CAAC,MAAM,GAAG,GAAG;4BAC3C,cAAc,aAAa,eAAe,CAAC,EAAE,GAAG,8BAA8B;wBAChF;oBACF,OAAO;wBACL,qBAAqB;wBACrB,aAAa,UAAU,CAAC;oBAC1B;oBACA;gBACF;gBAEA,oEAAoE;gBACpE,MAAM,SAAS,aAAa,OAAO,CAAC;gBACpC,IAAI,QAAQ;oBACV,MAAM,iBAAiC,KAAK,KAAK,CAAC;oBAClD,MAAM,MAAM,KAAK,GAAG;oBAEpB,kCAAkC;oBAClC,IAAI,MAAM,eAAe,cAAc,EAAE;wBACvC,cAAc,eAAe,UAAU;wBACvC,cAAc,eAAe,UAAU;wBACvC,mBAAmB;4BAAC,eAAe,UAAU;yBAAC;wBAE9C,wBAAwB;wBACxB,MAAM,eAAuC;4BAC3C,iBAAiB;gCAAC,eAAe,UAAU;6BAAC;4BAC5C,qBAAqB,eAAe,mBAAmB;4BACvD,gBAAgB,eAAe,cAAc;wBAC/C;wBACA,aAAa,OAAO,CAAC,sBAAsB,KAAK,SAAS,CAAC;wBAC1D,aAAa,UAAU,CAAC,cAAc,oBAAoB;oBAC5D,OAAO;wBACL,qBAAqB;wBACrB,aAAa,UAAU,CAAC;oBAC1B;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mCAAmC;gBACjD,aAAa,UAAU,CAAC;gBACxB,aAAa,UAAU,CAAC;YAC1B;QACF;QAEA;IACF,GAAG,EAAE;IAEL,0CAA0C;IAC1C,MAAM,wBAAwB,CAAC;QAC7B,MAAM,MAAM,KAAK,GAAG;QAEpB,IAAI;YACF,gCAAgC;YAChC,mBAAmB,CAAA;gBACjB,MAAM,oBAAoB,KAAK,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,OAAO,EAAE;gBAC3D,IAAI;gBAEJ,IAAI,mBAAmB;oBACrB,yBAAyB;oBACzB,iBAAiB,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,OAAO,EAAE,GAAG,SAAS;gBAC/D,OAAO;oBACL,iBAAiB;oBACjB,iBAAiB;2BAAI;wBAAM;qBAAO;gBACpC;gBAEA,uCAAuC;gBACvC,MAAM,eAAuC;oBAC3C,iBAAiB;oBACjB,qBAAqB;oBACrB,gBAAgB,MAAM;gBACxB;gBACA,aAAa,OAAO,CAAC,sBAAsB,KAAK,SAAS,CAAC;gBAE1D,OAAO;YACT;YAEA,4CAA4C;YAC5C,WAAW;gBACT,aAAa,UAAU,CAAC;gBACxB,cAAc;gBACd,mBAAmB,EAAE;gBACrB,cAAc,4IAAA,CAAA,qBAAkB;YAClC,GAAG;QACL,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;QACtD;IACF;IAEA,MAAM,iBAAiB;QACrB,cAAc;QACd,MAAM,gBAAgB;YACpB,GAAG,4IAAA,CAAA,qBAAkB;YACrB,QAAQ;QACV;QACA,cAAc;QACd,sBAAsB;IACxB;IAEA,MAAM,6BAA6B,CAAC;QAClC,cAAc;QACd,MAAM,gBAAgB;YACpB,GAAG,MAAM;YACT,QAAQ;YACR,eAAe;QACjB;QACA,cAAc;QACd,sBAAsB;IACxB;IAEA,MAAM,uBAAuB,CAAC;QAC5B,wDAAwD;QACxD,IAAI,WAAW,EAAE,KAAK,cAAc,EAAE,EAAE;YACtC,cAAc;QAChB;QAEA,6CAA6C;QAC7C,mBAAmB,CAAA,OACjB,KAAK,GAAG,CAAC,CAAA,SACP,OAAO,EAAE,KAAK,cAAc,EAAE,GAAG,gBAAgB;QAIrD,0CAA0C;QAC1C,IAAI,YAAY;YACd,sBAAsB;QACxB;IACF;IAEA,iDAAiD;IACjD,MAAM,qBAAqB,CAAC;QAC1B,OAAO,gBAAgB,IAAI,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;IACtD;IAEA,8BAA8B;IAC9B,MAAM,gBAAgB,CAAC;QACrB,OAAO,gBAAgB,IAAI,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;IACtD;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,kBAAkB,QAAQ;QAAC,OAAO;kBAChC;;;;;;AAGP", "debugId": null}}]}