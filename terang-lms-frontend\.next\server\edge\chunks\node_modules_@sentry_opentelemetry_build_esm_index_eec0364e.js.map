{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["turbopack:///[project]/node_modules/@sentry/opentelemetry/src/semanticAttributes.ts", "turbopack:///[project]/node_modules/@sentry/opentelemetry/src/utils/getParentSpanId.ts", "turbopack:///[project]/node_modules/@sentry/opentelemetry/src/utils/spanTypes.ts", "turbopack:///[project]/node_modules/@sentry/opentelemetry/src/utils/getRequestSpanData.ts", "turbopack:///[project]/node_modules/@sentry/opentelemetry/src/custom/client.ts", "turbopack:///[project]/node_modules/@sentry/opentelemetry/src/utils/getSpanKind.ts", "turbopack:///[project]/node_modules/@sentry/opentelemetry/src/constants.ts", "turbopack:///[project]/node_modules/@sentry/opentelemetry/src/utils/contextData.ts", "turbopack:///[project]/node_modules/@sentry/opentelemetry/src/utils/isSentryRequest.ts", "turbopack:///[project]/node_modules/@sentry/opentelemetry/src/utils/getSamplingDecision.ts", "turbopack:///[project]/node_modules/@sentry/opentelemetry/src/utils/parseSpanDescription.ts", "turbopack:///[project]/node_modules/@sentry/opentelemetry/src/utils/enhanceDscWithOpenTelemetryRootSpanName.ts", "turbopack:///[project]/node_modules/@sentry/opentelemetry/src/utils/getActiveSpan.ts", "turbopack:///[project]/node_modules/@sentry/opentelemetry/src/debug-build.ts", "turbopack:///[project]/node_modules/@sentry/opentelemetry/src/utils/makeTraceState.ts", "turbopack:///[project]/node_modules/@sentry/opentelemetry/src/utils/setupCheck.ts", "turbopack:///[project]/node_modules/@sentry/opentelemetry/src/propagator.ts", "turbopack:///[project]/node_modules/@sentry/opentelemetry/src/trace.ts", "turbopack:///[project]/node_modules/@sentry/opentelemetry/src/utils/suppressTracing.ts", "turbopack:///[project]/node_modules/@sentry/opentelemetry/src/setupEventContextTrace.ts", "turbopack:///[project]/node_modules/@sentry/opentelemetry/src/utils/getTraceData.ts", "turbopack:///[project]/node_modules/@sentry/opentelemetry/src/asyncContextStrategy.ts", "turbopack:///[project]/node_modules/@sentry/opentelemetry/src/contextManager.ts", "turbopack:///[project]/node_modules/@sentry/opentelemetry/src/utils/groupSpansWithParents.ts", "turbopack:///[project]/node_modules/@sentry/opentelemetry/src/utils/mapStatus.ts", "turbopack:///[project]/node_modules/@sentry/opentelemetry/src/spanExporter.ts", "turbopack:///[project]/node_modules/@sentry/opentelemetry/src/spanProcessor.ts", "turbopack:///[project]/node_modules/@sentry/opentelemetry/src/sampler.ts"], "sourcesContent": ["/** If this attribute is true, it means that the parent is a remote span. */\nexport const SEMANTIC_ATTRIBUTE_SENTRY_PARENT_IS_REMOTE = 'sentry.parentIsRemote';\n\n// These are not standardized yet, but used by the graphql instrumentation\nexport const SEMANTIC_ATTRIBUTE_SENTRY_GRAPHQL_OPERATION = 'sentry.graphql.operation';\n", "import type { ReadableSpan } from '@opentelemetry/sdk-trace-base';\n\n/**\n * Get the parent span id from a span.\n * In OTel v1, the parent span id is accessed as `parentSpanId`\n * In OTel v2, the parent span id is accessed as `spanId` on the `parentSpanContext`\n */\nexport function getParentSpanId(span: ReadableSpan): string | undefined {\n  if ('parentSpanId' in span) {\n    return span.parentSpanId as string | undefined;\n  } else if ('parentSpanContext' in span) {\n    return (span.parentSpanContext as { spanId?: string } | undefined)?.spanId;\n  }\n\n  return undefined;\n}\n", "import type { SpanKind, SpanStatus } from '@opentelemetry/api';\nimport type { ReadableSpan, TimedEvent } from '@opentelemetry/sdk-trace-base';\nimport type { AbstractSpan } from '../types';\nimport { getParentSpanId } from './getParentSpanId';\n\n/**\n * Check if a given span has attributes.\n * This is necessary because the base `Span` type does not have attributes,\n * so in places where we are passed a generic span, we need to check if we want to access them.\n */\nexport function spanHasAttributes<SpanType extends AbstractSpan>(\n  span: SpanType,\n): span is SpanType & { attributes: ReadableSpan['attributes'] } {\n  const castSpan = span as ReadableSpan;\n  return !!castSpan.attributes && typeof castSpan.attributes === 'object';\n}\n\n/**\n * Check if a given span has a kind.\n * This is necessary because the base `Span` type does not have a kind,\n * so in places where we are passed a generic span, we need to check if we want to access it.\n */\nexport function spanHasKind<SpanType extends AbstractSpan>(span: SpanType): span is SpanType & { kind: SpanKind } {\n  const castSpan = span as ReadableSpan;\n  return typeof castSpan.kind === 'number';\n}\n\n/**\n * Check if a given span has a status.\n * This is necessary because the base `Span` type does not have a status,\n * so in places where we are passed a generic span, we need to check if we want to access it.\n */\nexport function spanHasStatus<SpanType extends AbstractSpan>(\n  span: SpanType,\n): span is SpanType & { status: SpanStatus } {\n  const castSpan = span as ReadableSpan;\n  return !!castSpan.status;\n}\n\n/**\n * Check if a given span has a name.\n * This is necessary because the base `Span` type does not have a name,\n * so in places where we are passed a generic span, we need to check if we want to access it.\n */\nexport function spanHasName<SpanType extends AbstractSpan>(span: SpanType): span is SpanType & { name: string } {\n  const castSpan = span as ReadableSpan;\n  return !!castSpan.name;\n}\n\n/**\n * Check if a given span has a kind.\n * This is necessary because the base `Span` type does not have a kind,\n * so in places where we are passed a generic span, we need to check if we want to access it.\n */\nexport function spanHasParentId<SpanType extends AbstractSpan>(\n  span: SpanType,\n): span is SpanType & { parentSpanId: string } {\n  const castSpan = span as ReadableSpan;\n  return !!getParentSpanId(castSpan);\n}\n\n/**\n * Check if a given span has events.\n * This is necessary because the base `Span` type does not have events,\n * so in places where we are passed a generic span, we need to check if we want to access it.\n */\nexport function spanHasEvents<SpanType extends AbstractSpan>(\n  span: SpanType,\n): span is SpanType & { events: TimedEvent[] } {\n  const castSpan = span as ReadableSpan;\n  return Array.isArray(castSpan.events);\n}\n", "import type { Span } from '@opentelemetry/api';\nimport type { ReadableSpan } from '@opentelemetry/sdk-trace-base';\nimport {\n  ATTR_HTTP_REQUEST_METHOD,\n  ATTR_URL_FULL,\n  SEMATTRS_HTTP_METHOD,\n  SEMATTRS_HTTP_URL,\n} from '@opentelemetry/semantic-conventions';\nimport type { SanitizedRequestData } from '@sentry/core';\nimport { getSanitizedUrlString, parseUrl } from '@sentry/core';\nimport { spanHasAttributes } from './spanTypes';\n\n/**\n * Get sanitizied request data from an OTEL span.\n */\nexport function getRequestSpanData(span: Span | ReadableSpan): Partial<SanitizedRequestData> {\n  // The base `Span` type has no `attributes`, so we need to guard here against that\n  if (!spanHasAttributes(span)) {\n    return {};\n  }\n\n  // eslint-disable-next-line deprecation/deprecation\n  const maybeUrlAttribute = (span.attributes[ATTR_URL_FULL] || span.attributes[SEMATTRS_HTTP_URL]) as\n    | string\n    | undefined;\n\n  const data: Partial<SanitizedRequestData> = {\n    url: maybeUrlAttribute,\n    // eslint-disable-next-line deprecation/deprecation\n    'http.method': (span.attributes[ATTR_HTTP_REQUEST_METHOD] || span.attributes[SEMATTRS_HTTP_METHOD]) as\n      | string\n      | undefined,\n  };\n\n  // Default to GET if URL is set but method is not\n  if (!data['http.method'] && data.url) {\n    data['http.method'] = 'GET';\n  }\n\n  try {\n    if (typeof maybeUrlAttribute === 'string') {\n      const url = parseUrl(maybeUrlAttribute);\n\n      data.url = getSanitizedUrlString(url);\n\n      if (url.search) {\n        data['http.query'] = url.search;\n      }\n      if (url.hash) {\n        data['http.fragment'] = url.hash;\n      }\n    }\n  } catch {\n    // ignore\n  }\n\n  return data;\n}\n", "import type { Tracer } from '@opentelemetry/api';\nimport { trace } from '@opentelemetry/api';\nimport type { BasicTracerProvider } from '@opentelemetry/sdk-trace-base';\nimport type { Client } from '@sentry/core';\nimport { SDK_VERSION } from '@sentry/core';\nimport type { OpenTelemetryClient as OpenTelemetryClientInterface } from '../types';\n\n// Typescript complains if we do not use `...args: any[]` for the mixin, with:\n// A mixin class must have a constructor with a single rest parameter of type 'any[]'.ts(2545)\n/* eslint-disable @typescript-eslint/no-explicit-any */\n\n/**\n * Wrap an Client class with things we need for OpenTelemetry support.\n * Make sure that the Client class passed in is non-abstract!\n *\n * Usage:\n * const OpenTelemetryClient = getWrappedClientClass(NodeClient);\n * const client = new OpenTelemetryClient(options);\n */\nexport function wrapClientClass<\n  ClassConstructor extends new (...args: any[]) => Client,\n  WrappedClassConstructor extends new (...args: any[]) => Client & OpenTelemetryClientInterface,\n>(ClientClass: ClassConstructor): WrappedClassConstructor {\n  // @ts-expect-error We just assume that this is non-abstract, if you pass in an abstract class this would make it non-abstract\n  class OpenTelemetryClient extends ClientClass implements OpenTelemetryClientInterface {\n    public traceProvider: BasicTracerProvider | undefined;\n    private _tracer: Tracer | undefined;\n\n    public constructor(...args: any[]) {\n      super(...args);\n    }\n\n    /** Get the OTEL tracer. */\n    public get tracer(): Tracer {\n      if (this._tracer) {\n        return this._tracer;\n      }\n\n      const name = '@sentry/opentelemetry';\n      const version = SDK_VERSION;\n      const tracer = trace.getTracer(name, version);\n      this._tracer = tracer;\n\n      return tracer;\n    }\n\n    /**\n     * @inheritDoc\n     */\n    public async flush(timeout?: number): Promise<boolean> {\n      const provider = this.traceProvider;\n      await provider?.forceFlush();\n      return super.flush(timeout);\n    }\n  }\n\n  return OpenTelemetryClient as unknown as WrappedClassConstructor;\n}\n/* eslint-enable @typescript-eslint/no-explicit-any */\n", "import { SpanKind } from '@opentelemetry/api';\nimport type { AbstractSpan } from '../types';\nimport { spanHasKind } from './spanTypes';\n\n/**\n * Get the span kind from a span.\n * For whatever reason, this is not public API on the generic \"Span\" type,\n * so we need to check if we actually have a `SDKTraceBaseSpan` where we can fetch this from.\n * Otherwise, we fall back to `SpanKind.INTERNAL`.\n */\nexport function getSpanKind(span: AbstractSpan): SpanKind {\n  if (spanHasKind(span)) {\n    return span.kind;\n  }\n\n  return SpanKind.INTERNAL;\n}\n", "import { createContextKey } from '@opentelemetry/api';\n\nexport const SENTRY_TRACE_HEADER = 'sentry-trace';\nexport const SENTRY_BAGGAGE_HEADER = 'baggage';\n\nexport const SENTRY_TRACE_STATE_DSC = 'sentry.dsc';\nexport const SENTRY_TRACE_STATE_SAMPLED_NOT_RECORDING = 'sentry.sampled_not_recording';\nexport const SENTRY_TRACE_STATE_URL = 'sentry.url';\nexport const SENTRY_TRACE_STATE_SAMPLE_RAND = 'sentry.sample_rand';\nexport const SENTRY_TRACE_STATE_SAMPLE_RATE = 'sentry.sample_rate';\n\nexport const SENTRY_SCOPES_CONTEXT_KEY = createContextKey('sentry_scopes');\n\nexport const SENTRY_FORK_ISOLATION_SCOPE_CONTEXT_KEY = createContextKey('sentry_fork_isolation_scope');\n\nexport const SENTRY_FORK_SET_SCOPE_CONTEXT_KEY = createContextKey('sentry_fork_set_scope');\n\nexport const SENTRY_FORK_SET_ISOLATION_SCOPE_CONTEXT_KEY = createContextKey('sentry_fork_set_isolation_scope');\n", "import type { Context } from '@opentelemetry/api';\nimport type { Scope } from '@sentry/core';\nimport { addNonEnumerableProperty } from '@sentry/core';\nimport { SENTRY_SCOPES_CONTEXT_KEY } from '../constants';\nimport type { CurrentScopes } from '../types';\n\nconst SCOPE_CONTEXT_FIELD = '_scopeContext';\n\n/**\n * Try to get the current scopes from the given OTEL context.\n * This requires a Context Manager that was wrapped with getWrappedContextManager.\n */\nexport function getScopesFromContext(context: Context): CurrentScopes | undefined {\n  return context.getValue(SENTRY_SCOPES_CONTEXT_KEY) as CurrentScopes | undefined;\n}\n\n/**\n * Set the current scopes on an OTEL context.\n * This will return a forked context with the Propagation Context set.\n */\nexport function setScopesOnContext(context: Context, scopes: CurrentScopes): Context {\n  return context.setValue(SENTRY_SCOPES_CONTEXT_KEY, scopes);\n}\n\n/**\n * Set the context on the scope so we can later look it up.\n * We need this to get the context from the scope in the `trace` functions.\n */\nexport function setContextOnScope(scope: Scope, context: Context): void {\n  addNonEnumerableProperty(scope, SCOPE_CONTEXT_FIELD, context);\n}\n\n/**\n * Get the context related to a scope.\n */\nexport function getContextFromScope(scope: Scope): Context | undefined {\n  return (scope as { [SCOPE_CONTEXT_FIELD]?: Context })[SCOPE_CONTEXT_FIELD];\n}\n", "import { ATTR_URL_FULL, SEMATTRS_HTTP_URL } from '@opentelemetry/semantic-conventions';\nimport { getClient, isSentryRequestUrl } from '@sentry/core';\nimport type { AbstractSpan } from '../types';\nimport { spanHasAttributes } from './spanTypes';\n\n/**\n *\n * @param otelSpan Checks whether a given OTEL Span is an http request to sentry.\n * @returns boolean\n */\nexport function isSentryRequestSpan(span: AbstractSpan): boolean {\n  if (!spanHasAttributes(span)) {\n    return false;\n  }\n\n  const { attributes } = span;\n\n  // `ATTR_URL_FULL` is the new attribute, but we still support the old one, `ATTR_HTTP_URL`, for now.\n  // eslint-disable-next-line deprecation/deprecation\n  const httpUrl = attributes[SEMATTRS_HTTP_URL] || attributes[ATTR_URL_FULL];\n\n  if (!httpUrl) {\n    return false;\n  }\n\n  return isSentryRequestUrl(httpUrl.toString(), getClient());\n}\n", "import type { SpanContext } from '@opentelemetry/api';\nimport { TraceFlags } from '@opentelemetry/api';\nimport { baggageHeaderToDynamicSamplingContext } from '@sentry/core';\nimport { SENTRY_TRACE_STATE_DSC, SENTRY_TRACE_STATE_SAMPLED_NOT_RECORDING } from '../constants';\n\n/**\n * OpenTelemetry only knows about SAMPLED or NONE decision,\n * but for us it is important to differentiate between unset and unsampled.\n *\n * Both of these are identified as `traceFlags === TracegFlags.NONE`,\n * but we additionally look at a special trace state to differentiate between them.\n */\nexport function getSamplingDecision(spanContext: SpanContext): boolean | undefined {\n  const { traceFlags, traceState } = spanContext;\n\n  const sampledNotRecording = traceState ? traceState.get(SENTRY_TRACE_STATE_SAMPLED_NOT_RECORDING) === '1' : false;\n\n  // If trace flag is `SAMPLED`, we interpret this as sampled\n  // If it is `NONE`, it could mean either it was sampled to be not recorder, or that it was not sampled at all\n  // For us this is an important difference, sow e look at the SENTRY_TRACE_STATE_SAMPLED_NOT_RECORDING\n  // to identify which it is\n  if (traceFlags === TraceFlags.SAMPLED) {\n    return true;\n  }\n\n  if (sampledNotRecording) {\n    return false;\n  }\n\n  // Fall back to DSC as a last resort, that may also contain `sampled`...\n  const dscString = traceState ? traceState.get(SENTRY_TRACE_STATE_DSC) : undefined;\n  const dsc = dscString ? baggageHeaderToDynamicSamplingContext(dscString) : undefined;\n\n  if (dsc?.sampled === 'true') {\n    return true;\n  }\n  if (dsc?.sampled === 'false') {\n    return false;\n  }\n\n  return undefined;\n}\n", "import type { Attributes, AttributeValue } from '@opentelemetry/api';\nimport { SpanKind } from '@opentelemetry/api';\nimport {\n  ATTR_HTTP_REQUEST_METHOD,\n  ATTR_HTTP_ROUTE,\n  ATTR_URL_FULL,\n  SEMATTRS_DB_STATEMENT,\n  SEMATTRS_DB_SYSTEM,\n  SEMATTRS_FAAS_TRIGGER,\n  SEMATTRS_HTTP_METHOD,\n  SEMATTRS_HTTP_TARGET,\n  SEMATTRS_HTTP_URL,\n  SEMATTRS_MESSAGING_SYSTEM,\n  SEMATTRS_RPC_SERVICE,\n} from '@opentelemetry/semantic-conventions';\nimport type { SpanAttributes, TransactionSource } from '@sentry/core';\nimport {\n  getSanitizedUrlString,\n  parseUrl,\n  SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME,\n  SEMANTIC_ATTRIBUTE_SENTRY_OP,\n  SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,\n  SEMANTIC_ATTRIBUTE_SENTRY_SOURCE,\n  stripUrlQueryAndFragment,\n} from '@sentry/core';\nimport { SEMANTIC_ATTRIBUTE_SENTRY_GRAPHQL_OPERATION } from '../semanticAttributes';\nimport type { AbstractSpan } from '../types';\nimport { getSpanKind } from './getSpanKind';\nimport { spanHasAttributes, spanHasName } from './spanTypes';\n\ninterface SpanDescription {\n  op: string | undefined;\n  description: string;\n  source: TransactionSource;\n  data?: Record<string, string | undefined>;\n}\n\n/**\n * Infer the op & description for a set of name, attributes and kind of a span.\n */\nexport function inferSpanData(spanName: string, attributes: SpanAttributes, kind: SpanKind): SpanDescription {\n  // if http.method exists, this is an http request span\n  // eslint-disable-next-line deprecation/deprecation\n  const httpMethod = attributes[ATTR_HTTP_REQUEST_METHOD] || attributes[SEMATTRS_HTTP_METHOD];\n  if (httpMethod) {\n    return descriptionForHttpMethod({ attributes, name: spanName, kind }, httpMethod);\n  }\n\n  // eslint-disable-next-line deprecation/deprecation\n  const dbSystem = attributes[SEMATTRS_DB_SYSTEM];\n  const opIsCache =\n    typeof attributes[SEMANTIC_ATTRIBUTE_SENTRY_OP] === 'string' &&\n    attributes[SEMANTIC_ATTRIBUTE_SENTRY_OP].startsWith('cache.');\n\n  // If db.type exists then this is a database call span\n  // If the Redis DB is used as a cache, the span description should not be changed\n  if (dbSystem && !opIsCache) {\n    return descriptionForDbSystem({ attributes, name: spanName });\n  }\n\n  const customSourceOrRoute = attributes[SEMANTIC_ATTRIBUTE_SENTRY_SOURCE] === 'custom' ? 'custom' : 'route';\n\n  // If rpc.service exists then this is a rpc call span.\n  // eslint-disable-next-line deprecation/deprecation\n  const rpcService = attributes[SEMATTRS_RPC_SERVICE];\n  if (rpcService) {\n    return {\n      ...getUserUpdatedNameAndSource(spanName, attributes, 'route'),\n      op: 'rpc',\n    };\n  }\n\n  // If messaging.system exists then this is a messaging system span.\n  // eslint-disable-next-line deprecation/deprecation\n  const messagingSystem = attributes[SEMATTRS_MESSAGING_SYSTEM];\n  if (messagingSystem) {\n    return {\n      ...getUserUpdatedNameAndSource(spanName, attributes, customSourceOrRoute),\n      op: 'message',\n    };\n  }\n\n  // If faas.trigger exists then this is a function as a service span.\n  // eslint-disable-next-line deprecation/deprecation\n  const faasTrigger = attributes[SEMATTRS_FAAS_TRIGGER];\n  if (faasTrigger) {\n    return {\n      ...getUserUpdatedNameAndSource(spanName, attributes, customSourceOrRoute),\n      op: faasTrigger.toString(),\n    };\n  }\n\n  return { op: undefined, description: spanName, source: 'custom' };\n}\n\n/**\n * Extract better op/description from an otel span.\n *\n * Does not overwrite the span name if the source is already set to custom to ensure\n * that user-updated span names are preserved. In this case, we only adjust the op but\n * leave span description and source unchanged.\n *\n * Based on https://github.com/open-telemetry/opentelemetry-collector-contrib/blob/7422ce2a06337f68a59b552b8c5a2ac125d6bae5/exporter/sentryexporter/sentry_exporter.go#L306\n */\nexport function parseSpanDescription(span: AbstractSpan): SpanDescription {\n  const attributes = spanHasAttributes(span) ? span.attributes : {};\n  const name = spanHasName(span) ? span.name : '<unknown>';\n  const kind = getSpanKind(span);\n\n  return inferSpanData(name, attributes, kind);\n}\n\nfunction descriptionForDbSystem({ attributes, name }: { attributes: Attributes; name: string }): SpanDescription {\n  // if we already have a custom name, we don't overwrite it but only set the op\n  const userDefinedName = attributes[SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME];\n  if (typeof userDefinedName === 'string') {\n    return {\n      op: 'db',\n      description: userDefinedName,\n      source: (attributes[SEMANTIC_ATTRIBUTE_SENTRY_SOURCE] as TransactionSource) || 'custom',\n    };\n  }\n\n  // if we already have the source set to custom, we don't overwrite the span description but only set the op\n  if (attributes[SEMANTIC_ATTRIBUTE_SENTRY_SOURCE] === 'custom') {\n    return { op: 'db', description: name, source: 'custom' };\n  }\n\n  // Use DB statement (Ex \"SELECT * FROM table\") if possible as description.\n  // eslint-disable-next-line deprecation/deprecation\n  const statement = attributes[SEMATTRS_DB_STATEMENT];\n\n  const description = statement ? statement.toString() : name;\n\n  return { op: 'db', description, source: 'task' };\n}\n\n/** Only exported for tests. */\nexport function descriptionForHttpMethod(\n  { name, kind, attributes }: { name: string; attributes: Attributes; kind: SpanKind },\n  httpMethod: AttributeValue,\n): SpanDescription {\n  const opParts = ['http'];\n\n  switch (kind) {\n    case SpanKind.CLIENT:\n      opParts.push('client');\n      break;\n    case SpanKind.SERVER:\n      opParts.push('server');\n      break;\n  }\n\n  // Spans for HTTP requests we have determined to be prefetch requests will have a `.prefetch` postfix in the op\n  if (attributes['sentry.http.prefetch']) {\n    opParts.push('prefetch');\n  }\n\n  const { urlPath, url, query, fragment, hasRoute } = getSanitizedUrl(attributes, kind);\n\n  if (!urlPath) {\n    return { ...getUserUpdatedNameAndSource(name, attributes), op: opParts.join('.') };\n  }\n\n  const graphqlOperationsAttribute = attributes[SEMANTIC_ATTRIBUTE_SENTRY_GRAPHQL_OPERATION];\n\n  // Ex. GET /api/users\n  const baseDescription = `${httpMethod} ${urlPath}`;\n\n  // When the http span has a graphql operation, append it to the description\n  // We add these in the graphqlIntegration\n  const inferredDescription = graphqlOperationsAttribute\n    ? `${baseDescription} (${getGraphqlOperationNamesFromAttribute(graphqlOperationsAttribute)})`\n    : baseDescription;\n\n  // If `httpPath` is a root path, then we can categorize the transaction source as route.\n  const inferredSource: TransactionSource = hasRoute || urlPath === '/' ? 'route' : 'url';\n\n  const data: Record<string, string> = {};\n\n  if (url) {\n    data.url = url;\n  }\n  if (query) {\n    data['http.query'] = query;\n  }\n  if (fragment) {\n    data['http.fragment'] = fragment;\n  }\n\n  // If the span kind is neither client nor server, we use the original name\n  // this infers that somebody manually started this span, in which case we don't want to overwrite the name\n  const isClientOrServerKind = kind === SpanKind.CLIENT || kind === SpanKind.SERVER;\n\n  // If the span is an auto-span (=it comes from one of our instrumentations),\n  // we always want to infer the name\n  // this is necessary because some of the auto-instrumentation we use uses kind=INTERNAL\n  const origin = attributes[SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN] || 'manual';\n  const isManualSpan = !`${origin}`.startsWith('auto');\n\n  // If users (or in very rare occasions we) set the source to custom, we don't overwrite the name\n  const alreadyHasCustomSource = attributes[SEMANTIC_ATTRIBUTE_SENTRY_SOURCE] === 'custom';\n  const customSpanName = attributes[SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME];\n\n  const useInferredDescription =\n    !alreadyHasCustomSource && customSpanName == null && (isClientOrServerKind || !isManualSpan);\n\n  const { description, source } = useInferredDescription\n    ? { description: inferredDescription, source: inferredSource }\n    : getUserUpdatedNameAndSource(name, attributes);\n\n  return {\n    op: opParts.join('.'),\n    description,\n    source,\n    data,\n  };\n}\n\nfunction getGraphqlOperationNamesFromAttribute(attr: AttributeValue): string {\n  if (Array.isArray(attr)) {\n    const sorted = attr.slice().sort();\n\n    // Up to 5 items, we just add all of them\n    if (sorted.length <= 5) {\n      return sorted.join(', ');\n    } else {\n      // Else, we add the first 5 and the diff of other operations\n      return `${sorted.slice(0, 5).join(', ')}, +${sorted.length - 5}`;\n    }\n  }\n\n  return `${attr}`;\n}\n\n/** Exported for tests only */\nexport function getSanitizedUrl(\n  attributes: Attributes,\n  kind: SpanKind,\n): {\n  url: string | undefined;\n  urlPath: string | undefined;\n  query: string | undefined;\n  fragment: string | undefined;\n  hasRoute: boolean;\n} {\n  // This is the relative path of the URL, e.g. /sub\n  // eslint-disable-next-line deprecation/deprecation\n  const httpTarget = attributes[SEMATTRS_HTTP_TARGET];\n  // This is the full URL, including host & query params etc., e.g. https://example.com/sub?foo=bar\n  // eslint-disable-next-line deprecation/deprecation\n  const httpUrl = attributes[SEMATTRS_HTTP_URL] || attributes[ATTR_URL_FULL];\n  // This is the normalized route name - may not always be available!\n  const httpRoute = attributes[ATTR_HTTP_ROUTE];\n\n  const parsedUrl = typeof httpUrl === 'string' ? parseUrl(httpUrl) : undefined;\n  const url = parsedUrl ? getSanitizedUrlString(parsedUrl) : undefined;\n  const query = parsedUrl?.search || undefined;\n  const fragment = parsedUrl?.hash || undefined;\n\n  if (typeof httpRoute === 'string') {\n    return { urlPath: httpRoute, url, query, fragment, hasRoute: true };\n  }\n\n  if (kind === SpanKind.SERVER && typeof httpTarget === 'string') {\n    return { urlPath: stripUrlQueryAndFragment(httpTarget), url, query, fragment, hasRoute: false };\n  }\n\n  if (parsedUrl) {\n    return { urlPath: url, url, query, fragment, hasRoute: false };\n  }\n\n  // fall back to target even for client spans, if no URL is present\n  if (typeof httpTarget === 'string') {\n    return { urlPath: stripUrlQueryAndFragment(httpTarget), url, query, fragment, hasRoute: false };\n  }\n\n  return { urlPath: undefined, url, query, fragment, hasRoute: false };\n}\n\n/**\n * Because Otel instrumentation sometimes mutates span names via `span.updateName`, the only way\n * to ensure that a user-set span name is preserved is to store it as a tmp attribute on the span.\n * We delete this attribute once we're done with it when preparing the event envelope.\n *\n * This temp attribute always takes precedence over the original name.\n *\n * We also need to take care of setting the correct source. Users can always update the source\n * after updating the name, so we need to respect that.\n *\n * @internal exported only for testing\n */\nexport function getUserUpdatedNameAndSource(\n  originalName: string,\n  attributes: Attributes,\n  fallbackSource: TransactionSource = 'custom',\n): {\n  description: string;\n  source: TransactionSource;\n} {\n  const source = (attributes[SEMANTIC_ATTRIBUTE_SENTRY_SOURCE] as TransactionSource) || fallbackSource;\n  const description = attributes[SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME];\n\n  if (description && typeof description === 'string') {\n    return {\n      description,\n      source,\n    };\n  }\n\n  return { description: originalName, source };\n}\n", "import type { Client } from '@sentry/core';\nimport { hasSpansEnabled, SEMANTIC_ATTRIBUTE_SENTRY_SOURCE, spanToJSON } from '@sentry/core';\nimport { getSamplingDecision } from './getSamplingDecision';\nimport { parseSpanDescription } from './parseSpanDescription';\nimport { spanHasName } from './spanTypes';\n\n/**\n * Setup a DSC handler on the passed client,\n * ensuring that the transaction name is inferred from the span correctly.\n */\nexport function enhanceDscWithOpenTelemetryRootSpanName(client: Client): void {\n  client.on('createDsc', (dsc, rootSpan) => {\n    if (!rootSpan) {\n      return;\n    }\n\n    // We want to overwrite the transaction on the DSC that is created by default in core\n    // The reason for this is that we want to infer the span name, not use the initial one\n    // Otherwise, we'll get names like \"GET\" instead of e.g. \"GET /foo\"\n    // `parseSpanDescription` takes the attributes of the span into account for the name\n    // This mutates the passed-in DSC\n\n    const jsonSpan = spanToJSON(rootSpan);\n    const attributes = jsonSpan.data;\n    const source = attributes[SEMANTIC_ATTRIBUTE_SENTRY_SOURCE];\n\n    const { description } = spanHasName(rootSpan) ? parseSpanDescription(rootSpan) : { description: undefined };\n    if (source !== 'url' && description) {\n      dsc.transaction = description;\n    }\n\n    // Also ensure sampling decision is correctly inferred\n    // In core, we use `spanIsSampled`, which just looks at the trace flags\n    // but in OTEL, we use a slightly more complex logic to be able to differntiate between unsampled and deferred sampling\n    if (hasSpansEnabled()) {\n      const sampled = getSamplingDecision(rootSpan.spanContext());\n      dsc.sampled = sampled == undefined ? undefined : String(sampled);\n    }\n  });\n}\n", "import type { Span } from '@opentelemetry/api';\nimport { trace } from '@opentelemetry/api';\n\n/**\n * Returns the currently active span.\n */\nexport function getActiveSpan(): Span | undefined {\n  return trace.getActiveSpan();\n}\n", "declare const __DEBUG_BUILD__: boolean;\n\n/**\n * This serves as a build time flag that will be true by default, but false in non-debug builds or if users replace `__SENTRY_DEBUG__` in their generated code.\n *\n * ATTENTION: This constant must never cross package boundaries (i.e. be exported) to guarantee that it can be used for tree shaking.\n */\nexport const DEBUG_BUILD = __DEBUG_BUILD__;\n", "import { TraceState } from '@opentelemetry/core';\nimport type { DynamicSamplingContext } from '@sentry/core';\nimport { dynamicSamplingContextToSentryBaggageHeader } from '@sentry/core';\nimport { SENTRY_TRACE_STATE_DSC, SENTRY_TRACE_STATE_SAMPLED_NOT_RECORDING } from '../constants';\n\n/**\n * Generate a TraceState for the given data.\n */\nexport function makeTraceState({\n  dsc,\n  sampled,\n}: {\n  dsc?: Partial<DynamicSamplingContext>;\n  sampled?: boolean;\n}): TraceState {\n  // We store the DSC as OTEL trace state on the span context\n  const dscString = dsc ? dynamicSamplingContextToSentryBaggageHeader(dsc) : undefined;\n\n  const traceStateBase = new TraceState();\n\n  const traceStateWithDsc = dscString ? traceStateBase.set(SENTRY_TRACE_STATE_DSC, dscString) : traceStateBase;\n\n  // We also specifically want to store if this is sampled to be not recording,\n  // or unsampled (=could be either sampled or not)\n  return sampled === false ? traceStateWithDsc.set(SENTRY_TRACE_STATE_SAMPLED_NOT_RECORDING, '1') : traceStateWithDsc;\n}\n", "type OpenTelemetryElement = 'SentrySpanProcessor' | 'SentryContextManager' | 'SentryPropagator' | 'SentrySampler';\n\nconst setupElements = new Set<OpenTelemetryElement>();\n\n/** Get all the OpenTelemetry elements that have been set up. */\nexport function openTelemetrySetupCheck(): OpenTelemetryElement[] {\n  return Array.from(setupElements);\n}\n\n/** Mark an OpenTelemetry element as setup. */\nexport function setIsSetup(element: OpenTelemetryElement): void {\n  setupElements.add(element);\n}\n\n/** Only exported for tests. */\nexport function clearOpenTelemetrySetupCheck(): void {\n  setupElements.clear();\n}\n", "import type { Baggage, Context, Span, SpanContext, TextMapGetter, TextMapSetter } from '@opentelemetry/api';\nimport { context, INVALID_TRACEID, propagation, trace, TraceFlags } from '@opentelemetry/api';\nimport { isTracingSuppressed, W3CBaggagePropagator } from '@opentelemetry/core';\nimport { ATTR_URL_FULL, SEMATTRS_HTTP_URL } from '@opentelemetry/semantic-conventions';\nimport type { Client, continueTrace, DynamicSamplingContext, Options, Scope } from '@sentry/core';\nimport {\n  debug,\n  generateSentryTraceHeader,\n  getClient,\n  getCurrentScope,\n  getDynamicSamplingContextFromScope,\n  getDynamicSamplingContextFromSpan,\n  getIsolationScope,\n  LRUMap,\n  parseBaggageHeader,\n  propagationContextFromHeaders,\n  SENTRY_BAGGAGE_KEY_PREFIX,\n  spanToJSON,\n  stringMatchesSomePattern,\n} from '@sentry/core';\nimport { SENTRY_BAGGAGE_HEADER, SENTRY_TRACE_HEADER, SENTRY_TRACE_STATE_URL } from './constants';\nimport { DEBUG_BUILD } from './debug-build';\nimport { getScopesFromContext, setScopesOnContext } from './utils/contextData';\nimport { getSamplingDecision } from './utils/getSamplingDecision';\nimport { makeTraceState } from './utils/makeTraceState';\nimport { setIsSetup } from './utils/setupCheck';\n\n/**\n * Injects and extracts `sentry-trace` and `baggage` headers from carriers.\n */\nexport class SentryPropagator extends W3CBaggagePropagator {\n  /** A map of URLs that have already been checked for if they match tracePropagationTargets. */\n  private _urlMatchesTargetsMap: LRUMap<string, boolean>;\n\n  public constructor() {\n    super();\n    setIsSetup('SentryPropagator');\n\n    // We're caching results so we don't have to recompute regexp every time we create a request.\n    this._urlMatchesTargetsMap = new LRUMap<string, boolean>(100);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public inject(context: Context, carrier: unknown, setter: TextMapSetter): void {\n    if (isTracingSuppressed(context)) {\n      DEBUG_BUILD && debug.log('[Tracing] Not injecting trace data for url because tracing is suppressed.');\n      return;\n    }\n\n    const activeSpan = trace.getSpan(context);\n    const url = activeSpan && getCurrentURL(activeSpan);\n\n    const tracePropagationTargets = getClient()?.getOptions()?.tracePropagationTargets;\n    if (!shouldPropagateTraceForUrl(url, tracePropagationTargets, this._urlMatchesTargetsMap)) {\n      DEBUG_BUILD &&\n        debug.log('[Tracing] Not injecting trace data for url because it does not match tracePropagationTargets:', url);\n      return;\n    }\n\n    const existingBaggageHeader = getExistingBaggage(carrier);\n    let baggage = propagation.getBaggage(context) || propagation.createBaggage({});\n\n    const { dynamicSamplingContext, traceId, spanId, sampled } = getInjectionData(context);\n\n    if (existingBaggageHeader) {\n      const baggageEntries = parseBaggageHeader(existingBaggageHeader);\n\n      if (baggageEntries) {\n        Object.entries(baggageEntries).forEach(([key, value]) => {\n          baggage = baggage.setEntry(key, { value });\n        });\n      }\n    }\n\n    if (dynamicSamplingContext) {\n      baggage = Object.entries(dynamicSamplingContext).reduce<Baggage>((b, [dscKey, dscValue]) => {\n        if (dscValue) {\n          return b.setEntry(`${SENTRY_BAGGAGE_KEY_PREFIX}${dscKey}`, { value: dscValue });\n        }\n        return b;\n      }, baggage);\n    }\n\n    // We also want to avoid setting the default OTEL trace ID, if we get that for whatever reason\n    if (traceId && traceId !== INVALID_TRACEID) {\n      setter.set(carrier, SENTRY_TRACE_HEADER, generateSentryTraceHeader(traceId, spanId, sampled));\n    }\n\n    super.inject(propagation.setBaggage(context, baggage), carrier, setter);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public extract(context: Context, carrier: unknown, getter: TextMapGetter): Context {\n    const maybeSentryTraceHeader: string | string[] | undefined = getter.get(carrier, SENTRY_TRACE_HEADER);\n    const baggage = getter.get(carrier, SENTRY_BAGGAGE_HEADER);\n\n    const sentryTrace = maybeSentryTraceHeader\n      ? Array.isArray(maybeSentryTraceHeader)\n        ? maybeSentryTraceHeader[0]\n        : maybeSentryTraceHeader\n      : undefined;\n\n    // Add remote parent span context\n    // If there is no incoming trace, this will return the context as-is\n    return ensureScopesOnContext(getContextWithRemoteActiveSpan(context, { sentryTrace, baggage }));\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public fields(): string[] {\n    return [SENTRY_TRACE_HEADER, SENTRY_BAGGAGE_HEADER];\n  }\n}\n\nconst NOT_PROPAGATED_MESSAGE =\n  '[Tracing] Not injecting trace data for url because it does not match tracePropagationTargets:';\n\n/**\n * Check if a given URL should be propagated to or not.\n * If no url is defined, or no trace propagation targets are defined, this will always return `true`.\n * You can also optionally provide a decision map, to cache decisions and avoid repeated regex lookups.\n */\nexport function shouldPropagateTraceForUrl(\n  url: string | undefined,\n  tracePropagationTargets: Options['tracePropagationTargets'],\n  decisionMap?: LRUMap<string, boolean>,\n): boolean {\n  if (typeof url !== 'string' || !tracePropagationTargets) {\n    return true;\n  }\n\n  const cachedDecision = decisionMap?.get(url);\n  if (cachedDecision !== undefined) {\n    DEBUG_BUILD && !cachedDecision && debug.log(NOT_PROPAGATED_MESSAGE, url);\n    return cachedDecision;\n  }\n\n  const decision = stringMatchesSomePattern(url, tracePropagationTargets);\n  decisionMap?.set(url, decision);\n\n  DEBUG_BUILD && !decision && debug.log(NOT_PROPAGATED_MESSAGE, url);\n  return decision;\n}\n\n/**\n * Get propagation injection data for the given context.\n * The additional options can be passed to override the scope and client that is otherwise derived from the context.\n */\nexport function getInjectionData(\n  context: Context,\n  options: { scope?: Scope; client?: Client } = {},\n): {\n  dynamicSamplingContext: Partial<DynamicSamplingContext> | undefined;\n  traceId: string | undefined;\n  spanId: string | undefined;\n  sampled: boolean | undefined;\n} {\n  const span = trace.getSpan(context);\n\n  // If we have a remote span, the spanId should be considered as the parentSpanId, not spanId itself\n  // Instead, we use a virtual (generated) spanId for propagation\n  if (span?.spanContext().isRemote) {\n    const spanContext = span.spanContext();\n    const dynamicSamplingContext = getDynamicSamplingContextFromSpan(span);\n\n    return {\n      dynamicSamplingContext,\n      traceId: spanContext.traceId,\n      spanId: undefined,\n      sampled: getSamplingDecision(spanContext), // TODO: Do we need to change something here?\n    };\n  }\n\n  // If we have a local span, we just use this\n  if (span) {\n    const spanContext = span.spanContext();\n    const dynamicSamplingContext = getDynamicSamplingContextFromSpan(span);\n\n    return {\n      dynamicSamplingContext,\n      traceId: spanContext.traceId,\n      spanId: spanContext.spanId,\n      sampled: getSamplingDecision(spanContext), // TODO: Do we need to change something here?\n    };\n  }\n\n  // Else we try to use the propagation context from the scope\n  // The only scenario where this should happen is when we neither have a span, nor an incoming trace\n  const scope = options.scope || getScopesFromContext(context)?.scope || getCurrentScope();\n  const client = options.client || getClient();\n\n  const propagationContext = scope.getPropagationContext();\n  const dynamicSamplingContext = client ? getDynamicSamplingContextFromScope(client, scope) : undefined;\n  return {\n    dynamicSamplingContext,\n    traceId: propagationContext.traceId,\n    spanId: propagationContext.propagationSpanId,\n    sampled: propagationContext.sampled,\n  };\n}\n\nfunction getContextWithRemoteActiveSpan(\n  ctx: Context,\n  { sentryTrace, baggage }: Parameters<typeof continueTrace>[0],\n): Context {\n  const propagationContext = propagationContextFromHeaders(sentryTrace, baggage);\n\n  const { traceId, parentSpanId, sampled, dsc } = propagationContext;\n\n  // We only want to set the virtual span if we are continuing a concrete trace\n  // Otherwise, we ignore the incoming trace here, e.g. if we have no trace headers\n  if (!parentSpanId) {\n    return ctx;\n  }\n\n  const spanContext = generateRemoteSpanContext({\n    traceId,\n    spanId: parentSpanId,\n    sampled,\n    dsc,\n  });\n\n  return trace.setSpanContext(ctx, spanContext);\n}\n\n/**\n * Takes trace strings and propagates them as a remote active span.\n * This should be used in addition to `continueTrace` in OTEL-powered environments.\n */\nexport function continueTraceAsRemoteSpan<T>(\n  ctx: Context,\n  options: Parameters<typeof continueTrace>[0],\n  callback: () => T,\n): T {\n  const ctxWithSpanContext = ensureScopesOnContext(getContextWithRemoteActiveSpan(ctx, options));\n\n  return context.with(ctxWithSpanContext, callback);\n}\n\nfunction ensureScopesOnContext(ctx: Context): Context {\n  // If there are no scopes yet on the context, ensure we have them\n  const scopes = getScopesFromContext(ctx);\n  const newScopes = {\n    // If we have no scope here, this is most likely either the root context or a context manually derived from it\n    // In this case, we want to fork the current scope, to ensure we do not pollute the root scope\n    scope: scopes ? scopes.scope : getCurrentScope().clone(),\n    isolationScope: scopes ? scopes.isolationScope : getIsolationScope(),\n  };\n\n  return setScopesOnContext(ctx, newScopes);\n}\n\n/** Try to get the existing baggage header so we can merge this in. */\nfunction getExistingBaggage(carrier: unknown): string | undefined {\n  try {\n    const baggage = (carrier as Record<string, string | string[]>)[SENTRY_BAGGAGE_HEADER];\n    return Array.isArray(baggage) ? baggage.join(',') : baggage;\n  } catch {\n    return undefined;\n  }\n}\n\n/**\n * It is pretty tricky to get access to the outgoing request URL of a request in the propagator.\n * As we only have access to the context of the span to be sent and the carrier (=headers),\n * but the span may be unsampled and thus have no attributes.\n *\n * So we use the following logic:\n * 1. If we have an active span, we check if it has a URL attribute.\n * 2. Else, if the active span has no URL attribute (e.g. it is unsampled), we check a special trace state (which we set in our sampler).\n */\nfunction getCurrentURL(span: Span): string | undefined {\n  const spanData = spanToJSON(span).data;\n  // `ATTR_URL_FULL` is the new attribute, but we still support the old one, `SEMATTRS_HTTP_URL`, for now.\n  // eslint-disable-next-line deprecation/deprecation\n  const urlAttribute = spanData[SEMATTRS_HTTP_URL] || spanData[ATTR_URL_FULL];\n  if (typeof urlAttribute === 'string') {\n    return urlAttribute;\n  }\n\n  // Also look at the traceState, which we may set in the sampler even for unsampled spans\n  const urlTraceState = span.spanContext().traceState?.get(SENTRY_TRACE_STATE_URL);\n  if (urlTraceState) {\n    return urlTraceState;\n  }\n\n  return undefined;\n}\n\nfunction generateRemoteSpanContext({\n  spanId,\n  traceId,\n  sampled,\n  dsc,\n}: {\n  spanId: string;\n  traceId: string;\n  sampled: boolean | undefined;\n  dsc?: Partial<DynamicSamplingContext>;\n}): SpanContext {\n  // We store the DSC as OTEL trace state on the span context\n  const traceState = makeTraceState({\n    dsc,\n    sampled,\n  });\n\n  const spanContext: SpanContext = {\n    traceId,\n    spanId,\n    isRemote: true,\n    traceFlags: sampled ? TraceFlags.SAMPLED : TraceFlags.NONE,\n    traceState,\n  };\n\n  return spanContext;\n}\n", "import type { Con<PERSON>, Span, SpanContext, SpanO<PERSON><PERSON>, Tracer } from '@opentelemetry/api';\nimport { context, SpanStatusCode, trace, TraceFlags } from '@opentelemetry/api';\nimport { suppressTracing } from '@opentelemetry/core';\nimport type {\n  Client,\n  continueTrace as baseContinueTrace,\n  DynamicSamplingContext,\n  Scope,\n  Span as SentrySpan,\n  TraceContext,\n} from '@sentry/core';\nimport {\n  getClient,\n  getCurrentScope,\n  getDynamicSamplingContextFromScope,\n  getDynamicSamplingContextFromSpan,\n  getRootSpan,\n  getTraceContextFromScope,\n  handleCallbackErrors,\n  SDK_VERSION,\n  SEMANTIC_ATTRIBUTE_SENTRY_OP,\n  spanToJSON,\n  spanToTraceContext,\n} from '@sentry/core';\nimport { continueTraceAsRemoteSpan } from './propagator';\nimport type { OpenTelemetryClient, OpenTelemetrySpanContext } from './types';\nimport { getContextFromScope } from './utils/contextData';\nimport { getSamplingDecision } from './utils/getSamplingDecision';\nimport { makeTraceState } from './utils/makeTraceState';\n\n/**\n * Wraps a function with a transaction/span and finishes the span after the function is done.\n * The created span is the active span and will be used as parent by other spans created inside the function\n * and can be accessed via `Sentry.getActiveSpan()`, as long as the function is executed while the scope is active.\n *\n * If you want to create a span that is not set as active, use {@link startInactiveSpan}.\n *\n * You'll always get a span passed to the callback,\n * it may just be a non-recording span if the span is not sampled or if tracing is disabled.\n */\nexport function startSpan<T>(options: OpenTelemetrySpanContext, callback: (span: Span) => T): T {\n  const tracer = getTracer();\n\n  const { name, parentSpan: customParentSpan } = options;\n\n  // If `options.parentSpan` is defined, we want to wrap the callback in `withActiveSpan`\n  const wrapper = getActiveSpanWrapper<T>(customParentSpan);\n\n  return wrapper(() => {\n    const activeCtx = getContext(options.scope, options.forceTransaction);\n    const shouldSkipSpan = options.onlyIfParent && !trace.getSpan(activeCtx);\n    const ctx = shouldSkipSpan ? suppressTracing(activeCtx) : activeCtx;\n\n    const spanOptions = getSpanOptions(options);\n\n    return tracer.startActiveSpan(name, spanOptions, ctx, span => {\n      return handleCallbackErrors(\n        () => callback(span),\n        () => {\n          // Only set the span status to ERROR when there wasn't any status set before, in order to avoid stomping useful span statuses\n          if (spanToJSON(span).status === undefined) {\n            span.setStatus({ code: SpanStatusCode.ERROR });\n          }\n        },\n        () => span.end(),\n      );\n    });\n  });\n}\n\n/**\n * Similar to `Sentry.startSpan`. Wraps a function with a span, but does not finish the span\n * after the function is done automatically. You'll have to call `span.end()` manually.\n *\n * The created span is the active span and will be used as parent by other spans created inside the function\n * and can be accessed via `Sentry.getActiveSpan()`, as long as the function is executed while the scope is active.\n *\n * You'll always get a span passed to the callback,\n * it may just be a non-recording span if the span is not sampled or if tracing is disabled.\n */\nexport function startSpanManual<T>(\n  options: OpenTelemetrySpanContext,\n  callback: (span: Span, finish: () => void) => T,\n): T {\n  const tracer = getTracer();\n\n  const { name, parentSpan: customParentSpan } = options;\n\n  // If `options.parentSpan` is defined, we want to wrap the callback in `withActiveSpan`\n  const wrapper = getActiveSpanWrapper<T>(customParentSpan);\n\n  return wrapper(() => {\n    const activeCtx = getContext(options.scope, options.forceTransaction);\n    const shouldSkipSpan = options.onlyIfParent && !trace.getSpan(activeCtx);\n    const ctx = shouldSkipSpan ? suppressTracing(activeCtx) : activeCtx;\n\n    const spanOptions = getSpanOptions(options);\n\n    return tracer.startActiveSpan(name, spanOptions, ctx, span => {\n      return handleCallbackErrors(\n        () => callback(span, () => span.end()),\n        () => {\n          // Only set the span status to ERROR when there wasn't any status set before, in order to avoid stomping useful span statuses\n          if (spanToJSON(span).status === undefined) {\n            span.setStatus({ code: SpanStatusCode.ERROR });\n          }\n        },\n      );\n    });\n  });\n}\n\n/**\n * Creates a span. This span is not set as active, so will not get automatic instrumentation spans\n * as children or be able to be accessed via `Sentry.getActiveSpan()`.\n *\n * If you want to create a span that is set as active, use {@link startSpan}.\n *\n * This function will always return a span,\n * it may just be a non-recording span if the span is not sampled or if tracing is disabled.\n */\nexport function startInactiveSpan(options: OpenTelemetrySpanContext): Span {\n  const tracer = getTracer();\n\n  const { name, parentSpan: customParentSpan } = options;\n\n  // If `options.parentSpan` is defined, we want to wrap the callback in `withActiveSpan`\n  const wrapper = getActiveSpanWrapper<Span>(customParentSpan);\n\n  return wrapper(() => {\n    const activeCtx = getContext(options.scope, options.forceTransaction);\n    const shouldSkipSpan = options.onlyIfParent && !trace.getSpan(activeCtx);\n    const ctx = shouldSkipSpan ? suppressTracing(activeCtx) : activeCtx;\n\n    const spanOptions = getSpanOptions(options);\n\n    const span = tracer.startSpan(name, spanOptions, ctx);\n\n    return span;\n  });\n}\n\n/**\n * Forks the current scope and sets the provided span as active span in the context of the provided callback. Can be\n * passed `null` to start an entirely new span tree.\n *\n * @param span Spans started in the context of the provided callback will be children of this span. If `null` is passed,\n * spans started within the callback will be root spans.\n * @param callback Execution context in which the provided span will be active. Is passed the newly forked scope.\n * @returns the value returned from the provided callback function.\n */\nexport function withActiveSpan<T>(span: Span | null, callback: (scope: Scope) => T): T {\n  const newContextWithActiveSpan = span ? trace.setSpan(context.active(), span) : trace.deleteSpan(context.active());\n  return context.with(newContextWithActiveSpan, () => callback(getCurrentScope()));\n}\n\nfunction getTracer(): Tracer {\n  const client = getClient<Client & OpenTelemetryClient>();\n  return client?.tracer || trace.getTracer('@sentry/opentelemetry', SDK_VERSION);\n}\n\nfunction getSpanOptions(options: OpenTelemetrySpanContext): SpanOptions {\n  const { startTime, attributes, kind, op, links } = options;\n\n  // OTEL expects timestamps in ms, not seconds\n  const fixedStartTime = typeof startTime === 'number' ? ensureTimestampInMilliseconds(startTime) : startTime;\n\n  return {\n    attributes: op\n      ? {\n          [SEMANTIC_ATTRIBUTE_SENTRY_OP]: op,\n          ...attributes,\n        }\n      : attributes,\n    kind,\n    links,\n    startTime: fixedStartTime,\n  };\n}\n\nfunction ensureTimestampInMilliseconds(timestamp: number): number {\n  const isMs = timestamp < 9999999999;\n  return isMs ? timestamp * 1000 : timestamp;\n}\n\nfunction getContext(scope: Scope | undefined, forceTransaction: boolean | undefined): Context {\n  const ctx = getContextForScope(scope);\n  const parentSpan = trace.getSpan(ctx);\n\n  // In the case that we have no parent span, we start a new trace\n  // Note that if we continue a trace, we'll always have a remote parent span here anyhow\n  if (!parentSpan) {\n    return ctx;\n  }\n\n  // If we don't want to force a transaction, and we have a parent span, all good, we just return as-is!\n  if (!forceTransaction) {\n    return ctx;\n  }\n\n  // Else, if we do have a parent span but want to force a transaction, we have to simulate a \"root\" context\n\n  // Else, we need to do two things:\n  // 1. Unset the parent span from the context, so we'll create a new root span\n  // 2. Ensure the propagation context is correct, so we'll continue from the parent span\n  const ctxWithoutSpan = trace.deleteSpan(ctx);\n\n  const { spanId, traceId } = parentSpan.spanContext();\n  const sampled = getSamplingDecision(parentSpan.spanContext());\n\n  // In this case, when we are forcing a transaction, we want to treat this like continuing an incoming trace\n  // so we set the traceState according to the root span\n  const rootSpan = getRootSpan(parentSpan);\n  const dsc = getDynamicSamplingContextFromSpan(rootSpan);\n\n  const traceState = makeTraceState({\n    dsc,\n    sampled,\n  });\n\n  const spanOptions: SpanContext = {\n    traceId,\n    spanId,\n    isRemote: true,\n    traceFlags: sampled ? TraceFlags.SAMPLED : TraceFlags.NONE,\n    traceState,\n  };\n\n  const ctxWithSpanContext = trace.setSpanContext(ctxWithoutSpan, spanOptions);\n\n  return ctxWithSpanContext;\n}\n\nfunction getContextForScope(scope?: Scope): Context {\n  if (scope) {\n    const ctx = getContextFromScope(scope);\n    if (ctx) {\n      return ctx;\n    }\n  }\n\n  return context.active();\n}\n\n/**\n * Continue a trace from `sentry-trace` and `baggage` values.\n * These values can be obtained from incoming request headers, or in the browser from `<meta name=\"sentry-trace\">`\n * and `<meta name=\"baggage\">` HTML tags.\n *\n * Spans started with `startSpan`, `startSpanManual` and `startInactiveSpan`, within the callback will automatically\n * be attached to the incoming trace.\n *\n * This is a custom version of `continueTrace` that is used in OTEL-powered environments.\n * It propagates the trace as a remote span, in addition to setting it on the propagation context.\n */\nexport function continueTrace<T>(options: Parameters<typeof baseContinueTrace>[0], callback: () => T): T {\n  return continueTraceAsRemoteSpan(context.active(), options, callback);\n}\n\n/**\n * Get the trace context for a given scope.\n * We have a custom implemention here because we need an OTEL-specific way to get the span from a scope.\n */\nexport function getTraceContextForScope(\n  client: Client,\n  scope: Scope,\n): [dynamicSamplingContext: Partial<DynamicSamplingContext>, traceContext: TraceContext] {\n  const ctx = getContextFromScope(scope);\n  const span = ctx && trace.getSpan(ctx);\n\n  const traceContext = span ? spanToTraceContext(span) : getTraceContextFromScope(scope);\n\n  const dynamicSamplingContext = span\n    ? getDynamicSamplingContextFromSpan(span)\n    : getDynamicSamplingContextFromScope(client, scope);\n  return [dynamicSamplingContext, traceContext];\n}\n\nfunction getActiveSpanWrapper<T>(parentSpan: Span | SentrySpan | undefined | null): (callback: () => T) => T {\n  return parentSpan !== undefined\n    ? (callback: () => T) => {\n        return withActiveSpan(parentSpan, callback);\n      }\n    : (callback: () => T) => callback();\n}\n", "import { context } from '@opentelemetry/api';\nimport { suppressTracing as suppressTracingImpl } from '@opentelemetry/core';\n\n/** Suppress tracing in the given callback, ensuring no spans are generated inside of it. */\nexport function suppressTracing<T>(callback: () => T): T {\n  const ctx = suppressTracingImpl(context.active());\n  return context.with(ctx, callback);\n}\n", "import type { Client } from '@sentry/core';\nimport { getDynamicSamplingContextFromSpan, getRootSpan, spanToTraceContext } from '@sentry/core';\nimport { getActiveSpan } from './utils/getActiveSpan';\n\n/** Ensure the `trace` context is set on all events. */\nexport function setupEventContextTrace(client: Client): void {\n  client.on('preprocessEvent', event => {\n    const span = getActiveSpan();\n    // For transaction events, this is handled separately\n    // Because the active span may not be the span that is actually the transaction event\n    if (!span || event.type === 'transaction') {\n      return;\n    }\n\n    // If event has already set `trace` context, use that one.\n    event.contexts = {\n      trace: spanToTraceContext(span),\n      ...event.contexts,\n    };\n\n    const rootSpan = getRootSpan(span);\n\n    event.sdkProcessingMetadata = {\n      dynamicSamplingContext: getDynamicSamplingContextFromSpan(rootSpan),\n      ...event.sdkProcessingMetadata,\n    };\n\n    return event;\n  });\n}\n", "import * as api from '@opentelemetry/api';\nimport type { Client, Scope, SerializedTraceData, Span } from '@sentry/core';\nimport {\n  dynamicSamplingContextToSentryBaggageHeader,\n  generateSentryTraceHeader,\n  getCapturedScopesOnSpan,\n} from '@sentry/core';\nimport { getInjectionData } from '../propagator';\nimport { getContextFromScope } from './contextData';\n\n/**\n * Otel-specific implementation of `getTraceData`.\n * @see `@sentry/core` version of `getTraceData` for more information\n */\nexport function getTraceData({\n  span,\n  scope,\n  client,\n}: { span?: Span; scope?: Scope; client?: Client } = {}): SerializedTraceData {\n  let ctx = (scope && getContextFromScope(scope)) ?? api.context.active();\n\n  if (span) {\n    const { scope } = getCapturedScopesOnSpan(span);\n    // fall back to current context if for whatever reason we can't find the one of the span\n    ctx = (scope && getContextFromScope(scope)) || api.trace.setSpan(api.context.active(), span);\n  }\n\n  const { traceId, spanId, sampled, dynamicSamplingContext } = getInjectionData(ctx, { scope, client });\n\n  return {\n    'sentry-trace': generateSentryTraceHeader(traceId, spanId, sampled),\n    baggage: dynamicSamplingContextToSentryBaggageHeader(dynamicSamplingContext),\n  };\n}\n", "import * as api from '@opentelemetry/api';\nimport type { Scope, withActiveSpan as defaultWithActiveSpan } from '@sentry/core';\nimport { getDefaultCurrentScope, getDefaultIsolationScope, setAsyncContextStrategy } from '@sentry/core';\nimport {\n  SENTRY_FORK_ISOLATION_SCOPE_CONTEXT_KEY,\n  SENTRY_FORK_SET_ISOLATION_SCOPE_CONTEXT_KEY,\n  SENTRY_FORK_SET_SCOPE_CONTEXT_KEY,\n} from './constants';\nimport { continueTrace, startInactiveSpan, startSpan, startSpanManual, withActiveSpan } from './trace';\nimport type { CurrentScopes } from './types';\nimport { getContextFromScope, getScopesFromContext } from './utils/contextData';\nimport { getActiveSpan } from './utils/getActiveSpan';\nimport { getTraceData } from './utils/getTraceData';\nimport { suppressTracing } from './utils/suppressTracing';\n\n/**\n * Sets the async context strategy to use follow the OTEL context under the hood.\n * We handle forking a hub inside of our custom OTEL Context Manager (./otelContextManager.ts)\n */\nexport function setOpenTelemetryContextAsyncContextStrategy(): void {\n  function getScopes(): CurrentScopes {\n    const ctx = api.context.active();\n    const scopes = getScopesFromContext(ctx);\n\n    if (scopes) {\n      return scopes;\n    }\n\n    // fallback behavior:\n    // if, for whatever reason, we can't find scopes on the context here, we have to fix this somehow\n    return {\n      scope: getDefaultCurrentScope(),\n      isolationScope: getDefaultIsolationScope(),\n    };\n  }\n\n  function withScope<T>(callback: (scope: Scope) => T): T {\n    const ctx = api.context.active();\n\n    // We depend on the otelContextManager to handle the context/hub\n    // We set the `SENTRY_FORK_ISOLATION_SCOPE_CONTEXT_KEY` context value, which is picked up by\n    // the OTEL context manager, which uses the presence of this key to determine if it should\n    // fork the isolation scope, or not\n    // as by default, we don't want to fork this, unless triggered explicitly by `withScope`\n    return api.context.with(ctx, () => {\n      return callback(getCurrentScope());\n    });\n  }\n\n  function withSetScope<T>(scope: Scope, callback: (scope: Scope) => T): T {\n    const ctx = getContextFromScope(scope) || api.context.active();\n\n    // We depend on the otelContextManager to handle the context/hub\n    // We set the `SENTRY_FORK_SET_SCOPE_CONTEXT_KEY` context value, which is picked up by\n    // the OTEL context manager, which picks up this scope as the current scope\n    return api.context.with(ctx.setValue(SENTRY_FORK_SET_SCOPE_CONTEXT_KEY, scope), () => {\n      return callback(scope);\n    });\n  }\n\n  function withIsolationScope<T>(callback: (isolationScope: Scope) => T): T {\n    const ctx = api.context.active();\n\n    // We depend on the otelContextManager to handle the context/hub\n    // We set the `SENTRY_FORK_ISOLATION_SCOPE_CONTEXT_KEY` context value, which is picked up by\n    // the OTEL context manager, which uses the presence of this key to determine if it should\n    // fork the isolation scope, or not\n    return api.context.with(ctx.setValue(SENTRY_FORK_ISOLATION_SCOPE_CONTEXT_KEY, true), () => {\n      return callback(getIsolationScope());\n    });\n  }\n\n  function withSetIsolationScope<T>(isolationScope: Scope, callback: (isolationScope: Scope) => T): T {\n    const ctx = api.context.active();\n\n    // We depend on the otelContextManager to handle the context/hub\n    // We set the `SENTRY_FORK_ISOLATION_SCOPE_CONTEXT_KEY` context value, which is picked up by\n    // the OTEL context manager, which uses the presence of this key to determine if it should\n    // fork the isolation scope, or not\n    return api.context.with(ctx.setValue(SENTRY_FORK_SET_ISOLATION_SCOPE_CONTEXT_KEY, isolationScope), () => {\n      return callback(getIsolationScope());\n    });\n  }\n\n  function getCurrentScope(): Scope {\n    return getScopes().scope;\n  }\n\n  function getIsolationScope(): Scope {\n    return getScopes().isolationScope;\n  }\n\n  setAsyncContextStrategy({\n    withScope,\n    withSetScope,\n    withSetIsolationScope,\n    withIsolationScope,\n    getCurrentScope,\n    getIsolationScope,\n    startSpan,\n    startSpanManual,\n    startInactiveSpan,\n    getActiveSpan,\n    suppressTracing,\n    getTraceData,\n    continueTrace,\n    // The types here don't fully align, because our own `Span` type is narrower\n    // than the OTEL one - but this is OK for here, as we now we'll only have OTEL spans passed around\n    withActiveSpan: withActiveSpan as typeof defaultWithActiveSpan,\n  });\n}\n", "import type { Context, ContextManager } from '@opentelemetry/api';\nimport type { Scope } from '@sentry/core';\nimport { getCurrentScope, getIsolationScope } from '@sentry/core';\nimport {\n  SENTRY_FORK_ISOLATION_SCOPE_CONTEXT_KEY,\n  SENTRY_FORK_SET_ISOLATION_SCOPE_CONTEXT_KEY,\n  SENTRY_FORK_SET_SCOPE_CONTEXT_KEY,\n} from './constants';\nimport { getScopesFromContext, setContextOnScope, setScopesOnContext } from './utils/contextData';\nimport { setIsSetup } from './utils/setupCheck';\n\n/**\n * Wrap an OpenTelemetry ContextManager in a way that ensures the context is kept in sync with the Sentry Scope.\n *\n * Usage:\n * import { AsyncLocalStorageContextManager } from '@opentelemetry/context-async-hooks';\n * const SentryContextManager = wrapContextManagerClass(AsyncLocalStorageContextManager);\n * const contextManager = new SentryContextManager();\n */\nexport function wrapContextManagerClass<ContextManagerInstance extends ContextManager>(\n  ContextManagerClass: new (...args: unknown[]) => ContextManagerInstance,\n): typeof ContextManagerClass {\n  /**\n   * This is a custom ContextManager for OpenTelemetry, which extends the default AsyncLocalStorageContextManager.\n   * It ensures that we create new scopes per context, so that the OTEL Context & the Sentry Scope are always in sync.\n   *\n   * Note that we currently only support AsyncHooks with this,\n   * but since this should work for Node 14+ anyhow that should be good enough.\n   */\n\n  // @ts-expect-error TS does not like this, but we know this is fine\n  class SentryContextManager extends ContextManagerClass {\n    public constructor(...args: unknown[]) {\n      super(...args);\n      setIsSetup('SentryContextManager');\n    }\n    /**\n     * Overwrite with() of the original AsyncLocalStorageContextManager\n     * to ensure we also create new scopes per context.\n     */\n    public with<A extends unknown[], F extends (...args: A) => ReturnType<F>>(\n      context: Context,\n      fn: F,\n      thisArg?: ThisParameterType<F>,\n      ...args: A\n    ): ReturnType<F> {\n      const currentScopes = getScopesFromContext(context);\n      const currentScope = currentScopes?.scope || getCurrentScope();\n      const currentIsolationScope = currentScopes?.isolationScope || getIsolationScope();\n\n      const shouldForkIsolationScope = context.getValue(SENTRY_FORK_ISOLATION_SCOPE_CONTEXT_KEY) === true;\n      const scope = context.getValue(SENTRY_FORK_SET_SCOPE_CONTEXT_KEY) as Scope | undefined;\n      const isolationScope = context.getValue(SENTRY_FORK_SET_ISOLATION_SCOPE_CONTEXT_KEY) as Scope | undefined;\n\n      const newCurrentScope = scope || currentScope.clone();\n      const newIsolationScope =\n        isolationScope || (shouldForkIsolationScope ? currentIsolationScope.clone() : currentIsolationScope);\n      const scopes = { scope: newCurrentScope, isolationScope: newIsolationScope };\n\n      const ctx1 = setScopesOnContext(context, scopes);\n\n      // Remove the unneeded values again\n      const ctx2 = ctx1\n        .deleteValue(SENTRY_FORK_ISOLATION_SCOPE_CONTEXT_KEY)\n        .deleteValue(SENTRY_FORK_SET_SCOPE_CONTEXT_KEY)\n        .deleteValue(SENTRY_FORK_SET_ISOLATION_SCOPE_CONTEXT_KEY);\n\n      setContextOnScope(newCurrentScope, ctx2);\n\n      return super.with(ctx2, fn, thisArg, ...args);\n    }\n  }\n\n  return SentryContextManager as unknown as typeof ContextManagerClass;\n}\n", "import type { ReadableSpan } from '@opentelemetry/sdk-trace-base';\nimport { SEMANTIC_ATTRIBUTE_SENTRY_PARENT_IS_REMOTE } from '../semanticAttributes';\nimport { getParentSpanId } from './getParentSpanId';\n\nexport interface SpanNode {\n  id: string;\n  span?: ReadableSpan;\n  parentNode?: SpanNode | undefined;\n  children: SpanNode[];\n}\n\ntype SpanMap = Map<string, SpanNode>;\n\n/**\n * This function runs through a list of OTEL Spans, and wraps them in an `SpanNode`\n * where each node holds a reference to their parent node.\n */\nexport function groupSpansWithParents(spans: ReadableSpan[]): SpanNode[] {\n  const nodeMap: SpanMap = new Map<string, SpanNode>();\n\n  for (const span of spans) {\n    createOrUpdateSpanNodeAndRefs(nodeMap, span);\n  }\n\n  return Array.from(nodeMap, function ([_id, spanNode]) {\n    return spanNode;\n  });\n}\n\n/**\n * This returns the _local_ parent ID - `parentId` on the span may point to a remote span.\n */\nexport function getLocalParentId(span: ReadableSpan): string | undefined {\n  const parentIsRemote = span.attributes[SEMANTIC_ATTRIBUTE_SENTRY_PARENT_IS_REMOTE] === true;\n  // If the parentId is the trace parent ID, we pretend it's undefined\n  // As this means the parent exists somewhere else\n  return !parentIsRemote ? getParentSpanId(span) : undefined;\n}\n\nfunction createOrUpdateSpanNodeAndRefs(nodeMap: SpanMap, span: ReadableSpan): void {\n  const id = span.spanContext().spanId;\n  const parentId = getLocalParentId(span);\n\n  if (!parentId) {\n    createOrUpdateNode(nodeMap, { id, span, children: [] });\n    return;\n  }\n\n  // Else make sure to create parent node as well\n  // Note that the parent may not know it's parent _yet_, this may be updated in a later pass\n  const parentNode = createOrGetParentNode(nodeMap, parentId);\n  const node = createOrUpdateNode(nodeMap, { id, span, parentNode, children: [] });\n  parentNode.children.push(node);\n}\n\nfunction createOrGetParentNode(nodeMap: SpanMap, id: string): SpanNode {\n  const existing = nodeMap.get(id);\n\n  if (existing) {\n    return existing;\n  }\n\n  return createOrUpdateNode(nodeMap, { id, children: [] });\n}\n\nfunction createOrUpdateNode(nodeMap: SpanMap, spanNode: SpanNode): SpanNode {\n  const existing = nodeMap.get(spanNode.id);\n\n  // If span is already set, nothing to do here\n  if (existing?.span) {\n    return existing;\n  }\n\n  // If it exists but span is not set yet, we update it\n  if (existing && !existing.span) {\n    existing.span = spanNode.span;\n    existing.parentNode = spanNode.parentNode;\n    return existing;\n  }\n\n  // Else, we create a new one...\n  nodeMap.set(spanNode.id, spanNode);\n  return spanNode;\n}\n", "import { SpanStatusCode } from '@opentelemetry/api';\nimport {\n  ATTR_HTTP_RESPONSE_STATUS_CODE,\n  SEMATTRS_HTTP_STATUS_CODE,\n  SEMATTRS_RPC_GRPC_STATUS_CODE,\n} from '@opentelemetry/semantic-conventions';\nimport type { SpanAttributes, SpanStatus } from '@sentry/core';\nimport { getSpanStatusFromHttpCode, SPAN_STATUS_ERROR, SPAN_STATUS_OK } from '@sentry/core';\nimport type { AbstractSpan } from '../types';\nimport { spanHasAttributes, spanHasStatus } from './spanTypes';\n\n// canonicalCodesGrpcMap maps some GRPC codes to Sentry's span statuses. See description in grpc documentation.\nconst canonicalGrpcErrorCodesMap: Record<string, SpanStatus['message']> = {\n  '1': 'cancelled',\n  '2': 'unknown_error',\n  '3': 'invalid_argument',\n  '4': 'deadline_exceeded',\n  '5': 'not_found',\n  '6': 'already_exists',\n  '7': 'permission_denied',\n  '8': 'resource_exhausted',\n  '9': 'failed_precondition',\n  '10': 'aborted',\n  '11': 'out_of_range',\n  '12': 'unimplemented',\n  '13': 'internal_error',\n  '14': 'unavailable',\n  '15': 'data_loss',\n  '16': 'unauthenticated',\n} as const;\n\nconst isStatusErrorMessageValid = (message: string): boolean => {\n  return Object.values(canonicalGrpcErrorCodesMap).includes(message as SpanStatus['message']);\n};\n\n/**\n * Get a Sentry span status from an otel span.\n */\nexport function mapStatus(span: AbstractSpan): SpanStatus {\n  const attributes = spanHasAttributes(span) ? span.attributes : {};\n  const status = spanHasStatus(span) ? span.status : undefined;\n\n  if (status) {\n    // Since span status OK is not set by default, we give it priority: https://opentelemetry.io/docs/concepts/signals/traces/#span-status\n    if (status.code === SpanStatusCode.OK) {\n      return { code: SPAN_STATUS_OK };\n      // If the span is already marked as erroneous we return that exact status\n    } else if (status.code === SpanStatusCode.ERROR) {\n      if (typeof status.message === 'undefined') {\n        const inferredStatus = inferStatusFromAttributes(attributes);\n        if (inferredStatus) {\n          return inferredStatus;\n        }\n      }\n\n      if (status.message && isStatusErrorMessageValid(status.message)) {\n        return { code: SPAN_STATUS_ERROR, message: status.message };\n      } else {\n        return { code: SPAN_STATUS_ERROR, message: 'unknown_error' };\n      }\n    }\n  }\n\n  // If the span status is UNSET, we try to infer it from HTTP or GRPC status codes.\n  const inferredStatus = inferStatusFromAttributes(attributes);\n\n  if (inferredStatus) {\n    return inferredStatus;\n  }\n\n  // We default to setting the spans status to ok.\n  if (status?.code === SpanStatusCode.UNSET) {\n    return { code: SPAN_STATUS_OK };\n  } else {\n    return { code: SPAN_STATUS_ERROR, message: 'unknown_error' };\n  }\n}\n\nfunction inferStatusFromAttributes(attributes: SpanAttributes): SpanStatus | undefined {\n  // If the span status is UNSET, we try to infer it from HTTP or GRPC status codes.\n\n  // eslint-disable-next-line deprecation/deprecation\n  const httpCodeAttribute = attributes[ATTR_HTTP_RESPONSE_STATUS_CODE] || attributes[SEMATTRS_HTTP_STATUS_CODE];\n  // eslint-disable-next-line deprecation/deprecation\n  const grpcCodeAttribute = attributes[SEMATTRS_RPC_GRPC_STATUS_CODE];\n\n  const numberHttpCode =\n    typeof httpCodeAttribute === 'number'\n      ? httpCodeAttribute\n      : typeof httpCodeAttribute === 'string'\n        ? parseInt(httpCodeAttribute)\n        : undefined;\n\n  if (typeof numberHttpCode === 'number') {\n    return getSpanStatusFromHttpCode(numberHttpCode);\n  }\n\n  if (typeof grpcCodeAttribute === 'string') {\n    return { code: SPAN_STATUS_ERROR, message: canonicalGrpcErrorCodesMap[grpcCodeAttribute] || 'unknown_error' };\n  }\n\n  return undefined;\n}\n", "/* eslint-disable max-lines */\nimport type { Span } from '@opentelemetry/api';\nimport { SpanKind } from '@opentelemetry/api';\nimport type { ReadableSpan } from '@opentelemetry/sdk-trace-base';\nimport { ATTR_HTTP_RESPONSE_STATUS_CODE, SEMATTRS_HTTP_STATUS_CODE } from '@opentelemetry/semantic-conventions';\nimport type {\n  SpanAttributes,\n  SpanJSON,\n  SpanOrigin,\n  TraceContext,\n  TransactionEvent,\n  TransactionSource,\n} from '@sentry/core';\nimport {\n  captureEvent,\n  convertSpanLinksForEnvelope,\n  debounce,\n  debug,\n  getCapturedScopesOnSpan,\n  getDynamicSamplingContextFromSpan,\n  getStatusMessage,\n  SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME,\n  SEMANTIC_ATTRIBUTE_SENTRY_OP,\n  SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,\n  SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE,\n  SEMANTIC_ATTRIBUTE_SENTRY_SOURCE,\n  spanTimeInputToSeconds,\n  timedEventsToMeasurements,\n} from '@sentry/core';\nimport { DEBUG_BUILD } from './debug-build';\nimport { SEMANTIC_ATTRIBUTE_SENTRY_PARENT_IS_REMOTE } from './semanticAttributes';\nimport { getParentSpanId } from './utils/getParentSpanId';\nimport { getRequestSpanData } from './utils/getRequestSpanData';\nimport type { SpanNode } from './utils/groupSpansWithParents';\nimport { getLocalParentId, groupSpansWithParents } from './utils/groupSpansWithParents';\nimport { mapStatus } from './utils/mapStatus';\nimport { parseSpanDescription } from './utils/parseSpanDescription';\n\ntype SpanNodeCompleted = SpanNode & { span: ReadableSpan };\n\nconst MAX_SPAN_COUNT = 1000;\nconst DEFAULT_TIMEOUT = 300; // 5 min\n\ninterface FinishedSpanBucket {\n  timestampInS: number;\n  spans: Set<ReadableSpan>;\n}\n\n/**\n * A Sentry-specific exporter that converts OpenTelemetry Spans to Sentry Spans & Transactions.\n */\nexport class SentrySpanExporter {\n  /*\n   * A quick explanation on the buckets: We do bucketing of finished spans for efficiency. This span exporter is\n   * accumulating spans until a root span is encountered and then it flushes all the spans that are descendants of that\n   * root span. Because it is totally in the realm of possibilities that root spans are never finished, and we don't\n   * want to accumulate spans indefinitely in memory, we need to periodically evacuate spans. Naively we could simply\n   * store the spans in an array and each time a new span comes in we could iterate through the entire array and\n   * evacuate all spans that have an end-timestamp that is older than our limit. This could get quite expensive because\n   * we would have to iterate a potentially large number of spans every time we evacuate. We want to avoid these large\n   * bursts of computation.\n   *\n   * Instead we go for a bucketing approach and put spans into buckets, based on what second\n   * (modulo the time limit) the span was put into the exporter. With buckets, when we decide to evacuate, we can\n   * iterate through the bucket entries instead, which have an upper bound of items, making the evacuation much more\n   * efficient. Cleaning up also becomes much more efficient since it simply involves de-referencing a bucket within the\n   * bucket array, and letting garbage collection take care of the rest.\n   */\n  private _finishedSpanBuckets: (FinishedSpanBucket | undefined)[];\n  private _finishedSpanBucketSize: number;\n  private _spansToBucketEntry: WeakMap<ReadableSpan, FinishedSpanBucket>;\n  private _lastCleanupTimestampInS: number;\n  // Essentially a a set of span ids that are already sent. The values are expiration\n  // times in this cache so we don't hold onto them indefinitely.\n  private _sentSpans: Map<string, number>;\n  /* Internally, we use a debounced flush to give some wiggle room to the span processor to accumulate more spans. */\n  private _debouncedFlush: ReturnType<typeof debounce>;\n\n  public constructor(options?: {\n    /** Lower bound of time in seconds until spans that are buffered but have not been sent as part of a transaction get cleared from memory. */\n    timeout?: number;\n  }) {\n    this._finishedSpanBucketSize = options?.timeout || DEFAULT_TIMEOUT;\n    this._finishedSpanBuckets = new Array(this._finishedSpanBucketSize).fill(undefined);\n    this._lastCleanupTimestampInS = Math.floor(Date.now() / 1000);\n    this._spansToBucketEntry = new WeakMap();\n    this._sentSpans = new Map<string, number>();\n    this._debouncedFlush = debounce(this.flush.bind(this), 1, { maxWait: 100 });\n  }\n\n  /**\n   * Export a single span.\n   * This is called by the span processor whenever a span is ended.\n   */\n  public export(span: ReadableSpan): void {\n    const currentTimestampInS = Math.floor(Date.now() / 1000);\n\n    if (this._lastCleanupTimestampInS !== currentTimestampInS) {\n      let droppedSpanCount = 0;\n      this._finishedSpanBuckets.forEach((bucket, i) => {\n        if (bucket && bucket.timestampInS <= currentTimestampInS - this._finishedSpanBucketSize) {\n          droppedSpanCount += bucket.spans.size;\n          this._finishedSpanBuckets[i] = undefined;\n        }\n      });\n      if (droppedSpanCount > 0) {\n        DEBUG_BUILD &&\n          debug.log(\n            `SpanExporter dropped ${droppedSpanCount} spans because they were pending for more than ${this._finishedSpanBucketSize} seconds.`,\n          );\n      }\n      this._lastCleanupTimestampInS = currentTimestampInS;\n    }\n\n    const currentBucketIndex = currentTimestampInS % this._finishedSpanBucketSize;\n    const currentBucket = this._finishedSpanBuckets[currentBucketIndex] || {\n      timestampInS: currentTimestampInS,\n      spans: new Set(),\n    };\n    this._finishedSpanBuckets[currentBucketIndex] = currentBucket;\n    currentBucket.spans.add(span);\n    this._spansToBucketEntry.set(span, currentBucket);\n\n    // If the span doesn't have a local parent ID (it's a root span), we're gonna flush all the ended spans\n    const localParentId = getLocalParentId(span);\n    if (!localParentId || this._sentSpans.has(localParentId)) {\n      this._debouncedFlush();\n    }\n  }\n\n  /**\n   * Try to flush any pending spans immediately.\n   * This is called internally by the exporter (via _debouncedFlush),\n   * but can also be triggered externally if we force-flush.\n   */\n  public flush(): void {\n    const finishedSpans = this._finishedSpanBuckets.flatMap(bucket => (bucket ? Array.from(bucket.spans) : []));\n\n    this._flushSentSpanCache();\n    const sentSpans = this._maybeSend(finishedSpans);\n\n    const sentSpanCount = sentSpans.size;\n    const remainingOpenSpanCount = finishedSpans.length - sentSpanCount;\n    DEBUG_BUILD &&\n      debug.log(\n        `SpanExporter exported ${sentSpanCount} spans, ${remainingOpenSpanCount} spans are waiting for their parent spans to finish`,\n      );\n\n    const expirationDate = Date.now() + DEFAULT_TIMEOUT * 1000;\n\n    for (const span of sentSpans) {\n      this._sentSpans.set(span.spanContext().spanId, expirationDate);\n      const bucketEntry = this._spansToBucketEntry.get(span);\n      if (bucketEntry) {\n        bucketEntry.spans.delete(span);\n      }\n    }\n    // Cancel a pending debounced flush, if there is one\n    // This can be relevant if we directly flush, circumventing the debounce\n    // in that case, we want to cancel any pending debounced flush\n    this._debouncedFlush.cancel();\n  }\n\n  /**\n   * Clear the exporter.\n   * This is called when the span processor is shut down.\n   */\n  public clear(): void {\n    this._finishedSpanBuckets = this._finishedSpanBuckets.fill(undefined);\n    this._sentSpans.clear();\n    this._debouncedFlush.cancel();\n  }\n\n  /**\n   * Send the given spans, but only if they are part of a finished transaction.\n   *\n   * Returns the sent spans.\n   * Spans remain unsent when their parent span is not yet finished.\n   * This will happen regularly, as child spans are generally finished before their parents.\n   * But it _could_ also happen because, for whatever reason, a parent span was lost.\n   * In this case, we'll eventually need to clean this up.\n   */\n  private _maybeSend(spans: ReadableSpan[]): Set<ReadableSpan> {\n    const grouped = groupSpansWithParents(spans);\n    const sentSpans = new Set<ReadableSpan>();\n\n    const rootNodes = this._getCompletedRootNodes(grouped);\n\n    for (const root of rootNodes) {\n      const span = root.span;\n      sentSpans.add(span);\n      const transactionEvent = createTransactionForOtelSpan(span);\n\n      // Add an attribute to the transaction event to indicate that this transaction is an orphaned transaction\n      if (root.parentNode && this._sentSpans.has(root.parentNode.id)) {\n        const traceData = transactionEvent.contexts?.trace?.data;\n        if (traceData) {\n          traceData['sentry.parent_span_already_sent'] = true;\n        }\n      }\n\n      // We'll recursively add all the child spans to this array\n      const spans = transactionEvent.spans || [];\n\n      for (const child of root.children) {\n        createAndFinishSpanForOtelSpan(child, spans, sentSpans);\n      }\n\n      // spans.sort() mutates the array, but we do not use this anymore after this point\n      // so we can safely mutate it here\n      transactionEvent.spans =\n        spans.length > MAX_SPAN_COUNT\n          ? spans.sort((a, b) => a.start_timestamp - b.start_timestamp).slice(0, MAX_SPAN_COUNT)\n          : spans;\n\n      const measurements = timedEventsToMeasurements(span.events);\n      if (measurements) {\n        transactionEvent.measurements = measurements;\n      }\n\n      captureEvent(transactionEvent);\n    }\n\n    return sentSpans;\n  }\n\n  /** Remove \"expired\" span id entries from the _sentSpans cache. */\n  private _flushSentSpanCache(): void {\n    const currentTimestamp = Date.now();\n    // Note, it is safe to delete items from the map as we go: https://stackoverflow.com/a/35943995/90297\n    for (const [spanId, expirationTime] of this._sentSpans.entries()) {\n      if (expirationTime <= currentTimestamp) {\n        this._sentSpans.delete(spanId);\n      }\n    }\n  }\n\n  /** Check if a node is a completed root node or a node whose parent has already been sent */\n  private _nodeIsCompletedRootNodeOrHasSentParent(node: SpanNode): node is SpanNodeCompleted {\n    return !!node.span && (!node.parentNode || this._sentSpans.has(node.parentNode.id));\n  }\n\n  /** Get all completed root nodes from a list of nodes */\n  private _getCompletedRootNodes(nodes: SpanNode[]): SpanNodeCompleted[] {\n    // TODO: We should be able to remove the explicit `node is SpanNodeCompleted` type guard\n    //       once we stop supporting TS < 5.5\n    return nodes.filter((node): node is SpanNodeCompleted => this._nodeIsCompletedRootNodeOrHasSentParent(node));\n  }\n}\n\nfunction parseSpan(span: ReadableSpan): { op?: string; origin?: SpanOrigin; source?: TransactionSource } {\n  const attributes = span.attributes;\n\n  const origin = attributes[SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN] as SpanOrigin | undefined;\n  const op = attributes[SEMANTIC_ATTRIBUTE_SENTRY_OP] as string | undefined;\n  const source = attributes[SEMANTIC_ATTRIBUTE_SENTRY_SOURCE] as TransactionSource | undefined;\n\n  return { origin, op, source };\n}\n\n/** Exported only for tests. */\nexport function createTransactionForOtelSpan(span: ReadableSpan): TransactionEvent {\n  const { op, description, data, origin = 'manual', source } = getSpanData(span);\n  const capturedSpanScopes = getCapturedScopesOnSpan(span as unknown as Span);\n\n  const sampleRate = span.attributes[SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE] as number | undefined;\n\n  const attributes: SpanAttributes = {\n    [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: source,\n    [SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE]: sampleRate,\n    [SEMANTIC_ATTRIBUTE_SENTRY_OP]: op,\n    [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: origin,\n    ...data,\n    ...removeSentryAttributes(span.attributes),\n  };\n\n  const { links } = span;\n  const { traceId: trace_id, spanId: span_id } = span.spanContext();\n\n  // If parentSpanIdFromTraceState is defined at all, we want it to take precedence\n  // In that case, an empty string should be interpreted as \"no parent span id\",\n  // even if `span.parentSpanId` is set\n  // this is the case when we are starting a new trace, where we have a virtual span based on the propagationContext\n  // We only want to continue the traceId in this case, but ignore the parent span\n  const parent_span_id = getParentSpanId(span);\n\n  const status = mapStatus(span);\n\n  const traceContext: TraceContext = {\n    parent_span_id,\n    span_id,\n    trace_id,\n    data: attributes,\n    origin,\n    op,\n    status: getStatusMessage(status), // As per protocol, span status is allowed to be undefined\n    links: convertSpanLinksForEnvelope(links),\n  };\n\n  const statusCode = attributes[ATTR_HTTP_RESPONSE_STATUS_CODE];\n  const responseContext = typeof statusCode === 'number' ? { response: { status_code: statusCode } } : undefined;\n\n  const transactionEvent: TransactionEvent = {\n    contexts: {\n      trace: traceContext,\n      otel: {\n        resource: span.resource.attributes,\n      },\n      ...responseContext,\n    },\n    spans: [],\n    start_timestamp: spanTimeInputToSeconds(span.startTime),\n    timestamp: spanTimeInputToSeconds(span.endTime),\n    transaction: description,\n    type: 'transaction',\n    sdkProcessingMetadata: {\n      capturedSpanScope: capturedSpanScopes.scope,\n      capturedSpanIsolationScope: capturedSpanScopes.isolationScope,\n      sampleRate,\n      dynamicSamplingContext: getDynamicSamplingContextFromSpan(span as unknown as Span),\n    },\n    ...(source && {\n      transaction_info: {\n        source,\n      },\n    }),\n  };\n\n  return transactionEvent;\n}\n\nfunction createAndFinishSpanForOtelSpan(node: SpanNode, spans: SpanJSON[], sentSpans: Set<ReadableSpan>): void {\n  const span = node.span;\n\n  if (span) {\n    sentSpans.add(span);\n  }\n\n  const shouldDrop = !span;\n\n  // If this span should be dropped, we still want to create spans for the children of this\n  if (shouldDrop) {\n    node.children.forEach(child => {\n      createAndFinishSpanForOtelSpan(child, spans, sentSpans);\n    });\n    return;\n  }\n\n  const span_id = span.spanContext().spanId;\n  const trace_id = span.spanContext().traceId;\n  const parentSpanId = getParentSpanId(span);\n\n  const { attributes, startTime, endTime, links } = span;\n\n  const { op, description, data, origin = 'manual' } = getSpanData(span);\n  const allData = {\n    [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: origin,\n    [SEMANTIC_ATTRIBUTE_SENTRY_OP]: op,\n    ...removeSentryAttributes(attributes),\n    ...data,\n  };\n\n  const status = mapStatus(span);\n\n  const spanJSON: SpanJSON = {\n    span_id,\n    trace_id,\n    data: allData,\n    description,\n    parent_span_id: parentSpanId,\n    start_timestamp: spanTimeInputToSeconds(startTime),\n    // This is [0,0] by default in OTEL, in which case we want to interpret this as no end time\n    timestamp: spanTimeInputToSeconds(endTime) || undefined,\n    status: getStatusMessage(status), // As per protocol, span status is allowed to be undefined\n    op,\n    origin,\n    measurements: timedEventsToMeasurements(span.events),\n    links: convertSpanLinksForEnvelope(links),\n  };\n\n  spans.push(spanJSON);\n\n  node.children.forEach(child => {\n    createAndFinishSpanForOtelSpan(child, spans, sentSpans);\n  });\n}\n\nfunction getSpanData(span: ReadableSpan): {\n  data: Record<string, unknown>;\n  op?: string;\n  description: string;\n  source?: TransactionSource;\n  origin?: SpanOrigin;\n} {\n  const { op: definedOp, source: definedSource, origin } = parseSpan(span);\n  const { op: inferredOp, description, source: inferredSource, data: inferredData } = parseSpanDescription(span);\n\n  const op = definedOp || inferredOp;\n  const source = definedSource || inferredSource;\n\n  const data = { ...inferredData, ...getData(span) };\n\n  return {\n    op,\n    description,\n    source,\n    origin,\n    data,\n  };\n}\n\n/**\n * Remove custom `sentry.` attributes we do not need to send.\n * These are more carrier attributes we use inside of the SDK, we do not need to send them to the API.\n */\nfunction removeSentryAttributes(data: Record<string, unknown>): Record<string, unknown> {\n  const cleanedData = { ...data };\n\n  /* eslint-disable @typescript-eslint/no-dynamic-delete */\n  delete cleanedData[SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE];\n  delete cleanedData[SEMANTIC_ATTRIBUTE_SENTRY_PARENT_IS_REMOTE];\n  delete cleanedData[SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME];\n  /* eslint-enable @typescript-eslint/no-dynamic-delete */\n\n  return cleanedData;\n}\n\nfunction getData(span: ReadableSpan): Record<string, unknown> {\n  const attributes = span.attributes;\n  const data: Record<string, unknown> = {};\n\n  if (span.kind !== SpanKind.INTERNAL) {\n    data['otel.kind'] = SpanKind[span.kind];\n  }\n\n  // eslint-disable-next-line deprecation/deprecation\n  const maybeHttpStatusCodeAttribute = attributes[SEMATTRS_HTTP_STATUS_CODE];\n  if (maybeHttpStatusCodeAttribute) {\n    data[ATTR_HTTP_RESPONSE_STATUS_CODE] = maybeHttpStatusCodeAttribute as string;\n  }\n\n  const requestData = getRequestSpanData(span);\n\n  if (requestData.url) {\n    data.url = requestData.url;\n  }\n\n  if (requestData['http.query']) {\n    data['http.query'] = requestData['http.query'].slice(1);\n  }\n  if (requestData['http.fragment']) {\n    data['http.fragment'] = requestData['http.fragment'].slice(1);\n  }\n\n  return data;\n}\n", "import type { Context } from '@opentelemetry/api';\nimport { ROOT_CONTEXT, trace } from '@opentelemetry/api';\nimport type { ReadableSpan, Span, SpanProcessor as SpanProcessorInterface } from '@opentelemetry/sdk-trace-base';\nimport {\n  addChildSpanToSpan,\n  getClient,\n  getDefaultCurrentScope,\n  getDefaultIsolationScope,\n  logSpanEnd,\n  logSpanStart,\n  setCapturedScopesOnSpan,\n} from '@sentry/core';\nimport { SEMANTIC_ATTRIBUTE_SENTRY_PARENT_IS_REMOTE } from './semanticAttributes';\nimport { SentrySpanExporter } from './spanExporter';\nimport { getScopesFromContext } from './utils/contextData';\nimport { setIsSetup } from './utils/setupCheck';\n\nfunction onSpanStart(span: Span, parentContext: Context): void {\n  // This is a reliable way to get the parent span - because this is exactly how the parent is identified in the OTEL SDK\n  const parentSpan = trace.getSpan(parentContext);\n\n  let scopes = getScopesFromContext(parentContext);\n\n  // We need access to the parent span in order to be able to move up the span tree for breadcrumbs\n  if (parentSpan && !parentSpan.spanContext().isRemote) {\n    addChildSpanToSpan(parentSpan, span);\n  }\n\n  // We need this in the span exporter\n  if (parentSpan?.spanContext().isRemote) {\n    span.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_PARENT_IS_REMOTE, true);\n  }\n\n  // The root context does not have scopes stored, so we check for this specifically\n  // As fallback we attach the global scopes\n  if (parentContext === ROOT_CONTEXT) {\n    scopes = {\n      scope: getDefaultCurrentScope(),\n      isolationScope: getDefaultIsolationScope(),\n    };\n  }\n\n  // We need the scope at time of span creation in order to apply it to the event when the span is finished\n  if (scopes) {\n    setCapturedScopesOnSpan(span, scopes.scope, scopes.isolationScope);\n  }\n\n  logSpanStart(span);\n\n  const client = getClient();\n  client?.emit('spanStart', span);\n}\n\nfunction onSpanEnd(span: Span): void {\n  logSpanEnd(span);\n\n  const client = getClient();\n  client?.emit('spanEnd', span);\n}\n\n/**\n * Converts OpenTelemetry Spans to Sentry Spans and sends them to Sentry via\n * the Sentry SDK.\n */\nexport class SentrySpanProcessor implements SpanProcessorInterface {\n  private _exporter: SentrySpanExporter;\n\n  public constructor(options?: { timeout?: number }) {\n    setIsSetup('SentrySpanProcessor');\n    this._exporter = new SentrySpanExporter(options);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public async forceFlush(): Promise<void> {\n    this._exporter.flush();\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public async shutdown(): Promise<void> {\n    this._exporter.clear();\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public onStart(span: Span, parentContext: Context): void {\n    onSpanStart(span, parentContext);\n  }\n\n  /** @inheritDoc */\n  public onEnd(span: Span & ReadableSpan): void {\n    onSpanEnd(span);\n\n    this._exporter.export(span);\n  }\n}\n", "/* eslint-disable complexity */\nimport type { Context, Span, TraceState as TraceStateInterface } from '@opentelemetry/api';\nimport { isSpanContextValid, SpanKind, trace } from '@opentelemetry/api';\nimport { TraceState } from '@opentelemetry/core';\nimport type { Sam<PERSON>, SamplingResult } from '@opentelemetry/sdk-trace-base';\nimport { SamplingDecision } from '@opentelemetry/sdk-trace-base';\nimport {\n  ATTR_HTTP_REQUEST_METHOD,\n  ATTR_URL_FULL,\n  SEMATTRS_HTTP_METHOD,\n  SEMATTRS_HTTP_URL,\n} from '@opentelemetry/semantic-conventions';\nimport type { Client, SpanAttributes } from '@sentry/core';\nimport {\n  baggageHeaderToDynamicSamplingContext,\n  debug,\n  hasSpansEnabled,\n  parseSampleRate,\n  sampleSpan,\n  SEMANTIC_ATTRIBUTE_SENTRY_OP,\n  SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE,\n} from '@sentry/core';\nimport {\n  SENTRY_TRACE_STATE_DSC,\n  SENTRY_TRACE_STATE_SAMPLE_RAND,\n  SENTRY_TRACE_STATE_SAMPLE_RATE,\n  SENTRY_TRACE_STATE_SAMPLED_NOT_RECORDING,\n  SENTRY_TRACE_STATE_URL,\n} from './constants';\nimport { DEBUG_BUILD } from './debug-build';\nimport { getScopesFromContext } from './utils/contextData';\nimport { getSamplingDecision } from './utils/getSamplingDecision';\nimport { inferSpanData } from './utils/parseSpanDescription';\nimport { setIsSetup } from './utils/setupCheck';\n\n/**\n * A custom OTEL sampler that uses Sentry sampling rates to make its decision\n */\nexport class SentrySampler implements Sampler {\n  private _client: Client;\n\n  public constructor(client: Client) {\n    this._client = client;\n    setIsSetup('SentrySampler');\n  }\n\n  /** @inheritDoc */\n  public shouldSample(\n    context: Context,\n    traceId: string,\n    spanName: string,\n    spanKind: SpanKind,\n    spanAttributes: SpanAttributes,\n    _links: unknown,\n  ): SamplingResult {\n    const options = this._client.getOptions();\n\n    const parentSpan = getValidSpan(context);\n    const parentContext = parentSpan?.spanContext();\n\n    if (!hasSpansEnabled(options)) {\n      return wrapSamplingDecision({ decision: undefined, context, spanAttributes });\n    }\n\n    // `ATTR_HTTP_REQUEST_METHOD` is the new attribute, but we still support the old one, `SEMATTRS_HTTP_METHOD`, for now.\n    // eslint-disable-next-line deprecation/deprecation\n    const maybeSpanHttpMethod = spanAttributes[SEMATTRS_HTTP_METHOD] || spanAttributes[ATTR_HTTP_REQUEST_METHOD];\n\n    // If we have a http.client span that has no local parent, we never want to sample it\n    // but we want to leave downstream sampling decisions up to the server\n    if (spanKind === SpanKind.CLIENT && maybeSpanHttpMethod && (!parentSpan || parentContext?.isRemote)) {\n      return wrapSamplingDecision({ decision: undefined, context, spanAttributes });\n    }\n\n    const parentSampled = parentSpan ? getParentSampled(parentSpan, traceId, spanName) : undefined;\n    const isRootSpan = !parentSpan || parentContext?.isRemote;\n\n    // We only sample based on parameters (like tracesSampleRate or tracesSampler) for root spans (which is done in sampleSpan).\n    // Non-root-spans simply inherit the sampling decision from their parent.\n    if (!isRootSpan) {\n      return wrapSamplingDecision({\n        decision: parentSampled ? SamplingDecision.RECORD_AND_SAMPLED : SamplingDecision.NOT_RECORD,\n        context,\n        spanAttributes,\n      });\n    }\n\n    // We want to pass the inferred name & attributes to the sampler method\n    const {\n      description: inferredSpanName,\n      data: inferredAttributes,\n      op,\n    } = inferSpanData(spanName, spanAttributes, spanKind);\n\n    const mergedAttributes = {\n      ...inferredAttributes,\n      ...spanAttributes,\n    };\n\n    if (op) {\n      mergedAttributes[SEMANTIC_ATTRIBUTE_SENTRY_OP] = op;\n    }\n\n    const mutableSamplingDecision = { decision: true };\n    this._client.emit(\n      'beforeSampling',\n      {\n        spanAttributes: mergedAttributes,\n        spanName: inferredSpanName,\n        parentSampled: parentSampled,\n        parentContext: parentContext,\n      },\n      mutableSamplingDecision,\n    );\n    if (!mutableSamplingDecision.decision) {\n      return wrapSamplingDecision({ decision: undefined, context, spanAttributes });\n    }\n\n    const { isolationScope } = getScopesFromContext(context) ?? {};\n\n    const dscString = parentContext?.traceState ? parentContext.traceState.get(SENTRY_TRACE_STATE_DSC) : undefined;\n    const dsc = dscString ? baggageHeaderToDynamicSamplingContext(dscString) : undefined;\n\n    const sampleRand = parseSampleRate(dsc?.sample_rand) ?? Math.random();\n\n    const [sampled, sampleRate, localSampleRateWasApplied] = sampleSpan(\n      options,\n      {\n        name: inferredSpanName,\n        attributes: mergedAttributes,\n        normalizedRequest: isolationScope?.getScopeData().sdkProcessingMetadata.normalizedRequest,\n        parentSampled,\n        parentSampleRate: parseSampleRate(dsc?.sample_rate),\n      },\n      sampleRand,\n    );\n\n    const method = `${maybeSpanHttpMethod}`.toUpperCase();\n    if (method === 'OPTIONS' || method === 'HEAD') {\n      DEBUG_BUILD && debug.log(`[Tracing] Not sampling span because HTTP method is '${method}' for ${spanName}`);\n\n      return wrapSamplingDecision({\n        decision: SamplingDecision.NOT_RECORD,\n        context,\n        spanAttributes,\n        sampleRand,\n        downstreamTraceSampleRate: 0, // we don't want to sample anything in the downstream trace either\n      });\n    }\n\n    if (\n      !sampled &&\n      // We check for `parentSampled === undefined` because we only want to record client reports for spans that are trace roots (ie. when there was incoming trace)\n      parentSampled === undefined\n    ) {\n      DEBUG_BUILD && debug.log('[Tracing] Discarding root span because its trace was not chosen to be sampled.');\n      this._client.recordDroppedEvent('sample_rate', 'transaction');\n    }\n\n    return {\n      ...wrapSamplingDecision({\n        decision: sampled ? SamplingDecision.RECORD_AND_SAMPLED : SamplingDecision.NOT_RECORD,\n        context,\n        spanAttributes,\n        sampleRand,\n        downstreamTraceSampleRate: localSampleRateWasApplied ? sampleRate : undefined,\n      }),\n      attributes: {\n        // We set the sample rate on the span when a local sample rate was applied to better understand how traces were sampled in Sentry\n        [SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE]: localSampleRateWasApplied ? sampleRate : undefined,\n      },\n    };\n  }\n\n  /** Returns the sampler name or short description with the configuration. */\n  public toString(): string {\n    return 'SentrySampler';\n  }\n}\n\nfunction getParentSampled(parentSpan: Span, traceId: string, spanName: string): boolean | undefined {\n  const parentContext = parentSpan.spanContext();\n\n  // Only inherit sample rate if `traceId` is the same\n  // Note for testing: `isSpanContextValid()` checks the format of the traceId/spanId, so we need to pass valid ones\n  if (isSpanContextValid(parentContext) && parentContext.traceId === traceId) {\n    if (parentContext.isRemote) {\n      const parentSampled = getSamplingDecision(parentSpan.spanContext());\n      DEBUG_BUILD &&\n        debug.log(`[Tracing] Inheriting remote parent's sampled decision for ${spanName}: ${parentSampled}`);\n      return parentSampled;\n    }\n\n    const parentSampled = getSamplingDecision(parentContext);\n    DEBUG_BUILD && debug.log(`[Tracing] Inheriting parent's sampled decision for ${spanName}: ${parentSampled}`);\n    return parentSampled;\n  }\n\n  return undefined;\n}\n\n/**\n * Wrap a sampling decision with data that Sentry needs to work properly with it.\n * If you pass `decision: undefined`, it will be treated as `NOT_RECORDING`, but in contrast to passing `NOT_RECORDING`\n * it will not propagate this decision to downstream Sentry SDKs.\n */\nexport function wrapSamplingDecision({\n  decision,\n  context,\n  spanAttributes,\n  sampleRand,\n  downstreamTraceSampleRate,\n}: {\n  decision: SamplingDecision | undefined;\n  context: Context;\n  spanAttributes: SpanAttributes;\n  sampleRand?: number;\n  downstreamTraceSampleRate?: number;\n}): SamplingResult {\n  let traceState = getBaseTraceState(context, spanAttributes);\n\n  // We will override the propagated sample rate downstream when\n  // - the tracesSampleRate is applied\n  // - the tracesSampler is invoked\n  // Since unsampled OTEL spans (NonRecordingSpans) cannot hold attributes we need to store this on the (trace)context.\n  if (downstreamTraceSampleRate !== undefined) {\n    traceState = traceState.set(SENTRY_TRACE_STATE_SAMPLE_RATE, `${downstreamTraceSampleRate}`);\n  }\n\n  if (sampleRand !== undefined) {\n    traceState = traceState.set(SENTRY_TRACE_STATE_SAMPLE_RAND, `${sampleRand}`);\n  }\n\n  // If the decision is undefined, we treat it as NOT_RECORDING, but we don't propagate this decision to downstream SDKs\n  // Which is done by not setting `SENTRY_TRACE_STATE_SAMPLED_NOT_RECORDING` traceState\n  if (decision == undefined) {\n    return { decision: SamplingDecision.NOT_RECORD, traceState };\n  }\n\n  if (decision === SamplingDecision.NOT_RECORD) {\n    return { decision, traceState: traceState.set(SENTRY_TRACE_STATE_SAMPLED_NOT_RECORDING, '1') };\n  }\n\n  return { decision, traceState };\n}\n\nfunction getBaseTraceState(context: Context, spanAttributes: SpanAttributes): TraceStateInterface {\n  const parentSpan = trace.getSpan(context);\n  const parentContext = parentSpan?.spanContext();\n\n  let traceState = parentContext?.traceState || new TraceState();\n\n  // We always keep the URL on the trace state, so we can access it in the propagator\n  // `ATTR_URL_FULL` is the new attribute, but we still support the old one, `ATTR_HTTP_URL`, for now.\n  // eslint-disable-next-line deprecation/deprecation\n  const url = spanAttributes[SEMATTRS_HTTP_URL] || spanAttributes[ATTR_URL_FULL];\n  if (url && typeof url === 'string') {\n    traceState = traceState.set(SENTRY_TRACE_STATE_URL, url);\n  }\n\n  return traceState;\n}\n\n/**\n * If the active span is invalid, we want to ignore it as parent.\n * This aligns with how otel tracers and default samplers handle these cases.\n */\nfunction getValidSpan(context: Context): Span | undefined {\n  const span = trace.getSpan(context);\n  return span && isSpanContextValid(span.spanContext()) ? span : undefined;\n}\n"], "names": ["suppressTracing", "suppressTracingImpl"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,0EAAA,GACO,MAAM,0CAAA,GAA6C,uBAAuB;AAEjF,0EAAA;AACO,MAAM,2CAAA,GAA8C;ACF3D;;;;CAIA,GACO,SAAS,eAAe,CAAC,IAAI,EAAoC;IACtE,IAAI,cAAA,IAAkB,IAAI,EAAE;QAC1B,OAAO,IAAI,CAAC,YAAA;IAChB,OAAS,IAAI,mBAAA,IAAuB,IAAI,EAAE;QACtC,OAAO,AAAC,IAAI,CAAC,iBAAA,EAAuD,MAAM;IAC9E;IAEE,OAAO,SAAS;AAClB;ACVA;;;;CAIA,GACO,SAAS,iBAAiB,CAC/B,IAAI;IAEJ,MAAM,QAAA,GAAW,IAAA;IACjB,OAAO,CAAC,CAAC,QAAQ,CAAC,UAAA,IAAc,OAAO,QAAQ,CAAC,UAAA,KAAe,QAAQ;AACzE;AAEA;;;;CAIA,GACO,SAAS,WAAW,CAAgC,IAAI,EAAmD;IAChH,MAAM,QAAA,GAAW,IAAA;IACjB,OAAO,OAAO,QAAQ,CAAC,IAAA,KAAS,QAAQ;AAC1C;AAEA;;;;CAIA,GACO,SAAS,aAAa,CAC3B,IAAI;IAEJ,MAAM,QAAA,GAAW,IAAA;IACjB,OAAO,CAAC,CAAC,QAAQ,CAAC,MAAM;AAC1B;AAEA;;;;CAIA,GACO,SAAS,WAAW,CAAgC,IAAI,EAAiD;IAC9G,MAAM,QAAA,GAAW,IAAA;IACjB,OAAO,CAAC,CAAC,QAAQ,CAAC,IAAI;AACxB;AAEA;;;;CAIA,GACO,SAAS,eAAe,CAC7B,IAAI;IAEJ,MAAM,QAAA,GAAW,IAAA;IACjB,OAAO,CAAC,CAAC,eAAe,CAAC,QAAQ,CAAC;AACpC;AAEA;;;;CAIA,GACO,SAAS,aAAa,CAC3B,IAAI;IAEJ,MAAM,QAAA,GAAW,IAAA;IACjB,OAAO,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC;AACvC;AC3DA;;CAEA,GACO,SAAS,kBAAkB,CAAC,IAAI,EAAsD;IAC7F,kFAAA;IACE,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE;QAC5B,OAAO,CAAA,CAAE;IACb;IAEA,mDAAA;IACE,MAAM,iBAAA,GAAqB,IAAI,CAAC,UAAU,CAAC,6NAAa,CAAA,IAAK,IAAI,CAAC,UAAU,wNAAC,oBAAiB,CAAC;IAI/F,MAAM,IAAI,GAAkC;QAC1C,GAAG,EAAE,iBAAiB;QAC1B,mDAAA;QACI,aAAa,EAAG,IAAI,CAAC,UAAU,8MAAC,2BAAwB,CAAA,IAAK,IAAI,CAAC,UAAU,CAAC,8OAAoB,CAAC;IAGtG,CAAG;IAEH,iDAAA;IACE,IAAI,CAAC,IAAI,CAAC,aAAa,CAAA,IAAK,IAAI,CAAC,GAAG,EAAE;QACpC,IAAI,CAAC,aAAa,CAAA,GAAI,KAAK;IAC/B;IAEE,IAAI;QACF,IAAI,OAAO,iBAAA,KAAsB,QAAQ,EAAE;YACzC,MAAM,GAAA,qLAAM,WAAA,AAAQ,EAAC,iBAAiB,CAAC;YAEvC,IAAI,CAAC,GAAA,qLAAM,wBAAA,AAAqB,EAAC,GAAG,CAAC;YAErC,IAAI,GAAG,CAAC,MAAM,EAAE;gBACd,IAAI,CAAC,YAAY,CAAA,GAAI,GAAG,CAAC,MAAM;YACvC;YACM,IAAI,GAAG,CAAC,IAAI,EAAE;gBACZ,IAAI,CAAC,eAAe,CAAA,GAAI,GAAG,CAAC,IAAI;YACxC;QACA;IACA,EAAI,OAAM;IACV,SAAA;IACA;IAEE,OAAO,IAAI;AACb;AClDA,8EAAA;AACA,8FAAA;AACA,qDAAA,GAEA;;;;;;;CAOA,GACO,SAAS,gBAGd,WAAW,EAA6C;IAC1D,8HAAA;IACE,MAAM,mBAAA,SAA4B,WAAA,EAAoD;QAI7E,WAAW,CAAC,GAAG,IAAI,CAAS;YACjC,KAAK,CAAC,GAAG,IAAI,CAAC;QACpB;QAEA,yBAAA,GACW,IAAI,MAAM,GAAW;YAC1B,IAAI,IAAI,CAAC,OAAO,EAAE;gBAChB,OAAO,IAAI,CAAC,OAAO;YAC3B;YAEM,MAAM,IAAA,GAAO,uBAAuB;YACpC,MAAM,OAAA,qLAAU,cAAW;YAC3B,MAAM,MAAA,4LAAS,QAAK,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC;YAC7C,IAAI,CAAC,OAAA,GAAU,MAAM;YAErB,OAAO,MAAM;QACnB;QAEA;;KAEA,GACW,MAAM,KAAK,CAAC,OAAO,EAA6B;YACrD,MAAM,QAAA,GAAW,IAAI,CAAC,aAAa;YACnC,MAAM,QAAQ,EAAE,UAAU,EAAE;YAC5B,OAAO,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC;QACjC;IACA;IAEE,OAAO,mBAAA;AACT;AACA,oDAAA,GCtDA;;;;;CAKA,GACO,SAAS,WAAW,CAAC,IAAI,EAA0B;IACxD,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE;QACrB,OAAO,IAAI,CAAC,IAAI;IACpB;IAEE,gMAAO,WAAQ,CAAC,QAAQ;AAC1B;ACdO,MAAM,mBAAA,GAAsB,cAAc;AAC1C,MAAM,qBAAA,GAAwB,SAAS;AAEvC,MAAM,sBAAA,GAAyB,YAAY;AAC3C,MAAM,wCAAA,GAA2C,8BAA8B;AAC/E,MAAM,sBAAA,GAAyB,YAAY;AAC3C,MAAM,8BAAA,GAAiC,oBAAoB;AAC3D,MAAM,8BAAA,GAAiC,oBAAoB;AAE3D,MAAM,yBAAA,gMAA4B,mBAAA,AAAgB,EAAC,eAAe,CAAC;AAEnE,MAAM,uCAAA,gMAA0C,mBAAA,AAAgB,EAAC,6BAA6B,CAAC;AAE/F,MAAM,iCAAA,gMAAoC,mBAAA,AAAgB,EAAC,uBAAuB,CAAC;AAEnF,MAAM,2CAAA,+LAA8C,oBAAA,AAAgB,EAAC,iCAAiC,CAAC;ACX9G,MAAM,mBAAA,GAAsB,eAAe;AAE3C;;;CAGA,GACO,SAAS,oBAAoB,CAAC,OAAO,EAAsC;IAChF,OAAO,OAAO,CAAC,QAAQ,CAAC,yBAAyB,CAAA;AACnD;AAEA;;;CAGA,GACO,SAAS,kBAAkB,CAAC,OAAO,EAAW,MAAM,EAA0B;IACnF,OAAO,OAAO,CAAC,QAAQ,CAAC,yBAAyB,EAAE,MAAM,CAAC;AAC5D;AAEA;;;CAGA,GACO,SAAS,iBAAiB,CAAC,KAAK,EAAS,OAAO,EAAiB;IACtE,gNAAA,AAAwB,EAAC,KAAK,EAAE,mBAAmB,EAAE,OAAO,CAAC;AAC/D;AAEA;;CAEA,GACO,SAAS,mBAAmB,CAAC,KAAK,EAA8B;IACrE,OAAO,AAAC,KAAA,CAA8C,mBAAmB,CAAC;AAC5E;AChCA;;;;CAIA,GACO,SAAS,mBAAmB,CAAC,IAAI,EAAyB;IAC/D,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE;QAC5B,OAAO,KAAK;IAChB;IAEE,MAAM,EAAE,UAAA,EAAW,GAAI,IAAI;IAE7B,oGAAA;IACA,mDAAA;IACE,MAAM,OAAA,GAAU,UAAU,wNAAC,oBAAiB,CAAA,IAAK,UAAU,8MAAC,gBAAa,CAAC;IAE1E,IAAI,CAAC,OAAO,EAAE;QACZ,OAAO,KAAK;IAChB;IAEE,uMAAO,sBAAA,AAAkB,EAAC,OAAO,CAAC,QAAQ,EAAE,qLAAE,YAAA,AAAS,EAAE,CAAC;AAC5D;ACrBA;;;;;;CAMA,GACO,SAAS,mBAAmB,CAAC,WAAW,EAAoC;IACjF,MAAM,EAAE,UAAU,EAAE,UAAA,EAAW,GAAI,WAAW;IAE9C,MAAM,mBAAA,GAAsB,UAAA,GAAa,UAAU,CAAC,GAAG,CAAC,wCAAwC,CAAA,KAAM,GAAA,GAAM,KAAK;IAEnH,2DAAA;IACA,6GAAA;IACA,qGAAA;IACA,0BAAA;IACE,IAAI,UAAA,KAAe,sMAAU,CAAC,OAAO,EAAE;QACrC,OAAO,IAAI;IACf;IAEE,IAAI,mBAAmB,EAAE;QACvB,OAAO,KAAK;IAChB;IAEA,wEAAA;IACE,MAAM,SAAA,GAAY,UAAA,GAAa,UAAU,CAAC,GAAG,CAAC,sBAAsB,CAAA,GAAI,SAAS;IACjF,MAAM,GAAA,GAAM,SAAA,yLAAY,wCAAA,AAAqC,EAAC,SAAS,CAAA,GAAI,SAAS;IAEpF,IAAI,GAAG,EAAE,OAAA,KAAY,MAAM,EAAE;QAC3B,OAAO,IAAI;IACf;IACE,IAAI,GAAG,EAAE,OAAA,KAAY,OAAO,EAAE;QAC5B,OAAO,KAAK;IAChB;IAEE,OAAO,SAAS;AAClB;ACJA;;CAEA,GACO,SAAS,aAAa,CAAC,QAAQ,EAAU,UAAU,EAAkB,IAAI,EAA6B;IAC7G,sDAAA;IACA,mDAAA;IACE,MAAM,UAAA,GAAa,UAAU,8MAAC,2BAAwB,CAAA,IAAK,UAAU,wNAAC,uBAAoB,CAAC;IAC3F,IAAI,UAAU,EAAE;QACd,OAAO,wBAAwB,CAAC;YAAE,UAAU;YAAE,IAAI,EAAE,QAAQ;YAAE,IAAA;QAAA,CAAM,EAAE,UAAU,CAAC;IACrF;IAEA,mDAAA;IACE,MAAM,QAAA,GAAW,UAAU,wNAAC,qBAAkB,CAAC;IAC/C,MAAM,SAAA,GACJ,OAAO,UAAU,qLAAC,+BAA4B,CAAA,KAAM,QAAA,IACpD,UAAU,qLAAC,+BAA4B,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC;IAEjE,sDAAA;IACA,iFAAA;IACE,IAAI,QAAA,IAAY,CAAC,SAAS,EAAE;QAC1B,OAAO,sBAAsB,CAAC;YAAE,UAAU;YAAE,IAAI,EAAE,QAAA;QAAA,CAAU,CAAC;IACjE;IAEE,MAAM,mBAAA,GAAsB,UAAU,qLAAC,mCAAgC,CAAA,KAAM,QAAA,GAAW,QAAA,GAAW,OAAO;IAE5G,sDAAA;IACA,mDAAA;IACE,MAAM,UAAA,GAAa,UAAU,wNAAC,uBAAoB,CAAC;IACnD,IAAI,UAAU,EAAE;QACd,OAAO;YACL,GAAG,2BAA2B,CAAC,QAAQ,EAAE,UAAU,EAAE,OAAO,CAAC;YAC7D,EAAE,EAAE,KAAK;QACf,CAAK;IACL;IAEA,mEAAA;IACA,mDAAA;IACE,MAAM,eAAA,GAAkB,UAAU,wNAAC,4BAAyB,CAAC;IAC7D,IAAI,eAAe,EAAE;QACnB,OAAO;YACL,GAAG,2BAA2B,CAAC,QAAQ,EAAE,UAAU,EAAE,mBAAmB,CAAC;YACzE,EAAE,EAAE,SAAS;QACnB,CAAK;IACL;IAEA,oEAAA;IACA,mDAAA;IACE,MAAM,WAAA,GAAc,UAAU,wNAAC,wBAAqB,CAAC;IACrD,IAAI,WAAW,EAAE;QACf,OAAO;YACL,GAAG,2BAA2B,CAAC,QAAQ,EAAE,UAAU,EAAE,mBAAmB,CAAC;YACzE,EAAE,EAAE,WAAW,CAAC,QAAQ,EAAE;QAChC,CAAK;IACL;IAEE,OAAO;QAAE,EAAE,EAAE,SAAS;QAAE,WAAW,EAAE,QAAQ;QAAE,MAAM,EAAE;IAAA,CAAU;AACnE;AAEA;;;;;;;;CAQA,GACO,SAAS,oBAAoB,CAAC,IAAI,EAAiC;IACxE,MAAM,UAAA,GAAa,iBAAiB,CAAC,IAAI,CAAA,GAAI,IAAI,CAAC,UAAA,GAAa,CAAA,CAAE;IACjE,MAAM,IAAA,GAAO,WAAW,CAAC,IAAI,CAAA,GAAI,IAAI,CAAC,IAAA,GAAO,WAAW;IACxD,MAAM,IAAA,GAAO,WAAW,CAAC,IAAI,CAAC;IAE9B,OAAO,aAAa,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC;AAC9C;AAEA,SAAS,sBAAsB,CAAC,EAAE,UAAU,EAAE,IAAA,EAAM,EAA6D;IACjH,8EAAA;IACE,MAAM,eAAA,GAAkB,UAAU,qLAAC,6CAA0C,CAAC;IAC9E,IAAI,OAAO,eAAA,KAAoB,QAAQ,EAAE;QACvC,OAAO;YACL,EAAE,EAAE,IAAI;YACR,WAAW,EAAE,eAAe;YAC5B,MAAM,EAAE,AAAC,UAAU,qLAAC,mCAAgC,CAAA,IAA2B,QAAQ;QAC7F,CAAK;IACL;IAEA,2GAAA;IACE,IAAI,UAAU,qLAAC,mCAAgC,CAAA,KAAM,QAAQ,EAAE;QAC7D,OAAO;YAAE,EAAE,EAAE,IAAI;YAAE,WAAW,EAAE,IAAI;YAAE,MAAM,EAAE;QAAA,CAAU;IAC5D;IAEA,0EAAA;IACA,mDAAA;IACE,MAAM,SAAA,GAAY,UAAU,wNAAC,wBAAqB,CAAC;IAEnD,MAAM,WAAA,GAAc,SAAA,GAAY,SAAS,CAAC,QAAQ,EAAC,GAAI,IAAI;IAE3D,OAAO;QAAE,EAAE,EAAE,IAAI;QAAE,WAAW;QAAE,MAAM,EAAE,MAAA;IAAA,CAAQ;AAClD;AAEA,6BAAA,GACO,SAAS,wBAAwB,CACtC,EAAE,IAAI,EAAE,IAAI,EAAE,UAAA,EAAY,EAC1B,UAAU;IAEV,MAAM,OAAA,GAAU;QAAC,MAAM;KAAC;IAExB,OAAQ,IAAI;QACV,KAAK,oMAAQ,CAAC,MAAM;YAClB,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC;YACtB;QACF,8LAAK,WAAQ,CAAC,MAAM;YAClB,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC;YACtB;IACN;IAEA,+GAAA;IACE,IAAI,UAAU,CAAC,sBAAsB,CAAC,EAAE;QACtC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC;IAC5B;IAEE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAA,EAAA,GAAa,eAAe,CAAC,UAAU,EAAE,IAAI,CAAC;IAErF,IAAI,CAAC,OAAO,EAAE;QACZ,OAAO;YAAE,GAAG,2BAA2B,CAAC,IAAI,EAAE,UAAU,CAAC;YAAE,EAAE,EAAE,OAAO,CAAC,IAAI,CAAC,GAAG;QAAA,CAAG;IACtF;IAEE,MAAM,0BAAA,GAA6B,UAAU,CAAC,2CAA2C,CAAC;IAE5F,qBAAA;IACE,MAAM,eAAA,GAAkB,CAAC,EAAA,UAAA,CAAA,CAAA,EAAA,OAAA,CAAA,CAAA;IAEA,2EAAA;IACA,yCAAA;IACA,MAAA,mBAAA,GAAA,6BACA,CAAA,EAAA,eAAA,CAAA,EAAA,EAAA,qCAAA,CAAA,0BAAA,CAAA,CAAA,CAAA,CAAA,GACA,eAAA;IAEA,wFAAA;IACA,MAAA,cAAA,GAAA,QAAA,IAAA,OAAA,KAAA,GAAA,GAAA,OAAA,GAAA,KAAA;IAEA,MAAA,IAAA,GAAA,CAAA,CAAA;IAEA,IAAA,GAAA,EAAA;QACA,IAAA,CAAA,GAAA,GAAA,GAAA;IACA;IACA,IAAA,KAAA,EAAA;QACA,IAAA,CAAA,YAAA,CAAA,GAAA,KAAA;IACA;IACA,IAAA,QAAA,EAAA;QACA,IAAA,CAAA,eAAA,CAAA,GAAA,QAAA;IACA;IAEA,0EAAA;IACA,0GAAA;IACA,MAAA,oBAAA,GAAA,IAAA,8LAAA,WAAA,CAAA,MAAA,IAAA,IAAA,8LAAA,WAAA,CAAA,MAAA;IAEA,4EAAA;IACA,mCAAA;IACA,uFAAA;IACA,MAAA,MAAA,GAAA,UAAA,qLAAA,mCAAA,CAAA,IAAA,QAAA;IACA,MAAA,YAAA,GAAA,CAAA,CAAA,EAAA,MAAA,CAAA,CAAA,CAAA,UAAA,CAAA,MAAA,CAAA;IAEA,gGAAA;IACA,MAAA,sBAAA,GAAA,UAAA,CAAA,uNAAA,CAAA,KAAA,QAAA;IACA,MAAA,cAAA,GAAA,UAAA,qLAAA,6CAAA,CAAA;IAEA,MAAA,sBAAA,GACA,CAAA,sBAAA,IAAA,cAAA,IAAA,IAAA,IAAA,CAAA,oBAAA,IAAA,CAAA,YAAA,CAAA;IAEA,MAAA,EAAA,WAAA,EAAA,MAAA,EAAA,GAAA,yBACA;QAAA,WAAA,EAAA,mBAAA;QAAA,MAAA,EAAA,cAAA;IAAA,IACA,2BAAA,CAAA,IAAA,EAAA,UAAA,CAAA;IAEA,OAAA;QACA,EAAA,EAAA,OAAA,CAAA,IAAA,CAAA,GAAA,CAAA;QACA,WAAA;QACA,MAAA;QACA,IAAA;IACA,CAAA;AACA;AAEA,SAAA,qCAAA,CAAA,IAAA,EAAA;IACA,IAAA,KAAA,CAAA,OAAA,CAAA,IAAA,CAAA,EAAA;QACA,MAAA,MAAA,GAAA,IAAA,CAAA,KAAA,EAAA,CAAA,IAAA,EAAA;QAEA,yCAAA;QACA,IAAA,MAAA,CAAA,MAAA,IAAA,CAAA,EAAA;YACA,OAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA;QACA,CAAA,MAAA;YACA,4DAAA;YACA,OAAA,CAAA,EAAA,MAAA,CAAA,KAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,GAAA,EAAA,MAAA,CAAA,MAAA,GAAA,CAAA,CAAA,CAAA;QACA;IACA;IAEA,OAAA,CAAA,EAAA,IAAA,CAAA,CAAA;AACA;AAEA,4BAAA,GACA,SAAA,eAAA,CACA,UAAA,EACA,IAAA;IAQA,kDAAA;IACA,mDAAA;IACA,MAAA,UAAA,GAAA,UAAA,wNAAA,uBAAA,CAAA;IACA,iGAAA;IACA,mDAAA;IACA,MAAA,OAAA,GAAA,UAAA,CAAA,2OAAA,CAAA,IAAA,UAAA,8MAAA,gBAAA,CAAA;IACA,mEAAA;IACA,MAAA,SAAA,GAAA,UAAA,8MAAA,kBAAA,CAAA;IAEA,MAAA,SAAA,GAAA,OAAA,OAAA,KAAA,QAAA,GAAA,6LAAA,EAAA,OAAA,CAAA,GAAA,SAAA;IACA,MAAA,GAAA,GAAA,SAAA,qLAAA,wBAAA,EAAA,SAAA,CAAA,GAAA,SAAA;IACA,MAAA,KAAA,GAAA,SAAA,EAAA,MAAA,IAAA,SAAA;IACA,MAAA,QAAA,GAAA,SAAA,EAAA,IAAA,IAAA,SAAA;IAEA,IAAA,OAAA,SAAA,KAAA,QAAA,EAAA;QACA,OAAA;YAAA,OAAA,EAAA,SAAA;YAAA,GAAA;YAAA,KAAA;YAAA,QAAA;YAAA,QAAA,EAAA,IAAA;QAAA,CAAA;IACA;IAEA,IAAA,IAAA,8LAAA,WAAA,CAAA,MAAA,IAAA,OAAA,UAAA,KAAA,QAAA,EAAA;QACA,OAAA;YAAA,OAAA,oLAAA,2BAAA,EAAA,UAAA,CAAA;YAAA,GAAA;YAAA,KAAA;YAAA,QAAA;YAAA,QAAA,EAAA,KAAA;QAAA,CAAA;IACA;IAEA,IAAA,SAAA,EAAA;QACA,OAAA;YAAA,OAAA,EAAA,GAAA;YAAA,GAAA;YAAA,KAAA;YAAA,QAAA;YAAA,QAAA,EAAA,KAAA;QAAA,CAAA;IACA;IAEA,kEAAA;IACA,IAAA,OAAA,UAAA,KAAA,QAAA,EAAA;QACA,OAAA;YAAA,OAAA,oLAAA,2BAAA,EAAA,UAAA,CAAA;YAAA,GAAA;YAAA,KAAA;YAAA,QAAA;YAAA,QAAA,EAAA,KAAA;QAAA,CAAA;IACA;IAEA,OAAA;QAAA,OAAA,EAAA,SAAA;QAAA,GAAA;QAAA,KAAA;QAAA,QAAA;QAAA,QAAA,EAAA,KAAA;IAAA,CAAA;AACA;AAEA;;;;;;;;;;;CAWA,GACA,SAAA,2BAAA,CACA,YAAA,EACA,UAAA,EACA,cAAA,GAAA,QAAA;IAKA,MAAA,MAAA,GAAA,UAAA,qLAAA,mCAAA,CAAA,IAAA,cAAA;IACA,MAAA,WAAA,GAAA,UAAA,qLAAA,6CAAA,CAAA;IAEA,IAAA,WAAA,IAAA,OAAA,WAAA,KAAA,QAAA,EAAA;QACA,OAAA;YACA,WAAA;YACA,MAAA;QACA,CAAA;IACA;IAEA,OAAA;QAAA,WAAA,EAAA,YAAA;QAAA,MAAA;IAAA,CAAA;AACA;ACjT3B;;;CAGA,GACO,SAAS,uCAAuC,CAAC,MAAM,EAAgB;IAC5E,MAAM,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,QAAQ,KAAK;QACxC,IAAI,CAAC,QAAQ,EAAE;YACb;QACN;QAEA,qFAAA;QACA,sFAAA;QACA,mEAAA;QACA,oFAAA;QACA,iCAAA;QAEI,MAAM,QAAA,2LAAW,aAAA,AAAU,EAAC,QAAQ,CAAC;QACrC,MAAM,UAAA,GAAa,QAAQ,CAAC,IAAI;QAChC,MAAM,MAAA,GAAS,UAAU,qLAAC,mCAAgC,CAAC;QAE3D,MAAM,EAAE,WAAA,EAAA,GAAgB,WAAW,CAAC,QAAQ,CAAA,GAAI,oBAAoB,CAAC,QAAQ,CAAA,GAAI;YAAE,WAAW,EAAE;QAAA,CAAW;QAC3G,IAAI,MAAA,KAAW,KAAA,IAAS,WAAW,EAAE;YACnC,GAAG,CAAC,WAAA,GAAc,WAAW;QACnC;QAEA,sDAAA;QACA,uEAAA;QACA,uHAAA;QACI,QAAI,4MAAA,AAAe,EAAE,GAAE;YACrB,MAAM,OAAA,GAAU,mBAAmB,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;YAC3D,GAAG,CAAC,OAAA,GAAU,OAAA,IAAW,SAAA,GAAY,SAAA,GAAY,MAAM,CAAC,OAAO,CAAC;QACtE;IACA,CAAG,CAAC;AACJ;ACpCA;;CAEA,GACO,SAAS,aAAa,GAAqB;IAChD,gMAAO,QAAK,CAAC,aAAa,EAAE;AAC9B;ACNA;;;;CAIA,GACO,MAAM,WAAA,GAAc,OAAA,gBAAA,KAAA,WAAA,IAAA,gBAAA,CAAe;ACF1C;;CAEA,GACO,SAAS,cAAc,CAAC,EAC7B,GAAG,EACH,OAAO,EACT;IAIA,2DAAA;IACE,MAAM,SAAA,GAAY,GAAA,yLAAM,8CAAA,AAA2C,EAAC,GAAG,CAAA,GAAI,SAAS;IAEpF,MAAM,cAAA,GAAiB,gMAAI,aAAU,EAAE;IAEvC,MAAM,iBAAA,GAAoB,SAAA,GAAY,cAAc,CAAC,GAAG,CAAC,sBAAsB,EAAE,SAAS,CAAA,GAAI,cAAc;IAE9G,6EAAA;IACA,iDAAA;IACE,OAAO,OAAA,KAAY,KAAA,GAAQ,iBAAiB,CAAC,GAAG,CAAC,wCAAwC,EAAE,GAAG,CAAA,GAAI,iBAAiB;AACrH;ACvBA,MAAM,aAAA,GAAgB,IAAI,GAAG,EAAwB;AAErD,8DAAA,GACO,SAAS,uBAAuB,GAA2B;IAChE,OAAO,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC;AAClC;AAEA,4CAAA,GACO,SAAS,UAAU,CAAC,OAAO,EAA8B;IAC9D,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC;AAC5B;ACeA;;CAEA,GACO,MAAM,gBAAA,gOAAyB,uBAAA,CAAqB;IAC3D,4FAAA,GAGS,WAAW,EAAG;QACnB,KAAK,EAAE;QACP,UAAU,CAAC,kBAAkB,CAAC;QAElC,6FAAA;QACI,IAAI,CAAC,qBAAA,GAAwB,kLAAI,SAAM,CAAkB,GAAG,CAAC;IACjE;IAEA;;GAEA,GACS,MAAM,CAAC,OAAO,EAAW,OAAO,EAAW,MAAM,EAAuB;QAC7E,6MAAI,sBAAA,AAAmB,EAAC,OAAO,CAAC,EAAE;YAChC,wMAAe,SAAK,CAAC,GAAG,CAAC,2EAA2E,CAAC;YACrG;QACN;QAEI,MAAM,sMAAa,QAAK,CAAC,OAAO,CAAC,OAAO,CAAC;QACzC,MAAM,MAAM,UAAA,IAAc,aAAa,CAAC,UAAU,CAAC;QAEnD,MAAM,uBAAA,sLAA0B,YAAA,AAAS,EAAE,GAAE,UAAU,EAAE,EAAE,uBAAuB;QAClF,IAAI,CAAC,0BAA0B,CAAC,GAAG,EAAE,uBAAuB,EAAE,IAAI,CAAC,qBAAqB,CAAC,EAAE;YACzF,WAAA,8LACE,QAAK,CAAC,GAAG,CAAC,+FAA+F,EAAE,GAAG,CAAC;YACjH;QACN;QAEI,MAAM,qBAAA,GAAwB,kBAAkB,CAAC,OAAO,CAAC;QACzD,IAAI,OAAA,GAAU,uMAAW,CAAC,UAAU,CAAC,OAAO,CAAA,6LAAK,cAAW,CAAC,aAAa,CAAC,CAAA,CAAE,CAAC;QAE9E,MAAM,EAAE,sBAAsB,EAAE,OAAO,EAAE,MAAM,EAAE,OAAA,EAAQ,GAAI,gBAAgB,CAAC,OAAO,CAAC;QAEtF,IAAI,qBAAqB,EAAE;YACzB,MAAM,cAAA,yLAAiB,qBAAA,AAAkB,EAAC,qBAAqB,CAAC;YAEhE,IAAI,cAAc,EAAE;gBAClB,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK;oBACvD,OAAA,GAAU,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE;wBAAE,KAAA;oBAAA,CAAO,CAAC;gBACpD,CAAS,CAAC;YACV;QACA;QAEI,IAAI,sBAAsB,EAAE;YAC1B,OAAA,GAAU,MAAM,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC,MAAM,CAAU,CAAC,CAAC,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC,KAAK;gBAC1F,IAAI,QAAQ,EAAE;oBACZ,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,oLAAA,4BAAA,CAAA,EAAA,MAAA,CAAA,CAAA,EAAA;wBAAA,KAAA,EAAA,QAAA;oBAAA,CAAA,CAAA;gBACA;gBACA,OAAA,CAAA;YACA,CAAA,EAAA,OAAA,CAAA;QACA;QAEA,8FAAA;QACA,IAAA,OAAA,IAAA,OAAA,8LAAA,kBAAA,EAAA;YACA,MAAA,CAAA,GAAA,CAAA,OAAA,EAAA,mBAAA,wLAAA,4BAAA,EAAA,OAAA,EAAA,MAAA,EAAA,OAAA,CAAA,CAAA;QACA;QAEA,KAAA,CAAA,MAAA,0LAAA,cAAA,CAAA,UAAA,CAAA,OAAA,EAAA,OAAA,CAAA,EAAA,OAAA,EAAA,MAAA,CAAA;IACA;IAEA;;GAEA,GACA,OAAA,CAAA,OAAA,EAAA,OAAA,EAAA,MAAA,EAAA;QACA,MAAA,sBAAA,GAAA,MAAA,CAAA,GAAA,CAAA,OAAA,EAAA,mBAAA,CAAA;QACA,MAAA,OAAA,GAAA,MAAA,CAAA,GAAA,CAAA,OAAA,EAAA,qBAAA,CAAA;QAEA,MAAA,WAAA,GAAA,yBACA,KAAA,CAAA,OAAA,CAAA,sBAAA,IACA,sBAAA,CAAA,CAAA,CAAA,GACA,yBACA,SAAA;QAEA,iCAAA;QACA,oEAAA;QACA,OAAA,qBAAA,CAAA,8BAAA,CAAA,OAAA,EAAA;YAAA,WAAA;YAAA,OAAA;QAAA,CAAA,CAAA,CAAA;IACA;IAEA;;GAEA,GACA,MAAA,GAAA;QACA,OAAA;YAAA,mBAAA;YAAA,qBAAA;SAAA;IACA;AACA;AAEA,MAAA,sBAAA,GACA,+FAAA;AAEA;;;;CAIA,GACA,SAAA,0BAAA,CACA,GAAA,EACA,uBAAA,EACA,WAAA;IAEA,IAAA,OAAA,GAAA,KAAA,QAAA,IAAA,CAAA,uBAAA,EAAA;QACA,OAAA,IAAA;IACA;IAEA,MAAA,cAAA,GAAA,WAAA,EAAA,GAAA,CAAA,GAAA,CAAA;IACA,IAAA,cAAA,KAAA,SAAA,EAAA;QACA,WAAA,IAAA,CAAA,cAAA,8LAAA,QAAA,CAAA,GAAA,CAAA,sBAAA,EAAA,GAAA,CAAA;QACA,OAAA,cAAA;IACA;IAEA,MAAA,QAAA,wLAAA,2BAAA,EAAA,GAAA,EAAA,uBAAA,CAAA;IACA,WAAA,EAAA,GAAA,CAAA,GAAA,EAAA,QAAA,CAAA;IAEA,WAAA,IAAA,CAAA,QAAA,8LAAA,QAAA,CAAA,GAAA,CAAA,sBAAA,EAAA,GAAA,CAAA;IACA,OAAA,QAAA;AACA;AAEA;;;CAGA,GACA,SAAA,gBAAA,CACA,OAAA,EACA,OAAA,GAAA,CAAA,CAAA;IAOA,MAAA,IAAA,4LAAA,QAAA,CAAA,OAAA,CAAA,OAAA,CAAA;IAEA,mGAAA;IACA,+DAAA;IACA,IAAA,IAAA,EAAA,WAAA,EAAA,CAAA,QAAA,EAAA;QACA,MAAA,WAAA,GAAA,IAAA,CAAA,WAAA,EAAA;QACA,MAAA,sBAAA,0MAAA,oCAAA,EAAA,IAAA,CAAA;QAEA,OAAA;YACA,sBAAA;YACA,OAAA,EAAA,WAAA,CAAA,OAAA;YACA,MAAA,EAAA,SAAA;YACA,OAAA,EAAA,mBAAA,CAAA,WAAA,CAAA;QACA,CAAA;IACA;IAEA,4CAAA;IACA,IAAA,IAAA,EAAA;QACA,MAAA,WAAA,GAAA,IAAA,CAAA,WAAA,EAAA;QACA,MAAA,sBAAA,0MAAA,oCAAA,EAAA,IAAA,CAAA;QAEA,OAAA;YACA,sBAAA;YACA,OAAA,EAAA,WAAA,CAAA,OAAA;YACA,MAAA,EAAA,WAAA,CAAA,MAAA;YACA,OAAA,EAAA,mBAAA,CAAA,WAAA,CAAA;QACA,CAAA;IACA;IAEA,4DAAA;IACA,mGAAA;IACA,MAAA,KAAA,GAAA,OAAA,CAAA,KAAA,IAAA,oBAAA,CAAA,OAAA,CAAA,EAAA,KAAA,uLAAA,kBAAA,EAAA;IACA,MAAA,MAAA,GAAA,OAAA,CAAA,MAAA,uLAAA,YAAA,EAAA;IAEA,MAAA,kBAAA,GAAA,KAAA,CAAA,qBAAA,EAAA;IACA,MAAA,sBAAA,GAAA,MAAA,OAAA,wOAAA,EAAA,MAAA,EAAA,KAAA,CAAA,GAAA,SAAA;IACA,OAAA;QACA,sBAAA;QACA,OAAA,EAAA,kBAAA,CAAA,OAAA;QACA,MAAA,EAAA,kBAAA,CAAA,iBAAA;QACA,OAAA,EAAA,kBAAA,CAAA,OAAA;IACA,CAAA;AACA;AAEA,SAAA,8BAAA,CACA,GAAA,EACA,EAAA,WAAA,EAAA,OAAA,EAAA;IAEA,MAAA,kBAAA,wLAAA,iCAAA,EAAA,WAAA,EAAA,OAAA,CAAA;IAEA,MAAA,EAAA,OAAA,EAAA,YAAA,EAAA,OAAA,EAAA,GAAA,EAAA,GAAA,kBAAA;IAEA,6EAAA;IACA,iFAAA;IACA,IAAA,CAAA,YAAA,EAAA;QACA,OAAA,GAAA;IACA;IAEA,MAAA,WAAA,GAAA,yBAAA,CAAA;QACA,OAAA;QACA,MAAA,EAAA,YAAA;QACA,OAAA;QACA,GAAA;IACA,CAAA,CAAA;IAEA,OAAA,iMAAA,CAAA,cAAA,CAAA,GAAA,EAAA,WAAA,CAAA;AACA;AAEA;;;CAGA,GACA,SAAA,yBAAA,CACA,GAAA,EACA,OAAA,EACA,QAAA;IAEA,MAAA,kBAAA,GAAA,qBAAA,CAAA,8BAAA,CAAA,GAAA,EAAA,OAAA,CAAA,CAAA;IAEA,gMAAA,UAAA,CAAA,IAAA,CAAA,kBAAA,EAAA,QAAA,CAAA;AACA;AAEA,SAAA,qBAAA,CAAA,GAAA,EAAA;IACA,iEAAA;IACA,MAAA,MAAA,GAAA,oBAAA,CAAA,GAAA,CAAA;IACA,MAAA,SAAA,GAAA;QACA,8GAAA;QACA,8FAAA;QACA,KAAA,EAAA,MAAA,GAAA,MAAA,CAAA,KAAA,sLAAA,kBAAA,EAAA,EAAA,KAAA,EAAA;QACA,cAAA,EAAA,MAAA,GAAA,MAAA,CAAA,cAAA,sLAAA,oBAAA,EAAA;IACA,CAAA;IAEA,OAAA,kBAAA,CAAA,GAAA,EAAA,SAAA,CAAA;AACA;AAEA,oEAAA,GACA,SAAA,kBAAA,CAAA,OAAA,EAAA;IACA,IAAA;QACA,MAAA,OAAA,GAAA,OAAA,CAAA,qBAAA,CAAA;QACA,OAAA,KAAA,CAAA,OAAA,CAAA,OAAA,CAAA,GAAA,OAAA,CAAA,IAAA,CAAA,GAAA,CAAA,GAAA,OAAA;IACA,CAAA,CAAA,OAAA;QACA,OAAA,SAAA;IACA;AACA;AAEA;;;;;;;;CAQA,GACA,SAAA,aAAA,CAAA,IAAA,EAAA;IACA,MAAA,QAAA,2LAAA,aAAA,EAAA,IAAA,CAAA,CAAA,IAAA;IACA,wGAAA;IACA,mDAAA;IACA,MAAA,YAAA,GAAA,QAAA,wNAAA,oBAAA,CAAA,IAAA,QAAA,8MAAA,gBAAA,CAAA;IACA,IAAA,OAAA,YAAA,KAAA,QAAA,EAAA;QACA,OAAA,YAAA;IACA;IAEA,wFAAA;IACA,MAAA,aAAA,GAAA,IAAA,CAAA,WAAA,EAAA,CAAA,UAAA,EAAA,GAAA,CAAA,sBAAA,CAAA;IACA,IAAA,aAAA,EAAA;QACA,OAAA,aAAA;IACA;IAEA,OAAA,SAAA;AACA;AAEA,SAAA,yBAAA,CAAA,EACA,MAAA,EACA,OAAA,EACA,OAAA,EACA,GAAA,EACA;IAMA,2DAAA;IACA,MAAA,UAAA,GAAA,cAAA,CAAA;QACA,GAAA;QACA,OAAA;IACA,CAAA,CAAA;IAEA,MAAA,WAAA,GAAA;QACA,OAAA;QACA,MAAA;QACA,QAAA,EAAA,IAAA;QACA,UAAA,EAAA,OAAA,4LAAA,aAAA,CAAA,OAAA,4LAAA,aAAA,CAAA,IAAA;QACA,UAAA;IACA,CAAA;IAEA,OAAA,WAAA;AACA;AClS7B;;;;;;;;;CASA,GACO,SAAS,SAAS,CAAI,OAAO,EAA4B,QAAQ,EAAwB;IAC9F,MAAM,MAAA,GAAS,SAAS,EAAE;IAE1B,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,gBAAA,EAAiB,GAAI,OAAO;IAExD,uFAAA;IACE,MAAM,OAAA,GAAU,oBAAoB,CAAI,gBAAgB,CAAC;IAEzD,OAAO,OAAO,CAAC,MAAM;QACnB,MAAM,SAAA,GAAY,UAAU,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,gBAAgB,CAAC;QACrE,MAAM,cAAA,GAAiB,OAAO,CAAC,YAAA,IAAgB,0LAAC,QAAK,CAAC,OAAO,CAAC,SAAS,CAAC;QACxE,MAAM,GAAA,GAAM,cAAA,4MAAiBA,kBAAAA,AAAe,EAAC,SAAS,CAAA,GAAI,SAAS;QAEnE,MAAM,WAAA,GAAc,cAAc,CAAC,OAAO,CAAC;QAE3C,OAAO,MAAM,CAAC,eAAe,CAAC,IAAI,EAAE,WAAW,EAAE,GAAG,GAAE,IAAA,IAAQ;YAC5D,0MAAO,uBAAA,AAAoB,EACzB,IAAM,QAAQ,CAAC,IAAI,CAAC,EACpB,MAAM;gBACd,6HAAA;gBACU,KAAI,oMAAA,AAAU,EAAC,IAAI,CAAC,CAAC,MAAA,KAAW,SAAS,EAAE;oBACzC,IAAI,CAAC,SAAS,CAAC;wBAAE,IAAI,2LAAE,iBAAc,CAAC,KAAA;oBAAA,CAAO,CAAC;gBAC1D;YACA,CAAS,EACD,IAAM,IAAI,CAAC,GAAG,EAAE;QAExB,CAAK,CAAC;IACN,CAAG,CAAC;AACJ;AAEA;;;;;;;;;CASA,GACO,SAAS,eAAe,CAC7B,OAAO,EACP,QAAQ;IAER,MAAM,MAAA,GAAS,SAAS,EAAE;IAE1B,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,gBAAA,EAAiB,GAAI,OAAO;IAExD,uFAAA;IACE,MAAM,OAAA,GAAU,oBAAoB,CAAI,gBAAgB,CAAC;IAEzD,OAAO,OAAO,CAAC,MAAM;QACnB,MAAM,SAAA,GAAY,UAAU,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,gBAAgB,CAAC;QACrE,MAAM,cAAA,GAAiB,OAAO,CAAC,YAAA,IAAgB,0LAAC,QAAK,CAAC,OAAO,CAAC,SAAS,CAAC;QACxE,MAAM,GAAA,GAAM,cAAA,4MAAiBA,kBAAAA,AAAe,EAAC,SAAS,CAAA,GAAI,SAAS;QAEnE,MAAM,WAAA,GAAc,cAAc,CAAC,OAAO,CAAC;QAE3C,OAAO,MAAM,CAAC,eAAe,CAAC,IAAI,EAAE,WAAW,EAAE,GAAG,GAAE,IAAA,IAAQ;YAC5D,0MAAO,uBAAA,AAAoB,EACzB,IAAM,QAAQ,CAAC,IAAI,EAAE,IAAM,IAAI,CAAC,GAAG,EAAE,CAAC,EACtC,MAAM;gBACd,6HAAA;gBACU,2LAAI,cAAA,AAAU,EAAC,IAAI,CAAC,CAAC,MAAA,KAAW,SAAS,EAAE;oBACzC,IAAI,CAAC,SAAS,CAAC;wBAAE,IAAI,2LAAE,iBAAc,CAAC,KAAA;oBAAA,CAAO,CAAC;gBAC1D;YACA,CAAS;QAET,CAAK,CAAC;IACN,CAAG,CAAC;AACJ;AAEA;;;;;;;;CAQA,GACO,SAAS,iBAAiB,CAAC,OAAO,EAAkC;IACzE,MAAM,MAAA,GAAS,SAAS,EAAE;IAE1B,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,gBAAA,EAAiB,GAAI,OAAO;IAExD,uFAAA;IACE,MAAM,OAAA,GAAU,oBAAoB,CAAO,gBAAgB,CAAC;IAE5D,OAAO,OAAO,CAAC,MAAM;QACnB,MAAM,SAAA,GAAY,UAAU,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,gBAAgB,CAAC;QACrE,MAAM,cAAA,GAAiB,OAAO,CAAC,YAAA,IAAgB,0LAAC,QAAK,CAAC,OAAO,CAAC,SAAS,CAAC;QACxE,MAAM,GAAA,GAAM,cAAA,GAAiBA,2NAAAA,AAAe,EAAC,SAAS,CAAA,GAAI,SAAS;QAEnE,MAAM,WAAA,GAAc,cAAc,CAAC,OAAO,CAAC;QAE3C,MAAM,IAAA,GAAO,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,WAAW,EAAE,GAAG,CAAC;QAErD,OAAO,IAAI;IACf,CAAG,CAAC;AACJ;AAEA;;;;;;;;CAQA,GACO,SAAS,cAAc,CAAI,IAAI,EAAe,QAAQ,EAA0B;IACrF,MAAM,wBAAA,GAA2B,IAAA,4LAAO,QAAK,CAAC,OAAO,0LAAC,UAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAA,4LAAI,QAAK,CAAC,UAAU,0LAAC,UAAO,CAAC,MAAM,EAAE,CAAC;IAClH,gMAAO,UAAO,CAAC,IAAI,CAAC,wBAAwB,EAAE,IAAM,QAAQ,oLAAC,kBAAA,AAAe,EAAE,CAAC,CAAC;AAClF;AAEA,SAAS,SAAS,GAAW;IAC3B,MAAM,MAAA,OAAS,2LAAA,AAAS,EAAgC;IACxD,OAAO,MAAM,EAAE,MAAA,6LAAU,QAAK,CAAC,SAAS,CAAC,uBAAuB,oLAAE,cAAW,CAAC;AAChF;AAEA,SAAS,cAAc,CAAC,OAAO,EAAyC;IACtE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE,EAAE,KAAA,EAAM,GAAI,OAAO;IAE5D,6CAAA;IACE,MAAM,cAAA,GAAiB,OAAO,SAAA,KAAc,QAAA,GAAW,6BAA6B,CAAC,SAAS,CAAA,GAAI,SAAS;IAE3G,OAAO;QACL,UAAU,EAAE,KACR;YACE,qLAAC,+BAA4B,CAAA,EAAG,EAAE;YAClC,GAAG,UAAU;QACvB,IACQ,UAAU;QACd,IAAI;QACJ,KAAK;QACL,SAAS,EAAE,cAAc;IAC7B,CAAG;AACH;AAEA,SAAS,6BAA6B,CAAC,SAAS,EAAkB;IAChE,MAAM,IAAA,GAAO,SAAA,GAAY,UAAU;IACnC,OAAO,IAAA,GAAO,YAAY,IAAA,GAAO,SAAS;AAC5C;AAEA,SAAS,UAAU,CAAC,KAAK,EAAqB,gBAAgB,EAAgC;IAC5F,MAAM,GAAA,GAAM,kBAAkB,CAAC,KAAK,CAAC;IACrC,MAAM,sMAAa,QAAK,CAAC,OAAO,CAAC,GAAG,CAAC;IAEvC,gEAAA;IACA,uFAAA;IACE,IAAI,CAAC,UAAU,EAAE;QACf,OAAO,GAAG;IACd;IAEA,sGAAA;IACE,IAAI,CAAC,gBAAgB,EAAE;QACrB,OAAO,GAAG;IACd;IAEA,0GAAA;IAEA,kCAAA;IACA,6EAAA;IACA,uFAAA;IACE,MAAM,0MAAiB,QAAK,CAAC,UAAU,CAAC,GAAG,CAAC;IAE5C,MAAM,EAAE,MAAM,EAAE,OAAA,EAAQ,GAAI,UAAU,CAAC,WAAW,EAAE;IACpD,MAAM,OAAA,GAAU,mBAAmB,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC;IAE/D,2GAAA;IACA,sDAAA;IACE,MAAM,QAAA,2LAAW,cAAA,AAAW,EAAC,UAAU,CAAC;IACxC,MAAM,GAAA,0MAAM,oCAAA,AAAiC,EAAC,QAAQ,CAAC;IAEvD,MAAM,UAAA,GAAa,cAAc,CAAC;QAChC,GAAG;QACH,OAAO;IACX,CAAG,CAAC;IAEF,MAAM,WAAW,GAAgB;QAC/B,OAAO;QACP,MAAM;QACN,QAAQ,EAAE,IAAI;QACd,UAAU,EAAE,OAAA,GAAU,sMAAU,CAAC,OAAA,4LAAU,aAAU,CAAC,IAAI;QAC1D,UAAU;IACd,CAAG;IAED,MAAM,kBAAA,4LAAqB,QAAK,CAAC,cAAc,CAAC,cAAc,EAAE,WAAW,CAAC;IAE5E,OAAO,kBAAkB;AAC3B;AAEA,SAAS,kBAAkB,CAAC,KAAK,EAAmB;IAClD,IAAI,KAAK,EAAE;QACT,MAAM,GAAA,GAAM,mBAAmB,CAAC,KAAK,CAAC;QACtC,IAAI,GAAG,EAAE;YACP,OAAO,GAAG;QAChB;IACA;IAEE,gMAAO,UAAO,CAAC,MAAM,EAAE;AACzB;AAEA;;;;;;;;;;CAUA,GACO,SAAS,aAAa,CAAI,OAAO,EAA2C,QAAQ,EAAc;IACvG,OAAO,yBAAyB,0LAAC,UAAO,CAAC,MAAM,EAAE,EAAE,OAAO,EAAE,QAAQ,CAAC;AACvE;AAEA;;;CAGA,GACO,SAAS,uBAAuB,CACrC,MAAM,EACN,KAAK;IAEL,MAAM,GAAA,GAAM,mBAAmB,CAAC,KAAK,CAAC;IACtC,MAAM,IAAA,GAAO,GAAA,6LAAO,QAAK,CAAC,OAAO,CAAC,GAAG,CAAC;IAEtC,MAAM,YAAA,GAAe,IAAA,2LAAO,qBAAA,AAAkB,EAAC,IAAI,CAAA,sLAAI,2BAAA,AAAwB,EAAC,KAAK,CAAC;IAEtF,MAAM,yBAAyB,8MAC3B,oCAAA,AAAiC,EAAC,IAAI,2MACtC,qCAAA,AAAkC,EAAC,MAAM,EAAE,KAAK,CAAC;IACrD,OAAO;QAAC,sBAAsB;QAAE,YAAY;KAAC;AAC/C;AAEA,SAAS,oBAAoB,CAAI,UAAU,EAAkE;IAC3G,OAAO,eAAe,YAClB,CAAC,QAAQ,KAAc;QACrB,OAAO,cAAc,CAAC,UAAU,EAAE,QAAQ,CAAC;IACnD,IACM,CAAC,QAAQ,GAAc,QAAQ,EAAE;AACvC;ACzRA,0FAAA,GACO,SAAS,eAAe,CAAI,QAAQ,EAAc;IACvD,MAAM,GAAA,4MAAMC,kBAAAA,AAAmB,2LAAC,UAAO,CAAC,MAAM,EAAE,CAAC;IACjD,gMAAO,UAAO,CAAC,IAAI,CAAC,GAAG,EAAE,QAAQ,CAAC;AACpC;ACHA,qDAAA,GACO,SAAS,sBAAsB,CAAC,MAAM,EAAgB;IAC3D,MAAM,CAAC,EAAE,CAAC,iBAAiB,GAAE,SAAS;QACpC,MAAM,IAAA,GAAO,aAAa,EAAE;QAChC,qDAAA;QACA,qFAAA;QACI,IAAI,CAAC,IAAA,IAAQ,KAAK,CAAC,IAAA,KAAS,aAAa,EAAE;YACzC;QACN;QAEA,0DAAA;QACI,KAAK,CAAC,QAAA,GAAW;YACf,KAAK,0LAAE,qBAAA,AAAkB,EAAC,IAAI,CAAC;YAC/B,GAAG,KAAK,CAAC,QAAQ;QACvB,CAAK;QAED,MAAM,QAAA,2LAAW,cAAA,AAAW,EAAC,IAAI,CAAC;QAElC,KAAK,CAAC,qBAAA,GAAwB;YAC5B,sBAAsB,MAAE,uOAAA,AAAiC,EAAC,QAAQ,CAAC;YACnE,GAAG,KAAK,CAAC,qBAAqB;QACpC,CAAK;QAED,OAAO,KAAK;IAChB,CAAG,CAAC;AACJ;ACnBA;;;CAGA,GACO,SAAS,YAAY,CAAC,EAC3B,IAAI,EACJ,KAAK,EACL,MAAM,EACP,GAAoD,CAAA,CAAE,EAAuB;IAC5E,IAAI,GAAA,GAAM,CAAC,KAAA,IAAS,mBAAmB,CAAC,KAAK,CAAC,8LAAK,GAAG,CAAC,MAAO,CAAC,MAAM,EAAE;IAEvE,IAAI,IAAI,EAAE;QACR,MAAM,EAAE,KAAA,EAAM,yLAAI,0BAAA,AAAuB,EAAC,IAAI,CAAC;QACnD,wFAAA;QACI,GAAA,GAAM,AAAC,KAAA,IAAS,mBAAmB,CAAC,KAAK,CAAC,IAAK,GAAG,CAAC,6LAAK,CAAC,OAAO,0LAAC,GAAG,CAAC,MAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC;IAChG;IAEE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,sBAAA,EAAuB,GAAI,gBAAgB,CAAC,GAAG,EAAE;QAAE,KAAK;QAAE,MAAA;IAAA,CAAQ,CAAC;IAErG,OAAO;QACL,cAAc,wLAAE,4BAAA,AAAyB,EAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC;QACnE,OAAO,wLAAE,8CAAA,AAA2C,EAAC,sBAAsB,CAAC;IAChF,CAAG;AACH;AClBA;;;CAGA,GACO,SAAS,2CAA2C,GAAS;IAClE,SAAS,SAAS,GAAkB;QAClC,MAAM,+LAAM,GAAG,CAAC,MAAO,CAAC,MAAM,EAAE;QAChC,MAAM,MAAA,GAAS,oBAAoB,CAAC,GAAG,CAAC;QAExC,IAAI,MAAM,EAAE;YACV,OAAO,MAAM;QACnB;QAEA,qBAAA;QACA,iGAAA;QACI,OAAO;YACL,KAAK,qLAAE,yBAAA,AAAsB,EAAE;YAC/B,cAAc,EAAE,8MAAA,AAAwB,EAAE;QAChD,CAAK;IACL;IAEE,SAAS,SAAS,CAAI,QAAQ,EAA0B;QACtD,MAAM,MAAM,GAAG,CAAC,+LAAO,CAAC,MAAM,EAAE;QAEpC,gEAAA;QACA,4FAAA;QACA,0FAAA;QACA,mCAAA;QACA,wFAAA;QACI,gMAAO,GAAG,CAAC,MAAO,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM;YACjC,OAAO,QAAQ,CAAC,eAAe,EAAE,CAAC;QACxC,CAAK,CAAC;IACN;IAEE,SAAS,YAAY,CAAI,KAAK,EAAS,QAAQ,EAA0B;QACvE,MAAM,GAAA,GAAM,mBAAmB,CAAC,KAAK,CAAA,6LAAK,GAAG,CAAC,MAAO,CAAC,MAAM,EAAE;QAElE,gEAAA;QACA,sFAAA;QACA,2EAAA;QACI,gMAAO,GAAG,CAAC,MAAO,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,iCAAiC,EAAE,KAAK,CAAC,EAAE,MAAM;YACpF,OAAO,QAAQ,CAAC,KAAK,CAAC;QAC5B,CAAK,CAAC;IACN;IAEE,SAAS,kBAAkB,CAAI,QAAQ,EAAmC;QACxE,MAAM,MAAM,GAAG,CAAC,+LAAO,CAAC,MAAM,EAAE;QAEpC,gEAAA;QACA,4FAAA;QACA,0FAAA;QACA,mCAAA;QACI,gMAAO,GAAG,CAAC,MAAO,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,uCAAuC,EAAE,IAAI,CAAC,EAAE,MAAM;YACzF,OAAO,QAAQ,CAAC,iBAAiB,EAAE,CAAC;QAC1C,CAAK,CAAC;IACN;IAEE,SAAS,qBAAqB,CAAI,cAAc,EAAS,QAAQ,EAAmC;QAClG,MAAM,+LAAM,GAAG,CAAC,MAAO,CAAC,MAAM,EAAE;QAEpC,gEAAA;QACA,4FAAA;QACA,0FAAA;QACA,mCAAA;QACI,gMAAO,GAAG,CAAC,MAAO,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,2CAA2C,EAAE,cAAc,CAAC,EAAE,MAAM;YACvG,OAAO,QAAQ,CAAC,iBAAiB,EAAE,CAAC;QAC1C,CAAK,CAAC;IACN;IAEE,SAAS,eAAe,GAAU;QAChC,OAAO,SAAS,EAAE,CAAC,KAAK;IAC5B;IAEE,SAAS,iBAAiB,GAAU;QAClC,OAAO,SAAS,EAAE,CAAC,cAAc;IACrC;+LAEE,0BAAA,AAAuB,EAAC;QACtB,SAAS;QACT,YAAY;QACZ,qBAAqB;QACrB,kBAAkB;QAClB,eAAe;QACf,iBAAiB;QACjB,SAAS;QACT,eAAe;QACf,iBAAiB;QACjB,aAAa;QACb,eAAe;QACf,YAAY;QACZ,aAAa;QACjB,4EAAA;QACA,kGAAA;QACI,cAAc,EAAE,cAAA;IACpB,CAAG,CAAC;AACJ;ACnGA;;;;;;;CAOA,GACO,SAAS,uBAAuB,CACrC,mBAAmB;IAErB;;;;;;GAMA,GAEA,mEAAA;IACE,MAAM,oBAAA,SAA6B,mBAAA,CAAoB;QAC9C,WAAW,CAAC,GAAG,IAAI,CAAa;YACrC,KAAK,CAAC,GAAG,IAAI,CAAC;YACd,UAAU,CAAC,sBAAsB,CAAC;QACxC;QACA;;;KAGA,GACW,IAAI,CACT,OAAO,EACP,EAAE,EACF,OAAO,EACP,GAAG,IAAA,EACY;YACf,MAAM,aAAA,GAAgB,oBAAoB,CAAC,OAAO,CAAC;YACnD,MAAM,eAAe,aAAa,EAAE,KAAA,IAAS,qMAAA,AAAe,EAAE;YAC9D,MAAM,wBAAwB,aAAa,EAAE,cAAA,uLAAkB,oBAAA,AAAiB,EAAE;YAElF,MAAM,wBAAA,GAA2B,OAAO,CAAC,QAAQ,CAAC,uCAAuC,CAAA,KAAM,IAAI;YACnG,MAAM,QAAQ,OAAO,CAAC,QAAQ,CAAC,iCAAiC,CAAA;YAChE,MAAM,iBAAiB,OAAO,CAAC,QAAQ,CAAC,2CAA2C,CAAA;YAEnF,MAAM,kBAAkB,KAAA,IAAS,YAAY,CAAC,KAAK,EAAE;YACrD,MAAM,iBAAA,GACJ,cAAA,IAAA,CAAmB,wBAAA,GAA2B,qBAAqB,CAAC,KAAK,EAAC,GAAI,qBAAqB,CAAC;YACtG,MAAM,MAAA,GAAS;gBAAE,KAAK,EAAE,eAAe;gBAAE,cAAc,EAAE,iBAAA;YAAA,CAAmB;YAE5E,MAAM,OAAO,kBAAkB,CAAC,OAAO,EAAE,MAAM,CAAC;YAEtD,mCAAA;YACM,MAAM,OAAO,KACV,WAAW,CAAC,uCAAuC,EACnD,WAAW,CAAC,iCAAiC,EAC7C,WAAW,CAAC,2CAA2C,CAAC;YAE3D,iBAAiB,CAAC,eAAe,EAAE,IAAI,CAAC;YAExC,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;QACnD;IACA;IAEE,OAAO,oBAAA;AACT;AC7DA;;;CAGA,GACO,SAAS,qBAAqB,CAAC,KAAK,EAA8B;IACvE,MAAM,OAAO,GAAY,IAAI,GAAG,EAAoB;IAEpD,KAAK,MAAM,IAAA,IAAQ,KAAK,CAAE;QACxB,6BAA6B,CAAC,OAAO,EAAE,IAAI,CAAC;IAChD;IAEE,OAAO,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,SAAU,CAAC,GAAG,EAAE,QAAQ,CAAC,EAAE;QACpD,OAAO,QAAQ;IACnB,CAAG,CAAC;AACJ;AAEA;;CAEA,GACO,SAAS,gBAAgB,CAAC,IAAI,EAAoC;IACvE,MAAM,cAAA,GAAiB,IAAI,CAAC,UAAU,CAAC,0CAA0C,CAAA,KAAM,IAAI;IAC7F,oEAAA;IACA,iDAAA;IACE,OAAO,CAAC,cAAA,GAAiB,eAAe,CAAC,IAAI,CAAA,GAAI,SAAS;AAC5D;AAEA,SAAS,6BAA6B,CAAC,OAAO,EAAW,IAAI,EAAsB;IACjF,MAAM,KAAK,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM;IACpC,MAAM,QAAA,GAAW,gBAAgB,CAAC,IAAI,CAAC;IAEvC,IAAI,CAAC,QAAQ,EAAE;QACb,kBAAkB,CAAC,OAAO,EAAE;YAAE,EAAE;YAAE,IAAI;YAAE,QAAQ,EAAE,EAAC;QAAA,CAAG,CAAC;QACvD;IACJ;IAEA,+CAAA;IACA,2FAAA;IACE,MAAM,aAAa,qBAAqB,CAAC,OAAO,EAAE,QAAQ,CAAC;IAC3D,MAAM,IAAA,GAAO,kBAAkB,CAAC,OAAO,EAAE;QAAE,EAAE;QAAE,IAAI;QAAE,UAAU;QAAE,QAAQ,EAAE,EAAC;IAAA,CAAG,CAAC;IAChF,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;AAChC;AAEA,SAAS,qBAAqB,CAAC,OAAO,EAAW,EAAE,EAAoB;IACrE,MAAM,WAAW,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;IAEhC,IAAI,QAAQ,EAAE;QACZ,OAAO,QAAQ;IACnB;IAEE,OAAO,kBAAkB,CAAC,OAAO,EAAE;QAAE,EAAE;QAAE,QAAQ,EAAE,EAAC;IAAA,CAAG,CAAC;AAC1D;AAEA,SAAS,kBAAkB,CAAC,OAAO,EAAW,QAAQ,EAAsB;IAC1E,MAAM,QAAA,GAAW,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;IAE3C,6CAAA;IACE,IAAI,QAAQ,EAAE,IAAI,EAAE;QAClB,OAAO,QAAQ;IACnB;IAEA,qDAAA;IACE,IAAI,QAAA,IAAY,CAAC,QAAQ,CAAC,IAAI,EAAE;QAC9B,QAAQ,CAAC,IAAA,GAAO,QAAQ,CAAC,IAAI;QAC7B,QAAQ,CAAC,UAAA,GAAa,QAAQ,CAAC,UAAU;QACzC,OAAO,QAAQ;IACnB;IAEA,+BAAA;IACE,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC;IAClC,OAAO,QAAQ;AACjB;ACxEA,+GAAA;AACA,MAAM,0BAA0B,GAA0C;IACxE,GAAG,EAAE,WAAW;IAChB,GAAG,EAAE,eAAe;IACpB,GAAG,EAAE,kBAAkB;IACvB,GAAG,EAAE,mBAAmB;IACxB,GAAG,EAAE,WAAW;IAChB,GAAG,EAAE,gBAAgB;IACrB,GAAG,EAAE,mBAAmB;IACxB,GAAG,EAAE,oBAAoB;IACzB,GAAG,EAAE,qBAAqB;IAC1B,IAAI,EAAE,SAAS;IACf,IAAI,EAAE,cAAc;IACpB,IAAI,EAAE,eAAe;IACrB,IAAI,EAAE,gBAAgB;IACtB,IAAI,EAAE,aAAa;IACnB,IAAI,EAAE,WAAW;IACjB,IAAI,EAAE,iBAAiB;AACzB,CAAA;AAEA,MAAM,yBAAA,GAA4B,CAAC,OAAO,KAAsB;IAC9D,OAAO,MAAM,CAAC,MAAM,CAAC,0BAA0B,CAAC,CAAC,QAAQ,CAAC,OAAA,EAAiC;AAC7F,CAAC;AAED;;CAEA,GACO,SAAS,SAAS,CAAC,IAAI,EAA4B;IACxD,MAAM,UAAA,GAAa,iBAAiB,CAAC,IAAI,CAAA,GAAI,IAAI,CAAC,UAAA,GAAa,CAAA,CAAE;IACjE,MAAM,MAAA,GAAS,aAAa,CAAC,IAAI,CAAA,GAAI,IAAI,CAAC,MAAA,GAAS,SAAS;IAE5D,IAAI,MAAM,EAAE;QACd,sIAAA;QACI,IAAI,MAAM,CAAC,IAAA,8LAAS,iBAAc,CAAC,EAAE,EAAE;YACrC,OAAO;gBAAE,IAAI,yLAAE,iBAAA;YAAA,CAAgB;QACrC,yEAAA;QACA,CAAI,MAAO,IAAI,MAAM,CAAC,IAAA,8LAAS,iBAAc,CAAC,KAAK,EAAE;YAC/C,IAAI,OAAO,MAAM,CAAC,OAAA,KAAY,WAAW,EAAE;gBACzC,MAAM,cAAA,GAAiB,yBAAyB,CAAC,UAAU,CAAC;gBAC5D,IAAI,cAAc,EAAE;oBAClB,OAAO,cAAc;gBAC/B;YACA;YAEM,IAAI,MAAM,CAAC,OAAA,IAAW,yBAAyB,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;gBAC/D,OAAO;oBAAE,IAAI,yLAAE,oBAAiB;oBAAE,OAAO,EAAE,MAAM,CAAC,OAAA;gBAAA,CAAS;YACnE,OAAa;gBACL,OAAO;oBAAE,IAAI,yLAAE,oBAAiB;oBAAE,OAAO,EAAE,eAAA;gBAAA,CAAiB;YACpE;QACA;IACA;IAEA,kFAAA;IACE,MAAM,cAAA,GAAiB,yBAAyB,CAAC,UAAU,CAAC;IAE5D,IAAI,cAAc,EAAE;QAClB,OAAO,cAAc;IACzB;IAEA,gDAAA;IACE,IAAI,MAAM,EAAE,kMAAS,iBAAc,CAAC,KAAK,EAAE;QACzC,OAAO;YAAE,IAAI,yLAAE,iBAAA;QAAA,CAAgB;IACnC,OAAS;QACL,OAAO;YAAE,IAAI,yLAAE,oBAAiB;YAAE,OAAO,EAAE,eAAA;QAAA,CAAiB;IAChE;AACA;AAEA,SAAS,yBAAyB,CAAC,UAAU,EAA0C;IACvF,kFAAA;IAEA,mDAAA;IACE,MAAM,iBAAA,GAAoB,UAAU,8MAAC,iCAA8B,CAAA,IAAK,UAAU,wNAAC,4BAAyB,CAAC;IAC/G,mDAAA;IACE,MAAM,iBAAA,GAAoB,UAAU,wNAAC,gCAA6B,CAAC;IAEnE,MAAM,cAAA,GACJ,OAAO,sBAAsB,WACzB,oBACA,OAAO,iBAAA,KAAsB,WAC3B,QAAQ,CAAC,iBAAiB,IAC1B,SAAS;IAEjB,IAAI,OAAO,cAAA,KAAmB,QAAQ,EAAE;QACtC,kMAAO,4BAAA,AAAyB,EAAC,cAAc,CAAC;IACpD;IAEE,IAAI,OAAO,iBAAA,KAAsB,QAAQ,EAAE;QACzC,OAAO;YAAE,IAAI,yLAAE,oBAAiB;YAAE,OAAO,EAAE,0BAA0B,CAAC,iBAAiB,CAAA,IAAK;QAAA,CAAiB;IACjH;IAEE,OAAO,SAAS;AAClB;AC9DA,MAAM,cAAA,GAAiB,IAAI;AAC3B,MAAM,eAAA,GAAkB,GAAG,CAAA,CAAA,QAAA;AAO3B;;CAEA,GACO,MAAM,kBAAA,CAAmB;IAChC;;;;;;;;;;;;;;;GAeA,GAKA,mFAAA;IACA,+DAAA;IAEA,iHAAA,GAGS,WAAW,CAAC,OAAA,CAGhB;QACD,IAAI,CAAC,uBAAA,GAA0B,OAAO,EAAE,OAAA,IAAW,eAAe;QAClE,IAAI,CAAC,oBAAA,GAAuB,IAAI,KAAK,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC;QACnF,IAAI,CAAC,wBAAA,GAA2B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAC,GAAI,IAAI,CAAC;QAC7D,IAAI,CAAC,mBAAA,GAAsB,IAAI,OAAO,EAAE;QACxC,IAAI,CAAC,UAAA,GAAa,IAAI,GAAG,EAAkB;QAC3C,IAAI,CAAC,eAAA,0LAAkB,WAAA,AAAQ,EAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;YAAE,OAAO,EAAE,GAAA;QAAA,CAAK,CAAC;IAC/E;IAEA;;;GAGA,GACS,MAAM,CAAC,IAAI,EAAsB;QACtC,MAAM,mBAAA,GAAsB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAC,GAAI,IAAI,CAAC;QAEzD,IAAI,IAAI,CAAC,wBAAA,KAA6B,mBAAmB,EAAE;YACzD,IAAI,gBAAA,GAAmB,CAAC;YACxB,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC,KAAK;gBAC/C,IAAI,MAAA,IAAU,MAAM,CAAC,YAAA,IAAgB,mBAAA,GAAsB,IAAI,CAAC,uBAAuB,EAAE;oBACvF,oBAAoB,MAAM,CAAC,KAAK,CAAC,IAAI;oBACrC,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAA,GAAI,SAAS;gBAClD;YACA,CAAO,CAAC;YACF,IAAI,gBAAA,GAAmB,CAAC,EAAE;gBACxB,WAAA,8LACE,QAAK,CAAC,GAAG,CACP,CAAC,qBAAqB,EAAE,gBAAgB,CAAC,+CAA+C,EAAE,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC;YAE7I;YACM,IAAI,CAAC,wBAAA,GAA2B,mBAAmB;QACzD;QAEI,MAAM,kBAAA,GAAqB,sBAAsB,IAAI,CAAC,uBAAuB;QAC7E,MAAM,gBAAgB,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,CAAA,IAAK;YACrE,YAAY,EAAE,mBAAmB;YACjC,KAAK,EAAE,IAAI,GAAG,EAAE;QACtB,CAAK;QACD,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,CAAA,GAAI,aAAa;QAC7D,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC;QAC7B,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,IAAI,EAAE,aAAa,CAAC;QAErD,uGAAA;QACI,MAAM,aAAA,GAAgB,gBAAgB,CAAC,IAAI,CAAC;QAC5C,IAAI,CAAC,aAAA,IAAiB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE;YACxD,IAAI,CAAC,eAAe,EAAE;QAC5B;IACA;IAEA;;;;GAIA,GACS,KAAK,GAAS;QACnB,MAAM,aAAA,GAAgB,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAC,MAAA,GAAW,MAAA,GAAS,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAA,GAAI,EAAE,CAAC,CAAC;QAE3G,IAAI,CAAC,mBAAmB,EAAE;QAC1B,MAAM,YAAY,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC;QAEhD,MAAM,aAAA,GAAgB,SAAS,CAAC,IAAI;QACpC,MAAM,sBAAA,GAAyB,aAAa,CAAC,MAAA,GAAS,aAAa;QACnE,WAAA,IACE,kMAAK,CAAC,GAAG,CACP,CAAC,sBAAsB,EAAE,aAAa,CAAC,QAAQ,EAAE,sBAAsB,CAAC,mDAAmD,CAAC;QAGhI,MAAM,cAAA,GAAiB,IAAI,CAAC,GAAG,EAAC,GAAI,eAAA,GAAkB,IAAI;QAE1D,KAAK,MAAM,IAAA,IAAQ,SAAS,CAAE;YAC5B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM,EAAE,cAAc,CAAC;YAC9D,MAAM,WAAA,GAAc,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC;YACtD,IAAI,WAAW,EAAE;gBACf,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC;YACtC;QACA;QACA,oDAAA;QACA,wEAAA;QACA,8DAAA;QACI,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE;IACjC;IAEA;;;GAGA,GACS,KAAK,GAAS;QACnB,IAAI,CAAC,oBAAA,GAAuB,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC;QACrE,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE;QACvB,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE;IACjC;IAEA;;;;;;;;GAQA,GACU,UAAU,CAAC,KAAK,EAAqC;QAC3D,MAAM,OAAA,GAAU,qBAAqB,CAAC,KAAK,CAAC;QAC5C,MAAM,SAAA,GAAY,IAAI,GAAG,EAAgB;QAEzC,MAAM,YAAY,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC;QAEtD,KAAK,MAAM,IAAA,IAAQ,SAAS,CAAE;YAC5B,MAAM,IAAA,GAAO,IAAI,CAAC,IAAI;YACtB,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC;YACnB,MAAM,gBAAA,GAAmB,4BAA4B,CAAC,IAAI,CAAC;YAEjE,yGAAA;YACM,IAAI,IAAI,CAAC,UAAA,IAAc,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE;gBAC9D,MAAM,YAAY,gBAAgB,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI;gBACxD,IAAI,SAAS,EAAE;oBACb,SAAS,CAAC,iCAAiC,CAAA,GAAI,IAAI;gBAC7D;YACA;YAEA,0DAAA;YACM,MAAM,QAAQ,gBAAgB,CAAC,KAAA,IAAS,EAAE;YAE1C,KAAK,MAAM,KAAA,IAAS,IAAI,CAAC,QAAQ,CAAE;gBACjC,8BAA8B,CAAC,KAAK,EAAE,KAAK,EAAE,SAAS,CAAC;YAC/D;YAEA,kFAAA;YACA,kCAAA;YACM,gBAAgB,CAAC,KAAA,GACf,KAAK,CAAC,MAAA,GAAS,iBACX,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,GAAK,CAAC,CAAC,eAAA,GAAkB,CAAC,CAAC,eAAe,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,IACnF,KAAK;YAEX,MAAM,2MAAe,4BAAA,AAAyB,EAAC,IAAI,CAAC,MAAM,CAAC;YAC3D,IAAI,YAAY,EAAE;gBAChB,gBAAgB,CAAC,YAAA,GAAe,YAAY;YACpD;yLAEM,eAAA,AAAY,EAAC,gBAAgB,CAAC;QACpC;QAEI,OAAO,SAAS;IACpB;IAEA,gEAAA,GACU,mBAAmB,GAAS;QAClC,MAAM,gBAAA,GAAmB,IAAI,CAAC,GAAG,EAAE;QACvC,qGAAA;QACI,KAAK,MAAM,CAAC,MAAM,EAAE,cAAc,CAAA,IAAK,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAE;YAChE,IAAI,cAAA,IAAkB,gBAAgB,EAAE;gBACtC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC;YACtC;QACA;IACA;IAEA,0FAAA,GACU,uCAAuC,CAAC,IAAI,EAAuC;QACzF,OAAO,CAAC,CAAC,IAAI,CAAC,IAAA,IAAA,CAAS,CAAC,IAAI,CAAC,UAAA,IAAc,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;IACvF;IAEA,sDAAA,GACU,sBAAsB,CAAC,KAAK,EAAmC;QACzE,wFAAA;QACA,yCAAA;QACI,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,GAAgC,IAAI,CAAC,uCAAuC,CAAC,IAAI,CAAC,CAAC;IAChH;AACA;AAEA,SAAS,SAAS,CAAC,IAAI,EAAkF;IACvG,MAAM,UAAA,GAAa,IAAI,CAAC,UAAU;IAElC,MAAM,MAAA,GAAS,UAAU,CAAC,uNAAgC,CAAA;IAC1D,MAAM,EAAA,GAAK,UAAU,qLAAC,+BAA4B,CAAA;IAClD,MAAM,MAAA,GAAS,UAAU,qLAAC,mCAAgC,CAAA;IAE1D,OAAO;QAAE,MAAM;QAAE,EAAE;QAAE;IAAA,CAAQ;AAC/B;AAEA,6BAAA,GACO,SAAS,4BAA4B,CAAC,IAAI,EAAkC;IACjF,MAAM,EAAE,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,MAAA,GAAS,QAAQ,EAAE,MAAA,EAAO,GAAI,WAAW,CAAC,IAAI,CAAC;IAC9E,MAAM,kBAAA,yLAAqB,0BAAA,AAAuB,EAAC,MAAwB;IAE3E,MAAM,aAAa,IAAI,CAAC,UAAU,qLAAC,wCAAqC,CAAA;IAExE,MAAM,UAAU,GAAmB;QACjC,CAAC,uNAAgC,CAAA,EAAG,MAAM;QAC1C,qLAAC,wCAAqC,CAAA,EAAG,UAAU;QACnD,qLAAC,+BAA4B,CAAA,EAAG,EAAE;QAClC,qLAAC,mCAAgC,CAAA,EAAG,MAAM;QAC1C,GAAG,IAAI;QACP,GAAG,sBAAsB,CAAC,IAAI,CAAC,UAAU,CAAC;IAC9C,CAAG;IAED,MAAM,EAAE,KAAA,EAAM,GAAI,IAAI;IACtB,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAA,EAAA,GAAY,IAAI,CAAC,WAAW,EAAE;IAEnE,iFAAA;IACA,8EAAA;IACA,qCAAA;IACA,kHAAA;IACA,gFAAA;IACE,MAAM,cAAA,GAAiB,eAAe,CAAC,IAAI,CAAC;IAE5C,MAAM,MAAA,GAAS,SAAS,CAAC,IAAI,CAAC;IAE9B,MAAM,YAAY,GAAiB;QACjC,cAAc;QACd,OAAO;QACP,QAAQ;QACR,IAAI,EAAE,UAAU;QAChB,MAAM;QACN,EAAE;QACF,MAAM,0LAAE,mBAAA,AAAgB,EAAC,MAAM,CAAC;QAChC,KAAK,EAAE,sNAAA,AAA2B,EAAC,KAAK,CAAC;IAC7C,CAAG;IAED,MAAM,UAAA,GAAa,UAAU,8MAAC,iCAA8B,CAAC;IAC7D,MAAM,eAAA,GAAkB,OAAO,UAAA,KAAe,WAAW;QAAE,QAAQ,EAAE;YAAE,WAAW,EAAE;QAAA,CAAW;IAAA,CAAE,GAAI,SAAS;IAE9G,MAAM,gBAAgB,GAAqB;QACzC,QAAQ,EAAE;YACR,KAAK,EAAE,YAAY;YACnB,IAAI,EAAE;gBACJ,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,UAAU;YAC1C,CAAO;YACD,GAAG,eAAe;QACxB,CAAK;QACD,KAAK,EAAE,EAAE;QACT,eAAe,0LAAE,yBAAA,AAAsB,EAAC,IAAI,CAAC,SAAS,CAAC;QACvD,SAAS,0LAAE,yBAAA,AAAsB,EAAC,IAAI,CAAC,OAAO,CAAC;QAC/C,WAAW,EAAE,WAAW;QACxB,IAAI,EAAE,aAAa;QACnB,qBAAqB,EAAE;YACrB,iBAAiB,EAAE,kBAAkB,CAAC,KAAK;YAC3C,0BAA0B,EAAE,kBAAkB,CAAC,cAAc;YAC7D,UAAU;YACV,sBAAsB,yMAAE,oCAAA,AAAiC,EAAC,MAAwB;QACxF,CAAK;QACD,GAAI,MAAA,IAAU;YACZ,gBAAgB,EAAE;gBAChB,MAAM;YACd,CAAO;QACP,CAAK,CAAC;IACN,CAAG;IAED,OAAO,gBAAgB;AACzB;AAEA,SAAS,8BAA8B,CAAC,IAAI,EAAY,KAAK,EAAc,SAAS,EAA2B;IAC7G,MAAM,IAAA,GAAO,IAAI,CAAC,IAAI;IAEtB,IAAI,IAAI,EAAE;QACR,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC;IACvB;IAEE,MAAM,UAAA,GAAa,CAAC,IAAI;IAE1B,yFAAA;IACE,IAAI,UAAU,EAAE;QACd,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAC,SAAS;YAC7B,8BAA8B,CAAC,KAAK,EAAE,KAAK,EAAE,SAAS,CAAC;QAC7D,CAAK,CAAC;QACF;IACJ;IAEE,MAAM,UAAU,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM;IACzC,MAAM,WAAW,IAAI,CAAC,WAAW,EAAE,CAAC,OAAO;IAC3C,MAAM,YAAA,GAAe,eAAe,CAAC,IAAI,CAAC;IAE1C,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,EAAE,KAAA,EAAM,GAAI,IAAI;IAEtD,MAAM,EAAE,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,MAAA,GAAS,QAAA,EAAS,GAAI,WAAW,CAAC,IAAI,CAAC;IACtE,MAAM,UAAU;QACd,qLAAC,mCAAgC,CAAA,EAAG,MAAM;QAC1C,qLAAC,+BAA4B,CAAA,EAAG,EAAE;QAClC,GAAG,sBAAsB,CAAC,UAAU,CAAC;QACrC,GAAG,IAAI;IACX,CAAG;IAED,MAAM,MAAA,GAAS,SAAS,CAAC,IAAI,CAAC;IAE9B,MAAM,QAAQ,GAAa;QACzB,OAAO;QACP,QAAQ;QACR,IAAI,EAAE,OAAO;QACb,WAAW;QACX,cAAc,EAAE,YAAY;QAC5B,eAAe,0LAAE,yBAAA,AAAsB,EAAC,SAAS,CAAC;QACtD,2FAAA;QACI,SAAS,0LAAE,yBAAA,AAAsB,EAAC,OAAO,CAAA,IAAK,SAAS;QACvD,MAAM,EAAE,2MAAA,AAAgB,EAAC,MAAM,CAAC;QAChC,EAAE;QACF,MAAM;QACN,YAAY,8LAAE,4BAAA,AAAyB,EAAC,IAAI,CAAC,MAAM,CAAC;QACpD,KAAK,EAAE,sNAAA,AAA2B,EAAC,KAAK,CAAC;IAC7C,CAAG;IAED,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC;IAEpB,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAC,SAAS;QAC7B,8BAA8B,CAAC,KAAK,EAAE,KAAK,EAAE,SAAS,CAAC;IAC3D,CAAG,CAAC;AACJ;AAEA,SAAS,WAAW,CAAC,IAAI;IAOvB,MAAM,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,aAAa,EAAE,MAAA,EAAO,GAAI,SAAS,CAAC,IAAI,CAAC;IACxE,MAAM,EAAE,EAAE,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,cAAc,EAAE,IAAI,EAAE,YAAA,EAAa,GAAI,oBAAoB,CAAC,IAAI,CAAC;IAE9G,MAAM,EAAA,GAAK,SAAA,IAAa,UAAU;IAClC,MAAM,MAAA,GAAS,aAAA,IAAiB,cAAc;IAE9C,MAAM,IAAA,GAAO;QAAE,GAAG,YAAY;QAAE,GAAG,OAAO,CAAC,IAAI,CAAA;IAAA,CAAG;IAElD,OAAO;QACL,EAAE;QACF,WAAW;QACX,MAAM;QACN,MAAM;QACN,IAAI;IACR,CAAG;AACH;AAEA;;;CAGA,GACA,SAAS,sBAAsB,CAAC,IAAI,EAAoD;IACtF,MAAM,WAAA,GAAc;QAAE,GAAG,IAAA;IAAA,CAAM;IAEjC,uDAAA,GACE,OAAO,WAAW,qLAAC,wCAAqC,CAAC;IACzD,OAAO,WAAW,CAAC,0CAA0C,CAAC;IAC9D,OAAO,WAAW,oLAAC,8CAA0C,CAAC;IAChE,sDAAA,GAEE,OAAO,WAAW;AACpB;AAEA,SAAS,OAAO,CAAC,IAAI,EAAyC;IAC5D,MAAM,UAAA,GAAa,IAAI,CAAC,UAAU;IAClC,MAAM,IAAI,GAA4B,CAAA,CAAE;IAExC,IAAI,IAAI,CAAC,IAAA,8LAAS,WAAQ,CAAC,QAAQ,EAAE;QACnC,IAAI,CAAC,WAAW,CAAA,4LAAI,WAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;IAC3C;IAEA,mDAAA;IACE,MAAM,4BAAA,GAA+B,UAAU,wNAAC,4BAAyB,CAAC;IAC1E,IAAI,4BAA4B,EAAE;QAChC,IAAI,8MAAC,iCAA8B,CAAA,GAAI,4BAAA;IAC3C;IAEE,MAAM,WAAA,GAAc,kBAAkB,CAAC,IAAI,CAAC;IAE5C,IAAI,WAAW,CAAC,GAAG,EAAE;QACnB,IAAI,CAAC,GAAA,GAAM,WAAW,CAAC,GAAG;IAC9B;IAEE,IAAI,WAAW,CAAC,YAAY,CAAC,EAAE;QAC7B,IAAI,CAAC,YAAY,CAAA,GAAI,WAAW,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;IAC3D;IACE,IAAI,WAAW,CAAC,eAAe,CAAC,EAAE;QAChC,IAAI,CAAC,eAAe,CAAA,GAAI,WAAW,CAAC,eAAe,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;IACjE;IAEE,OAAO,IAAI;AACb;ACtbA,SAAS,WAAW,CAAC,IAAI,EAAQ,aAAa,EAAiB;IAC/D,uHAAA;IACE,MAAM,sMAAa,QAAK,CAAC,OAAO,CAAC,aAAa,CAAC;IAE/C,IAAI,MAAA,GAAS,oBAAoB,CAAC,aAAa,CAAC;IAElD,iGAAA;IACE,IAAI,UAAA,IAAc,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC,QAAQ,EAAE;+LACpD,sBAAA,AAAkB,EAAC,UAAU,EAAE,IAAI,CAAC;IACxC;IAEA,oCAAA;IACE,IAAI,UAAU,EAAE,WAAW,EAAE,CAAC,QAAQ,EAAE;QACtC,IAAI,CAAC,YAAY,CAAC,0CAA0C,EAAE,IAAI,CAAC;IACvE;IAEA,kFAAA;IACA,0CAAA;IACE,IAAI,aAAA,8LAAkB,eAAY,EAAE;QAClC,SAAS;YACP,KAAK,qLAAE,yBAAA,AAAsB,EAAE;YAC/B,cAAc,GAAE,6MAAA,AAAwB,EAAE;QAChD,CAAK;IACL;IAEA,yGAAA;IACE,IAAI,MAAM,EAAE;SACV,+MAAA,AAAuB,EAAC,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,cAAc,CAAC;IACtE;6LAEE,eAAA,AAAY,EAAC,IAAI,CAAC;IAElB,MAAM,MAAA,GAAS,+LAAA,AAAS,EAAE;IAC1B,MAAM,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC;AACjC;AAEA,SAAS,SAAS,CAAC,IAAI,EAAc;6LACnC,aAAA,AAAU,EAAC,IAAI,CAAC;IAEhB,MAAM,MAAA,qLAAS,aAAA,AAAS,EAAE;IAC1B,MAAM,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC;AAC/B;AAEA;;;CAGA,GACO,MAAM,qBAAsD;IAG1D,WAAW,CAAC,OAAO,CAAyB;QACjD,UAAU,CAAC,qBAAqB,CAAC;QACjC,IAAI,CAAC,SAAA,GAAY,IAAI,kBAAkB,CAAC,OAAO,CAAC;IACpD;IAEA;;GAEA,GACS,MAAM,UAAU,GAAkB;QACvC,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;IAC1B;IAEA;;GAEA,GACS,MAAM,QAAQ,GAAkB;QACrC,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;IAC1B;IAEA;;GAEA,GACS,OAAO,CAAC,IAAI,EAAQ,aAAa,EAAiB;QACvD,WAAW,CAAC,IAAI,EAAE,aAAa,CAAC;IACpC;IAEA,gBAAA,GACS,KAAK,CAAC,IAAI,EAA6B;QAC5C,SAAS,CAAC,IAAI,CAAC;QAEf,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC;IAC/B;AACA;AChEA;;CAEA,GACO,MAAM,eAAiC;IAGrC,WAAW,CAAC,MAAM,CAAU;QACjC,IAAI,CAAC,OAAA,GAAU,MAAM;QACrB,UAAU,CAAC,eAAe,CAAC;IAC/B;IAEA,gBAAA,GACS,YAAY,CACjB,OAAO,EACP,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,cAAc,EACd,MAAM,EACU;QAChB,MAAM,UAAU,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;QAEzC,MAAM,UAAA,GAAa,YAAY,CAAC,OAAO,CAAC;QACxC,MAAM,aAAA,GAAgB,UAAU,EAAE,WAAW,EAAE;QAE/C,IAAI,+LAAC,kBAAA,AAAe,EAAC,OAAO,CAAC,EAAE;YAC7B,OAAO,oBAAoB,CAAC;gBAAE,QAAQ,EAAE,SAAS;gBAAE,OAAO;gBAAE,cAAA;YAAA,CAAgB,CAAC;QACnF;QAEA,sHAAA;QACA,mDAAA;QACI,MAAM,mBAAA,GAAsB,cAAc,wNAAC,uBAAoB,CAAA,IAAK,cAAc,8MAAC,2BAAwB,CAAC;QAEhH,qFAAA;QACA,sEAAA;QACI,IAAI,QAAA,8LAAa,WAAQ,CAAC,MAAA,IAAU,mBAAA,IAAA,CAAwB,CAAC,UAAA,IAAc,aAAa,EAAE,QAAQ,CAAC,EAAE;YACnG,OAAO,oBAAoB,CAAC;gBAAE,QAAQ,EAAE,SAAS;gBAAE,OAAO;gBAAE,cAAA;YAAA,CAAgB,CAAC;QACnF;QAEI,MAAM,aAAA,GAAgB,UAAA,GAAa,gBAAgB,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,CAAA,GAAI,SAAS;QAC9F,MAAM,aAAa,CAAC,cAAc,aAAa,EAAE,QAAQ;QAE7D,4HAAA;QACA,yEAAA;QACI,IAAI,CAAC,UAAU,EAAE;YACf,OAAO,oBAAoB,CAAC;gBAC1B,QAAQ,EAAE,aAAA,kMAAgB,oBAAgB,CAAC,kBAAA,mMAAqB,mBAAgB,CAAC,UAAU;gBAC3F,OAAO;gBACP,cAAc;YACtB,CAAO,CAAC;QACR;QAEA,uEAAA;QACI,MAAM,EACJ,WAAW,EAAE,gBAAgB,EAC7B,IAAI,EAAE,kBAAkB,EACxB,EAAE,EACJ,GAAI,aAAa,CAAC,QAAQ,EAAE,cAAc,EAAE,QAAQ,CAAC;QAErD,MAAM,mBAAmB;YACvB,GAAG,kBAAkB;YACrB,GAAG,cAAc;QACvB,CAAK;QAED,IAAI,EAAE,EAAE;YACN,gBAAgB,qLAAC,+BAA4B,CAAA,GAAI,EAAE;QACzD;QAEI,MAAM,uBAAA,GAA0B;YAAE,QAAQ,EAAE;QAAA,CAAM;QAClD,IAAI,CAAC,OAAO,CAAC,IAAI,CACf,gBAAgB,EAChB;YACE,cAAc,EAAE,gBAAgB;YAChC,QAAQ,EAAE,gBAAgB;YAC1B,aAAa,EAAE,aAAa;YAC5B,aAAa,EAAE,aAAa;QACpC,CAAO,EACD,uBAAuB;QAEzB,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE;YACrC,OAAO,oBAAoB,CAAC;gBAAE,QAAQ,EAAE,SAAS;gBAAE,OAAO;gBAAE,cAAA;YAAA,CAAgB,CAAC;QACnF;QAEI,MAAM,EAAE,cAAA,EAAe,GAAI,oBAAoB,CAAC,OAAO,CAAA,IAAK,CAAA,CAAE;QAE9D,MAAM,SAAA,GAAY,aAAa,EAAE,aAAa,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC,sBAAsB,CAAA,GAAI,SAAS;QAC9G,MAAM,GAAA,GAAM,SAAA,yLAAY,wCAAA,AAAqC,EAAC,SAAS,CAAA,GAAI,SAAS;QAEpF,MAAM,UAAA,OAAa,4MAAA,AAAe,EAAC,GAAG,EAAE,WAAW,CAAA,IAAK,IAAI,CAAC,MAAM,EAAE;QAErE,MAAM,CAAC,OAAO,EAAE,UAAU,EAAE,yBAAyB,CAAA,4LAAI,aAAA,AAAU,EACjE,OAAO,EACP;YACE,IAAI,EAAE,gBAAgB;YACtB,UAAU,EAAE,gBAAgB;YAC5B,iBAAiB,EAAE,cAAc,EAAE,YAAY,EAAE,CAAC,qBAAqB,CAAC,iBAAiB;YACzF,aAAa;YACb,gBAAgB,gMAAE,kBAAA,AAAe,EAAC,GAAG,EAAE,WAAW,CAAC;QAC3D,CAAO,EACD,UAAU;QAGZ,MAAM,MAAA,GAAS,CAAC,EAAA,mBAAA,CAAA,CAAA,CAAA,WAAA,EAAA;QACA,IAAA,MAAA,KAAA,SAAA,IAAA,MAAA,KAAA,MAAA,EAAA;YACA,WAAA,8LAAA,QAAA,CAAA,GAAA,CAAA,CAAA,oDAAA,EAAA,MAAA,CAAA,MAAA,EAAA,QAAA,CAAA,CAAA,CAAA;YAEA,OAAA,oBAAA,CAAA;gBACA,QAAA,kMAAA,mBAAA,CAAA,UAAA;gBACA,OAAA;gBACA,cAAA;gBACA,UAAA;gBACA,yBAAA,EAAA,CAAA;YACA,CAAA,CAAA;QACA;QAEA,IACA,CAAA,OAAA,IACA,8JAAA;QACA,aAAA,KAAA,WACA;YACA,WAAA,8LAAA,QAAA,CAAA,GAAA,CAAA,gFAAA,CAAA;YACA,IAAA,CAAA,OAAA,CAAA,kBAAA,CAAA,aAAA,EAAA,aAAA,CAAA;QACA;QAEA,OAAA;YACA,GAAA,oBAAA,CAAA;gBACA,QAAA,EAAA,OAAA,mMAAA,mBAAA,CAAA,kBAAA,mMAAA,mBAAA,CAAA,UAAA;gBACA,OAAA;gBACA,cAAA;gBACA,UAAA;gBACA,yBAAA,EAAA,yBAAA,GAAA,UAAA,GAAA,SAAA;YACA,CAAA,CAAA;YACA,UAAA,EAAA;gBACA,iIAAA;gBACA,CAAA,4NAAA,CAAA,EAAA,yBAAA,GAAA,UAAA,GAAA,SAAA;YACA,CAAA;QACA,CAAA;IACA;IAEA,0EAAA,GACA,QAAA,GAAA;QACA,OAAA,eAAA;IACA;AACA;AAEA,SAAA,gBAAA,CAAA,UAAA,EAAA,OAAA,EAAA,QAAA,EAAA;IACA,MAAA,aAAA,GAAA,UAAA,CAAA,WAAA,EAAA;IAEA,oDAAA;IACA,kHAAA;IACA,iMAAA,qBAAA,EAAA,aAAA,CAAA,IAAA,aAAA,CAAA,OAAA,KAAA,OAAA,EAAA;QACA,IAAA,aAAA,CAAA,QAAA,EAAA;YACA,MAAA,aAAA,GAAA,mBAAA,CAAA,UAAA,CAAA,WAAA,EAAA,CAAA;YACA,WAAA,6LACA,SAAA,CAAA,GAAA,CAAA,CAAA,0DAAA,EAAA,QAAA,CAAA,EAAA,EAAA,aAAA,CAAA,CAAA,CAAA;YACA,OAAA,aAAA;QACA;QAEA,MAAA,aAAA,GAAA,mBAAA,CAAA,aAAA,CAAA;QACA,WAAA,IAAA,kMAAA,CAAA,GAAA,CAAA,CAAA,mDAAA,EAAA,QAAA,CAAA,EAAA,EAAA,aAAA,CAAA,CAAA,CAAA;QACA,OAAA,aAAA;IACA;IAEA,OAAA,SAAA;AACA;AAEA;;;;CAIA,GACA,SAAA,oBAAA,CAAA,EACA,QAAA,EACA,OAAA,EACA,cAAA,EACA,UAAA,EACA,yBAAA,EACA;IAOA,IAAA,UAAA,GAAA,iBAAA,CAAA,OAAA,EAAA,cAAA,CAAA;IAEA,8DAAA;IACA,oCAAA;IACA,iCAAA;IACA,qHAAA;IACA,IAAA,yBAAA,KAAA,SAAA,EAAA;QACA,UAAA,GAAA,UAAA,CAAA,GAAA,CAAA,8BAAA,EAAA,CAAA,EAAA,yBAAA,CAAA,CAAA,CAAA;IACA;IAEA,IAAA,UAAA,KAAA,SAAA,EAAA;QACA,UAAA,GAAA,UAAA,CAAA,GAAA,CAAA,8BAAA,EAAA,CAAA,EAAA,UAAA,CAAA,CAAA,CAAA;IACA;IAEA,sHAAA;IACA,qFAAA;IACA,IAAA,QAAA,IAAA,SAAA,EAAA;QACA,OAAA;YAAA,QAAA,kMAAA,mBAAA,CAAA,UAAA;YAAA,UAAA;QAAA,CAAA;IACA;IAEA,IAAA,QAAA,qMAAA,mBAAA,CAAA,UAAA,EAAA;QACA,OAAA;YAAA,QAAA;YAAA,UAAA,EAAA,UAAA,CAAA,GAAA,CAAA,wCAAA,EAAA,GAAA,CAAA;QAAA,CAAA;IACA;IAEA,OAAA;QAAA,QAAA;QAAA,UAAA;IAAA,CAAA;AACA;AAEA,SAAA,iBAAA,CAAA,OAAA,EAAA,cAAA,EAAA;IACA,MAAA,UAAA,4LAAA,QAAA,CAAA,OAAA,CAAA,OAAA,CAAA;IACA,MAAA,aAAA,GAAA,UAAA,EAAA,WAAA,EAAA;IAEA,IAAA,UAAA,GAAA,aAAA,EAAA,UAAA,IAAA,gMAAA,aAAA,EAAA;IAEA,mFAAA;IACA,oGAAA;IACA,mDAAA;IACA,MAAA,GAAA,GAAA,cAAA,wNAAA,oBAAA,CAAA,IAAA,cAAA,6MAAA,iBAAA,CAAA;IACA,IAAA,GAAA,IAAA,OAAA,GAAA,KAAA,QAAA,EAAA;QACA,UAAA,GAAA,UAAA,CAAA,GAAA,CAAA,sBAAA,EAAA,GAAA,CAAA;IACA;IAEA,OAAA,UAAA;AACA;AAEA;;;CAGA,GACA,SAAA,YAAA,CAAA,OAAA,EAAA;IACA,MAAA,IAAA,4LAAA,QAAA,CAAA,OAAA,CAAA,OAAA,CAAA;IACA,OAAA,IAAA,iMAAA,qBAAA,EAAA,IAAA,CAAA,WAAA,EAAA,CAAA,GAAA,IAAA,GAAA,SAAA;AACA", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27]}}]}