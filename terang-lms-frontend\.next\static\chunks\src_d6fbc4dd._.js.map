{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/constants/data.ts"], "sourcesContent": ["import { NavItem } from '@/types';\r\n\r\nexport type Product = {\r\n  photo_url: string;\r\n  name: string;\r\n  description: string;\r\n  created_at: string;\r\n  price: number;\r\n  id: number;\r\n  category: string;\r\n  updated_at: string;\r\n};\r\n\r\n//Info: The following data is used for the sidebar navigation and Cmd K bar.\r\nexport const getNavItems = (pathname: string): NavItem[] => [\r\n  {\r\n    title: 'Available Courses',\r\n    url: '/courses',\r\n    icon: 'searchList',\r\n    isActive: pathname.startsWith('/courses'),\r\n    shortcut: ['a', 'c'],\r\n    items: [] // Empty array as there are no child items\r\n  },\r\n  {\r\n    title: 'My Courses',\r\n    url: '/my-courses',\r\n    icon: 'graduationCap',\r\n    isActive: pathname.startsWith('/my-courses'),\r\n    shortcut: ['m', 'c'],\r\n    items: [] // Empty array as there are no child items\r\n  }\r\n];\r\n\r\n// Legacy export for backward compatibility\r\nexport const navItems: NavItem[] = getNavItems('');\r\n\r\nexport interface SaleUser {\r\n  id: number;\r\n  name: string;\r\n  email: string;\r\n  amount: string;\r\n  image: string;\r\n  initials: string;\r\n}\r\n\r\nexport const recentSalesData: SaleUser[] = [\r\n  {\r\n    id: 1,\r\n    name: '<PERSON>',\r\n    email: '<EMAIL>',\r\n    amount: '+$1,999.00',\r\n    image: 'https://api.slingacademy.com/public/sample-users/1.png',\r\n    initials: 'OM'\r\n  },\r\n  {\r\n    id: 2,\r\n    name: 'Jackson Lee',\r\n    email: '<EMAIL>',\r\n    amount: '+$39.00',\r\n    image: 'https://api.slingacademy.com/public/sample-users/2.png',\r\n    initials: 'JL'\r\n  },\r\n  {\r\n    id: 3,\r\n    name: 'Isabella Nguyen',\r\n    email: '<EMAIL>',\r\n    amount: '+$299.00',\r\n    image: 'https://api.slingacademy.com/public/sample-users/3.png',\r\n    initials: 'IN'\r\n  },\r\n  {\r\n    id: 4,\r\n    name: 'William Kim',\r\n    email: '<EMAIL>',\r\n    amount: '+$99.00',\r\n    image: 'https://api.slingacademy.com/public/sample-users/4.png',\r\n    initials: 'WK'\r\n  },\r\n  {\r\n    id: 5,\r\n    name: 'Sofia Davis',\r\n    email: '<EMAIL>',\r\n    amount: '+$39.00',\r\n    image: 'https://api.slingacademy.com/public/sample-users/5.png',\r\n    initials: 'SD'\r\n  }\r\n];\r\n"], "names": [], "mappings": ";;;;;AAcO,MAAM,cAAc,CAAC,WAAgC;QAC1D;YACE,OAAO;YACP,KAAK;YACL,MAAM;YACN,UAAU,SAAS,UAAU,CAAC;YAC9B,UAAU;gBAAC;gBAAK;aAAI;YACpB,OAAO,EAAE,CAAC,0CAA0C;QACtD;QACA;YACE,OAAO;YACP,KAAK;YACL,MAAM;YACN,UAAU,SAAS,UAAU,CAAC;YAC9B,UAAU;gBAAC;gBAAK;aAAI;YACpB,OAAO,EAAE,CAAC,0CAA0C;QACtD;KACD;AAGM,MAAM,WAAsB,YAAY;AAWxC,MAAM,kBAA8B;IACzC;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,OAAO;QACP,UAAU;IACZ;CACD", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/kbar/result-item.tsx"], "sourcesContent": ["import type { ActionId, ActionImpl } from 'kbar';\r\nimport * as React from 'react';\r\n\r\nconst ResultItem = React.forwardRef(\r\n  (\r\n    {\r\n      action,\r\n      active,\r\n      currentRootActionId\r\n    }: {\r\n      action: ActionImpl;\r\n      active: boolean;\r\n      currentRootActionId: ActionId;\r\n    },\r\n    ref: React.Ref<HTMLDivElement>\r\n  ) => {\r\n    const ancestors = React.useMemo(() => {\r\n      if (!currentRootActionId) return action.ancestors;\r\n      const index = action.ancestors.findIndex(\r\n        (ancestor) => ancestor.id === currentRootActionId\r\n      );\r\n      return action.ancestors.slice(index + 1);\r\n    }, [action.ancestors, currentRootActionId]);\r\n\r\n    return (\r\n      <div\r\n        ref={ref}\r\n        className={`relative z-10 flex cursor-pointer items-center justify-between px-4 py-3`}\r\n      >\r\n        {active && (\r\n          <div\r\n            id='kbar-result-item'\r\n            className='border-primary bg-accent/50 absolute inset-0 z-[-1]! border-l-4'\r\n          ></div>\r\n        )}\r\n        <div className='relative z-10 flex items-center gap-2'>\r\n          {action.icon && action.icon}\r\n          <div className='flex flex-col'>\r\n            <div>\r\n              {ancestors.length > 0 &&\r\n                ancestors.map((ancestor) => (\r\n                  <React.Fragment key={ancestor.id}>\r\n                    <span className='text-muted-foreground mr-2'>\r\n                      {ancestor.name}\r\n                    </span>\r\n                    <span className='mr-2'>&rsaquo;</span>\r\n                  </React.Fragment>\r\n                ))}\r\n              <span>{action.name}</span>\r\n            </div>\r\n            {action.subtitle && (\r\n              <span className='text-muted-foreground text-sm'>\r\n                {action.subtitle}\r\n              </span>\r\n            )}\r\n          </div>\r\n        </div>\r\n        {action.shortcut?.length ? (\r\n          <div className='relative z-10 grid grid-flow-col gap-1'>\r\n            {action.shortcut.map((sc, i) => (\r\n              <kbd\r\n                key={sc + i}\r\n                className='bg-muted flex h-5 items-center gap-1 rounded-md border px-1.5 text-[10px] font-medium'\r\n              >\r\n                {sc}\r\n              </kbd>\r\n            ))}\r\n          </div>\r\n        ) : null}\r\n      </div>\r\n    );\r\n  }\r\n);\r\n\r\nResultItem.displayName = 'KBarResultItem';\r\n\r\nexport default ResultItem;\r\n"], "names": [], "mappings": ";;;;AACA;;;;AAEA,MAAM,2BAAa,GAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,UAChC,CACE,EACE,MAAM,EACN,MAAM,EACN,mBAAmB,EAKpB,EACD;;IAEA,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;yCAAE;YAC9B,IAAI,CAAC,qBAAqB,OAAO,OAAO,SAAS;YACjD,MAAM,QAAQ,OAAO,SAAS,CAAC,SAAS;uDACtC,CAAC,WAAa,SAAS,EAAE,KAAK;;YAEhC,OAAO,OAAO,SAAS,CAAC,KAAK,CAAC,QAAQ;QACxC;wCAAG;QAAC,OAAO,SAAS;QAAE;KAAoB;IAE1C,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAC,wEAAwE,CAAC;;YAEpF,wBACC,6LAAC;gBACC,IAAG;gBACH,WAAU;;;;;;0BAGd,6LAAC;gBAAI,WAAU;;oBACZ,OAAO,IAAI,IAAI,OAAO,IAAI;kCAC3B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;oCACE,UAAU,MAAM,GAAG,KAClB,UAAU,GAAG,CAAC,CAAC,yBACb,6LAAC,6JAAA,CAAA,WAAc;;8DACb,6LAAC;oDAAK,WAAU;8DACb,SAAS,IAAI;;;;;;8DAEhB,6LAAC;oDAAK,WAAU;8DAAO;;;;;;;2CAJJ,SAAS,EAAE;;;;;kDAOpC,6LAAC;kDAAM,OAAO,IAAI;;;;;;;;;;;;4BAEnB,OAAO,QAAQ,kBACd,6LAAC;gCAAK,WAAU;0CACb,OAAO,QAAQ;;;;;;;;;;;;;;;;;;YAKvB,OAAO,QAAQ,EAAE,uBAChB,6LAAC;gBAAI,WAAU;0BACZ,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,kBACxB,6LAAC;wBAEC,WAAU;kCAET;uBAHI,KAAK;;;;;;;;;uBAOd;;;;;;;AAGV;;AAGF,WAAW,WAAW,GAAG;uCAEV", "debugId": null}}, {"offset": {"line": 225, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/kbar/render-result.tsx"], "sourcesContent": ["import { KBarResults, useMatches } from 'kbar';\r\nimport ResultItem from './result-item';\r\n\r\nexport default function RenderResults() {\r\n  const { results, rootActionId } = useMatches();\r\n\r\n  return (\r\n    <KBarResults\r\n      items={results}\r\n      onRender={({ item, active }) =>\r\n        typeof item === 'string' ? (\r\n          <div className='text-primary-foreground px-4 py-2 text-sm uppercase opacity-50'>\r\n            {item}\r\n          </div>\r\n        ) : (\r\n          <ResultItem\r\n            action={item}\r\n            active={active}\r\n            currentRootActionId={rootActionId ?? ''}\r\n          />\r\n        )\r\n      }\r\n    />\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;AAEe,SAAS;;IACtB,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,aAAU,AAAD;IAE3C,qBACE,6LAAC,uIAAA,CAAA,cAAW;QACV,OAAO;QACP,UAAU,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,GACzB,OAAO,SAAS,yBACd,6LAAC;gBAAI,WAAU;0BACZ;;;;;uCAGH,6LAAC,+IAAA,CAAA,UAAU;gBACT,QAAQ;gBACR,QAAQ;gBACR,qBAAqB,gBAAgB;;;;;;;;;;;AAMjD;GArBwB;;QACY,uIAAA,CAAA,aAAU;;;KADtB", "debugId": null}}, {"offset": {"line": 279, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/kbar/use-theme-switching.tsx"], "sourcesContent": ["import { useRegisterActions } from 'kbar';\r\nimport { useTheme } from 'next-themes';\r\n\r\nconst useThemeSwitching = () => {\r\n  const { theme, setTheme } = useTheme();\r\n\r\n  const toggleTheme = () => {\r\n    setTheme(theme === 'light' ? 'dark' : 'light');\r\n  };\r\n\r\n  const themeAction = [\r\n    {\r\n      id: 'toggleTheme',\r\n      name: 'Toggle Theme',\r\n      shortcut: ['t', 't'],\r\n      section: 'Theme',\r\n      perform: toggleTheme\r\n    },\r\n    {\r\n      id: 'setLightTheme',\r\n      name: 'Set Light Theme',\r\n      section: 'Theme',\r\n      perform: () => setTheme('light')\r\n    },\r\n    {\r\n      id: 'setDarkTheme',\r\n      name: 'Set Dark Theme',\r\n      section: 'Theme',\r\n      perform: () => setTheme('dark')\r\n    }\r\n  ];\r\n\r\n  useRegisterActions(themeAction, [theme]);\r\n};\r\n\r\nexport default useThemeSwitching;\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;;AAEA,MAAM,oBAAoB;;IACxB,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAEnC,MAAM,cAAc;QAClB,SAAS,UAAU,UAAU,SAAS;IACxC;IAEA,MAAM,cAAc;QAClB;YACE,IAAI;YACJ,MAAM;YACN,UAAU;gBAAC;gBAAK;aAAI;YACpB,SAAS;YACT,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,SAAS;YACT,SAAS,IAAM,SAAS;QAC1B;QACA;YACE,IAAI;YACJ,MAAM;YACN,SAAS;YACT,SAAS,IAAM,SAAS;QAC1B;KACD;IAED,CAAA,GAAA,uIAAA,CAAA,qBAAkB,AAAD,EAAE,aAAa;QAAC;KAAM;AACzC;GA9BM;;QACwB,mJAAA,CAAA,WAAQ;QA4BpC,uIAAA,CAAA,qBAAkB;;;uCAGL", "debugId": null}}, {"offset": {"line": 337, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/kbar/index.tsx"], "sourcesContent": ["'use client';\r\nimport { navItems } from '@/constants/data';\r\nimport {\r\n  KBarAnimator,\r\n  KBarPortal,\r\n  KBarPositioner,\r\n  KBarProvider,\r\n  KBarSearch\r\n} from 'kbar';\r\nimport { useRouter } from 'next/navigation';\r\nimport { useMemo } from 'react';\r\nimport RenderResults from './render-result';\r\nimport useThemeSwitching from './use-theme-switching';\r\n\r\nexport default function KBar({ children }: { children: React.ReactNode }) {\r\n  const router = useRouter();\r\n\r\n  // These action are for the navigation\r\n  const actions = useMemo(() => {\r\n    // Define navigateTo inside the useMemo callback to avoid dependency array issues\r\n    const navigateTo = (url: string) => {\r\n      router.push(url);\r\n    };\r\n\r\n    return navItems.flatMap((navItem) => {\r\n      // Only include base action if the navItem has a real URL and is not just a container\r\n      const baseAction =\r\n        navItem.url !== '#'\r\n          ? {\r\n              id: `${navItem.title.toLowerCase()}Action`,\r\n              name: navItem.title,\r\n              shortcut: navItem.shortcut,\r\n              keywords: navItem.title.toLowerCase(),\r\n              section: 'Navigation',\r\n              subtitle: `Go to ${navItem.title}`,\r\n              perform: () => navigateTo(navItem.url)\r\n            }\r\n          : null;\r\n\r\n      // Map child items into actions\r\n      const childActions =\r\n        navItem.items?.map((childItem) => ({\r\n          id: `${childItem.title.toLowerCase()}Action`,\r\n          name: childItem.title,\r\n          shortcut: childItem.shortcut,\r\n          keywords: childItem.title.toLowerCase(),\r\n          section: navItem.title,\r\n          subtitle: `Go to ${childItem.title}`,\r\n          perform: () => navigateTo(childItem.url)\r\n        })) ?? [];\r\n\r\n      // Return only valid actions (ignoring null base actions for containers)\r\n      return baseAction ? [baseAction, ...childActions] : childActions;\r\n    });\r\n  }, [router]);\r\n\r\n  return (\r\n    <KBarProvider actions={actions}>\r\n      <KBarComponent>{children}</KBarComponent>\r\n    </KBarProvider>\r\n  );\r\n}\r\nconst KBarComponent = ({ children }: { children: React.ReactNode }) => {\r\n  useThemeSwitching();\r\n\r\n  return (\r\n    <>\r\n      <KBarPortal>\r\n        <KBarPositioner className='bg-background/80 fixed inset-0 z-99999 p-0! backdrop-blur-sm'>\r\n          <KBarAnimator className='bg-card text-card-foreground relative mt-64! w-full max-w-[600px] -translate-y-12! overflow-hidden rounded-lg border shadow-lg'>\r\n            <div className='bg-card border-border sticky top-0 z-10 border-b'>\r\n              <KBarSearch className='bg-card w-full border-none px-6 py-4 text-lg outline-hidden focus:ring-0 focus:ring-offset-0 focus:outline-hidden' />\r\n            </div>\r\n            <div className='max-h-[400px]'>\r\n              <RenderResults />\r\n            </div>\r\n          </KBarAnimator>\r\n        </KBarPositioner>\r\n      </KBarPortal>\r\n      {children}\r\n    </>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAOA;AACA;AACA;AACA;;;AAZA;;;;;;;AAce,SAAS,KAAK,EAAE,QAAQ,EAAiC;;IACtE,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,sCAAsC;IACtC,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;iCAAE;YACtB,iFAAiF;YACjF,MAAM;oDAAa,CAAC;oBAClB,OAAO,IAAI,CAAC;gBACd;;YAEA,OAAO,2HAAA,CAAA,WAAQ,CAAC,OAAO;yCAAC,CAAC;oBACvB,qFAAqF;oBACrF,MAAM,aACJ,QAAQ,GAAG,KAAK,MACZ;wBACE,IAAI,GAAG,QAAQ,KAAK,CAAC,WAAW,GAAG,MAAM,CAAC;wBAC1C,MAAM,QAAQ,KAAK;wBACnB,UAAU,QAAQ,QAAQ;wBAC1B,UAAU,QAAQ,KAAK,CAAC,WAAW;wBACnC,SAAS;wBACT,UAAU,CAAC,MAAM,EAAE,QAAQ,KAAK,EAAE;wBAClC,OAAO;qDAAE,IAAM,WAAW,QAAQ,GAAG;;oBACvC,IACA;oBAEN,+BAA+B;oBAC/B,MAAM,eACJ,QAAQ,KAAK,EAAE;iDAAI,CAAC,YAAc,CAAC;gCACjC,IAAI,GAAG,UAAU,KAAK,CAAC,WAAW,GAAG,MAAM,CAAC;gCAC5C,MAAM,UAAU,KAAK;gCACrB,UAAU,UAAU,QAAQ;gCAC5B,UAAU,UAAU,KAAK,CAAC,WAAW;gCACrC,SAAS,QAAQ,KAAK;gCACtB,UAAU,CAAC,MAAM,EAAE,UAAU,KAAK,EAAE;gCACpC,OAAO;6DAAE,IAAM,WAAW,UAAU,GAAG;;4BACzC,CAAC;mDAAM,EAAE;oBAEX,wEAAwE;oBACxE,OAAO,aAAa;wBAAC;2BAAe;qBAAa,GAAG;gBACtD;;QACF;gCAAG;QAAC;KAAO;IAEX,qBACE,6LAAC,uIAAA,CAAA,eAAY;QAAC,SAAS;kBACrB,cAAA,6LAAC;sBAAe;;;;;;;;;;;AAGtB;GA/CwB;;QACP,qIAAA,CAAA,YAAS;;;KADF;AAgDxB,MAAM,gBAAgB,CAAC,EAAE,QAAQ,EAAiC;;IAChE,CAAA,GAAA,0JAAA,CAAA,UAAiB,AAAD;IAEhB,qBACE;;0BACE,6LAAC,uIAAA,CAAA,aAAU;0BACT,cAAA,6LAAC,uIAAA,CAAA,iBAAc;oBAAC,WAAU;8BACxB,cAAA,6LAAC,uIAAA,CAAA,eAAY;wBAAC,WAAU;;0CACtB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,uIAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;0CAExB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,iJAAA,CAAA,UAAa;;;;;;;;;;;;;;;;;;;;;;;;;;YAKrB;;;AAGP;IApBM;;QACJ,0JAAA,CAAA,UAAiB;;;MADb", "debugId": null}}, {"offset": {"line": 503, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/constants/shared-course-data.ts"], "sourcesContent": ["import { Course } from '@/types/lms';\r\n\r\n// Shared Architecture course data for both enroll and modules pages\r\nexport const architectureCourse: Course = {\r\n  id: 'arch-cert-001',\r\n  name: 'Sertifikasi Arsitek Profesional IAI',\r\n  code: 'IAI-CERT-001',\r\n  description:\r\n    'Program sertifikasi profesional untuk arsitek yang ingin memperoleh sertifikat Ikatan Arsitek Indonesia (IAI) dan meningkatkan kompetensi di bidang perencanaan dan pengawasan bangunan',\r\n  instructor: 'Ar. <PERSON>, IAI, AA',\r\n  startDate: '2024-08-01',\r\n  endDate: '2024-12-31',\r\n  enrollmentType: 'both',\r\n  enrollmentCode: 'IAI-CERT-2024',\r\n  minPassingScore: 80,\r\n  totalProgress: 0,\r\n  status: 'not-started',\r\n  modules: [\r\n    {\r\n      id: 'mod1',\r\n      title: 'Modul 1: Dasar-Dasar Arsitektur',\r\n      description: 'Memahami prinsip-prinsip dasar arsitektur dan sejarahnya',\r\n      order: 1,\r\n      isUnlocked: true,\r\n      completionPercentage: 0,\r\n      chapters: [\r\n        {\r\n          id: 'ch1',\r\n          title: 'Bab 1: Sejarah Arsitektur',\r\n          order: 1,\r\n          isUnlocked: true,\r\n          completionPercentage: 0,\r\n          contents: [\r\n            {\r\n              id: 'content1',\r\n              title: 'Prinsip-Prinsip Arsitektur Kuno',\r\n              type: 'text',\r\n              content: `# Ancient Architecture Principles\r\n\r\n## Introduction\r\n\r\nArchitecture has been a **fundamental aspect** of human civilization since ancient times. From the pyramids of Egypt to the temples of Greece, architectural principles have evolved to reflect cultural values, technological advances, and environmental considerations.\r\n\r\n## Key Civilizations and Their Contributions\r\n\r\n### 1. Egyptian Architecture\r\n- **Pyramids**: Monumental structures demonstrating advanced engineering\r\n- **Materials**: Limestone, granite, and sandstone\r\n- **Key Features**:\r\n  - Massive scale\r\n  - Precise geometric proportions\r\n  - Durability across millennia\r\n\r\n### 2. Greek Architecture\r\nThe Greeks developed the classical orders that influence architecture to this day:\r\n\r\n1. **Doric Order** - Simple and sturdy\r\n2. **Ionic Order** - Elegant with scroll-like capitals\r\n3. **Corinthian Order** - Ornate with acanthus leaf decorations\r\n\r\n### 3. Roman Architecture\r\nRomans revolutionized construction with:\r\n- **Concrete technology**\r\n- **Arches and vaults**\r\n- **Aqueducts and infrastructure**\r\n\r\n## Fundamental Principles\r\n\r\n> \"Architecture is the learned game, correct and magnificent, of forms assembled in the light.\" - Le Corbusier\r\n\r\n### Proportion and Scale\r\n- **Golden Ratio**: φ ≈ 1.618\r\n- **Human scale**: Buildings designed for human comfort\r\n- **Visual balance**: Creating harmony through proportional relationships\r\n\r\n### Materials and Structure\r\n\\`\\`\\`\r\nLoad-bearing elements:\r\n├── Walls\r\n├── Columns\r\n├── Beams\r\n└── Foundations\r\n\\`\\`\\`\r\n\r\n## Learning Objectives\r\nBy the end of this section, you will understand:\r\n- [ ] The evolution of architectural styles\r\n- [ ] Key principles of proportion and scale\r\n- [ ] Material properties and their applications\r\n- [ ] Cultural influences on architectural design\r\n\r\n---\r\n\r\n*Next: We'll explore how these ancient principles influenced Renaissance and modern architecture.*`,\r\n              isCompleted: false,\r\n              createdAt: '2024-01-15'\r\n            },\r\n            {\r\n              id: 'content2',\r\n              title: 'Tata Orde Klasik dalam Arsitektur',\r\n              type: 'video',\r\n              content: 'https://example.com/classical-orders-video',\r\n              duration: 25,\r\n              isCompleted: false,\r\n              createdAt: '2024-01-16'\r\n            },\r\n            {\r\n              id: 'content3',\r\n              title: 'Dasar-Dasar Gambar Arsitektur',\r\n              type: 'pdf',\r\n              content: 'https://example.com/drawing-fundamentals.pdf',\r\n              isCompleted: false,\r\n              createdAt: '2024-01-17'\r\n            }\r\n          ],\r\n          quiz: {\r\n            id: 'quiz-ch1',\r\n            title: 'Kuis Bab 1: Sejarah Arsitektur',\r\n            type: 'chapter',\r\n            questions: [\r\n              {\r\n                id: 'q1',\r\n                question:\r\n                  'Which architectural order is characterized by simple, sturdy columns?',\r\n                type: 'multiple-choice',\r\n                options: ['Doric', 'Ionic', 'Corinthian', 'Composite'],\r\n                correctAnswer: 0,\r\n                explanation:\r\n                  'The Doric order is the simplest and most robust of the classical orders.'\r\n              }\r\n            ],\r\n            minimumScore: 70,\r\n            timeLimit: 15,\r\n            attempts: 0,\r\n            maxAttempts: 3,\r\n            isPassed: false\r\n          }\r\n        },\r\n        {\r\n          id: 'ch2',\r\n          title: 'Bab 2: Prinsip-Prinsip Desain',\r\n          order: 2,\r\n          isUnlocked: false,\r\n          completionPercentage: 0,\r\n          contents: [\r\n            {\r\n              id: 'content4',\r\n              title: 'Proporsi dan Skala',\r\n              type: 'text',\r\n              content: `# Proportion and Scale in Architecture\r\n\r\n## What is Proportion?\r\n\r\n**Proportion** refers to the relationship between different elements in architectural design. It's the foundation of creating visually pleasing and harmonious structures.\r\n\r\n## The Golden Ratio (φ = 1.618)\r\n\r\nThe **Golden Ratio** has been used in architecture for millennia:\r\n\r\n| Building | Golden Ratio Application |\r\n|----------|-------------------------|\r\n| Parthenon | Façade proportions |\r\n| Notre-Dame | Rose window design |\r\n| Villa Savoye | Room dimensions |\r\n\r\n### Mathematical Expression\r\n\\`\\`\\`\r\nφ = (1 + √5) / 2 ≈ 1.618033988...\r\n\\`\\`\\`\r\n\r\n## Types of Scale\r\n\r\n### 1. Human Scale\r\nBuildings should relate to human dimensions:\r\n- **Door heights**: ~2.1m (7 feet)\r\n- **Ceiling heights**: 2.4-3.0m (8-10 feet)\r\n- **Stair risers**: 15-18cm (6-7 inches)\r\n\r\n### 2. Architectural Scale\r\nHow building elements relate to each other:\r\n- Window to wall ratios\r\n- Column spacing\r\n- Room proportions\r\n\r\n### 3. Urban Scale\r\nHow buildings relate to their context:\r\n- Street width to building height\r\n- Public space dimensions\r\n- Neighborhood character\r\n\r\n## Classical Proportional Systems\r\n\r\n### Vitruvian Proportions\r\nBased on the human body:\r\n> \"The human body is a model of proportion because with arms or legs extended it fits into those 'perfect' geometrical forms, the square and the circle.\"\r\n\r\n### Modular Systems\r\n- **Le Corbusier's Modulor**: Based on human measurements\r\n- **Japanese Ken**: Traditional proportional system\r\n- **Classical Orders**: Column to entablature ratios\r\n\r\n## Design Exercise\r\n\r\nTry this proportional analysis:\r\n1. Find a building you admire\r\n2. Measure key dimensions\r\n3. Calculate the ratios\r\n4. Compare to golden ratio or other systems\r\n\r\n## Key Takeaways\r\n\r\n✅ **Good proportion creates visual harmony**  \r\n✅ **Human scale ensures comfort**  \r\n✅ **Mathematical ratios provide guidelines**  \r\n✅ **Context matters in scale decisions**  \r\n\r\n---\r\n\r\n*Remember: Proportion is felt, not calculated. Trust your eye, but understand the principles.*`,\r\n              isCompleted: false,\r\n              createdAt: '2024-01-18'\r\n            },\r\n            {\r\n              id: 'content5',\r\n              title: 'Rasio Emas dalam Arsitektur',\r\n              type: 'zoom-recording',\r\n              content: 'https://zoom.us/rec/golden-ratio-lecture',\r\n              duration: 45,\r\n              isCompleted: false,\r\n              createdAt: '2024-01-19'\r\n            }\r\n          ],\r\n          quiz: {\r\n            id: 'quiz-ch2',\r\n            title: 'Kuis Bab 2: Prinsip-Prinsip Desain',\r\n            type: 'chapter',\r\n            questions: [\r\n              {\r\n                id: 'q2',\r\n                question: 'What is the Golden Ratio approximately equal to?',\r\n                type: 'multiple-choice',\r\n                options: ['1.414', '1.618', '1.732', '2.000'],\r\n                correctAnswer: 1,\r\n                explanation:\r\n                  'The Golden Ratio is approximately 1.618 and is commonly used in architectural proportions.'\r\n              },\r\n              {\r\n                id: 'q3',\r\n                question:\r\n                  'Proportion in architecture refers to the relationship between different elements.',\r\n                type: 'true-false',\r\n                correctAnswer: 'true',\r\n                explanation:\r\n                  'Proportion is indeed about the harmonious relationship between different architectural elements.'\r\n              }\r\n            ],\r\n            minimumScore: 70,\r\n            attempts: 0,\r\n            maxAttempts: 3,\r\n            isPassed: false\r\n          }\r\n        }\r\n      ],\r\n      moduleQuiz: {\r\n        id: 'quiz-mod1',\r\n        title: 'Kuis Akhir Modul 1',\r\n        type: 'module',\r\n        questions: [\r\n          {\r\n            id: 'qm1',\r\n            question:\r\n              'Which of the following are classical architectural orders? (Select all that apply)',\r\n            type: 'multiple-choice',\r\n            options: ['Doric', 'Ionic', 'Corinthian', 'All of the above'],\r\n            correctAnswer: 3,\r\n            explanation:\r\n              'Doric, Ionic, and Corinthian are the three main classical architectural orders.'\r\n          }\r\n        ],\r\n        minimumScore: 75,\r\n        attempts: 0,\r\n        maxAttempts: 2,\r\n        isPassed: false\r\n      }\r\n    },\r\n    {\r\n      id: 'mod2',\r\n      title: 'Modul 2: Arsitektur Kontemporer',\r\n      description: 'Gerakan arsitektur modern dan desain berkelanjutan',\r\n      order: 2,\r\n      isUnlocked: false,\r\n      completionPercentage: 0,\r\n      chapters: [\r\n        {\r\n          id: 'ch3',\r\n          title: 'Bab 3: Gerakan Modern',\r\n          order: 1,\r\n          isUnlocked: false,\r\n          completionPercentage: 0,\r\n          contents: [\r\n            {\r\n              id: 'content6',\r\n              title: 'Gerakan Bauhaus',\r\n              type: 'text',\r\n              content: `# The Bauhaus Movement: Form Follows Function\r\n\r\n## Overview\r\n\r\nThe **Bauhaus** (1919-1933) was a revolutionary design school that fundamentally changed architecture, art, and design. Founded by Walter Gropius in Weimar, Germany, it promoted the integration of art, craft, and industrial technology.\r\n\r\n## Core Principles\r\n\r\n### 1. **Form Follows Function**\r\n- Design should be determined by purpose\r\n- No unnecessary ornamentation\r\n- Efficiency in both form and construction\r\n\r\n### 2. **Gesamtkunstwerk** (Total Work of Art)\r\n- Integration of all arts and crafts\r\n- Architecture as the master art\r\n- Unified design philosophy\r\n\r\n### 3. **Machine Aesthetic**\r\n- Embrace industrial production methods\r\n- Clean lines and geometric forms\r\n- Mass production capabilities\r\n\r\n## Key Figures\r\n\r\n| Name | Role | Contribution |\r\n|------|------|-------------|\r\n| **Walter Gropius** | Founder & Director | Established core philosophy |\r\n| **Ludwig Mies van der Rohe** | Director (1930-33) | \"Less is more\" principle |\r\n| **Marcel Breuer** | Faculty | Furniture design innovation |\r\n| **László Moholy-Nagy** | Faculty | Photography and typography |\r\n\r\n## Architectural Characteristics\r\n\r\n### Visual Elements\r\n- ✅ **Clean geometric forms**\r\n- ✅ **Flat roofs**\r\n- ✅ **Large windows**\r\n- ✅ **Open floor plans**\r\n- ❌ Historical references\r\n- ❌ Decorative elements\r\n\r\n### Materials\r\n\\`\\`\\`\r\nPrimary materials:\r\n├── Steel frame construction\r\n├── Glass curtain walls\r\n├── Reinforced concrete\r\n└── Industrial materials\r\n\\`\\`\\`\r\n\r\n## Famous Bauhaus Buildings\r\n\r\n### 1. Fagus Factory (1911-1913)\r\n- **Architect**: Walter Gropius\r\n- **Innovation**: Glass curtain wall\r\n- **Significance**: Proto-Bauhaus design\r\n\r\n### 2. Bauhaus Dessau (1925-1926)\r\n- **Architect**: Walter Gropius\r\n- **Features**: Modular design, industrial materials\r\n- **Legacy**: Became the school's permanent home\r\n\r\n### 3. Barcelona Pavilion (1929)\r\n- **Architect**: Mies van der Rohe\r\n- **Concept**: Flowing space, minimal structure\r\n- **Impact**: Redefined modern architecture\r\n\r\n## Impact on Modern Architecture\r\n\r\n> \"The ultimate goal of all visual arts is the complete building!\" - Walter Gropius\r\n\r\n### International Style\r\nThe Bauhaus influenced what became known as the **International Style**:\r\n- Emphasis on volume over mass\r\n- Regularity rather than symmetry\r\n- No applied ornamentation\r\n\r\n### Urban Planning\r\n- **Functional zoning**\r\n- **Housing for the masses**\r\n- **Integration with landscape**\r\n\r\n## Legacy and Criticism\r\n\r\n### Positive Impact\r\n- **Democratized design**: Good design for everyone\r\n- **Functional solutions**: Buildings that work efficiently  \r\n- **Modern materials**: Pioneered new construction techniques\r\n\r\n### Criticisms\r\n- **Cultural homogenization**: Loss of regional character\r\n- **Human scale**: Sometimes overlooked human needs\r\n- **Context insensitivity**: Universal solutions vs. local conditions\r\n\r\n## Contemporary Relevance\r\n\r\nThe Bauhaus principles remain relevant today:\r\n- **Sustainable design**: Efficiency and functionality\r\n- **Digital integration**: Technology serving human needs\r\n- **Social responsibility**: Design for collective benefit\r\n\r\n---\r\n\r\n*\"The Bauhaus is not a style but a principle - the principle that the artistic process and the industrial process are one.\"* - Walter Gropius`,\r\n              isCompleted: false,\r\n              createdAt: '2024-01-20'\r\n            }\r\n          ],\r\n          quiz: {\r\n            id: 'quiz-ch3',\r\n            title: 'Kuis Bab 3',\r\n            type: 'chapter',\r\n            questions: [\r\n              {\r\n                id: 'q4',\r\n                question:\r\n                  'The Bauhaus movement emphasized functionality over ornamentation.',\r\n                type: 'true-false',\r\n                correctAnswer: 'true',\r\n                explanation:\r\n                  'The Bauhaus movement was known for its emphasis on functionality and minimalism.'\r\n              }\r\n            ],\r\n            minimumScore: 70,\r\n            attempts: 0,\r\n            maxAttempts: 3,\r\n            isPassed: false\r\n          }\r\n        }\r\n      ],\r\n      moduleQuiz: {\r\n        id: 'quiz-mod2',\r\n        title: 'Kuis Akhir Modul 2',\r\n        type: 'module',\r\n        questions: [\r\n          {\r\n            id: 'qm2',\r\n            question: 'What are the key principles of modern architecture?',\r\n            type: 'essay',\r\n            correctAnswer:\r\n              'Sample answer about functionality, simplicity, and form follows function',\r\n            explanation:\r\n              'Modern architecture emphasizes functionality, simplicity, and the principle that form should follow function.'\r\n          }\r\n        ],\r\n        minimumScore: 75,\r\n        attempts: 0,\r\n        maxAttempts: 2,\r\n        isPassed: false\r\n      }\r\n    }\r\n  ],\r\n  finalExam: {\r\n    id: 'final-exam',\r\n    title: 'Ujian Akhir Sertifikasi Arsitektur',\r\n    type: 'final',\r\n    questions: [\r\n      {\r\n        id: 'qf1',\r\n        question:\r\n          'Which architectural order features ornate capitals with acanthus leaves?',\r\n        type: 'multiple-choice',\r\n        options: ['Doric', 'Ionic', 'Corinthian', 'Tuscan'],\r\n        correctAnswer: 2,\r\n        explanation:\r\n          'The Corinthian order is distinguished by its elaborate capitals decorated with acanthus leaves.'\r\n      },\r\n      {\r\n        id: 'qf2',\r\n        question:\r\n          'The Golden Ratio is frequently used in architectural design.',\r\n        type: 'true-false',\r\n        correctAnswer: 'true',\r\n        explanation:\r\n          'The Golden Ratio (approximately 1.618) has been used in architecture for centuries to create pleasing proportions.'\r\n      },\r\n      {\r\n        id: 'qf3',\r\n        question:\r\n          'Describe the influence of the Bauhaus movement on modern architecture.',\r\n        type: 'essay',\r\n        correctAnswer:\r\n          'The Bauhaus movement emphasized functionality, simplicity, and the integration of art and technology, leading to modern minimalist design principles.',\r\n        explanation:\r\n          'The Bauhaus movement revolutionized architecture by promoting functional design, clean lines, and the principle that form should follow function.'\r\n      }\r\n    ],\r\n    minimumScore: 80,\r\n    timeLimit: 120,\r\n    attempts: 0,\r\n    maxAttempts: 2,\r\n    isPassed: false\r\n  },\r\n  certificate: {\r\n    isEligible: true,\r\n    isGenerated: false,\r\n    type: 'professional',\r\n    issuedBy: 'Ikatan Arsitek Indonesia (IAI)',\r\n    validityPeriod: '5 years',\r\n    credentialId: 'IAI-CERT-2024',\r\n    description: 'Sertifikat Arsitek Profesional yang diakui secara nasional untuk praktik arsitektur di Indonesia'\r\n  }\r\n};\r\n\r\n// For enroll page compatibility, convert to class format\r\nexport interface ClassData {\r\n  id: number;\r\n  name: string;\r\n  teacher: string;\r\n  schedule: string;\r\n  enrollmentCode: string;\r\n  materials: any[];\r\n  thumbnail?: string;\r\n  description?: string;\r\n  startDate?: string;\r\n  endDate?: string;\r\n}\r\n\r\nexport const architectureClass: ClassData = {\r\n  id: 1,\r\n  name: architectureCourse.name,\r\n  teacher: architectureCourse.instructor,\r\n  schedule: 'Sen, Rab 10:00 WIB',\r\n  enrollmentCode: architectureCourse.enrollmentCode || 'ARCH2024-001',\r\n  description: architectureCourse.description,\r\n  startDate: architectureCourse.startDate,\r\n  endDate: architectureCourse.endDate,\r\n  materials: [] // Will be populated from modules when enrolled\r\n};\r\n\r\n// Institution data\r\nexport const architectureInstitution = {\r\n  id: 'iai-indonesia',\r\n  name: 'Ikatan Arsitek Indonesia',\r\n  shortName: 'IAI',\r\n  website: 'https://iai.or.id',\r\n  certificateTemplate: {\r\n    primaryColor: '#1e40af',\r\n    secondaryColor: '#f59e0b',\r\n    signatoryName: 'Ar. Georgius Budi Yulianto, IAI, AA',\r\n    signatoryTitle: 'Ketua Umum IAI 2024-2027'\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;;AAGO,MAAM,qBAA6B;IACxC,IAAI;IACJ,MAAM;IACN,MAAM;IACN,aACE;IACF,YAAY;IACZ,WAAW;IACX,SAAS;IACT,gBAAgB;IAChB,gBAAgB;IAChB,iBAAiB;IACjB,eAAe;IACf,QAAQ;IACR,SAAS;QACP;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,YAAY;YACZ,sBAAsB;YACtB,UAAU;gBACR;oBACE,IAAI;oBACJ,OAAO;oBACP,OAAO;oBACP,YAAY;oBACZ,sBAAsB;oBACtB,UAAU;wBACR;4BACE,IAAI;4BACJ,OAAO;4BACP,MAAM;4BACN,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kGAwD0E,CAAC;4BACrF,aAAa;4BACb,WAAW;wBACb;wBACA;4BACE,IAAI;4BACJ,OAAO;4BACP,MAAM;4BACN,SAAS;4BACT,UAAU;4BACV,aAAa;4BACb,WAAW;wBACb;wBACA;4BACE,IAAI;4BACJ,OAAO;4BACP,MAAM;4BACN,SAAS;4BACT,aAAa;4BACb,WAAW;wBACb;qBACD;oBACD,MAAM;wBACJ,IAAI;wBACJ,OAAO;wBACP,MAAM;wBACN,WAAW;4BACT;gCACE,IAAI;gCACJ,UACE;gCACF,MAAM;gCACN,SAAS;oCAAC;oCAAS;oCAAS;oCAAc;iCAAY;gCACtD,eAAe;gCACf,aACE;4BACJ;yBACD;wBACD,cAAc;wBACd,WAAW;wBACX,UAAU;wBACV,aAAa;wBACb,UAAU;oBACZ;gBACF;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,OAAO;oBACP,YAAY;oBACZ,sBAAsB;oBACtB,UAAU;wBACR;4BACE,IAAI;4BACJ,OAAO;4BACP,MAAM;4BACN,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8FAqEsE,CAAC;4BACjF,aAAa;4BACb,WAAW;wBACb;wBACA;4BACE,IAAI;4BACJ,OAAO;4BACP,MAAM;4BACN,SAAS;4BACT,UAAU;4BACV,aAAa;4BACb,WAAW;wBACb;qBACD;oBACD,MAAM;wBACJ,IAAI;wBACJ,OAAO;wBACP,MAAM;wBACN,WAAW;4BACT;gCACE,IAAI;gCACJ,UAAU;gCACV,MAAM;gCACN,SAAS;oCAAC;oCAAS;oCAAS;oCAAS;iCAAQ;gCAC7C,eAAe;gCACf,aACE;4BACJ;4BACA;gCACE,IAAI;gCACJ,UACE;gCACF,MAAM;gCACN,eAAe;gCACf,aACE;4BACJ;yBACD;wBACD,cAAc;wBACd,UAAU;wBACV,aAAa;wBACb,UAAU;oBACZ;gBACF;aACD;YACD,YAAY;gBACV,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,WAAW;oBACT;wBACE,IAAI;wBACJ,UACE;wBACF,MAAM;wBACN,SAAS;4BAAC;4BAAS;4BAAS;4BAAc;yBAAmB;wBAC7D,eAAe;wBACf,aACE;oBACJ;iBACD;gBACD,cAAc;gBACd,UAAU;gBACV,aAAa;gBACb,UAAU;YACZ;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,YAAY;YACZ,sBAAsB;YACtB,UAAU;gBACR;oBACE,IAAI;oBACJ,OAAO;oBACP,OAAO;oBACP,YAAY;oBACZ,sBAAsB;oBACtB,UAAU;wBACR;4BACE,IAAI;4BACJ,OAAO;4BACP,MAAM;4BACN,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6IAwGqH,CAAC;4BAChI,aAAa;4BACb,WAAW;wBACb;qBACD;oBACD,MAAM;wBACJ,IAAI;wBACJ,OAAO;wBACP,MAAM;wBACN,WAAW;4BACT;gCACE,IAAI;gCACJ,UACE;gCACF,MAAM;gCACN,eAAe;gCACf,aACE;4BACJ;yBACD;wBACD,cAAc;wBACd,UAAU;wBACV,aAAa;wBACb,UAAU;oBACZ;gBACF;aACD;YACD,YAAY;gBACV,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,WAAW;oBACT;wBACE,IAAI;wBACJ,UAAU;wBACV,MAAM;wBACN,eACE;wBACF,aACE;oBACJ;iBACD;gBACD,cAAc;gBACd,UAAU;gBACV,aAAa;gBACb,UAAU;YACZ;QACF;KACD;IACD,WAAW;QACT,IAAI;QACJ,OAAO;QACP,MAAM;QACN,WAAW;YACT;gBACE,IAAI;gBACJ,UACE;gBACF,MAAM;gBACN,SAAS;oBAAC;oBAAS;oBAAS;oBAAc;iBAAS;gBACnD,eAAe;gBACf,aACE;YACJ;YACA;gBACE,IAAI;gBACJ,UACE;gBACF,MAAM;gBACN,eAAe;gBACf,aACE;YACJ;YACA;gBACE,IAAI;gBACJ,UACE;gBACF,MAAM;gBACN,eACE;gBACF,aACE;YACJ;SACD;QACD,cAAc;QACd,WAAW;QACX,UAAU;QACV,aAAa;QACb,UAAU;IACZ;IACA,aAAa;QACX,YAAY;QACZ,aAAa;QACb,MAAM;QACN,UAAU;QACV,gBAAgB;QAChB,cAAc;QACd,aAAa;IACf;AACF;AAgBO,MAAM,oBAA+B;IAC1C,IAAI;IACJ,MAAM,mBAAmB,IAAI;IAC7B,SAAS,mBAAmB,UAAU;IACtC,UAAU;IACV,gBAAgB,mBAAmB,cAAc,IAAI;IACrD,aAAa,mBAAmB,WAAW;IAC3C,WAAW,mBAAmB,SAAS;IACvC,SAAS,mBAAmB,OAAO;IACnC,WAAW,EAAE,CAAC,+CAA+C;AAC/D;AAGO,MAAM,0BAA0B;IACrC,IAAI;IACJ,MAAM;IACN,WAAW;IACX,SAAS;IACT,qBAAqB;QACnB,cAAc;QACd,gBAAgB;QAChB,eAAe;QACf,gBAAgB;IAClB;AACF", "debugId": null}}, {"offset": {"line": 1046, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/contexts/enrollment-context.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\r\nimport { architectureCourse } from '@/constants/shared-course-data';\r\nimport { Course } from '@/types/lms';\r\n\r\ninterface EnrollmentData {\r\n  isEnrolled: boolean;\r\n  courseData: Course;\r\n  enrollmentTimestamp: number;\r\n  expirationTime: number; // 10 minutes in milliseconds\r\n}\r\n\r\ninterface MultipleEnrollmentData {\r\n  enrolledCourses: Course[];\r\n  enrollmentTimestamp: number;\r\n  expirationTime: number; // 10 minutes in milliseconds\r\n}\r\n\r\ninterface EnrollmentContextType {\r\n  isEnrolled: boolean;\r\n  courseData: Course;\r\n  enrollInCourse: () => void;\r\n  enrollInCourseWithPurchase: (course: Course) => void;\r\n  updateCourseProgress: (updatedCourse: Course) => void;\r\n  enrolledCourses: Course[];\r\n  isEnrolledInCourse: (courseId: string) => boolean;\r\n  getCourseById: (courseId: string) => Course | undefined;\r\n}\r\n\r\nconst EnrollmentContext = createContext<EnrollmentContextType | undefined>(\r\n  undefined\r\n);\r\n\r\nexport const useEnrollment = () => {\r\n  const context = useContext(EnrollmentContext);\r\n  if (!context) {\r\n    throw new Error('useEnrollment must be used within an EnrollmentProvider');\r\n  }\r\n  return context;\r\n};\r\n\r\ninterface EnrollmentProviderProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport const EnrollmentProvider: React.FC<EnrollmentProviderProps> = ({\r\n  children\r\n}) => {\r\n  const [isEnrolled, setIsEnrolled] = useState(false);\r\n  const [courseData, setCourseData] = useState<Course>(architectureCourse);\r\n  const [enrolledCourses, setEnrolledCourses] = useState<Course[]>([]);\r\n\r\n  const STORAGE_KEY = 'lms-enrollment-data';\r\n  const MULTIPLE_STORAGE_KEY = 'lms-multiple-enrollment-data';\r\n  const EXPIRATION_TIME = 10 * 60 * 1000; // 10 minutes in milliseconds\r\n\r\n  // Load persisted data on component mount\r\n  useEffect(() => {\r\n    const loadPersistedData = () => {\r\n      try {\r\n        // Try to load multiple enrollment data first\r\n        const multipleStored = localStorage.getItem(MULTIPLE_STORAGE_KEY);\r\n        if (multipleStored) {\r\n          const multipleData: MultipleEnrollmentData = JSON.parse(multipleStored);\r\n          const now = Date.now();\r\n\r\n          // Check if enrollment has expired\r\n          if (now < multipleData.expirationTime) {\r\n            setEnrolledCourses(multipleData.enrolledCourses);\r\n            setIsEnrolled(multipleData.enrolledCourses.length > 0);\r\n            if (multipleData.enrolledCourses.length > 0) {\r\n              setCourseData(multipleData.enrolledCourses[0]); // Set first course as primary\r\n            }\r\n          } else {\r\n            // Clear expired data\r\n            localStorage.removeItem(MULTIPLE_STORAGE_KEY);\r\n          }\r\n          return;\r\n        }\r\n\r\n        // Fallback to old single enrollment data for backward compatibility\r\n        const stored = localStorage.getItem(STORAGE_KEY);\r\n        if (stored) {\r\n          const enrollmentData: EnrollmentData = JSON.parse(stored);\r\n          const now = Date.now();\r\n\r\n          // Check if enrollment has expired\r\n          if (now < enrollmentData.expirationTime) {\r\n            setIsEnrolled(enrollmentData.isEnrolled);\r\n            setCourseData(enrollmentData.courseData);\r\n            setEnrolledCourses([enrollmentData.courseData]);\r\n\r\n            // Migrate to new format\r\n            const multipleData: MultipleEnrollmentData = {\r\n              enrolledCourses: [enrollmentData.courseData],\r\n              enrollmentTimestamp: enrollmentData.enrollmentTimestamp,\r\n              expirationTime: enrollmentData.expirationTime\r\n            };\r\n            localStorage.setItem(MULTIPLE_STORAGE_KEY, JSON.stringify(multipleData));\r\n            localStorage.removeItem(STORAGE_KEY); // Remove old format\r\n          } else {\r\n            // Clear expired data\r\n            localStorage.removeItem(STORAGE_KEY);\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('Failed to load enrollment data:', error);\r\n        localStorage.removeItem(STORAGE_KEY);\r\n        localStorage.removeItem(MULTIPLE_STORAGE_KEY);\r\n      }\r\n    };\r\n\r\n    loadPersistedData();\r\n  }, []);\r\n\r\n  // Persist enrollment data to localStorage\r\n  const persistEnrollmentData = (course: Course) => {\r\n    const now = Date.now();\r\n\r\n    try {\r\n      // Update enrolled courses state\r\n      setEnrolledCourses(prev => {\r\n        const isAlreadyEnrolled = prev.some(c => c.id === course.id);\r\n        let updatedCourses;\r\n\r\n        if (isAlreadyEnrolled) {\r\n          // Update existing course\r\n          updatedCourses = prev.map(c => c.id === course.id ? course : c);\r\n        } else {\r\n          // Add new course\r\n          updatedCourses = [...prev, course];\r\n        }\r\n\r\n        // Save to localStorage with new format\r\n        const multipleData: MultipleEnrollmentData = {\r\n          enrolledCourses: updatedCourses,\r\n          enrollmentTimestamp: now,\r\n          expirationTime: now + EXPIRATION_TIME\r\n        };\r\n        localStorage.setItem(MULTIPLE_STORAGE_KEY, JSON.stringify(multipleData));\r\n\r\n        return updatedCourses;\r\n      });\r\n\r\n      // Set up automatic cleanup after expiration\r\n      setTimeout(() => {\r\n        localStorage.removeItem(MULTIPLE_STORAGE_KEY);\r\n        setIsEnrolled(false);\r\n        setEnrolledCourses([]);\r\n        setCourseData(architectureCourse);\r\n      }, EXPIRATION_TIME);\r\n    } catch (error) {\r\n      console.error('Failed to persist enrollment data:', error);\r\n    }\r\n  };\r\n\r\n  const enrollInCourse = () => {\r\n    setIsEnrolled(true);\r\n    const updatedCourse = {\r\n      ...architectureCourse,\r\n      status: 'in-progress' as const\r\n    };\r\n    setCourseData(updatedCourse);\r\n    persistEnrollmentData(updatedCourse);\r\n  };\r\n\r\n  const enrollInCourseWithPurchase = (course: Course) => {\r\n    setIsEnrolled(true);\r\n    const updatedCourse = {\r\n      ...course,\r\n      status: 'in-progress' as const,\r\n      totalProgress: 0\r\n    };\r\n    setCourseData(updatedCourse);\r\n    persistEnrollmentData(updatedCourse);\r\n  };\r\n\r\n  const updateCourseProgress = (updatedCourse: Course) => {\r\n    // Update the primary courseData if it's the same course\r\n    if (courseData.id === updatedCourse.id) {\r\n      setCourseData(updatedCourse);\r\n    }\r\n\r\n    // Update the course in enrolledCourses array\r\n    setEnrolledCourses(prev =>\r\n      prev.map(course =>\r\n        course.id === updatedCourse.id ? updatedCourse : course\r\n      )\r\n    );\r\n\r\n    // Update persisted data with new progress\r\n    if (isEnrolled) {\r\n      persistEnrollmentData(updatedCourse);\r\n    }\r\n  };\r\n\r\n  // Check if user is enrolled in a specific course\r\n  const isEnrolledInCourse = (courseId: string): boolean => {\r\n    return enrolledCourses.some(course => course.id === courseId);\r\n  };\r\n\r\n  // Get a specific course by ID\r\n  const getCourseById = (courseId: string): Course | undefined => {\r\n    return enrolledCourses.find(course => course.id === courseId);\r\n  };\r\n\r\n  const value = {\r\n    isEnrolled,\r\n    courseData,\r\n    enrollInCourse,\r\n    enrollInCourseWithPurchase,\r\n    updateCourseProgress,\r\n    enrolledCourses,\r\n    isEnrolledInCourse,\r\n    getCourseById\r\n  };\r\n\r\n  return (\r\n    <EnrollmentContext.Provider value={value}>\r\n      {children}\r\n    </EnrollmentContext.Provider>\r\n  );\r\n};"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AA8BA,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EACpC;AAGK,MAAM,gBAAgB;;IAC3B,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANa;AAYN,MAAM,qBAAwD,CAAC,EACpE,QAAQ,EACT;;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,+IAAA,CAAA,qBAAkB;IACvE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAEnE,MAAM,cAAc;IACpB,MAAM,uBAAuB;IAC7B,MAAM,kBAAkB,KAAK,KAAK,MAAM,6BAA6B;IAErE,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,MAAM;kEAAoB;oBACxB,IAAI;wBACF,6CAA6C;wBAC7C,MAAM,iBAAiB,aAAa,OAAO,CAAC;wBAC5C,IAAI,gBAAgB;4BAClB,MAAM,eAAuC,KAAK,KAAK,CAAC;4BACxD,MAAM,MAAM,KAAK,GAAG;4BAEpB,kCAAkC;4BAClC,IAAI,MAAM,aAAa,cAAc,EAAE;gCACrC,mBAAmB,aAAa,eAAe;gCAC/C,cAAc,aAAa,eAAe,CAAC,MAAM,GAAG;gCACpD,IAAI,aAAa,eAAe,CAAC,MAAM,GAAG,GAAG;oCAC3C,cAAc,aAAa,eAAe,CAAC,EAAE,GAAG,8BAA8B;gCAChF;4BACF,OAAO;gCACL,qBAAqB;gCACrB,aAAa,UAAU,CAAC;4BAC1B;4BACA;wBACF;wBAEA,oEAAoE;wBACpE,MAAM,SAAS,aAAa,OAAO,CAAC;wBACpC,IAAI,QAAQ;4BACV,MAAM,iBAAiC,KAAK,KAAK,CAAC;4BAClD,MAAM,MAAM,KAAK,GAAG;4BAEpB,kCAAkC;4BAClC,IAAI,MAAM,eAAe,cAAc,EAAE;gCACvC,cAAc,eAAe,UAAU;gCACvC,cAAc,eAAe,UAAU;gCACvC,mBAAmB;oCAAC,eAAe,UAAU;iCAAC;gCAE9C,wBAAwB;gCACxB,MAAM,eAAuC;oCAC3C,iBAAiB;wCAAC,eAAe,UAAU;qCAAC;oCAC5C,qBAAqB,eAAe,mBAAmB;oCACvD,gBAAgB,eAAe,cAAc;gCAC/C;gCACA,aAAa,OAAO,CAAC,sBAAsB,KAAK,SAAS,CAAC;gCAC1D,aAAa,UAAU,CAAC,cAAc,oBAAoB;4BAC5D,OAAO;gCACL,qBAAqB;gCACrB,aAAa,UAAU,CAAC;4BAC1B;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,mCAAmC;wBACjD,aAAa,UAAU,CAAC;wBACxB,aAAa,UAAU,CAAC;oBAC1B;gBACF;;YAEA;QACF;uCAAG,EAAE;IAEL,0CAA0C;IAC1C,MAAM,wBAAwB,CAAC;QAC7B,MAAM,MAAM,KAAK,GAAG;QAEpB,IAAI;YACF,gCAAgC;YAChC,mBAAmB,CAAA;gBACjB,MAAM,oBAAoB,KAAK,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,OAAO,EAAE;gBAC3D,IAAI;gBAEJ,IAAI,mBAAmB;oBACrB,yBAAyB;oBACzB,iBAAiB,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,OAAO,EAAE,GAAG,SAAS;gBAC/D,OAAO;oBACL,iBAAiB;oBACjB,iBAAiB;2BAAI;wBAAM;qBAAO;gBACpC;gBAEA,uCAAuC;gBACvC,MAAM,eAAuC;oBAC3C,iBAAiB;oBACjB,qBAAqB;oBACrB,gBAAgB,MAAM;gBACxB;gBACA,aAAa,OAAO,CAAC,sBAAsB,KAAK,SAAS,CAAC;gBAE1D,OAAO;YACT;YAEA,4CAA4C;YAC5C,WAAW;gBACT,aAAa,UAAU,CAAC;gBACxB,cAAc;gBACd,mBAAmB,EAAE;gBACrB,cAAc,+IAAA,CAAA,qBAAkB;YAClC,GAAG;QACL,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;QACtD;IACF;IAEA,MAAM,iBAAiB;QACrB,cAAc;QACd,MAAM,gBAAgB;YACpB,GAAG,+IAAA,CAAA,qBAAkB;YACrB,QAAQ;QACV;QACA,cAAc;QACd,sBAAsB;IACxB;IAEA,MAAM,6BAA6B,CAAC;QAClC,cAAc;QACd,MAAM,gBAAgB;YACpB,GAAG,MAAM;YACT,QAAQ;YACR,eAAe;QACjB;QACA,cAAc;QACd,sBAAsB;IACxB;IAEA,MAAM,uBAAuB,CAAC;QAC5B,wDAAwD;QACxD,IAAI,WAAW,EAAE,KAAK,cAAc,EAAE,EAAE;YACtC,cAAc;QAChB;QAEA,6CAA6C;QAC7C,mBAAmB,CAAA,OACjB,KAAK,GAAG,CAAC,CAAA,SACP,OAAO,EAAE,KAAK,cAAc,EAAE,GAAG,gBAAgB;QAIrD,0CAA0C;QAC1C,IAAI,YAAY;YACd,sBAAsB;QACxB;IACF;IAEA,iDAAiD;IACjD,MAAM,qBAAqB,CAAC;QAC1B,OAAO,gBAAgB,IAAI,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;IACtD;IAEA,8BAA8B;IAC9B,MAAM,gBAAgB,CAAC;QACrB,OAAO,gBAAgB,IAAI,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;IACtD;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,kBAAkB,QAAQ;QAAC,OAAO;kBAChC;;;;;;AAGP;IAjLa;KAAA", "debugId": null}}]}