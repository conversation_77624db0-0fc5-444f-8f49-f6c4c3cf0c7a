{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next|static|.*\\..*|_static|_vercel).*){(\\\\.json)}?", "originalSource": "/((?!_next|static|.*\\..*|_static|_vercel).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "oQU80BEb9Ysp0y5Cc6KvygFOTlbLolBab1Eeg0Ic8No=", "__NEXT_PREVIEW_MODE_ID": "15e0fb76919bf81c6ad8e91dee0590c0", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d00ad35a6ffb492e168006d4f1637c90f06c0a44942908fcdf5483ef32cf91ab", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "33f108a849c1ae68643a08de39f424bad358e88cd51681e1b25a1d83be37a96e"}}}, "instrumentation": null, "functions": {}}