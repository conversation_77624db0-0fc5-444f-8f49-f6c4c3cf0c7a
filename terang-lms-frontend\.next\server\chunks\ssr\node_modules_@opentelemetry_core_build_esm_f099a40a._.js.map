{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "constants.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40opentelemetry/core/src/baggage/constants.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport const BAGGAGE_KEY_PAIR_SEPARATOR = '=';\nexport const BAGGAGE_PROPERTIES_SEPARATOR = ';';\nexport const BAGGAGE_ITEMS_SEPARATOR = ',';\n\n// Name of the http header used to propagate the baggage\nexport const BAGGAGE_HEADER = 'baggage';\n// Maximum number of name-value pairs allowed by w3c spec\nexport const BAGGAGE_MAX_NAME_VALUE_PAIRS = 180;\n// Maximum number of bytes per a single name-value pair allowed by w3c spec\nexport const BAGGAGE_MAX_PER_NAME_VALUE_PAIRS = 4096;\n// Maximum total length of all name-value pairs allowed by w3c spec\nexport const BAGGAGE_MAX_TOTAL_LENGTH = 8192;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;AAEI,IAAM,0BAA0B,GAAG,GAAG,CAAC;AACvC,IAAM,4BAA4B,GAAG,GAAG,CAAC;AACzC,IAAM,uBAAuB,GAAG,GAAG,CAAC;AAGpC,IAAM,cAAc,GAAG,SAAS,CAAC;AAEjC,IAAM,4BAA4B,GAAG,GAAG,CAAC;AAEzC,IAAM,gCAAgC,GAAG,IAAI,CAAC;AAE9C,IAAM,wBAAwB,GAAG,IAAI,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40opentelemetry/core/src/baggage/utils.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  Baggage,\n  BaggageEntryMetadata,\n  baggageEntryMetadataFromString,\n} from '@opentelemetry/api';\nimport {\n  BAGGAGE_ITEMS_SEPARATOR,\n  BAGGAGE_PROPERTIES_SEPARATOR,\n  BAGGAGE_KEY_PAIR_SEPARATOR,\n  BAGGAGE_MAX_TOTAL_LENGTH,\n} from './constants';\n\ntype ParsedBaggageKeyValue = {\n  key: string;\n  value: string;\n  metadata: BaggageEntryMetadata | undefined;\n};\n\nexport function serializeKeyPairs(keyPairs: string[]): string {\n  return keyPairs.reduce((hValue: string, current: string) => {\n    const value = `${hValue}${\n      hValue !== '' ? BAGGAGE_ITEMS_SEPARATOR : ''\n    }${current}`;\n    return value.length > BAGGAGE_MAX_TOTAL_LENGTH ? hValue : value;\n  }, '');\n}\n\nexport function getKeyPairs(baggage: Baggage): string[] {\n  return baggage.getAllEntries().map(([key, value]) => {\n    let entry = `${encodeURIComponent(key)}=${encodeURIComponent(value.value)}`;\n\n    // include opaque metadata if provided\n    // NOTE: we intentionally don't URI-encode the metadata - that responsibility falls on the metadata implementation\n    if (value.metadata !== undefined) {\n      entry += BAGGAGE_PROPERTIES_SEPARATOR + value.metadata.toString();\n    }\n\n    return entry;\n  });\n}\n\nexport function parsePairKeyValue(\n  entry: string\n): ParsedBaggageKeyValue | undefined {\n  const valueProps = entry.split(BAGGAGE_PROPERTIES_SEPARATOR);\n  if (valueProps.length <= 0) return;\n  const keyPairPart = valueProps.shift();\n  if (!keyPairPart) return;\n  const separatorIndex = keyPairPart.indexOf(BAGGAGE_KEY_PAIR_SEPARATOR);\n  if (separatorIndex <= 0) return;\n  const key = decodeURIComponent(\n    keyPairPart.substring(0, separatorIndex).trim()\n  );\n  const value = decodeURIComponent(\n    keyPairPart.substring(separatorIndex + 1).trim()\n  );\n  let metadata;\n  if (valueProps.length > 0) {\n    metadata = baggageEntryMetadataFromString(\n      valueProps.join(BAGGAGE_PROPERTIES_SEPARATOR)\n    );\n  }\n  return { key, value, metadata };\n}\n\n/**\n * Parse a string serialized in the baggage HTTP Format (without metadata):\n * https://github.com/w3c/baggage/blob/master/baggage/HTTP_HEADER_FORMAT.md\n */\nexport function parseKeyPairsIntoRecord(\n  value?: string\n): Record<string, string> {\n  if (typeof value !== 'string' || value.length === 0) return {};\n  return value\n    .split(BAGGAGE_ITEMS_SEPARATOR)\n    .map(entry => {\n      return parsePairKeyValue(entry);\n    })\n    .filter(keyPair => keyPair !== undefined && keyPair.value.length > 0)\n    .reduce<Record<string, string>>((headers, keyPair) => {\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      headers[keyPair!.key] = keyPair!.value;\n      return headers;\n    }, {});\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;;;;;;;;;;;;;GAcG,CACH,OAAO,EAGL,8BAA8B,GAC/B,MAAM,oBAAoB,CAAC;AAC5B,OAAO,EACL,uBAAuB,EACvB,4BAA4B,EAC5B,0BAA0B,EAC1B,wBAAwB,GACzB,MAAM,aAAa,CAAC;;;;;;;;;;;;;;;;;;;;;;AAQf,SAAU,iBAAiB,CAAC,QAAkB;IAClD,OAAO,QAAQ,CAAC,MAAM,CAAC,SAAC,MAAc,EAAE,OAAe;QACrD,IAAM,KAAK,GAAG,KAAG,MAAM,GAAA,CACrB,MAAM,KAAK,EAAE,CAAC,CAAC,iLAAC,0BAAuB,CAAC,CAAC,CAAC,EAAE,IAC3C,OAAS,CAAC;QACb,OAAO,KAAK,CAAC,MAAM,mLAAG,2BAAwB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC;IAClE,CAAC,EAAE,EAAE,CAAC,CAAC;AACT,CAAC;AAEK,SAAU,WAAW,CAAC,OAAgB;IAC1C,OAAO,OAAO,CAAC,aAAa,EAAE,CAAC,GAAG,CAAC,SAAC,EAAY;YAAZ,KAAA,OAAA,IAAA,EAAY,EAAX,GAAG,GAAA,EAAA,CAAA,EAAA,EAAE,KAAK,GAAA,EAAA,CAAA,EAAA;QAC7C,IAAI,KAAK,GAAM,kBAAkB,CAAC,GAAG,CAAC,GAAA,MAAI,kBAAkB,CAAC,KAAK,CAAC,KAAK,CAAG,CAAC;QAE5E,sCAAsC;QACtC,kHAAkH;QAClH,IAAI,KAAK,CAAC,QAAQ,KAAK,SAAS,EAAE;YAChC,KAAK,oLAAI,+BAA4B,GAAG,KAAK,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;SACnE;QAED,OAAO,KAAK,CAAC;IACf,CAAC,CAAC,CAAC;AACL,CAAC;AAEK,SAAU,iBAAiB,CAC/B,KAAa;IAEb,IAAM,UAAU,GAAG,KAAK,CAAC,KAAK,iLAAC,+BAA4B,CAAC,CAAC;IAC7D,IAAI,UAAU,CAAC,MAAM,IAAI,CAAC,EAAE,OAAO;IACnC,IAAM,WAAW,GAAG,UAAU,CAAC,KAAK,EAAE,CAAC;IACvC,IAAI,CAAC,WAAW,EAAE,OAAO;IACzB,IAAM,cAAc,GAAG,WAAW,CAAC,OAAO,iLAAC,6BAA0B,CAAC,CAAC;IACvE,IAAI,cAAc,IAAI,CAAC,EAAE,OAAO;IAChC,IAAM,GAAG,GAAG,kBAAkB,CAC5B,WAAW,CAAC,SAAS,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,IAAI,EAAE,CAChD,CAAC;IACF,IAAM,KAAK,GAAG,kBAAkB,CAC9B,WAAW,CAAC,SAAS,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,CACjD,CAAC;IACF,IAAI,QAAQ,CAAC;IACb,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;QACzB,QAAQ,6IAAG,iCAAA,AAA8B,EACvC,UAAU,CAAC,IAAI,iLAAC,+BAA4B,CAAC,CAC9C,CAAC;KACH;IACD,OAAO;QAAE,GAAG,EAAA,GAAA;QAAE,KAAK,EAAA,KAAA;QAAE,QAAQ,EAAA,QAAA;IAAA,CAAE,CAAC;AAClC,CAAC;AAMK,SAAU,uBAAuB,CACrC,KAAc;IAEd,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,OAAO,CAAA,CAAE,CAAC;IAC/D,OAAO,KAAK,CACT,KAAK,iLAAC,0BAAuB,CAAC,CAC9B,GAAG,CAAC,SAAA,KAAK;QACR,OAAO,iBAAiB,CAAC,KAAK,CAAC,CAAC;IAClC,CAAC,CAAC,CACD,MAAM,CAAC,SAAA,OAAO;QAAI,OAAA,OAAO,KAAK,SAAS,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC;IAAjD,CAAiD,CAAC,CACpE,MAAM,CAAyB,SAAC,OAAO,EAAE,OAAO;QAC/C,oEAAoE;QACpE,OAAO,CAAC,OAAQ,CAAC,GAAG,CAAC,GAAG,OAAQ,CAAC,KAAK,CAAC;QACvC,OAAO,OAAO,CAAC;IACjB,CAAC,EAAE,CAAA,CAAE,CAAC,CAAC;AACX,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 141, "column": 0}, "map": {"version": 3, "file": "suppress-tracing.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40opentelemetry/core/src/trace/suppress-tracing.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context, createContextKey } from '@opentelemetry/api';\n\nconst SUPPRESS_TRACING_KEY = createContextKey(\n  'OpenTelemetry SDK Context Key SUPPRESS_TRACING'\n);\n\nexport function suppressTracing(context: Context): Context {\n  return context.setValue(SUPPRESS_TRACING_KEY, true);\n}\n\nexport function unsuppressTracing(context: Context): Context {\n  return context.deleteValue(SUPPRESS_TRACING_KEY);\n}\n\nexport function isTracingSuppressed(context: Context): boolean {\n  return context.getValue(SUPPRESS_TRACING_KEY) === true;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;AAEH,OAAO,EAAW,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;;AAE/D,IAAM,oBAAoB,6IAAG,mBAAA,AAAgB,EAC3C,gDAAgD,CACjD,CAAC;AAEI,SAAU,eAAe,CAAC,OAAgB;IAC9C,OAAO,OAAO,CAAC,QAAQ,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;AACtD,CAAC;AAEK,SAAU,iBAAiB,CAAC,OAAgB;IAChD,OAAO,OAAO,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC;AACnD,CAAC;AAEK,SAAU,mBAAmB,CAAC,OAAgB;IAClD,OAAO,OAAO,CAAC,QAAQ,CAAC,oBAAoB,CAAC,KAAK,IAAI,CAAC;AACzD,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 178, "column": 0}, "map": {"version": 3, "file": "exporter.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40opentelemetry/core/src/internal/exporter.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { context } from '@opentelemetry/api';\nimport { ExportResult } from '../ExportResult';\nimport { suppressTracing } from '../trace/suppress-tracing';\n\nexport interface Exporter<T> {\n  export(arg: T, resultCallback: (result: ExportResult) => void): void;\n}\n\n/**\n * @internal\n * Shared functionality used by Exporters while exporting data, including suppression of Traces.\n */\nexport function _export<T>(\n  exporter: Exporter<T>,\n  arg: T\n): Promise<ExportResult> {\n  return new Promise(resolve => {\n    // prevent downstream exporter calls from generating spans\n    context.with(suppressTracing(context.active()), () => {\n      exporter.export(arg, (result: ExportResult) => {\n        resolve(result);\n      });\n    });\n  });\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,OAAO,EAAE,MAAM,oBAAoB,CAAC;AAE7C,OAAO,EAAE,eAAe,EAAE,MAAM,2BAA2B,CAAC;;;AAUtD,SAAU,OAAO,CACrB,QAAqB,EACrB,GAAM;IAEN,OAAO,IAAI,OAAO,CAAC,SAAA,OAAO;QACxB,0DAA0D;8IAC1D,UAAO,CAAC,IAAI,6LAAC,kBAAA,AAAe,wIAAC,UAAO,CAAC,MAAM,EAAE,CAAC,EAAE;YAC9C,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,SAAC,MAAoB;gBACxC,OAAO,CAAC,MAAM,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 215, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40opentelemetry/core/src/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { W3CBaggagePropagator } from './baggage/propagation/W3CBaggagePropagator';\nexport { AnchoredClock, Clock } from './common/anchored-clock';\nexport {\n  isAttributeKey,\n  isAttributeValue,\n  sanitizeAttributes,\n} from './common/attributes';\nexport {\n  globalErrorHandler,\n  setGlobalErrorHandler,\n} from './common/global-error-handler';\nexport { loggingErrorHandler } from './common/logging-error-handler';\nexport {\n  addHrTimes,\n  getTimeOrigin,\n  hrTime,\n  hrTimeDuration,\n  hrTimeToMicroseconds,\n  hrTimeToMilliseconds,\n  hrTimeToNanoseconds,\n  hrTimeToTimeStamp,\n  isTimeInput,\n  isTimeInputHrTime,\n  millisToHrTime,\n  timeInputToHrTime,\n} from './common/time';\nexport {\n  ErrorHandler,\n  InstrumentationLibrary,\n  InstrumentationScope,\n  ShimWrapped,\n  TimeOriginLegacy,\n} from './common/types';\nexport { hexToBinary } from './common/hex-to-binary';\nexport { ExportResult, ExportResultCode } from './ExportResult';\nimport {\n  getKeyPairs,\n  serializeKeyPairs,\n  parseKeyPairsIntoRecord,\n  parsePairKeyValue,\n} from './baggage/utils';\nexport const baggageUtils = {\n  getKeyPairs,\n  serializeKeyPairs,\n  parseKeyPairsIntoRecord,\n  parsePairKeyValue,\n};\nexport {\n  RandomIdGenerator,\n  SDK_INFO,\n  _globalThis,\n  getEnv,\n  getEnvWithoutDefaults,\n  hexToBase64,\n  otperformance,\n  unrefTimer,\n} from './platform';\nexport {\n  CompositePropagator,\n  CompositePropagatorConfig,\n} from './propagation/composite';\nexport {\n  TRACE_PARENT_HEADER,\n  TRACE_STATE_HEADER,\n  W3CTraceContextPropagator,\n  parseTraceParent,\n} from './trace/W3CTraceContextPropagator';\nexport { IdGenerator } from './trace/IdGenerator';\nexport {\n  RPCMetadata,\n  RPCType,\n  deleteRPCMetadata,\n  getRPCMetadata,\n  setRPCMetadata,\n} from './trace/rpc-metadata';\nexport { AlwaysOffSampler } from './trace/sampler/AlwaysOffSampler';\nexport { AlwaysOnSampler } from './trace/sampler/AlwaysOnSampler';\nexport { ParentBasedSampler } from './trace/sampler/ParentBasedSampler';\nexport { TraceIdRatioBasedSampler } from './trace/sampler/TraceIdRatioBasedSampler';\nexport {\n  isTracingSuppressed,\n  suppressTracing,\n  unsuppressTracing,\n} from './trace/suppress-tracing';\nexport { TraceState } from './trace/TraceState';\nexport {\n  DEFAULT_ATTRIBUTE_COUNT_LIMIT,\n  DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT,\n  DEFAULT_ENVIRONMENT,\n  DEFAULT_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT,\n  DEFAULT_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT,\n  ENVIRONMENT,\n  RAW_ENVIRONMENT,\n  parseEnvironment,\n} from './utils/environment';\nexport { merge } from './utils/merge';\nexport { TracesSamplerValues } from './utils/sampling';\nexport { TimeoutError, callWithTimeout } from './utils/timeout';\nexport { isUrlIgnored, urlMatches } from './utils/url';\nexport { isWrapped } from './utils/wrap';\nexport { BindOnceFuture } from './utils/callback';\nexport { VERSION } from './version';\nimport { _export } from './internal/exporter';\nexport const internal = {\n  _export,\n};\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAqCH,OAAO,EACL,WAAW,EACX,iBAAiB,EACjB,uBAAuB,EACvB,iBAAiB,GAClB,MAAM,iBAAiB,CAAC;AA8DzB,OAAO,EAAE,OAAO,EAAE,MAAM,qBAAqB,CAAC;;;;;;;;;;AA7DvC,IAAM,YAAY,GAAG;IAC1B,WAAW,EAAA,2KAAA,CAAA,cAAA;IACX,iBAAiB,EAAA,2KAAA,CAAA,oBAAA;IACjB,uBAAuB,EAAA,2KAAA,CAAA,0BAAA;IACvB,iBAAiB,EAAA,2KAAA,CAAA,oBAAA;CAClB,CAAC;;;;;;;;;;;;;;;;;;;;AAyDK,IAAM,QAAQ,GAAG;IACtB,OAAO,EAAA,+KAAA,CAAA,UAAA;CACR,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 286, "column": 0}, "map": {"version": 3, "file": "W3CBaggagePropagator.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40opentelemetry/core/src/baggage/propagation/W3CBaggagePropagator.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  BaggageEntry,\n  Context,\n  propagation,\n  TextMapGetter,\n  TextMapPropagator,\n  TextMapSetter,\n} from '@opentelemetry/api';\n\nimport { isTracingSuppressed } from '../../trace/suppress-tracing';\nimport {\n  BAGGAGE_HEADER,\n  BAGGAGE_ITEMS_SEPARATOR,\n  BAGGAGE_MAX_NAME_VALUE_PAIRS,\n  BAGGAGE_MAX_PER_NAME_VALUE_PAIRS,\n} from '../constants';\nimport { getKeyPairs, parsePairKeyValue, serializeKeyPairs } from '../utils';\n\n/**\n * Propagates {@link Baggage} through Context format propagation.\n *\n * Based on the Baggage specification:\n * https://w3c.github.io/baggage/\n */\nexport class W3CBaggagePropagator implements TextMapPropagator {\n  inject(context: Context, carrier: unknown, setter: TextMapSetter): void {\n    const baggage = propagation.getBaggage(context);\n    if (!baggage || isTracingSuppressed(context)) return;\n    const keyPairs = getKeyPairs(baggage)\n      .filter((pair: string) => {\n        return pair.length <= BAGGAGE_MAX_PER_NAME_VALUE_PAIRS;\n      })\n      .slice(0, BAGGAGE_MAX_NAME_VALUE_PAIRS);\n    const headerValue = serializeKeyPairs(keyPairs);\n    if (headerValue.length > 0) {\n      setter.set(carrier, BAGGAGE_HEADER, headerValue);\n    }\n  }\n\n  extract(context: Context, carrier: unknown, getter: TextMapGetter): Context {\n    const headerValue = getter.get(carrier, BAGGAGE_HEADER);\n    const baggageString = Array.isArray(headerValue)\n      ? headerValue.join(BAGGAGE_ITEMS_SEPARATOR)\n      : headerValue;\n    if (!baggageString) return context;\n    const baggage: Record<string, BaggageEntry> = {};\n    if (baggageString.length === 0) {\n      return context;\n    }\n    const pairs = baggageString.split(BAGGAGE_ITEMS_SEPARATOR);\n    pairs.forEach(entry => {\n      const keyPair = parsePairKeyValue(entry);\n      if (keyPair) {\n        const baggageEntry: BaggageEntry = { value: keyPair.value };\n        if (keyPair.metadata) {\n          baggageEntry.metadata = keyPair.metadata;\n        }\n        baggage[keyPair.key] = baggageEntry;\n      }\n    });\n    if (Object.entries(baggage).length === 0) {\n      return context;\n    }\n    return propagation.setBaggage(context, propagation.createBaggage(baggage));\n  }\n\n  fields(): string[] {\n    return [BAGGAGE_HEADER];\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAGL,WAAW,GAIZ,MAAM,oBAAoB,CAAC;AAE5B,OAAO,EAAE,mBAAmB,EAAE,MAAM,8BAA8B,CAAC;AACnE,OAAO,EACL,cAAc,EACd,uBAAuB,EACvB,4BAA4B,EAC5B,gCAAgC,GACjC,MAAM,cAAc,CAAC;AACtB,OAAO,EAAE,WAAW,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,MAAM,UAAU,CAAC;;;;;AAE7E;;;;;GAKG,CACH,IAAA,uBAAA;IAAA,SAAA,wBA6CA,CAAC;IA5CC,qBAAA,SAAA,CAAA,MAAM,GAAN,SAAO,OAAgB,EAAE,OAAgB,EAAE,MAAqB;QAC9D,IAAM,OAAO,yIAAG,cAAW,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAChD,IAAI,CAAC,OAAO,gMAAI,sBAAA,AAAmB,EAAC,OAAO,CAAC,EAAE,OAAO;QACrD,IAAM,QAAQ,GAAG,8LAAA,AAAW,EAAC,OAAO,CAAC,CAClC,MAAM,CAAC,SAAC,IAAY;YACnB,OAAO,IAAI,CAAC,MAAM,mLAAI,oCAAgC,CAAC;QACzD,CAAC,CAAC,CACD,KAAK,CAAC,CAAC,kLAAE,+BAA4B,CAAC,CAAC;QAC1C,IAAM,WAAW,GAAG,oMAAA,AAAiB,EAAC,QAAQ,CAAC,CAAC;QAChD,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1B,MAAM,CAAC,GAAG,CAAC,OAAO,kLAAE,iBAAc,EAAE,WAAW,CAAC,CAAC;SAClD;IACH,CAAC;IAED,qBAAA,SAAA,CAAA,OAAO,GAAP,SAAQ,OAAgB,EAAE,OAAgB,EAAE,MAAqB;QAC/D,IAAM,WAAW,GAAG,MAAM,CAAC,GAAG,CAAC,OAAO,kLAAE,iBAAc,CAAC,CAAC;QACxD,IAAM,aAAa,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,GAC5C,WAAW,CAAC,IAAI,iLAAC,0BAAuB,CAAC,GACzC,WAAW,CAAC;QAChB,IAAI,CAAC,aAAa,EAAE,OAAO,OAAO,CAAC;QACnC,IAAM,OAAO,GAAiC,CAAA,CAAE,CAAC;QACjD,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE;YAC9B,OAAO,OAAO,CAAC;SAChB;QACD,IAAM,KAAK,GAAG,aAAa,CAAC,KAAK,iLAAC,0BAAuB,CAAC,CAAC;QAC3D,KAAK,CAAC,OAAO,CAAC,SAAA,KAAK;YACjB,IAAM,OAAO,mLAAG,oBAAA,AAAiB,EAAC,KAAK,CAAC,CAAC;YACzC,IAAI,OAAO,EAAE;gBACX,IAAM,YAAY,GAAiB;oBAAE,KAAK,EAAE,OAAO,CAAC,KAAK;gBAAA,CAAE,CAAC;gBAC5D,IAAI,OAAO,CAAC,QAAQ,EAAE;oBACpB,YAAY,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;iBAC1C;gBACD,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC;aACrC;QACH,CAAC,CAAC,CAAC;QACH,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;YACxC,OAAO,OAAO,CAAC;SAChB;QACD,6IAAO,cAAW,CAAC,UAAU,CAAC,OAAO,wIAAE,cAAW,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC;IAC7E,CAAC;IAED,qBAAA,SAAA,CAAA,MAAM,GAAN;QACE,OAAO;4LAAC,iBAAc;SAAC,CAAC;IAC1B,CAAC;IACH,OAAA,oBAAC;AAAD,CAAC,AA7CD,IA6CC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 370, "column": 0}, "map": {"version": 3, "file": "anchored-clock.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40opentelemetry/core/src/common/anchored-clock.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport interface Clock {\n  /**\n   * Return the current time in milliseconds from some epoch such as the Unix epoch or process start\n   */\n  now(): number;\n}\n\n/**\n * A utility for returning wall times anchored to a given point in time. Wall time measurements will\n * not be taken from the system, but instead are computed by adding a monotonic clock time\n * to the anchor point.\n *\n * This is needed because the system time can change and result in unexpected situations like\n * spans ending before they are started. Creating an anchored clock for each local root span\n * ensures that span timings and durations are accurate while preventing span times from drifting\n * too far from the system clock.\n *\n * Only creating an anchored clock once per local trace ensures span times are correct relative\n * to each other. For example, a child span will never have a start time before its parent even\n * if the system clock is corrected during the local trace.\n *\n * Heavily inspired by the OTel Java anchored clock\n * https://github.com/open-telemetry/opentelemetry-java/blob/main/sdk/trace/src/main/java/io/opentelemetry/sdk/trace/AnchoredClock.java\n */\nexport class AnchoredClock implements Clock {\n  private _monotonicClock: Clock;\n  private _epochMillis: number;\n  private _performanceMillis: number;\n\n  /**\n   * Create a new AnchoredClock anchored to the current time returned by systemClock.\n   *\n   * @param systemClock should be a clock that returns the number of milliseconds since January 1 1970 such as Date\n   * @param monotonicClock should be a clock that counts milliseconds monotonically such as window.performance or perf_hooks.performance\n   */\n  public constructor(systemClock: Clock, monotonicClock: Clock) {\n    this._monotonicClock = monotonicClock;\n    this._epochMillis = systemClock.now();\n    this._performanceMillis = monotonicClock.now();\n  }\n\n  /**\n   * Returns the current time by adding the number of milliseconds since the\n   * AnchoredClock was created to the creation epoch time\n   */\n  public now(): number {\n    const delta = this._monotonicClock.now() - this._performanceMillis;\n    return this._epochMillis + delta;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CASH;;;;;;;;;;;;;;;;GAgBG;;;AACH,IAAA,gBAAA;IAKE;;;;;OAKG,CACH,SAAA,cAAmB,WAAkB,EAAE,cAAqB;QAC1D,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QACtC,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QACtC,IAAI,CAAC,kBAAkB,GAAG,cAAc,CAAC,GAAG,EAAE,CAAC;IACjD,CAAC;IAED;;;OAGG,CACI,cAAA,SAAA,CAAA,GAAG,GAAV;QACE,IAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,kBAAkB,CAAC;QACnE,OAAO,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;IACnC,CAAC;IACH,OAAA,aAAC;AAAD,CAAC,AAzBD,IAyBC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 431, "column": 0}, "map": {"version": 3, "file": "attributes.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40opentelemetry/core/src/common/attributes.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { diag, SpanAttributeValue, SpanAttributes } from '@opentelemetry/api';\n\nexport function sanitizeAttributes(attributes: unknown): SpanAttributes {\n  const out: SpanAttributes = {};\n\n  if (typeof attributes !== 'object' || attributes == null) {\n    return out;\n  }\n\n  for (const [key, val] of Object.entries(attributes)) {\n    if (!isAttributeKey(key)) {\n      diag.warn(`Invalid attribute key: ${key}`);\n      continue;\n    }\n    if (!isAttributeValue(val)) {\n      diag.warn(`Invalid attribute value set for key: ${key}`);\n      continue;\n    }\n    if (Array.isArray(val)) {\n      out[key] = val.slice();\n    } else {\n      out[key] = val;\n    }\n  }\n\n  return out;\n}\n\nexport function isAttributeKey(key: unknown): key is string {\n  return typeof key === 'string' && key.length > 0;\n}\n\nexport function isAttributeValue(val: unknown): val is SpanAttributeValue {\n  if (val == null) {\n    return true;\n  }\n\n  if (Array.isArray(val)) {\n    return isHomogeneousAttributeValueArray(val);\n  }\n\n  return isValidPrimitiveAttributeValue(val);\n}\n\nfunction isHomogeneousAttributeValueArray(arr: unknown[]): boolean {\n  let type: string | undefined;\n\n  for (const element of arr) {\n    // null/undefined elements are allowed\n    if (element == null) continue;\n\n    if (!type) {\n      if (isValidPrimitiveAttributeValue(element)) {\n        type = typeof element;\n        continue;\n      }\n      // encountered an invalid primitive\n      return false;\n    }\n\n    if (typeof element === type) {\n      continue;\n    }\n\n    return false;\n  }\n\n  return true;\n}\n\nfunction isValidPrimitiveAttributeValue(val: unknown): boolean {\n  switch (typeof val) {\n    case 'number':\n    case 'boolean':\n    case 'string':\n      return true;\n  }\n\n  return false;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;AAEH,OAAO,EAAE,IAAI,EAAsC,MAAM,oBAAoB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAExE,SAAU,kBAAkB,CAAC,UAAmB;;IACpD,IAAM,GAAG,GAAmB,CAAA,CAAE,CAAC;IAE/B,IAAI,OAAO,UAAU,KAAK,QAAQ,IAAI,UAAU,IAAI,IAAI,EAAE;QACxD,OAAO,GAAG,CAAC;KACZ;;QAED,IAAyB,IAAA,KAAA,SAAA,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA,EAAA,KAAA,GAAA,IAAA,EAAA,EAAA,CAAA,GAAA,IAAA,EAAA,KAAA,GAAA,IAAA,GAAE;YAA1C,IAAA,KAAA,OAAA,GAAA,KAAA,EAAA,EAAU,EAAT,GAAG,GAAA,EAAA,CAAA,EAAA,EAAE,GAAG,GAAA,EAAA,CAAA,EAAA;YAClB,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;gBACxB,6IAAI,CAAC,IAAI,CAAC,4BAA0B,GAAK,CAAC,CAAC;gBAC3C,SAAS;aACV;YACD,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE;sJAC1B,OAAI,CAAC,IAAI,CAAC,0CAAwC,GAAK,CAAC,CAAC;gBACzD,SAAS;aACV;YACD,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gBACtB,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,KAAK,EAAE,CAAC;aACxB,MAAM;gBACL,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;aAChB;SACF;;;;;;;;;;;;IAED,OAAO,GAAG,CAAC;AACb,CAAC;AAEK,SAAU,cAAc,CAAC,GAAY;IACzC,OAAO,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;AACnD,CAAC;AAEK,SAAU,gBAAgB,CAAC,GAAY;IAC3C,IAAI,GAAG,IAAI,IAAI,EAAE;QACf,OAAO,IAAI,CAAC;KACb;IAED,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QACtB,OAAO,gCAAgC,CAAC,GAAG,CAAC,CAAC;KAC9C;IAED,OAAO,8BAA8B,CAAC,GAAG,CAAC,CAAC;AAC7C,CAAC;AAED,SAAS,gCAAgC,CAAC,GAAc;;IACtD,IAAI,IAAwB,CAAC;;QAE7B,IAAsB,IAAA,QAAA,SAAA,GAAG,CAAA,EAAA,UAAA,MAAA,IAAA,EAAA,EAAA,CAAA,QAAA,IAAA,EAAA,UAAA,MAAA,IAAA,GAAE;YAAtB,IAAM,OAAO,GAAA,QAAA,KAAA;YAChB,sCAAsC;YACtC,IAAI,OAAO,IAAI,IAAI,EAAE,SAAS;YAE9B,IAAI,CAAC,IAAI,EAAE;gBACT,IAAI,8BAA8B,CAAC,OAAO,CAAC,EAAE;oBAC3C,IAAI,GAAG,OAAO,OAAO,CAAC;oBACtB,SAAS;iBACV;gBACD,mCAAmC;gBACnC,OAAO,KAAK,CAAC;aACd;YAED,IAAI,OAAO,OAAO,KAAK,IAAI,EAAE;gBAC3B,SAAS;aACV;YAED,OAAO,KAAK,CAAC;SACd;;;;;;;;;;;;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,8BAA8B,CAAC,GAAY;IAClD,OAAQ,OAAO,GAAG,EAAE;QAClB,KAAK,QAAQ,CAAC;QACd,KAAK,SAAS,CAAC;QACf,KAAK,QAAQ;YACX,OAAO,IAAI,CAAC;KACf;IAED,OAAO,KAAK,CAAC;AACf,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 582, "column": 0}, "map": {"version": 3, "file": "logging-error-handler.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40opentelemetry/core/src/common/logging-error-handler.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { diag, Exception } from '@opentelemetry/api';\nimport { ErrorHandler } from './types';\n\n/**\n * Returns a function that logs an error using the provided logger, or a\n * console logger if one was not provided.\n */\nexport function loggingErrorHandler(): ErrorHandler {\n  return (ex: Exception) => {\n    diag.error(stringifyException(ex));\n  };\n}\n\n/**\n * Converts an exception into a string representation\n * @param {Exception} ex\n */\nfunction stringifyException(ex: Exception | string): string {\n  if (typeof ex === 'string') {\n    return ex;\n  } else {\n    return JSON.stringify(flattenException(ex));\n  }\n}\n\n/**\n * Flattens an exception into key-value pairs by traversing the prototype chain\n * and coercing values to strings. Duplicate properties will not be overwritten;\n * the first insert wins.\n */\nfunction flattenException(ex: Exception): Record<string, string> {\n  const result = {} as Record<string, string>;\n  let current = ex;\n\n  while (current !== null) {\n    Object.getOwnPropertyNames(current).forEach(propertyName => {\n      if (result[propertyName]) return;\n      const value = current[propertyName as keyof typeof current];\n      if (value) {\n        result[propertyName] = String(value);\n      }\n    });\n    current = Object.getPrototypeOf(current);\n  }\n\n  return result;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,IAAI,EAAa,MAAM,oBAAoB,CAAC;;AAO/C,SAAU,mBAAmB;IACjC,OAAO,SAAC,EAAa;8IACnB,OAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC,CAAC;IACrC,CAAC,CAAC;AACJ,CAAC;AAED;;;GAGG,CACH,SAAS,kBAAkB,CAAC,EAAsB;IAChD,IAAI,OAAO,EAAE,KAAK,QAAQ,EAAE;QAC1B,OAAO,EAAE,CAAC;KACX,MAAM;QACL,OAAO,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAC;KAC7C;AACH,CAAC;AAED;;;;GAIG,CACH,SAAS,gBAAgB,CAAC,EAAa;IACrC,IAAM,MAAM,GAAG,CAAA,CAA4B,CAAC;IAC5C,IAAI,OAAO,GAAG,EAAE,CAAC;IAEjB,MAAO,OAAO,KAAK,IAAI,CAAE;QACvB,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,SAAA,YAAY;YACtD,IAAI,MAAM,CAAC,YAAY,CAAC,EAAE,OAAO;YACjC,IAAM,KAAK,GAAG,OAAO,CAAC,YAAoC,CAAC,CAAC;YAC5D,IAAI,KAAK,EAAE;gBACT,MAAM,CAAC,YAAY,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;aACtC;QACH,CAAC,CAAC,CAAC;QACH,OAAO,GAAG,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;KAC1C;IAED,OAAO,MAAM,CAAC;AAChB,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 641, "column": 0}, "map": {"version": 3, "file": "global-error-handler.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40opentelemetry/core/src/common/global-error-handler.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Exception } from '@opentelemetry/api';\nimport { loggingErrorHandler } from './logging-error-handler';\nimport { ErrorHandler } from './types';\n\n/** The global error handler delegate */\nlet delegateHandler = loggingErrorHandler();\n\n/**\n * Set the global error handler\n * @param {ErrorHandler} handler\n */\nexport function setGlobalErrorHandler(handler: ErrorHandler): void {\n  delegateHandler = handler;\n}\n\n/**\n * Return the global error handler\n * @param {Exception} ex\n */\nexport function globalErrorHandler(ex: Exception): void {\n  try {\n    delegateHandler(ex);\n  } catch {} // eslint-disable-line no-empty\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAGH,OAAO,EAAE,mBAAmB,EAAE,MAAM,yBAAyB,CAAC;;AAG9D,sCAAA,EAAwC,CACxC,IAAI,eAAe,wMAAG,sBAAA,AAAmB,EAAE,CAAC;AAMtC,SAAU,qBAAqB,CAAC,OAAqB;IACzD,eAAe,GAAG,OAAO,CAAC;AAC5B,CAAC;AAMK,SAAU,kBAAkB,CAAC,EAAa;IAC9C,IAAI;QACF,eAAe,CAAC,EAAE,CAAC,CAAC;KACrB,CAAC,OAAA,IAAM,CAAA,CAAE,CAAC,+BAA+B;AAC5C,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 676, "column": 0}, "map": {"version": 3, "file": "performance.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40opentelemetry/core/src/platform/node/performance.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { performance } from 'perf_hooks';\n\nexport const otperformance = performance;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,WAAW,EAAE,MAAM,YAAY,CAAC;;AAElC,IAAM,aAAa,iHAAG,cAAW,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 702, "column": 0}, "map": {"version": 3, "file": "time.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40opentelemetry/core/src/common/time.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as api from '@opentelemetry/api';\nimport { otperformance as performance } from '../platform';\nimport { TimeOriginLegacy } from './types';\n\nconst NANOSECOND_DIGITS = 9;\nconst NANOSECOND_DIGITS_IN_MILLIS = 6;\nconst MILLISECONDS_TO_NANOSECONDS = Math.pow(10, NANOSECOND_DIGITS_IN_MILLIS);\nconst SECOND_TO_NANOSECONDS = Math.pow(10, NANOSECOND_DIGITS);\n\n/**\n * Converts a number of milliseconds from epoch to HrTime([seconds, remainder in nanoseconds]).\n * @param epochMillis\n */\nexport function millisToHrTime(epochMillis: number): api.HrTime {\n  const epochSeconds = epochMillis / 1000;\n  // Decimals only.\n  const seconds = Math.trunc(epochSeconds);\n  // Round sub-nanosecond accuracy to nanosecond.\n  const nanos = Math.round((epochMillis % 1000) * MILLISECONDS_TO_NANOSECONDS);\n  return [seconds, nanos];\n}\n\nexport function getTimeOrigin(): number {\n  let timeOrigin = performance.timeOrigin;\n  if (typeof timeOrigin !== 'number') {\n    const perf: TimeOriginLegacy = performance as unknown as TimeOriginLegacy;\n    timeOrigin = perf.timing && perf.timing.fetchStart;\n  }\n  return timeOrigin;\n}\n\n/**\n * Returns an hrtime calculated via performance component.\n * @param performanceNow\n */\nexport function hrTime(performanceNow?: number): api.HrTime {\n  const timeOrigin = millisToHrTime(getTimeOrigin());\n  const now = millisToHrTime(\n    typeof performanceNow === 'number' ? performanceNow : performance.now()\n  );\n\n  return addHrTimes(timeOrigin, now);\n}\n\n/**\n *\n * Converts a TimeInput to an HrTime, defaults to _hrtime().\n * @param time\n */\nexport function timeInputToHrTime(time: api.TimeInput): api.HrTime {\n  // process.hrtime\n  if (isTimeInputHrTime(time)) {\n    return time as api.HrTime;\n  } else if (typeof time === 'number') {\n    // Must be a performance.now() if it's smaller than process start time.\n    if (time < getTimeOrigin()) {\n      return hrTime(time);\n    } else {\n      // epoch milliseconds or performance.timeOrigin\n      return millisToHrTime(time);\n    }\n  } else if (time instanceof Date) {\n    return millisToHrTime(time.getTime());\n  } else {\n    throw TypeError('Invalid input type');\n  }\n}\n\n/**\n * Returns a duration of two hrTime.\n * @param startTime\n * @param endTime\n */\nexport function hrTimeDuration(\n  startTime: api.HrTime,\n  endTime: api.HrTime\n): api.HrTime {\n  let seconds = endTime[0] - startTime[0];\n  let nanos = endTime[1] - startTime[1];\n\n  // overflow\n  if (nanos < 0) {\n    seconds -= 1;\n    // negate\n    nanos += SECOND_TO_NANOSECONDS;\n  }\n\n  return [seconds, nanos];\n}\n\n/**\n * Convert hrTime to timestamp, for example \"2019-05-14T17:00:00.000123456Z\"\n * @param time\n */\nexport function hrTimeToTimeStamp(time: api.HrTime): string {\n  const precision = NANOSECOND_DIGITS;\n  const tmp = `${'0'.repeat(precision)}${time[1]}Z`;\n  const nanoString = tmp.substring(tmp.length - precision - 1);\n  const date = new Date(time[0] * 1000).toISOString();\n  return date.replace('000Z', nanoString);\n}\n\n/**\n * Convert hrTime to nanoseconds.\n * @param time\n */\nexport function hrTimeToNanoseconds(time: api.HrTime): number {\n  return time[0] * SECOND_TO_NANOSECONDS + time[1];\n}\n\n/**\n * Convert hrTime to milliseconds.\n * @param time\n */\nexport function hrTimeToMilliseconds(time: api.HrTime): number {\n  return time[0] * 1e3 + time[1] / 1e6;\n}\n\n/**\n * Convert hrTime to microseconds.\n * @param time\n */\nexport function hrTimeToMicroseconds(time: api.HrTime): number {\n  return time[0] * 1e6 + time[1] / 1e3;\n}\n\n/**\n * check if time is HrTime\n * @param value\n */\nexport function isTimeInputHrTime(value: unknown): value is api.HrTime {\n  return (\n    Array.isArray(value) &&\n    value.length === 2 &&\n    typeof value[0] === 'number' &&\n    typeof value[1] === 'number'\n  );\n}\n\n/**\n * check if input value is a correct types.TimeInput\n * @param value\n */\nexport function isTimeInput(\n  value: unknown\n): value is api.HrTime | number | Date {\n  return (\n    isTimeInputHrTime(value) ||\n    typeof value === 'number' ||\n    value instanceof Date\n  );\n}\n\n/**\n * Given 2 HrTime formatted times, return their sum as an HrTime.\n */\nexport function addHrTimes(time1: api.HrTime, time2: api.HrTime): api.HrTime {\n  const out = [time1[0] + time2[0], time1[1] + time2[1]] as api.HrTime;\n\n  // Nanoseconds\n  if (out[1] >= SECOND_TO_NANOSECONDS) {\n    out[1] -= SECOND_TO_NANOSECONDS;\n    out[0] += 1;\n  }\n\n  return out;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;AAGH,OAAO,EAAE,aAAa,IAAI,WAAW,EAAE,MAAM,aAAa,CAAC;;AAG3D,IAAM,iBAAiB,GAAG,CAAC,CAAC;AAC5B,IAAM,2BAA2B,GAAG,CAAC,CAAC;AACtC,IAAM,2BAA2B,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,2BAA2B,CAAC,CAAC;AAC9E,IAAM,qBAAqB,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,iBAAiB,CAAC,CAAC;AAMxD,SAAU,cAAc,CAAC,WAAmB;IAChD,IAAM,YAAY,GAAG,WAAW,GAAG,IAAI,CAAC;IACxC,iBAAiB;IACjB,IAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;IACzC,+CAA+C;IAC/C,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,AAAC,WAAW,GAAG,IAAI,CAAC,EAAG,2BAA2B,CAAC,CAAC;IAC7E,OAAO;QAAC,OAAO;QAAE,KAAK;KAAC,CAAC;AAC1B,CAAC;AAEK,SAAU,aAAa;IAC3B,IAAI,UAAU,8LAAG,gBAAW,CAAC,UAAU,CAAC;IACxC,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;QAClC,IAAM,IAAI,8LAAqB,gBAA0C,CAAC;QAC1E,UAAU,GAAG,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;KACpD;IACD,OAAO,UAAU,CAAC;AACpB,CAAC;AAMK,SAAU,MAAM,CAAC,cAAuB;IAC5C,IAAM,UAAU,GAAG,cAAc,CAAC,aAAa,EAAE,CAAC,CAAC;IACnD,IAAM,GAAG,GAAG,cAAc,CACxB,OAAO,cAAc,KAAK,QAAQ,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,4LAAC,gBAAW,CAAC,GAAG,EAAE,CACxE,CAAC;IAEF,OAAO,UAAU,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;AACrC,CAAC;AAOK,SAAU,iBAAiB,CAAC,IAAmB;IACnD,iBAAiB;IACjB,IAAI,iBAAiB,CAAC,IAAI,CAAC,EAAE;QAC3B,OAAO,IAAkB,CAAC;KAC3B,MAAM,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;QACnC,uEAAuE;QACvE,IAAI,IAAI,GAAG,aAAa,EAAE,EAAE;YAC1B,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC;SACrB,MAAM;YACL,+CAA+C;YAC/C,OAAO,cAAc,CAAC,IAAI,CAAC,CAAC;SAC7B;KACF,MAAM,IAAI,IAAI,YAAY,IAAI,EAAE;QAC/B,OAAO,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;KACvC,MAAM;QACL,MAAM,SAAS,CAAC,oBAAoB,CAAC,CAAC;KACvC;AACH,CAAC;AAOK,SAAU,cAAc,CAC5B,SAAqB,EACrB,OAAmB;IAEnB,IAAI,OAAO,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;IACxC,IAAI,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;IAEtC,WAAW;IACX,IAAI,KAAK,GAAG,CAAC,EAAE;QACb,OAAO,IAAI,CAAC,CAAC;QACb,SAAS;QACT,KAAK,IAAI,qBAAqB,CAAC;KAChC;IAED,OAAO;QAAC,OAAO;QAAE,KAAK;KAAC,CAAC;AAC1B,CAAC;AAMK,SAAU,iBAAiB,CAAC,IAAgB;IAChD,IAAM,SAAS,GAAG,iBAAiB,CAAC;IACpC,IAAM,GAAG,GAAG,KAAG,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAA,GAAG,CAAC;IAClD,IAAM,UAAU,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,GAAG,SAAS,GAAG,CAAC,CAAC,CAAC;IAC7D,IAAM,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;IACpD,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;AAC1C,CAAC;AAMK,SAAU,mBAAmB,CAAC,IAAgB;IAClD,OAAO,IAAI,CAAC,CAAC,CAAC,GAAG,qBAAqB,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AACnD,CAAC;AAMK,SAAU,oBAAoB,CAAC,IAAgB;IACnD,OAAO,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACvC,CAAC;AAMK,SAAU,oBAAoB,CAAC,IAAgB;IACnD,OAAO,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACvC,CAAC;AAMK,SAAU,iBAAiB,CAAC,KAAc;IAC9C,OAAO,AACL,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IACpB,KAAK,CAAC,MAAM,KAAK,CAAC,IAClB,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,QAAQ,IAC5B,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,QAAQ,CAC7B,CAAC;AACJ,CAAC;AAMK,SAAU,WAAW,CACzB,KAAc;IAEd,OAAO,AACL,iBAAiB,CAAC,KAAK,CAAC,IACxB,OAAO,KAAK,KAAK,QAAQ,IACzB,KAAK,YAAY,IAAI,CACtB,CAAC;AACJ,CAAC;AAKK,SAAU,UAAU,CAAC,KAAiB,EAAE,KAAiB;IAC7D,IAAM,GAAG,GAAG;QAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;QAAE,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;KAAe,CAAC;IAErE,cAAc;IACd,IAAI,GAAG,CAAC,CAAC,CAAC,IAAI,qBAAqB,EAAE;QACnC,GAAG,CAAC,CAAC,CAAC,IAAI,qBAAqB,CAAC;QAChC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;KACb;IAED,OAAO,GAAG,CAAC;AACb,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 832, "column": 0}, "map": {"version": 3, "file": "hex-to-binary.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40opentelemetry/core/src/common/hex-to-binary.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nfunction intValue(charCode: number): number {\n  // 0-9\n  if (charCode >= 48 && charCode <= 57) {\n    return charCode - 48;\n  }\n\n  // a-f\n  if (charCode >= 97 && charCode <= 102) {\n    return charCode - 87;\n  }\n\n  // A-F\n  return charCode - 55;\n}\n\nexport function hexToBinary(hexStr: string): Uint8Array {\n  const buf = new Uint8Array(hexStr.length / 2);\n  let offset = 0;\n\n  for (let i = 0; i < hexStr.length; i += 2) {\n    const hi = intValue(hexStr.charCodeAt(i));\n    const lo = intValue(hexStr.charCodeAt(i + 1));\n    buf[offset++] = (hi << 4) | lo;\n  }\n\n  return buf;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,SAAS,QAAQ,CAAC,QAAgB;IAChC,MAAM;IACN,IAAI,QAAQ,IAAI,EAAE,IAAI,QAAQ,IAAI,EAAE,EAAE;QACpC,OAAO,QAAQ,GAAG,EAAE,CAAC;KACtB;IAED,MAAM;IACN,IAAI,QAAQ,IAAI,EAAE,IAAI,QAAQ,IAAI,GAAG,EAAE;QACrC,OAAO,QAAQ,GAAG,EAAE,CAAC;KACtB;IAED,MAAM;IACN,OAAO,QAAQ,GAAG,EAAE,CAAC;AACvB,CAAC;AAEK,SAAU,WAAW,CAAC,MAAc;IACxC,IAAM,GAAG,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAC9C,IAAI,MAAM,GAAG,CAAC,CAAC;IAEf,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,CAAE;QACzC,IAAM,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C,IAAM,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC9C,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,AAAC,EAAE,IAAI,CAAC,CAAC,EAAG,EAAE,CAAC;KAChC;IAED,OAAO,GAAG,CAAC;AACb,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 877, "column": 0}, "map": {"version": 3, "file": "ExportResult.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40opentelemetry/core/src/ExportResult.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport interface ExportResult {\n  code: ExportResultCode;\n  error?: Error;\n}\n\nexport enum ExportResultCode {\n  SUCCESS,\n  FAILED,\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAOH,IAAY,gBAGX;AAHD,CAAA,SAAY,gBAAgB;IAC1B,gBAAA,CAAA,gBAAA,CAAA,UAAA,GAAA,EAAA,GAAA,SAAO,CAAA;IACP,gBAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,EAAA,GAAA,QAAM,CAAA;AACR,CAAC,EAHW,gBAAgB,IAAA,CAAhB,gBAAgB,GAAA,CAAA,CAAA,GAG3B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 905, "column": 0}, "map": {"version": 3, "file": "RandomIdGenerator.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40opentelemetry/core/src/platform/node/RandomIdGenerator.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { IdGenerator } from '../../trace/IdGenerator';\nconst SPAN_ID_BYTES = 8;\nconst TRACE_ID_BYTES = 16;\n\n/**\n * @deprecated Use the one defined in @opentelemetry/sdk-trace-base instead.\n */\nexport class RandomIdGenerator implements IdGenerator {\n  /**\n   * Returns a random 16-byte trace ID formatted/encoded as a 32 lowercase hex\n   * characters corresponding to 128 bits.\n   */\n  generateTraceId = getIdGenerator(TRACE_ID_BYTES);\n\n  /**\n   * Returns a random 8-byte span ID formatted/encoded as a 16 lowercase hex\n   * characters corresponding to 64 bits.\n   */\n  generateSpanId = getIdGenerator(SPAN_ID_BYTES);\n}\n\nconst SHARED_BUFFER = Buffer.allocUnsafe(TRACE_ID_BYTES);\nfunction getIdGenerator(bytes: number): () => string {\n  return function generateId() {\n    for (let i = 0; i < bytes / 4; i++) {\n      // unsigned right shift drops decimal part of the number\n      // it is required because if a number between 2**32 and 2**32 - 1 is generated, an out of range error is thrown by writeUInt32BE\n      SHARED_BUFFER.writeUInt32BE((Math.random() * 2 ** 32) >>> 0, i * 4);\n    }\n\n    // If buffer is all 0, set the last byte to 1 to guarantee a valid w3c id is generated\n    for (let i = 0; i < bytes; i++) {\n      if (SHARED_BUFFER[i] > 0) {\n        break;\n      } else if (i === bytes - 1) {\n        SHARED_BUFFER[bytes - 1] = 1;\n      }\n    }\n\n    return SHARED_BUFFER.toString('hex', 0, bytes);\n  };\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAGH,IAAM,aAAa,GAAG,CAAC,CAAC;AACxB,IAAM,cAAc,GAAG,EAAE,CAAC;AAE1B;;GAEG,CACH,IAAA,oBAAA;IAAA,SAAA;QACE;;;WAGG,CACH,IAAA,CAAA,eAAe,GAAG,cAAc,CAAC,cAAc,CAAC,CAAC;QAEjD;;;WAGG,CACH,IAAA,CAAA,cAAc,GAAG,cAAc,CAAC,aAAa,CAAC,CAAC;IACjD,CAAC;IAAD,OAAA,iBAAC;AAAD,CAAC,AAZD,IAYC;;AAED,IAAM,aAAa,GAAG,MAAM,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;AACzD,SAAS,cAAc,CAAC,KAAa;IACnC,OAAO,SAAS,UAAU;QACxB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE;YAClC,wDAAwD;YACxD,gIAAgI;YAChI,aAAa,CAAC,aAAa,CAAC,AAAC,IAAI,CAAC,MAAM,EAAE,GAAG,KAAA,GAAA,CAAA,CAAC,EAAI,EAAE,CAAA,CAAC,IAAK,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;SACrE;QAED,sFAAsF;QACtF,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,CAAE;YAC9B,IAAI,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;gBACxB,MAAM;aACP,MAAM,IAAI,CAAC,KAAK,KAAK,GAAG,CAAC,EAAE;gBAC1B,aAAa,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;aAC9B;SACF;QAED,OAAO,aAAa,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;IACjD,CAAC,CAAC;AACJ,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 965, "column": 0}, "map": {"version": 3, "file": "version.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40opentelemetry/core/src/version.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// this is autogenerated file, see scripts/version-update.js\nexport const VERSION = '1.30.1';\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH,4DAA4D;;;;AACrD,IAAM,OAAO,GAAG,QAAQ,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 990, "column": 0}, "map": {"version": 3, "file": "sdk-info.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40opentelemetry/core/src/platform/node/sdk-info.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { VERSION } from '../../version';\nimport {\n  SEMRESATTRS_TELEMETRY_SDK_NAME,\n  SEMRESATTRS_PROCESS_RUNTIME_NAME,\n  SEMRESATTRS_TELEMETRY_SDK_LANGUAGE,\n  TELEMETRYSDKLANGUAGEVALUES_NODEJS,\n  SEMRESATTRS_TELEMETRY_SDK_VERSION,\n} from '@opentelemetry/semantic-conventions';\n\n/** Constants describing the SDK in use */\nexport const SDK_INFO = {\n  [SEMRESATTRS_TELEMETRY_SDK_NAME]: 'opentelemetry',\n  [SEMRESATTRS_PROCESS_RUNTIME_NAME]: 'node',\n  [SEMRESATTRS_TELEMETRY_SDK_LANGUAGE]: TELEMETRYSDKLANGUAGEVALUES_NODEJS,\n  [SEMRESATTRS_TELEMETRY_SDK_VERSION]: VERSION,\n};\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,OAAO,EAAE,MAAM,eAAe,CAAC;AACxC,OAAO,EACL,8BAA8B,EAC9B,gCAAgC,EAChC,kCAAkC,EAClC,iCAAiC,EACjC,iCAAiC,GAClC,MAAM,qCAAqC,CAAC;;;;AAGtC,IAAM,QAAQ,GAAA,CAAA,KAAA,CAAA,GACnB,EAAA,iQAAC,iCAA8B,CAAA,GAAG,eAAe,EACjD,EAAA,CAAC,mSAAgC,CAAA,GAAG,MAAM,EAC1C,EAAA,iQAAC,qCAAkC,CAAA,mQAAG,oCAAiC,EACvE,EAAA,iQAAC,oCAAiC,CAAA,sKAAG,UAAO,KAC7C,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1019, "column": 0}, "map": {"version": 3, "file": "globalThis.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40opentelemetry/core/src/platform/node/globalThis.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** only globals that common to node and browsers are allowed */\n// eslint-disable-next-line node/no-unsupported-features/es-builtins\nexport const _globalThis = typeof globalThis === 'object' ? globalThis : global;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH,8DAAA,EAAgE,CAChE,oEAAoE;;;;AAC7D,IAAM,WAAW,GAAG,OAAO,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1044, "column": 0}, "map": {"version": 3, "file": "sampling.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40opentelemetry/core/src/utils/sampling.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport enum TracesSamplerValues {\n  AlwaysOff = 'always_off',\n  AlwaysOn = 'always_on',\n  ParentBasedAlwaysOff = 'parentbased_always_off',\n  ParentBasedAlwaysOn = 'parentbased_always_on',\n  ParentBasedTraceIdRatio = 'parentbased_traceidratio',\n  TraceIdRatio = 'traceidratio',\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,IAAY,mBAOX;AAPD,CAAA,SAAY,mBAAmB;IAC7B,mBAAA,CAAA,YAAA,GAAA,YAAwB,CAAA;IACxB,mBAAA,CAAA,WAAA,GAAA,WAAsB,CAAA;IACtB,mBAAA,CAAA,uBAAA,GAAA,wBAA+C,CAAA;IAC/C,mBAAA,CAAA,sBAAA,GAAA,uBAA6C,CAAA;IAC7C,mBAAA,CAAA,0BAAA,GAAA,0BAAoD,CAAA;IACpD,mBAAA,CAAA,eAAA,GAAA,cAA6B,CAAA;AAC/B,CAAC,EAPW,mBAAmB,IAAA,CAAnB,mBAAmB,GAAA,CAAA,CAAA,GAO9B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1076, "column": 0}, "map": {"version": 3, "file": "environment.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40opentelemetry/core/src/utils/environment.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DiagLogLevel } from '@opentelemetry/api';\nimport { TracesSamplerValues } from './sampling';\n\nconst DEFAULT_LIST_SEPARATOR = ',';\n\n/**\n * Environment interface to define all names\n */\n\nconst ENVIRONMENT_BOOLEAN_KEYS = ['OTEL_SDK_DISABLED'] as const;\n\ntype ENVIRONMENT_BOOLEANS = {\n  [K in (typeof ENVIRONMENT_BOOLEAN_KEYS)[number]]?: boolean;\n};\n\nfunction isEnvVarABoolean(key: unknown): key is keyof ENVIRONMENT_BOOLEANS {\n  return (\n    ENVIRONMENT_BOOLEAN_KEYS.indexOf(key as keyof ENVIRONMENT_BOOLEANS) > -1\n  );\n}\n\nconst ENVIRONMENT_NUMBERS_KEYS = [\n  'OTEL_BSP_EXPORT_TIMEOUT',\n  'OTEL_BSP_MAX_EXPORT_BATCH_SIZE',\n  'OTEL_BSP_MAX_QUEUE_SIZE',\n  'OTEL_BSP_SCHEDULE_DELAY',\n  'OTEL_BLRP_EXPORT_TIMEOUT',\n  'OTEL_BLRP_MAX_EXPORT_BATCH_SIZE',\n  'OTEL_BLRP_MAX_QUEUE_SIZE',\n  'OTEL_BLRP_SCHEDULE_DELAY',\n  'OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT',\n  'OTEL_ATTRIBUTE_COUNT_LIMIT',\n  'OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT',\n  'OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT',\n  'OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT',\n  'OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT',\n  'OTEL_SPAN_EVENT_COUNT_LIMIT',\n  'OTEL_SPAN_LINK_COUNT_LIMIT',\n  'OTEL_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT',\n  'OTEL_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT',\n  'OTEL_EXPORTER_OTLP_TIMEOUT',\n  'OTEL_EXPORTER_OTLP_TRACES_TIMEOUT',\n  'OTEL_EXPORTER_OTLP_METRICS_TIMEOUT',\n  'OTEL_EXPORTER_OTLP_LOGS_TIMEOUT',\n  'OTEL_EXPORTER_JAEGER_AGENT_PORT',\n] as const;\n\ntype ENVIRONMENT_NUMBERS = {\n  [K in (typeof ENVIRONMENT_NUMBERS_KEYS)[number]]?: number;\n};\n\nfunction isEnvVarANumber(key: unknown): key is keyof ENVIRONMENT_NUMBERS {\n  return (\n    ENVIRONMENT_NUMBERS_KEYS.indexOf(key as keyof ENVIRONMENT_NUMBERS) > -1\n  );\n}\n\nconst ENVIRONMENT_LISTS_KEYS = [\n  'OTEL_NO_PATCH_MODULES',\n  'OTEL_PROPAGATORS',\n  'OTEL_SEMCONV_STABILITY_OPT_IN',\n] as const;\n\ntype ENVIRONMENT_LISTS = {\n  [K in (typeof ENVIRONMENT_LISTS_KEYS)[number]]?: string[];\n};\n\nfunction isEnvVarAList(key: unknown): key is keyof ENVIRONMENT_LISTS {\n  return ENVIRONMENT_LISTS_KEYS.indexOf(key as keyof ENVIRONMENT_LISTS) > -1;\n}\n\nexport type ENVIRONMENT = {\n  CONTAINER_NAME?: string;\n  ECS_CONTAINER_METADATA_URI_V4?: string;\n  ECS_CONTAINER_METADATA_URI?: string;\n  HOSTNAME?: string;\n  KUBERNETES_SERVICE_HOST?: string;\n  NAMESPACE?: string;\n  OTEL_EXPORTER_JAEGER_AGENT_HOST?: string;\n  OTEL_EXPORTER_JAEGER_ENDPOINT?: string;\n  OTEL_EXPORTER_JAEGER_PASSWORD?: string;\n  OTEL_EXPORTER_JAEGER_USER?: string;\n  OTEL_EXPORTER_OTLP_ENDPOINT?: string;\n  OTEL_EXPORTER_OTLP_TRACES_ENDPOINT?: string;\n  OTEL_EXPORTER_OTLP_METRICS_ENDPOINT?: string;\n  OTEL_EXPORTER_OTLP_LOGS_ENDPOINT?: string;\n  OTEL_EXPORTER_OTLP_HEADERS?: string;\n  OTEL_EXPORTER_OTLP_TRACES_HEADERS?: string;\n  OTEL_EXPORTER_OTLP_METRICS_HEADERS?: string;\n  OTEL_EXPORTER_OTLP_LOGS_HEADERS?: string;\n  OTEL_EXPORTER_ZIPKIN_ENDPOINT?: string;\n  OTEL_LOG_LEVEL?: DiagLogLevel;\n  OTEL_RESOURCE_ATTRIBUTES?: string;\n  OTEL_SERVICE_NAME?: string;\n  OTEL_TRACES_EXPORTER?: string;\n  OTEL_TRACES_SAMPLER_ARG?: string;\n  OTEL_TRACES_SAMPLER?: string;\n  OTEL_LOGS_EXPORTER?: string;\n  OTEL_EXPORTER_OTLP_INSECURE?: string;\n  OTEL_EXPORTER_OTLP_TRACES_INSECURE?: string;\n  OTEL_EXPORTER_OTLP_METRICS_INSECURE?: string;\n  OTEL_EXPORTER_OTLP_LOGS_INSECURE?: string;\n  OTEL_EXPORTER_OTLP_CERTIFICATE?: string;\n  OTEL_EXPORTER_OTLP_TRACES_CERTIFICATE?: string;\n  OTEL_EXPORTER_OTLP_METRICS_CERTIFICATE?: string;\n  OTEL_EXPORTER_OTLP_LOGS_CERTIFICATE?: string;\n  OTEL_EXPORTER_OTLP_COMPRESSION?: string;\n  OTEL_EXPORTER_OTLP_TRACES_COMPRESSION?: string;\n  OTEL_EXPORTER_OTLP_METRICS_COMPRESSION?: string;\n  OTEL_EXPORTER_OTLP_LOGS_COMPRESSION?: string;\n  OTEL_EXPORTER_OTLP_CLIENT_KEY?: string;\n  OTEL_EXPORTER_OTLP_TRACES_CLIENT_KEY?: string;\n  OTEL_EXPORTER_OTLP_METRICS_CLIENT_KEY?: string;\n  OTEL_EXPORTER_OTLP_LOGS_CLIENT_KEY?: string;\n  OTEL_EXPORTER_OTLP_CLIENT_CERTIFICATE?: string;\n  OTEL_EXPORTER_OTLP_TRACES_CLIENT_CERTIFICATE?: string;\n  OTEL_EXPORTER_OTLP_METRICS_CLIENT_CERTIFICATE?: string;\n  OTEL_EXPORTER_OTLP_LOGS_CLIENT_CERTIFICATE?: string;\n  OTEL_EXPORTER_OTLP_PROTOCOL?: string;\n  OTEL_EXPORTER_OTLP_TRACES_PROTOCOL?: string;\n  OTEL_EXPORTER_OTLP_METRICS_PROTOCOL?: string;\n  OTEL_EXPORTER_OTLP_LOGS_PROTOCOL?: string;\n  OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE?: string;\n} & ENVIRONMENT_BOOLEANS &\n  ENVIRONMENT_NUMBERS &\n  ENVIRONMENT_LISTS;\n\nexport type RAW_ENVIRONMENT = {\n  [key: string]: string | number | undefined | string[];\n};\n\nexport const DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT = Infinity;\n\nexport const DEFAULT_ATTRIBUTE_COUNT_LIMIT = 128;\n\nexport const DEFAULT_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT = 128;\nexport const DEFAULT_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT = 128;\n\n/**\n * Default environment variables\n */\nexport const DEFAULT_ENVIRONMENT: Required<ENVIRONMENT> = {\n  OTEL_SDK_DISABLED: false,\n  CONTAINER_NAME: '',\n  ECS_CONTAINER_METADATA_URI_V4: '',\n  ECS_CONTAINER_METADATA_URI: '',\n  HOSTNAME: '',\n  KUBERNETES_SERVICE_HOST: '',\n  NAMESPACE: '',\n  OTEL_BSP_EXPORT_TIMEOUT: 30000,\n  OTEL_BSP_MAX_EXPORT_BATCH_SIZE: 512,\n  OTEL_BSP_MAX_QUEUE_SIZE: 2048,\n  OTEL_BSP_SCHEDULE_DELAY: 5000,\n  OTEL_BLRP_EXPORT_TIMEOUT: 30000,\n  OTEL_BLRP_MAX_EXPORT_BATCH_SIZE: 512,\n  OTEL_BLRP_MAX_QUEUE_SIZE: 2048,\n  OTEL_BLRP_SCHEDULE_DELAY: 5000,\n  OTEL_EXPORTER_JAEGER_AGENT_HOST: '',\n  OTEL_EXPORTER_JAEGER_AGENT_PORT: 6832,\n  OTEL_EXPORTER_JAEGER_ENDPOINT: '',\n  OTEL_EXPORTER_JAEGER_PASSWORD: '',\n  OTEL_EXPORTER_JAEGER_USER: '',\n  OTEL_EXPORTER_OTLP_ENDPOINT: '',\n  OTEL_EXPORTER_OTLP_TRACES_ENDPOINT: '',\n  OTEL_EXPORTER_OTLP_METRICS_ENDPOINT: '',\n  OTEL_EXPORTER_OTLP_LOGS_ENDPOINT: '',\n  OTEL_EXPORTER_OTLP_HEADERS: '',\n  OTEL_EXPORTER_OTLP_TRACES_HEADERS: '',\n  OTEL_EXPORTER_OTLP_METRICS_HEADERS: '',\n  OTEL_EXPORTER_OTLP_LOGS_HEADERS: '',\n  OTEL_EXPORTER_OTLP_TIMEOUT: 10000,\n  OTEL_EXPORTER_OTLP_TRACES_TIMEOUT: 10000,\n  OTEL_EXPORTER_OTLP_METRICS_TIMEOUT: 10000,\n  OTEL_EXPORTER_OTLP_LOGS_TIMEOUT: 10000,\n  OTEL_EXPORTER_ZIPKIN_ENDPOINT: 'http://localhost:9411/api/v2/spans',\n  OTEL_LOG_LEVEL: DiagLogLevel.INFO,\n  OTEL_NO_PATCH_MODULES: [],\n  OTEL_PROPAGATORS: ['tracecontext', 'baggage'],\n  OTEL_RESOURCE_ATTRIBUTES: '',\n  OTEL_SERVICE_NAME: '',\n  OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT: DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT,\n  OTEL_ATTRIBUTE_COUNT_LIMIT: DEFAULT_ATTRIBUTE_COUNT_LIMIT,\n  OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT: DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT,\n  OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT: DEFAULT_ATTRIBUTE_COUNT_LIMIT,\n  OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT:\n    DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT,\n  OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT: DEFAULT_ATTRIBUTE_COUNT_LIMIT,\n  OTEL_SPAN_EVENT_COUNT_LIMIT: 128,\n  OTEL_SPAN_LINK_COUNT_LIMIT: 128,\n  OTEL_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT:\n    DEFAULT_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT,\n  OTEL_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT:\n    DEFAULT_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT,\n  OTEL_TRACES_EXPORTER: '',\n  OTEL_TRACES_SAMPLER: TracesSamplerValues.ParentBasedAlwaysOn,\n  OTEL_TRACES_SAMPLER_ARG: '',\n  OTEL_LOGS_EXPORTER: '',\n  OTEL_EXPORTER_OTLP_INSECURE: '',\n  OTEL_EXPORTER_OTLP_TRACES_INSECURE: '',\n  OTEL_EXPORTER_OTLP_METRICS_INSECURE: '',\n  OTEL_EXPORTER_OTLP_LOGS_INSECURE: '',\n  OTEL_EXPORTER_OTLP_CERTIFICATE: '',\n  OTEL_EXPORTER_OTLP_TRACES_CERTIFICATE: '',\n  OTEL_EXPORTER_OTLP_METRICS_CERTIFICATE: '',\n  OTEL_EXPORTER_OTLP_LOGS_CERTIFICATE: '',\n  OTEL_EXPORTER_OTLP_COMPRESSION: '',\n  OTEL_EXPORTER_OTLP_TRACES_COMPRESSION: '',\n  OTEL_EXPORTER_OTLP_METRICS_COMPRESSION: '',\n  OTEL_EXPORTER_OTLP_LOGS_COMPRESSION: '',\n  OTEL_EXPORTER_OTLP_CLIENT_KEY: '',\n  OTEL_EXPORTER_OTLP_TRACES_CLIENT_KEY: '',\n  OTEL_EXPORTER_OTLP_METRICS_CLIENT_KEY: '',\n  OTEL_EXPORTER_OTLP_LOGS_CLIENT_KEY: '',\n  OTEL_EXPORTER_OTLP_CLIENT_CERTIFICATE: '',\n  OTEL_EXPORTER_OTLP_TRACES_CLIENT_CERTIFICATE: '',\n  OTEL_EXPORTER_OTLP_METRICS_CLIENT_CERTIFICATE: '',\n  OTEL_EXPORTER_OTLP_LOGS_CLIENT_CERTIFICATE: '',\n  OTEL_EXPORTER_OTLP_PROTOCOL: 'http/protobuf',\n  OTEL_EXPORTER_OTLP_TRACES_PROTOCOL: 'http/protobuf',\n  OTEL_EXPORTER_OTLP_METRICS_PROTOCOL: 'http/protobuf',\n  OTEL_EXPORTER_OTLP_LOGS_PROTOCOL: 'http/protobuf',\n  OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE: 'cumulative',\n  OTEL_SEMCONV_STABILITY_OPT_IN: [],\n};\n\n/**\n * @param key\n * @param environment\n * @param values\n */\nfunction parseBoolean(\n  key: keyof ENVIRONMENT_BOOLEANS,\n  environment: ENVIRONMENT,\n  values: RAW_ENVIRONMENT\n) {\n  if (typeof values[key] === 'undefined') {\n    return;\n  }\n\n  const value = String(values[key]);\n  // support case-insensitive \"true\"\n  environment[key] = value.toLowerCase() === 'true';\n}\n\n/**\n * Parses a variable as number with number validation\n * @param name\n * @param environment\n * @param values\n * @param min\n * @param max\n */\nfunction parseNumber(\n  name: keyof ENVIRONMENT_NUMBERS,\n  environment: ENVIRONMENT,\n  values: RAW_ENVIRONMENT,\n  min = -Infinity,\n  max = Infinity\n) {\n  if (typeof values[name] !== 'undefined') {\n    const value = Number(values[name] as string);\n    if (!isNaN(value)) {\n      if (value < min) {\n        environment[name] = min;\n      } else if (value > max) {\n        environment[name] = max;\n      } else {\n        environment[name] = value;\n      }\n    }\n  }\n}\n\n/**\n * Parses list-like strings from input into output.\n * @param name\n * @param environment\n * @param values\n * @param separator\n */\nfunction parseStringList(\n  name: keyof ENVIRONMENT_LISTS,\n  output: ENVIRONMENT,\n  input: RAW_ENVIRONMENT,\n  separator = DEFAULT_LIST_SEPARATOR\n) {\n  const givenValue = input[name];\n  if (typeof givenValue === 'string') {\n    output[name] = givenValue.split(separator).map(v => v.trim());\n  }\n}\n\n// The support string -> DiagLogLevel mappings\nconst logLevelMap: { [key: string]: DiagLogLevel } = {\n  ALL: DiagLogLevel.ALL,\n  VERBOSE: DiagLogLevel.VERBOSE,\n  DEBUG: DiagLogLevel.DEBUG,\n  INFO: DiagLogLevel.INFO,\n  WARN: DiagLogLevel.WARN,\n  ERROR: DiagLogLevel.ERROR,\n  NONE: DiagLogLevel.NONE,\n};\n\n/**\n * Environmentally sets log level if valid log level string is provided\n * @param key\n * @param environment\n * @param values\n */\nfunction setLogLevelFromEnv(\n  key: keyof ENVIRONMENT,\n  environment: RAW_ENVIRONMENT | ENVIRONMENT,\n  values: RAW_ENVIRONMENT\n) {\n  const value = values[key];\n  if (typeof value === 'string') {\n    const theLevel = logLevelMap[value.toUpperCase()];\n    if (theLevel != null) {\n      environment[key] = theLevel;\n    }\n  }\n}\n\n/**\n * Parses environment values\n * @param values\n */\nexport function parseEnvironment(values: RAW_ENVIRONMENT): ENVIRONMENT {\n  const environment: ENVIRONMENT = {};\n\n  for (const env in DEFAULT_ENVIRONMENT) {\n    const key = env as keyof ENVIRONMENT;\n\n    switch (key) {\n      case 'OTEL_LOG_LEVEL':\n        setLogLevelFromEnv(key, environment, values);\n        break;\n\n      default:\n        if (isEnvVarABoolean(key)) {\n          parseBoolean(key, environment, values);\n        } else if (isEnvVarANumber(key)) {\n          parseNumber(key, environment, values);\n        } else if (isEnvVarAList(key)) {\n          parseStringList(key, environment, values);\n        } else {\n          const value = values[key];\n          if (typeof value !== 'undefined' && value !== null) {\n            environment[key] = String(value);\n          }\n        }\n    }\n  }\n\n  return environment;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;AAEH,OAAO,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AAClD,OAAO,EAAE,mBAAmB,EAAE,MAAM,YAAY,CAAC;;;AAEjD,IAAM,sBAAsB,GAAG,GAAG,CAAC;AAEnC;;GAEG,CAEH,IAAM,wBAAwB,GAAG;IAAC,mBAAmB;CAAU,CAAC;AAMhE,SAAS,gBAAgB,CAAC,GAAY;IACpC,OAAO,AACL,wBAAwB,CAAC,OAAO,CAAC,GAAiC,CAAC,GAAG,CAAC,CAAC,CACzE,CAAC;AACJ,CAAC;AAED,IAAM,wBAAwB,GAAG;IAC/B,yBAAyB;IACzB,gCAAgC;IAChC,yBAAyB;IACzB,yBAAyB;IACzB,0BAA0B;IAC1B,iCAAiC;IACjC,0BAA0B;IAC1B,0BAA0B;IAC1B,mCAAmC;IACnC,4BAA4B;IAC5B,wCAAwC;IACxC,iCAAiC;IACjC,6CAA6C;IAC7C,sCAAsC;IACtC,6BAA6B;IAC7B,4BAA4B;IAC5B,2CAA2C;IAC3C,0CAA0C;IAC1C,4BAA4B;IAC5B,mCAAmC;IACnC,oCAAoC;IACpC,iCAAiC;IACjC,iCAAiC;CACzB,CAAC;AAMX,SAAS,eAAe,CAAC,GAAY;IACnC,OAAO,AACL,wBAAwB,CAAC,OAAO,CAAC,GAAgC,CAAC,GAAG,CAAC,CAAC,CACxE,CAAC;AACJ,CAAC;AAED,IAAM,sBAAsB,GAAG;IAC7B,uBAAuB;IACvB,kBAAkB;IAClB,+BAA+B;CACvB,CAAC;AAMX,SAAS,aAAa,CAAC,GAAY;IACjC,OAAO,sBAAsB,CAAC,OAAO,CAAC,GAA8B,CAAC,GAAG,CAAC,CAAC,CAAC;AAC7E,CAAC;AA8DM,IAAM,oCAAoC,GAAG,QAAQ,CAAC;AAEtD,IAAM,6BAA6B,GAAG,GAAG,CAAC;AAE1C,IAAM,4CAA4C,GAAG,GAAG,CAAC;AACzD,IAAM,2CAA2C,GAAG,GAAG,CAAC;AAKxD,IAAM,mBAAmB,GAA0B;IACxD,iBAAiB,EAAE,KAAK;IACxB,cAAc,EAAE,EAAE;IAClB,6BAA6B,EAAE,EAAE;IACjC,0BAA0B,EAAE,EAAE;IAC9B,QAAQ,EAAE,EAAE;IACZ,uBAAuB,EAAE,EAAE;IAC3B,SAAS,EAAE,EAAE;IACb,uBAAuB,EAAE,KAAK;IAC9B,8BAA8B,EAAE,GAAG;IACnC,uBAAuB,EAAE,IAAI;IAC7B,uBAAuB,EAAE,IAAI;IAC7B,wBAAwB,EAAE,KAAK;IAC/B,+BAA+B,EAAE,GAAG;IACpC,wBAAwB,EAAE,IAAI;IAC9B,wBAAwB,EAAE,IAAI;IAC9B,+BAA+B,EAAE,EAAE;IACnC,+BAA+B,EAAE,IAAI;IACrC,6BAA6B,EAAE,EAAE;IACjC,6BAA6B,EAAE,EAAE;IACjC,yBAAyB,EAAE,EAAE;IAC7B,2BAA2B,EAAE,EAAE;IAC/B,kCAAkC,EAAE,EAAE;IACtC,mCAAmC,EAAE,EAAE;IACvC,gCAAgC,EAAE,EAAE;IACpC,0BAA0B,EAAE,EAAE;IAC9B,iCAAiC,EAAE,EAAE;IACrC,kCAAkC,EAAE,EAAE;IACtC,+BAA+B,EAAE,EAAE;IACnC,0BAA0B,EAAE,KAAK;IACjC,iCAAiC,EAAE,KAAK;IACxC,kCAAkC,EAAE,KAAK;IACzC,+BAA+B,EAAE,KAAK;IACtC,6BAA6B,EAAE,oCAAoC;IACnE,cAAc,wIAAE,eAAY,CAAC,IAAI;IACjC,qBAAqB,EAAE,EAAE;IACzB,gBAAgB,EAAE;QAAC,cAAc;QAAE,SAAS;KAAC;IAC7C,wBAAwB,EAAE,EAAE;IAC5B,iBAAiB,EAAE,EAAE;IACrB,iCAAiC,EAAE,oCAAoC;IACvE,0BAA0B,EAAE,6BAA6B;IACzD,sCAAsC,EAAE,oCAAoC;IAC5E,+BAA+B,EAAE,6BAA6B;IAC9D,2CAA2C,EACzC,oCAAoC;IACtC,oCAAoC,EAAE,6BAA6B;IACnE,2BAA2B,EAAE,GAAG;IAChC,0BAA0B,EAAE,GAAG;IAC/B,yCAAyC,EACvC,4CAA4C;IAC9C,wCAAwC,EACtC,2CAA2C;IAC7C,oBAAoB,EAAE,EAAE;IACxB,mBAAmB,+KAAE,sBAAmB,CAAC,mBAAmB;IAC5D,uBAAuB,EAAE,EAAE;IAC3B,kBAAkB,EAAE,EAAE;IACtB,2BAA2B,EAAE,EAAE;IAC/B,kCAAkC,EAAE,EAAE;IACtC,mCAAmC,EAAE,EAAE;IACvC,gCAAgC,EAAE,EAAE;IACpC,8BAA8B,EAAE,EAAE;IAClC,qCAAqC,EAAE,EAAE;IACzC,sCAAsC,EAAE,EAAE;IAC1C,mCAAmC,EAAE,EAAE;IACvC,8BAA8B,EAAE,EAAE;IAClC,qCAAqC,EAAE,EAAE;IACzC,sCAAsC,EAAE,EAAE;IAC1C,mCAAmC,EAAE,EAAE;IACvC,6BAA6B,EAAE,EAAE;IACjC,oCAAoC,EAAE,EAAE;IACxC,qCAAqC,EAAE,EAAE;IACzC,kCAAkC,EAAE,EAAE;IACtC,qCAAqC,EAAE,EAAE;IACzC,4CAA4C,EAAE,EAAE;IAChD,6CAA6C,EAAE,EAAE;IACjD,0CAA0C,EAAE,EAAE;IAC9C,2BAA2B,EAAE,eAAe;IAC5C,kCAAkC,EAAE,eAAe;IACnD,mCAAmC,EAAE,eAAe;IACpD,gCAAgC,EAAE,eAAe;IACjD,iDAAiD,EAAE,YAAY;IAC/D,6BAA6B,EAAE,EAAE;CAClC,CAAC;AAEF;;;;GAIG,CACH,SAAS,YAAY,CACnB,GAA+B,EAC/B,WAAwB,EACxB,MAAuB;IAEvB,IAAI,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,WAAW,EAAE;QACtC,OAAO;KACR;IAED,IAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;IAClC,kCAAkC;IAClC,WAAW,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,WAAW,EAAE,KAAK,MAAM,CAAC;AACpD,CAAC;AAED;;;;;;;GAOG,CACH,SAAS,WAAW,CAClB,IAA+B,EAC/B,WAAwB,EACxB,MAAuB,EACvB,GAAe,EACf,GAAc;IADd,IAAA,QAAA,KAAA,GAAA;QAAA,MAAA,CAAO,QAAQ;IAAA;IACf,IAAA,QAAA,KAAA,GAAA;QAAA,MAAA,QAAc;IAAA;IAEd,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,WAAW,EAAE;QACvC,IAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAW,CAAC,CAAC;QAC7C,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YACjB,IAAI,KAAK,GAAG,GAAG,EAAE;gBACf,WAAW,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;aACzB,MAAM,IAAI,KAAK,GAAG,GAAG,EAAE;gBACtB,WAAW,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;aACzB,MAAM;gBACL,WAAW,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;aAC3B;SACF;KACF;AACH,CAAC;AAED;;;;;;GAMG,CACH,SAAS,eAAe,CACtB,IAA6B,EAC7B,MAAmB,EACnB,KAAsB,EACtB,SAAkC;IAAlC,IAAA,cAAA,KAAA,GAAA;QAAA,YAAA,sBAAkC;IAAA;IAElC,IAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;IAC/B,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;QAClC,MAAM,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,SAAA,CAAC;YAAI,OAAA,CAAC,CAAC,IAAI,EAAE;QAAR,CAAQ,CAAC,CAAC;KAC/D;AACH,CAAC;AAED,8CAA8C;AAC9C,IAAM,WAAW,GAAoC;IACnD,GAAG,wIAAE,eAAY,CAAC,GAAG;IACrB,OAAO,wIAAE,eAAY,CAAC,OAAO;IAC7B,KAAK,wIAAE,eAAY,CAAC,KAAK;IACzB,IAAI,wIAAE,eAAY,CAAC,IAAI;IACvB,IAAI,wIAAE,eAAY,CAAC,IAAI;IACvB,KAAK,wIAAE,eAAY,CAAC,KAAK;IACzB,IAAI,wIAAE,eAAY,CAAC,IAAI;CACxB,CAAC;AAEF;;;;;GAKG,CACH,SAAS,kBAAkB,CACzB,GAAsB,EACtB,WAA0C,EAC1C,MAAuB;IAEvB,IAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;IAC1B,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAC7B,IAAM,QAAQ,GAAG,WAAW,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC;QAClD,IAAI,QAAQ,IAAI,IAAI,EAAE;YACpB,WAAW,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC;SAC7B;KACF;AACH,CAAC;AAMK,SAAU,gBAAgB,CAAC,MAAuB;IACtD,IAAM,WAAW,GAAgB,CAAA,CAAE,CAAC;IAEpC,IAAK,IAAM,GAAG,IAAI,mBAAmB,CAAE;QACrC,IAAM,GAAG,GAAG,GAAwB,CAAC;QAErC,OAAQ,GAAG,EAAE;YACX,KAAK,gBAAgB;gBACnB,kBAAkB,CAAC,GAAG,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC;gBAC7C,MAAM;YAER;gBACE,IAAI,gBAAgB,CAAC,GAAG,CAAC,EAAE;oBACzB,YAAY,CAAC,GAAG,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC;iBACxC,MAAM,IAAI,eAAe,CAAC,GAAG,CAAC,EAAE;oBAC/B,WAAW,CAAC,GAAG,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC;iBACvC,MAAM,IAAI,aAAa,CAAC,GAAG,CAAC,EAAE;oBAC7B,eAAe,CAAC,GAAG,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC;iBAC3C,MAAM;oBACL,IAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;oBAC1B,IAAI,OAAO,KAAK,KAAK,WAAW,IAAI,KAAK,KAAK,IAAI,EAAE;wBAClD,WAAW,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;qBAClC;iBACF;SACJ;KACF;IAED,OAAO,WAAW,CAAC;AACrB,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1345, "column": 0}, "map": {"version": 3, "file": "environment.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40opentelemetry/core/src/platform/node/environment.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  DEFAULT_ENVIRONMENT,\n  ENVIRONMENT,\n  RAW_ENVIRONMENT,\n  parseEnvironment,\n} from '../../utils/environment';\n\n/**\n * Gets the environment variables\n */\nexport function getEnv(): Required<ENVIRONMENT> {\n  const processEnv = parseEnvironment(process.env as RAW_ENVIRONMENT);\n  return Object.assign({}, DEFAULT_ENVIRONMENT, processEnv);\n}\n\nexport function getEnvWithoutDefaults(): ENVIRONMENT {\n  return parseEnvironment(process.env as RAW_ENVIRONMENT);\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAEH,OAAO,EACL,mBAAmB,EAGnB,gBAAgB,GACjB,MAAM,yBAAyB,CAAC;;AAK3B,SAAU,MAAM;IACpB,IAAM,UAAU,uLAAG,mBAAA,AAAgB,EAAC,OAAO,CAAC,GAAsB,CAAC,CAAC;IACpE,OAAO,MAAM,CAAC,MAAM,CAAC,CAAA,CAAE,kLAAE,sBAAmB,EAAE,UAAU,CAAC,CAAC;AAC5D,CAAC;AAEK,SAAU,qBAAqB;IACnC,2LAAO,mBAAA,AAAgB,EAAC,OAAO,CAAC,GAAsB,CAAC,CAAC;AAC1D,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1378, "column": 0}, "map": {"version": 3, "file": "hex-to-base64.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40opentelemetry/core/src/platform/node/hex-to-base64.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { hexToBinary } from '../../common/hex-to-binary';\n\nexport function hexToBase64(hexStr: string): string {\n  return Buffer.from(hexToBinary(hexStr)).toString('base64');\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AACH,OAAO,EAAE,WAAW,EAAE,MAAM,4BAA4B,CAAC;;AAEnD,SAAU,WAAW,CAAC,MAAc;IACxC,OAAO,MAAM,CAAC,IAAI,8LAAC,cAAA,AAAW,EAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAC7D,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1406, "column": 0}, "map": {"version": 3, "file": "timer-util.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40opentelemetry/core/src/platform/node/timer-util.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport function unrefTimer(timer: NodeJS.Timer): void {\n  timer.unref();\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AACG,SAAU,UAAU,CAAC,KAAmB;IAC5C,KAAK,CAAC,KAAK,EAAE,CAAC;AAChB,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1432, "column": 0}, "map": {"version": 3, "file": "composite.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40opentelemetry/core/src/propagation/composite.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Context,\n  TextMapGetter,\n  TextMapPropagator,\n  diag,\n  TextMapSetter,\n} from '@opentelemetry/api';\n\n/** Configuration object for composite propagator */\nexport interface CompositePropagatorConfig {\n  /**\n   * List of propagators to run. Propagators run in the\n   * list order. If a propagator later in the list writes the same context\n   * key as a propagator earlier in the list, the later on will \"win\".\n   */\n  propagators?: TextMapPropagator[];\n}\n\n/** Combines multiple propagators into a single propagator. */\nexport class CompositePropagator implements TextMapPropagator {\n  private readonly _propagators: TextMapPropagator[];\n  private readonly _fields: string[];\n\n  /**\n   * Construct a composite propagator from a list of propagators.\n   *\n   * @param [config] Configuration object for composite propagator\n   */\n  constructor(config: CompositePropagatorConfig = {}) {\n    this._propagators = config.propagators ?? [];\n\n    this._fields = Array.from(\n      new Set(\n        this._propagators\n          // older propagators may not have fields function, null check to be sure\n          .map(p => (typeof p.fields === 'function' ? p.fields() : []))\n          .reduce((x, y) => x.concat(y), [])\n      )\n    );\n  }\n\n  /**\n   * Run each of the configured propagators with the given context and carrier.\n   * Propagators are run in the order they are configured, so if multiple\n   * propagators write the same carrier key, the propagator later in the list\n   * will \"win\".\n   *\n   * @param context Context to inject\n   * @param carrier Carrier into which context will be injected\n   */\n  inject(context: Context, carrier: unknown, setter: TextMapSetter): void {\n    for (const propagator of this._propagators) {\n      try {\n        propagator.inject(context, carrier, setter);\n      } catch (err) {\n        diag.warn(\n          `Failed to inject with ${propagator.constructor.name}. Err: ${err.message}`\n        );\n      }\n    }\n  }\n\n  /**\n   * Run each of the configured propagators with the given context and carrier.\n   * Propagators are run in the order they are configured, so if multiple\n   * propagators write the same context key, the propagator later in the list\n   * will \"win\".\n   *\n   * @param context Context to add values to\n   * @param carrier Carrier from which to extract context\n   */\n  extract(context: Context, carrier: unknown, getter: TextMapGetter): Context {\n    return this._propagators.reduce((ctx, propagator) => {\n      try {\n        return propagator.extract(ctx, carrier, getter);\n      } catch (err) {\n        diag.warn(\n          `Failed to extract with ${propagator.constructor.name}. Err: ${err.message}`\n        );\n      }\n      return ctx;\n    }, context);\n  }\n\n  fields(): string[] {\n    // return a new array so our fields cannot be modified\n    return this._fields.slice();\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAIL,IAAI,GAEL,MAAM,oBAAoB,CAAC;;;;;;;;;;;;;;;;AAY5B,4DAAA,EAA8D,CAC9D,IAAA,sBAAA;IAIE;;;;OAIG,CACH,SAAA,oBAAY,MAAsC;QAAtC,IAAA,WAAA,KAAA,GAAA;YAAA,SAAA,CAAA,CAAsC;QAAA;;QAChD,IAAI,CAAC,YAAY,GAAG,CAAA,KAAA,MAAM,CAAC,WAAW,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,EAAE,CAAC;QAE7C,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,IAAI,CACvB,IAAI,GAAG,CACL,IAAI,CAAC,YAAY,AACf,wEAAwE;SACvE,GAAG,CAAC,SAAA,CAAC;YAAI,OAAA,AAAC,OAAO,CAAC,CAAC,MAAM,KAAK,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QAAlD,CAAkD,CAAC,CAC5D,MAAM,CAAC,SAAC,CAAC,EAAE,CAAC;YAAK,OAAA,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;QAAX,CAAW,EAAE,EAAE,CAAC,CACrC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;OAQG,CACH,oBAAA,SAAA,CAAA,MAAM,GAAN,SAAO,OAAgB,EAAE,OAAgB,EAAE,MAAqB;;;YAC9D,IAAyB,IAAA,KAAA,SAAA,IAAI,CAAC,YAAY,CAAA,EAAA,KAAA,GAAA,IAAA,EAAA,EAAA,CAAA,GAAA,IAAA,EAAA,KAAA,GAAA,IAAA,GAAE;gBAAvC,IAAM,UAAU,GAAA,GAAA,KAAA;gBACnB,IAAI;oBACF,UAAU,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;iBAC7C,CAAC,OAAO,GAAG,EAAE;0JACZ,OAAI,CAAC,IAAI,CACP,2BAAyB,UAAU,CAAC,WAAW,CAAC,IAAI,GAAA,YAAU,GAAG,CAAC,OAAS,CAC5E,CAAC;iBACH;aACF;;;;;;;;;;;;IACH,CAAC;IAED;;;;;;;;OAQG,CACH,oBAAA,SAAA,CAAA,OAAO,GAAP,SAAQ,OAAgB,EAAE,OAAgB,EAAE,MAAqB;QAC/D,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,SAAC,GAAG,EAAE,UAAU;YAC9C,IAAI;gBACF,OAAO,UAAU,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;aACjD,CAAC,OAAO,GAAG,EAAE;sJACZ,OAAI,CAAC,IAAI,CACP,4BAA0B,UAAU,CAAC,WAAW,CAAC,IAAI,GAAA,YAAU,GAAG,CAAC,OAAS,CAC7E,CAAC;aACH;YACD,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,OAAO,CAAC,CAAC;IACd,CAAC;IAED,oBAAA,SAAA,CAAA,MAAM,GAAN;QACE,sDAAsD;QACtD,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;IAC9B,CAAC;IACH,OAAA,mBAAC;AAAD,CAAC,AArED,IAqEC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1546, "column": 0}, "map": {"version": 3, "file": "validators.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40opentelemetry/core/src/internal/validators.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nconst VALID_KEY_CHAR_RANGE = '[_0-9a-z-*/]';\nconst VALID_KEY = `[a-z]${VALID_KEY_CHAR_RANGE}{0,255}`;\nconst VALID_VENDOR_KEY = `[a-z0-9]${VALID_KEY_CHAR_RANGE}{0,240}@[a-z]${VALID_KEY_CHAR_RANGE}{0,13}`;\nconst VALID_KEY_REGEX = new RegExp(`^(?:${VALID_KEY}|${VALID_VENDOR_KEY})$`);\nconst VALID_VALUE_BASE_REGEX = /^[ -~]{0,255}[!-~]$/;\nconst INVALID_VALUE_COMMA_EQUAL_REGEX = /,|=/;\n\n/**\n * Key is opaque string up to 256 characters printable. It MUST begin with a\n * lowercase letter, and can only contain lowercase letters a-z, digits 0-9,\n * underscores _, dashes -, asterisks *, and forward slashes /.\n * For multi-tenant vendor scenarios, an at sign (@) can be used to prefix the\n * vendor name. Vendors SHOULD set the tenant ID at the beginning of the key.\n * see https://www.w3.org/TR/trace-context/#key\n */\nexport function validateKey(key: string): boolean {\n  return VALID_KEY_REGEX.test(key);\n}\n\n/**\n * Value is opaque string up to 256 characters printable ASCII RFC0020\n * characters (i.e., the range 0x20 to 0x7E) except comma , and =.\n */\nexport function validateValue(value: string): boolean {\n  return (\n    VALID_VALUE_BASE_REGEX.test(value) &&\n    !INVALID_VALUE_COMMA_EQUAL_REGEX.test(value)\n  );\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAEH,IAAM,oBAAoB,GAAG,cAAc,CAAC;AAC5C,IAAM,SAAS,GAAG,UAAQ,oBAAoB,GAAA,SAAS,CAAC;AACxD,IAAM,gBAAgB,GAAG,aAAW,oBAAoB,GAAA,kBAAgB,oBAAoB,GAAA,QAAQ,CAAC;AACrG,IAAM,eAAe,GAAG,IAAI,MAAM,CAAC,SAAO,SAAS,GAAA,MAAI,gBAAgB,GAAA,IAAI,CAAC,CAAC;AAC7E,IAAM,sBAAsB,GAAG,qBAAqB,CAAC;AACrD,IAAM,+BAA+B,GAAG,KAAK,CAAC;AAUxC,SAAU,WAAW,CAAC,GAAW;IACrC,OAAO,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACnC,CAAC;AAMK,SAAU,aAAa,CAAC,KAAa;IACzC,OAAO,AACL,sBAAsB,CAAC,IAAI,CAAC,KAAK,CAAC,IAClC,CAAC,+BAA+B,CAAC,IAAI,CAAC,KAAK,CAAC,CAC7C,CAAC;AACJ,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1582, "column": 0}, "map": {"version": 3, "file": "TraceState.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40opentelemetry/core/src/trace/TraceState.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as api from '@opentelemetry/api';\nimport { validateKey, validateValue } from '../internal/validators';\n\nconst MAX_TRACE_STATE_ITEMS = 32;\nconst MAX_TRACE_STATE_LEN = 512;\nconst LIST_MEMBERS_SEPARATOR = ',';\nconst LIST_MEMBER_KEY_VALUE_SPLITTER = '=';\n\n/**\n * TraceState must be a class and not a simple object type because of the spec\n * requirement (https://www.w3.org/TR/trace-context/#tracestate-field).\n *\n * Here is the list of allowed mutations:\n * - New key-value pair should be added into the beginning of the list\n * - The value of any key can be updated. Modified keys MUST be moved to the\n * beginning of the list.\n */\nexport class TraceState implements api.TraceState {\n  private _internalState: Map<string, string> = new Map();\n\n  constructor(rawTraceState?: string) {\n    if (rawTraceState) this._parse(rawTraceState);\n  }\n\n  set(key: string, value: string): TraceState {\n    // TODO: Benchmark the different approaches(map vs list) and\n    // use the faster one.\n    const traceState = this._clone();\n    if (traceState._internalState.has(key)) {\n      traceState._internalState.delete(key);\n    }\n    traceState._internalState.set(key, value);\n    return traceState;\n  }\n\n  unset(key: string): TraceState {\n    const traceState = this._clone();\n    traceState._internalState.delete(key);\n    return traceState;\n  }\n\n  get(key: string): string | undefined {\n    return this._internalState.get(key);\n  }\n\n  serialize(): string {\n    return this._keys()\n      .reduce((agg: string[], key) => {\n        agg.push(key + LIST_MEMBER_KEY_VALUE_SPLITTER + this.get(key));\n        return agg;\n      }, [])\n      .join(LIST_MEMBERS_SEPARATOR);\n  }\n\n  private _parse(rawTraceState: string) {\n    if (rawTraceState.length > MAX_TRACE_STATE_LEN) return;\n    this._internalState = rawTraceState\n      .split(LIST_MEMBERS_SEPARATOR)\n      .reverse() // Store in reverse so new keys (.set(...)) will be placed at the beginning\n      .reduce((agg: Map<string, string>, part: string) => {\n        const listMember = part.trim(); // Optional Whitespace (OWS) handling\n        const i = listMember.indexOf(LIST_MEMBER_KEY_VALUE_SPLITTER);\n        if (i !== -1) {\n          const key = listMember.slice(0, i);\n          const value = listMember.slice(i + 1, part.length);\n          if (validateKey(key) && validateValue(value)) {\n            agg.set(key, value);\n          } else {\n            // TODO: Consider to add warning log\n          }\n        }\n        return agg;\n      }, new Map());\n\n    // Because of the reverse() requirement, trunc must be done after map is created\n    if (this._internalState.size > MAX_TRACE_STATE_ITEMS) {\n      this._internalState = new Map(\n        Array.from(this._internalState.entries())\n          .reverse() // Use reverse same as original tracestate parse chain\n          .slice(0, MAX_TRACE_STATE_ITEMS)\n      );\n    }\n  }\n\n  private _keys(): string[] {\n    return Array.from(this._internalState.keys()).reverse();\n  }\n\n  private _clone(): TraceState {\n    const traceState = new TraceState();\n    traceState._internalState = new Map(this._internalState);\n    return traceState;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAGH,OAAO,EAAE,WAAW,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;;AAEpE,IAAM,qBAAqB,GAAG,EAAE,CAAC;AACjC,IAAM,mBAAmB,GAAG,GAAG,CAAC;AAChC,IAAM,sBAAsB,GAAG,GAAG,CAAC;AACnC,IAAM,8BAA8B,GAAG,GAAG,CAAC;AAE3C;;;;;;;;GAQG,CACH,IAAA,aAAA;IAGE,SAAA,WAAY,aAAsB;QAF1B,IAAA,CAAA,cAAc,GAAwB,IAAI,GAAG,EAAE,CAAC;QAGtD,IAAI,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;IAChD,CAAC;IAED,WAAA,SAAA,CAAA,GAAG,GAAH,SAAI,GAAW,EAAE,KAAa;QAC5B,4DAA4D;QAC5D,sBAAsB;QACtB,IAAM,UAAU,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QACjC,IAAI,UAAU,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YACtC,UAAU,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;SACvC;QACD,UAAU,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAC1C,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,WAAA,SAAA,CAAA,KAAK,GAAL,SAAM,GAAW;QACf,IAAM,UAAU,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QACjC,UAAU,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACtC,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,WAAA,SAAA,CAAA,GAAG,GAAH,SAAI,GAAW;QACb,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACtC,CAAC;IAED,WAAA,SAAA,CAAA,SAAS,GAAT;QAAA,IAAA,QAAA,IAAA,CAOC;QANC,OAAO,IAAI,CAAC,KAAK,EAAE,CAChB,MAAM,CAAC,SAAC,GAAa,EAAE,GAAG;YACzB,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,8BAA8B,GAAG,KAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;YAC/D,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAAE,CAAC,CACL,IAAI,CAAC,sBAAsB,CAAC,CAAC;IAClC,CAAC;IAEO,WAAA,SAAA,CAAA,MAAM,GAAd,SAAe,aAAqB;QAClC,IAAI,aAAa,CAAC,MAAM,GAAG,mBAAmB,EAAE,OAAO;QACvD,IAAI,CAAC,cAAc,GAAG,aAAa,CAChC,KAAK,CAAC,sBAAsB,CAAC,CAC7B,OAAO,EAAE,CAAC,2EAA2E;SACrF,MAAM,CAAC,SAAC,GAAwB,EAAE,IAAY;YAC7C,IAAM,UAAU,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,qCAAqC;YACrE,IAAM,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,8BAA8B,CAAC,CAAC;YAC7D,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;gBACZ,IAAM,GAAG,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACnC,IAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;gBACnD,0LAAI,cAAA,AAAW,EAAC,GAAG,CAAC,0LAAI,gBAAA,AAAa,EAAC,KAAK,CAAC,EAAE;oBAC5C,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;iBACrB,MAAM;gBACL,oCAAoC;iBACrC;aACF;YACD,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;QAEhB,gFAAgF;QAChF,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,GAAG,qBAAqB,EAAE;YACpD,IAAI,CAAC,cAAc,GAAG,IAAI,GAAG,CAC3B,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC,CACtC,OAAO,EAAE,CAAC,sDAAsD;aAChE,KAAK,CAAC,CAAC,EAAE,qBAAqB,CAAC,CACnC,CAAC;SACH;IACH,CAAC;IAEO,WAAA,SAAA,CAAA,KAAK,GAAb;QACE,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC;IAC1D,CAAC;IAEO,WAAA,SAAA,CAAA,MAAM,GAAd;QACE,IAAM,UAAU,GAAG,IAAI,UAAU,EAAE,CAAC;QACpC,UAAU,CAAC,cAAc,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACzD,OAAO,UAAU,CAAC;IACpB,CAAC;IACH,OAAA,UAAC;AAAD,CAAC,AA5ED,IA4EC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1684, "column": 0}, "map": {"version": 3, "file": "W3CTraceContextPropagator.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40opentelemetry/core/src/trace/W3CTraceContextPropagator.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Context,\n  isSpanContextValid,\n  SpanContext,\n  TextMapGetter,\n  TextMapPropagator,\n  TextMapSetter,\n  trace,\n  TraceFlags,\n} from '@opentelemetry/api';\nimport { isTracingSuppressed } from './suppress-tracing';\nimport { TraceState } from './TraceState';\n\nexport const TRACE_PARENT_HEADER = 'traceparent';\nexport const TRACE_STATE_HEADER = 'tracestate';\n\nconst VERSION = '00';\nconst VERSION_PART = '(?!ff)[\\\\da-f]{2}';\nconst TRACE_ID_PART = '(?![0]{32})[\\\\da-f]{32}';\nconst PARENT_ID_PART = '(?![0]{16})[\\\\da-f]{16}';\nconst FLAGS_PART = '[\\\\da-f]{2}';\nconst TRACE_PARENT_REGEX = new RegExp(\n  `^\\\\s?(${VERSION_PART})-(${TRACE_ID_PART})-(${PARENT_ID_PART})-(${FLAGS_PART})(-.*)?\\\\s?$`\n);\n\n/**\n * Parses information from the [traceparent] span tag and converts it into {@link SpanContext}\n * @param traceParent - A meta property that comes from server.\n *     It should be dynamically generated server side to have the server's request trace Id,\n *     a parent span Id that was set on the server's request span,\n *     and the trace flags to indicate the server's sampling decision\n *     (01 = sampled, 00 = not sampled).\n *     for example: '{version}-{traceId}-{spanId}-{sampleDecision}'\n *     For more information see {@link https://www.w3.org/TR/trace-context/}\n */\nexport function parseTraceParent(traceParent: string): SpanContext | null {\n  const match = TRACE_PARENT_REGEX.exec(traceParent);\n  if (!match) return null;\n\n  // According to the specification the implementation should be compatible\n  // with future versions. If there are more parts, we only reject it if it's using version 00\n  // See https://www.w3.org/TR/trace-context/#versioning-of-traceparent\n  if (match[1] === '00' && match[5]) return null;\n\n  return {\n    traceId: match[2],\n    spanId: match[3],\n    traceFlags: parseInt(match[4], 16),\n  };\n}\n\n/**\n * Propagates {@link SpanContext} through Trace Context format propagation.\n *\n * Based on the Trace Context specification:\n * https://www.w3.org/TR/trace-context/\n */\nexport class W3CTraceContextPropagator implements TextMapPropagator {\n  inject(context: Context, carrier: unknown, setter: TextMapSetter): void {\n    const spanContext = trace.getSpanContext(context);\n    if (\n      !spanContext ||\n      isTracingSuppressed(context) ||\n      !isSpanContextValid(spanContext)\n    )\n      return;\n\n    const traceParent = `${VERSION}-${spanContext.traceId}-${\n      spanContext.spanId\n    }-0${Number(spanContext.traceFlags || TraceFlags.NONE).toString(16)}`;\n\n    setter.set(carrier, TRACE_PARENT_HEADER, traceParent);\n    if (spanContext.traceState) {\n      setter.set(\n        carrier,\n        TRACE_STATE_HEADER,\n        spanContext.traceState.serialize()\n      );\n    }\n  }\n\n  extract(context: Context, carrier: unknown, getter: TextMapGetter): Context {\n    const traceParentHeader = getter.get(carrier, TRACE_PARENT_HEADER);\n    if (!traceParentHeader) return context;\n    const traceParent = Array.isArray(traceParentHeader)\n      ? traceParentHeader[0]\n      : traceParentHeader;\n    if (typeof traceParent !== 'string') return context;\n    const spanContext = parseTraceParent(traceParent);\n    if (!spanContext) return context;\n\n    spanContext.isRemote = true;\n\n    const traceStateHeader = getter.get(carrier, TRACE_STATE_HEADER);\n    if (traceStateHeader) {\n      // If more than one `tracestate` header is found, we merge them into a\n      // single header.\n      const state = Array.isArray(traceStateHeader)\n        ? traceStateHeader.join(',')\n        : traceStateHeader;\n      spanContext.traceState = new TraceState(\n        typeof state === 'string' ? state : undefined\n      );\n    }\n    return trace.setSpanContext(context, spanContext);\n  }\n\n  fields(): string[] {\n    return [TRACE_PARENT_HEADER, TRACE_STATE_HEADER];\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;AAEH,OAAO,EAEL,kBAAkB,EAKlB,KAAK,EACL,UAAU,GACX,MAAM,oBAAoB,CAAC;AAC5B,OAAO,EAAE,mBAAmB,EAAE,MAAM,oBAAoB,CAAC;AACzD,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;;;;AAEnC,IAAM,mBAAmB,GAAG,aAAa,CAAC;AAC1C,IAAM,kBAAkB,GAAG,YAAY,CAAC;AAE/C,IAAM,OAAO,GAAG,IAAI,CAAC;AACrB,IAAM,YAAY,GAAG,mBAAmB,CAAC;AACzC,IAAM,aAAa,GAAG,yBAAyB,CAAC;AAChD,IAAM,cAAc,GAAG,yBAAyB,CAAC;AACjD,IAAM,UAAU,GAAG,aAAa,CAAC;AACjC,IAAM,kBAAkB,GAAG,IAAI,MAAM,CACnC,WAAS,YAAY,GAAA,QAAM,aAAa,GAAA,QAAM,cAAc,GAAA,QAAM,UAAU,GAAA,cAAc,CAC3F,CAAC;AAYI,SAAU,gBAAgB,CAAC,WAAmB;IAClD,IAAM,KAAK,GAAG,kBAAkB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IACnD,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,CAAC;IAExB,yEAAyE;IACzE,4FAA4F;IAC5F,qEAAqE;IACrE,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,OAAO,IAAI,CAAC;IAE/C,OAAO;QACL,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;QACjB,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;QAChB,UAAU,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;KACnC,CAAC;AACJ,CAAC;AAED;;;;;GAKG,CACH,IAAA,4BAAA;IAAA,SAAA,6BAqDA,CAAC;IApDC,0BAAA,SAAA,CAAA,MAAM,GAAN,SAAO,OAAgB,EAAE,OAAgB,EAAE,MAAqB;QAC9D,IAAM,WAAW,yIAAG,QAAK,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QAClD,IACE,CAAC,WAAW,gMACZ,sBAAA,AAAmB,EAAC,OAAO,CAAC,IAC5B,EAAC,8JAAA,AAAkB,EAAC,WAAW,CAAC,EAEhC,OAAO;QAET,IAAM,WAAW,GAAM,OAAO,GAAA,MAAI,WAAW,CAAC,OAAO,GAAA,MACnD,WAAW,CAAC,MAAM,GAAA,OACf,MAAM,CAAC,WAAW,CAAC,UAAU,0IAAI,aAAU,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAG,CAAC;QAEtE,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,mBAAmB,EAAE,WAAW,CAAC,CAAC;QACtD,IAAI,WAAW,CAAC,UAAU,EAAE;YAC1B,MAAM,CAAC,GAAG,CACR,OAAO,EACP,kBAAkB,EAClB,WAAW,CAAC,UAAU,CAAC,SAAS,EAAE,CACnC,CAAC;SACH;IACH,CAAC;IAED,0BAAA,SAAA,CAAA,OAAO,GAAP,SAAQ,OAAgB,EAAE,OAAgB,EAAE,MAAqB;QAC/D,IAAM,iBAAiB,GAAG,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,mBAAmB,CAAC,CAAC;QACnE,IAAI,CAAC,iBAAiB,EAAE,OAAO,OAAO,CAAC;QACvC,IAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC,GAChD,iBAAiB,CAAC,CAAC,CAAC,GACpB,iBAAiB,CAAC;QACtB,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE,OAAO,OAAO,CAAC;QACpD,IAAM,WAAW,GAAG,gBAAgB,CAAC,WAAW,CAAC,CAAC;QAClD,IAAI,CAAC,WAAW,EAAE,OAAO,OAAO,CAAC;QAEjC,WAAW,CAAC,QAAQ,GAAG,IAAI,CAAC;QAE5B,IAAM,gBAAgB,GAAG,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;QACjE,IAAI,gBAAgB,EAAE;YACpB,sEAAsE;YACtE,iBAAiB;YACjB,IAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC,GACzC,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,GAC1B,gBAAgB,CAAC;YACrB,WAAW,CAAC,UAAU,GAAG,mLAAI,aAAU,CACrC,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAC9C,CAAC;SACH;QACD,6IAAO,QAAK,CAAC,cAAc,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;IACpD,CAAC;IAED,0BAAA,SAAA,CAAA,MAAM,GAAN;QACE,OAAO;YAAC,mBAAmB;YAAE,kBAAkB;SAAC,CAAC;IACnD,CAAC;IACH,OAAA,yBAAC;AAAD,CAAC,AArDD,IAqDC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1780, "column": 0}, "map": {"version": 3, "file": "rpc-metadata.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40opentelemetry/core/src/trace/rpc-metadata.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context, createContextKey, Span } from '@opentelemetry/api';\n\nconst RPC_METADATA_KEY = createContextKey(\n  'OpenTelemetry SDK Context Key RPC_METADATA'\n);\n\nexport enum RPCType {\n  HTTP = 'http',\n}\n\ntype HTTPMetadata = {\n  type: RPCType.HTTP;\n  route?: string;\n  span: Span;\n};\n\n/**\n * Allows for future rpc metadata to be used with this mechanism\n */\nexport type RPCMetadata = HTTPMetadata;\n\nexport function setRPCMetadata(context: Context, meta: RPCMetadata): Context {\n  return context.setValue(RPC_METADATA_KEY, meta);\n}\n\nexport function deleteRPCMetadata(context: Context): Context {\n  return context.deleteValue(RPC_METADATA_KEY);\n}\n\nexport function getRPCMetadata(context: Context): RPCMetadata | undefined {\n  return context.getValue(RPC_METADATA_KEY) as RPCMetadata | undefined;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;AAEH,OAAO,EAAW,gBAAgB,EAAQ,MAAM,oBAAoB,CAAC;;AAErE,IAAM,gBAAgB,6IAAG,mBAAA,AAAgB,EACvC,4CAA4C,CAC7C,CAAC;AAEF,IAAY,OAEX;AAFD,CAAA,SAAY,OAAO;IACjB,OAAA,CAAA,OAAA,GAAA,MAAa,CAAA;AACf,CAAC,EAFW,OAAO,IAAA,CAAP,OAAO,GAAA,CAAA,CAAA,GAElB;AAaK,SAAU,cAAc,CAAC,OAAgB,EAAE,IAAiB;IAChE,OAAO,OAAO,CAAC,QAAQ,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;AAClD,CAAC;AAEK,SAAU,iBAAiB,CAAC,OAAgB;IAChD,OAAO,OAAO,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC;AAC/C,CAAC;AAEK,SAAU,cAAc,CAAC,OAAgB;IAC7C,OAAO,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAA4B,CAAC;AACvE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1822, "column": 0}, "map": {"version": 3, "file": "AlwaysOffSampler.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40opentelemetry/core/src/trace/sampler/AlwaysOffSampler.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Sampler, SamplingDecision, SamplingResult } from '@opentelemetry/api';\n\n/**\n * @deprecated Use the one defined in @opentelemetry/sdk-trace-base instead.\n * Sampler that samples no traces.\n */\nexport class AlwaysOffSampler implements Sampler {\n  shouldSample(): SamplingResult {\n    return {\n      decision: SamplingDecision.NOT_RECORD,\n    };\n  }\n\n  toString(): string {\n    return 'AlwaysOffSampler';\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAW,gBAAgB,EAAkB,MAAM,oBAAoB,CAAC;;AAE/E;;;GAGG,CACH,IAAA,mBAAA;IAAA,SAAA,oBAUA,CAAC;IATC,iBAAA,SAAA,CAAA,YAAY,GAAZ;QACE,OAAO;YACL,QAAQ,wIAAE,mBAAgB,CAAC,UAAU;SACtC,CAAC;IACJ,CAAC;IAED,iBAAA,SAAA,CAAA,QAAQ,GAAR;QACE,OAAO,kBAAkB,CAAC;IAC5B,CAAC;IACH,OAAA,gBAAC;AAAD,CAAC,AAVD,IAUC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1864, "column": 0}, "map": {"version": 3, "file": "AlwaysOnSampler.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40opentelemetry/core/src/trace/sampler/AlwaysOnSampler.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Sampler, SamplingDecision, SamplingResult } from '@opentelemetry/api';\n\n/**\n * @deprecated Use the one defined in @opentelemetry/sdk-trace-base instead.\n * Sampler that samples all traces.\n */\nexport class AlwaysOnSampler implements Sampler {\n  shouldSample(): SamplingResult {\n    return {\n      decision: SamplingDecision.RECORD_AND_SAMPLED,\n    };\n  }\n\n  toString(): string {\n    return 'AlwaysOnSampler';\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAW,gBAAgB,EAAkB,MAAM,oBAAoB,CAAC;;AAE/E;;;GAGG,CACH,IAAA,kBAAA;IAAA,SAAA,mBAUA,CAAC;IATC,gBAAA,SAAA,CAAA,YAAY,GAAZ;QACE,OAAO;YACL,QAAQ,wIAAE,mBAAgB,CAAC,kBAAkB;SAC9C,CAAC;IACJ,CAAC;IAED,gBAAA,SAAA,CAAA,QAAQ,GAAR;QACE,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IACH,OAAA,eAAC;AAAD,CAAC,AAVD,IAUC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1906, "column": 0}, "map": {"version": 3, "file": "ParentBasedSampler.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40opentelemetry/core/src/trace/sampler/ParentBasedSampler.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Context,\n  isSpanContextValid,\n  Link,\n  Sampler,\n  SamplingResult,\n  SpanAttributes,\n  SpanKind,\n  TraceFlags,\n  trace,\n} from '@opentelemetry/api';\nimport { globalErrorHandler } from '../../common/global-error-handler';\nimport { AlwaysOffSampler } from './AlwaysOffSampler';\nimport { AlwaysOnSampler } from './AlwaysOnSampler';\n\n/**\n * @deprecated Use the one defined in @opentelemetry/sdk-trace-base instead.\n * A composite sampler that either respects the parent span's sampling decision\n * or delegates to `delegateSampler` for root spans.\n */\nexport class ParentBasedSampler implements Sampler {\n  private _root: Sampler;\n  private _remoteParentSampled: Sampler;\n  private _remoteParentNotSampled: Sampler;\n  private _localParentSampled: Sampler;\n  private _localParentNotSampled: Sampler;\n\n  constructor(config: ParentBasedSamplerConfig) {\n    this._root = config.root;\n\n    if (!this._root) {\n      globalErrorHandler(\n        new Error('ParentBasedSampler must have a root sampler configured')\n      );\n      this._root = new AlwaysOnSampler();\n    }\n\n    this._remoteParentSampled =\n      config.remoteParentSampled ?? new AlwaysOnSampler();\n    this._remoteParentNotSampled =\n      config.remoteParentNotSampled ?? new AlwaysOffSampler();\n    this._localParentSampled =\n      config.localParentSampled ?? new AlwaysOnSampler();\n    this._localParentNotSampled =\n      config.localParentNotSampled ?? new AlwaysOffSampler();\n  }\n\n  shouldSample(\n    context: Context,\n    traceId: string,\n    spanName: string,\n    spanKind: SpanKind,\n    attributes: SpanAttributes,\n    links: Link[]\n  ): SamplingResult {\n    const parentContext = trace.getSpanContext(context);\n\n    if (!parentContext || !isSpanContextValid(parentContext)) {\n      return this._root.shouldSample(\n        context,\n        traceId,\n        spanName,\n        spanKind,\n        attributes,\n        links\n      );\n    }\n\n    if (parentContext.isRemote) {\n      if (parentContext.traceFlags & TraceFlags.SAMPLED) {\n        return this._remoteParentSampled.shouldSample(\n          context,\n          traceId,\n          spanName,\n          spanKind,\n          attributes,\n          links\n        );\n      }\n      return this._remoteParentNotSampled.shouldSample(\n        context,\n        traceId,\n        spanName,\n        spanKind,\n        attributes,\n        links\n      );\n    }\n\n    if (parentContext.traceFlags & TraceFlags.SAMPLED) {\n      return this._localParentSampled.shouldSample(\n        context,\n        traceId,\n        spanName,\n        spanKind,\n        attributes,\n        links\n      );\n    }\n\n    return this._localParentNotSampled.shouldSample(\n      context,\n      traceId,\n      spanName,\n      spanKind,\n      attributes,\n      links\n    );\n  }\n\n  toString(): string {\n    return `ParentBased{root=${this._root.toString()}, remoteParentSampled=${this._remoteParentSampled.toString()}, remoteParentNotSampled=${this._remoteParentNotSampled.toString()}, localParentSampled=${this._localParentSampled.toString()}, localParentNotSampled=${this._localParentNotSampled.toString()}}`;\n  }\n}\n\ninterface ParentBasedSamplerConfig {\n  /** Sampler called for spans with no parent */\n  root: Sampler;\n  /** Sampler called for spans with a remote parent which was sampled. Default AlwaysOn */\n  remoteParentSampled?: Sampler;\n  /** Sampler called for spans with a remote parent which was not sampled. Default AlwaysOff */\n  remoteParentNotSampled?: Sampler;\n  /** Sampler called for spans with a local parent which was sampled. Default AlwaysOn */\n  localParentSampled?: Sampler;\n  /** Sampler called for spans with a local parent which was not sampled. Default AlwaysOff */\n  localParentNotSampled?: Sampler;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAEL,kBAAkB,EAMlB,UAAU,EACV,KAAK,GACN,MAAM,oBAAoB,CAAC;AAC5B,OAAO,EAAE,kBAAkB,EAAE,MAAM,mCAAmC,CAAC;AACvE,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AACtD,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;;;;;AAEpD;;;;GAIG,CACH,IAAA,qBAAA;IAOE,SAAA,mBAAY,MAAgC;;QAC1C,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC;QAEzB,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;gNACf,qBAAA,AAAkB,EAChB,IAAI,KAAK,CAAC,wDAAwD,CAAC,CACpE,CAAC;YACF,IAAI,CAAC,KAAK,GAAG,mMAAI,kBAAe,EAAE,CAAC;SACpC;QAED,IAAI,CAAC,oBAAoB,GACvB,CAAA,KAAA,MAAM,CAAC,mBAAmB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,mMAAI,kBAAe,EAAE,CAAC;QACtD,IAAI,CAAC,uBAAuB,GAC1B,CAAA,KAAA,MAAM,CAAC,sBAAsB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,oMAAI,mBAAgB,EAAE,CAAC;QAC1D,IAAI,CAAC,mBAAmB,GACtB,CAAA,KAAA,MAAM,CAAC,kBAAkB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,mMAAI,kBAAe,EAAE,CAAC;QACrD,IAAI,CAAC,sBAAsB,GACzB,CAAA,KAAA,MAAM,CAAC,qBAAqB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,oMAAI,mBAAgB,EAAE,CAAC;IAC3D,CAAC;IAED,mBAAA,SAAA,CAAA,YAAY,GAAZ,SACE,OAAgB,EAChB,OAAe,EACf,QAAgB,EAChB,QAAkB,EAClB,UAA0B,EAC1B,KAAa;QAEb,IAAM,aAAa,wIAAG,SAAK,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QAEpD,IAAI,CAAC,aAAa,IAAI,2IAAC,qBAAA,AAAkB,EAAC,aAAa,CAAC,EAAE;YACxD,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAC5B,OAAO,EACP,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,UAAU,EACV,KAAK,CACN,CAAC;SACH;QAED,IAAI,aAAa,CAAC,QAAQ,EAAE;YAC1B,IAAI,aAAa,CAAC,UAAU,yIAAG,aAAU,CAAC,OAAO,EAAE;gBACjD,OAAO,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAC3C,OAAO,EACP,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,UAAU,EACV,KAAK,CACN,CAAC;aACH;YACD,OAAO,IAAI,CAAC,uBAAuB,CAAC,YAAY,CAC9C,OAAO,EACP,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,UAAU,EACV,KAAK,CACN,CAAC;SACH;QAED,IAAI,aAAa,CAAC,UAAU,yIAAG,aAAU,CAAC,OAAO,EAAE;YACjD,OAAO,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAC1C,OAAO,EACP,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,UAAU,EACV,KAAK,CACN,CAAC;SACH;QAED,OAAO,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAC7C,OAAO,EACP,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,UAAU,EACV,KAAK,CACN,CAAC;IACJ,CAAC;IAED,mBAAA,SAAA,CAAA,QAAQ,GAAR;QACE,OAAO,sBAAoB,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAA,2BAAyB,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,GAAA,8BAA4B,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,GAAA,0BAAwB,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,GAAA,6BAA2B,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,GAAA,GAAG,CAAC;IAClT,CAAC;IACH,OAAA,kBAAC;AAAD,CAAC,AA7FD,IA6FC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1977, "column": 0}, "map": {"version": 3, "file": "TraceIdRatioBasedSampler.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40opentelemetry/core/src/trace/sampler/TraceIdRatioBasedSampler.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Sampler,\n  SamplingDecision,\n  SamplingResult,\n  isValidTraceId,\n} from '@opentelemetry/api';\n\n/**\n * @deprecated Use the one defined in @opentelemetry/sdk-trace-base instead.\n * Sampler that samples a given fraction of traces based of trace id deterministically.\n */\nexport class TraceIdRatioBasedSampler implements Sampler {\n  private _upperBound: number;\n\n  constructor(private readonly _ratio: number = 0) {\n    this._ratio = this._normalize(_ratio);\n    this._upperBound = Math.floor(this._ratio * 0xffffffff);\n  }\n\n  shouldSample(context: unknown, traceId: string): SamplingResult {\n    return {\n      decision:\n        isValidTraceId(traceId) && this._accumulate(traceId) < this._upperBound\n          ? SamplingDecision.RECORD_AND_SAMPLED\n          : SamplingDecision.NOT_RECORD,\n    };\n  }\n\n  toString(): string {\n    return `TraceIdRatioBased{${this._ratio}}`;\n  }\n\n  private _normalize(ratio: number): number {\n    if (typeof ratio !== 'number' || isNaN(ratio)) return 0;\n    return ratio >= 1 ? 1 : ratio <= 0 ? 0 : ratio;\n  }\n\n  private _accumulate(traceId: string): number {\n    let accumulation = 0;\n    for (let i = 0; i < traceId.length / 8; i++) {\n      const pos = i * 8;\n      const part = parseInt(traceId.slice(pos, pos + 8), 16);\n      accumulation = (accumulation ^ part) >>> 0;\n    }\n    return accumulation;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAEL,gBAAgB,EAEhB,cAAc,GACf,MAAM,oBAAoB,CAAC;;AAE5B;;;GAGG,CACH,IAAA,2BAAA;IAGE,SAAA,yBAA6B,MAAkB;QAAlB,IAAA,WAAA,KAAA,GAAA;YAAA,SAAA,CAAkB;QAAA;QAAlB,IAAA,CAAA,MAAM,GAAN,MAAM,CAAY;QAC7C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QACtC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC,CAAC;IAC1D,CAAC;IAED,yBAAA,SAAA,CAAA,YAAY,GAAZ,SAAa,OAAgB,EAAE,OAAe;QAC5C,OAAO;YACL,QAAQ,4IACN,iBAAA,AAAc,EAAC,OAAO,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,WAAW,yIACnE,mBAAgB,CAAC,kBAAkB,yIACnC,mBAAgB,CAAC,UAAU;SAClC,CAAC;IACJ,CAAC;IAED,yBAAA,SAAA,CAAA,QAAQ,GAAR;QACE,OAAO,uBAAqB,IAAI,CAAC,MAAM,GAAA,GAAG,CAAC;IAC7C,CAAC;IAEO,yBAAA,SAAA,CAAA,UAAU,GAAlB,SAAmB,KAAa;QAC9B,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC;QACxD,OAAO,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IACjD,CAAC;IAEO,yBAAA,SAAA,CAAA,WAAW,GAAnB,SAAoB,OAAe;QACjC,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE;YAC3C,IAAM,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;YAClB,IAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YACvD,YAAY,GAAG,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;SAC5C;QACD,OAAO,YAAY,CAAC;IACtB,CAAC;IACH,OAAA,wBAAC;AAAD,CAAC,AAnCD,IAmCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2039, "column": 0}, "map": {"version": 3, "file": "lodash.merge.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40opentelemetry/core/src/utils/lodash.merge.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\n\n/**\n * based on lodash in order to support esm builds without esModuleInterop.\n * lodash is using MIT License.\n **/\n\nconst objectTag = '[object Object]';\nconst nullTag = '[object Null]';\nconst undefinedTag = '[object Undefined]';\nconst funcProto = Function.prototype;\nconst funcToString = funcProto.toString;\nconst objectCtorString = funcToString.call(Object);\nconst getPrototype = overArg(Object.getPrototypeOf, Object);\nconst objectProto = Object.prototype;\nconst hasOwnProperty = objectProto.hasOwnProperty;\nconst symToStringTag = Symbol ? Symbol.toStringTag : undefined;\nconst nativeObjectToString = objectProto.toString;\n\n/**\n * Creates a unary function that invokes `func` with its argument transformed.\n *\n * @private\n * @param {Function} func The function to wrap.\n * @param {Function} transform The argument transform.\n * @returns {Function} Returns the new function.\n */\nfunction overArg(func: Function, transform: any): any {\n  return function (arg: any) {\n    return func(transform(arg));\n  };\n}\n\n/**\n * Checks if `value` is a plain object, that is, an object created by the\n * `Object` constructor or one with a `[[Prototype]]` of `null`.\n *\n * @static\n * @memberOf _\n * @since 0.8.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a plain object, else `false`.\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n * }\n *\n * _.isPlainObject(new Foo);\n * // => false\n *\n * _.isPlainObject([1, 2, 3]);\n * // => false\n *\n * _.isPlainObject({ 'x': 0, 'y': 0 });\n * // => true\n *\n * _.isPlainObject(Object.create(null));\n * // => true\n */\nexport function isPlainObject(value: any) {\n  if (!isObjectLike(value) || baseGetTag(value) !== objectTag) {\n    return false;\n  }\n  const proto = getPrototype(value);\n  if (proto === null) {\n    return true;\n  }\n  const Ctor = hasOwnProperty.call(proto, 'constructor') && proto.constructor;\n  return (\n    typeof Ctor == 'function' &&\n    Ctor instanceof Ctor &&\n    funcToString.call(Ctor) === objectCtorString\n  );\n}\n\n/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */\nfunction isObjectLike(value: any) {\n  return value != null && typeof value == 'object';\n}\n\n/**\n * The base implementation of `getTag` without fallbacks for buggy environments.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the `toStringTag`.\n */\nfunction baseGetTag(value: any) {\n  if (value == null) {\n    return value === undefined ? undefinedTag : nullTag;\n  }\n  return symToStringTag && symToStringTag in Object(value)\n    ? getRawTag(value)\n    : objectToString(value);\n}\n\n/**\n * A specialized version of `baseGetTag` which ignores `Symbol.toStringTag` values.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the raw `toStringTag`.\n */\nfunction getRawTag(value: any) {\n  const isOwn = hasOwnProperty.call(value, symToStringTag as any),\n    tag = value[symToStringTag as any];\n  let unmasked = false;\n\n  try {\n    value[symToStringTag as any] = undefined;\n    unmasked = true;\n  } catch (e) {\n    // silence\n  }\n\n  const result = nativeObjectToString.call(value);\n  if (unmasked) {\n    if (isOwn) {\n      value[symToStringTag as any] = tag;\n    } else {\n      delete value[symToStringTag as any];\n    }\n  }\n  return result;\n}\n\n/**\n * Converts `value` to a string using `Object.prototype.toString`.\n *\n * @private\n * @param {*} value The value to convert.\n * @returns {string} Returns the converted string.\n */\nfunction objectToString(value: any) {\n  return nativeObjectToString.call(value);\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH,qDAAA,EAAuD,CAEvD;;;IAGI;;;AAEJ,IAAM,SAAS,GAAG,iBAAiB,CAAC;AACpC,IAAM,OAAO,GAAG,eAAe,CAAC;AAChC,IAAM,YAAY,GAAG,oBAAoB,CAAC;AAC1C,IAAM,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC;AACrC,IAAM,YAAY,GAAG,SAAS,CAAC,QAAQ,CAAC;AACxC,IAAM,gBAAgB,GAAG,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACnD,IAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;AAC5D,IAAM,WAAW,GAAG,MAAM,CAAC,SAAS,CAAC;AACrC,IAAM,cAAc,GAAG,WAAW,CAAC,cAAc,CAAC;AAClD,IAAM,cAAc,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC;AAC/D,IAAM,oBAAoB,GAAG,WAAW,CAAC,QAAQ,CAAC;AAElD;;;;;;;GAOG,CACH,SAAS,OAAO,CAAC,IAAc,EAAE,SAAc;IAC7C,OAAO,SAAU,GAAQ;QACvB,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC;AACJ,CAAC;AA8BK,SAAU,aAAa,CAAC,KAAU;IACtC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,SAAS,EAAE;QAC3D,OAAO,KAAK,CAAC;KACd;IACD,IAAM,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;IAClC,IAAI,KAAK,KAAK,IAAI,EAAE;QAClB,OAAO,IAAI,CAAC;KACb;IACD,IAAM,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC,IAAI,KAAK,CAAC,WAAW,CAAC;IAC5E,OAAO,AACL,OAAO,IAAI,IAAI,UAAU,IACzB,IAAI,YAAY,IAAI,IACpB,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,gBAAgB,CAC7C,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;GAuBG,CACH,SAAS,YAAY,CAAC,KAAU;IAC9B,OAAO,KAAK,IAAI,IAAI,IAAI,OAAO,KAAK,IAAI,QAAQ,CAAC;AACnD,CAAC;AAED;;;;;;GAMG,CACH,SAAS,UAAU,CAAC,KAAU;IAC5B,IAAI,KAAK,IAAI,IAAI,EAAE;QACjB,OAAO,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC;KACrD;IACD,OAAO,cAAc,IAAI,cAAc,IAAI,MAAM,CAAC,KAAK,CAAC,GACpD,SAAS,CAAC,KAAK,CAAC,GAChB,cAAc,CAAC,KAAK,CAAC,CAAC;AAC5B,CAAC;AAED;;;;;;GAMG,CACH,SAAS,SAAS,CAAC,KAAU;IAC3B,IAAM,KAAK,GAAG,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,cAAqB,CAAC,EAC7D,GAAG,GAAG,KAAK,CAAC,cAAqB,CAAC,CAAC;IACrC,IAAI,QAAQ,GAAG,KAAK,CAAC;IAErB,IAAI;QACF,KAAK,CAAC,cAAqB,CAAC,GAAG,SAAS,CAAC;QACzC,QAAQ,GAAG,IAAI,CAAC;KACjB,CAAC,OAAO,CAAC,EAAE;IACV,UAAU;KACX;IAED,IAAM,MAAM,GAAG,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAChD,IAAI,QAAQ,EAAE;QACZ,IAAI,KAAK,EAAE;YACT,KAAK,CAAC,cAAqB,CAAC,GAAG,GAAG,CAAC;SACpC,MAAM;YACL,OAAO,KAAK,CAAC,cAAqB,CAAC,CAAC;SACrC;KACF;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;;GAMG,CACH,SAAS,cAAc,CAAC,KAAU;IAChC,OAAO,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC1C,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2171, "column": 0}, "map": {"version": 3, "file": "merge.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40opentelemetry/core/src/utils/merge.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\n\nimport { isPlainObject } from './lodash.merge';\n\nconst MAX_LEVEL = 20;\n\ninterface ObjectInto {\n  obj: any;\n  key: string;\n}\n\n/**\n * Merges objects together\n * @param args - objects / values to be merged\n */\nexport function merge(...args: any[]): any {\n  let result: any = args.shift();\n  const objects: WeakMap<any, ObjectInto[]> | undefined = new WeakMap<\n    any,\n    ObjectInto[]\n  >();\n  while (args.length > 0) {\n    result = mergeTwoObjects(result, args.shift(), 0, objects);\n  }\n\n  return result;\n}\n\nfunction takeValue(value: any): any {\n  if (isArray(value)) {\n    return value.slice();\n  }\n  return value;\n}\n\n/**\n * Merges two objects\n * @param one - first object\n * @param two - second object\n * @param level - current deep level\n * @param objects - objects holder that has been already referenced - to prevent\n * cyclic dependency\n */\nfunction mergeTwoObjects(\n  one: any,\n  two: any,\n  level = 0,\n  objects: WeakMap<any, ObjectInto[]>\n): any {\n  let result: any;\n  if (level > MAX_LEVEL) {\n    return undefined;\n  }\n  level++;\n  if (isPrimitive(one) || isPrimitive(two) || isFunction(two)) {\n    result = takeValue(two);\n  } else if (isArray(one)) {\n    result = one.slice();\n    if (isArray(two)) {\n      for (let i = 0, j = two.length; i < j; i++) {\n        result.push(takeValue(two[i]));\n      }\n    } else if (isObject(two)) {\n      const keys = Object.keys(two);\n      for (let i = 0, j = keys.length; i < j; i++) {\n        const key = keys[i];\n        result[key] = takeValue(two[key]);\n      }\n    }\n  } else if (isObject(one)) {\n    if (isObject(two)) {\n      if (!shouldMerge(one, two)) {\n        return two;\n      }\n      result = Object.assign({}, one);\n      const keys = Object.keys(two);\n\n      for (let i = 0, j = keys.length; i < j; i++) {\n        const key = keys[i];\n        const twoValue = two[key];\n\n        if (isPrimitive(twoValue)) {\n          if (typeof twoValue === 'undefined') {\n            delete result[key];\n          } else {\n            // result[key] = takeValue(twoValue);\n            result[key] = twoValue;\n          }\n        } else {\n          const obj1 = result[key];\n          const obj2 = twoValue;\n\n          if (\n            wasObjectReferenced(one, key, objects) ||\n            wasObjectReferenced(two, key, objects)\n          ) {\n            delete result[key];\n          } else {\n            if (isObject(obj1) && isObject(obj2)) {\n              const arr1 = objects.get(obj1) || [];\n              const arr2 = objects.get(obj2) || [];\n              arr1.push({ obj: one, key });\n              arr2.push({ obj: two, key });\n              objects.set(obj1, arr1);\n              objects.set(obj2, arr2);\n            }\n\n            result[key] = mergeTwoObjects(\n              result[key],\n              twoValue,\n              level,\n              objects\n            );\n          }\n        }\n      }\n    } else {\n      result = two;\n    }\n  }\n\n  return result;\n}\n\n/**\n * Function to check if object has been already reference\n * @param obj\n * @param key\n * @param objects\n */\nfunction wasObjectReferenced(\n  obj: any,\n  key: string,\n  objects: WeakMap<any, ObjectInto[]>\n): boolean {\n  const arr = objects.get(obj[key]) || [];\n  for (let i = 0, j = arr.length; i < j; i++) {\n    const info = arr[i];\n    if (info.key === key && info.obj === obj) {\n      return true;\n    }\n  }\n  return false;\n}\n\nfunction isArray(value: any): boolean {\n  return Array.isArray(value);\n}\n\nfunction isFunction(value: any): boolean {\n  return typeof value === 'function';\n}\n\nfunction isObject(value: any): boolean {\n  return (\n    !isPrimitive(value) &&\n    !isArray(value) &&\n    !isFunction(value) &&\n    typeof value === 'object'\n  );\n}\n\nfunction isPrimitive(value: any): boolean {\n  return (\n    typeof value === 'string' ||\n    typeof value === 'number' ||\n    typeof value === 'boolean' ||\n    typeof value === 'undefined' ||\n    value instanceof Date ||\n    value instanceof RegExp ||\n    value === null\n  );\n}\n\nfunction shouldMerge(one: any, two: any): boolean {\n  if (!isPlainObject(one) || !isPlainObject(two)) {\n    return false;\n  }\n\n  return true;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH,qDAAA,EAAuD;;;AAEvD,OAAO,EAAE,aAAa,EAAE,MAAM,gBAAgB,CAAC;;AAE/C,IAAM,SAAS,GAAG,EAAE,CAAC;AAWf,SAAU,KAAK;IAAC,IAAA,OAAA,EAAA,CAAc;QAAd,IAAA,KAAA,CAAc,EAAd,KAAA,UAAA,MAAc,EAAd,IAAc,CAAA;QAAd,IAAA,CAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAc;;IAClC,IAAI,MAAM,GAAQ,IAAI,CAAC,KAAK,EAAE,CAAC;IAC/B,IAAM,OAAO,GAA2C,IAAI,OAAO,EAGhE,CAAC;IACJ,MAAO,IAAI,CAAC,MAAM,GAAG,CAAC,CAAE;QACtB,MAAM,GAAG,eAAe,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;KAC5D;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,SAAS,CAAC,KAAU;IAC3B,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE;QAClB,OAAO,KAAK,CAAC,KAAK,EAAE,CAAC;KACtB;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;;;;;GAOG,CACH,SAAS,eAAe,CACtB,GAAQ,EACR,GAAQ,EACR,KAAS,EACT,OAAmC;IADnC,IAAA,UAAA,KAAA,GAAA;QAAA,QAAA,CAAS;IAAA;IAGT,IAAI,MAAW,CAAC;IAChB,IAAI,KAAK,GAAG,SAAS,EAAE;QACrB,OAAO,SAAS,CAAC;KAClB;IACD,KAAK,EAAE,CAAC;IACR,IAAI,WAAW,CAAC,GAAG,CAAC,IAAI,WAAW,CAAC,GAAG,CAAC,IAAI,UAAU,CAAC,GAAG,CAAC,EAAE;QAC3D,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;KACzB,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,EAAE;QACvB,MAAM,GAAG,GAAG,CAAC,KAAK,EAAE,CAAC;QACrB,IAAI,OAAO,CAAC,GAAG,CAAC,EAAE;YAChB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE;gBAC1C,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aAChC;SACF,MAAM,IAAI,QAAQ,CAAC,GAAG,CAAC,EAAE;YACxB,IAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC9B,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE;gBAC3C,IAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gBACpB,MAAM,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;aACnC;SACF;KACF,MAAM,IAAI,QAAQ,CAAC,GAAG,CAAC,EAAE;QACxB,IAAI,QAAQ,CAAC,GAAG,CAAC,EAAE;YACjB,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE;gBAC1B,OAAO,GAAG,CAAC;aACZ;YACD,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAA,CAAE,EAAE,GAAG,CAAC,CAAC;YAChC,IAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAE9B,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE;gBAC3C,IAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gBACpB,IAAM,QAAQ,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;gBAE1B,IAAI,WAAW,CAAC,QAAQ,CAAC,EAAE;oBACzB,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;wBACnC,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;qBACpB,MAAM;wBACL,qCAAqC;wBACrC,MAAM,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC;qBACxB;iBACF,MAAM;oBACL,IAAM,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;oBACzB,IAAM,IAAI,GAAG,QAAQ,CAAC;oBAEtB,IACE,mBAAmB,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,IACtC,mBAAmB,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,EACtC;wBACA,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;qBACpB,MAAM;wBACL,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE;4BACpC,IAAM,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;4BACrC,IAAM,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;4BACrC,IAAI,CAAC,IAAI,CAAC;gCAAE,GAAG,EAAE,GAAG;gCAAE,GAAG,EAAA,GAAA;4BAAA,CAAE,CAAC,CAAC;4BAC7B,IAAI,CAAC,IAAI,CAAC;gCAAE,GAAG,EAAE,GAAG;gCAAE,GAAG,EAAA,GAAA;4BAAA,CAAE,CAAC,CAAC;4BAC7B,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;4BACxB,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;yBACzB;wBAED,MAAM,CAAC,GAAG,CAAC,GAAG,eAAe,CAC3B,MAAM,CAAC,GAAG,CAAC,EACX,QAAQ,EACR,KAAK,EACL,OAAO,CACR,CAAC;qBACH;iBACF;aACF;SACF,MAAM;YACL,MAAM,GAAG,GAAG,CAAC;SACd;KACF;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;GAKG,CACH,SAAS,mBAAmB,CAC1B,GAAQ,EACR,GAAW,EACX,OAAmC;IAEnC,IAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;IACxC,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE;QAC1C,IAAM,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;QACpB,IAAI,IAAI,CAAC,GAAG,KAAK,GAAG,IAAI,IAAI,CAAC,GAAG,KAAK,GAAG,EAAE;YACxC,OAAO,IAAI,CAAC;SACb;KACF;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,OAAO,CAAC,KAAU;IACzB,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC9B,CAAC;AAED,SAAS,UAAU,CAAC,KAAU;IAC5B,OAAO,OAAO,KAAK,KAAK,UAAU,CAAC;AACrC,CAAC;AAED,SAAS,QAAQ,CAAC,KAAU;IAC1B,OAAO,AACL,CAAC,WAAW,CAAC,KAAK,CAAC,IACnB,CAAC,OAAO,CAAC,KAAK,CAAC,IACf,CAAC,UAAU,CAAC,KAAK,CAAC,IAClB,OAAO,KAAK,KAAK,QAAQ,CAC1B,CAAC;AACJ,CAAC;AAED,SAAS,WAAW,CAAC,KAAU;IAC7B,OAAO,AACL,OAAO,KAAK,KAAK,QAAQ,IACzB,OAAO,KAAK,KAAK,QAAQ,IACzB,OAAO,KAAK,KAAK,SAAS,IAC1B,OAAO,KAAK,KAAK,WAAW,IAC5B,KAAK,YAAY,IAAI,IACrB,KAAK,YAAY,MAAM,IACvB,KAAK,KAAK,IAAI,CACf,CAAC;AACJ,CAAC;AAED,SAAS,WAAW,CAAC,GAAQ,EAAE,GAAQ;IACrC,IAAI,yLAAC,gBAAA,AAAa,EAAC,GAAG,CAAC,IAAI,yLAAC,gBAAA,AAAa,EAAC,GAAG,CAAC,EAAE;QAC9C,OAAO,KAAK,CAAC;KACd;IAED,OAAO,IAAI,CAAC;AACd,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2326, "column": 0}, "map": {"version": 3, "file": "timeout.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40opentelemetry/core/src/utils/timeout.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Error that is thrown on timeouts.\n */\nexport class TimeoutError extends Error {\n  constructor(message?: string) {\n    super(message);\n\n    // manually adjust prototype to retain `instanceof` functionality when targeting ES5, see:\n    // https://github.com/Microsoft/TypeScript-wiki/blob/main/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    Object.setPrototypeOf(this, TimeoutError.prototype);\n  }\n}\n\n/**\n * Adds a timeout to a promise and rejects if the specified timeout has elapsed. Also rejects if the specified promise\n * rejects, and resolves if the specified promise resolves.\n *\n * <p> NOTE: this operation will continue even after it throws a {@link TimeoutError}.\n *\n * @param promise promise to use with timeout.\n * @param timeout the timeout in milliseconds until the returned promise is rejected.\n */\nexport function callWithTimeout<T>(\n  promise: Promise<T>,\n  timeout: number\n): Promise<T> {\n  let timeoutHandle: ReturnType<typeof setTimeout>;\n\n  const timeoutPromise = new Promise<never>(function timeoutFunction(\n    _resolve,\n    reject\n  ) {\n    timeoutHandle = setTimeout(function timeoutHandler() {\n      reject(new TimeoutError('Operation timed out.'));\n    }, timeout);\n  });\n\n  return Promise.race([promise, timeoutPromise]).then(\n    result => {\n      clearTimeout(timeoutHandle);\n      return result;\n    },\n    reason => {\n      clearTimeout(timeoutHandle);\n      throw reason;\n    }\n  );\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;;;;;;;AAEH;;GAEG,CACH,IAAA,eAAA,SAAA,MAAA;IAAkC,UAAA,cAAA,QAAK;IACrC,SAAA,aAAY,OAAgB;QAA5B,IAAA,QACE,OAAA,IAAA,CAAA,IAAA,EAAM,OAAO,CAAC,IAAA,IAAA,CAKf;QAHC,0FAA0F;QAC1F,6IAA6I;QAC7I,MAAM,CAAC,cAAc,CAAC,KAAI,EAAE,YAAY,CAAC,SAAS,CAAC,CAAC;;IACtD,CAAC;IACH,OAAA,YAAC;AAAD,CAAC,AARD,CAAkC,KAAK,GAQtC;;AAWK,SAAU,eAAe,CAC7B,OAAmB,EACnB,OAAe;IAEf,IAAI,aAA4C,CAAC;IAEjD,IAAM,cAAc,GAAG,IAAI,OAAO,CAAQ,SAAS,eAAe,CAChE,QAAQ,EACR,MAAM;QAEN,aAAa,GAAG,UAAU,CAAC,SAAS,cAAc;YAChD,MAAM,CAAC,IAAI,YAAY,CAAC,sBAAsB,CAAC,CAAC,CAAC;QACnD,CAAC,EAAE,OAAO,CAAC,CAAC;IACd,CAAC,CAAC,CAAC;IAEH,OAAO,OAAO,CAAC,IAAI,CAAC;QAAC,OAAO;QAAE,cAAc;KAAC,CAAC,CAAC,IAAI,CACjD,SAAA,MAAM;QACJ,YAAY,CAAC,aAAa,CAAC,CAAC;QAC5B,OAAO,MAAM,CAAC;IAChB,CAAC,EACD,SAAA,MAAM;QACJ,YAAY,CAAC,aAAa,CAAC,CAAC;QAC5B,MAAM,MAAM,CAAC;IACf,CAAC,CACF,CAAC;AACJ,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2402, "column": 0}, "map": {"version": 3, "file": "url.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40opentelemetry/core/src/utils/url.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport function urlMatches(url: string, urlToMatch: string | RegExp): boolean {\n  if (typeof urlToMatch === 'string') {\n    return url === urlToMatch;\n  } else {\n    return !!url.match(urlToMatch);\n  }\n}\n/**\n * Check if {@param url} should be ignored when comparing against {@param ignoredUrls}\n * @param url\n * @param ignoredUrls\n */\nexport function isUrlIgnored(\n  url: string,\n  ignoredUrls?: Array<string | RegExp>\n): boolean {\n  if (!ignoredUrls) {\n    return false;\n  }\n\n  for (const ignoreUrl of ignoredUrls) {\n    if (urlMatches(url, ignoreUrl)) {\n      return true;\n    }\n  }\n  return false;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAeM,SAAU,UAAU,CAAC,GAAW,EAAE,UAA2B;IACjE,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;QAClC,OAAO,GAAG,KAAK,UAAU,CAAC;KAC3B,MAAM;QACL,OAAO,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;KAChC;AACH,CAAC;AAMK,SAAU,YAAY,CAC1B,GAAW,EACX,WAAoC;;IAEpC,IAAI,CAAC,WAAW,EAAE;QAChB,OAAO,KAAK,CAAC;KACd;;QAED,IAAwB,IAAA,gBAAA,SAAA,WAAW,CAAA,EAAA,kBAAA,cAAA,IAAA,EAAA,EAAA,CAAA,gBAAA,IAAA,EAAA,kBAAA,cAAA,IAAA,GAAE;YAAhC,IAAM,SAAS,GAAA,gBAAA,KAAA;YAClB,IAAI,UAAU,CAAC,GAAG,EAAE,SAAS,CAAC,EAAE;gBAC9B,OAAO,IAAI,CAAC;aACb;SACF;;;;;;;;;;;;IACD,OAAO,KAAK,CAAC;AACf,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2458, "column": 0}, "map": {"version": 3, "file": "wrap.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40opentelemetry/core/src/utils/wrap.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ShimWrapped } from '../common/types';\n\n/**\n * Checks if certain function has been already wrapped\n * @param func\n */\nexport function isWrapped(func: unknown): func is ShimWrapped {\n  return (\n    typeof func === 'function' &&\n    typeof (func as ShimWrapped).__original === 'function' &&\n    typeof (func as ShimWrapped).__unwrap === 'function' &&\n    (func as ShimWrapped).__wrapped === true\n  );\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAIH;;;GAGG;;;AACG,SAAU,SAAS,CAAC,IAAa;IACrC,OAAO,AACL,OAAO,IAAI,KAAK,UAAU,IAC1B,OAAQ,IAAoB,CAAC,UAAU,KAAK,UAAU,IACtD,OAAQ,IAAoB,CAAC,QAAQ,KAAK,UAAU,IACnD,IAAoB,CAAC,SAAS,KAAK,IAAI,CACzC,CAAC;AACJ,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2487, "column": 0}, "map": {"version": 3, "file": "promise.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40opentelemetry/core/src/utils/promise.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport class Deferred<T> {\n  private _promise: Promise<T>;\n  private _resolve!: (val: T) => void;\n  private _reject!: (error: unknown) => void;\n  constructor() {\n    this._promise = new Promise((resolve, reject) => {\n      this._resolve = resolve;\n      this._reject = reject;\n    });\n  }\n\n  get promise() {\n    return this._promise;\n  }\n\n  resolve(val: T) {\n    this._resolve(val);\n  }\n\n  reject(err: unknown) {\n    this._reject(err);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,IAAA,WAAA;IAIE,SAAA;QAAA,IAAA,QAAA,IAAA,CAKC;QAJC,IAAI,CAAC,QAAQ,GAAG,IAAI,OAAO,CAAC,SAAC,OAAO,EAAE,MAAM;YAC1C,KAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;YACxB,KAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACxB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,OAAA,cAAA,CAAI,SAAA,SAAA,EAAA,SAAO,EAAA;aAAX;YACE,OAAO,IAAI,CAAC,QAAQ,CAAC;QACvB,CAAC;;;OAAA;IAED,SAAA,SAAA,CAAA,OAAO,GAAP,SAAQ,GAAM;QACZ,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;IACrB,CAAC;IAED,SAAA,SAAA,CAAA,MAAM,GAAN,SAAO,GAAY;QACjB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IACpB,CAAC;IACH,OAAA,QAAC;AAAD,CAAC,AAtBD,IAsBC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2535, "column": 0}, "map": {"version": 3, "file": "callback.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40opentelemetry/core/src/utils/callback.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Deferred } from './promise';\n\n/**\n * Bind the callback and only invoke the callback once regardless how many times `BindOnceFuture.call` is invoked.\n */\nexport class BindOnceFuture<\n  R,\n  This = unknown,\n  T extends (this: This, ...args: unknown[]) => R = () => R,\n> {\n  private _isCalled = false;\n  private _deferred = new Deferred<R>();\n  constructor(\n    private _callback: T,\n    private _that: This\n  ) {}\n\n  get isCalled() {\n    return this._isCalled;\n  }\n\n  get promise() {\n    return this._deferred.promise;\n  }\n\n  call(...args: Parameters<T>): Promise<R> {\n    if (!this._isCalled) {\n      this._isCalled = true;\n      try {\n        Promise.resolve(this._callback.call(this._that, ...args)).then(\n          val => this._deferred.resolve(val),\n          err => this._deferred.reject(err)\n        );\n      } catch (err) {\n        this._deferred.reject(err);\n      }\n    }\n    return this._deferred.promise;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,QAAQ,EAAE,MAAM,WAAW,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAErC;;GAEG,CACH,IAAA,iBAAA;IAOE,SAAA,eACU,SAAY,EACZ,KAAW;QADX,IAAA,CAAA,SAAS,GAAT,SAAS,CAAG;QACZ,IAAA,CAAA,KAAK,GAAL,KAAK,CAAM;QAJb,IAAA,CAAA,SAAS,GAAG,KAAK,CAAC;QAClB,IAAA,CAAA,SAAS,GAAG,IAAI,uLAAQ,EAAK,CAAC;IAInC,CAAC;IAEJ,OAAA,cAAA,CAAI,eAAA,SAAA,EAAA,UAAQ,EAAA;aAAZ;YACE,OAAO,IAAI,CAAC,SAAS,CAAC;QACxB,CAAC;;;OAAA;IAED,OAAA,cAAA,CAAI,eAAA,SAAA,EAAA,SAAO,EAAA;aAAX;YACE,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;QAChC,CAAC;;;OAAA;IAED,eAAA,SAAA,CAAA,IAAI,GAAJ;;QAAA,IAAA,QAAA,IAAA,CAaC;QAbI,IAAA,OAAA,EAAA,CAAsB;YAAtB,IAAA,KAAA,CAAsB,EAAtB,KAAA,UAAA,MAAsB,EAAtB,IAAsB,CAAA;YAAtB,IAAA,CAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAsB;;QACzB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI;gBACF,OAAO,CAAC,OAAO,CAAC,CAAA,KAAA,IAAI,CAAC,SAAS,CAAA,CAAC,IAAI,CAAA,KAAA,CAAA,IAAA,cAAA;oBAAC,IAAI,CAAC,KAAK;iBAAA,EAAA,OAAK,IAAI,GAAA,QAAE,CAAC,IAAI,CAC5D,SAAA,GAAG;oBAAI,OAAA,KAAI,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC;gBAA3B,CAA2B,EAClC,SAAA,GAAG;oBAAI,OAAA,KAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC;gBAA1B,CAA0B,CAClC,CAAC;aACH,CAAC,OAAO,GAAG,EAAE;gBACZ,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;aAC5B;SACF;QACD,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;IAChC,CAAC;IACH,OAAA,cAAC;AAAD,CAAC,AAlCD,IAkCC", "ignoreList": [0], "debugId": null}}]}