{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/layout/providers.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/providers.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/providers.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuS,GACpU,qEACA", "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/layout/providers.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/providers.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/providers.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmR,GAChT,iDACA", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/sonner.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sonner.tsx <module evaluation>\",\n    \"Toaster\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,8DACA", "debugId": null}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/sonner.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sonner.tsx\",\n    \"Toaster\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,0CACA", "debugId": null}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/quicksand_55ab6c05.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"quicksand_55ab6c05-module__fWnvEa__className\",\n  \"variable\": \"quicksand_55ab6c05-module__fWnvEa__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/quicksand_55ab6c05.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22font.ts%22,%22import%22:%22Quicksand%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22variable%22:%22--font-sans%22}],%22variableName%22:%22fontSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Quicksand', 'Quicksand Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,yJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,yJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,yJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_f40efb7e.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_mono_f40efb7e-module__9VzD3G__className\",\n  \"variable\": \"geist_mono_f40efb7e-module__9VzD3G__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_f40efb7e.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22font.ts%22,%22import%22:%22Geist_Mono%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22variable%22:%22--font-mono%22}],%22variableName%22:%22fontMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist Mono', 'Geist Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,0JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,0JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,0JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 144, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/instrument_sans_ea57aed0.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"instrument_sans_ea57aed0-module__mJf4-q__className\",\n  \"variable\": \"instrument_sans_ea57aed0-module__mJf4-q__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/instrument_sans_ea57aed0.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22font.ts%22,%22import%22:%22Instrument_Sans%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22variable%22:%22--font-instrument%22}],%22variableName%22:%22fontInstrument%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Instrument Sans', 'Instrument Sans Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,+JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,+JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,+JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 175, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/noto_sans_mono_ac878e45.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"noto_sans_mono_ac878e45-module__SfBW4G__className\",\n  \"variable\": \"noto_sans_mono_ac878e45-module__SfBW4G__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 185, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/noto_sans_mono_ac878e45.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22font.ts%22,%22import%22:%22Noto_Sans_Mono%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22variable%22:%22--font-noto-mono%22}],%22variableName%22:%22fontNotoMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Noto Sans Mono', 'Noto Sans Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,8JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,8JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,8JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 206, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/mulish_d412d3cf.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"mulish_d412d3cf-module__oKc9Da__className\",\n  \"variable\": \"mulish_d412d3cf-module__oKc9Da__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 216, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/mulish_d412d3cf.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22font.ts%22,%22import%22:%22Mulish%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22variable%22:%22--font-mullish%22}],%22variableName%22:%22fontMullish%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Mulish', 'Mulish Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,sJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,sJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,sJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 237, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_707c4bd4.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_707c4bd4-module__7ihonW__className\",\n  \"variable\": \"inter_707c4bd4-module__7ihonW__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 247, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_707c4bd4.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22font.ts%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22variable%22:%22--font-inter%22}],%22variableName%22:%22fontInter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 269, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\r\nimport { twMerge } from 'tailwind-merge';\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\nexport function formatBytes(\r\n  bytes: number,\r\n  opts: {\r\n    decimals?: number;\r\n    sizeType?: 'accurate' | 'normal';\r\n  } = {}\r\n) {\r\n  const { decimals = 0, sizeType = 'normal' } = opts;\r\n\r\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\r\n  const accurateSizes = ['Bytes', 'KiB', 'MiB', 'GiB', 'TiB'];\r\n  if (bytes === 0) return '0 Byte';\r\n  const i = Math.floor(Math.log(bytes) / Math.log(1024));\r\n  return `${(bytes / Math.pow(1024, i)).toFixed(decimals)} ${\r\n    sizeType === 'accurate'\r\n      ? (accurateSizes[i] ?? 'Bytest')\r\n      : (sizes[i] ?? 'Bytes')\r\n  }`;\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YACd,KAAa,EACb,OAGI,CAAC,CAAC;IAEN,MAAM,EAAE,WAAW,CAAC,EAAE,WAAW,QAAQ,EAAE,GAAG;IAE9C,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;KAAK;IAC/C,MAAM,gBAAgB;QAAC;QAAS;QAAO;QAAO;QAAO;KAAM;IAC3D,IAAI,UAAU,GAAG,OAAO;IACxB,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAChD,OAAO,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,EAAE,EAAE,OAAO,CAAC,UAAU,CAAC,EACvD,aAAa,aACR,aAAa,CAAC,EAAE,IAAI,WACpB,KAAK,CAAC,EAAE,IAAI,SACjB;AACJ", "debugId": null}}, {"offset": {"line": 306, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/lib/font.ts"], "sourcesContent": ["import {\r\n  <PERSON>eist_Mono,\r\n  Instrument_Sans,\r\n  Inter,\r\n  Mulish,\r\n  Noto_Sans_Mono,\r\n  Quicksand\r\n} from 'next/font/google';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst fontSans = Quicksand({\r\n  subsets: ['latin'],\r\n  variable: '--font-sans'\r\n});\r\n\r\nconst fontMono = Geist_Mono({\r\n  subsets: ['latin'],\r\n  variable: '--font-mono'\r\n});\r\n\r\nconst fontInstrument = Instrument_Sans({\r\n  subsets: ['latin'],\r\n  variable: '--font-instrument'\r\n});\r\n\r\nconst fontNotoMono = Noto_Sans_Mono({\r\n  subsets: ['latin'],\r\n  variable: '--font-noto-mono'\r\n});\r\n\r\nconst fontMullish = Mulish({\r\n  subsets: ['latin'],\r\n  variable: '--font-mullish'\r\n});\r\n\r\nconst fontInter = Inter({\r\n  subsets: ['latin'],\r\n  variable: '--font-inter'\r\n});\r\n\r\nexport const fontVariables = cn(\r\n  fontSans.variable,\r\n  fontMono.variable,\r\n  fontInstrument.variable,\r\n  fontNotoMono.variable,\r\n  fontMullish.variable,\r\n  fontInter.variable\r\n);\r\n"], "names": [], "mappings": ";;;;;;;;;AASA;;;;;;;;AAgCO,MAAM,gBAAgB,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAC5B,6IAAA,CAAA,UAAQ,CAAC,QAAQ,EACjB,8IAAA,CAAA,UAAQ,CAAC,QAAQ,EACjB,mJAAA,CAAA,UAAc,CAAC,QAAQ,EACvB,kJAAA,CAAA,UAAY,CAAC,QAAQ,EACrB,0IAAA,CAAA,UAAW,CAAC,QAAQ,EACpB,yIAAA,CAAA,UAAS,CAAC,QAAQ", "debugId": null}}, {"offset": {"line": 330, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/layout/ThemeToggle/theme-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/ThemeToggle/theme-provider.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/ThemeToggle/theme-provider.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwT,GACrV,sFACA", "debugId": null}}, {"offset": {"line": 344, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/layout/ThemeToggle/theme-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/ThemeToggle/theme-provider.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/ThemeToggle/theme-provider.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 358, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 368, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/app/layout.tsx"], "sourcesContent": ["import Providers from '@/components/layout/providers';\r\nimport { Toaster } from '@/components/ui/sonner';\r\nimport { fontVariables } from '@/lib/font';\r\nimport ThemeProvider from '@/components/layout/ThemeToggle/theme-provider';\r\nimport { cn } from '@/lib/utils';\r\nimport type { Metadata, Viewport } from 'next';\r\nimport { cookies } from 'next/headers';\r\nimport NextTopLoader from 'nextjs-toploader';\r\nimport { NuqsAdapter } from 'nuqs/adapters/next/app';\r\nimport './globals.css';\r\nimport './theme.css';\r\n\r\nconst META_THEME_COLORS = {\r\n  light: '#ffffff',\r\n  dark: '#09090b'\r\n};\r\n\r\nexport const metadata: Metadata = {\r\n  title: 'Akademi IAI',\r\n  description: 'LMS Sertifikasi Profesional'\r\n};\r\n\r\nexport const viewport: Viewport = {\r\n  themeColor: META_THEME_COLORS.light\r\n};\r\n\r\nexport default async function RootLayout({\r\n  children\r\n}: {\r\n  children: React.ReactNode;\r\n}) {\r\n  const cookieStore = await cookies();\r\n  const activeThemeValue = cookieStore.get('active_theme')?.value;\r\n  const isScaled = activeThemeValue?.endsWith('-scaled');\r\n\r\n  return (\r\n    <html lang='en' suppressHydrationWarning>\r\n      <head />\r\n      <body\r\n        className={cn(\r\n          'bg-background font-sans antialiased',\r\n          activeThemeValue ? `theme-${activeThemeValue}` : '',\r\n          isScaled ? 'theme-scaled' : '',\r\n          fontVariables\r\n        )}\r\n      >\r\n        <NextTopLoader showSpinner={false} />\r\n        <NuqsAdapter>\r\n          <ThemeProvider\r\n            attribute='class'\r\n            defaultTheme='light'\r\n            enableSystem\r\n            disableTransitionOnChange\r\n            enableColorScheme\r\n          >\r\n            <Providers activeThemeValue={activeThemeValue as string}>\r\n              <Toaster />\r\n              {children}\r\n            </Providers>\r\n          </ThemeProvider>\r\n        </NuqsAdapter>\r\n      </body>\r\n    </html>\r\n  );\r\n}"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;;;;;;;;;;;;AAIA,MAAM,oBAAoB;IACxB,OAAO;IACP,MAAM;AACR;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEO,MAAM,WAAqB;IAChC,YAAY,kBAAkB,KAAK;AACrC;AAEe,eAAe,WAAW,EACvC,QAAQ,EAGT;IACC,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,mBAAmB,YAAY,GAAG,CAAC,iBAAiB;IAC1D,MAAM,WAAW,kBAAkB,SAAS;IAE5C,qBACE,8OAAC;QAAK,MAAK;QAAK,wBAAwB;;0BACtC,8OAAC;;;;;0BACD,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uCACA,mBAAmB,CAAC,MAAM,EAAE,kBAAkB,GAAG,IACjD,WAAW,iBAAiB,IAC5B,kHAAA,CAAA,gBAAa;;kCAGf,8OAAC,oJAAA,CAAA,UAAa;wBAAC,aAAa;;;;;;kCAC5B,8OAAC,uJAAA,CAAA,cAAW;kCACV,cAAA,8OAAC,gKAAA,CAAA,UAAa;4BACZ,WAAU;4BACV,cAAa;4BACb,YAAY;4BACZ,yBAAyB;4BACzB,iBAAiB;sCAEjB,cAAA,8OAAC,yIAAA,CAAA,UAAS;gCAAC,kBAAkB;;kDAC3B,8OAAC,kIAAA,CAAA,UAAO;;;;;oCACP;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}]}