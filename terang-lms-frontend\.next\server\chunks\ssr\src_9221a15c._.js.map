{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\r\nimport { twMerge } from 'tailwind-merge';\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\nexport function formatBytes(\r\n  bytes: number,\r\n  opts: {\r\n    decimals?: number;\r\n    sizeType?: 'accurate' | 'normal';\r\n  } = {}\r\n) {\r\n  const { decimals = 0, sizeType = 'normal' } = opts;\r\n\r\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\r\n  const accurateSizes = ['Bytes', 'KiB', 'MiB', 'GiB', 'TiB'];\r\n  if (bytes === 0) return '0 Byte';\r\n  const i = Math.floor(Math.log(bytes) / Math.log(1024));\r\n  return `${(bytes / Math.pow(1024, i)).toFixed(decimals)} ${\r\n    sizeType === 'accurate'\r\n      ? (accurateSizes[i] ?? 'Bytest')\r\n      : (sizes[i] ?? 'Bytes')\r\n  }`;\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YACd,KAAa,EACb,OAGI,CAAC,CAAC;IAEN,MAAM,EAAE,WAAW,CAAC,EAAE,WAAW,QAAQ,EAAE,GAAG;IAE9C,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;KAAK;IAC/C,MAAM,gBAAgB;QAAC;QAAS;QAAO;QAAO;QAAO;KAAM;IAC3D,IAAI,UAAU,GAAG,OAAO;IACxB,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAChD,OAAO,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,EAAE,EAAE,OAAO,CAAC,UAAU,CAAC,EACvD,aAAa,aACR,aAAa,CAAC,EAAE,IAAI,WACpB,KAAK,CAAC,EAAE,IAAI,SACjB;AACJ", "debugId": null}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { cva, type VariantProps } from 'class-variance-authority';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',\r\n        destructive:\r\n          'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\r\n        outline:\r\n          'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50',\r\n        secondary:\r\n          'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',\r\n        ghost:\r\n          'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',\r\n        link: 'text-primary underline-offset-4 hover:underline',\r\n        iai: 'bg-[var(--iai-primary)] text-white shadow-xs hover:bg-[var(--iai-secondary)] focus-visible:ring-[var(--iai-primary)]/20',\r\n        'iai-outline': 'border border-[var(--iai-primary)] text-[var(--iai-primary)] bg-background shadow-xs hover:bg-[var(--iai-primary)] hover:text-white'\r\n      },\r\n      size: {\r\n        default: 'h-9 px-4 py-2 has-[>svg]:px-3',\r\n        sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',\r\n        lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',\r\n        icon: 'size-9'\r\n      }\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default',\r\n      size: 'default'\r\n    }\r\n  }\r\n);\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<'button'> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean;\r\n  }) {\r\n  const Comp = asChild ? Slot : 'button';\r\n\r\n  return (\r\n    <Comp\r\n      data-slot='button'\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,8cACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;YACN,KAAK;YACL,eAAe;QACjB;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Progress = React.forwardRef<\r\n  React.ElementRef<typeof ProgressPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>\r\n>(({ className, value, ...props }, ref) => (\r\n  <ProgressPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative h-4 w-full overflow-hidden rounded-full bg-secondary\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ProgressPrimitive.Indicator\r\n      className=\"h-full w-full flex-1 bg-primary transition-all\"\r\n      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\r\n    />\r\n  </ProgressPrimitive.Root>\r\n))\r\nProgress.displayName = ProgressPrimitive.Root.displayName\r\n\r\nexport { Progress }"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,oKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIhE,SAAS,WAAW,GAAG,oKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='card'\r\n      className={cn(\r\n        'bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='card-header'\r\n      className={cn(\r\n        '@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='card-title'\r\n      className={cn('leading-none font-semibold', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='card-description'\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='card-action'\r\n      className={cn(\r\n        'col-start-2 row-span-2 row-start-1 self-start justify-self-end',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='card-content'\r\n      className={cn('px-6', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='card-footer'\r\n      className={cn('flex items-center px-6 [.border-t]:pt-6', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 239, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/lms/final-exam/option.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { ContentBlock } from '@/components/dynamic-content-editor';\r\n\r\ninterface OptionProps {\r\n  option: string | { content: ContentBlock[]; isCorrect: boolean };\r\n  index: number;\r\n  questionId: string;\r\n  selectedAnswer: number | string | number[] | undefined;\r\n  onAnswerChange: (questionId: string, answer: number | string | number[]) => void;\r\n  type: 'radio' | 'checkbox';\r\n  disabled?: boolean;\r\n  showResults?: boolean;\r\n  correctAnswer?: number | string | number[];\r\n  isCorrect?: boolean;\r\n}\r\n\r\nexport const Option: React.FC<OptionProps> = ({\r\n  option,\r\n  index,\r\n  questionId,\r\n  selectedAnswer,\r\n  onAnswerChange,\r\n  type = 'radio',\r\n  disabled = false,\r\n  showResults = false,\r\n  correctAnswer,\r\n  isCorrect\r\n}) => {\r\n  const isSelected = type === 'radio' \r\n    ? selectedAnswer === index \r\n    : Array.isArray(selectedAnswer) && selectedAnswer.includes(index);\r\n\r\n  const isCorrectOption = type === 'radio' \r\n    ? correctAnswer === index \r\n    : Array.isArray(correctAnswer) && correctAnswer.includes(index);\r\n\r\n  const handleChange = () => {\r\n    if (disabled) return;\r\n    \r\n    if (type === 'radio') {\r\n      onAnswerChange(questionId, index);\r\n    } else {\r\n      // Handle checkbox logic for multiple selection\r\n      const currentAnswers = Array.isArray(selectedAnswer) ? selectedAnswer : [];\r\n      const newAnswers = isSelected \r\n        ? currentAnswers.filter((ans) => ans !== index)\r\n        : [...currentAnswers, index];\r\n      onAnswerChange(questionId, newAnswers);\r\n    }\r\n  };\r\n\r\n  const getOptionStyles = () => {\r\n    if (!showResults) {\r\n      // Normal mode - just show selected state\r\n      return isSelected \r\n        ? 'border-blue-500 bg-blue-50' \r\n        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50';\r\n    }\r\n\r\n    // Results mode - show correct/incorrect highlighting\r\n    if (isCorrectOption) {\r\n      // This is the correct answer - always highlight green\r\n      return 'border-green-500 bg-green-50';\r\n    } else if (isSelected) {\r\n      // User selected this wrong answer - highlight red\r\n      return 'border-red-500 bg-red-50';\r\n    } else {\r\n      // Not selected, not correct - neutral\r\n      return 'border-gray-200 bg-gray-50';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <label \r\n      className={`\r\n        flex cursor-pointer items-start space-x-3 p-3 rounded-lg border-2 transition-all\r\n        ${getOptionStyles()}\r\n        ${disabled ? 'opacity-50 cursor-not-allowed' : ''}\r\n        ${showResults && isCorrectOption ? 'ring-2 ring-green-200' : ''}\r\n        ${showResults && isSelected && !isCorrectOption ? 'ring-2 ring-red-200' : ''}\r\n      `}\r\n    >\r\n      <input\r\n        type={type}\r\n        name={questionId}\r\n        value={index}\r\n        checked={isSelected}\r\n        onChange={handleChange}\r\n        disabled={disabled}\r\n        className={`\r\n          mt-0.5 h-4 w-4 shrink-0\r\n          ${type === 'radio' ? 'text-blue-600' : 'text-blue-600 rounded'}\r\n          ${disabled ? 'cursor-not-allowed' : 'cursor-pointer'}\r\n        `}\r\n      />\r\n      <span className={`text-sm leading-relaxed flex-1 ${disabled ? 'text-gray-500' : 'text-gray-900'}`}>\r\n        <span className=\"font-medium mr-2\">\r\n          {String.fromCharCode(65 + index)}.\r\n        </span>\r\n        {typeof option === 'string' ? (\r\n          option\r\n        ) : (\r\n          option.content.map((block, blockIndex) => (\r\n            <React.Fragment key={blockIndex}>\r\n              {block.type === 'text' && <span>{block.value}</span>}\r\n              {block.type === 'image' && block.value && (\r\n                <img src={block.value} alt={`Option image ${blockIndex}`} className=\"inline-block max-h-8 object-contain ml-1\" />\r\n              )}\r\n              {block.type === 'video' && <span>[Video: {block.value}]</span>}\r\n              {block.type === 'pdf' && <span>[PDF: {block.value}]</span>}\r\n              {block.type === 'zoom-recording' && <span>[Recording: {block.value}]</span>}\r\n            </React.Fragment>\r\n          ))\r\n        )}\r\n      </span>\r\n      \r\n      {/* Show correct/incorrect indicators in results mode */}\r\n      {showResults && (\r\n        <div className=\"flex items-center ml-2\">\r\n          {isCorrectOption && (\r\n            <span className=\"text-green-600 font-medium text-xs bg-green-100 px-2 py-1 rounded-full\">\r\n              ✓ Benar\r\n            </span>\r\n          )}\r\n          {isSelected && !isCorrectOption && (\r\n            <span className=\"text-red-600 font-medium text-xs bg-red-100 px-2 py-1 rounded-full\">\r\n              ✗ Salah\r\n            </span>\r\n          )}\r\n        </div>\r\n      )}\r\n    </label>\r\n  );\r\n};"], "names": [], "mappings": ";;;;AAAA;;;AAgBO,MAAM,SAAgC,CAAC,EAC5C,MAAM,EACN,KAAK,EACL,UAAU,EACV,cAAc,EACd,cAAc,EACd,OAAO,OAAO,EACd,WAAW,KAAK,EAChB,cAAc,KAAK,EACnB,aAAa,EACb,SAAS,EACV;IACC,MAAM,aAAa,SAAS,UACxB,mBAAmB,QACnB,MAAM,OAAO,CAAC,mBAAmB,eAAe,QAAQ,CAAC;IAE7D,MAAM,kBAAkB,SAAS,UAC7B,kBAAkB,QAClB,MAAM,OAAO,CAAC,kBAAkB,cAAc,QAAQ,CAAC;IAE3D,MAAM,eAAe;QACnB,IAAI,UAAU;QAEd,IAAI,SAAS,SAAS;YACpB,eAAe,YAAY;QAC7B,OAAO;YACL,+CAA+C;YAC/C,MAAM,iBAAiB,MAAM,OAAO,CAAC,kBAAkB,iBAAiB,EAAE;YAC1E,MAAM,aAAa,aACf,eAAe,MAAM,CAAC,CAAC,MAAQ,QAAQ,SACvC;mBAAI;gBAAgB;aAAM;YAC9B,eAAe,YAAY;QAC7B;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,aAAa;YAChB,yCAAyC;YACzC,OAAO,aACH,+BACA;QACN;QAEA,qDAAqD;QACrD,IAAI,iBAAiB;YACnB,sDAAsD;YACtD,OAAO;QACT,OAAO,IAAI,YAAY;YACrB,kDAAkD;YAClD,OAAO;QACT,OAAO;YACL,sCAAsC;YACtC,OAAO;QACT;IACF;IAEA,qBACE,8OAAC;QACC,WAAW,CAAC;;QAEV,EAAE,kBAAkB;QACpB,EAAE,WAAW,kCAAkC,GAAG;QAClD,EAAE,eAAe,kBAAkB,0BAA0B,GAAG;QAChE,EAAE,eAAe,cAAc,CAAC,kBAAkB,wBAAwB,GAAG;MAC/E,CAAC;;0BAED,8OAAC;gBACC,MAAM;gBACN,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,UAAU;gBACV,UAAU;gBACV,WAAW,CAAC;;UAEV,EAAE,SAAS,UAAU,kBAAkB,wBAAwB;UAC/D,EAAE,WAAW,uBAAuB,iBAAiB;QACvD,CAAC;;;;;;0BAEH,8OAAC;gBAAK,WAAW,CAAC,+BAA+B,EAAE,WAAW,kBAAkB,iBAAiB;;kCAC/F,8OAAC;wBAAK,WAAU;;4BACb,OAAO,YAAY,CAAC,KAAK;4BAAO;;;;;;;oBAElC,OAAO,WAAW,WACjB,SAEA,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO,2BACzB,8OAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ;;gCACZ,MAAM,IAAI,KAAK,wBAAU,8OAAC;8CAAM,MAAM,KAAK;;;;;;gCAC3C,MAAM,IAAI,KAAK,WAAW,MAAM,KAAK,kBACpC,8OAAC;oCAAI,KAAK,MAAM,KAAK;oCAAE,KAAK,CAAC,aAAa,EAAE,YAAY;oCAAE,WAAU;;;;;;gCAErE,MAAM,IAAI,KAAK,yBAAW,8OAAC;;wCAAK;wCAAS,MAAM,KAAK;wCAAC;;;;;;;gCACrD,MAAM,IAAI,KAAK,uBAAS,8OAAC;;wCAAK;wCAAO,MAAM,KAAK;wCAAC;;;;;;;gCACjD,MAAM,IAAI,KAAK,kCAAoB,8OAAC;;wCAAK;wCAAa,MAAM,KAAK;wCAAC;;;;;;;;2BAPhD;;;;;;;;;;;YAc1B,6BACC,8OAAC;gBAAI,WAAU;;oBACZ,iCACC,8OAAC;wBAAK,WAAU;kCAAyE;;;;;;oBAI1F,cAAc,CAAC,iCACd,8OAAC;wBAAK,WAAU;kCAAqE;;;;;;;;;;;;;;;;;;AAQjG", "debugId": null}}, {"offset": {"line": 421, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from 'react';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { cva, type VariantProps } from 'class-variance-authority';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst badgeVariants = cva(\r\n  'inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden',\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          'border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90',\r\n        secondary:\r\n          'border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90',\r\n        destructive:\r\n          'border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\r\n        outline:\r\n          'text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground'\r\n      }\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default'\r\n    }\r\n  }\r\n);\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<'span'> &\r\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : 'span';\r\n\r\n  return (\r\n    <Comp\r\n      data-slot='badge'\r\n      className={cn(badgeVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Badge, badgeVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 467, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/lms/final-exam/question.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { Card, CardContent } from '@/components/ui/card';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Option } from './option';\r\nimport { Question as QuestionType } from '@/types/lms';\r\n\r\ninterface QuestionProps {\r\n  question: QuestionType;\r\n  questionNumber: number;\r\n  totalQuestions: number;\r\n  selectedAnswer: number | string | number[] | undefined;\r\n  onAnswerChange: (questionId: string, answer: number | string | number[]) => void;\r\n  showResults?: boolean;\r\n  isCorrect?: boolean;\r\n  disabled?: boolean;\r\n}\r\n\r\nexport const Question: React.FC<QuestionProps> = ({\r\n  question,\r\n  questionNumber,\r\n  totalQuestions,\r\n  selectedAnswer,\r\n  onAnswerChange,\r\n  showResults = false,\r\n  isCorrect,\r\n  disabled = false\r\n}) => {\r\n  const getQuestionTypeLabel = (type: string) => {\r\n    switch (type) {\r\n      case 'multiple-choice':\r\n      case 'multiple_choice':\r\n        return 'Pilihan Ganda';\r\n      case 'true-false':\r\n      case 'true_false':\r\n        return 'Benar/Salah';\r\n      case 'essay':\r\n        return 'Esai';\r\n      default:\r\n        return type;\r\n    }\r\n  };\r\n\r\n  const handleEssayChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {\r\n    if (!disabled) {\r\n      onAnswerChange(question.id, e.target.value);\r\n    }\r\n  };\r\n\r\n  const handleTrueFalseChange = (value: string) => {\r\n    if (!disabled) {\r\n      onAnswerChange(question.id, value);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Card className={`\r\n      border-2 transition-all\r\n      ${showResults \r\n        ? (isCorrect ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50')\r\n        : 'border-gray-200'\r\n      }\r\n    `}>\r\n      <CardContent className=\"p-6\">\r\n        <div className=\"mb-4 flex items-center justify-between\">\r\n          <div className=\"flex items-center space-x-3\">\r\n            <Badge variant=\"outline\" className=\"bg-blue-50 text-blue-700 border-blue-200\">\r\n              Soal {questionNumber} dari {totalQuestions}\r\n            </Badge>\r\n            <Badge variant=\"secondary\" className=\"text-xs\">\r\n              {getQuestionTypeLabel(question.type)}\r\n            </Badge>\r\n          </div>\r\n          {showResults && (\r\n            <Badge \r\n              variant={isCorrect ? 'default' : 'destructive'}\r\n              className={isCorrect ? 'bg-green-600 hover:bg-green-700' : ''}\r\n            >\r\n              {isCorrect ? 'Benar' : 'Salah'}\r\n            </Badge>\r\n          )}\r\n        </div>\r\n\r\n        <div className=\"mb-6\">\r\n          <p className=\"text-lg leading-relaxed text-gray-900 whitespace-pre-wrap\">\r\n            {typeof question.question === 'string' ? (\r\n              question.question\r\n            ) : Array.isArray(question.question) ? (\r\n              question.question.map((block, index) => (\r\n                <React.Fragment key={index}>\r\n                  {block.type === 'text' && <span>{block.value}</span>}\r\n                  {block.type === 'image' && block.value && (\r\n                    <img src={block.value} alt={`Question image ${index}`} className=\"inline-block max-h-16 object-contain ml-2\" />\r\n                  )}\r\n                </React.Fragment>\r\n              ))\r\n            ) : (\r\n              <span>{String(question.question)}</span>\r\n            )}\r\n          </p>\r\n        </div>\r\n\r\n        {/* Multiple Choice Options */}\r\n        {(question.type === 'multiple-choice' || question.type === 'multiple_choice') && question.options && (\r\n          <div className=\"space-y-3\">\r\n            {question.options.map((option, index) => (\r\n              <Option\r\n                key={index}\r\n                option={option}\r\n                index={index}\r\n                questionId={question.id}\r\n                selectedAnswer={selectedAnswer}\r\n                onAnswerChange={onAnswerChange}\r\n                type=\"radio\"\r\n                disabled={disabled}\r\n                showResults={showResults}\r\n                correctAnswer={question.correctAnswer}\r\n                isCorrect={isCorrect}\r\n              />\r\n            ))}\r\n          </div>\r\n        )}\r\n\r\n        {/* True/False Options */}\r\n        {(question.type === 'true-false' || question.type === 'true_false') && (\r\n          <div className=\"space-y-3\">\r\n            {['true', 'false'].map((value, index) => {\r\n              const isSelected = selectedAnswer === value;\r\n              const isCorrectOption = question.correctAnswer === value;\r\n              \r\n              const getOptionStyles = () => {\r\n                if (!showResults) {\r\n                  return isSelected \r\n                    ? 'border-blue-500 bg-blue-50' \r\n                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50';\r\n                }\r\n                \r\n                if (isCorrectOption) {\r\n                  return 'border-green-500 bg-green-50';\r\n                } else if (isSelected) {\r\n                  return 'border-red-500 bg-red-50';\r\n                } else {\r\n                  return 'border-gray-200 bg-gray-50';\r\n                }\r\n              };\r\n              \r\n              return (\r\n                <label \r\n                  key={value}\r\n                  className={`\r\n                    flex cursor-pointer items-center justify-between p-3 rounded-lg border-2 transition-all\r\n                    ${getOptionStyles()}\r\n                    ${disabled ? 'opacity-50 cursor-not-allowed' : ''}\r\n                    ${showResults && isCorrectOption ? 'ring-2 ring-green-200' : ''}\r\n                    ${showResults && isSelected && !isCorrectOption ? 'ring-2 ring-red-200' : ''}\r\n                  `}\r\n                >\r\n                  <div className=\"flex items-center space-x-3\">\r\n                    <input\r\n                      type=\"radio\"\r\n                      name={question.id}\r\n                      value={value}\r\n                      checked={isSelected}\r\n                      onChange={() => handleTrueFalseChange(value)}\r\n                      disabled={disabled}\r\n                      className=\"h-4 w-4 text-blue-600\"\r\n                    />\r\n                    <span className={`font-medium ${disabled ? 'text-gray-500' : 'text-gray-900'}`}>\r\n                      {String.fromCharCode(65 + index)}. {value === 'true' ? 'Benar' : 'Salah'}\r\n                    </span>\r\n                  </div>\r\n                  \r\n                  {/* Show correct/incorrect indicators in results mode */}\r\n                  {showResults && (\r\n                    <div className=\"flex items-center\">\r\n                      {isCorrectOption && (\r\n                        <span className=\"text-green-600 font-medium text-xs bg-green-100 px-2 py-1 rounded-full\">\r\n                          ✓ Benar\r\n                        </span>\r\n                      )}\r\n                      {isSelected && !isCorrectOption && (\r\n                        <span className=\"text-red-600 font-medium text-xs bg-red-100 px-2 py-1 rounded-full\">\r\n                          ✗ Salah\r\n                        </span>\r\n                      )}\r\n                    </div>\r\n                  )}\r\n                </label>\r\n              );\r\n            })}\r\n          </div>\r\n        )}\r\n\r\n        {/* Essay Question */}\r\n        {question.type === 'essay' && (\r\n          <textarea\r\n            className={`\r\n              w-full resize-none rounded-lg border-2 p-4 focus:border-transparent focus:ring-2 focus:ring-blue-500\r\n              ${disabled ? 'bg-gray-50 cursor-not-allowed' : ''}\r\n            `}\r\n            rows={8}\r\n            placeholder=\"Ketik jawaban Anda di sini...\"\r\n            value={selectedAnswer as string || ''}\r\n            onChange={handleEssayChange}\r\n            disabled={disabled}\r\n          />\r\n        )}\r\n\r\n        {/* Show explanation in results */}\r\n        {showResults && question.explanation && (\r\n          <div className=\"mt-4 p-4 bg-gray-50 rounded-lg border-l-4 border-blue-500\">\r\n            <h4 className=\"font-semibold text-gray-900 mb-2\">Penjelasan:</h4>\r\n            <p className=\"text-sm text-gray-700 leading-relaxed\">\r\n              {typeof question.explanation === 'string' ? (\r\n                question.explanation\r\n              ) : Array.isArray(question.explanation) ? (\r\n                question.explanation.map((block, index) => (\r\n                  <React.Fragment key={index}>\r\n                    {block.type === 'text' && <span>{block.value}</span>}\r\n                    {block.type === 'image' && block.value && (\r\n                      <img src={block.value} alt={`Explanation image ${index}`} className=\"inline-block max-h-16 object-contain ml-2\" />\r\n                    )}\r\n                    {block.type === 'video' && <span>[Video: {block.value}]</span>}\r\n                    {block.type === 'pdf' && <span>[PDF: {block.value}]</span>}\r\n                    {block.type === 'zoom-recording' && <span>[Recording: {block.value}]</span>}\r\n                  </React.Fragment>\r\n                ))\r\n              ) : (\r\n                <span>{String(question.explanation)}</span>\r\n              )}\r\n            </p>\r\n          </div>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAcO,MAAM,WAAoC,CAAC,EAChD,QAAQ,EACR,cAAc,EACd,cAAc,EACd,cAAc,EACd,cAAc,EACd,cAAc,KAAK,EACnB,SAAS,EACT,WAAW,KAAK,EACjB;IACC,MAAM,uBAAuB,CAAC;QAC5B,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,CAAC,UAAU;YACb,eAAe,SAAS,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;QAC5C;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,IAAI,CAAC,UAAU;YACb,eAAe,SAAS,EAAE,EAAE;QAC9B;IACF;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW,CAAC;;MAEhB,EAAE,cACG,YAAY,iCAAiC,6BAC9C,kBACH;IACH,CAAC;kBACC,cAAA,8OAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;;8BACrB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAU;;wCAA2C;wCACtE;wCAAe;wCAAO;;;;;;;8CAE9B,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;8CAClC,qBAAqB,SAAS,IAAI;;;;;;;;;;;;wBAGtC,6BACC,8OAAC,iIAAA,CAAA,QAAK;4BACJ,SAAS,YAAY,YAAY;4BACjC,WAAW,YAAY,oCAAoC;sCAE1D,YAAY,UAAU;;;;;;;;;;;;8BAK7B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCACV,OAAO,SAAS,QAAQ,KAAK,WAC5B,SAAS,QAAQ,GACf,MAAM,OAAO,CAAC,SAAS,QAAQ,IACjC,SAAS,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC5B,8OAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ;;oCACZ,MAAM,IAAI,KAAK,wBAAU,8OAAC;kDAAM,MAAM,KAAK;;;;;;oCAC3C,MAAM,IAAI,KAAK,WAAW,MAAM,KAAK,kBACpC,8OAAC;wCAAI,KAAK,MAAM,KAAK;wCAAE,KAAK,CAAC,eAAe,EAAE,OAAO;wCAAE,WAAU;;;;;;;+BAHhD;;;;sDAQvB,8OAAC;sCAAM,OAAO,SAAS,QAAQ;;;;;;;;;;;;;;;;gBAMpC,CAAC,SAAS,IAAI,KAAK,qBAAqB,SAAS,IAAI,KAAK,iBAAiB,KAAK,SAAS,OAAO,kBAC/F,8OAAC;oBAAI,WAAU;8BACZ,SAAS,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC7B,8OAAC,oJAAA,CAAA,SAAM;4BAEL,QAAQ;4BACR,OAAO;4BACP,YAAY,SAAS,EAAE;4BACvB,gBAAgB;4BAChB,gBAAgB;4BAChB,MAAK;4BACL,UAAU;4BACV,aAAa;4BACb,eAAe,SAAS,aAAa;4BACrC,WAAW;2BAVN;;;;;;;;;;gBAiBZ,CAAC,SAAS,IAAI,KAAK,gBAAgB,SAAS,IAAI,KAAK,YAAY,mBAChE,8OAAC;oBAAI,WAAU;8BACZ;wBAAC;wBAAQ;qBAAQ,CAAC,GAAG,CAAC,CAAC,OAAO;wBAC7B,MAAM,aAAa,mBAAmB;wBACtC,MAAM,kBAAkB,SAAS,aAAa,KAAK;wBAEnD,MAAM,kBAAkB;4BACtB,IAAI,CAAC,aAAa;gCAChB,OAAO,aACH,+BACA;4BACN;4BAEA,IAAI,iBAAiB;gCACnB,OAAO;4BACT,OAAO,IAAI,YAAY;gCACrB,OAAO;4BACT,OAAO;gCACL,OAAO;4BACT;wBACF;wBAEA,qBACE,8OAAC;4BAEC,WAAW,CAAC;;oBAEV,EAAE,kBAAkB;oBACpB,EAAE,WAAW,kCAAkC,GAAG;oBAClD,EAAE,eAAe,kBAAkB,0BAA0B,GAAG;oBAChE,EAAE,eAAe,cAAc,CAAC,kBAAkB,wBAAwB,GAAG;kBAC/E,CAAC;;8CAED,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,MAAM,SAAS,EAAE;4CACjB,OAAO;4CACP,SAAS;4CACT,UAAU,IAAM,sBAAsB;4CACtC,UAAU;4CACV,WAAU;;;;;;sDAEZ,8OAAC;4CAAK,WAAW,CAAC,YAAY,EAAE,WAAW,kBAAkB,iBAAiB;;gDAC3E,OAAO,YAAY,CAAC,KAAK;gDAAO;gDAAG,UAAU,SAAS,UAAU;;;;;;;;;;;;;gCAKpE,6BACC,8OAAC;oCAAI,WAAU;;wCACZ,iCACC,8OAAC;4CAAK,WAAU;sDAAyE;;;;;;wCAI1F,cAAc,CAAC,iCACd,8OAAC;4CAAK,WAAU;sDAAqE;;;;;;;;;;;;;2BAjCtF;;;;;oBAyCX;;;;;;gBAKH,SAAS,IAAI,KAAK,yBACjB,8OAAC;oBACC,WAAW,CAAC;;cAEV,EAAE,WAAW,kCAAkC,GAAG;YACpD,CAAC;oBACD,MAAM;oBACN,aAAY;oBACZ,OAAO,kBAA4B;oBACnC,UAAU;oBACV,UAAU;;;;;;gBAKb,eAAe,SAAS,WAAW,kBAClC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,8OAAC;4BAAE,WAAU;sCACV,OAAO,SAAS,WAAW,KAAK,WAC/B,SAAS,WAAW,GAClB,MAAM,OAAO,CAAC,SAAS,WAAW,IACpC,SAAS,WAAW,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC/B,8OAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ;;wCACZ,MAAM,IAAI,KAAK,wBAAU,8OAAC;sDAAM,MAAM,KAAK;;;;;;wCAC3C,MAAM,IAAI,KAAK,WAAW,MAAM,KAAK,kBACpC,8OAAC;4CAAI,KAAK,MAAM,KAAK;4CAAE,KAAK,CAAC,kBAAkB,EAAE,OAAO;4CAAE,WAAU;;;;;;wCAErE,MAAM,IAAI,KAAK,yBAAW,8OAAC;;gDAAK;gDAAS,MAAM,KAAK;gDAAC;;;;;;;wCACrD,MAAM,IAAI,KAAK,uBAAS,8OAAC;;gDAAK;gDAAO,MAAM,KAAK;gDAAC;;;;;;;wCACjD,MAAM,IAAI,KAAK,kCAAoB,8OAAC;;gDAAK;gDAAa,MAAM,KAAK;gDAAC;;;;;;;;mCAPhD;;;;0DAWvB,8OAAC;0CAAM,OAAO,SAAS,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQlD", "debugId": null}}, {"offset": {"line": 850, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/lms/final-exam/question-bank.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Question } from '@/types/lms';\r\n\r\ninterface QuestionBankProps {\r\n  questions: Question[];\r\n  currentQuestion: number;\r\n  answeredQuestions: Set<number>;\r\n  onQuestionSelect: (questionIndex: number) => void;\r\n  flaggedQuestions?: Set<number>;\r\n  onToggleFlag?: (questionIndex: number) => void;\r\n  showFlags?: boolean;\r\n  onSubmit?: () => void;\r\n  canSubmit?: boolean;\r\n  isSubmitting?: boolean;\r\n  reviewMode?: boolean;\r\n  results?: { [key: string]: boolean };\r\n}\r\n\r\nexport const QuestionBank: React.FC<QuestionBankProps> = ({\r\n  questions,\r\n  currentQuestion,\r\n  answeredQuestions,\r\n  onQuestionSelect,\r\n  flaggedQuestions = new Set(),\r\n  onToggleFlag,\r\n  showFlags = true,\r\n  onSubmit,\r\n  canSubmit = false,\r\n  isSubmitting = false,\r\n  reviewMode = false,\r\n  results = {}\r\n}) => {\r\n  const getQuestionStatus = (index: number) => {\r\n    if (reviewMode) {\r\n      const question = questions[index];\r\n      const isCorrect = results[question.id];\r\n      if (index === currentQuestion) {\r\n        return isCorrect ? 'current-correct' : 'current-incorrect';\r\n      }\r\n      return isCorrect ? 'correct' : 'incorrect';\r\n    }\r\n    \r\n    if (index === currentQuestion) return 'current';\r\n    if (answeredQuestions.has(index)) return 'answered';\r\n    return 'unanswered';\r\n  };\r\n\r\n  const getStatusColor = (status: string) => {\r\n    switch (status) {\r\n      case 'current':\r\n        return 'bg-blue-600 text-white border-blue-600';\r\n      case 'current-correct':\r\n        return 'bg-green-600 text-white border-green-600 ring-2 ring-green-200';\r\n      case 'current-incorrect':\r\n        return 'bg-red-600 text-white border-red-600 ring-2 ring-red-200';\r\n      case 'correct':\r\n        return 'bg-green-100 text-green-800 border-green-300 hover:bg-green-200';\r\n      case 'incorrect':\r\n        return 'bg-red-100 text-red-800 border-red-300 hover:bg-red-200';\r\n      case 'answered':\r\n        return 'bg-green-100 text-green-800 border-green-300 hover:bg-green-200';\r\n      case 'unanswered':\r\n        return 'bg-gray-50 text-gray-600 border-gray-300 hover:bg-gray-100';\r\n      default:\r\n        return 'bg-gray-50 text-gray-600 border-gray-300';\r\n    }\r\n  };\r\n\r\n  const answeredCount = answeredQuestions.size;\r\n  const unansweredCount = questions.length - answeredCount;\r\n  \r\n  // Count correct/incorrect in review mode\r\n  const correctCount = reviewMode ? questions.filter(q => results[q.id]).length : 0;\r\n  const incorrectCount = reviewMode ? questions.filter(q => !results[q.id]).length : 0;\r\n\r\n  return (\r\n    <Card className=\"h-fit sticky top-4\">\r\n      <CardHeader className=\"pb-4\">\r\n        <CardTitle className=\"text-lg\">{reviewMode ? 'Review Soal' : 'Nomor Soal'}</CardTitle>\r\n        {reviewMode ? (\r\n          <div className=\"grid grid-cols-2 gap-4 text-sm\">\r\n            <div className=\"flex items-center space-x-2\">\r\n              <div className=\"w-4 h-4 bg-green-100 border border-green-300 rounded\"></div>\r\n              <span>Benar: {correctCount}</span>\r\n            </div>\r\n            <div className=\"flex items-center space-x-2\">\r\n              <div className=\"w-4 h-4 bg-red-100 border border-red-300 rounded\"></div>\r\n              <span>Salah: {incorrectCount}</span>\r\n            </div>\r\n          </div>\r\n        ) : (\r\n          <div className=\"grid grid-cols-2 gap-4 text-sm\">\r\n            <div className=\"flex items-center space-x-2\">\r\n              <div className=\"w-4 h-4 bg-green-100 border border-green-300 rounded\"></div>\r\n              <span>Terjawab: {answeredCount}</span>\r\n            </div>\r\n            <div className=\"flex items-center space-x-2\">\r\n              <div className=\"w-4 h-4 bg-gray-50 border border-gray-300 rounded\"></div>\r\n              <span>Belum: {unansweredCount}</span>\r\n            </div>\r\n          </div>\r\n        )}\r\n        {showFlags && flaggedQuestions.size > 0 && (\r\n          <div className=\"flex items-center space-x-2 text-sm\">\r\n            <div className=\"w-4 h-4 bg-yellow-100 border border-yellow-400 rounded relative\">\r\n              <div className=\"absolute -top-1 -right-1 w-2 h-2 bg-yellow-500 rounded-full\"></div>\r\n            </div>\r\n            <span>Ditandai: {flaggedQuestions.size}</span>\r\n          </div>\r\n        )}\r\n      </CardHeader>\r\n      \r\n      <CardContent className=\"space-y-4\">\r\n        {/* Question Grid */}\r\n        <div className=\"grid grid-cols-4 gap-2\">\r\n          {questions.map((_, index) => {\r\n            const status = getQuestionStatus(index);\r\n            const isFlagged = flaggedQuestions.has(index);\r\n            \r\n            return (\r\n              <Button\r\n                key={index}\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n                className={`\r\n                  relative h-12 w-12 p-0 font-medium transition-all\r\n                  ${getStatusColor(status)}\r\n                  ${isFlagged ? 'ring-2 ring-yellow-400' : ''}\r\n                `}\r\n                onClick={() => onQuestionSelect(index)}\r\n              >\r\n                {index + 1}\r\n                {isFlagged && (\r\n                  <div className=\"absolute -top-1 -right-1 w-3 h-3 bg-yellow-500 rounded-full border-2 border-white\"></div>\r\n                )}\r\n              </Button>\r\n            );\r\n          })}\r\n        </div>\r\n\r\n        {/* Legend */}\r\n        <div className=\"pt-4 border-t space-y-2 text-xs text-gray-600\">\r\n          {reviewMode ? (\r\n            <>\r\n              <div className=\"flex items-center space-x-2\">\r\n                <div className=\"w-3 h-3 bg-green-600 rounded\"></div>\r\n                <span>Soal saat ini (Benar)</span>\r\n              </div>\r\n              <div className=\"flex items-center space-x-2\">\r\n                <div className=\"w-3 h-3 bg-red-600 rounded\"></div>\r\n                <span>Soal saat ini (Salah)</span>\r\n              </div>\r\n              <div className=\"flex items-center space-x-2\">\r\n                <div className=\"w-3 h-3 bg-green-100 border border-green-300 rounded\"></div>\r\n                <span>Jawaban benar</span>\r\n              </div>\r\n              <div className=\"flex items-center space-x-2\">\r\n                <div className=\"w-3 h-3 bg-red-100 border border-red-300 rounded\"></div>\r\n                <span>Jawaban salah</span>\r\n              </div>\r\n            </>\r\n          ) : (\r\n            <>\r\n              <div className=\"flex items-center space-x-2\">\r\n                <div className=\"w-3 h-3 bg-blue-600 rounded\"></div>\r\n                <span>Soal saat ini</span>\r\n              </div>\r\n              <div className=\"flex items-center space-x-2\">\r\n                <div className=\"w-3 h-3 bg-green-100 border border-green-300 rounded\"></div>\r\n                <span>Sudah dijawab</span>\r\n              </div>\r\n              <div className=\"flex items-center space-x-2\">\r\n                <div className=\"w-3 h-3 bg-gray-50 border border-gray-300 rounded\"></div>\r\n                <span>Belum dijawab</span>\r\n              </div>\r\n            </>\r\n          )}\r\n          {showFlags && (\r\n            <div className=\"flex items-center space-x-2\">\r\n              <div className=\"w-3 h-3 bg-yellow-100 border border-yellow-400 rounded relative\">\r\n                <div className=\"absolute -top-0.5 -right-0.5 w-1.5 h-1.5 bg-yellow-500 rounded-full\"></div>\r\n              </div>\r\n              <span>Ditandai untuk review</span>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Quick Actions */}\r\n        {showFlags && onToggleFlag && (\r\n          <div className=\"pt-4 border-t\">\r\n            <Button\r\n              variant=\"outline\"\r\n              size=\"sm\"\r\n              className=\"w-full text-xs\"\r\n              onClick={() => onToggleFlag(currentQuestion)}\r\n            >\r\n              {flaggedQuestions.has(currentQuestion) ? 'Hapus Tanda' : 'Tandai Soal'}\r\n            </Button>\r\n          </div>\r\n        )}\r\n\r\n        {/* Submit Button */}\r\n        {onSubmit && (\r\n          <div className=\"pt-4 border-t\">\r\n            <Button\r\n              onClick={onSubmit}\r\n              disabled={!canSubmit || isSubmitting}\r\n              className=\"w-full bg-blue-600 hover:bg-blue-700 text-white\"\r\n              size=\"lg\"\r\n            >\r\n              {isSubmitting ? 'Menyerahkan...' : 'Submit Ujian'}\r\n            </Button>\r\n          </div>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};"], "names": [], "mappings": ";;;;AACA;AAEA;;;;AAkBO,MAAM,eAA4C,CAAC,EACxD,SAAS,EACT,eAAe,EACf,iBAAiB,EACjB,gBAAgB,EAChB,mBAAmB,IAAI,KAAK,EAC5B,YAAY,EACZ,YAAY,IAAI,EAChB,QAAQ,EACR,YAAY,KAAK,EACjB,eAAe,KAAK,EACpB,aAAa,KAAK,EAClB,UAAU,CAAC,CAAC,EACb;IACC,MAAM,oBAAoB,CAAC;QACzB,IAAI,YAAY;YACd,MAAM,WAAW,SAAS,CAAC,MAAM;YACjC,MAAM,YAAY,OAAO,CAAC,SAAS,EAAE,CAAC;YACtC,IAAI,UAAU,iBAAiB;gBAC7B,OAAO,YAAY,oBAAoB;YACzC;YACA,OAAO,YAAY,YAAY;QACjC;QAEA,IAAI,UAAU,iBAAiB,OAAO;QACtC,IAAI,kBAAkB,GAAG,CAAC,QAAQ,OAAO;QACzC,OAAO;IACT;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,kBAAkB,IAAI;IAC5C,MAAM,kBAAkB,UAAU,MAAM,GAAG;IAE3C,yCAAyC;IACzC,MAAM,eAAe,aAAa,UAAU,MAAM,CAAC,CAAA,IAAK,OAAO,CAAC,EAAE,EAAE,CAAC,EAAE,MAAM,GAAG;IAChF,MAAM,iBAAiB,aAAa,UAAU,MAAM,CAAC,CAAA,IAAK,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,EAAE,MAAM,GAAG;IAEnF,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;kCAAW,aAAa,gBAAgB;;;;;;oBAC5D,2BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;;4CAAK;4CAAQ;;;;;;;;;;;;;0CAEhB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;;4CAAK;4CAAQ;;;;;;;;;;;;;;;;;;6CAIlB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;;4CAAK;4CAAW;;;;;;;;;;;;;0CAEnB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;;4CAAK;4CAAQ;;;;;;;;;;;;;;;;;;;oBAInB,aAAa,iBAAiB,IAAI,GAAG,mBACpC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;;;;;;;;;;0CAEjB,8OAAC;;oCAAK;oCAAW,iBAAiB,IAAI;;;;;;;;;;;;;;;;;;;0BAK5C,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,8OAAC;wBAAI,WAAU;kCACZ,UAAU,GAAG,CAAC,CAAC,GAAG;4BACjB,MAAM,SAAS,kBAAkB;4BACjC,MAAM,YAAY,iBAAiB,GAAG,CAAC;4BAEvC,qBACE,8OAAC,kIAAA,CAAA,SAAM;gCAEL,SAAQ;gCACR,MAAK;gCACL,WAAW,CAAC;;kBAEV,EAAE,eAAe,QAAQ;kBACzB,EAAE,YAAY,2BAA2B,GAAG;gBAC9C,CAAC;gCACD,SAAS,IAAM,iBAAiB;;oCAE/B,QAAQ;oCACR,2BACC,8OAAC;wCAAI,WAAU;;;;;;;+BAZZ;;;;;wBAgBX;;;;;;kCAIF,8OAAC;wBAAI,WAAU;;4BACZ,2BACC;;kDACE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;0DAAK;;;;;;;;;;;;;6DAIV;;kDACE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;0DAAK;;;;;;;;;;;;;;4BAIX,2BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;;;;;;;;;;kDAEjB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;oBAMX,aAAa,8BACZ,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,aAAa;sCAE3B,iBAAiB,GAAG,CAAC,mBAAmB,gBAAgB;;;;;;;;;;;oBAM9D,0BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,CAAC,aAAa;4BACxB,WAAU;4BACV,MAAK;sCAEJ,eAAe,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;AAOjD", "debugId": null}}, {"offset": {"line": 1372, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/lms/final-exam/index.ts"], "sourcesContent": ["export { Option } from './option';\r\nexport { Question } from './question';\r\nexport { QuestionBank } from './question-bank';"], "names": [], "mappings": ";AAAA;AACA;AACA", "debugId": null}}, {"offset": {"line": 1396, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/app/%28course-view%29/my-courses/%5BcourseId%5D/exam/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useCallback } from 'react';\r\nimport { useParams, useRouter, useSearchParams } from 'next/navigation';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Progress } from '@/components/ui/progress';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { \r\n  Clock01Icon as ClockIcon, \r\n  Flag01Icon as FlagIcon, \r\n  Alert01Icon as AlertTriangleIcon, \r\n  CheckmarkCircle01Icon as CheckCircle2Icon,\r\n  Cancel01Icon as XCircleIcon,\r\n  Award01Icon as TrophyIcon,\r\n  Rotate01Icon as RotateCcwIcon,\r\n  ArrowLeft01Icon as ArrowLeftIcon,\r\n  Home01Icon as HomeIcon\r\n} from 'hugeicons-react';\r\nimport { Course, Quiz } from '@/types/lms';\r\nimport { useEnrollment } from '@/contexts/enrollment-context';\r\nimport { Question, QuestionBank } from '@/components/lms/final-exam';\r\n\r\nconst ExamPage: React.FC = () => {\r\n  const params = useParams();\r\n  const router = useRouter();\r\n  const searchParams = useSearchParams();\r\n  const courseId = params.courseId as string;\r\n  const examType = searchParams.get('type') || 'final'; // 'final', 'chapter', 'module'\r\n  const examId = searchParams.get('examId');\r\n\r\n  const { courseData, updateCourseProgress } = useEnrollment();\r\n\r\n  const [currentQuestion, setCurrentQuestion] = useState(0);\r\n  const [answers, setAnswers] = useState<{ [key: string]: any }>({});\r\n  const [timeLeft, setTimeLeft] = useState<number | null>(null);\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [showConfirmSubmit, setShowConfirmSubmit] = useState(false);\r\n  const [flaggedQuestions, setFlaggedQuestions] = useState<Set<number>>(new Set());\r\n  const [showResults, setShowResults] = useState(false);\r\n  const [examResults, setExamResults] = useState<{\r\n    score: number;\r\n    correctAnswers: number;\r\n    totalQuestions: number;\r\n    results: { [key: string]: boolean };\r\n  } | null>(null);\r\n  const [examStarted, setExamStarted] = useState(false);\r\n  const [showConfirmStart, setShowConfirmStart] = useState(true);\r\n  const [timeWarnings, setTimeWarnings] = useState({\r\n    fifteenMin: false,\r\n    fiveMin: false,\r\n    oneMin: false\r\n  });\r\n  const [showTimeWarning, setShowTimeWarning] = useState<{\r\n    show: boolean;\r\n    message: string;\r\n    type: 'warning' | 'critical';\r\n  }>({ show: false, message: '', type: 'warning' });\r\n\r\n  // Get the current exam/quiz\r\n  const getCurrentExam = (): Quiz | null => {\r\n    if (examType === 'final') {\r\n      return courseData.finalExam;\r\n    }\r\n    \r\n    // Find chapter or module quiz by examId\r\n    for (const courseModule of courseData.modules) {\r\n      if (courseModule.moduleQuiz.id === examId) {\r\n        return courseModule.moduleQuiz;\r\n      }\r\n      for (const chapter of courseModule.chapters) {\r\n        if (chapter.quiz.id === examId) {\r\n          return chapter.quiz;\r\n        }\r\n      }\r\n    }\r\n    return null;\r\n  };\r\n\r\n  const currentExam = getCurrentExam();\r\n\r\n  // Initialize timer when exam starts\r\n  useEffect(() => {\r\n    if (examStarted && currentExam?.timeLimit && timeLeft === null) {\r\n      setTimeLeft(currentExam.timeLimit * 60);\r\n    }\r\n  }, [examStarted, currentExam, timeLeft]);\r\n\r\n  // Timer countdown with warnings\r\n  useEffect(() => {\r\n    if (examStarted && timeLeft !== null && timeLeft > 0 && !showResults) {\r\n      const timer = setInterval(() => {\r\n        setTimeLeft((prev) => {\r\n          if (prev === null || prev <= 1) {\r\n            handleTimeUp();\r\n            return 0;\r\n          }\r\n          \r\n          const newTime = prev - 1;\r\n          \r\n          // Time warnings with notifications\r\n          if (newTime === 900 && !timeWarnings.fifteenMin) { // 15 minutes\r\n            setTimeWarnings(prev => ({ ...prev, fifteenMin: true }));\r\n            setShowTimeWarning({\r\n              show: true,\r\n              message: 'Peringatan: Sisa waktu 15 menit!',\r\n              type: 'warning'\r\n            });\r\n            setTimeout(() => setShowTimeWarning(prev => ({ ...prev, show: false })), 5000);\r\n          }\r\n          if (newTime === 300 && !timeWarnings.fiveMin) { // 5 minutes\r\n            setTimeWarnings(prev => ({ ...prev, fiveMin: true }));\r\n            setShowTimeWarning({\r\n              show: true,\r\n              message: 'Peringatan: Sisa waktu 5 menit!',\r\n              type: 'warning'\r\n            });\r\n            setTimeout(() => setShowTimeWarning(prev => ({ ...prev, show: false })), 5000);\r\n          }\r\n          if (newTime === 60 && !timeWarnings.oneMin) { // 1 minute\r\n            setTimeWarnings(prev => ({ ...prev, oneMin: true }));\r\n            setShowTimeWarning({\r\n              show: true,\r\n              message: 'PERINGATAN KRITIS: Sisa waktu 1 menit!',\r\n              type: 'critical'\r\n            });\r\n            setTimeout(() => setShowTimeWarning(prev => ({ ...prev, show: false })), 8000);\r\n          }\r\n          \r\n          return newTime;\r\n        });\r\n      }, 1000);\r\n      return () => clearInterval(timer);\r\n    }\r\n  }, [examStarted, timeLeft, showResults, timeWarnings]);\r\n\r\n  // Prevent navigation during exam\r\n  useEffect(() => {\r\n    if (examStarted && !showResults) {\r\n      const handleBeforeUnload = (e: BeforeUnloadEvent) => {\r\n        e.preventDefault();\r\n        e.returnValue = '';\r\n        return '';\r\n      };\r\n\r\n      window.addEventListener('beforeunload', handleBeforeUnload);\r\n      return () => window.removeEventListener('beforeunload', handleBeforeUnload);\r\n    }\r\n  }, [examStarted, showResults]);\r\n\r\n  const formatTime = (seconds: number) => {\r\n    const hours = Math.floor(seconds / 3600);\r\n    const mins = Math.floor((seconds % 3600) / 60);\r\n    const secs = seconds % 60;\r\n    \r\n    if (hours > 0) {\r\n      return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\r\n    }\r\n    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\r\n  };\r\n\r\n  const getTimeStatus = (seconds: number) => {\r\n    if (!currentExam?.timeLimit) return 'normal';\r\n    const totalTime = currentExam.timeLimit * 60;\r\n    const remaining = seconds / totalTime;\r\n    \r\n    if (remaining <= 0.1) return 'critical'; // Less than 10%\r\n    if (remaining <= 0.25) return 'warning'; // Less than 25%\r\n    return 'normal';\r\n  };\r\n\r\n  const getTimeColor = (status: string) => {\r\n    switch (status) {\r\n      case 'critical':\r\n        return 'border-red-500 text-red-600 bg-red-50';\r\n      case 'warning':\r\n        return 'border-amber-500 text-amber-600 bg-amber-50';\r\n      default:\r\n        return 'border-blue-500 text-blue-600 bg-blue-50';\r\n    }\r\n  };\r\n\r\n  const handleStartExam = () => {\r\n    setExamStarted(true);\r\n    setShowConfirmStart(false);\r\n    if (currentExam?.timeLimit) {\r\n      setTimeLeft(currentExam.timeLimit * 60);\r\n    }\r\n  };\r\n\r\n  const handleAnswerChange = (questionId: string, answer: any) => {\r\n    setAnswers((prev) => ({ ...prev, [questionId]: answer }));\r\n  };\r\n\r\n  const handleQuestionSelect = (questionIndex: number) => {\r\n    setCurrentQuestion(questionIndex);\r\n  };\r\n\r\n  const handleToggleFlag = (questionIndex: number) => {\r\n    setFlaggedQuestions(prev => {\r\n      const newSet = new Set(prev);\r\n      if (newSet.has(questionIndex)) {\r\n        newSet.delete(questionIndex);\r\n      } else {\r\n        newSet.add(questionIndex);\r\n      }\r\n      return newSet;\r\n    });\r\n  };\r\n\r\n  const getAnsweredQuestions = () => {\r\n    const answered = new Set<number>();\r\n    currentExam?.questions.forEach((question, index) => {\r\n      if (answers[question.id] !== undefined && answers[question.id] !== '') {\r\n        answered.add(index);\r\n      }\r\n    });\r\n    return answered;\r\n  };\r\n\r\n  const handleTimeUp = () => {\r\n    handleSubmitExam();\r\n  };\r\n\r\n  const handleSubmitConfirm = () => {\r\n    setShowConfirmSubmit(true);\r\n  };\r\n\r\n  const handleSubmitExam = useCallback(() => {\r\n    if (isSubmitting || !currentExam) return;\r\n    setIsSubmitting(true);\r\n    setShowConfirmSubmit(false);\r\n\r\n    // Calculate score and results\r\n    let correctAnswers = 0;\r\n    const results: { [key: string]: boolean } = {};\r\n\r\n    currentExam.questions.forEach((question) => {\r\n      const userAnswer = answers[question.id];\r\n      const isCorrect = userAnswer === question.correctAnswer;\r\n      results[question.id] = isCorrect;\r\n      if (isCorrect) {\r\n        correctAnswers++;\r\n      }\r\n    });\r\n\r\n    const score = Math.round((correctAnswers / currentExam.questions.length) * 100);\r\n    \r\n    // Store results in session storage for results page\r\n    sessionStorage.setItem(`exam_answers_${examId || 'final'}`, JSON.stringify(answers));\r\n    sessionStorage.setItem(`exam_results_${examId || 'final'}`, JSON.stringify(results));\r\n    sessionStorage.setItem(`exam_flags_${examId || 'final'}`, JSON.stringify(Array.from(flaggedQuestions)));\r\n\r\n    setExamResults({\r\n      score,\r\n      correctAnswers,\r\n      totalQuestions: currentExam.questions.length,\r\n      results\r\n    });\r\n\r\n    // Update course progress\r\n    updateExamProgress(score);\r\n\r\n    // Navigate to results page with score data\r\n    const resultsUrl = `/my-courses/${courseId}/exam/results?type=${examType}&examId=${examId || 'final'}&score=${score}&correct=${correctAnswers}&total=${currentExam.questions.length}`;\r\n    router.push(resultsUrl);\r\n  }, [isSubmitting, currentExam, answers]);\r\n\r\n  const updateExamProgress = (score: number) => {\r\n    if (!currentExam) return;\r\n\r\n    const newCourse = JSON.parse(JSON.stringify(courseData)) as Course;\r\n\r\n    // Update the specific exam/quiz\r\n    if (examType === 'final') {\r\n      newCourse.finalExam.attempts += 1;\r\n      newCourse.finalExam.lastScore = score;\r\n      newCourse.finalExam.isPassed = score >= newCourse.finalExam.minimumScore;\r\n\r\n      // Update certificate eligibility if passed\r\n      if (newCourse.finalExam.isPassed) {\r\n        const allModulesCompleted = newCourse.modules.every(\r\n          (m) => m.chapters.every(\r\n            (ch) => ch.contents.every((c) => c.isCompleted) && ch.quiz.isPassed\r\n          ) && m.moduleQuiz.isPassed\r\n        );\r\n        \r\n        if (allModulesCompleted) {\r\n          newCourse.certificate.isEligible = true;\r\n          newCourse.certificate.completionDate = new Date().toISOString().split('T')[0];\r\n          newCourse.status = 'completed';\r\n        }\r\n      }\r\n    } else {\r\n      // Find and update chapter or module quiz\r\n      for (const courseModule of newCourse.modules) {\r\n        if (courseModule.moduleQuiz.id === examId) {\r\n          courseModule.moduleQuiz.attempts += 1;\r\n          courseModule.moduleQuiz.lastScore = score;\r\n          courseModule.moduleQuiz.isPassed = score >= courseModule.moduleQuiz.minimumScore;\r\n          break;\r\n        }\r\n        for (const chapter of courseModule.chapters) {\r\n          if (chapter.quiz.id === examId) {\r\n            chapter.quiz.attempts += 1;\r\n            chapter.quiz.lastScore = score;\r\n            chapter.quiz.isPassed = score >= chapter.quiz.minimumScore;\r\n            break;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    updateCourseProgress(newCourse);\r\n  };\r\n\r\n  const handleRetakeExam = () => {\r\n    // Reset all exam state\r\n    setCurrentQuestion(0);\r\n    setAnswers({});\r\n    setFlaggedQuestions(new Set());\r\n    setShowResults(false);\r\n    setExamResults(null);\r\n    setIsSubmitting(false);\r\n    setShowConfirmSubmit(false);\r\n    setExamStarted(true);\r\n    if (currentExam?.timeLimit) {\r\n      setTimeLeft(currentExam.timeLimit * 60);\r\n    }\r\n  };\r\n\r\n  const handleBackToCourse = () => {\r\n    router.push(`/my-courses/${courseId}`);\r\n  };\r\n\r\n  const handleBackHome = () => {\r\n    router.push('/my-courses');\r\n  };\r\n\r\n  if (!currentExam) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\r\n        <Card className=\"w-full max-w-md\">\r\n          <CardContent className=\"p-6 text-center\">\r\n            <XCircleIcon className=\"h-12 w-12 text-red-500 mx-auto mb-4\" />\r\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Ujian Tidak Ditemukan</h3>\r\n            <p className=\"text-gray-600 mb-4\">Ujian yang diminta tidak dapat ditemukan.</p>\r\n            <Button onClick={handleBackToCourse}>\r\n              <ArrowLeftIcon className=\"mr-2 h-4 w-4\" />\r\n              Kembali ke Kursus\r\n            </Button>\r\n          </CardContent>\r\n        </Card>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const answeredQuestions = getAnsweredQuestions();\r\n  const progressPercentage = (answeredQuestions.size / currentExam.questions.length) * 100;\r\n  const canSubmit = answeredQuestions.size > 0;\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50\">\r\n      {/* Header */}\r\n      <div className=\"bg-white border-b shadow-sm sticky top-0 z-10\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"flex items-center justify-between h-16\">\r\n            <div className=\"flex items-center space-x-4\">\r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n                onClick={handleBackToCourse}\r\n                className=\"flex items-center space-x-2\"\r\n              >\r\n                <ArrowLeftIcon className=\"h-4 w-4\" />\r\n                <span>Kembali</span>\r\n              </Button>\r\n              <TrophyIcon className=\"h-6 w-6 text-[var(--iai-primary)]\" />\r\n              <div>\r\n                <h1 className=\"text-lg font-semibold text-gray-900\">{currentExam.title}</h1>\r\n                <p className=\"text-sm text-gray-600\">{courseData.name}</p>\r\n              </div>\r\n            </div>\r\n            \r\n            {examStarted && timeLeft !== null && !showResults && (\r\n              <div className=\"flex items-center gap-3\">\r\n                {/* Time Progress Bar */}\r\n                {currentExam?.timeLimit && (\r\n                  <div className=\"hidden sm:flex flex-col items-end min-w-[120px]\">\r\n                    <div className=\"text-xs text-gray-500 mb-1\">\r\n                      Sisa Waktu\r\n                    </div>\r\n                    <div className=\"w-full bg-gray-200 rounded-full h-2\">\r\n                      <div \r\n                        className={`h-2 rounded-full transition-all duration-1000 ${\r\n                          getTimeStatus(timeLeft) === 'critical' ? 'bg-red-500' :\r\n                          getTimeStatus(timeLeft) === 'warning' ? 'bg-amber-500' : 'bg-blue-500'\r\n                        }`}\r\n                        style={{ \r\n                          width: `${(timeLeft / (currentExam.timeLimit * 60)) * 100}%` \r\n                        }}\r\n                      ></div>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n                \r\n                {/* Timer Display */}\r\n                <div className={`\r\n                  flex items-center gap-2 px-4 py-2 rounded-lg border-2 font-mono text-lg font-bold transition-all duration-300\r\n                  ${getTimeColor(getTimeStatus(timeLeft))}\r\n                  ${getTimeStatus(timeLeft) === 'critical' ? 'animate-pulse shadow-lg' : ''}\r\n                `}>\r\n                  <ClockIcon className=\"h-5 w-5\" />\r\n                  <div className=\"flex flex-col items-center\">\r\n                    <span className=\"leading-tight\">{formatTime(timeLeft)}</span>\r\n                    {currentExam?.timeLimit && (\r\n                      <span className=\"text-xs opacity-75 leading-tight\">\r\n                        {getTimeStatus(timeLeft) === 'critical' ? 'SEGERA HABIS!' :\r\n                         getTimeStatus(timeLeft) === 'warning' ? 'Perhatian' : 'Tersisa'}\r\n                      </span>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {!examStarted || showResults ? (\r\n              <div className=\"flex space-x-2\">\r\n                <Button variant=\"outline\" onClick={handleBackToCourse}>\r\n                  <ArrowLeftIcon className=\"mr-2 h-4 w-4\" />\r\n                  Kembali ke Kursus\r\n                </Button>\r\n                <Button variant=\"outline\" onClick={handleBackHome}>\r\n                  <HomeIcon className=\"mr-2 h-4 w-4\" />\r\n                  Dashboard\r\n                </Button>\r\n              </div>\r\n            ) : null}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\r\n        {/* Pre-exam Instructions */}\r\n        {showConfirmStart && (\r\n          <Card className=\"max-w-2xl mx-auto\">\r\n            <CardHeader>\r\n              <CardTitle className=\"text-center\">Instruksi Ujian</CardTitle>\r\n            </CardHeader>\r\n            <CardContent className=\"space-y-6\">\r\n              <div className=\"space-y-4\">\r\n                <div className=\"flex items-center space-x-3\">\r\n                  <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\">\r\n                    <span className=\"text-sm font-medium text-blue-600\">1</span>\r\n                  </div>\r\n                  <p className=\"text-gray-700\">Pastikan koneksi internet Anda stabil</p>\r\n                </div>\r\n                <div className=\"flex items-center space-x-3\">\r\n                  <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\">\r\n                    <span className=\"text-sm font-medium text-blue-600\">2</span>\r\n                  </div>\r\n                  <p className=\"text-gray-700\">\r\n                    Anda memiliki waktu {currentExam.timeLimit ? `${currentExam.timeLimit} menit` : 'tidak terbatas'} untuk menyelesaikan ujian\r\n                  </p>\r\n                </div>\r\n                <div className=\"flex items-center space-x-3\">\r\n                  <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\">\r\n                    <span className=\"text-sm font-medium text-blue-600\">3</span>\r\n                  </div>\r\n                  <p className=\"text-gray-700\">Nilai minimum untuk lulus: {currentExam.minimumScore}%</p>\r\n                </div>\r\n                <div className=\"flex items-center space-x-3\">\r\n                  <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\">\r\n                    <span className=\"text-sm font-medium text-blue-600\">4</span>\r\n                  </div>\r\n                  <p className=\"text-gray-700\">Total soal: {currentExam.questions.length}</p>\r\n                </div>\r\n                <div className=\"flex items-center space-x-3\">\r\n                  <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\">\r\n                    <span className=\"text-sm font-medium text-blue-600\">5</span>\r\n                  </div>\r\n                  <p className=\"text-gray-700\">Maksimal percobaan: {currentExam.maxAttempts}</p>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"bg-amber-50 border border-amber-200 rounded-lg p-4\">\r\n                <div className=\"flex items-center space-x-2\">\r\n                  <AlertTriangleIcon className=\"h-5 w-5 text-amber-600\" />\r\n                  <p className=\"font-medium text-amber-800\">Peringatan</p>\r\n                </div>\r\n                <p className=\"text-amber-700 text-sm mt-2\">\r\n                  Setelah ujian dimulai, jangan menutup browser atau meninggalkan halaman. \r\n                  Ujian akan otomatis diserahkan jika waktu habis.\r\n                </p>\r\n              </div>\r\n\r\n              <div className=\"text-center\">\r\n                <Button \r\n                  size=\"lg\" \r\n                  variant=\"iai\"\r\n                  onClick={handleStartExam}\r\n                >\r\n                  <TrophyIcon className=\"mr-2 h-5 w-5\" />\r\n                  Mulai Ujian\r\n                </Button>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n        )}\r\n\r\n        {/* Results Screen */}\r\n        {showResults && examResults && (\r\n          <div className=\"max-w-2xl mx-auto space-y-6\">\r\n            <Card className={`border-2 ${examResults.score >= currentExam.minimumScore ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>\r\n              <CardHeader className=\"text-center\">\r\n                <div className=\"flex justify-center mb-4\">\r\n                  {examResults.score >= currentExam.minimumScore ? (\r\n                    <CheckCircle2Icon className=\"h-16 w-16 text-green-600\" />\r\n                  ) : (\r\n                    <XCircleIcon className=\"h-16 w-16 text-red-600\" />\r\n                  )}\r\n                </div>\r\n                <CardTitle className=\"text-2xl\">\r\n                  {examResults.score >= currentExam.minimumScore ? 'Selamat! Anda Lulus' : 'Maaf, Anda Belum Lulus'}\r\n                </CardTitle>\r\n              </CardHeader>\r\n              <CardContent className=\"text-center space-y-4\">\r\n                <div className=\"text-4xl font-bold text-gray-900\">\r\n                  {examResults.score}%\r\n                </div>\r\n                <div className=\"text-gray-600\">\r\n                  {examResults.correctAnswers} dari {examResults.totalQuestions} soal dijawab benar\r\n                </div>\r\n                <div className=\"text-sm text-gray-500\">\r\n                  Nilai minimum untuk lulus: {currentExam.minimumScore}%\r\n                </div>\r\n                \r\n                <div className=\"flex justify-center space-x-4 mt-6\">\r\n                  {examResults.score < currentExam.minimumScore && currentExam.attempts < currentExam.maxAttempts && (\r\n                    <Button onClick={handleRetakeExam}>\r\n                      <RotateCcwIcon className=\"mr-2 h-4 w-4\" />\r\n                      Ulangi Ujian\r\n                    </Button>\r\n                  )}\r\n                  \r\n                  <Button variant=\"outline\" onClick={handleBackToCourse}>\r\n                    <ArrowLeftIcon className=\"mr-2 h-4 w-4\" />\r\n                    Kembali ke Kursus\r\n                  </Button>\r\n                </div>\r\n              </CardContent>\r\n            </Card>\r\n          </div>\r\n        )}\r\n\r\n        {/* Exam Interface */}\r\n        {examStarted && !showResults && (\r\n          <div className=\"grid grid-cols-1 xl:grid-cols-4 gap-8\">\r\n            {/* Question Area */}\r\n            <div className=\"xl:col-span-3 space-y-6\">\r\n              {/* Progress */}\r\n              <div className=\"space-y-2\">\r\n                <div className=\"flex justify-between text-sm text-gray-600\">\r\n                  <span>Soal {currentQuestion + 1} dari {currentExam.questions.length}</span>\r\n                  <span>{Math.round(progressPercentage)}% Selesai</span>\r\n                </div>\r\n                <Progress value={progressPercentage} className=\"h-3\" />\r\n              </div>\r\n\r\n              {/* Current Question */}\r\n              <Question\r\n                question={currentExam.questions[currentQuestion]}\r\n                questionNumber={currentQuestion + 1}\r\n                totalQuestions={currentExam.questions.length}\r\n                selectedAnswer={answers[currentExam.questions[currentQuestion].id]}\r\n                onAnswerChange={handleAnswerChange}\r\n                disabled={isSubmitting}\r\n              />\r\n\r\n              {/* Navigation */}\r\n              <div className=\"flex justify-between items-center\">\r\n                <Button\r\n                  variant=\"outline\"\r\n                  onClick={() => setCurrentQuestion(prev => Math.max(0, prev - 1))}\r\n                  disabled={currentQuestion === 0 || isSubmitting}\r\n                >\r\n                  Previous\r\n                </Button>\r\n\r\n                <div className=\"flex items-center space-x-3\">\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    size=\"sm\"\r\n                    onClick={() => handleToggleFlag(currentQuestion)}\r\n                    disabled={isSubmitting}\r\n                    className={flaggedQuestions.has(currentQuestion) ? 'bg-yellow-100 border-yellow-400' : ''}\r\n                  >\r\n                    <FlagIcon className={`h-4 w-4 mr-2 ${flaggedQuestions.has(currentQuestion) ? 'text-yellow-600' : ''}`} />\r\n                    Flag\r\n                  </Button>\r\n                </div>\r\n\r\n                <Button\r\n                  variant=\"iai\"\r\n                  onClick={() => setCurrentQuestion(prev => \r\n                    Math.min(currentExam.questions.length - 1, prev + 1)\r\n                  )}\r\n                  disabled={currentQuestion === currentExam.questions.length - 1 || isSubmitting}\r\n                >\r\n                  Next\r\n                </Button>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Question Bank Sidebar */}\r\n            <div className=\"xl:col-span-1\">\r\n              <QuestionBank\r\n                questions={currentExam.questions}\r\n                currentQuestion={currentQuestion}\r\n                answeredQuestions={answeredQuestions}\r\n                onQuestionSelect={handleQuestionSelect}\r\n                flaggedQuestions={flaggedQuestions}\r\n                onToggleFlag={handleToggleFlag}\r\n                onSubmit={handleSubmitConfirm}\r\n                canSubmit={canSubmit}\r\n                isSubmitting={isSubmitting}\r\n              />\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Submit Confirmation Dialog - Popup with Blurred Background */}\r\n      {showConfirmSubmit && (\r\n        <div className=\"fixed inset-0 backdrop-blur-sm flex items-center justify-center z-50\">\r\n          <Card className=\"w-full max-w-md mx-4 shadow-2xl border-2\">\r\n            <CardHeader>\r\n              <CardTitle className=\"flex items-center space-x-2 text-amber-700\">\r\n                <AlertTriangleIcon className=\"h-5 w-5\" />\r\n                <span>Konfirmasi Penyerahan</span>\r\n              </CardTitle>\r\n            </CardHeader>\r\n            <CardContent className=\"space-y-4\">\r\n              <p className=\"text-gray-600\">\r\n                Apakah Anda yakin ingin menyerahkan ujian? Pastikan semua jawaban sudah benar.\r\n              </p>\r\n              <div className=\"grid grid-cols-2 gap-4\">\r\n                <div className=\"text-center\">\r\n                  <div className=\"text-2xl font-bold text-green-600\">{answeredQuestions.size}</div>\r\n                  <div className=\"text-xs text-gray-500\">Terjawab</div>\r\n                </div>\r\n                <div className=\"text-center\">\r\n                  <div className=\"text-2xl font-bold text-gray-400\">\r\n                    {currentExam.questions.length - answeredQuestions.size}\r\n                  </div>\r\n                  <div className=\"text-xs text-gray-500\">Belum</div>\r\n                </div>\r\n              </div>\r\n              <div className=\"flex space-x-2\">\r\n                <Button\r\n                  variant=\"outline\"\r\n                  className=\"flex-1\"\r\n                  onClick={() => setShowConfirmSubmit(false)}\r\n                >\r\n                  Batal\r\n                </Button>\r\n                <Button\r\n                  variant=\"iai\"\r\n                  className=\"flex-1\"\r\n                  onClick={handleSubmitExam}\r\n                  disabled={isSubmitting}\r\n                >\r\n                  {isSubmitting ? 'Menyerahkan...' : 'Ya, Serahkan'}\r\n                </Button>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n        </div>\r\n      )}\r\n\r\n      {/* Time Warning Toast */}\r\n      {showTimeWarning.show && (\r\n        <div className=\"fixed top-4 right-4 z-50 animate-in slide-in-from-top-2\">\r\n          <div\r\n            className={`flex items-center space-x-3 rounded-lg px-6 py-4 shadow-lg border-2 ${\r\n              showTimeWarning.type === 'critical'\r\n                ? 'bg-red-50 text-red-800 border-red-200'\r\n                : 'bg-amber-50 text-amber-800 border-amber-200'\r\n            } min-w-[320px]`}\r\n          >\r\n            <div className={`flex-shrink-0 ${\r\n              showTimeWarning.type === 'critical' ? 'animate-pulse' : ''\r\n            }`}>\r\n              <ClockIcon className={`h-6 w-6 ${\r\n                showTimeWarning.type === 'critical' ? 'text-red-600' : 'text-amber-600'\r\n              }`} />\r\n            </div>\r\n            <div>\r\n              <p className=\"font-semibold text-sm\">{showTimeWarning.message}</p>\r\n              <p className=\"text-xs opacity-75 mt-1\">\r\n                {showTimeWarning.type === 'critical' \r\n                  ? 'Segera serahkan ujian Anda!'\r\n                  : 'Pastikan untuk menyerahkan ujian tepat waktu.'\r\n                }\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ExamPage;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AAAA;AAAA;AArBA;;;;;;;;;;AAuBA,MAAM,WAAqB;IACzB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,WAAW,OAAO,QAAQ;IAChC,MAAM,WAAW,aAAa,GAAG,CAAC,WAAW,SAAS,+BAA+B;IACrF,MAAM,SAAS,aAAa,GAAG,CAAC;IAEhC,MAAM,EAAE,UAAU,EAAE,oBAAoB,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,gBAAa,AAAD;IAEzD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAChE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACxD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAC1E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAKnC;IACV,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC/C,YAAY;QACZ,SAAS;QACT,QAAQ;IACV;IACA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAIlD;QAAE,MAAM;QAAO,SAAS;QAAI,MAAM;IAAU;IAE/C,4BAA4B;IAC5B,MAAM,iBAAiB;QACrB,IAAI,aAAa,SAAS;YACxB,OAAO,WAAW,SAAS;QAC7B;QAEA,wCAAwC;QACxC,KAAK,MAAM,gBAAgB,WAAW,OAAO,CAAE;YAC7C,IAAI,aAAa,UAAU,CAAC,EAAE,KAAK,QAAQ;gBACzC,OAAO,aAAa,UAAU;YAChC;YACA,KAAK,MAAM,WAAW,aAAa,QAAQ,CAAE;gBAC3C,IAAI,QAAQ,IAAI,CAAC,EAAE,KAAK,QAAQ;oBAC9B,OAAO,QAAQ,IAAI;gBACrB;YACF;QACF;QACA,OAAO;IACT;IAEA,MAAM,cAAc;IAEpB,oCAAoC;IACpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,eAAe,aAAa,aAAa,aAAa,MAAM;YAC9D,YAAY,YAAY,SAAS,GAAG;QACtC;IACF,GAAG;QAAC;QAAa;QAAa;KAAS;IAEvC,gCAAgC;IAChC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,eAAe,aAAa,QAAQ,WAAW,KAAK,CAAC,aAAa;YACpE,MAAM,QAAQ,YAAY;gBACxB,YAAY,CAAC;oBACX,IAAI,SAAS,QAAQ,QAAQ,GAAG;wBAC9B;wBACA,OAAO;oBACT;oBAEA,MAAM,UAAU,OAAO;oBAEvB,mCAAmC;oBACnC,IAAI,YAAY,OAAO,CAAC,aAAa,UAAU,EAAE;wBAC/C,gBAAgB,CAAA,OAAQ,CAAC;gCAAE,GAAG,IAAI;gCAAE,YAAY;4BAAK,CAAC;wBACtD,mBAAmB;4BACjB,MAAM;4BACN,SAAS;4BACT,MAAM;wBACR;wBACA,WAAW,IAAM,mBAAmB,CAAA,OAAQ,CAAC;oCAAE,GAAG,IAAI;oCAAE,MAAM;gCAAM,CAAC,IAAI;oBAC3E;oBACA,IAAI,YAAY,OAAO,CAAC,aAAa,OAAO,EAAE;wBAC5C,gBAAgB,CAAA,OAAQ,CAAC;gCAAE,GAAG,IAAI;gCAAE,SAAS;4BAAK,CAAC;wBACnD,mBAAmB;4BACjB,MAAM;4BACN,SAAS;4BACT,MAAM;wBACR;wBACA,WAAW,IAAM,mBAAmB,CAAA,OAAQ,CAAC;oCAAE,GAAG,IAAI;oCAAE,MAAM;gCAAM,CAAC,IAAI;oBAC3E;oBACA,IAAI,YAAY,MAAM,CAAC,aAAa,MAAM,EAAE;wBAC1C,gBAAgB,CAAA,OAAQ,CAAC;gCAAE,GAAG,IAAI;gCAAE,QAAQ;4BAAK,CAAC;wBAClD,mBAAmB;4BACjB,MAAM;4BACN,SAAS;4BACT,MAAM;wBACR;wBACA,WAAW,IAAM,mBAAmB,CAAA,OAAQ,CAAC;oCAAE,GAAG,IAAI;oCAAE,MAAM;gCAAM,CAAC,IAAI;oBAC3E;oBAEA,OAAO;gBACT;YACF,GAAG;YACH,OAAO,IAAM,cAAc;QAC7B;IACF,GAAG;QAAC;QAAa;QAAU;QAAa;KAAa;IAErD,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,eAAe,CAAC,aAAa;YAC/B,MAAM,qBAAqB,CAAC;gBAC1B,EAAE,cAAc;gBAChB,EAAE,WAAW,GAAG;gBAChB,OAAO;YACT;YAEA,OAAO,gBAAgB,CAAC,gBAAgB;YACxC,OAAO,IAAM,OAAO,mBAAmB,CAAC,gBAAgB;QAC1D;IACF,GAAG;QAAC;QAAa;KAAY;IAE7B,MAAM,aAAa,CAAC;QAClB,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;QACnC,MAAM,OAAO,KAAK,KAAK,CAAC,AAAC,UAAU,OAAQ;QAC3C,MAAM,OAAO,UAAU;QAEvB,IAAI,QAAQ,GAAG;YACb,OAAO,GAAG,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;QACvH;QACA,OAAO,GAAG,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IAClF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,CAAC,aAAa,WAAW,OAAO;QACpC,MAAM,YAAY,YAAY,SAAS,GAAG;QAC1C,MAAM,YAAY,UAAU;QAE5B,IAAI,aAAa,KAAK,OAAO,YAAY,gBAAgB;QACzD,IAAI,aAAa,MAAM,OAAO,WAAW,gBAAgB;QACzD,OAAO;IACT;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB;QACtB,eAAe;QACf,oBAAoB;QACpB,IAAI,aAAa,WAAW;YAC1B,YAAY,YAAY,SAAS,GAAG;QACtC;IACF;IAEA,MAAM,qBAAqB,CAAC,YAAoB;QAC9C,WAAW,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,WAAW,EAAE;YAAO,CAAC;IACzD;IAEA,MAAM,uBAAuB,CAAC;QAC5B,mBAAmB;IACrB;IAEA,MAAM,mBAAmB,CAAC;QACxB,oBAAoB,CAAA;YAClB,MAAM,SAAS,IAAI,IAAI;YACvB,IAAI,OAAO,GAAG,CAAC,gBAAgB;gBAC7B,OAAO,MAAM,CAAC;YAChB,OAAO;gBACL,OAAO,GAAG,CAAC;YACb;YACA,OAAO;QACT;IACF;IAEA,MAAM,uBAAuB;QAC3B,MAAM,WAAW,IAAI;QACrB,aAAa,UAAU,QAAQ,CAAC,UAAU;YACxC,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC,KAAK,aAAa,OAAO,CAAC,SAAS,EAAE,CAAC,KAAK,IAAI;gBACrE,SAAS,GAAG,CAAC;YACf;QACF;QACA,OAAO;IACT;IAEA,MAAM,eAAe;QACnB;IACF;IAEA,MAAM,sBAAsB;QAC1B,qBAAqB;IACvB;IAEA,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACnC,IAAI,gBAAgB,CAAC,aAAa;QAClC,gBAAgB;QAChB,qBAAqB;QAErB,8BAA8B;QAC9B,IAAI,iBAAiB;QACrB,MAAM,UAAsC,CAAC;QAE7C,YAAY,SAAS,CAAC,OAAO,CAAC,CAAC;YAC7B,MAAM,aAAa,OAAO,CAAC,SAAS,EAAE,CAAC;YACvC,MAAM,YAAY,eAAe,SAAS,aAAa;YACvD,OAAO,CAAC,SAAS,EAAE,CAAC,GAAG;YACvB,IAAI,WAAW;gBACb;YACF;QACF;QAEA,MAAM,QAAQ,KAAK,KAAK,CAAC,AAAC,iBAAiB,YAAY,SAAS,CAAC,MAAM,GAAI;QAE3E,oDAAoD;QACpD,eAAe,OAAO,CAAC,CAAC,aAAa,EAAE,UAAU,SAAS,EAAE,KAAK,SAAS,CAAC;QAC3E,eAAe,OAAO,CAAC,CAAC,aAAa,EAAE,UAAU,SAAS,EAAE,KAAK,SAAS,CAAC;QAC3E,eAAe,OAAO,CAAC,CAAC,WAAW,EAAE,UAAU,SAAS,EAAE,KAAK,SAAS,CAAC,MAAM,IAAI,CAAC;QAEpF,eAAe;YACb;YACA;YACA,gBAAgB,YAAY,SAAS,CAAC,MAAM;YAC5C;QACF;QAEA,yBAAyB;QACzB,mBAAmB;QAEnB,2CAA2C;QAC3C,MAAM,aAAa,CAAC,YAAY,EAAE,SAAS,mBAAmB,EAAE,SAAS,QAAQ,EAAE,UAAU,QAAQ,OAAO,EAAE,MAAM,SAAS,EAAE,eAAe,OAAO,EAAE,YAAY,SAAS,CAAC,MAAM,EAAE;QACrL,OAAO,IAAI,CAAC;IACd,GAAG;QAAC;QAAc;QAAa;KAAQ;IAEvC,MAAM,qBAAqB,CAAC;QAC1B,IAAI,CAAC,aAAa;QAElB,MAAM,YAAY,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC;QAE5C,gCAAgC;QAChC,IAAI,aAAa,SAAS;YACxB,UAAU,SAAS,CAAC,QAAQ,IAAI;YAChC,UAAU,SAAS,CAAC,SAAS,GAAG;YAChC,UAAU,SAAS,CAAC,QAAQ,GAAG,SAAS,UAAU,SAAS,CAAC,YAAY;YAExE,2CAA2C;YAC3C,IAAI,UAAU,SAAS,CAAC,QAAQ,EAAE;gBAChC,MAAM,sBAAsB,UAAU,OAAO,CAAC,KAAK,CACjD,CAAC,IAAM,EAAE,QAAQ,CAAC,KAAK,CACrB,CAAC,KAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAM,EAAE,WAAW,KAAK,GAAG,IAAI,CAAC,QAAQ,KAChE,EAAE,UAAU,CAAC,QAAQ;gBAG5B,IAAI,qBAAqB;oBACvB,UAAU,WAAW,CAAC,UAAU,GAAG;oBACnC,UAAU,WAAW,CAAC,cAAc,GAAG,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;oBAC7E,UAAU,MAAM,GAAG;gBACrB;YACF;QACF,OAAO;YACL,yCAAyC;YACzC,KAAK,MAAM,gBAAgB,UAAU,OAAO,CAAE;gBAC5C,IAAI,aAAa,UAAU,CAAC,EAAE,KAAK,QAAQ;oBACzC,aAAa,UAAU,CAAC,QAAQ,IAAI;oBACpC,aAAa,UAAU,CAAC,SAAS,GAAG;oBACpC,aAAa,UAAU,CAAC,QAAQ,GAAG,SAAS,aAAa,UAAU,CAAC,YAAY;oBAChF;gBACF;gBACA,KAAK,MAAM,WAAW,aAAa,QAAQ,CAAE;oBAC3C,IAAI,QAAQ,IAAI,CAAC,EAAE,KAAK,QAAQ;wBAC9B,QAAQ,IAAI,CAAC,QAAQ,IAAI;wBACzB,QAAQ,IAAI,CAAC,SAAS,GAAG;wBACzB,QAAQ,IAAI,CAAC,QAAQ,GAAG,SAAS,QAAQ,IAAI,CAAC,YAAY;wBAC1D;oBACF;gBACF;YACF;QACF;QAEA,qBAAqB;IACvB;IAEA,MAAM,mBAAmB;QACvB,uBAAuB;QACvB,mBAAmB;QACnB,WAAW,CAAC;QACZ,oBAAoB,IAAI;QACxB,eAAe;QACf,eAAe;QACf,gBAAgB;QAChB,qBAAqB;QACrB,eAAe;QACf,IAAI,aAAa,WAAW;YAC1B,YAAY,YAAY,SAAS,GAAG;QACtC;IACF;IAEA,MAAM,qBAAqB;QACzB,OAAO,IAAI,CAAC,CAAC,YAAY,EAAE,UAAU;IACvC;IAEA,MAAM,iBAAiB;QACrB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,aAAa;QAChB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC,uNAAA,CAAA,eAAW;4BAAC,WAAU;;;;;;sCACvB,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAClC,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAS;;8CACf,8OAAC,8NAAA,CAAA,kBAAa;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;IAOtD;IAEA,MAAM,oBAAoB;IAC1B,MAAM,qBAAqB,AAAC,kBAAkB,IAAI,GAAG,YAAY,SAAS,CAAC,MAAM,GAAI;IACrF,MAAM,YAAY,kBAAkB,IAAI,GAAG;IAE3C,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,8OAAC,8NAAA,CAAA,kBAAa;gDAAC,WAAU;;;;;;0DACzB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC,qNAAA,CAAA,cAAU;wCAAC,WAAU;;;;;;kDACtB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAuC,YAAY,KAAK;;;;;;0DACtE,8OAAC;gDAAE,WAAU;0DAAyB,WAAW,IAAI;;;;;;;;;;;;;;;;;;4BAIxD,eAAe,aAAa,QAAQ,CAAC,6BACpC,8OAAC;gCAAI,WAAU;;oCAEZ,aAAa,2BACZ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAA6B;;;;;;0DAG5C,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,WAAW,CAAC,8CAA8C,EACxD,cAAc,cAAc,aAAa,eACzC,cAAc,cAAc,YAAY,iBAAiB,eACzD;oDACF,OAAO;wDACL,OAAO,GAAG,AAAC,WAAW,CAAC,YAAY,SAAS,GAAG,EAAE,IAAK,IAAI,CAAC,CAAC;oDAC9D;;;;;;;;;;;;;;;;;kDAOR,8OAAC;wCAAI,WAAW,CAAC;;kBAEf,EAAE,aAAa,cAAc,WAAW;kBACxC,EAAE,cAAc,cAAc,aAAa,4BAA4B,GAAG;gBAC5E,CAAC;;0DACC,8OAAC,qNAAA,CAAA,cAAS;gDAAC,WAAU;;;;;;0DACrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAiB,WAAW;;;;;;oDAC3C,aAAa,2BACZ,8OAAC;wDAAK,WAAU;kEACb,cAAc,cAAc,aAAa,kBACzC,cAAc,cAAc,YAAY,cAAc;;;;;;;;;;;;;;;;;;;;;;;;4BAQlE,CAAC,eAAe,4BACf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,SAAS;;0DACjC,8OAAC,8NAAA,CAAA,kBAAa;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAG5C,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,SAAS;;0DACjC,8OAAC,mNAAA,CAAA,aAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;uCAIvC;;;;;;;;;;;;;;;;;0BAKV,8OAAC;gBAAI,WAAU;;oBAEZ,kCACC,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAc;;;;;;;;;;;0CAErC,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;sEAAoC;;;;;;;;;;;kEAEtD,8OAAC;wDAAE,WAAU;kEAAgB;;;;;;;;;;;;0DAE/B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;sEAAoC;;;;;;;;;;;kEAEtD,8OAAC;wDAAE,WAAU;;4DAAgB;4DACN,YAAY,SAAS,GAAG,GAAG,YAAY,SAAS,CAAC,MAAM,CAAC,GAAG;4DAAiB;;;;;;;;;;;;;0DAGrG,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;sEAAoC;;;;;;;;;;;kEAEtD,8OAAC;wDAAE,WAAU;;4DAAgB;4DAA4B,YAAY,YAAY;4DAAC;;;;;;;;;;;;;0DAEpF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;sEAAoC;;;;;;;;;;;kEAEtD,8OAAC;wDAAE,WAAU;;4DAAgB;4DAAa,YAAY,SAAS,CAAC,MAAM;;;;;;;;;;;;;0DAExE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;sEAAoC;;;;;;;;;;;kEAEtD,8OAAC;wDAAE,WAAU;;4DAAgB;4DAAqB,YAAY,WAAW;;;;;;;;;;;;;;;;;;;kDAI7E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,qNAAA,CAAA,cAAiB;wDAAC,WAAU;;;;;;kEAC7B,8OAAC;wDAAE,WAAU;kEAA6B;;;;;;;;;;;;0DAE5C,8OAAC;gDAAE,WAAU;0DAA8B;;;;;;;;;;;;kDAM7C,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,SAAS;;8DAET,8OAAC,qNAAA,CAAA,cAAU;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;oBAShD,eAAe,6BACd,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAW,CAAC,SAAS,EAAE,YAAY,KAAK,IAAI,YAAY,YAAY,GAAG,iCAAiC,4BAA4B;;8CACxI,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,8OAAC;4CAAI,WAAU;sDACZ,YAAY,KAAK,IAAI,YAAY,YAAY,iBAC5C,8OAAC,0OAAA,CAAA,wBAAgB;gDAAC,WAAU;;;;;qEAE5B,8OAAC,uNAAA,CAAA,eAAW;gDAAC,WAAU;;;;;;;;;;;sDAG3B,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAClB,YAAY,KAAK,IAAI,YAAY,YAAY,GAAG,wBAAwB;;;;;;;;;;;;8CAG7E,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;gDACZ,YAAY,KAAK;gDAAC;;;;;;;sDAErB,8OAAC;4CAAI,WAAU;;gDACZ,YAAY,cAAc;gDAAC;gDAAO,YAAY,cAAc;gDAAC;;;;;;;sDAEhE,8OAAC;4CAAI,WAAU;;gDAAwB;gDACT,YAAY,YAAY;gDAAC;;;;;;;sDAGvD,8OAAC;4CAAI,WAAU;;gDACZ,YAAY,KAAK,GAAG,YAAY,YAAY,IAAI,YAAY,QAAQ,GAAG,YAAY,WAAW,kBAC7F,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAS;;sEACf,8OAAC,uNAAA,CAAA,eAAa;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAK9C,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,SAAS;;sEACjC,8OAAC,8NAAA,CAAA,kBAAa;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAUrD,eAAe,CAAC,6BACf,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;4DAAK;4DAAM,kBAAkB;4DAAE;4DAAO,YAAY,SAAS,CAAC,MAAM;;;;;;;kEACnE,8OAAC;;4DAAM,KAAK,KAAK,CAAC;4DAAoB;;;;;;;;;;;;;0DAExC,8OAAC,oIAAA,CAAA,WAAQ;gDAAC,OAAO;gDAAoB,WAAU;;;;;;;;;;;;kDAIjD,8OAAC,sJAAA,CAAA,WAAQ;wCACP,UAAU,YAAY,SAAS,CAAC,gBAAgB;wCAChD,gBAAgB,kBAAkB;wCAClC,gBAAgB,YAAY,SAAS,CAAC,MAAM;wCAC5C,gBAAgB,OAAO,CAAC,YAAY,SAAS,CAAC,gBAAgB,CAAC,EAAE,CAAC;wCAClE,gBAAgB;wCAChB,UAAU;;;;;;kDAIZ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,SAAS,IAAM,mBAAmB,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;gDAC7D,UAAU,oBAAoB,KAAK;0DACpC;;;;;;0DAID,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,iBAAiB;oDAChC,UAAU;oDACV,WAAW,iBAAiB,GAAG,CAAC,mBAAmB,oCAAoC;;sEAEvF,8OAAC,mNAAA,CAAA,aAAQ;4DAAC,WAAW,CAAC,aAAa,EAAE,iBAAiB,GAAG,CAAC,mBAAmB,oBAAoB,IAAI;;;;;;wDAAI;;;;;;;;;;;;0DAK7G,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,SAAS,IAAM,mBAAmB,CAAA,OAChC,KAAK,GAAG,CAAC,YAAY,SAAS,CAAC,MAAM,GAAG,GAAG,OAAO;gDAEpD,UAAU,oBAAoB,YAAY,SAAS,CAAC,MAAM,GAAG,KAAK;0DACnE;;;;;;;;;;;;;;;;;;0CAOL,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,8JAAA,CAAA,eAAY;oCACX,WAAW,YAAY,SAAS;oCAChC,iBAAiB;oCACjB,mBAAmB;oCACnB,kBAAkB;oCAClB,kBAAkB;oCAClB,cAAc;oCACd,UAAU;oCACV,WAAW;oCACX,cAAc;;;;;;;;;;;;;;;;;;;;;;;YAQvB,mCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,8OAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,8OAAC,qNAAA,CAAA,cAAiB;wCAAC,WAAU;;;;;;kDAC7B,8OAAC;kDAAK;;;;;;;;;;;;;;;;;sCAGV,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;8CAG7B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAqC,kBAAkB,IAAI;;;;;;8DAC1E,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ,YAAY,SAAS,CAAC,MAAM,GAAG,kBAAkB,IAAI;;;;;;8DAExD,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAG3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;4CACV,SAAS,IAAM,qBAAqB;sDACrC;;;;;;sDAGD,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;4CACV,SAAS;4CACT,UAAU;sDAET,eAAe,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAS9C,gBAAgB,IAAI,kBACnB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,WAAW,CAAC,oEAAoE,EAC9E,gBAAgB,IAAI,KAAK,aACrB,0CACA,8CACL,cAAc,CAAC;;sCAEhB,8OAAC;4BAAI,WAAW,CAAC,cAAc,EAC7B,gBAAgB,IAAI,KAAK,aAAa,kBAAkB,IACxD;sCACA,cAAA,8OAAC,qNAAA,CAAA,cAAS;gCAAC,WAAW,CAAC,QAAQ,EAC7B,gBAAgB,IAAI,KAAK,aAAa,iBAAiB,kBACvD;;;;;;;;;;;sCAEJ,8OAAC;;8CACC,8OAAC;oCAAE,WAAU;8CAAyB,gBAAgB,OAAO;;;;;;8CAC7D,8OAAC;oCAAE,WAAU;8CACV,gBAAgB,IAAI,KAAK,aACtB,gCACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpB;uCAEe", "debugId": null}}]}