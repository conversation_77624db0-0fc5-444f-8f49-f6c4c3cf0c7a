{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/app/api/certificates/route.ts"], "sourcesContent": ["import { NextRequest } from 'next/server';\r\n\r\n// PDF generation using puppeteer-service\r\nexport async function POST(req: NextRequest) {\r\n  try {\r\n    const body = await req.json();\r\n    const { htmlContent } = body;\r\n\r\n    // Validate input\r\n    if (!htmlContent) {\r\n      return new Response(\r\n        JSON.stringify({ error: 'HTML content is required' }),\r\n        { status: 400, headers: { 'Content-Type': 'application/json' } }\r\n      );\r\n    }\r\n\r\n    // Call puppeteer-service to generate PDF\r\n    // In production, this should be configured via environment variables\r\n    const PUPPETEER_SERVICE_URL = process.env.PUPPETEER_SERVICE_URL || 'http://localhost:3000';\r\n    \r\n    const response = await fetch(`${PUPPETEER_SERVICE_URL}/pdf`, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({\r\n        html: htmlContent,\r\n        format: 'A4',\r\n        landscape: true,\r\n      }),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json();\r\n      return new Response(\r\n        JSON.stringify({ error: 'Failed to generate PDF', details: errorData }),\r\n        { status: response.status, headers: { 'Content-Type': 'application/json' } }\r\n      );\r\n    }\r\n\r\n    const pdfBuffer = await response.arrayBuffer();\r\n    \r\n    return new Response(pdfBuffer, {\r\n      status: 200,\r\n      headers: {\r\n        'Content-Type': 'application/pdf',\r\n        'Content-Disposition': 'attachment; filename=\"certificate.pdf\"',\r\n      },\r\n    });\r\n  } catch (error) {\r\n    console.error('Error generating PDF:', error);\r\n    return new Response(\r\n      JSON.stringify({ error: 'Internal server error', details: (error as Error).message }),\r\n      { status: 500, headers: { 'Content-Type': 'application/json' } }\r\n    );\r\n  }\r\n}"], "names": [], "mappings": ";;;AAGO,eAAe,KAAK,GAAgB;IACzC,IAAI;QACF,MAAM,OAAO,MAAM,IAAI,IAAI;QAC3B,MAAM,EAAE,WAAW,EAAE,GAAG;QAExB,iBAAiB;QACjB,IAAI,CAAC,aAAa;YAChB,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,OAAO;YAA2B,IACnD;gBAAE,QAAQ;gBAAK,SAAS;oBAAE,gBAAgB;gBAAmB;YAAE;QAEnE;QAEA,yCAAyC;QACzC,qEAAqE;QACrE,MAAM,wBAAwB,QAAQ,GAAG,CAAC,qBAAqB,IAAI;QAEnE,MAAM,WAAW,MAAM,MAAM,GAAG,sBAAsB,IAAI,CAAC,EAAE;YAC3D,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,MAAM;gBACN,QAAQ;gBACR,WAAW;YACb;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,OAAO;gBAA0B,SAAS;YAAU,IACrE;gBAAE,QAAQ,SAAS,MAAM;gBAAE,SAAS;oBAAE,gBAAgB;gBAAmB;YAAE;QAE/E;QAEA,MAAM,YAAY,MAAM,SAAS,WAAW;QAE5C,OAAO,IAAI,SAAS,WAAW;YAC7B,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,uBAAuB;YACzB;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;YAAE,OAAO;YAAyB,SAAS,AAAC,MAAgB,OAAO;QAAC,IACnF;YAAE,QAAQ;YAAK,SAAS;gBAAE,gBAAgB;YAAmB;QAAE;IAEnE;AACF", "debugId": null}}]}