const CHUNK_PUBLIC_PATH = "server/app/api/courses/route.js";
const runtime = require("../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_next_42bc417d._.js");
runtime.loadChunk("server/chunks/node_modules_@neondatabase_serverless_index_mjs_f936482b._.js");
runtime.loadChunk("server/chunks/node_modules_drizzle-orm_d457836a._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__90f5826a._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/courses/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/courses/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/courses/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
