{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "file": "capture.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/logs/capture.ts"], "sourcesContent": ["import { format } from 'node:util';\nimport type { Log, LogSeverityLevel, ParameterizedString } from '@sentry/core';\nimport { _INTERNAL_captureLog } from '@sentry/core';\n\nexport type CaptureLogArgs =\n  | [message: ParameterizedString, attributes?: Log['attributes']]\n  | [messageTemplate: string, messageParams: Array<unknown>, attributes?: Log['attributes']];\n\n/**\n * Capture a log with the given level.\n *\n * @param level - The level of the log.\n * @param message - The message to log.\n * @param attributes - Arbitrary structured data that stores information about the log - e.g., userId: 100.\n */\nexport function captureLog(level: LogSeverityLevel, ...args: CaptureLogArgs): void {\n  const [messageOrMessageTemplate, paramsOrAttributes, maybeAttributes] = args;\n  if (Array.isArray(paramsOrAttributes)) {\n    const attributes = { ...maybeAttributes };\n    attributes['sentry.message.template'] = messageOrMessageTemplate;\n    paramsOrAttributes.forEach((param, index) => {\n      attributes[`sentry.message.parameter.${index}`] = param;\n    });\n    const message = format(messageOrMessageTemplate, ...paramsOrAttributes);\n    _INTERNAL_captureLog({ level, message, attributes });\n  } else {\n    _INTERNAL_captureLog({ level, message: messageOrMessageTemplate, attributes: paramsOrAttributes });\n  }\n}\n"], "names": ["format", "_INTERNAL_captureLog"], "mappings": ";;;;;AAQA;;;;;;CAMA,GACO,SAAS,UAAU,CAAC,KAAK,EAAoB,GAAG,IAAI,EAAwB;IACjF,MAAM,CAAC,wBAAwB,EAAE,kBAAkB,EAAE,eAAe,CAAA,GAAI,IAAI;IAC5E,IAAI,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE;QACrC,MAAM,UAAA,GAAa;YAAE,GAAG,eAAA;QAAA,CAAiB;QACzC,UAAU,CAAC,yBAAyB,CAAA,GAAI,wBAAwB;QAChE,kBAAkB,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,KAAK,KAAK;YAC3C,UAAU,CAAC,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAA,CAAA,GAAA,KAAA;QACA,CAAA,CAAA;QACA,MAAA,OAAA,GAAAA,UAAAA,MAAA,CAAA,wBAAA,EAAA,GAAA,kBAAA,CAAA;QACAC,KAAAA,oBAAA,CAAA;YAAA,KAAA;YAAA,OAAA;YAAA,UAAA;QAAA,CAAA,CAAA;IACA,CAAA,MAAA;QACAA,KAAAA,oBAAA,CAAA;YAAA,KAAA;YAAA,OAAA,EAAA,wBAAA;YAAA,UAAA,EAAA,kBAAA;QAAA,CAAA,CAAA;IACA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "file": "exports.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/logs/exports.ts"], "sourcesContent": ["import { type CaptureLogArgs, captureLog } from './capture';\n\n/**\n * @summary Capture a log with the `trace` level. Requires the `enableLogs` option to be enabled.\n *\n * You can either pass a message and attributes or a message template, params and attributes.\n *\n * @example\n *\n * ```\n * Sentry.logger.trace('Starting database connection', {\n *   database: 'users',\n *   connectionId: 'conn_123'\n * });\n * ```\n *\n * @example With template strings\n *\n * ```\n * Sentry.logger.trace('Database connection %s established for %s',\n *   ['successful', 'users'],\n *   { connectionId: 'conn_123' }\n * );\n * ```\n */\nexport function trace(...args: CaptureLogArgs): void {\n  captureLog('trace', ...args);\n}\n\n/**\n * @summary Capture a log with the `debug` level. Requires the `enableLogs` option to be enabled.\n *\n * You can either pass a message and attributes or a message template, params and attributes.\n *\n * @example\n *\n * ```\n * Sentry.logger.debug('Cache miss for user profile', {\n *   userId: 'user_123',\n *   cacheKey: 'profile:user_123'\n * });\n * ```\n *\n * @example With template strings\n *\n * ```\n * Sentry.logger.debug('Cache %s for %s: %s',\n *   ['miss', 'user profile', 'key not found'],\n *   { userId: 'user_123' }\n * );\n * ```\n */\nexport function debug(...args: CaptureLogArgs): void {\n  captureLog('debug', ...args);\n}\n\n/**\n * @summary Capture a log with the `info` level. Requires the `enableLogs` option to be enabled.\n *\n * You can either pass a message and attributes or a message template, params and attributes.\n *\n * @example\n *\n * ```\n * Sentry.logger.info('User profile updated', {\n *   userId: 'user_123',\n *   updatedFields: ['email', 'preferences']\n * });\n * ```\n *\n * @example With template strings\n *\n * ```\n * Sentry.logger.info('User %s updated their %s',\n *   ['John Doe', 'profile settings'],\n *   { userId: 'user_123' }\n * );\n * ```\n */\nexport function info(...args: CaptureLogArgs): void {\n  captureLog('info', ...args);\n}\n\n/**\n * @summary Capture a log with the `warn` level. Requires the `enableLogs` option to be enabled.\n *\n * You can either pass a message and attributes or a message template, params and attributes.\n *\n * @example\n *\n * ```\n * Sentry.logger.warn('Rate limit approaching', {\n *   endpoint: '/api/users',\n *   currentRate: '95/100',\n *   resetTime: '2024-03-20T10:00:00Z'\n * });\n * ```\n *\n * @example With template strings\n *\n * ```\n * Sentry.logger.warn('Rate limit %s for %s: %s',\n *   ['approaching', '/api/users', '95/100 requests'],\n *   { resetTime: '2024-03-20T10:00:00Z' }\n * );\n * ```\n */\nexport function warn(...args: CaptureLogArgs): void {\n  captureLog('warn', ...args);\n}\n\n/**\n * @summary Capture a log with the `error` level. Requires the `enableLogs` option to be enabled.\n *\n * You can either pass a message and attributes or a message template, params and attributes.\n *\n * @example\n *\n * ```\n * Sentry.logger.error('Failed to process payment', {\n *   orderId: 'order_123',\n *   errorCode: 'PAYMENT_FAILED',\n *   amount: 99.99\n * });\n * ```\n *\n * @example With template strings\n *\n * ```\n * Sentry.logger.error('Payment processing failed for order %s: %s',\n *   ['order_123', 'insufficient funds'],\n *   { amount: 99.99 }\n * );\n * ```\n */\nexport function error(...args: CaptureLogArgs): void {\n  captureLog('error', ...args);\n}\n\n/**\n * @summary Capture a log with the `fatal` level. Requires the `enableLogs` option to be enabled.\n *\n * You can either pass a message and attributes or a message template, params and attributes.\n *\n * @example\n *\n * ```\n * Sentry.logger.fatal('Database connection pool exhausted', {\n *   database: 'users',\n *   activeConnections: 100,\n *   maxConnections: 100\n * });\n * ```\n *\n * @example With template strings\n *\n * ```\n * Sentry.logger.fatal('Database %s: %s connections active',\n *   ['connection pool exhausted', '100/100'],\n *   { database: 'users' }\n * );\n * ```\n */\nexport function fatal(...args: CaptureLogArgs): void {\n  captureLog('fatal', ...args);\n}\n\nexport { fmt } from '@sentry/core';\n"], "names": ["captureLog"], "mappings": ";;;;;AAEA;;;;;;;;;;;;;;;;;;;;;;CAsBA,GACO,SAAS,KAAK,CAAC,GAAG,IAAI,EAAwB;IACnDA,QAAAA,UAAU,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC;AAC9B;AAEA;;;;;;;;;;;;;;;;;;;;;;CAsBA,GACO,SAAS,KAAK,CAAC,GAAG,IAAI,EAAwB;IACnDA,QAAAA,UAAU,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC;AAC9B;AAEA;;;;;;;;;;;;;;;;;;;;;;CAsBA,GACO,SAAS,IAAI,CAAC,GAAG,IAAI,EAAwB;IAClDA,QAAAA,UAAU,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC;AAC7B;AAEA;;;;;;;;;;;;;;;;;;;;;;;CAuBA,GACO,SAAS,IAAI,CAAC,GAAG,IAAI,EAAwB;IAClDA,QAAAA,UAAU,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC;AAC7B;AAEA;;;;;;;;;;;;;;;;;;;;;;;CAuBA,GACO,SAAS,KAAK,CAAC,GAAG,IAAI,EAAwB;IACnDA,QAAAA,UAAU,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC;AAC9B;AAEA;;;;;;;;;;;;;;;;;;;;;;;CAuBA,GACO,SAAS,KAAK,CAAC,GAAG,IAAI,EAAwB;IACnDA,QAAAA,UAAU,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC;AAC9B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 217, "column": 0}, "map": {"version": 3, "file": "instrument.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/otel/instrument.ts"], "sourcesContent": ["import { type Instrumentation, registerInstrumentations } from '@opentelemetry/instrumentation';\n\n/** Exported only for tests. */\nexport const INSTRUMENTED: Record<string, Instrumentation> = {};\n\nexport function generateInstrumentOnce<\n  Options,\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  InstrumentationClass extends new (...args: any[]) => Instrumentation,\n>(\n  name: string,\n  instrumentationClass: InstrumentationClass,\n  optionsCallback: (options: Options) => ConstructorParameters<InstrumentationClass>[0],\n): ((options: Options) => InstanceType<InstrumentationClass>) & { id: string };\nexport function generateInstrumentOnce<\n  Options = unknown,\n  InstrumentationInstance extends Instrumentation = Instrumentation,\n>(\n  name: string,\n  creator: (options?: Options) => InstrumentationInstance,\n): ((options?: Options) => InstrumentationInstance) & { id: string };\n/**\n * Instrument an OpenTelemetry instrumentation once.\n * This will skip running instrumentation again if it was already instrumented.\n */\nexport function generateInstrumentOnce<Options>(\n  name: string,\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  creatorOrClass: (new (...args: any[]) => Instrumentation) | ((options?: Options) => Instrumentation),\n  optionsCallback?: (options: Options) => unknown,\n): ((options: Options) => Instrumentation) & { id: string } {\n  if (optionsCallback) {\n    return _generateInstrumentOnceWithOptions(\n      name,\n      creatorOrClass as new (...args: unknown[]) => Instrumentation,\n      optionsCallback,\n    );\n  }\n\n  return _generateInstrumentOnce(name, creatorOrClass as (options?: Options) => Instrumentation);\n}\n\n// The plain version without handling of options\n// Should not be used with custom options that are mutated in the creator!\nfunction _generateInstrumentOnce<Options = unknown, InstrumentationInstance extends Instrumentation = Instrumentation>(\n  name: string,\n  creator: (options?: Options) => InstrumentationInstance,\n): ((options?: Options) => InstrumentationInstance) & { id: string } {\n  return Object.assign(\n    (options?: Options) => {\n      const instrumented = INSTRUMENTED[name] as InstrumentationInstance | undefined;\n      if (instrumented) {\n        // If options are provided, ensure we update them\n        if (options) {\n          instrumented.setConfig(options);\n        }\n        return instrumented;\n      }\n\n      const instrumentation = creator(options);\n      INSTRUMENTED[name] = instrumentation;\n\n      registerInstrumentations({\n        instrumentations: [instrumentation],\n      });\n\n      return instrumentation;\n    },\n    { id: name },\n  );\n}\n\n// This version handles options properly\nfunction _generateInstrumentOnceWithOptions<\n  Options,\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  InstrumentationClass extends new (...args: any[]) => Instrumentation,\n>(\n  name: string,\n  instrumentationClass: InstrumentationClass,\n  optionsCallback: (options: Options) => ConstructorParameters<InstrumentationClass>[0],\n): ((options: Options) => InstanceType<InstrumentationClass>) & { id: string } {\n  return Object.assign(\n    (_options: Options) => {\n      const options = optionsCallback(_options);\n\n      const instrumented = INSTRUMENTED[name] as InstanceType<InstrumentationClass> | undefined;\n      if (instrumented) {\n        // Ensure we update options\n        instrumented.setConfig(options);\n        return instrumented;\n      }\n\n      const instrumentation = new instrumentationClass(options) as InstanceType<InstrumentationClass>;\n      INSTRUMENTED[name] = instrumentation;\n\n      registerInstrumentations({\n        instrumentations: [instrumentation],\n      });\n\n      return instrumentation;\n    },\n    { id: name },\n  );\n}\n\n/**\n * Ensure a given callback is called when the instrumentation is actually wrapping something.\n * This can be used to ensure some logic is only called when the instrumentation is actually active.\n *\n * This function returns a function that can be invoked with a callback.\n * This callback will either be invoked immediately\n * (e.g. if the instrumentation was already wrapped, or if _wrap could not be patched),\n * or once the instrumentation is actually wrapping something.\n *\n * Make sure to call this function right after adding the instrumentation, otherwise it may be too late!\n * The returned callback can be used any time, and also multiple times.\n */\nexport function instrumentWhenWrapped<T extends Instrumentation>(instrumentation: T): (callback: () => void) => void {\n  let isWrapped = false;\n  let callbacks: (() => void)[] = [];\n\n  if (!hasWrap(instrumentation)) {\n    isWrapped = true;\n  } else {\n    const originalWrap = instrumentation['_wrap'];\n\n    instrumentation['_wrap'] = (...args: Parameters<typeof originalWrap>) => {\n      isWrapped = true;\n      callbacks.forEach(callback => callback());\n      callbacks = [];\n      return originalWrap(...args);\n    };\n  }\n\n  const registerCallback = (callback: () => void): void => {\n    if (isWrapped) {\n      callback();\n    } else {\n      callbacks.push(callback);\n    }\n  };\n\n  return registerCallback;\n}\n\nfunction hasWrap<T extends Instrumentation>(\n  instrumentation: T,\n): instrumentation is T & { _wrap: (...args: unknown[]) => unknown } {\n  return typeof (instrumentation as T & { _wrap?: (...args: unknown[]) => unknown })['_wrap'] === 'function';\n}\n"], "names": ["instrumentation", "registerInstrumentations"], "mappings": ";;;;AAEA,6BAAA,GACO,MAAM,YAAY,GAAoC,CAAA;AAkB7D;;;CAGA,GACO,SAAS,sBAAsB,CACpC,IAAI,EACN,8DAAA;AACE,cAAc,EACd,eAAe;IAEf,IAAI,eAAe,EAAE;QACnB,OAAO,kCAAkC,CACvC,IAAI,EACJ,cAAA,EACA,eAAe;IAErB;IAEE,OAAO,uBAAuB,CAAC,IAAI,EAAE,gBAAyD;AAChG;AAEA,gDAAA;AACA,0EAAA;AACA,SAAS,uBAAuB,CAC9B,IAAI,EACJ,OAAO;IAEP,OAAO,MAAM,CAAC,MAAM,CAClB,CAAC,OAAO,KAAe;QACrB,MAAM,YAAA,GAAe,YAAY,CAAC,IAAI,CAAA;QACtC,IAAI,YAAY,EAAE;YACxB,iDAAA;YACQ,IAAI,OAAO,EAAE;gBACX,YAAY,CAAC,SAAS,CAAC,OAAO,CAAC;YACzC;YACQ,OAAO,YAAY;QAC3B;QAEM,MAAMA,iBAAA,GAAkB,OAAO,CAAC,OAAO,CAAC;QACxC,YAAY,CAAC,IAAI,CAAA,GAAIA,iBAAe;QAEpCC,gBAAAA,wBAAwB,CAAC;YACvB,gBAAgB,EAAE;gBAACD,iBAAe;aAAC;QAC3C,CAAO,CAAC;QAEF,OAAOA,iBAAe;IAC5B,CAAK,EACD;QAAE,EAAE,EAAE,IAAA;IAAA,CAAM;AAEhB;AAEA,wCAAA;AACA,SAAS,mCAKP,IAAI,EACJ,oBAAoB,EACpB,eAAe;IAEf,OAAO,MAAM,CAAC,MAAM,CAClB,CAAC,QAAQ,KAAc;QACrB,MAAM,OAAA,GAAU,eAAe,CAAC,QAAQ,CAAC;QAEzC,MAAM,YAAA,GAAe,YAAY,CAAC,IAAI,CAAA;QACtC,IAAI,YAAY,EAAE;YACxB,2BAAA;YACQ,YAAY,CAAC,SAAS,CAAC,OAAO,CAAC;YAC/B,OAAO,YAAY;QAC3B;QAEM,MAAMA,iBAAA,GAAkB,IAAI,oBAAoB,CAAC,OAAO,CAAA;QACxD,YAAY,CAAC,IAAI,CAAA,GAAIA,iBAAe;QAEpCC,gBAAAA,wBAAwB,CAAC;YACvB,gBAAgB,EAAE;gBAACD,iBAAe;aAAC;QAC3C,CAAO,CAAC;QAEF,OAAOA,iBAAe;IAC5B,CAAK,EACD;QAAE,EAAE,EAAE,IAAA;IAAA,CAAM;AAEhB;AAEA;;;;;;;;;;;CAWA,GACO,SAAS,qBAAqB,CAA4B,eAAe,EAAqC;IACnH,IAAI,SAAA,GAAY,KAAK;IACrB,IAAI,SAAS,GAAmB,EAAE;IAElC,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,EAAE;QAC7B,SAAA,GAAY,IAAI;IACpB,OAAS;QACL,MAAM,YAAA,GAAe,eAAe,CAAC,OAAO,CAAC;QAE7C,eAAe,CAAC,OAAO,CAAA,GAAI,CAAC,GAAG,IAAI,KAAsC;YACvE,SAAA,GAAY,IAAI;YAChB,SAAS,CAAC,OAAO,EAAC,WAAY,QAAQ,EAAE,CAAC;YACzC,SAAA,GAAY,EAAE;YACd,OAAO,YAAY,CAAC,GAAG,IAAI,CAAC;QAClC,CAAK;IACL;IAEE,MAAM,gBAAA,GAAmB,CAAC,QAAQ,KAAuB;QACvD,IAAI,SAAS,EAAE;YACb,QAAQ,EAAE;QAChB,OAAW;YACL,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;QAC9B;IACA,CAAG;IAED,OAAO,gBAAgB;AACzB;AAEA,SAAS,OAAO,CACd,eAAe;IAEf,OAAO,OAAO,AAAC,eAAA,CAAoE,OAAO,CAAA,KAAM,UAAU;AAC5G", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 323, "column": 0}, "map": {"version": 3, "file": "debug-build.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/debug-build.ts"], "sourcesContent": ["declare const __DEBUG_BUILD__: boolean;\n\n/**\n * This serves as a build time flag that will be true by default, but false in non-debug builds or if users replace `__SENTRY_DEBUG__` in their generated code.\n *\n * ATTENTION: This constant must never cross package boundaries (i.e. be exported) to guarantee that it can be used for tree shaking.\n */\nexport const DEBUG_BUILD = __DEBUG_BUILD__;\n"], "names": [], "mappings": ";;;AAEA;;;;CAIA,GACO,MAAM,WAAA,GAAc,OAAA,gBAAA,KAAA,WAAA,IAAA,gBAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 337, "column": 0}, "map": {"version": 3, "file": "baggage.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/utils/baggage.ts"], "sourcesContent": ["import { objectToBaggageHeader, parseBaggageHeader } from '@sentry/core';\n\n/**\n * Merge two baggage headers into one, where the existing one takes precedence.\n * The order of the existing baggage will be preserved, and new entries will be added to the end.\n */\nexport function mergeBaggageHeaders<Existing extends string | string[] | number | undefined>(\n  existing: Existing,\n  baggage: string,\n): string | undefined | Existing {\n  if (!existing) {\n    return baggage;\n  }\n\n  const existingBaggageEntries = parseBaggageHeader(existing);\n  const newBaggageEntries = parseBaggageHeader(baggage);\n\n  if (!newBaggageEntries) {\n    return existing;\n  }\n\n  // Existing entries take precedence, ensuring order remains stable for minimal changes\n  const mergedBaggageEntries = { ...existingBaggageEntries };\n  Object.entries(newBaggageEntries).forEach(([key, value]) => {\n    if (!mergedBaggageEntries[key]) {\n      mergedBaggageEntries[key] = value;\n    }\n  });\n\n  return objectToBaggageHeader(mergedBaggageEntries);\n}\n"], "names": ["parseBaggageHeader", "objectToBaggageHeader"], "mappings": ";;;;AAEA;;;CAGA,GACO,SAAS,mBAAmB,CACjC,QAAQ,EACR,OAAO;IAEP,IAAI,CAAC,QAAQ,EAAE;QACb,OAAO,OAAO;IAClB;IAEE,MAAM,sBAAA,GAAyBA,KAAAA,kBAAkB,CAAC,QAAQ,CAAC;IAC3D,MAAM,iBAAA,GAAoBA,KAAAA,kBAAkB,CAAC,OAAO,CAAC;IAErD,IAAI,CAAC,iBAAiB,EAAE;QACtB,OAAO,QAAQ;IACnB;IAEA,sFAAA;IACE,MAAM,oBAAA,GAAuB;QAAE,GAAG,sBAAA;IAAA,CAAwB;IAC1D,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK;QAC1D,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,EAAE;YAC9B,oBAAoB,CAAC,GAAG,CAAA,GAAI,KAAK;QACvC;IACA,CAAG,CAAC;IAEF,OAAOC,KAAAA,qBAAqB,CAAC,oBAAoB,CAAC;AACpD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 370, "column": 0}, "map": {"version": 3, "file": "getRequestUrl.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/utils/getRequestUrl.ts"], "sourcesContent": ["import type { RequestOptions } from 'node:http';\n\n/** Build a full URL from request options. */\nexport function getRequestUrl(requestOptions: RequestOptions): string {\n  const protocol = requestOptions.protocol || '';\n  const hostname = requestOptions.hostname || requestOptions.host || '';\n  // Don't log standard :80 (http) and :443 (https) ports to reduce the noise\n  // Also don't add port if the hostname already includes a port\n  const port =\n    !requestOptions.port || requestOptions.port === 80 || requestOptions.port === 443 || /^(.*):(\\d+)$/.test(hostname)\n      ? ''\n      : `:${requestOptions.port}`;\n  const path = requestOptions.path ? requestOptions.path : '/';\n  return `${protocol}//${hostname}${port}${path}`;\n}\n"], "names": [], "mappings": ";;;AAEA,2CAAA,GACO,SAAS,aAAa,CAAC,cAAc,EAA0B;IACpE,MAAM,QAAA,GAAW,cAAc,CAAC,QAAA,IAAY,EAAE;IAC9C,MAAM,QAAA,GAAW,cAAc,CAAC,QAAA,IAAY,cAAc,CAAC,IAAA,IAAQ,EAAE;IACvE,2EAAA;IACA,8DAAA;IACE,MAAM,IAAA,GACJ,CAAC,cAAc,CAAC,IAAA,IAAQ,cAAc,CAAC,IAAA,KAAS,EAAA,IAAM,cAAc,CAAC,IAAA,KAAS,GAAA,IAAO,cAAc,CAAC,IAAI,CAAC,QAAQ,IAC7G,KACA,CAAC,CAAC,EAAE,cAAc,CAAC,IAAI,CAAC,CAAA;IACA,MAAA,IAAA,GAAA,cAAA,CAAA,IAAA,GAAA,cAAA,CAAA,IAAA,GAAA,GAAA;IACA,OAAA,CAAA,EAAA,QAAA,CAAA,EAAA,EAAA,QAAA,CAAA,EAAA,IAAA,CAAA,EAAA,IAAA,CAAA,CAAA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 388, "column": 0}, "map": {"version": 3, "file": "SentryHttpInstrumentation.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/integrations/http/SentryHttpInstrumentation.ts"], "sourcesContent": ["/* eslint-disable max-lines */\nimport type { ChannelListener } from 'node:diagnostics_channel';\nimport { subscribe, unsubscribe } from 'node:diagnostics_channel';\nimport type * as http from 'node:http';\nimport type * as https from 'node:https';\nimport type { EventEmitter } from 'node:stream';\nimport { context, propagation } from '@opentelemetry/api';\nimport { isTracingSuppressed } from '@opentelemetry/core';\nimport type { InstrumentationConfig } from '@opentelemetry/instrumentation';\nimport { InstrumentationBase, InstrumentationNodeModuleDefinition } from '@opentelemetry/instrumentation';\nimport type { AggregationCounts, Client, SanitizedRequestData, Scope } from '@sentry/core';\nimport {\n  addBreadcrumb,\n  addNonEnumerableProperty,\n  debug,\n  generateSpanId,\n  getBreadcrumbLogLevelFromHttpStatusCode,\n  getClient,\n  getCurrentScope,\n  getIsolationScope,\n  getSanitizedUrlString,\n  getTraceData,\n  httpRequestToRequestData,\n  isError,\n  LRUMap,\n  parseUrl,\n  SDK_VERSION,\n  stripUrlQueryAndFragment,\n  withIsolationScope,\n} from '@sentry/core';\nimport { shouldPropagateTraceForUrl } from '@sentry/opentelemetry';\nimport { DEBUG_BUILD } from '../../debug-build';\nimport { mergeBaggageHeaders } from '../../utils/baggage';\nimport { getRequestUrl } from '../../utils/getRequestUrl';\n\nconst INSTRUMENTATION_NAME = '@sentry/instrumentation-http';\n\ntype Http = typeof http;\ntype Https = typeof https;\n\nexport type SentryHttpInstrumentationOptions = InstrumentationConfig & {\n  /**\n   * Whether breadcrumbs should be recorded for requests.\n   *\n   * @default `true`\n   */\n  breadcrumbs?: boolean;\n\n  /**\n   * Whether to extract the trace ID from the `sentry-trace` header for incoming requests.\n   * By default this is done by the HttpInstrumentation, but if that is not added (e.g. because tracing is disabled, ...)\n   * then this instrumentation can take over.\n   *\n   * @default `false`\n   */\n  extractIncomingTraceFromHeader?: boolean;\n\n  /**\n   * Whether to propagate Sentry trace headers in outgoing requests.\n   * By default this is done by the HttpInstrumentation, but if that is not added (e.g. because tracing is disabled)\n   * then this instrumentation can take over.\n   *\n   * @default `false`\n   */\n  propagateTraceInOutgoingRequests?: boolean;\n\n  /**\n   * Do not capture breadcrumbs for outgoing HTTP requests to URLs where the given callback returns `true`.\n   * For the scope of this instrumentation, this callback only controls breadcrumb creation.\n   * The same option can be passed to the top-level httpIntegration where it controls both, breadcrumb and\n   * span creation.\n   *\n   * @param url Contains the entire URL, including query string (if any), protocol, host, etc. of the outgoing request.\n   * @param request Contains the {@type RequestOptions} object used to make the outgoing request.\n   */\n  ignoreOutgoingRequests?: (url: string, request: http.RequestOptions) => boolean;\n\n  /**\n   * Do not capture the request body for incoming HTTP requests to URLs where the given callback returns `true`.\n   * This can be useful for long running requests where the body is not needed and we want to avoid capturing it.\n   *\n   * @param url Contains the entire URL, including query string (if any), protocol, host, etc. of the incoming request.\n   * @param request Contains the {@type RequestOptions} object used to make the incoming request.\n   */\n  ignoreIncomingRequestBody?: (url: string, request: http.RequestOptions) => boolean;\n\n  /**\n   * Controls the maximum size of incoming HTTP request bodies attached to events.\n   *\n   * Available options:\n   * - 'none': No request bodies will be attached\n   * - 'small': Request bodies up to 1,000 bytes will be attached\n   * - 'medium': Request bodies up to 10,000 bytes will be attached (default)\n   * - 'always': Request bodies will always be attached\n   *\n   * Note that even with 'always' setting, bodies exceeding 1MB will never be attached\n   * for performance and security reasons.\n   *\n   * @default 'medium'\n   */\n  maxIncomingRequestBodySize?: 'none' | 'small' | 'medium' | 'always';\n\n  /**\n   * Whether the integration should create [Sessions](https://docs.sentry.io/product/releases/health/#sessions) for incoming requests to track the health and crash-free rate of your releases in Sentry.\n   * Read more about Release Health: https://docs.sentry.io/product/releases/health/\n   *\n   * Defaults to `true`.\n   */\n  trackIncomingRequestsAsSessions?: boolean;\n\n  /**\n   * Number of milliseconds until sessions tracked with `trackIncomingRequestsAsSessions` will be flushed as a session aggregate.\n   *\n   * Defaults to `60000` (60s).\n   */\n  sessionFlushingDelayMS?: number;\n};\n\n// We only want to capture request bodies up to 1mb.\nconst MAX_BODY_BYTE_LENGTH = 1024 * 1024;\n\n/**\n * This custom HTTP instrumentation is used to isolate incoming requests and annotate them with additional information.\n * It does not emit any spans.\n *\n * The reason this is isolated from the OpenTelemetry instrumentation is that users may overwrite this,\n * which would lead to Sentry not working as expected.\n *\n * Important note: Contrary to other OTEL instrumentation, this one cannot be unwrapped.\n * It only does minimal things though and does not emit any spans.\n *\n * This is heavily inspired & adapted from:\n * https://github.com/open-telemetry/opentelemetry-js/blob/f8ab5592ddea5cba0a3b33bf8d74f27872c0367f/experimental/packages/opentelemetry-instrumentation-http/src/http.ts\n */\nexport class SentryHttpInstrumentation extends InstrumentationBase<SentryHttpInstrumentationOptions> {\n  private _propagationDecisionMap: LRUMap<string, boolean>;\n  private _ignoreOutgoingRequestsMap: WeakMap<http.ClientRequest, boolean>;\n\n  public constructor(config: SentryHttpInstrumentationOptions = {}) {\n    super(INSTRUMENTATION_NAME, SDK_VERSION, config);\n\n    this._propagationDecisionMap = new LRUMap<string, boolean>(100);\n    this._ignoreOutgoingRequestsMap = new WeakMap<http.ClientRequest, boolean>();\n  }\n\n  /** @inheritdoc */\n  public init(): [InstrumentationNodeModuleDefinition, InstrumentationNodeModuleDefinition] {\n    // We register handlers when either http or https is instrumented\n    // but we only want to register them once, whichever is loaded first\n    let hasRegisteredHandlers = false;\n\n    const onHttpServerRequestStart = ((_data: unknown) => {\n      const data = _data as { server: http.Server };\n      this._patchServerEmitOnce(data.server);\n    }) satisfies ChannelListener;\n\n    const onHttpClientResponseFinish = ((_data: unknown) => {\n      const data = _data as { request: http.ClientRequest; response: http.IncomingMessage };\n      this._onOutgoingRequestFinish(data.request, data.response);\n    }) satisfies ChannelListener;\n\n    const onHttpClientRequestError = ((_data: unknown) => {\n      const data = _data as { request: http.ClientRequest };\n      this._onOutgoingRequestFinish(data.request, undefined);\n    }) satisfies ChannelListener;\n\n    const onHttpClientRequestCreated = ((_data: unknown) => {\n      const data = _data as { request: http.ClientRequest };\n      this._onOutgoingRequestCreated(data.request);\n    }) satisfies ChannelListener;\n\n    const wrap = <T extends Http | Https>(moduleExports: T): T => {\n      if (hasRegisteredHandlers) {\n        return moduleExports;\n      }\n\n      hasRegisteredHandlers = true;\n\n      subscribe('http.server.request.start', onHttpServerRequestStart);\n      subscribe('http.client.response.finish', onHttpClientResponseFinish);\n\n      // When an error happens, we still want to have a breadcrumb\n      // In this case, `http.client.response.finish` is not triggered\n      subscribe('http.client.request.error', onHttpClientRequestError);\n\n      // NOTE: This channel only exist since Node 22\n      // Before that, outgoing requests are not patched\n      // and trace headers are not propagated, sadly.\n      if (this.getConfig().propagateTraceInOutgoingRequests) {\n        subscribe('http.client.request.created', onHttpClientRequestCreated);\n      }\n\n      return moduleExports;\n    };\n\n    const unwrap = (): void => {\n      unsubscribe('http.server.request.start', onHttpServerRequestStart);\n      unsubscribe('http.client.response.finish', onHttpClientResponseFinish);\n      unsubscribe('http.client.request.error', onHttpClientRequestError);\n      unsubscribe('http.client.request.created', onHttpClientRequestCreated);\n    };\n\n    /**\n     * You may be wondering why we register these diagnostics-channel listeners\n     * in such a convoluted way (as InstrumentationNodeModuleDefinition...)˝,\n     * instead of simply subscribing to the events once in here.\n     * The reason for this is timing semantics: These functions are called once the http or https module is loaded.\n     * If we'd subscribe before that, there seem to be conflicts with the OTEL native instrumentation in some scenarios,\n     * especially the \"import-on-top\" pattern of setting up ESM applications.\n     */\n    return [\n      new InstrumentationNodeModuleDefinition('http', ['*'], wrap, unwrap),\n      new InstrumentationNodeModuleDefinition('https', ['*'], wrap, unwrap),\n    ];\n  }\n\n  /**\n   * This is triggered when an outgoing request finishes.\n   * It has access to the final request and response objects.\n   */\n  private _onOutgoingRequestFinish(request: http.ClientRequest, response?: http.IncomingMessage): void {\n    DEBUG_BUILD && debug.log(INSTRUMENTATION_NAME, 'Handling finished outgoing request');\n\n    const _breadcrumbs = this.getConfig().breadcrumbs;\n    const breadCrumbsEnabled = typeof _breadcrumbs === 'undefined' ? true : _breadcrumbs;\n\n    // Note: We cannot rely on the map being set by `_onOutgoingRequestCreated`, because that is not run in Node <22\n    const shouldIgnore = this._ignoreOutgoingRequestsMap.get(request) ?? this._shouldIgnoreOutgoingRequest(request);\n    this._ignoreOutgoingRequestsMap.set(request, shouldIgnore);\n\n    if (breadCrumbsEnabled && !shouldIgnore) {\n      addRequestBreadcrumb(request, response);\n    }\n  }\n\n  /**\n   * This is triggered when an outgoing request is created.\n   * It has access to the request object, and can mutate it before the request is sent.\n   */\n  private _onOutgoingRequestCreated(request: http.ClientRequest): void {\n    const shouldIgnore = this._ignoreOutgoingRequestsMap.get(request) ?? this._shouldIgnoreOutgoingRequest(request);\n    this._ignoreOutgoingRequestsMap.set(request, shouldIgnore);\n\n    if (shouldIgnore) {\n      return;\n    }\n\n    // Add trace propagation headers\n    const url = getRequestUrl(request);\n\n    // Manually add the trace headers, if it applies\n    // Note: We do not use `propagation.inject()` here, because our propagator relies on an active span\n    // Which we do not have in this case\n    const tracePropagationTargets = getClient()?.getOptions().tracePropagationTargets;\n    const addedHeaders = shouldPropagateTraceForUrl(url, tracePropagationTargets, this._propagationDecisionMap)\n      ? getTraceData()\n      : undefined;\n\n    if (!addedHeaders) {\n      return;\n    }\n\n    const { 'sentry-trace': sentryTrace, baggage } = addedHeaders;\n\n    // We do not want to overwrite existing header here, if it was already set\n    if (sentryTrace && !request.getHeader('sentry-trace')) {\n      try {\n        request.setHeader('sentry-trace', sentryTrace);\n        DEBUG_BUILD && debug.log(INSTRUMENTATION_NAME, 'Added sentry-trace header to outgoing request');\n      } catch (error) {\n        DEBUG_BUILD &&\n          debug.error(\n            INSTRUMENTATION_NAME,\n            'Failed to add sentry-trace header to outgoing request:',\n            isError(error) ? error.message : 'Unknown error',\n          );\n      }\n    }\n\n    if (baggage) {\n      // For baggage, we make sure to merge this into a possibly existing header\n      const newBaggage = mergeBaggageHeaders(request.getHeader('baggage'), baggage);\n      if (newBaggage) {\n        try {\n          request.setHeader('baggage', newBaggage);\n          DEBUG_BUILD && debug.log(INSTRUMENTATION_NAME, 'Added baggage header to outgoing request');\n        } catch (error) {\n          DEBUG_BUILD &&\n            debug.error(\n              INSTRUMENTATION_NAME,\n              'Failed to add baggage header to outgoing request:',\n              isError(error) ? error.message : 'Unknown error',\n            );\n        }\n      }\n    }\n  }\n\n  /**\n   * Patch a server.emit function to handle process isolation for incoming requests.\n   * This will only patch the emit function if it was not already patched.\n   */\n  private _patchServerEmitOnce(server: http.Server): void {\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    const originalEmit = server.emit;\n\n    // This means it was already patched, do nothing\n    if ((originalEmit as { __sentry_patched__?: boolean }).__sentry_patched__) {\n      return;\n    }\n\n    DEBUG_BUILD && debug.log(INSTRUMENTATION_NAME, 'Patching server.emit');\n\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    const instrumentation = this;\n    const { ignoreIncomingRequestBody, maxIncomingRequestBodySize = 'medium' } = instrumentation.getConfig();\n\n    const newEmit = new Proxy(originalEmit, {\n      apply(target, thisArg, args: [event: string, ...args: unknown[]]) {\n        // Only traces request events\n        if (args[0] !== 'request') {\n          return target.apply(thisArg, args);\n        }\n\n        DEBUG_BUILD && debug.log(INSTRUMENTATION_NAME, 'Handling incoming request');\n\n        const isolationScope = getIsolationScope().clone();\n        const request = args[1] as http.IncomingMessage;\n        const response = args[2] as http.OutgoingMessage;\n\n        const normalizedRequest = httpRequestToRequestData(request);\n\n        // request.ip is non-standard but some frameworks set this\n        const ipAddress = (request as { ip?: string }).ip || request.socket?.remoteAddress;\n\n        const url = request.url || '/';\n        if (!ignoreIncomingRequestBody?.(url, request) && maxIncomingRequestBodySize !== 'none') {\n          patchRequestToCaptureBody(request, isolationScope, maxIncomingRequestBodySize);\n        }\n\n        // Update the isolation scope, isolate this request\n        isolationScope.setSDKProcessingMetadata({ normalizedRequest, ipAddress });\n\n        // attempt to update the scope's `transactionName` based on the request URL\n        // Ideally, framework instrumentations coming after the HttpInstrumentation\n        // update the transactionName once we get a parameterized route.\n        const httpMethod = (request.method || 'GET').toUpperCase();\n        const httpTarget = stripUrlQueryAndFragment(url);\n\n        const bestEffortTransactionName = `${httpMethod} ${httpTarget}`;\n\n        isolationScope.setTransactionName(bestEffortTransactionName);\n\n        if (instrumentation.getConfig().trackIncomingRequestsAsSessions !== false) {\n          recordRequestSession({\n            requestIsolationScope: isolationScope,\n            response,\n            sessionFlushingDelayMS: instrumentation.getConfig().sessionFlushingDelayMS ?? 60_000,\n          });\n        }\n\n        return withIsolationScope(isolationScope, () => {\n          // Set a new propagationSpanId for this request\n          // We rely on the fact that `withIsolationScope()` will implicitly also fork the current scope\n          // This way we can save an \"unnecessary\" `withScope()` invocation\n          getCurrentScope().getPropagationContext().propagationSpanId = generateSpanId();\n\n          // If we don't want to extract the trace from the header, we can skip this\n          if (!instrumentation.getConfig().extractIncomingTraceFromHeader) {\n            return target.apply(thisArg, args);\n          }\n\n          const ctx = propagation.extract(context.active(), normalizedRequest.headers);\n          return context.with(ctx, () => {\n            return target.apply(thisArg, args);\n          });\n        });\n      },\n    });\n\n    addNonEnumerableProperty(newEmit, '__sentry_patched__', true);\n\n    server.emit = newEmit;\n  }\n\n  /**\n   * Check if the given outgoing request should be ignored.\n   */\n  private _shouldIgnoreOutgoingRequest(request: http.ClientRequest): boolean {\n    if (isTracingSuppressed(context.active())) {\n      return true;\n    }\n\n    const ignoreOutgoingRequests = this.getConfig().ignoreOutgoingRequests;\n\n    if (!ignoreOutgoingRequests) {\n      return false;\n    }\n\n    const options = getRequestOptions(request);\n    const url = getRequestUrl(request);\n    return ignoreOutgoingRequests(url, options);\n  }\n}\n\n/** Add a breadcrumb for outgoing requests. */\nfunction addRequestBreadcrumb(request: http.ClientRequest, response: http.IncomingMessage | undefined): void {\n  const data = getBreadcrumbData(request);\n\n  const statusCode = response?.statusCode;\n  const level = getBreadcrumbLogLevelFromHttpStatusCode(statusCode);\n\n  addBreadcrumb(\n    {\n      category: 'http',\n      data: {\n        status_code: statusCode,\n        ...data,\n      },\n      type: 'http',\n      level,\n    },\n    {\n      event: 'response',\n      request,\n      response,\n    },\n  );\n}\n\nfunction getBreadcrumbData(request: http.ClientRequest): Partial<SanitizedRequestData> {\n  try {\n    // `request.host` does not contain the port, but the host header does\n    const host = request.getHeader('host') || request.host;\n    const url = new URL(request.path, `${request.protocol}//${host}`);\n    const parsedUrl = parseUrl(url.toString());\n\n    const data: Partial<SanitizedRequestData> = {\n      url: getSanitizedUrlString(parsedUrl),\n      'http.method': request.method || 'GET',\n    };\n\n    if (parsedUrl.search) {\n      data['http.query'] = parsedUrl.search;\n    }\n    if (parsedUrl.hash) {\n      data['http.fragment'] = parsedUrl.hash;\n    }\n\n    return data;\n  } catch {\n    return {};\n  }\n}\n\n/**\n * This method patches the request object to capture the body.\n * Instead of actually consuming the streamed body ourselves, which has potential side effects,\n * we monkey patch `req.on('data')` to intercept the body chunks.\n * This way, we only read the body if the user also consumes the body, ensuring we do not change any behavior in unexpected ways.\n */\nfunction patchRequestToCaptureBody(\n  req: http.IncomingMessage,\n  isolationScope: Scope,\n  maxIncomingRequestBodySize: 'small' | 'medium' | 'always',\n): void {\n  let bodyByteLength = 0;\n  const chunks: Buffer[] = [];\n\n  DEBUG_BUILD && debug.log(INSTRUMENTATION_NAME, 'Patching request.on');\n\n  /**\n   * We need to keep track of the original callbacks, in order to be able to remove listeners again.\n   * Since `off` depends on having the exact same function reference passed in, we need to be able to map\n   * original listeners to our wrapped ones.\n   */\n  const callbackMap = new WeakMap();\n\n  const maxBodySize =\n    maxIncomingRequestBodySize === 'small'\n      ? 1_000\n      : maxIncomingRequestBodySize === 'medium'\n        ? 10_000\n        : MAX_BODY_BYTE_LENGTH;\n\n  try {\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    req.on = new Proxy(req.on, {\n      apply: (target, thisArg, args: Parameters<typeof req.on>) => {\n        const [event, listener, ...restArgs] = args;\n\n        if (event === 'data') {\n          DEBUG_BUILD &&\n            debug.log(INSTRUMENTATION_NAME, `Handling request.on(\"data\") with maximum body size of ${maxBodySize}b`);\n\n          const callback = new Proxy(listener, {\n            apply: (target, thisArg, args: Parameters<typeof listener>) => {\n              try {\n                const chunk = args[0] as Buffer | string;\n                const bufferifiedChunk = Buffer.from(chunk);\n\n                if (bodyByteLength < maxBodySize) {\n                  chunks.push(bufferifiedChunk);\n                  bodyByteLength += bufferifiedChunk.byteLength;\n                } else if (DEBUG_BUILD) {\n                  debug.log(\n                    INSTRUMENTATION_NAME,\n                    `Dropping request body chunk because maximum body length of ${maxBodySize}b is exceeded.`,\n                  );\n                }\n              } catch (err) {\n                DEBUG_BUILD && debug.error(INSTRUMENTATION_NAME, 'Encountered error while storing body chunk.');\n              }\n\n              return Reflect.apply(target, thisArg, args);\n            },\n          });\n\n          callbackMap.set(listener, callback);\n\n          return Reflect.apply(target, thisArg, [event, callback, ...restArgs]);\n        }\n\n        return Reflect.apply(target, thisArg, args);\n      },\n    });\n\n    // Ensure we also remove callbacks correctly\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    req.off = new Proxy(req.off, {\n      apply: (target, thisArg, args: Parameters<typeof req.off>) => {\n        const [, listener] = args;\n\n        const callback = callbackMap.get(listener);\n        if (callback) {\n          callbackMap.delete(listener);\n\n          const modifiedArgs = args.slice();\n          modifiedArgs[1] = callback;\n          return Reflect.apply(target, thisArg, modifiedArgs);\n        }\n\n        return Reflect.apply(target, thisArg, args);\n      },\n    });\n\n    req.on('end', () => {\n      try {\n        const body = Buffer.concat(chunks).toString('utf-8');\n        if (body) {\n          // Using Buffer.byteLength here, because the body may contain characters that are not 1 byte long\n          const bodyByteLength = Buffer.byteLength(body, 'utf-8');\n          const truncatedBody =\n            bodyByteLength > maxBodySize\n              ? `${Buffer.from(body)\n                  .subarray(0, maxBodySize - 3)\n                  .toString('utf-8')}...`\n              : body;\n\n          isolationScope.setSDKProcessingMetadata({ normalizedRequest: { data: truncatedBody } });\n        }\n      } catch (error) {\n        if (DEBUG_BUILD) {\n          debug.error(INSTRUMENTATION_NAME, 'Error building captured request body', error);\n        }\n      }\n    });\n  } catch (error) {\n    if (DEBUG_BUILD) {\n      debug.error(INSTRUMENTATION_NAME, 'Error patching request to capture body', error);\n    }\n  }\n}\n\nfunction getRequestOptions(request: http.ClientRequest): http.RequestOptions {\n  return {\n    method: request.method,\n    protocol: request.protocol,\n    host: request.host,\n    hostname: request.host,\n    path: request.path,\n    headers: request.getHeaders(),\n  };\n}\n\n/**\n * Starts a session and tracks it in the context of a given isolation scope.\n * When the passed response is finished, the session is put into a task and is\n * aggregated with other sessions that may happen in a certain time window\n * (sessionFlushingDelayMs).\n *\n * The sessions are always aggregated by the client that is on the current scope\n * at the time of ending the response (if there is one).\n */\n// Exported for unit tests\nexport function recordRequestSession({\n  requestIsolationScope,\n  response,\n  sessionFlushingDelayMS,\n}: {\n  requestIsolationScope: Scope;\n  response: EventEmitter;\n  sessionFlushingDelayMS?: number;\n}): void {\n  requestIsolationScope.setSDKProcessingMetadata({\n    requestSession: { status: 'ok' },\n  });\n  response.once('close', () => {\n    // We need to grab the client off the current scope instead of the isolation scope because the isolation scope doesn't hold any client out of the box.\n    const client = getClient();\n    const requestSession = requestIsolationScope.getScopeData().sdkProcessingMetadata.requestSession;\n\n    if (client && requestSession) {\n      DEBUG_BUILD && debug.log(`Recorded request session with status: ${requestSession.status}`);\n\n      const roundedDate = new Date();\n      roundedDate.setSeconds(0, 0);\n      const dateBucketKey = roundedDate.toISOString();\n\n      const existingClientAggregate = clientToRequestSessionAggregatesMap.get(client);\n      const bucket = existingClientAggregate?.[dateBucketKey] || { exited: 0, crashed: 0, errored: 0 };\n      bucket[({ ok: 'exited', crashed: 'crashed', errored: 'errored' } as const)[requestSession.status]]++;\n\n      if (existingClientAggregate) {\n        existingClientAggregate[dateBucketKey] = bucket;\n      } else {\n        DEBUG_BUILD && debug.log('Opened new request session aggregate.');\n        const newClientAggregate = { [dateBucketKey]: bucket };\n        clientToRequestSessionAggregatesMap.set(client, newClientAggregate);\n\n        const flushPendingClientAggregates = (): void => {\n          clearTimeout(timeout);\n          unregisterClientFlushHook();\n          clientToRequestSessionAggregatesMap.delete(client);\n\n          const aggregatePayload: AggregationCounts[] = Object.entries(newClientAggregate).map(\n            ([timestamp, value]) => ({\n              started: timestamp,\n              exited: value.exited,\n              errored: value.errored,\n              crashed: value.crashed,\n            }),\n          );\n          client.sendSession({ aggregates: aggregatePayload });\n        };\n\n        const unregisterClientFlushHook = client.on('flush', () => {\n          DEBUG_BUILD && debug.log('Sending request session aggregate due to client flush');\n          flushPendingClientAggregates();\n        });\n        const timeout = setTimeout(() => {\n          DEBUG_BUILD && debug.log('Sending request session aggregate due to flushing schedule');\n          flushPendingClientAggregates();\n        }, sessionFlushingDelayMS).unref();\n      }\n    }\n  });\n}\n\nconst clientToRequestSessionAggregatesMap = new Map<\n  Client,\n  { [timestampRoundedToSeconds: string]: { exited: number; crashed: number; errored: number } }\n>();\n"], "names": ["InstrumentationBase", "SDK_VERSION", "LRUMap", "subscribe", "unsubscribe", "InstrumentationNodeModuleDefinition", "DEBUG_BUILD", "debug", "getRequestUrl", "getClient", "shouldPropagateTraceForUrl", "getTraceData", "baggage", "isError", "mergeBaggageHeaders", "getIsolationScope", "httpRequestToRequestData", "stripUrlQueryAndFragment", "withIsolationScope", "getCurrentScope", "generateSpanId", "propagation", "context", "addNonEnumerableProperty", "isTracingSuppressed", "getBreadcrumbLogLevelFromHttpStatusCode", "addBreadcrumb", "parseUrl", "getSanitizedUrlString"], "mappings": ";;;;;;;;;;;;AAmCA,MAAM,oBAAA,GAAuB,8BAA8B;AAmF3D,oDAAA;AACA,MAAM,oBAAA,GAAuB,IAAA,GAAO,IAAI;AAExC;;;;;;;;;;;;CAYA,GACO,MAAM,yBAAA,SAAkCA,gBAAAA,mBAAmB,CAAmC;IAI5F,WAAW,CAAC,MAAM,GAAqC,CAAA,CAAE,CAAE;QAChE,KAAK,CAAC,oBAAoB,EAAEC,KAAAA,WAAW,EAAE,MAAM,CAAC;QAEhD,IAAI,CAAC,uBAAA,GAA0B,IAAIC,KAAAA,MAAM,CAAkB,GAAG,CAAC;QAC/D,IAAI,CAAC,0BAAA,GAA6B,IAAI,OAAO,EAA+B;IAChF;IAEA,gBAAA,GACS,IAAI,GAA+E;QAC5F,iEAAA;QACA,oEAAA;QACI,IAAI,qBAAA,GAAwB,KAAK;QAEjC,MAAM,wBAAA,GAA4B,CAAC,KAAK,KAAc;YACpD,MAAM,IAAA,GAAO,KAAA;YACb,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,CAAC;QAC5C,CAAK,CAAA;QAED,MAAM,0BAAA,GAA8B,CAAC,KAAK,KAAc;YACtD,MAAM,IAAA,GAAO,KAAA;YACb,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC;QAChE,CAAK,CAAA;QAED,MAAM,wBAAA,GAA4B,CAAC,KAAK,KAAc;YACpD,MAAM,IAAA,GAAO,KAAA;YACb,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC;QAC5D,CAAK,CAAA;QAED,MAAM,0BAAA,GAA8B,CAAC,KAAK,KAAc;YACtD,MAAM,IAAA,GAAO,KAAA;YACb,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,OAAO,CAAC;QAClD,CAAK,CAAA;QAED,MAAM,IAAA,GAAO,CAAyB,aAAa,KAAW;YAC5D,IAAI,qBAAqB,EAAE;gBACzB,OAAO,aAAa;YAC5B;YAEM,qBAAA,GAAwB,IAAI;YAE5BC,mBAAAA,SAAS,CAAC,2BAA2B,EAAE,wBAAwB,CAAC;YAChEA,mBAAAA,SAAS,CAAC,6BAA6B,EAAE,0BAA0B,CAAC;YAE1E,4DAAA;YACA,+DAAA;YACMA,mBAAAA,SAAS,CAAC,2BAA2B,EAAE,wBAAwB,CAAC;YAEtE,8CAAA;YACA,iDAAA;YACA,+CAAA;YACM,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC,gCAAgC,EAAE;gBACrDA,mBAAAA,SAAS,CAAC,6BAA6B,EAAE,0BAA0B,CAAC;YAC5E;YAEM,OAAO,aAAa;QAC1B,CAAK;QAED,MAAM,MAAA,GAAS,MAAY;YACzBC,mBAAAA,WAAW,CAAC,2BAA2B,EAAE,wBAAwB,CAAC;YAClEA,mBAAAA,WAAW,CAAC,6BAA6B,EAAE,0BAA0B,CAAC;YACtEA,mBAAAA,WAAW,CAAC,2BAA2B,EAAE,wBAAwB,CAAC;YAClEA,mBAAAA,WAAW,CAAC,6BAA6B,EAAE,0BAA0B,CAAC;QAC5E,CAAK;QAEL;;;;;;;KAOA,GACI,OAAO;YACL,IAAIC,gBAAAA,mCAAmC,CAAC,MAAM,EAAE;gBAAC,GAAG;aAAC,EAAE,IAAI,EAAE,MAAM,CAAC;YACpE,IAAIA,gBAAAA,mCAAmC,CAAC,OAAO,EAAE;gBAAC,GAAG;aAAC,EAAE,IAAI,EAAE,MAAM,CAAC;SACtE;IACL;IAEA;;;GAGA,GACU,wBAAwB,CAAC,OAAO,EAAsB,QAAQ,EAA+B;QACnGC,WAAAA,WAAA,IAAeC,KAAAA,KAAK,CAAC,GAAG,CAAC,oBAAoB,EAAE,oCAAoC,CAAC;QAEpF,MAAM,eAAe,IAAI,CAAC,SAAS,EAAE,CAAC,WAAW;QACjD,MAAM,kBAAA,GAAqB,OAAO,YAAA,KAAiB,WAAA,GAAc,IAAA,GAAO,YAAY;QAExF,gHAAA;QACI,MAAM,YAAA,GAAe,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,OAAO,KAAK,IAAI,CAAC,4BAA4B,CAAC,OAAO,CAAC;QAC/G,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,OAAO,EAAE,YAAY,CAAC;QAE1D,IAAI,kBAAA,IAAsB,CAAC,YAAY,EAAE;YACvC,oBAAoB,CAAC,OAAO,EAAE,QAAQ,CAAC;QAC7C;IACA;IAEA;;;GAGA,GACU,yBAAyB,CAAC,OAAO,EAA4B;QACnE,MAAM,YAAA,GAAe,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,OAAO,KAAK,IAAI,CAAC,4BAA4B,CAAC,OAAO,CAAC;QAC/G,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,OAAO,EAAE,YAAY,CAAC;QAE1D,IAAI,YAAY,EAAE;YAChB;QACN;QAEA,gCAAA;QACI,MAAM,GAAA,GAAMC,cAAAA,aAAa,CAAC,OAAO,CAAC;QAEtC,gDAAA;QACA,mGAAA;QACA,oCAAA;QACI,MAAM,uBAAA,GAA0BC,KAAAA,SAAS,EAAE,EAAE,UAAU,EAAE,CAAC,uBAAuB;QACjF,MAAM,YAAA,GAAeC,cAAAA,0BAA0B,CAAC,GAAG,EAAE,uBAAuB,EAAE,IAAI,CAAC,uBAAuB,IACtGC,KAAAA,YAAY,KACZ,SAAS;QAEb,IAAI,CAAC,YAAY,EAAE;YACjB;QACN;QAEI,MAAM,EAAE,cAAc,EAAE,WAAW,EAAA,SAAEC,SAAA,EAAQ,GAAI,YAAY;QAEjE,0EAAA;QACI,IAAI,WAAA,IAAe,CAAC,OAAO,CAAC,SAAS,CAAC,cAAc,CAAC,EAAE;YACrD,IAAI;gBACF,OAAO,CAAC,SAAS,CAAC,cAAc,EAAE,WAAW,CAAC;gBAC9CN,WAAAA,WAAA,IAAeC,KAAAA,KAAK,CAAC,GAAG,CAAC,oBAAoB,EAAE,+CAA+C,CAAC;YACvG,CAAM,CAAE,OAAO,KAAK,EAAE;gBACdD,WAAAA,WAAA,IACEC,KAAAA,KAAK,CAAC,KAAK,CACT,oBAAoB,EACpB,wDAAwD,EACxDM,KAAAA,OAAO,CAAC,KAAK,CAAA,GAAI,KAAK,CAAC,OAAA,GAAU,eAAe;YAE5D;QACA;QAEI,IAAID,SAAO,EAAE;YACjB,0EAAA;YACM,MAAM,UAAA,GAAaE,QAAAA,mBAAmB,CAAC,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC,EAAEF,SAAO,CAAC;YAC7E,IAAI,UAAU,EAAE;gBACd,IAAI;oBACF,OAAO,CAAC,SAAS,CAAC,SAAS,EAAE,UAAU,CAAC;oBACxCN,WAAAA,WAAA,IAAeC,KAAAA,KAAK,CAAC,GAAG,CAAC,oBAAoB,EAAE,0CAA0C,CAAC;gBACpG,CAAQ,CAAE,OAAO,KAAK,EAAE;oBACdD,WAAAA,WAAA,IACEC,KAAAA,KAAK,CAAC,KAAK,CACT,oBAAoB,EACpB,mDAAmD,EACnDM,KAAAA,OAAO,CAAC,KAAK,CAAA,GAAI,KAAK,CAAC,OAAA,GAAU,eAAe;gBAE9D;YACA;QACA;IACA;IAEA;;;GAGA,GACU,oBAAoB,CAAC,MAAM,EAAqB;QAC1D,6DAAA;QACI,MAAM,YAAA,GAAe,MAAM,CAAC,IAAI;QAEpC,gDAAA;QACI,IAAI,AAAC,aAAkD,kBAAkB,EAAE;YACzE;QACN;QAEIP,WAAAA,WAAA,IAAeC,KAAAA,KAAK,CAAC,GAAG,CAAC,oBAAoB,EAAE,sBAAsB,CAAC;QAE1E,4DAAA;QACI,MAAM,eAAA,GAAkB,IAAI;QAC5B,MAAM,EAAE,yBAAyB,EAAE,0BAAA,GAA6B,QAAA,EAAS,GAAI,eAAe,CAAC,SAAS,EAAE;QAExG,MAAM,OAAA,GAAU,IAAI,KAAK,CAAC,YAAY,EAAE;YACtC,KAAK,EAAC,MAAM,EAAE,OAAO,EAAE,IAAI,EAAuC;gBACxE,6BAAA;gBACQ,IAAI,IAAI,CAAC,CAAC,CAAA,KAAM,SAAS,EAAE;oBACzB,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC;gBAC5C;gBAEQD,WAAAA,WAAA,IAAeC,KAAAA,KAAK,CAAC,GAAG,CAAC,oBAAoB,EAAE,2BAA2B,CAAC;gBAE3E,MAAM,iBAAiBQ,KAAAA,iBAAiB,EAAE,CAAC,KAAK,EAAE;gBAClD,MAAM,OAAA,GAAU,IAAI,CAAC,CAAC,CAAA;gBACtB,MAAM,QAAA,GAAW,IAAI,CAAC,CAAC,CAAA;gBAEvB,MAAM,iBAAA,GAAoBC,KAAAA,wBAAwB,CAAC,OAAO,CAAC;gBAEnE,0DAAA;gBACQ,MAAM,SAAA,GAAY,AAAC,OAAA,CAA4B,EAAA,IAAM,OAAO,CAAC,MAAM,EAAE,aAAa;gBAElF,MAAM,GAAA,GAAM,OAAO,CAAC,GAAA,IAAO,GAAG;gBAC9B,IAAI,CAAC,yBAAyB,GAAG,GAAG,EAAE,OAAO,CAAA,IAAK,0BAAA,KAA+B,MAAM,EAAE;oBACvF,yBAAyB,CAAC,OAAO,EAAE,cAAc,EAAE,0BAA0B,CAAC;gBACxF;gBAEA,mDAAA;gBACQ,cAAc,CAAC,wBAAwB,CAAC;oBAAE,iBAAiB;oBAAE,SAAA;gBAAA,CAAW,CAAC;gBAEjF,2EAAA;gBACA,2EAAA;gBACA,gEAAA;gBACQ,MAAM,UAAA,GAAa,CAAC,OAAO,CAAC,MAAA,IAAU,KAAK,EAAE,WAAW,EAAE;gBAC1D,MAAM,UAAA,GAAaC,KAAAA,wBAAwB,CAAC,GAAG,CAAC;gBAEhD,MAAM,yBAAA,GAA4B,CAAC,EAAA,UAAA,CAAA,CAAA,EAAA,UAAA,CAAA,CAAA;gBAEA,cAAA,CAAA,kBAAA,CAAA,yBAAA,CAAA;gBAEA,IAAA,eAAA,CAAA,SAAA,EAAA,CAAA,+BAAA,KAAA,KAAA,EAAA;oBACA,oBAAA,CAAA;wBACA,qBAAA,EAAA,cAAA;wBACA,QAAA;wBACA,sBAAA,EAAA,eAAA,CAAA,SAAA,EAAA,CAAA,sBAAA,IAAA,KAAA;oBACA,CAAA,CAAA;gBACA;gBAEA,OAAAC,KAAAA,kBAAA,CAAA,cAAA,EAAA,MAAA;oBACA,+CAAA;oBACA,8FAAA;oBACA,iEAAA;oBACAC,KAAAA,eAAA,EAAA,CAAA,qBAAA,EAAA,CAAA,iBAAA,GAAAC,KAAAA,cAAA,EAAA;oBAEA,0EAAA;oBACA,IAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,8BAAA,EAAA;wBACA,OAAA,MAAA,CAAA,KAAA,CAAA,OAAA,EAAA,IAAA,CAAA;oBACA;oBAEA,MAAA,GAAA,GAAAC,IAAAA,WAAA,CAAA,OAAA,CAAAC,IAAAA,OAAA,CAAA,MAAA,EAAA,EAAA,iBAAA,CAAA,OAAA,CAAA;oBACA,OAAAA,IAAAA,OAAA,CAAA,IAAA,CAAA,GAAA,EAAA,MAAA;wBACA,OAAA,MAAA,CAAA,KAAA,CAAA,OAAA,EAAA,IAAA,CAAA;oBACA,CAAA,CAAA;gBACA,CAAA,CAAA;YACA,CAAA;QACA,CAAA,CAAA;QAEAC,KAAAA,wBAAA,CAAA,OAAA,EAAA,oBAAA,EAAA,IAAA,CAAA;QAEA,MAAA,CAAA,IAAA,GAAA,OAAA;IACA;IAEA;;GAEA,GACA,4BAAA,CAAA,OAAA,EAAA;QACA,IAAAC,OAAAA,mBAAA,CAAAF,IAAAA,OAAA,CAAA,MAAA,EAAA,CAAA,EAAA;YACA,OAAA,IAAA;QACA;QAEA,MAAA,sBAAA,GAAA,IAAA,CAAA,SAAA,EAAA,CAAA,sBAAA;QAEA,IAAA,CAAA,sBAAA,EAAA;YACA,OAAA,KAAA;QACA;QAEA,MAAA,OAAA,GAAA,iBAAA,CAAA,OAAA,CAAA;QACA,MAAA,GAAA,GAAAd,cAAAA,aAAA,CAAA,OAAA,CAAA;QACA,OAAA,sBAAA,CAAA,GAAA,EAAA,OAAA,CAAA;IACA;AACA;AAEA,4CAAA,GACA,SAAA,oBAAA,CAAA,OAAA,EAAA,QAAA,EAAA;IACA,MAAA,IAAA,GAAA,iBAAA,CAAA,OAAA,CAAA;IAEA,MAAA,UAAA,GAAA,QAAA,EAAA,UAAA;IACA,MAAA,KAAA,GAAAiB,KAAAA,uCAAA,CAAA,UAAA,CAAA;IAEAC,KAAAA,aAAA,CACA;QACA,QAAA,EAAA,MAAA;QACA,IAAA,EAAA;YACA,WAAA,EAAA,UAAA;YACA,GAAA,IAAA;QACA,CAAA;QACA,IAAA,EAAA,MAAA;QACA,KAAA;IACA,CAAA,EACA;QACA,KAAA,EAAA,UAAA;QACA,OAAA;QACA,QAAA;IACA,CAAA;AAEA;AAEA,SAAA,iBAAA,CAAA,OAAA,EAAA;IACA,IAAA;QACA,qEAAA;QACA,MAAA,IAAA,GAAA,OAAA,CAAA,SAAA,CAAA,MAAA,CAAA,IAAA,OAAA,CAAA,IAAA;QACA,MAAA,GAAA,GAAA,IAAA,GAAA,CAAA,OAAA,CAAA,IAAA,EAAA,CAAA,EAAA,OAAA,CAAA,QAAA,CAAA,EAAA,EAAA,IAAA,CAAA,CAAA,CAAA;QACA,MAAA,SAAA,GAAAC,KAAAA,QAAA,CAAA,GAAA,CAAA,QAAA,EAAA,CAAA;QAEA,MAAA,IAAA,GAAA;YACA,GAAA,EAAAC,KAAAA,qBAAA,CAAA,SAAA,CAAA;YACA,aAAA,EAAA,OAAA,CAAA,MAAA,IAAA,KAAA;QACA,CAAA;QAEA,IAAA,SAAA,CAAA,MAAA,EAAA;YACA,IAAA,CAAA,YAAA,CAAA,GAAA,SAAA,CAAA,MAAA;QACA;QACA,IAAA,SAAA,CAAA,IAAA,EAAA;YACA,IAAA,CAAA,eAAA,CAAA,GAAA,SAAA,CAAA,IAAA;QACA;QAEA,OAAA,IAAA;IACA,CAAA,CAAA,OAAA;QACA,OAAA,CAAA,CAAA;IACA;AACA;AAEA;;;;;CAKA,GACA,SAAA,yBAAA,CACA,GAAA,EACA,cAAA,EACA,0BAAA;IAEA,IAAA,cAAA,GAAA,CAAA;IACA,MAAA,MAAA,GAAA,EAAA;IAEAtB,WAAAA,WAAA,IAAAC,KAAAA,KAAA,CAAA,GAAA,CAAA,oBAAA,EAAA,qBAAA,CAAA;IAEA;;;;GAIA,GACA,MAAA,WAAA,GAAA,IAAA,OAAA,EAAA;IAEA,MAAA,WAAA,GACA,0BAAA,KAAA,UACA,OACA,0BAAA,KAAA,WACA,QACA,oBAAA;IAEA,IAAA;QACA,6DAAA;QACA,GAAA,CAAA,EAAA,GAAA,IAAA,KAAA,CAAA,GAAA,CAAA,EAAA,EAAA;YACA,KAAA,EAAA,CAAA,MAAA,EAAA,OAAA,EAAA,IAAA,KAAA;gBACA,MAAA,CAAA,KAAA,EAAA,QAAA,EAAA,GAAA,QAAA,CAAA,GAAA,IAAA;gBAEA,IAAA,KAAA,KAAA,MAAA,EAAA;oBACAD,WAAAA,WAAA,IACAC,KAAAA,KAAA,CAAA,GAAA,CAAA,oBAAA,EAAA,CAAA,sDAAA,EAAA,WAAA,CAAA,CAAA,CAAA,CAAA;oBAEA,MAAA,QAAA,GAAA,IAAA,KAAA,CAAA,QAAA,EAAA;wBACA,KAAA,EAAA,CAAA,MAAA,EAAA,OAAA,EAAA,IAAA,KAAA;4BACA,IAAA;gCACA,MAAA,KAAA,GAAA,IAAA,CAAA,CAAA,CAAA;gCACA,MAAA,gBAAA,GAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA;gCAEA,IAAA,cAAA,GAAA,WAAA,EAAA;oCACA,MAAA,CAAA,IAAA,CAAA,gBAAA,CAAA;oCACA,cAAA,IAAA,gBAAA,CAAA,UAAA;gCACA,CAAA,MAAA,IAAAD,WAAAA,WAAA,EAAA;oCACAC,KAAAA,KAAA,CAAA,GAAA,CACA,oBAAA,EACA,CAAA,2DAAA,EAAA,WAAA,CAAA,cAAA,CAAA;gCAEA;4BACA,CAAA,CAAA,OAAA,GAAA,EAAA;gCACAD,WAAAA,WAAA,IAAAC,KAAAA,KAAA,CAAA,KAAA,CAAA,oBAAA,EAAA,6CAAA,CAAA;4BACA;4BAEA,OAAA,OAAA,CAAA,KAAA,CAAA,MAAA,EAAA,OAAA,EAAA,IAAA,CAAA;wBACA,CAAA;oBACA,CAAA,CAAA;oBAEA,WAAA,CAAA,GAAA,CAAA,QAAA,EAAA,QAAA,CAAA;oBAEA,OAAA,OAAA,CAAA,KAAA,CAAA,MAAA,EAAA,OAAA,EAAA;wBAAA,KAAA;wBAAA,QAAA,EAAA;2BAAA,QAAA;qBAAA,CAAA;gBACA;gBAEA,OAAA,OAAA,CAAA,KAAA,CAAA,MAAA,EAAA,OAAA,EAAA,IAAA,CAAA;YACA,CAAA;QACA,CAAA,CAAA;QAEA,4CAAA;QACA,6DAAA;QACA,GAAA,CAAA,GAAA,GAAA,IAAA,KAAA,CAAA,GAAA,CAAA,GAAA,EAAA;YACA,KAAA,EAAA,CAAA,MAAA,EAAA,OAAA,EAAA,IAAA,KAAA;gBACA,MAAA,GAAA,QAAA,CAAA,GAAA,IAAA;gBAEA,MAAA,QAAA,GAAA,WAAA,CAAA,GAAA,CAAA,QAAA,CAAA;gBACA,IAAA,QAAA,EAAA;oBACA,WAAA,CAAA,MAAA,CAAA,QAAA,CAAA;oBAEA,MAAA,YAAA,GAAA,IAAA,CAAA,KAAA,EAAA;oBACA,YAAA,CAAA,CAAA,CAAA,GAAA,QAAA;oBACA,OAAA,OAAA,CAAA,KAAA,CAAA,MAAA,EAAA,OAAA,EAAA,YAAA,CAAA;gBACA;gBAEA,OAAA,OAAA,CAAA,KAAA,CAAA,MAAA,EAAA,OAAA,EAAA,IAAA,CAAA;YACA,CAAA;QACA,CAAA,CAAA;QAEA,GAAA,CAAA,EAAA,CAAA,KAAA,EAAA,MAAA;YACA,IAAA;gBACA,MAAA,IAAA,GAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,CAAA,QAAA,CAAA,OAAA,CAAA;gBACA,IAAA,IAAA,EAAA;oBACA,iGAAA;oBACA,MAAA,cAAA,GAAA,MAAA,CAAA,UAAA,CAAA,IAAA,EAAA,OAAA,CAAA;oBACA,MAAA,aAAA,GACA,cAAA,GAAA,cACA,CAAA,EAAA,MAAA,CAAA,IAAA,CAAA,IAAA,EACA,QAAA,CAAA,CAAA,EAAA,WAAA,GAAA,CAAA,EACA,QAAA,CAAA,OAAA,CAAA,CAAA,GAAA,CAAA,GACA,IAAA;oBAEA,cAAA,CAAA,wBAAA,CAAA;wBAAA,iBAAA,EAAA;4BAAA,IAAA,EAAA,aAAA;wBAAA,CAAA;oBAAA,CAAA,CAAA;gBACA;YACA,CAAA,CAAA,OAAA,KAAA,EAAA;gBACA,IAAAD,WAAAA,WAAA,EAAA;oBACAC,KAAAA,KAAA,CAAA,KAAA,CAAA,oBAAA,EAAA,sCAAA,EAAA,KAAA,CAAA;gBACA;YACA;QACA,CAAA,CAAA;IACA,CAAA,CAAA,OAAA,KAAA,EAAA;QACA,IAAAD,WAAAA,WAAA,EAAA;YACAC,KAAAA,KAAA,CAAA,KAAA,CAAA,oBAAA,EAAA,wCAAA,EAAA,KAAA,CAAA;QACA;IACA;AACA;AAEA,SAAA,iBAAA,CAAA,OAAA,EAAA;IACA,OAAA;QACA,MAAA,EAAA,OAAA,CAAA,MAAA;QACA,QAAA,EAAA,OAAA,CAAA,QAAA;QACA,IAAA,EAAA,OAAA,CAAA,IAAA;QACA,QAAA,EAAA,OAAA,CAAA,IAAA;QACA,IAAA,EAAA,OAAA,CAAA,IAAA;QACA,OAAA,EAAA,OAAA,CAAA,UAAA,EAAA;IACA,CAAA;AACA;AAEA;;;;;;;;CAQA,GACA,0BAAA;AACA,SAAA,oBAAA,CAAA,EACA,qBAAA,EACA,QAAA,EACA,sBAAA,EACA;IAKA,qBAAA,CAAA,wBAAA,CAAA;QACA,cAAA,EAAA;YAAA,MAAA,EAAA,IAAA;QAAA,CAAA;IACA,CAAA,CAAA;IACA,QAAA,CAAA,IAAA,CAAA,OAAA,EAAA,MAAA;QACA,sJAAA;QACA,MAAA,MAAA,GAAAE,KAAAA,SAAA,EAAA;QACA,MAAA,cAAA,GAAA,qBAAA,CAAA,YAAA,EAAA,CAAA,qBAAA,CAAA,cAAA;QAEA,IAAA,MAAA,IAAA,cAAA,EAAA;YACAH,WAAAA,WAAA,IAAAC,KAAAA,KAAA,CAAA,GAAA,CAAA,CAAA,sCAAA,EAAA,cAAA,CAAA,MAAA,CAAA,CAAA,CAAA;YAEA,MAAA,WAAA,GAAA,IAAA,IAAA,EAAA;YACA,WAAA,CAAA,UAAA,CAAA,CAAA,EAAA,CAAA,CAAA;YACA,MAAA,aAAA,GAAA,WAAA,CAAA,WAAA,EAAA;YAEA,MAAA,uBAAA,GAAA,mCAAA,CAAA,GAAA,CAAA,MAAA,CAAA;YACA,MAAA,MAAA,GAAA,uBAAA,EAAA,CAAA,aAAA,CAAA,IAAA;gBAAA,MAAA,EAAA,CAAA;gBAAA,OAAA,EAAA,CAAA;gBAAA,OAAA,EAAA,CAAA;YAAA,CAAA;YACA,MAAA,CAAA,CAAA;gBAAA,EAAA,EAAA,QAAA;gBAAA,OAAA,EAAA,SAAA;gBAAA,OAAA,EAAA,SAAA;YAAA,CAAA,CAAA,CAAA,cAAA,CAAA,MAAA,CAAA,CAAA,EAAA;YAEA,IAAA,uBAAA,EAAA;gBACA,uBAAA,CAAA,aAAA,CAAA,GAAA,MAAA;YACA,CAAA,MAAA;gBACAD,WAAAA,WAAA,IAAAC,KAAAA,KAAA,CAAA,GAAA,CAAA,uCAAA,CAAA;gBACA,MAAA,kBAAA,GAAA;oBAAA,CAAA,aAAA,CAAA,EAAA,MAAA;gBAAA,CAAA;gBACA,mCAAA,CAAA,GAAA,CAAA,MAAA,EAAA,kBAAA,CAAA;gBAEA,MAAA,4BAAA,GAAA,MAAA;oBACA,YAAA,CAAA,OAAA,CAAA;oBACA,yBAAA,EAAA;oBACA,mCAAA,CAAA,MAAA,CAAA,MAAA,CAAA;oBAEA,MAAA,gBAAA,GAAA,MAAA,CAAA,OAAA,CAAA,kBAAA,CAAA,CAAA,GAAA,CACA,CAAA,CAAA,SAAA,EAAA,KAAA,CAAA,GAAA,CAAA;4BACA,OAAA,EAAA,SAAA;4BACA,MAAA,EAAA,KAAA,CAAA,MAAA;4BACA,OAAA,EAAA,KAAA,CAAA,OAAA;4BACA,OAAA,EAAA,KAAA,CAAA,OAAA;wBACA,CAAA,CAAA;oBAEA,MAAA,CAAA,WAAA,CAAA;wBAAA,UAAA,EAAA,gBAAA;oBAAA,CAAA,CAAA;gBACA,CAAA;gBAEA,MAAA,yBAAA,GAAA,MAAA,CAAA,EAAA,CAAA,OAAA,EAAA,MAAA;oBACAD,WAAAA,WAAA,IAAAC,KAAAA,KAAA,CAAA,GAAA,CAAA,uDAAA,CAAA;oBACA,4BAAA,EAAA;gBACA,CAAA,CAAA;gBACA,MAAA,OAAA,GAAA,UAAA,CAAA,MAAA;oBACAD,WAAAA,WAAA,IAAAC,KAAAA,KAAA,CAAA,GAAA,CAAA,4DAAA,CAAA;oBACA,4BAAA,EAAA;gBACA,CAAA,EAAA,sBAAA,CAAA,CAAA,KAAA,EAAA;YACA;QACA;IACA,CAAA,CAAA;AACA;AAEA,MAAA,mCAAA,GAAA,IAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 836, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/integrations/http/index.ts"], "sourcesContent": ["import type { IncomingMessage, RequestOptions } from 'node:http';\nimport { defineIntegration } from '@sentry/core';\nimport { generateInstrumentOnce } from '../../otel/instrument';\nimport type { SentryHttpInstrumentationOptions } from './SentryHttpInstrumentation';\nimport { SentryHttpInstrumentation } from './SentryHttpInstrumentation';\n\nconst INTEGRATION_NAME = 'Http';\n\ninterface HttpOptions {\n  /**\n   * Whether breadcrumbs should be recorded for outgoing requests.\n   * Defaults to true\n   */\n  breadcrumbs?: boolean;\n\n  /**\n   * Whether the integration should create [Sessions](https://docs.sentry.io/product/releases/health/#sessions) for incoming requests to track the health and crash-free rate of your releases in Sentry.\n   * Read more about Release Health: https://docs.sentry.io/product/releases/health/\n   *\n   * Defaults to `true`.\n   */\n  trackIncomingRequestsAsSessions?: boolean;\n\n  /**\n   * Number of milliseconds until sessions tracked with `trackIncomingRequestsAsSessions` will be flushed as a session aggregate.\n   *\n   * Defaults to `60000` (60s).\n   */\n  sessionFlushingDelayMS?: number;\n\n  /**\n   * Do not capture spans or breadcrumbs for outgoing HTTP requests to URLs where the given callback returns `true`.\n   * This controls both span & breadcrumb creation - spans will be non recording if tracing is disabled.\n   *\n   * The `url` param contains the entire URL, including query string (if any), protocol, host, etc. of the outgoing request.\n   * For example: `'https://someService.com/users/details?id=123'`\n   *\n   * The `request` param contains the original {@type RequestOptions} object used to make the outgoing request.\n   * You can use it to filter on additional properties like method, headers, etc.\n   */\n  ignoreOutgoingRequests?: (url: string, request: RequestOptions) => boolean;\n\n  /**\n   * Do not capture spans for incoming HTTP requests to URLs where the given callback returns `true`.\n   * Spans will be non recording if tracing is disabled.\n   *\n   * The `urlPath` param consists of the URL path and query string (if any) of the incoming request.\n   * For example: `'/users/details?id=123'`\n   *\n   * The `request` param contains the original {@type IncomingMessage} object of the incoming request.\n   * You can use it to filter on additional properties like method, headers, etc.\n   */\n  ignoreIncomingRequests?: (urlPath: string, request: IncomingMessage) => boolean;\n\n  /**\n   * Do not capture spans for incoming HTTP requests with the given status codes.\n   * By default, spans with 404 status code are ignored.\n   * Expects an array of status codes or a range of status codes, e.g. [[300,399], 404] would ignore 3xx and 404 status codes.\n   *\n   * @default `[[401, 404], [300, 399]]`\n   */\n  dropSpansForIncomingRequestStatusCodes?: (number | [number, number])[];\n\n  /**\n   * Do not capture the request body for incoming HTTP requests to URLs where the given callback returns `true`.\n   * This can be useful for long running requests where the body is not needed and we want to avoid capturing it.\n   *\n   * @param url Contains the entire URL, including query string (if any), protocol, host, etc. of the incoming request.\n   * @param request Contains the {@type RequestOptions} object used to make the incoming request.\n   */\n  ignoreIncomingRequestBody?: (url: string, request: RequestOptions) => boolean;\n\n  /**\n   * Controls the maximum size of incoming HTTP request bodies attached to events.\n   *\n   * Available options:\n   * - 'none': No request bodies will be attached\n   * - 'small': Request bodies up to 1,000 bytes will be attached\n   * - 'medium': Request bodies up to 10,000 bytes will be attached (default)\n   * - 'always': Request bodies will always be attached\n   *\n   * Note that even with 'always' setting, bodies exceeding 1MB will never be attached\n   * for performance and security reasons.\n   *\n   * @default 'medium'\n   */\n  maxIncomingRequestBodySize?: 'none' | 'small' | 'medium' | 'always';\n\n  /**\n   * If true, do not generate spans for incoming requests at all.\n   * This is used by Remix to avoid generating spans for incoming requests, as it generates its own spans.\n   */\n  disableIncomingRequestSpans?: boolean;\n}\n\nconst instrumentSentryHttp = generateInstrumentOnce<SentryHttpInstrumentationOptions>(\n  `${INTEGRATION_NAME}.sentry`,\n  options => {\n    return new SentryHttpInstrumentation(options);\n  },\n);\n\n/**\n * The http integration instruments Node's internal http and https modules.\n * It creates breadcrumbs for outgoing HTTP requests which will be attached to the currently active span.\n */\nexport const httpIntegration = defineIntegration((options: HttpOptions = {}) => {\n  const dropSpansForIncomingRequestStatusCodes = options.dropSpansForIncomingRequestStatusCodes ?? [\n    [401, 404],\n    [300, 399],\n  ];\n\n  return {\n    name: INTEGRATION_NAME,\n    setupOnce() {\n      instrumentSentryHttp({\n        ...options,\n        extractIncomingTraceFromHeader: true,\n        propagateTraceInOutgoingRequests: true,\n      });\n    },\n    processEvent(event) {\n      // Drop transaction if it has a status code that should be ignored\n      if (event.type === 'transaction') {\n        const statusCode = event.contexts?.trace?.data?.['http.response.status_code'];\n        if (\n          typeof statusCode === 'number' &&\n          dropSpansForIncomingRequestStatusCodes.some(code => {\n            if (typeof code === 'number') {\n              return code === statusCode;\n            }\n\n            const [min, max] = code;\n            return statusCode >= min && statusCode <= max;\n          })\n        ) {\n          return null;\n        }\n      }\n\n      return event;\n    },\n  };\n});\n"], "names": ["generateInstrumentOnce", "SentryHttpInstrumentation", "defineIntegration"], "mappings": ";;;;;;AAMA,MAAM,gBAAA,GAAmB,MAAM;AAyF/B,MAAM,oBAAA,GAAuBA,WAAAA,sBAAsB,CACjD,CAAC,EAAA,gBAAA,CAAA,OAAA,CAAA,GACA,OAAA,IAAA;IACA,OAAA,IAAAC,0BAAAA,yBAAA,CAAA,OAAA,CAAA;AACA,CAAA;AAGA;;;CAGA,GACA,MAAA,eAAA,GAAAC,KAAAA,iBAAA,CAAA,CAAA,OAAA,GAAA,CAAA,CAAA,KAAA;IACA,MAAA,sCAAA,GAAA,OAAA,CAAA,sCAAA,IAAA;QACA;YAAA,GAAA;YAAA,GAAA;SAAA;QACA;YAAA,GAAA;YAAA,GAAA;SAAA;KACA;IAEA,OAAA;QACA,IAAA,EAAA,gBAAA;QACA,SAAA,GAAA;YACA,oBAAA,CAAA;gBACA,GAAA,OAAA;gBACA,8BAAA,EAAA,IAAA;gBACA,gCAAA,EAAA,IAAA;YACA,CAAA,CAAA;QACA,CAAA;QACA,YAAA,EAAA,KAAA,EAAA;YACA,kEAAA;YACA,IAAA,KAAA,CAAA,IAAA,KAAA,aAAA,EAAA;gBACA,MAAA,UAAA,GAAA,KAAA,CAAA,QAAA,EAAA,KAAA,EAAA,IAAA,EAAA,CAAA,2BAAA,CAAA;gBACA,IACA,OAAA,UAAA,KAAA,QAAA,IACA,sCAAA,CAAA,IAAA,EAAA,IAAA,IAAA;oBACA,IAAA,OAAA,IAAA,KAAA,QAAA,EAAA;wBACA,OAAA,IAAA,KAAA,UAAA;oBACA;oBAEA,MAAA,CAAA,GAAA,EAAA,GAAA,CAAA,GAAA,IAAA;oBACA,OAAA,UAAA,IAAA,GAAA,IAAA,UAAA,IAAA,GAAA;gBACA,CAAA,GACA;oBACA,OAAA,IAAA;gBACA;YACA;YAEA,OAAA,KAAA;QACA,CAAA;IACA,CAAA;AACA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 893, "column": 0}, "map": {"version": 3, "file": "nodeVersion.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/nodeVersion.ts"], "sourcesContent": ["import { parseSemver } from '@sentry/core';\n\nexport const NODE_VERSION = parseSemver(process.versions.node) as { major: number; minor: number; patch: number };\nexport const NODE_MAJOR = NODE_VERSION.major;\nexport const NODE_MINOR = NODE_VERSION.minor;\n"], "names": ["parseSemver"], "mappings": ";;;;AAEO,MAAM,YAAA,GAAeA,KAAAA,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAA;AACtD,MAAM,UAAA,GAAa,YAAY,CAAC,KAAA;AAChC,MAAM,UAAA,GAAa,YAAY,CAAC,KAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 908, "column": 0}, "map": {"version": 3, "file": "SentryNodeFetchInstrumentation.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/integrations/node-fetch/SentryNodeFetchInstrumentation.ts"], "sourcesContent": ["import { context } from '@opentelemetry/api';\nimport { isTracingSuppressed } from '@opentelemetry/core';\nimport type { InstrumentationConfig } from '@opentelemetry/instrumentation';\nimport { InstrumentationBase } from '@opentelemetry/instrumentation';\nimport type { SanitizedRequestData } from '@sentry/core';\nimport {\n  addBreadcrumb,\n  getBreadcrumbLogLevelFromHttpStatusCode,\n  getClient,\n  getSanitizedUrlString,\n  getTraceData,\n  LRUMap,\n  parseUrl,\n  SDK_VERSION,\n} from '@sentry/core';\nimport { shouldPropagateTraceForUrl } from '@sentry/opentelemetry';\nimport * as diagch from 'diagnostics_channel';\nimport { NODE_MAJOR, NODE_MINOR } from '../../nodeVersion';\nimport { mergeBaggageHeaders } from '../../utils/baggage';\nimport type { UndiciRequest, UndiciResponse } from './types';\n\nconst SENTRY_TRACE_HEADER = 'sentry-trace';\nconst SENTRY_BAGGAGE_HEADER = 'baggage';\n\n// For baggage, we make sure to merge this into a possibly existing header\nconst BAGGAGE_HEADER_REGEX = /baggage: (.*)\\r\\n/;\n\nexport type SentryNodeFetchInstrumentationOptions = InstrumentationConfig & {\n  /**\n   * Whether breadcrumbs should be recorded for requests.\n   *\n   * @default `true`\n   */\n  breadcrumbs?: boolean;\n\n  /**\n   * Do not capture breadcrumbs or inject headers for outgoing fetch requests to URLs where the given callback returns `true`.\n   * The same option can be passed to the top-level httpIntegration where it controls both, breadcrumb and\n   * span creation.\n   *\n   * @param url Contains the entire URL, including query string (if any), protocol, host, etc. of the outgoing request.\n   */\n  ignoreOutgoingRequests?: (url: string) => boolean;\n};\n\ninterface ListenerRecord {\n  name: string;\n  unsubscribe: () => void;\n}\n\n/**\n * This custom node-fetch instrumentation is used to instrument outgoing fetch requests.\n * It does not emit any spans.\n *\n * The reason this is isolated from the OpenTelemetry instrumentation is that users may overwrite this,\n * which would lead to Sentry not working as expected.\n *\n * This is heavily inspired & adapted from:\n * https://github.com/open-telemetry/opentelemetry-js-contrib/blob/28e209a9da36bc4e1f8c2b0db7360170ed46cb80/plugins/node/instrumentation-undici/src/undici.ts\n */\nexport class SentryNodeFetchInstrumentation extends InstrumentationBase<SentryNodeFetchInstrumentationOptions> {\n  // Keep ref to avoid https://github.com/nodejs/node/issues/42170 bug and for\n  // unsubscribing.\n  private _channelSubs: Array<ListenerRecord>;\n  private _propagationDecisionMap: LRUMap<string, boolean>;\n  private _ignoreOutgoingRequestsMap: WeakMap<UndiciRequest, boolean>;\n\n  public constructor(config: SentryNodeFetchInstrumentationOptions = {}) {\n    super('@sentry/instrumentation-node-fetch', SDK_VERSION, config);\n    this._channelSubs = [];\n    this._propagationDecisionMap = new LRUMap<string, boolean>(100);\n    this._ignoreOutgoingRequestsMap = new WeakMap<UndiciRequest, boolean>();\n  }\n\n  /** No need to instrument files/modules. */\n  public init(): void {\n    return undefined;\n  }\n\n  /** Disable the instrumentation. */\n  public disable(): void {\n    super.disable();\n    this._channelSubs.forEach(sub => sub.unsubscribe());\n    this._channelSubs = [];\n  }\n\n  /** Enable the instrumentation. */\n  public enable(): void {\n    // \"enabled\" handling is currently a bit messy with InstrumentationBase.\n    // If constructed with `{enabled: false}`, this `.enable()` is still called,\n    // and `this.getConfig().enabled !== this.isEnabled()`, creating confusion.\n    //\n    // For now, this class will setup for instrumenting if `.enable()` is\n    // called, but use `this.getConfig().enabled` to determine if\n    // instrumentation should be generated. This covers the more likely common\n    // case of config being given a construction time, rather than later via\n    // `instance.enable()`, `.disable()`, or `.setConfig()` calls.\n    super.enable();\n\n    // This method is called by the super-class constructor before ours is\n    // called. So we need to ensure the property is initalized.\n    this._channelSubs = this._channelSubs || [];\n\n    // Avoid to duplicate subscriptions\n    if (this._channelSubs.length > 0) {\n      return;\n    }\n\n    this._subscribeToChannel('undici:request:create', this._onRequestCreated.bind(this));\n    this._subscribeToChannel('undici:request:headers', this._onResponseHeaders.bind(this));\n  }\n\n  /**\n   * This method is called when a request is created.\n   * You can still mutate the request here before it is sent.\n   */\n  private _onRequestCreated({ request }: { request: UndiciRequest }): void {\n    const config = this.getConfig();\n    const enabled = config.enabled !== false;\n\n    if (!enabled) {\n      return;\n    }\n\n    const shouldIgnore = this._shouldIgnoreOutgoingRequest(request);\n    // We store this decisision for later so we do not need to re-evaluate it\n    // Additionally, the active context is not correct in _onResponseHeaders, so we need to make sure it is evaluated here\n    this._ignoreOutgoingRequestsMap.set(request, shouldIgnore);\n\n    if (shouldIgnore) {\n      return;\n    }\n\n    const url = getAbsoluteUrl(request.origin, request.path);\n\n    // Manually add the trace headers, if it applies\n    // Note: We do not use `propagation.inject()` here, because our propagator relies on an active span\n    // Which we do not have in this case\n    // The propagator _may_ overwrite this, but this should be fine as it is the same data\n    const tracePropagationTargets = getClient()?.getOptions().tracePropagationTargets;\n    const addedHeaders = shouldPropagateTraceForUrl(url, tracePropagationTargets, this._propagationDecisionMap)\n      ? getTraceData()\n      : undefined;\n\n    if (!addedHeaders) {\n      return;\n    }\n\n    const { 'sentry-trace': sentryTrace, baggage } = addedHeaders;\n\n    // We do not want to overwrite existing headers here\n    // If the core UndiciInstrumentation is registered, it will already have set the headers\n    // We do not want to add any then\n    if (Array.isArray(request.headers)) {\n      const requestHeaders = request.headers;\n\n      // We do not want to overwrite existing header here, if it was already set\n      if (sentryTrace && !requestHeaders.includes(SENTRY_TRACE_HEADER)) {\n        requestHeaders.push(SENTRY_TRACE_HEADER, sentryTrace);\n      }\n\n      // For baggage, we make sure to merge this into a possibly existing header\n      const existingBaggagePos = requestHeaders.findIndex(header => header === SENTRY_BAGGAGE_HEADER);\n      if (baggage && existingBaggagePos === -1) {\n        requestHeaders.push(SENTRY_BAGGAGE_HEADER, baggage);\n      } else if (baggage) {\n        const existingBaggage = requestHeaders[existingBaggagePos + 1];\n        const merged = mergeBaggageHeaders(existingBaggage, baggage);\n        if (merged) {\n          requestHeaders[existingBaggagePos + 1] = merged;\n        }\n      }\n    } else {\n      const requestHeaders = request.headers;\n      // We do not want to overwrite existing header here, if it was already set\n      if (sentryTrace && !requestHeaders.includes(`${SENTRY_TRACE_HEADER}:`)) {\n        request.headers += `${SENTRY_TRACE_HEADER}: ${sentryTrace}\\r\\n`;\n      }\n\n      const existingBaggage = request.headers.match(BAGGAGE_HEADER_REGEX)?.[1];\n      if (baggage && !existingBaggage) {\n        request.headers += `${SENTRY_BAGGAGE_HEADER}: ${baggage}\\r\\n`;\n      } else if (baggage) {\n        const merged = mergeBaggageHeaders(existingBaggage, baggage);\n        if (merged) {\n          request.headers = request.headers.replace(BAGGAGE_HEADER_REGEX, `baggage: ${merged}\\r\\n`);\n        }\n      }\n    }\n  }\n\n  /**\n   * This method is called when a response is received.\n   */\n  private _onResponseHeaders({ request, response }: { request: UndiciRequest; response: UndiciResponse }): void {\n    const config = this.getConfig();\n    const enabled = config.enabled !== false;\n\n    if (!enabled) {\n      return;\n    }\n\n    const _breadcrumbs = config.breadcrumbs;\n    const breadCrumbsEnabled = typeof _breadcrumbs === 'undefined' ? true : _breadcrumbs;\n\n    const shouldIgnore = this._ignoreOutgoingRequestsMap.get(request);\n\n    if (breadCrumbsEnabled && !shouldIgnore) {\n      addRequestBreadcrumb(request, response);\n    }\n  }\n\n  /** Subscribe to a diagnostics channel. */\n  private _subscribeToChannel(\n    diagnosticChannel: string,\n    onMessage: (message: unknown, name: string | symbol) => void,\n  ): void {\n    // `diagnostics_channel` had a ref counting bug until v18.19.0.\n    // https://github.com/nodejs/node/pull/47520\n    const useNewSubscribe = NODE_MAJOR > 18 || (NODE_MAJOR === 18 && NODE_MINOR >= 19);\n\n    let unsubscribe: () => void;\n    if (useNewSubscribe) {\n      diagch.subscribe?.(diagnosticChannel, onMessage);\n      unsubscribe = () => diagch.unsubscribe?.(diagnosticChannel, onMessage);\n    } else {\n      const channel = diagch.channel(diagnosticChannel);\n      channel.subscribe(onMessage);\n      unsubscribe = () => channel.unsubscribe(onMessage);\n    }\n\n    this._channelSubs.push({\n      name: diagnosticChannel,\n      unsubscribe,\n    });\n  }\n\n  /**\n   * Check if the given outgoing request should be ignored.\n   */\n  private _shouldIgnoreOutgoingRequest(request: UndiciRequest): boolean {\n    if (isTracingSuppressed(context.active())) {\n      return true;\n    }\n\n    // Add trace propagation headers\n    const url = getAbsoluteUrl(request.origin, request.path);\n    const ignoreOutgoingRequests = this.getConfig().ignoreOutgoingRequests;\n\n    if (typeof ignoreOutgoingRequests !== 'function' || !url) {\n      return false;\n    }\n\n    return ignoreOutgoingRequests(url);\n  }\n}\n\n/** Add a breadcrumb for outgoing requests. */\nfunction addRequestBreadcrumb(request: UndiciRequest, response: UndiciResponse): void {\n  const data = getBreadcrumbData(request);\n\n  const statusCode = response.statusCode;\n  const level = getBreadcrumbLogLevelFromHttpStatusCode(statusCode);\n\n  addBreadcrumb(\n    {\n      category: 'http',\n      data: {\n        status_code: statusCode,\n        ...data,\n      },\n      type: 'http',\n      level,\n    },\n    {\n      event: 'response',\n      request,\n      response,\n    },\n  );\n}\n\nfunction getBreadcrumbData(request: UndiciRequest): Partial<SanitizedRequestData> {\n  try {\n    const url = getAbsoluteUrl(request.origin, request.path);\n    const parsedUrl = parseUrl(url);\n\n    const data: Partial<SanitizedRequestData> = {\n      url: getSanitizedUrlString(parsedUrl),\n      'http.method': request.method || 'GET',\n    };\n\n    if (parsedUrl.search) {\n      data['http.query'] = parsedUrl.search;\n    }\n    if (parsedUrl.hash) {\n      data['http.fragment'] = parsedUrl.hash;\n    }\n\n    return data;\n  } catch {\n    return {};\n  }\n}\n\nfunction getAbsoluteUrl(origin: string, path: string = '/'): string {\n  try {\n    const url = new URL(path, origin);\n    return url.toString();\n  } catch {\n    // fallback: Construct it on our own\n    const url = `${origin}`;\n\n    if (url.endsWith('/') && path.startsWith('/')) {\n      return `${url}${path.slice(1)}`;\n    }\n\n    if (!url.endsWith('/') && !path.startsWith('/')) {\n      return `${url}/${path.slice(1)}`;\n    }\n\n    return `${url}${path}`;\n  }\n}\n"], "names": ["InstrumentationBase", "SDK_VERSION", "LRUMap", "getClient", "shouldPropagateTraceForUrl", "getTraceData", "baggage", "mergeBaggageHeaders", "NODE_MAJOR", "NODE_MINOR", "isTracingSuppressed", "context", "getBreadcrumbLogLevelFromHttpStatusCode", "addBreadcrumb", "parseUrl", "getSanitizedUrlString"], "mappings": ";;;;;;;;;;;AAqBA,MAAM,mBAAA,GAAsB,cAAc;AAC1C,MAAM,qBAAA,GAAwB,SAAS;AAEvC,0EAAA;AACA,MAAM,oBAAA,GAAuB,mBAAmB;AAyBhD;;;;;;;;;CASA,GACO,MAAM,8BAAA,SAAuCA,gBAAAA,mBAAmB,CAAwC;IAC/G,4EAAA;IACA,iBAAA;IAKS,WAAW,CAAC,MAAM,GAA0C,CAAA,CAAE,CAAE;QACrE,KAAK,CAAC,oCAAoC,EAAEC,KAAAA,WAAW,EAAE,MAAM,CAAC;QAChE,IAAI,CAAC,YAAA,GAAe,EAAE;QACtB,IAAI,CAAC,uBAAA,GAA0B,IAAIC,KAAAA,MAAM,CAAkB,GAAG,CAAC;QAC/D,IAAI,CAAC,0BAAA,GAA6B,IAAI,OAAO,EAA0B;IAC3E;IAEA,yCAAA,GACS,IAAI,GAAS;QAClB,OAAO,SAAS;IACpB;IAEA,iCAAA,GACS,OAAO,GAAS;QACrB,KAAK,CAAC,OAAO,EAAE;QACf,IAAI,CAAC,YAAY,CAAC,OAAO,EAAC,GAAA,GAAO,GAAG,CAAC,WAAW,EAAE,CAAC;QACnD,IAAI,CAAC,YAAA,GAAe,EAAE;IAC1B;IAEA,gCAAA,GACS,MAAM,GAAS;QACxB,wEAAA;QACA,4EAAA;QACA,2EAAA;QACA,EAAA;QACA,qEAAA;QACA,6DAAA;QACA,0EAAA;QACA,wEAAA;QACA,8DAAA;QACI,KAAK,CAAC,MAAM,EAAE;QAElB,sEAAA;QACA,2DAAA;QACI,IAAI,CAAC,YAAA,GAAe,IAAI,CAAC,YAAA,IAAgB,EAAE;QAE/C,mCAAA;QACI,IAAI,IAAI,CAAC,YAAY,CAAC,MAAA,GAAS,CAAC,EAAE;YAChC;QACN;QAEI,IAAI,CAAC,mBAAmB,CAAC,uBAAuB,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpF,IAAI,CAAC,mBAAmB,CAAC,wBAAwB,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1F;IAEA;;;GAGA,GACU,iBAAiB,CAAC,EAAE,OAAA,EAAS,EAAoC;QACvE,MAAM,MAAA,GAAS,IAAI,CAAC,SAAS,EAAE;QAC/B,MAAM,OAAA,GAAU,MAAM,CAAC,OAAA,KAAY,KAAK;QAExC,IAAI,CAAC,OAAO,EAAE;YACZ;QACN;QAEI,MAAM,eAAe,IAAI,CAAC,4BAA4B,CAAC,OAAO,CAAC;QACnE,yEAAA;QACA,sHAAA;QACI,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,OAAO,EAAE,YAAY,CAAC;QAE1D,IAAI,YAAY,EAAE;YAChB;QACN;QAEI,MAAM,GAAA,GAAM,cAAc,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC;QAE5D,gDAAA;QACA,mGAAA;QACA,oCAAA;QACA,sFAAA;QACI,MAAM,uBAAA,GAA0BC,KAAAA,SAAS,EAAE,EAAE,UAAU,EAAE,CAAC,uBAAuB;QACjF,MAAM,YAAA,GAAeC,cAAAA,0BAA0B,CAAC,GAAG,EAAE,uBAAuB,EAAE,IAAI,CAAC,uBAAuB,IACtGC,KAAAA,YAAY,KACZ,SAAS;QAEb,IAAI,CAAC,YAAY,EAAE;YACjB;QACN;QAEI,MAAM,EAAE,cAAc,EAAE,WAAW,EAAA,SAAEC,SAAA,EAAQ,GAAI,YAAY;QAEjE,oDAAA;QACA,wFAAA;QACA,iCAAA;QACI,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAClC,MAAM,cAAA,GAAiB,OAAO,CAAC,OAAO;YAE5C,0EAAA;YACM,IAAI,WAAA,IAAe,CAAC,cAAc,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE;gBAChE,cAAc,CAAC,IAAI,CAAC,mBAAmB,EAAE,WAAW,CAAC;YAC7D;YAEA,0EAAA;YACM,MAAM,kBAAA,GAAqB,cAAc,CAAC,SAAS,EAAC,MAAA,GAAU,MAAA,KAAW,qBAAqB,CAAC;YAC/F,IAAIA,SAAA,IAAW,uBAAuB,CAAA,CAAE,EAAE;gBACxC,cAAc,CAAC,IAAI,CAAC,qBAAqB,EAAEA,SAAO,CAAC;YAC3D,CAAM,MAAO,IAAIA,SAAO,EAAE;gBAClB,MAAM,kBAAkB,cAAc,CAAC,kBAAA,GAAqB,CAAC,CAAC;gBAC9D,MAAM,SAASC,QAAAA,mBAAmB,CAAC,eAAe,EAAED,SAAO,CAAC;gBAC5D,IAAI,MAAM,EAAE;oBACV,cAAc,CAAC,kBAAA,GAAqB,CAAC,CAAA,GAAI,MAAM;gBACzD;YACA;QACA,OAAW;YACL,MAAM,cAAA,GAAiB,OAAO,CAAC,OAAO;YAC5C,0EAAA;YACM,IAAI,WAAA,IAAe,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,EAAA,mBAAA,CAAA,CAAA,CAAA,CAAA,EAAA;gBACA,OAAA,CAAA,OAAA,IAAA,CAAA,EAAA,mBAAA,CAAA,EAAA,EAAA,WAAA,CAAA,IAAA,CAAA;YACA;YAEA,MAAA,eAAA,GAAA,OAAA,CAAA,OAAA,CAAA,KAAA,CAAA,oBAAA,CAAA,EAAA,CAAA,CAAA,CAAA;YACA,IAAAA,SAAA,IAAA,CAAA,eAAA,EAAA;gBACA,OAAA,CAAA,OAAA,IAAA,CAAA,EAAA,qBAAA,CAAA,EAAA,EAAAA,SAAA,CAAA,IAAA,CAAA;YACA,CAAA,MAAA,IAAAA,SAAA,EAAA;gBACA,MAAA,MAAA,GAAAC,QAAAA,mBAAA,CAAA,eAAA,EAAAD,SAAA,CAAA;gBACA,IAAA,MAAA,EAAA;oBACA,OAAA,CAAA,OAAA,GAAA,OAAA,CAAA,OAAA,CAAA,OAAA,CAAA,oBAAA,EAAA,CAAA,SAAA,EAAA,MAAA,CAAA,IAAA,CAAA,CAAA;gBACA;YACA;QACA;IACA;IAEA;;GAEA,GACA,kBAAA,CAAA,EAAA,OAAA,EAAA,QAAA,EAAA,EAAA;QACA,MAAA,MAAA,GAAA,IAAA,CAAA,SAAA,EAAA;QACA,MAAA,OAAA,GAAA,MAAA,CAAA,OAAA,KAAA,KAAA;QAEA,IAAA,CAAA,OAAA,EAAA;YACA;QACA;QAEA,MAAA,YAAA,GAAA,MAAA,CAAA,WAAA;QACA,MAAA,kBAAA,GAAA,OAAA,YAAA,KAAA,WAAA,GAAA,IAAA,GAAA,YAAA;QAEA,MAAA,YAAA,GAAA,IAAA,CAAA,0BAAA,CAAA,GAAA,CAAA,OAAA,CAAA;QAEA,IAAA,kBAAA,IAAA,CAAA,YAAA,EAAA;YACA,oBAAA,CAAA,OAAA,EAAA,QAAA,CAAA;QACA;IACA;IAEA,wCAAA,GACA,mBAAA,CACA,iBAAA,EACA,SAAA,EACA;QACA,+DAAA;QACA,4CAAA;QACA,MAAA,eAAA,GAAAE,YAAAA,UAAA,GAAA,EAAA,IAAAA,YAAAA,UAAA,KAAA,EAAA,IAAAC,YAAAA,UAAA,IAAA,EAAA,CAAA;QAEA,IAAA,WAAA;QACA,IAAA,eAAA,EAAA;YACA,MAAA,CAAA,SAAA,GAAA,iBAAA,EAAA,SAAA,CAAA;YACA,WAAA,GAAA,IAAA,MAAA,CAAA,WAAA,GAAA,iBAAA,EAAA,SAAA,CAAA;QACA,CAAA,MAAA;YACA,MAAA,OAAA,GAAA,MAAA,CAAA,OAAA,CAAA,iBAAA,CAAA;YACA,OAAA,CAAA,SAAA,CAAA,SAAA,CAAA;YACA,WAAA,GAAA,IAAA,OAAA,CAAA,WAAA,CAAA,SAAA,CAAA;QACA;QAEA,IAAA,CAAA,YAAA,CAAA,IAAA,CAAA;YACA,IAAA,EAAA,iBAAA;YACA,WAAA;QACA,CAAA,CAAA;IACA;IAEA;;GAEA,GACA,4BAAA,CAAA,OAAA,EAAA;QACA,IAAAC,OAAAA,mBAAA,CAAAC,IAAAA,OAAA,CAAA,MAAA,EAAA,CAAA,EAAA;YACA,OAAA,IAAA;QACA;QAEA,gCAAA;QACA,MAAA,GAAA,GAAA,cAAA,CAAA,OAAA,CAAA,MAAA,EAAA,OAAA,CAAA,IAAA,CAAA;QACA,MAAA,sBAAA,GAAA,IAAA,CAAA,SAAA,EAAA,CAAA,sBAAA;QAEA,IAAA,OAAA,sBAAA,KAAA,UAAA,IAAA,CAAA,GAAA,EAAA;YACA,OAAA,KAAA;QACA;QAEA,OAAA,sBAAA,CAAA,GAAA,CAAA;IACA;AACA;AAEA,4CAAA,GACA,SAAA,oBAAA,CAAA,OAAA,EAAA,QAAA,EAAA;IACA,MAAA,IAAA,GAAA,iBAAA,CAAA,OAAA,CAAA;IAEA,MAAA,UAAA,GAAA,QAAA,CAAA,UAAA;IACA,MAAA,KAAA,GAAAC,KAAAA,uCAAA,CAAA,UAAA,CAAA;IAEAC,KAAAA,aAAA,CACA;QACA,QAAA,EAAA,MAAA;QACA,IAAA,EAAA;YACA,WAAA,EAAA,UAAA;YACA,GAAA,IAAA;QACA,CAAA;QACA,IAAA,EAAA,MAAA;QACA,KAAA;IACA,CAAA,EACA;QACA,KAAA,EAAA,UAAA;QACA,OAAA;QACA,QAAA;IACA,CAAA;AAEA;AAEA,SAAA,iBAAA,CAAA,OAAA,EAAA;IACA,IAAA;QACA,MAAA,GAAA,GAAA,cAAA,CAAA,OAAA,CAAA,MAAA,EAAA,OAAA,CAAA,IAAA,CAAA;QACA,MAAA,SAAA,GAAAC,KAAAA,QAAA,CAAA,GAAA,CAAA;QAEA,MAAA,IAAA,GAAA;YACA,GAAA,EAAAC,KAAAA,qBAAA,CAAA,SAAA,CAAA;YACA,aAAA,EAAA,OAAA,CAAA,MAAA,IAAA,KAAA;QACA,CAAA;QAEA,IAAA,SAAA,CAAA,MAAA,EAAA;YACA,IAAA,CAAA,YAAA,CAAA,GAAA,SAAA,CAAA,MAAA;QACA;QACA,IAAA,SAAA,CAAA,IAAA,EAAA;YACA,IAAA,CAAA,eAAA,CAAA,GAAA,SAAA,CAAA,IAAA;QACA;QAEA,OAAA,IAAA;IACA,CAAA,CAAA,OAAA;QACA,OAAA,CAAA,CAAA;IACA;AACA;AAEA,SAAA,cAAA,CAAA,MAAA,EAAA,IAAA,GAAA,GAAA,EAAA;IACA,IAAA;QACA,MAAA,GAAA,GAAA,IAAA,GAAA,CAAA,IAAA,EAAA,MAAA,CAAA;QACA,OAAA,GAAA,CAAA,QAAA,EAAA;IACA,CAAA,CAAA,OAAA;QACA,oCAAA;QACA,MAAA,GAAA,GAAA,CAAA,EAAA,MAAA,CAAA,CAAA;QAEA,IAAA,GAAA,CAAA,QAAA,CAAA,GAAA,CAAA,IAAA,IAAA,CAAA,UAAA,CAAA,GAAA,CAAA,EAAA;YACA,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;QAEA,IAAA,CAAA,GAAA,CAAA,QAAA,CAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,UAAA,CAAA,GAAA,CAAA,EAAA;YACA,OAAA,CAAA,EAAA,GAAA,CAAA,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;QAEA,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,IAAA,CAAA,CAAA;IACA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1141, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/integrations/node-fetch/index.ts"], "sourcesContent": ["import type { IntegrationFn } from '@sentry/core';\nimport { defineIntegration } from '@sentry/core';\nimport { generateInstrumentOnce } from '../../otel/instrument';\nimport { SentryNodeFetchInstrumentation } from './SentryNodeFetchInstrumentation';\n\nconst INTEGRATION_NAME = 'NodeFetch';\n\ninterface NodeFetchOptions {\n  /**\n   * Whether breadcrumbs should be recorded for requests.\n   * Defaults to true\n   */\n  breadcrumbs?: boolean;\n\n  /**\n   * Do not capture spans or breadcrumbs for outgoing fetch requests to URLs where the given callback returns `true`.\n   * This controls both span & breadcrumb creation - spans will be non recording if tracing is disabled.\n   */\n  ignoreOutgoingRequests?: (url: string) => boolean;\n}\n\nconst instrumentSentryNodeFetch = generateInstrumentOnce(\n  `${INTEGRATION_NAME}.sentry`,\n  SentryNodeFetchInstrumentation,\n  (options: NodeFetchOptions) => {\n    return options;\n  },\n);\n\nconst _nativeNodeFetchIntegration = ((options: NodeFetchOptions = {}) => {\n  return {\n    name: 'NodeFetch',\n    setupOnce() {\n      instrumentSentryNodeFetch(options);\n    },\n  };\n}) satisfies IntegrationFn;\n\nexport const nativeNodeFetchIntegration = defineIntegration(_nativeNodeFetchIntegration);\n"], "names": ["generateInstrumentOnce", "SentryNodeFetchInstrumentation", "defineIntegration"], "mappings": ";;;;;;AAKA,MAAM,gBAAA,GAAmB,WAAW;AAgBpC,MAAM,yBAAA,GAA4BA,WAAAA,sBAAsB,CACtD,CAAC,EAAA,gBAAA,CAAA,OAAA,CAAA,EACAC,+BAAAA,8BAAA,EACA,CAAA,OAAA,KAAA;IACA,OAAA,OAAA;AACA,CAAA;AAGA,MAAA,2BAAA,GAAA,CAAA,OAAA,GAAA,CAAA,CAAA,KAAA;IACA,OAAA;QACA,IAAA,EAAA,WAAA;QACA,SAAA,GAAA;YACA,yBAAA,CAAA,OAAA,CAAA;QACA,CAAA;IACA,CAAA;AACA,CAAA,CAAA;AAEA,MAAA,0BAAA,GAAAC,KAAAA,iBAAA,CAAA,2BAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1166, "column": 0}, "map": {"version": 3, "file": "context.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/integrations/context.ts"], "sourcesContent": ["/* eslint-disable max-lines */\n\nimport { execFile } from 'node:child_process';\nimport { readdir, readFile } from 'node:fs';\nimport * as os from 'node:os';\nimport { join } from 'node:path';\nimport { promisify } from 'node:util';\nimport type {\n  AppContext,\n  CloudResourceContext,\n  Contexts,\n  CultureContext,\n  DeviceContext,\n  Event,\n  IntegrationFn,\n  OsContext,\n} from '@sentry/core';\nimport { defineIntegration } from '@sentry/core';\n\nexport const readFileAsync = promisify(readFile);\nexport const readDirAsync = promisify(readdir);\n\n// Process enhanced with methods from Node 18, 20, 22 as @types/node\n// is on `14.18.0` to match minimum version requirements of the SDK\ninterface ProcessWithCurrentValues extends NodeJS.Process {\n  availableMemory?(): number;\n}\n\nconst INTEGRATION_NAME = 'Context';\n\ninterface DeviceContextOptions {\n  cpu?: boolean;\n  memory?: boolean;\n}\n\ninterface ContextOptions {\n  app?: boolean;\n  os?: boolean;\n  device?: DeviceContextOptions | boolean;\n  culture?: boolean;\n  cloudResource?: boolean;\n}\n\nconst _nodeContextIntegration = ((options: ContextOptions = {}) => {\n  let cachedContext: Promise<Contexts> | undefined;\n\n  const _options = {\n    app: true,\n    os: true,\n    device: true,\n    culture: true,\n    cloudResource: true,\n    ...options,\n  };\n\n  /** Add contexts to the event. Caches the context so we only look it up once. */\n  async function addContext(event: Event): Promise<Event> {\n    if (cachedContext === undefined) {\n      cachedContext = _getContexts();\n    }\n\n    const updatedContext = _updateContext(await cachedContext);\n\n    // TODO(v10): conditional with `sendDefaultPii` here?\n    event.contexts = {\n      ...event.contexts,\n      app: { ...updatedContext.app, ...event.contexts?.app },\n      os: { ...updatedContext.os, ...event.contexts?.os },\n      device: { ...updatedContext.device, ...event.contexts?.device },\n      culture: { ...updatedContext.culture, ...event.contexts?.culture },\n      cloud_resource: { ...updatedContext.cloud_resource, ...event.contexts?.cloud_resource },\n    };\n\n    return event;\n  }\n\n  /** Get the contexts from node. */\n  async function _getContexts(): Promise<Contexts> {\n    const contexts: Contexts = {};\n\n    if (_options.os) {\n      contexts.os = await getOsContext();\n    }\n\n    if (_options.app) {\n      contexts.app = getAppContext();\n    }\n\n    if (_options.device) {\n      contexts.device = getDeviceContext(_options.device);\n    }\n\n    if (_options.culture) {\n      const culture = getCultureContext();\n\n      if (culture) {\n        contexts.culture = culture;\n      }\n    }\n\n    if (_options.cloudResource) {\n      contexts.cloud_resource = getCloudResourceContext();\n    }\n\n    return contexts;\n  }\n\n  return {\n    name: INTEGRATION_NAME,\n    processEvent(event) {\n      return addContext(event);\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Capture context about the environment and the device that the client is running on, to events.\n */\nexport const nodeContextIntegration = defineIntegration(_nodeContextIntegration);\n\n/**\n * Updates the context with dynamic values that can change\n */\nfunction _updateContext(contexts: Contexts): Contexts {\n  // Only update properties if they exist\n\n  if (contexts.app?.app_memory) {\n    contexts.app.app_memory = process.memoryUsage().rss;\n  }\n\n  if (contexts.app?.free_memory && typeof (process as ProcessWithCurrentValues).availableMemory === 'function') {\n    const freeMemory = (process as ProcessWithCurrentValues).availableMemory?.();\n    if (freeMemory != null) {\n      contexts.app.free_memory = freeMemory;\n    }\n  }\n\n  if (contexts.device?.free_memory) {\n    contexts.device.free_memory = os.freemem();\n  }\n\n  return contexts;\n}\n\n/**\n * Returns the operating system context.\n *\n * Based on the current platform, this uses a different strategy to provide the\n * most accurate OS information. Since this might involve spawning subprocesses\n * or accessing the file system, this should only be executed lazily and cached.\n *\n *  - On macOS (Darwin), this will execute the `sw_vers` utility. The context\n *    has a `name`, `version`, `build` and `kernel_version` set.\n *  - On Linux, this will try to load a distribution release from `/etc` and set\n *    the `name`, `version` and `kernel_version` fields.\n *  - On all other platforms, only a `name` and `version` will be returned. Note\n *    that `version` might actually be the kernel version.\n */\nasync function getOsContext(): Promise<OsContext> {\n  const platformId = os.platform();\n  switch (platformId) {\n    case 'darwin':\n      return getDarwinInfo();\n    case 'linux':\n      return getLinuxInfo();\n    default:\n      return {\n        name: PLATFORM_NAMES[platformId] || platformId,\n        version: os.release(),\n      };\n  }\n}\n\nfunction getCultureContext(): CultureContext | undefined {\n  try {\n    if (typeof process.versions.icu !== 'string') {\n      // Node was built without ICU support\n      return;\n    }\n\n    // Check that node was built with full Intl support. Its possible it was built without support for non-English\n    // locales which will make resolvedOptions inaccurate\n    //\n    // https://nodejs.org/api/intl.html#detecting-internationalization-support\n    const january = new Date(9e8);\n    const spanish = new Intl.DateTimeFormat('es', { month: 'long' });\n    if (spanish.format(january) === 'enero') {\n      const options = Intl.DateTimeFormat().resolvedOptions();\n\n      return {\n        locale: options.locale,\n        timezone: options.timeZone,\n      };\n    }\n  } catch {\n    //\n  }\n\n  return;\n}\n\n/**\n * Get app context information from process\n */\nexport function getAppContext(): AppContext {\n  const app_memory = process.memoryUsage().rss;\n  const app_start_time = new Date(Date.now() - process.uptime() * 1000).toISOString();\n  // https://nodejs.org/api/process.html#processavailablememory\n  const appContext: AppContext = { app_start_time, app_memory };\n\n  if (typeof (process as ProcessWithCurrentValues).availableMemory === 'function') {\n    const freeMemory = (process as ProcessWithCurrentValues).availableMemory?.();\n    if (freeMemory != null) {\n      appContext.free_memory = freeMemory;\n    }\n  }\n\n  return appContext;\n}\n\n/**\n * Gets device information from os\n */\nexport function getDeviceContext(deviceOpt: DeviceContextOptions | true): DeviceContext {\n  const device: DeviceContext = {};\n\n  // Sometimes os.uptime() throws due to lacking permissions: https://github.com/getsentry/sentry-javascript/issues/8202\n  let uptime;\n  try {\n    uptime = os.uptime();\n  } catch {\n    // noop\n  }\n\n  // os.uptime or its return value seem to be undefined in certain environments (e.g. Azure functions).\n  // Hence, we only set boot time, if we get a valid uptime value.\n  // @see https://github.com/getsentry/sentry-javascript/issues/5856\n  if (typeof uptime === 'number') {\n    device.boot_time = new Date(Date.now() - uptime * 1000).toISOString();\n  }\n\n  device.arch = os.arch();\n\n  if (deviceOpt === true || deviceOpt.memory) {\n    device.memory_size = os.totalmem();\n    device.free_memory = os.freemem();\n  }\n\n  if (deviceOpt === true || deviceOpt.cpu) {\n    const cpuInfo = os.cpus() as os.CpuInfo[] | undefined;\n    const firstCpu = cpuInfo?.[0];\n    if (firstCpu) {\n      device.processor_count = cpuInfo.length;\n      device.cpu_description = firstCpu.model;\n      device.processor_frequency = firstCpu.speed;\n    }\n  }\n\n  return device;\n}\n\n/** Mapping of Node's platform names to actual OS names. */\nconst PLATFORM_NAMES: { [platform: string]: string } = {\n  aix: 'IBM AIX',\n  freebsd: 'FreeBSD',\n  openbsd: 'OpenBSD',\n  sunos: 'SunOS',\n  win32: 'Windows',\n};\n\n/** Linux version file to check for a distribution. */\ninterface DistroFile {\n  /** The file name, located in `/etc`. */\n  name: string;\n  /** Potential distributions to check. */\n  distros: [string, ...string[]];\n}\n\n/** Mapping of linux release files located in /etc to distributions. */\nconst LINUX_DISTROS: DistroFile[] = [\n  { name: 'fedora-release', distros: ['Fedora'] },\n  { name: 'redhat-release', distros: ['Red Hat Linux', 'Centos'] },\n  { name: 'redhat_version', distros: ['Red Hat Linux'] },\n  { name: 'SuSE-release', distros: ['SUSE Linux'] },\n  { name: 'lsb-release', distros: ['Ubuntu Linux', 'Arch Linux'] },\n  { name: 'debian_version', distros: ['Debian'] },\n  { name: 'debian_release', distros: ['Debian'] },\n  { name: 'arch-release', distros: ['Arch Linux'] },\n  { name: 'gentoo-release', distros: ['Gentoo Linux'] },\n  { name: 'novell-release', distros: ['SUSE Linux'] },\n  { name: 'alpine-release', distros: ['Alpine Linux'] },\n];\n\n/** Functions to extract the OS version from Linux release files. */\nconst LINUX_VERSIONS: {\n  [identifier: string]: (content: string) => string | undefined;\n} = {\n  alpine: content => content,\n  arch: content => matchFirst(/distrib_release=(.*)/, content),\n  centos: content => matchFirst(/release ([^ ]+)/, content),\n  debian: content => content,\n  fedora: content => matchFirst(/release (..)/, content),\n  mint: content => matchFirst(/distrib_release=(.*)/, content),\n  red: content => matchFirst(/release ([^ ]+)/, content),\n  suse: content => matchFirst(/VERSION = (.*)\\n/, content),\n  ubuntu: content => matchFirst(/distrib_release=(.*)/, content),\n};\n\n/**\n * Executes a regular expression with one capture group.\n *\n * @param regex A regular expression to execute.\n * @param text Content to execute the RegEx on.\n * @returns The captured string if matched; otherwise undefined.\n */\nfunction matchFirst(regex: RegExp, text: string): string | undefined {\n  const match = regex.exec(text);\n  return match ? match[1] : undefined;\n}\n\n/** Loads the macOS operating system context. */\nasync function getDarwinInfo(): Promise<OsContext> {\n  // Default values that will be used in case no operating system information\n  // can be loaded. The default version is computed via heuristics from the\n  // kernel version, but the build ID is missing.\n  const darwinInfo: OsContext = {\n    kernel_version: os.release(),\n    name: 'Mac OS X',\n    version: `10.${Number(os.release().split('.')[0]) - 4}`,\n  };\n\n  try {\n    // We try to load the actual macOS version by executing the `sw_vers` tool.\n    // This tool should be available on every standard macOS installation. In\n    // case this fails, we stick with the values computed above.\n\n    const output = await new Promise<string>((resolve, reject) => {\n      execFile('/usr/bin/sw_vers', (error: Error | null, stdout: string) => {\n        if (error) {\n          reject(error);\n          return;\n        }\n        resolve(stdout);\n      });\n    });\n\n    darwinInfo.name = matchFirst(/^ProductName:\\s+(.*)$/m, output);\n    darwinInfo.version = matchFirst(/^ProductVersion:\\s+(.*)$/m, output);\n    darwinInfo.build = matchFirst(/^BuildVersion:\\s+(.*)$/m, output);\n  } catch {\n    // ignore\n  }\n\n  return darwinInfo;\n}\n\n/** Returns a distribution identifier to look up version callbacks. */\nfunction getLinuxDistroId(name: string): string {\n  return (name.split(' ') as [string])[0].toLowerCase();\n}\n\n/** Loads the Linux operating system context. */\nasync function getLinuxInfo(): Promise<OsContext> {\n  // By default, we cannot assume anything about the distribution or Linux\n  // version. `os.release()` returns the kernel version and we assume a generic\n  // \"Linux\" name, which will be replaced down below.\n  const linuxInfo: OsContext = {\n    kernel_version: os.release(),\n    name: 'Linux',\n  };\n\n  try {\n    // We start guessing the distribution by listing files in the /etc\n    // directory. This is were most Linux distributions (except Knoppix) store\n    // release files with certain distribution-dependent meta data. We search\n    // for exactly one known file defined in `LINUX_DISTROS` and exit if none\n    // are found. In case there are more than one file, we just stick with the\n    // first one.\n    const etcFiles = await readDirAsync('/etc');\n    const distroFile = LINUX_DISTROS.find(file => etcFiles.includes(file.name));\n    if (!distroFile) {\n      return linuxInfo;\n    }\n\n    // Once that file is known, load its contents. To make searching in those\n    // files easier, we lowercase the file contents. Since these files are\n    // usually quite small, this should not allocate too much memory and we only\n    // hold on to it for a very short amount of time.\n    const distroPath = join('/etc', distroFile.name);\n    const contents = ((await readFileAsync(distroPath, { encoding: 'utf-8' })) as string).toLowerCase();\n\n    // Some Linux distributions store their release information in the same file\n    // (e.g. RHEL and Centos). In those cases, we scan the file for an\n    // identifier, that basically consists of the first word of the linux\n    // distribution name (e.g. \"red\" for Red Hat). In case there is no match, we\n    // just assume the first distribution in our list.\n    const { distros } = distroFile;\n    linuxInfo.name = distros.find(d => contents.indexOf(getLinuxDistroId(d)) >= 0) || distros[0];\n\n    // Based on the found distribution, we can now compute the actual version\n    // number. This is different for every distribution, so several strategies\n    // are computed in `LINUX_VERSIONS`.\n    const id = getLinuxDistroId(linuxInfo.name);\n    linuxInfo.version = LINUX_VERSIONS[id]?.(contents);\n  } catch {\n    // ignore\n  }\n\n  return linuxInfo;\n}\n\n/**\n * Grabs some information about hosting provider based on best effort.\n */\nfunction getCloudResourceContext(): CloudResourceContext | undefined {\n  if (process.env.VERCEL) {\n    // https://vercel.com/docs/concepts/projects/environment-variables/system-environment-variables#system-environment-variables\n    return {\n      'cloud.provider': 'vercel',\n      'cloud.region': process.env.VERCEL_REGION,\n    };\n  } else if (process.env.AWS_REGION) {\n    // https://docs.aws.amazon.com/lambda/latest/dg/configuration-envvars.html\n    return {\n      'cloud.provider': 'aws',\n      'cloud.region': process.env.AWS_REGION,\n      'cloud.platform': process.env.AWS_EXECUTION_ENV,\n    };\n  } else if (process.env.GCP_PROJECT) {\n    // https://cloud.google.com/composer/docs/how-to/managing/environment-variables#reserved_variables\n    return {\n      'cloud.provider': 'gcp',\n    };\n  } else if (process.env.ALIYUN_REGION_ID) {\n    // TODO: find where I found these environment variables - at least gc.github.com returns something\n    return {\n      'cloud.provider': 'alibaba_cloud',\n      'cloud.region': process.env.ALIYUN_REGION_ID,\n    };\n  } else if (process.env.WEBSITE_SITE_NAME && process.env.REGION_NAME) {\n    // https://learn.microsoft.com/en-us/azure/app-service/reference-app-settings?tabs=kudu%2Cdotnet#app-environment\n    return {\n      'cloud.provider': 'azure',\n      'cloud.region': process.env.REGION_NAME,\n    };\n  } else if (process.env.IBM_CLOUD_REGION) {\n    // TODO: find where I found these environment variables - at least gc.github.com returns something\n    return {\n      'cloud.provider': 'ibm_cloud',\n      'cloud.region': process.env.IBM_CLOUD_REGION,\n    };\n  } else if (process.env.TENCENTCLOUD_REGION) {\n    // https://www.tencentcloud.com/document/product/583/32748\n    return {\n      'cloud.provider': 'tencent_cloud',\n      'cloud.region': process.env.TENCENTCLOUD_REGION,\n      'cloud.account.id': process.env.TENCENTCLOUD_APPID,\n      'cloud.availability_zone': process.env.TENCENTCLOUD_ZONE,\n    };\n  } else if (process.env.NETLIFY) {\n    // https://docs.netlify.com/configure-builds/environment-variables/#read-only-variables\n    return {\n      'cloud.provider': 'netlify',\n    };\n  } else if (process.env.FLY_REGION) {\n    // https://fly.io/docs/reference/runtime-environment/\n    return {\n      'cloud.provider': 'fly.io',\n      'cloud.region': process.env.FLY_REGION,\n    };\n  } else if (process.env.DYNO) {\n    // https://devcenter.heroku.com/articles/dynos#local-environment-variables\n    return {\n      'cloud.provider': 'heroku',\n    };\n  } else {\n    return undefined;\n  }\n}\n"], "names": ["promisify", "readFile", "readdir", "defineIntegration", "execFile", "join"], "mappings": ";;;;;;;;;AAAA,4BAAA,SAmBa,aAAA,GAAgBA,UAAAA,SAAS,CAACC,QAAAA,QAAQ;MAClC,YAAA,GAAeD,UAAAA,SAAS,CAACE,QAAAA,OAAO;AAE7C,oEAAA;AACA,mEAAA;AAKA,MAAM,gBAAA,GAAmB,SAAS;AAelC,MAAM,uBAAA,GAA2B,CAAC,OAAO,GAAmB,CAAA,CAAE,KAAK;IACjE,IAAI,aAAa;IAEjB,MAAM,WAAW;QACf,GAAG,EAAE,IAAI;QACT,EAAE,EAAE,IAAI;QACR,MAAM,EAAE,IAAI;QACZ,OAAO,EAAE,IAAI;QACb,aAAa,EAAE,IAAI;QACnB,GAAG,OAAO;IACd,CAAG;IAEH,8EAAA,GACE,eAAe,UAAU,CAAC,KAAK,EAAyB;QACtD,IAAI,aAAA,KAAkB,SAAS,EAAE;YAC/B,aAAA,GAAgB,YAAY,EAAE;QACpC;QAEI,MAAM,cAAA,GAAiB,cAAc,CAAC,MAAM,aAAa,CAAC;QAE9D,qDAAA;QACI,KAAK,CAAC,QAAA,GAAW;YACf,GAAG,KAAK,CAAC,QAAQ;YACjB,GAAG,EAAE;gBAAE,GAAG,cAAc,CAAC,GAAG;gBAAE,GAAG,KAAK,CAAC,QAAQ,EAAE,GAAA;YAAA,CAAK;YACtD,EAAE,EAAE;gBAAE,GAAG,cAAc,CAAC,EAAE;gBAAE,GAAG,KAAK,CAAC,QAAQ,EAAE,EAAA;YAAA,CAAI;YACnD,MAAM,EAAE;gBAAE,GAAG,cAAc,CAAC,MAAM;gBAAE,GAAG,KAAK,CAAC,QAAQ,EAAE,MAAA;YAAA,CAAQ;YAC/D,OAAO,EAAE;gBAAE,GAAG,cAAc,CAAC,OAAO;gBAAE,GAAG,KAAK,CAAC,QAAQ,EAAE,OAAA;YAAA,CAAS;YAClE,cAAc,EAAE;gBAAE,GAAG,cAAc,CAAC,cAAc;gBAAE,GAAG,KAAK,CAAC,QAAQ,EAAE,cAAA;YAAA,CAAgB;QAC7F,CAAK;QAED,OAAO,KAAK;IAChB;IAEA,gCAAA,GACE,eAAe,YAAY,GAAsB;QAC/C,MAAM,QAAQ,GAAa,CAAA,CAAE;QAE7B,IAAI,QAAQ,CAAC,EAAE,EAAE;YACf,QAAQ,CAAC,EAAA,GAAK,MAAM,YAAY,EAAE;QACxC;QAEI,IAAI,QAAQ,CAAC,GAAG,EAAE;YAChB,QAAQ,CAAC,GAAA,GAAM,aAAa,EAAE;QACpC;QAEI,IAAI,QAAQ,CAAC,MAAM,EAAE;YACnB,QAAQ,CAAC,MAAA,GAAS,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC;QACzD;QAEI,IAAI,QAAQ,CAAC,OAAO,EAAE;YACpB,MAAM,OAAA,GAAU,iBAAiB,EAAE;YAEnC,IAAI,OAAO,EAAE;gBACX,QAAQ,CAAC,OAAA,GAAU,OAAO;YAClC;QACA;QAEI,IAAI,QAAQ,CAAC,aAAa,EAAE;YAC1B,QAAQ,CAAC,cAAA,GAAiB,uBAAuB,EAAE;QACzD;QAEI,OAAO,QAAQ;IACnB;IAEE,OAAO;QACL,IAAI,EAAE,gBAAgB;QACtB,YAAY,EAAC,KAAK,EAAE;YAClB,OAAO,UAAU,CAAC,KAAK,CAAC;QAC9B,CAAK;IACL,CAAG;AACH,CAAC,CAAA;AAED;;CAEA,SACa,sBAAA,GAAyBC,KAAAA,iBAAiB,CAAC,uBAAuB;AAE/E;;CAEA,GACA,SAAS,cAAc,CAAC,QAAQ,EAAsB;IACtD,uCAAA;IAEE,IAAI,QAAQ,CAAC,GAAG,EAAE,UAAU,EAAE;QAC5B,QAAQ,CAAC,GAAG,CAAC,UAAA,GAAa,OAAO,CAAC,WAAW,EAAE,CAAC,GAAG;IACvD;IAEE,IAAI,QAAQ,CAAC,GAAG,EAAE,WAAA,IAAe,OAAO,AAAC,QAAqC,eAAA,KAAoB,UAAU,EAAE;QAC5G,MAAM,UAAA,GAAa,AAAC,OAAA,CAAqC,eAAe,IAAI;QAC5E,IAAI,UAAA,IAAc,IAAI,EAAE;YACtB,QAAQ,CAAC,GAAG,CAAC,WAAA,GAAc,UAAU;QAC3C;IACA;IAEE,IAAI,QAAQ,CAAC,MAAM,EAAE,WAAW,EAAE;QAChC,QAAQ,CAAC,MAAM,CAAC,WAAA,GAAc,EAAE,CAAC,OAAO,EAAE;IAC9C;IAEE,OAAO,QAAQ;AACjB;AAEA;;;;;;;;;;;;;CAaA,GACA,eAAe,YAAY,GAAuB;IAChD,MAAM,UAAA,GAAa,EAAE,CAAC,QAAQ,EAAE;IAChC,OAAQ,UAAU;QAChB,KAAK,QAAQ;YACX,OAAO,aAAa,EAAE;QACxB,KAAK,OAAO;YACV,OAAO,YAAY,EAAE;QACvB;YACE,OAAO;gBACL,IAAI,EAAE,cAAc,CAAC,UAAU,CAAA,IAAK,UAAU;gBAC9C,OAAO,EAAE,EAAE,CAAC,OAAO,EAAE;YAC7B,CAAO;IACP;AACA;AAEA,SAAS,iBAAiB,GAA+B;IACvD,IAAI;QACF,IAAI,OAAO,OAAO,CAAC,QAAQ,CAAC,GAAA,KAAQ,QAAQ,EAAE;YAClD,qCAAA;YACM;QACN;QAEA,8GAAA;QACA,qDAAA;QACA,EAAA;QACA,0EAAA;QACI,MAAM,OAAA,GAAU,IAAI,IAAI,CAAC,GAAG,CAAC;QAC7B,MAAM,OAAA,GAAU,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE;YAAE,KAAK,EAAE,MAAA;QAAA,CAAQ,CAAC;QAChE,IAAI,OAAO,CAAC,MAAM,CAAC,OAAO,CAAA,KAAM,OAAO,EAAE;YACvC,MAAM,OAAA,GAAU,IAAI,CAAC,cAAc,EAAE,CAAC,eAAe,EAAE;YAEvD,OAAO;gBACL,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAClC,CAAO;QACP;IACA,EAAI,OAAM;IACV,EAAA;IACA;IAEE;AACF;AAEA;;CAEA,GACO,SAAS,aAAa,GAAe;IAC1C,MAAM,aAAa,OAAO,CAAC,WAAW,EAAE,CAAC,GAAG;IAC5C,MAAM,iBAAiB,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAC,GAAI,OAAO,CAAC,MAAM,EAAC,GAAI,IAAI,CAAC,CAAC,WAAW,EAAE;IACrF,6DAAA;IACE,MAAM,UAAU,GAAe;QAAE,cAAc;QAAE;IAAA,CAAY;IAE7D,IAAI,OAAO,AAAC,OAAA,CAAqC,eAAA,KAAoB,UAAU,EAAE;QAC/E,MAAM,UAAA,GAAa,AAAC,OAAA,CAAqC,eAAe,IAAI;QAC5E,IAAI,UAAA,IAAc,IAAI,EAAE;YACtB,UAAU,CAAC,WAAA,GAAc,UAAU;QACzC;IACA;IAEE,OAAO,UAAU;AACnB;AAEA;;CAEA,GACO,SAAS,gBAAgB,CAAC,SAAS,EAA8C;IACtF,MAAM,MAAM,GAAkB,CAAA,CAAE;IAElC,sHAAA;IACE,IAAI,MAAM;IACV,IAAI;QACF,SAAS,EAAE,CAAC,MAAM,EAAE;IACxB,EAAI,OAAM;IACV,OAAA;IACA;IAEA,qGAAA;IACA,gEAAA;IACA,kEAAA;IACE,IAAI,OAAO,MAAA,KAAW,QAAQ,EAAE;QAC9B,MAAM,CAAC,SAAA,GAAY,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAC,GAAI,SAAS,IAAI,CAAC,CAAC,WAAW,EAAE;IACzE;IAEE,MAAM,CAAC,IAAA,GAAO,EAAE,CAAC,IAAI,EAAE;IAEvB,IAAI,SAAA,KAAc,QAAQ,SAAS,CAAC,MAAM,EAAE;QAC1C,MAAM,CAAC,WAAA,GAAc,EAAE,CAAC,QAAQ,EAAE;QAClC,MAAM,CAAC,WAAA,GAAc,EAAE,CAAC,OAAO,EAAE;IACrC;IAEE,IAAI,SAAA,KAAc,QAAQ,SAAS,CAAC,GAAG,EAAE;QACvC,MAAM,OAAA,GAAU,EAAE,CAAC,IAAI,EAAC;QACxB,MAAM,QAAA,GAAW,OAAO,EAAA,CAAG,CAAC,CAAC;QAC7B,IAAI,QAAQ,EAAE;YACZ,MAAM,CAAC,eAAA,GAAkB,OAAO,CAAC,MAAM;YACvC,MAAM,CAAC,eAAA,GAAkB,QAAQ,CAAC,KAAK;YACvC,MAAM,CAAC,mBAAA,GAAsB,QAAQ,CAAC,KAAK;QACjD;IACA;IAEE,OAAO,MAAM;AACf;AAEA,yDAAA,GACA,MAAM,cAAc,GAAmC;IACrD,GAAG,EAAE,SAAS;IACd,OAAO,EAAE,SAAS;IAClB,OAAO,EAAE,SAAS;IAClB,KAAK,EAAE,OAAO;IACd,KAAK,EAAE,SAAS;AAClB,CAAC;AAED,oDAAA,GAQA,qEAAA,GACA,MAAM,aAAa,GAAiB;IAClC;QAAE,IAAI,EAAE,gBAAgB;QAAE,OAAO,EAAE;YAAC,QAAQ;SAAA;IAAA,CAAG;IAC/C;QAAE,IAAI,EAAE,gBAAgB;QAAE,OAAO,EAAE;YAAC,eAAe;YAAE,QAAQ;SAAA;IAAA,CAAG;IAChE;QAAE,IAAI,EAAE,gBAAgB;QAAE,OAAO,EAAE;YAAC,eAAe;SAAA;IAAA,CAAG;IACtD;QAAE,IAAI,EAAE,cAAc;QAAE,OAAO,EAAE;YAAC,YAAY;SAAA;IAAA,CAAG;IACjD;QAAE,IAAI,EAAE,aAAa;QAAE,OAAO,EAAE;YAAC,cAAc;YAAE,YAAY;SAAA;IAAA,CAAG;IAChE;QAAE,IAAI,EAAE,gBAAgB;QAAE,OAAO,EAAE;YAAC,QAAQ;SAAA;IAAA,CAAG;IAC/C;QAAE,IAAI,EAAE,gBAAgB;QAAE,OAAO,EAAE;YAAC,QAAQ;SAAA;IAAA,CAAG;IAC/C;QAAE,IAAI,EAAE,cAAc;QAAE,OAAO,EAAE;YAAC,YAAY;SAAA;IAAA,CAAG;IACjD;QAAE,IAAI,EAAE,gBAAgB;QAAE,OAAO,EAAE;YAAC,cAAc;SAAA;IAAA,CAAG;IACrD;QAAE,IAAI,EAAE,gBAAgB;QAAE,OAAO,EAAE;YAAC,YAAY;SAAA;IAAA,CAAG;IACnD;QAAE,IAAI,EAAE,gBAAgB;QAAE,OAAO,EAAE;YAAC,cAAc;SAAA;IAAA,CAAG;CACtD;AAED,kEAAA,GACA,MAAM,iBAEF;IACF,MAAM,GAAE,OAAA,GAAW,OAAO;IAC1B,IAAI,GAAE,OAAA,GAAW,UAAU,CAAC,sBAAsB,EAAE,OAAO,CAAC;IAC5D,MAAM,GAAE,OAAA,GAAW,UAAU,CAAC,iBAAiB,EAAE,OAAO,CAAC;IACzD,MAAM,GAAE,OAAA,GAAW,OAAO;IAC1B,MAAM,GAAE,OAAA,GAAW,UAAU,CAAC,cAAc,EAAE,OAAO,CAAC;IACtD,IAAI,GAAE,OAAA,GAAW,UAAU,CAAC,sBAAsB,EAAE,OAAO,CAAC;IAC5D,GAAG,GAAE,OAAA,GAAW,UAAU,CAAC,iBAAiB,EAAE,OAAO,CAAC;IACtD,IAAI,GAAE,OAAA,GAAW,UAAU,CAAC,kBAAkB,EAAE,OAAO,CAAC;IACxD,MAAM,GAAE,OAAA,GAAW,UAAU,CAAC,sBAAsB,EAAE,OAAO,CAAC;AAChE,CAAC;AAED;;;;;;CAMA,GACA,SAAS,UAAU,CAAC,KAAK,EAAU,IAAI,EAA8B;IACnE,MAAM,QAAQ,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;IAC9B,OAAO,QAAQ,KAAK,CAAC,CAAC,CAAA,GAAI,SAAS;AACrC;AAEA,8CAAA,GACA,eAAe,aAAa,GAAuB;IACnD,2EAAA;IACA,yEAAA;IACA,+CAAA;IACE,MAAM,UAAU,GAAc;QAC5B,cAAc,EAAE,EAAE,CAAC,OAAO,EAAE;QAC5B,IAAI,EAAE,UAAU;QAChB,OAAO,EAAE,CAAC,GAAG,EAAE,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA,GAAI,CAAC,CAAC,CAAA;IACA,CAAA;IAEA,IAAA;QACA,2EAAA;QACA,yEAAA;QACA,4DAAA;QAEA,MAAA,MAAA,GAAA,MAAA,IAAA,OAAA,CAAA,CAAA,OAAA,EAAA,MAAA,KAAA;YACAC,mBAAAA,QAAA,CAAA,kBAAA,EAAA,CAAA,KAAA,EAAA,MAAA,KAAA;gBACA,IAAA,KAAA,EAAA;oBACA,MAAA,CAAA,KAAA,CAAA;oBACA;gBACA;gBACA,OAAA,CAAA,MAAA,CAAA;YACA,CAAA,CAAA;QACA,CAAA,CAAA;QAEA,UAAA,CAAA,IAAA,GAAA,UAAA,CAAA,wBAAA,EAAA,MAAA,CAAA;QACA,UAAA,CAAA,OAAA,GAAA,UAAA,CAAA,2BAAA,EAAA,MAAA,CAAA;QACA,UAAA,CAAA,KAAA,GAAA,UAAA,CAAA,yBAAA,EAAA,MAAA,CAAA;IACA,CAAA,CAAA,OAAA;IACA,SAAA;IACA;IAEA,OAAA,UAAA;AACA;AAEA,oEAAA,GACA,SAAA,gBAAA,CAAA,IAAA,EAAA;IACA,OAAA,IAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,EAAA;AACA;AAEA,8CAAA,GACA,eAAA,YAAA,GAAA;IACA,wEAAA;IACA,6EAAA;IACA,mDAAA;IACA,MAAA,SAAA,GAAA;QACA,cAAA,EAAA,EAAA,CAAA,OAAA,EAAA;QACA,IAAA,EAAA,OAAA;IACA,CAAA;IAEA,IAAA;QACA,kEAAA;QACA,0EAAA;QACA,yEAAA;QACA,yEAAA;QACA,0EAAA;QACA,aAAA;QACA,MAAA,QAAA,GAAA,MAAA,YAAA,CAAA,MAAA,CAAA;QACA,MAAA,UAAA,GAAA,aAAA,CAAA,IAAA,EAAA,IAAA,GAAA,QAAA,CAAA,QAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;QACA,IAAA,CAAA,UAAA,EAAA;YACA,OAAA,SAAA;QACA;QAEA,yEAAA;QACA,sEAAA;QACA,4EAAA;QACA,iDAAA;QACA,MAAA,UAAA,GAAAC,UAAAA,IAAA,CAAA,MAAA,EAAA,UAAA,CAAA,IAAA,CAAA;QACA,MAAA,QAAA,GAAA,CAAA,MAAA,aAAA,CAAA,UAAA,EAAA;YAAA,QAAA,EAAA,OAAA;QAAA,CAAA,CAAA,EAAA,WAAA,EAAA;QAEA,4EAAA;QACA,kEAAA;QACA,qEAAA;QACA,4EAAA;QACA,kDAAA;QACA,MAAA,EAAA,OAAA,EAAA,GAAA,UAAA;QACA,SAAA,CAAA,IAAA,GAAA,OAAA,CAAA,IAAA,EAAA,CAAA,GAAA,QAAA,CAAA,OAAA,CAAA,gBAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,IAAA,OAAA,CAAA,CAAA,CAAA;QAEA,yEAAA;QACA,0EAAA;QACA,oCAAA;QACA,MAAA,EAAA,GAAA,gBAAA,CAAA,SAAA,CAAA,IAAA,CAAA;QACA,SAAA,CAAA,OAAA,GAAA,cAAA,CAAA,EAAA,CAAA,GAAA,QAAA,CAAA;IACA,CAAA,CAAA,OAAA;IACA,SAAA;IACA;IAEA,OAAA,SAAA;AACA;AAEA;;CAEA,GACA,SAAA,uBAAA,GAAA;IACA,IAAA,OAAA,CAAA,GAAA,CAAA,MAAA,EAAA;QACA,4HAAA;QACA,OAAA;YACA,gBAAA,EAAA,QAAA;YACA,cAAA,EAAA,OAAA,CAAA,GAAA,CAAA,aAAA;QACA,CAAA;IACA,CAAA,MAAA,IAAA,OAAA,CAAA,GAAA,CAAA,UAAA,EAAA;QACA,0EAAA;QACA,OAAA;YACA,gBAAA,EAAA,KAAA;YACA,cAAA,EAAA,OAAA,CAAA,GAAA,CAAA,UAAA;YACA,gBAAA,EAAA,OAAA,CAAA,GAAA,CAAA,iBAAA;QACA,CAAA;IACA,CAAA,MAAA,IAAA,OAAA,CAAA,GAAA,CAAA,WAAA,EAAA;QACA,kGAAA;QACA,OAAA;YACA,gBAAA,EAAA,KAAA;QACA,CAAA;IACA,CAAA,MAAA,IAAA,OAAA,CAAA,GAAA,CAAA,gBAAA,EAAA;QACA,kGAAA;QACA,OAAA;YACA,gBAAA,EAAA,eAAA;YACA,cAAA,EAAA,OAAA,CAAA,GAAA,CAAA,gBAAA;QACA,CAAA;IACA,CAAA,MAAA,IAAA,OAAA,CAAA,GAAA,CAAA,iBAAA,IAAA,OAAA,CAAA,GAAA,CAAA,WAAA,EAAA;QACA,gHAAA;QACA,OAAA;YACA,gBAAA,EAAA,OAAA;YACA,cAAA,EAAA,OAAA,CAAA,GAAA,CAAA,WAAA;QACA,CAAA;IACA,CAAA,MAAA,IAAA,OAAA,CAAA,GAAA,CAAA,gBAAA,EAAA;QACA,kGAAA;QACA,OAAA;YACA,gBAAA,EAAA,WAAA;YACA,cAAA,EAAA,OAAA,CAAA,GAAA,CAAA,gBAAA;QACA,CAAA;IACA,CAAA,MAAA,IAAA,OAAA,CAAA,GAAA,CAAA,mBAAA,EAAA;QACA,0DAAA;QACA,OAAA;YACA,gBAAA,EAAA,eAAA;YACA,cAAA,EAAA,OAAA,CAAA,GAAA,CAAA,mBAAA;YACA,kBAAA,EAAA,OAAA,CAAA,GAAA,CAAA,kBAAA;YACA,yBAAA,EAAA,OAAA,CAAA,GAAA,CAAA,iBAAA;QACA,CAAA;IACA,CAAA,MAAA,IAAA,OAAA,CAAA,GAAA,CAAA,OAAA,EAAA;QACA,uFAAA;QACA,OAAA;YACA,gBAAA,EAAA,SAAA;QACA,CAAA;IACA,CAAA,MAAA,IAAA,OAAA,CAAA,GAAA,CAAA,UAAA,EAAA;QACA,qDAAA;QACA,OAAA;YACA,gBAAA,EAAA,QAAA;YACA,cAAA,EAAA,OAAA,CAAA,GAAA,CAAA,UAAA;QACA,CAAA;IACA,CAAA,MAAA,IAAA,OAAA,CAAA,GAAA,CAAA,IAAA,EAAA;QACA,0EAAA;QACA,OAAA;YACA,gBAAA,EAAA,QAAA;QACA,CAAA;IACA,CAAA,MAAA;QACA,OAAA,SAAA;IACA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1628, "column": 0}, "map": {"version": 3, "file": "contextlines.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/integrations/contextlines.ts"], "sourcesContent": ["import { createReadStream } from 'node:fs';\nimport { createInterface } from 'node:readline';\nimport type { Event, IntegrationFn, StackFrame } from '@sentry/core';\nimport { debug, defineIntegration, LRUMap, snipLine } from '@sentry/core';\nimport { DEBUG_BUILD } from '../debug-build';\n\nconst LRU_FILE_CONTENTS_CACHE = new LRUMap<string, Record<number, string>>(10);\nconst LRU_FILE_CONTENTS_FS_READ_FAILED = new LRUMap<string, 1>(20);\nconst DEFAULT_LINES_OF_CONTEXT = 7;\nconst INTEGRATION_NAME = 'ContextLines';\n// Determines the upper bound of lineno/colno that we will attempt to read. Large colno values are likely to be\n// minified code while large lineno values are likely to be bundled code.\n// Exported for testing purposes.\nexport const MAX_CONTEXTLINES_COLNO: number = 1000;\nexport const MAX_CONTEXTLINES_LINENO: number = 10000;\n\ninterface ContextLinesOptions {\n  /**\n   * Sets the number of context lines for each frame when loading a file.\n   * Defaults to 7.\n   *\n   * Set to 0 to disable loading and inclusion of source files.\n   **/\n  frameContextLines?: number;\n}\n\n/**\n * Exported for testing purposes.\n */\nexport function resetFileContentCache(): void {\n  LRU_FILE_CONTENTS_CACHE.clear();\n}\n\n/**\n * Get or init map value\n */\nfunction emplace<T extends LRUMap<K, V>, K extends string, V>(map: T, key: K, contents: V): V {\n  const value = map.get(key);\n\n  if (value === undefined) {\n    map.set(key, contents);\n    return contents;\n  }\n\n  return value;\n}\n\n/**\n * Determines if context lines should be skipped for a file.\n * - .min.(mjs|cjs|js) files are and not useful since they dont point to the original source\n * - node: prefixed modules are part of the runtime and cannot be resolved to a file\n * - data: skip json, wasm and inline js https://nodejs.org/api/esm.html#data-imports\n */\nfunction shouldSkipContextLinesForFile(path: string): boolean {\n  // Test the most common prefix and extension first. These are the ones we\n  // are most likely to see in user applications and are the ones we can break out of first.\n  if (path.startsWith('node:')) return true;\n  if (path.endsWith('.min.js')) return true;\n  if (path.endsWith('.min.cjs')) return true;\n  if (path.endsWith('.min.mjs')) return true;\n  if (path.startsWith('data:')) return true;\n  return false;\n}\n\n/**\n * Determines if we should skip contextlines based off the max lineno and colno values.\n */\nfunction shouldSkipContextLinesForFrame(frame: StackFrame): boolean {\n  if (frame.lineno !== undefined && frame.lineno > MAX_CONTEXTLINES_LINENO) return true;\n  if (frame.colno !== undefined && frame.colno > MAX_CONTEXTLINES_COLNO) return true;\n  return false;\n}\n/**\n * Checks if we have all the contents that we need in the cache.\n */\nfunction rangeExistsInContentCache(file: string, range: ReadlineRange): boolean {\n  const contents = LRU_FILE_CONTENTS_CACHE.get(file);\n  if (contents === undefined) return false;\n\n  for (let i = range[0]; i <= range[1]; i++) {\n    if (contents[i] === undefined) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Creates contiguous ranges of lines to read from a file. In the case where context lines overlap,\n * the ranges are merged to create a single range.\n */\nfunction makeLineReaderRanges(lines: number[], linecontext: number): ReadlineRange[] {\n  if (!lines.length) {\n    return [];\n  }\n\n  let i = 0;\n  const line = lines[0];\n\n  if (typeof line !== 'number') {\n    return [];\n  }\n\n  let current = makeContextRange(line, linecontext);\n  const out: ReadlineRange[] = [];\n  // eslint-disable-next-line no-constant-condition\n  while (true) {\n    if (i === lines.length - 1) {\n      out.push(current);\n      break;\n    }\n\n    // If the next line falls into the current range, extend the current range to lineno + linecontext.\n    const next = lines[i + 1];\n    if (typeof next !== 'number') {\n      break;\n    }\n    if (next <= current[1]) {\n      current[1] = next + linecontext;\n    } else {\n      out.push(current);\n      current = makeContextRange(next, linecontext);\n    }\n\n    i++;\n  }\n\n  return out;\n}\n\n/**\n * Extracts lines from a file and stores them in a cache.\n */\nfunction getContextLinesFromFile(path: string, ranges: ReadlineRange[], output: Record<number, string>): Promise<void> {\n  return new Promise((resolve, _reject) => {\n    // It is important *not* to have any async code between createInterface and the 'line' event listener\n    // as it will cause the 'line' event to\n    // be emitted before the listener is attached.\n    const stream = createReadStream(path);\n    const lineReaded = createInterface({\n      input: stream,\n    });\n\n    // We need to explicitly destroy the stream to prevent memory leaks,\n    // removing the listeners on the readline interface is not enough.\n    // See: https://github.com/nodejs/node/issues/9002 and https://github.com/getsentry/sentry-javascript/issues/14892\n    function destroyStreamAndResolve(): void {\n      stream.destroy();\n      resolve();\n    }\n\n    // Init at zero and increment at the start of the loop because lines are 1 indexed.\n    let lineNumber = 0;\n    let currentRangeIndex = 0;\n    const range = ranges[currentRangeIndex];\n    if (range === undefined) {\n      // We should never reach this point, but if we do, we should resolve the promise to prevent it from hanging.\n      destroyStreamAndResolve();\n      return;\n    }\n    let rangeStart = range[0];\n    let rangeEnd = range[1];\n\n    // We use this inside Promise.all, so we need to resolve the promise even if there is an error\n    // to prevent Promise.all from short circuiting the rest.\n    function onStreamError(e: Error): void {\n      // Mark file path as failed to read and prevent multiple read attempts.\n      LRU_FILE_CONTENTS_FS_READ_FAILED.set(path, 1);\n      DEBUG_BUILD && debug.error(`Failed to read file: ${path}. Error: ${e}`);\n      lineReaded.close();\n      lineReaded.removeAllListeners();\n      destroyStreamAndResolve();\n    }\n\n    // We need to handle the error event to prevent the process from crashing in < Node 16\n    // https://github.com/nodejs/node/pull/31603\n    stream.on('error', onStreamError);\n    lineReaded.on('error', onStreamError);\n    lineReaded.on('close', destroyStreamAndResolve);\n\n    lineReaded.on('line', line => {\n      lineNumber++;\n      if (lineNumber < rangeStart) return;\n\n      // !Warning: This mutates the cache by storing the snipped line into the cache.\n      output[lineNumber] = snipLine(line, 0);\n\n      if (lineNumber >= rangeEnd) {\n        if (currentRangeIndex === ranges.length - 1) {\n          // We need to close the file stream and remove listeners, else the reader will continue to run our listener;\n          lineReaded.close();\n          lineReaded.removeAllListeners();\n          return;\n        }\n        currentRangeIndex++;\n        const range = ranges[currentRangeIndex];\n        if (range === undefined) {\n          // This should never happen as it means we have a bug in the context.\n          lineReaded.close();\n          lineReaded.removeAllListeners();\n          return;\n        }\n        rangeStart = range[0];\n        rangeEnd = range[1];\n      }\n    });\n  });\n}\n\n/**\n * Adds surrounding (context) lines of the line that an exception occurred on to the event.\n * This is done by reading the file line by line and extracting the lines. The extracted lines are stored in\n * a cache to prevent multiple reads of the same file. Failures to read a file are similarly cached to prevent multiple\n * failing reads from happening.\n */\n/* eslint-disable complexity */\nasync function addSourceContext(event: Event, contextLines: number): Promise<Event> {\n  // keep a lookup map of which files we've already enqueued to read,\n  // so we don't enqueue the same file multiple times which would cause multiple i/o reads\n  const filesToLines: Record<string, number[]> = {};\n\n  if (contextLines > 0 && event.exception?.values) {\n    for (const exception of event.exception.values) {\n      if (!exception.stacktrace?.frames?.length) {\n        continue;\n      }\n\n      // Maps preserve insertion order, so we iterate in reverse, starting at the\n      // outermost frame and closer to where the exception has occurred (poor mans priority)\n      for (let i = exception.stacktrace.frames.length - 1; i >= 0; i--) {\n        const frame: StackFrame | undefined = exception.stacktrace.frames[i];\n        const filename = frame?.filename;\n\n        if (\n          !frame ||\n          typeof filename !== 'string' ||\n          typeof frame.lineno !== 'number' ||\n          shouldSkipContextLinesForFile(filename) ||\n          shouldSkipContextLinesForFrame(frame)\n        ) {\n          continue;\n        }\n\n        const filesToLinesOutput = filesToLines[filename];\n        if (!filesToLinesOutput) filesToLines[filename] = [];\n        // @ts-expect-error this is defined above\n        filesToLines[filename].push(frame.lineno);\n      }\n    }\n  }\n\n  const files = Object.keys(filesToLines);\n  if (files.length == 0) {\n    return event;\n  }\n\n  const readlinePromises: Promise<void>[] = [];\n  for (const file of files) {\n    // If we failed to read this before, dont try reading it again.\n    if (LRU_FILE_CONTENTS_FS_READ_FAILED.get(file)) {\n      continue;\n    }\n\n    const filesToLineRanges = filesToLines[file];\n    if (!filesToLineRanges) {\n      continue;\n    }\n\n    // Sort ranges so that they are sorted by line increasing order and match how the file is read.\n    filesToLineRanges.sort((a, b) => a - b);\n    // Check if the contents are already in the cache and if we can avoid reading the file again.\n    const ranges = makeLineReaderRanges(filesToLineRanges, contextLines);\n    if (ranges.every(r => rangeExistsInContentCache(file, r))) {\n      continue;\n    }\n\n    const cache = emplace(LRU_FILE_CONTENTS_CACHE, file, {});\n    readlinePromises.push(getContextLinesFromFile(file, ranges, cache));\n  }\n\n  // The promise rejections are caught in order to prevent them from short circuiting Promise.all\n  await Promise.all(readlinePromises).catch(() => {\n    DEBUG_BUILD && debug.log('Failed to read one or more source files and resolve context lines');\n  });\n\n  // Perform the same loop as above, but this time we can assume all files are in the cache\n  // and attempt to add source context to frames.\n  if (contextLines > 0 && event.exception?.values) {\n    for (const exception of event.exception.values) {\n      if (exception.stacktrace?.frames && exception.stacktrace.frames.length > 0) {\n        addSourceContextToFrames(exception.stacktrace.frames, contextLines, LRU_FILE_CONTENTS_CACHE);\n      }\n    }\n  }\n\n  return event;\n}\n/* eslint-enable complexity */\n\n/** Adds context lines to frames */\nfunction addSourceContextToFrames(\n  frames: StackFrame[],\n  contextLines: number,\n  cache: LRUMap<string, Record<number, string>>,\n): void {\n  for (const frame of frames) {\n    // Only add context if we have a filename and it hasn't already been added\n    if (frame.filename && frame.context_line === undefined && typeof frame.lineno === 'number') {\n      const contents = cache.get(frame.filename);\n      if (contents === undefined) {\n        continue;\n      }\n\n      addContextToFrame(frame.lineno, frame, contextLines, contents);\n    }\n  }\n}\n\n/**\n * Clears the context lines from a frame, used to reset a frame to its original state\n * if we fail to resolve all context lines for it.\n */\nfunction clearLineContext(frame: StackFrame): void {\n  delete frame.pre_context;\n  delete frame.context_line;\n  delete frame.post_context;\n}\n\n/**\n * Resolves context lines before and after the given line number and appends them to the frame;\n */\nexport function addContextToFrame(\n  lineno: number,\n  frame: StackFrame,\n  contextLines: number,\n  contents: Record<number, string> | undefined,\n): void {\n  // When there is no line number in the frame, attaching context is nonsensical and will even break grouping.\n  // We already check for lineno before calling this, but since StackFrame lineno ism optional, we check it again.\n  if (frame.lineno === undefined || contents === undefined) {\n    DEBUG_BUILD && debug.error('Cannot resolve context for frame with no lineno or file contents');\n    return;\n  }\n\n  frame.pre_context = [];\n  for (let i = makeRangeStart(lineno, contextLines); i < lineno; i++) {\n    // We always expect the start context as line numbers cannot be negative. If we dont find a line, then\n    // something went wrong somewhere. Clear the context and return without adding any linecontext.\n    const line = contents[i];\n    if (line === undefined) {\n      clearLineContext(frame);\n      DEBUG_BUILD && debug.error(`Could not find line ${i} in file ${frame.filename}`);\n      return;\n    }\n\n    frame.pre_context.push(line);\n  }\n\n  // We should always have the context line. If we dont, something went wrong, so we clear the context and return\n  // without adding any linecontext.\n  if (contents[lineno] === undefined) {\n    clearLineContext(frame);\n    DEBUG_BUILD && debug.error(`Could not find line ${lineno} in file ${frame.filename}`);\n    return;\n  }\n\n  frame.context_line = contents[lineno];\n\n  const end = makeRangeEnd(lineno, contextLines);\n  frame.post_context = [];\n  for (let i = lineno + 1; i <= end; i++) {\n    // Since we dont track when the file ends, we cant clear the context if we dont find a line as it could\n    // just be that we reached the end of the file.\n    const line = contents[i];\n    if (line === undefined) {\n      break;\n    }\n    frame.post_context.push(line);\n  }\n}\n\n// Helper functions for generating line context ranges. They take a line number and the number of lines of context to\n// include before and after the line and generate an inclusive range of indices.\ntype ReadlineRange = [start: number, end: number];\n// Compute inclusive end context range\nfunction makeRangeStart(line: number, linecontext: number): number {\n  return Math.max(1, line - linecontext);\n}\n// Compute inclusive start context range\nfunction makeRangeEnd(line: number, linecontext: number): number {\n  return line + linecontext;\n}\n// Determine start and end indices for context range (inclusive);\nfunction makeContextRange(line: number, linecontext: number): [start: number, end: number] {\n  return [makeRangeStart(line, linecontext), makeRangeEnd(line, linecontext)];\n}\n\n/** Exported only for tests, as a type-safe variant. */\nexport const _contextLinesIntegration = ((options: ContextLinesOptions = {}) => {\n  const contextLines = options.frameContextLines !== undefined ? options.frameContextLines : DEFAULT_LINES_OF_CONTEXT;\n\n  return {\n    name: INTEGRATION_NAME,\n    processEvent(event) {\n      return addSourceContext(event, contextLines);\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Capture the lines before and after the frame's context.\n */\nexport const contextLinesIntegration = defineIntegration(_contextLinesIntegration);\n"], "names": ["LRUMap", "createReadStream", "createInterface", "DEBUG_BUILD", "debug", "snipLine", "defineIntegration"], "mappings": ";;;;;;;AAMA,MAAM,0BAA0B,IAAIA,KAAAA,MAAM,CAAiC,EAAE,CAAC;AAC9E,MAAM,mCAAmC,IAAIA,KAAAA,MAAM,CAAY,EAAE,CAAC;AAClE,MAAM,wBAAA,GAA2B,CAAC;AAClC,MAAM,gBAAA,GAAmB,cAAc;AACvC,+GAAA;AACA,yEAAA;AACA,iCAAA;AACO,MAAM,sBAAsB,GAAW;AACvC,MAAM,uBAAuB,GAAW;AAmB/C;;CAEA,GACA,SAAS,OAAO,CAA8C,GAAG,EAAK,GAAG,EAAK,QAAQ,EAAQ;IAC5F,MAAM,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;IAE1B,IAAI,KAAA,KAAU,SAAS,EAAE;QACvB,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC;QACtB,OAAO,QAAQ;IACnB;IAEE,OAAO,KAAK;AACd;AAEA;;;;;CAKA,GACA,SAAS,6BAA6B,CAAC,IAAI,EAAmB;IAC9D,yEAAA;IACA,0FAAA;IACE,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,OAAO,IAAI;IACzC,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,OAAO,IAAI;IACzC,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,OAAO,IAAI;IAC1C,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,OAAO,IAAI;IAC1C,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,OAAO,IAAI;IACzC,OAAO,KAAK;AACd;AAEA;;CAEA,GACA,SAAS,8BAA8B,CAAC,KAAK,EAAuB;IAClE,IAAI,KAAK,CAAC,MAAA,KAAW,SAAA,IAAa,KAAK,CAAC,MAAA,GAAS,uBAAuB,EAAE,OAAO,IAAI;IACrF,IAAI,KAAK,CAAC,KAAA,KAAU,SAAA,IAAa,KAAK,CAAC,KAAA,GAAQ,sBAAsB,EAAE,OAAO,IAAI;IAClF,OAAO,KAAK;AACd;AACA;;CAEA,GACA,SAAS,yBAAyB,CAAC,IAAI,EAAU,KAAK,EAA0B;IAC9E,MAAM,WAAW,uBAAuB,CAAC,GAAG,CAAC,IAAI,CAAC;IAClD,IAAI,QAAA,KAAa,SAAS,EAAE,OAAO,KAAK;IAExC,IAAK,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,CAAA,IAAK,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAE;QACzC,IAAI,QAAQ,CAAC,CAAC,CAAA,KAAM,SAAS,EAAE;YAC7B,OAAO,KAAK;QAClB;IACA;IAEE,OAAO,IAAI;AACb;AAEA;;;CAGA,GACA,SAAS,oBAAoB,CAAC,KAAK,EAAY,WAAW,EAA2B;IACnF,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;QACjB,OAAO,EAAE;IACb;IAEE,IAAI,CAAA,GAAI,CAAC;IACT,MAAM,IAAA,GAAO,KAAK,CAAC,CAAC,CAAC;IAErB,IAAI,OAAO,IAAA,KAAS,QAAQ,EAAE;QAC5B,OAAO,EAAE;IACb;IAEE,IAAI,UAAU,gBAAgB,CAAC,IAAI,EAAE,WAAW,CAAC;IACjD,MAAM,GAAG,GAAoB,EAAE;IACjC,iDAAA;IACE,MAAO,IAAI,CAAE;QACX,IAAI,CAAA,KAAM,KAAK,CAAC,MAAA,GAAS,CAAC,EAAE;YAC1B,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC;YACjB;QACN;QAEA,mGAAA;QACI,MAAM,OAAO,KAAK,CAAC,CAAA,GAAI,CAAC,CAAC;QACzB,IAAI,OAAO,IAAA,KAAS,QAAQ,EAAE;YAC5B;QACN;QACI,IAAI,IAAA,IAAQ,OAAO,CAAC,CAAC,CAAC,EAAE;YACtB,OAAO,CAAC,CAAC,CAAA,GAAI,IAAA,GAAO,WAAW;QACrC,OAAW;YACL,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC;YACjB,UAAU,gBAAgB,CAAC,IAAI,EAAE,WAAW,CAAC;QACnD;QAEI,CAAC,EAAE;IACP;IAEE,OAAO,GAAG;AACZ;AAEA;;CAEA,GACA,SAAS,uBAAuB,CAAC,IAAI,EAAU,MAAM,EAAmB,MAAM,EAAyC;IACrH,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,OAAO,KAAK;QAC3C,qGAAA;QACA,uCAAA;QACA,8CAAA;QACI,MAAM,MAAA,GAASC,QAAAA,gBAAgB,CAAC,IAAI,CAAC;QACrC,MAAM,UAAA,GAAaC,cAAAA,eAAe,CAAC;YACjC,KAAK,EAAE,MAAM;QACnB,CAAK,CAAC;QAEN,oEAAA;QACA,kEAAA;QACA,kHAAA;QACI,SAAS,uBAAuB,GAAS;YACvC,MAAM,CAAC,OAAO,EAAE;YAChB,OAAO,EAAE;QACf;QAEA,mFAAA;QACI,IAAI,UAAA,GAAa,CAAC;QAClB,IAAI,iBAAA,GAAoB,CAAC;QACzB,MAAM,KAAA,GAAQ,MAAM,CAAC,iBAAiB,CAAC;QACvC,IAAI,KAAA,KAAU,SAAS,EAAE;YAC7B,4GAAA;YACM,uBAAuB,EAAE;YACzB;QACN;QACI,IAAI,UAAA,GAAa,KAAK,CAAC,CAAC,CAAC;QACzB,IAAI,QAAA,GAAW,KAAK,CAAC,CAAC,CAAC;QAE3B,8FAAA;QACA,yDAAA;QACI,SAAS,aAAa,CAAC,CAAC,EAAe;YAC3C,uEAAA;YACM,gCAAgC,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;YAC7CC,WAAAA,WAAA,IAAeC,KAAAA,KAAK,CAAC,KAAK,CAAC,CAAC,qBAAqB,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAA,CAAA;YACA,UAAA,CAAA,KAAA,EAAA;YACA,UAAA,CAAA,kBAAA,EAAA;YACA,uBAAA,EAAA;QACA;QAEA,sFAAA;QACA,4CAAA;QACA,MAAA,CAAA,EAAA,CAAA,OAAA,EAAA,aAAA,CAAA;QACA,UAAA,CAAA,EAAA,CAAA,OAAA,EAAA,aAAA,CAAA;QACA,UAAA,CAAA,EAAA,CAAA,OAAA,EAAA,uBAAA,CAAA;QAEA,UAAA,CAAA,EAAA,CAAA,MAAA,GAAA,IAAA,IAAA;YACA,UAAA,EAAA;YACA,IAAA,UAAA,GAAA,UAAA,EAAA;YAEA,+EAAA;YACA,MAAA,CAAA,UAAA,CAAA,GAAAC,KAAAA,QAAA,CAAA,IAAA,EAAA,CAAA,CAAA;YAEA,IAAA,UAAA,IAAA,QAAA,EAAA;gBACA,IAAA,iBAAA,KAAA,MAAA,CAAA,MAAA,GAAA,CAAA,EAAA;oBACA,4GAAA;oBACA,UAAA,CAAA,KAAA,EAAA;oBACA,UAAA,CAAA,kBAAA,EAAA;oBACA;gBACA;gBACA,iBAAA,EAAA;gBACA,MAAA,KAAA,GAAA,MAAA,CAAA,iBAAA,CAAA;gBACA,IAAA,KAAA,KAAA,SAAA,EAAA;oBACA,qEAAA;oBACA,UAAA,CAAA,KAAA,EAAA;oBACA,UAAA,CAAA,kBAAA,EAAA;oBACA;gBACA;gBACA,UAAA,GAAA,KAAA,CAAA,CAAA,CAAA;gBACA,QAAA,GAAA,KAAA,CAAA,CAAA,CAAA;YACA;QACA,CAAA,CAAA;IACA,CAAA,CAAA;AACA;AAEA;;;;;CAKA,GACA,6BAAA,GACA,eAAA,gBAAA,CAAA,KAAA,EAAA,YAAA,EAAA;IACA,mEAAA;IACA,wFAAA;IACA,MAAA,YAAA,GAAA,CAAA,CAAA;IAEA,IAAA,YAAA,GAAA,CAAA,IAAA,KAAA,CAAA,SAAA,EAAA,MAAA,EAAA;QACA,KAAA,MAAA,SAAA,IAAA,KAAA,CAAA,SAAA,CAAA,MAAA,CAAA;YACA,IAAA,CAAA,SAAA,CAAA,UAAA,EAAA,MAAA,EAAA,MAAA,EAAA;gBACA;YACA;YAEA,2EAAA;YACA,sFAAA;YACA,IAAA,IAAA,CAAA,GAAA,SAAA,CAAA,UAAA,CAAA,MAAA,CAAA,MAAA,GAAA,CAAA,EAAA,CAAA,IAAA,CAAA,EAAA,CAAA,EAAA,CAAA;gBACA,MAAA,KAAA,GAAA,SAAA,CAAA,UAAA,CAAA,MAAA,CAAA,CAAA,CAAA;gBACA,MAAA,QAAA,GAAA,KAAA,EAAA,QAAA;gBAEA,IACA,CAAA,KAAA,IACA,OAAA,QAAA,KAAA,QAAA,IACA,OAAA,KAAA,CAAA,MAAA,KAAA,QAAA,IACA,6BAAA,CAAA,QAAA,CAAA,IACA,8BAAA,CAAA,KAAA,GACA;oBACA;gBACA;gBAEA,MAAA,kBAAA,GAAA,YAAA,CAAA,QAAA,CAAA;gBACA,IAAA,CAAA,kBAAA,EAAA,YAAA,CAAA,QAAA,CAAA,GAAA,EAAA;gBACA,yCAAA;gBACA,YAAA,CAAA,QAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,MAAA,CAAA;YACA;QACA;IACA;IAEA,MAAA,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,YAAA,CAAA;IACA,IAAA,KAAA,CAAA,MAAA,IAAA,CAAA,EAAA;QACA,OAAA,KAAA;IACA;IAEA,MAAA,gBAAA,GAAA,EAAA;IACA,KAAA,MAAA,IAAA,IAAA,KAAA,CAAA;QACA,+DAAA;QACA,IAAA,gCAAA,CAAA,GAAA,CAAA,IAAA,CAAA,EAAA;YACA;QACA;QAEA,MAAA,iBAAA,GAAA,YAAA,CAAA,IAAA,CAAA;QACA,IAAA,CAAA,iBAAA,EAAA;YACA;QACA;QAEA,+FAAA;QACA,iBAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA,CAAA,GAAA,CAAA,GAAA,CAAA,CAAA;QACA,6FAAA;QACA,MAAA,MAAA,GAAA,oBAAA,CAAA,iBAAA,EAAA,YAAA,CAAA;QACA,IAAA,MAAA,CAAA,KAAA,EAAA,CAAA,GAAA,yBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,CAAA,EAAA;YACA;QACA;QAEA,MAAA,KAAA,GAAA,OAAA,CAAA,uBAAA,EAAA,IAAA,EAAA,CAAA,CAAA,CAAA;QACA,gBAAA,CAAA,IAAA,CAAA,uBAAA,CAAA,IAAA,EAAA,MAAA,EAAA,KAAA,CAAA,CAAA;IACA;IAEA,+FAAA;IACA,MAAA,OAAA,CAAA,GAAA,CAAA,gBAAA,CAAA,CAAA,KAAA,CAAA,MAAA;QACAF,WAAAA,WAAA,IAAAC,KAAAA,KAAA,CAAA,GAAA,CAAA,mEAAA,CAAA;IACA,CAAA,CAAA;IAEA,yFAAA;IACA,+CAAA;IACA,IAAA,YAAA,GAAA,CAAA,IAAA,KAAA,CAAA,SAAA,EAAA,MAAA,EAAA;QACA,KAAA,MAAA,SAAA,IAAA,KAAA,CAAA,SAAA,CAAA,MAAA,CAAA;YACA,IAAA,SAAA,CAAA,UAAA,EAAA,MAAA,IAAA,SAAA,CAAA,UAAA,CAAA,MAAA,CAAA,MAAA,GAAA,CAAA,EAAA;gBACA,wBAAA,CAAA,SAAA,CAAA,UAAA,CAAA,MAAA,EAAA,YAAA,EAAA,uBAAA,CAAA;YACA;QACA;IACA;IAEA,OAAA,KAAA;AACA;AACA,4BAAA,GAEA,iCAAA,GACA,SAAA,wBAAA,CACA,MAAA,EACA,YAAA,EACA,KAAA;IAEA,KAAA,MAAA,KAAA,IAAA,MAAA,CAAA;QACA,0EAAA;QACA,IAAA,KAAA,CAAA,QAAA,IAAA,KAAA,CAAA,YAAA,KAAA,SAAA,IAAA,OAAA,KAAA,CAAA,MAAA,KAAA,QAAA,EAAA;YACA,MAAA,QAAA,GAAA,KAAA,CAAA,GAAA,CAAA,KAAA,CAAA,QAAA,CAAA;YACA,IAAA,QAAA,KAAA,SAAA,EAAA;gBACA;YACA;YAEA,iBAAA,CAAA,KAAA,CAAA,MAAA,EAAA,KAAA,EAAA,YAAA,EAAA,QAAA,CAAA;QACA;IACA;AACA;AAEA;;;CAGA,GACA,SAAA,gBAAA,CAAA,KAAA,EAAA;IACA,OAAA,KAAA,CAAA,WAAA;IACA,OAAA,KAAA,CAAA,YAAA;IACA,OAAA,KAAA,CAAA,YAAA;AACA;AAEA;;CAEA,GACA,SAAA,iBAAA,CACA,MAAA,EACA,KAAA,EACA,YAAA,EACA,QAAA;IAEA,4GAAA;IACA,gHAAA;IACA,IAAA,KAAA,CAAA,MAAA,KAAA,SAAA,IAAA,QAAA,KAAA,SAAA,EAAA;QACAD,WAAAA,WAAA,IAAAC,KAAAA,KAAA,CAAA,KAAA,CAAA,kEAAA,CAAA;QACA;IACA;IAEA,KAAA,CAAA,WAAA,GAAA,EAAA;IACA,IAAA,IAAA,CAAA,GAAA,cAAA,CAAA,MAAA,EAAA,YAAA,CAAA,EAAA,CAAA,GAAA,MAAA,EAAA,CAAA,EAAA,CAAA;QACA,sGAAA;QACA,+FAAA;QACA,MAAA,IAAA,GAAA,QAAA,CAAA,CAAA,CAAA;QACA,IAAA,IAAA,KAAA,SAAA,EAAA;YACA,gBAAA,CAAA,KAAA,CAAA;YACAD,WAAAA,WAAA,IAAAC,KAAAA,KAAA,CAAA,KAAA,CAAA,CAAA,oBAAA,EAAA,CAAA,CAAA,SAAA,EAAA,KAAA,CAAA,QAAA,CAAA,CAAA,CAAA;YACA;QACA;QAEA,KAAA,CAAA,WAAA,CAAA,IAAA,CAAA,IAAA,CAAA;IACA;IAEA,+GAAA;IACA,kCAAA;IACA,IAAA,QAAA,CAAA,MAAA,CAAA,KAAA,SAAA,EAAA;QACA,gBAAA,CAAA,KAAA,CAAA;QACAD,WAAAA,WAAA,IAAAC,KAAAA,KAAA,CAAA,KAAA,CAAA,CAAA,oBAAA,EAAA,MAAA,CAAA,SAAA,EAAA,KAAA,CAAA,QAAA,CAAA,CAAA,CAAA;QACA;IACA;IAEA,KAAA,CAAA,YAAA,GAAA,QAAA,CAAA,MAAA,CAAA;IAEA,MAAA,GAAA,GAAA,YAAA,CAAA,MAAA,EAAA,YAAA,CAAA;IACA,KAAA,CAAA,YAAA,GAAA,EAAA;IACA,IAAA,IAAA,CAAA,GAAA,MAAA,GAAA,CAAA,EAAA,CAAA,IAAA,GAAA,EAAA,CAAA,EAAA,CAAA;QACA,uGAAA;QACA,+CAAA;QACA,MAAA,IAAA,GAAA,QAAA,CAAA,CAAA,CAAA;QACA,IAAA,IAAA,KAAA,SAAA,EAAA;YACA;QACA;QACA,KAAA,CAAA,YAAA,CAAA,IAAA,CAAA,IAAA,CAAA;IACA;AACA;AAEA,qHAAA;AACA,gFAAA;AAEA,sCAAA;AACA,SAAA,cAAA,CAAA,IAAA,EAAA,WAAA,EAAA;IACA,OAAA,IAAA,CAAA,GAAA,CAAA,CAAA,EAAA,IAAA,GAAA,WAAA,CAAA;AACA;AACA,wCAAA;AACA,SAAA,YAAA,CAAA,IAAA,EAAA,WAAA,EAAA;IACA,OAAA,IAAA,GAAA,WAAA;AACA;AACA,iEAAA;AACA,SAAA,gBAAA,CAAA,IAAA,EAAA,WAAA,EAAA;IACA,OAAA;QAAA,cAAA,CAAA,IAAA,EAAA,WAAA,CAAA;QAAA,YAAA,CAAA,IAAA,EAAA,WAAA,CAAA;KAAA;AACA;AAEA,qDAAA,GACA,MAAA,wBAAA,GAAA,CAAA,OAAA,GAAA,CAAA,CAAA,KAAA;IACA,MAAA,YAAA,GAAA,OAAA,CAAA,iBAAA,KAAA,SAAA,GAAA,OAAA,CAAA,iBAAA,GAAA,wBAAA;IAEA,OAAA;QACA,IAAA,EAAA,gBAAA;QACA,YAAA,EAAA,KAAA,EAAA;YACA,OAAA,gBAAA,CAAA,KAAA,EAAA,YAAA,CAAA;QACA,CAAA;IACA,CAAA;AACA,CAAA,CAAA;AAEA;;CAEA,GACA,MAAA,uBAAA,GAAAE,KAAAA,iBAAA,CAAA,wBAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1961, "column": 0}, "map": {"version": 3, "file": "debug.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/utils/debug.ts"], "sourcesContent": ["let cachedDebuggerEnabled: boolean | undefined;\n\n/**\n * Was the debugger enabled when this function was first called?\n */\nexport async function isDebuggerEnabled(): Promise<boolean> {\n  if (cachedDebuggerEnabled === undefined) {\n    try {\n      // Node can be built without inspector support\n      const inspector = await import('node:inspector');\n      cachedDebuggerEnabled = !!inspector.url();\n    } catch {\n      cachedDebuggerEnabled = false;\n    }\n  }\n\n  return cachedDebuggerEnabled;\n}\n"], "names": [], "mappings": ";;;AAAA,IAAI,qBAAqB;AAEzB;;CAEA,GACO,eAAe,iBAAiB,GAAqB;IAC1D,IAAI,qBAAA,KAA0B,SAAS,EAAE;QACvC,IAAI;YACR,8CAAA;YACM,MAAM,SAAA,GAAY,MAAM,OAAO,gBAAgB,CAAC;YAChD,qBAAA,GAAwB,CAAC,CAAC,SAAS,CAAC,GAAG,EAAE;QAC/C,EAAM,OAAM;YACN,qBAAA,GAAwB,KAAK;QACnC;IACA;IAEE,OAAO,qBAAqB;AAC9B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1985, "column": 0}, "map": {"version": 3, "file": "common.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/integrations/local-variables/common.ts"], "sourcesContent": ["import type { Debugger } from 'node:inspector';\n\nexport type Variables = Record<string, unknown>;\n\nexport type RateLimitIncrement = () => void;\n\n/**\n * The key used to store the local variables on the error object.\n */\nexport const LOCAL_VARIABLES_KEY = '__SENTRY_ERROR_LOCAL_VARIABLES__';\n\n/**\n * Creates a rate limiter that will call the disable callback when the rate limit is reached and the enable callback\n * when a timeout has occurred.\n * @param maxPerSecond Maximum number of calls per second\n * @param enable Callback to enable capture\n * @param disable Callback to disable capture\n * @returns A function to call to increment the rate limiter count\n */\nexport function createRateLimiter(\n  maxPerSecond: number,\n  enable: () => void,\n  disable: (seconds: number) => void,\n): RateLimitIncrement {\n  let count = 0;\n  let retrySeconds = 5;\n  let disabledTimeout = 0;\n\n  setInterval(() => {\n    if (disabledTimeout === 0) {\n      if (count > maxPerSecond) {\n        retrySeconds *= 2;\n        disable(retrySeconds);\n\n        // Cap at one day\n        if (retrySeconds > 86400) {\n          retrySeconds = 86400;\n        }\n        disabledTimeout = retrySeconds;\n      }\n    } else {\n      disabledTimeout -= 1;\n\n      if (disabledTimeout === 0) {\n        enable();\n      }\n    }\n\n    count = 0;\n  }, 1_000).unref();\n\n  return () => {\n    count += 1;\n  };\n}\n\n// Add types for the exception event data\nexport type PausedExceptionEvent = Debugger.PausedEventDataType & {\n  data: {\n    // This contains error.stack\n    description: string;\n    objectId?: string;\n  };\n};\n\n/** Could this be an anonymous function? */\nexport function isAnonymous(name: string | undefined): boolean {\n  return name !== undefined && (name.length === 0 || name === '?' || name === '<anonymous>');\n}\n\n/** Do the function names appear to match? */\nexport function functionNamesMatch(a: string | undefined, b: string | undefined): boolean {\n  return a === b || `Object.${a}` === b || a === `Object.${b}` || (isAnonymous(a) && isAnonymous(b));\n}\n\nexport interface FrameVariables {\n  function: string;\n  vars?: Variables;\n}\n\nexport interface LocalVariablesIntegrationOptions {\n  /**\n   * Capture local variables for both caught and uncaught exceptions\n   *\n   * - When false, only uncaught exceptions will have local variables\n   * - When true, both caught and uncaught exceptions will have local variables.\n   *\n   * Defaults to `true`.\n   *\n   * Capturing local variables for all exceptions can be expensive since the debugger pauses for every throw to collect\n   * local variables.\n   *\n   * To reduce the likelihood of this feature impacting app performance or throughput, this feature is rate-limited.\n   * Once the rate limit is reached, local variables will only be captured for uncaught exceptions until a timeout has\n   * been reached.\n   */\n  captureAllExceptions?: boolean;\n  /**\n   * Maximum number of exceptions to capture local variables for per second before rate limiting is triggered.\n   */\n  maxExceptionsPerSecond?: number;\n}\n\nexport interface LocalVariablesWorkerArgs extends LocalVariablesIntegrationOptions {\n  /**\n   * Whether to enable debug logging.\n   */\n  debug: boolean;\n  /**\n   * Base path used to calculate module name.\n   *\n   * Defaults to `dirname(process.argv[1])` and falls back to `process.cwd()`\n   */\n  basePath?: string;\n}\n"], "names": [], "mappings": ";;;AAMA;;CAEA,GACO,MAAM,mBAAA,GAAsB;AAEnC;;;;;;;CAOA,GACO,SAAS,iBAAiB,CAC/B,YAAY,EACZ,MAAM,EACN,OAAO;IAEP,IAAI,KAAA,GAAQ,CAAC;IACb,IAAI,YAAA,GAAe,CAAC;IACpB,IAAI,eAAA,GAAkB,CAAC;IAEvB,WAAW,CAAC,MAAM;QAChB,IAAI,eAAA,KAAoB,CAAC,EAAE;YACzB,IAAI,KAAA,GAAQ,YAAY,EAAE;gBACxB,YAAA,IAAgB,CAAC;gBACjB,OAAO,CAAC,YAAY,CAAC;gBAE7B,iBAAA;gBACQ,IAAI,YAAA,GAAe,KAAK,EAAE;oBACxB,YAAA,GAAe,KAAK;gBAC9B;gBACQ,eAAA,GAAkB,YAAY;YACtC;QACA,OAAW;YACL,eAAA,IAAmB,CAAC;YAEpB,IAAI,eAAA,KAAoB,CAAC,EAAE;gBACzB,MAAM,EAAE;YAChB;QACA;QAEI,KAAA,GAAQ,CAAC;IACb,CAAG,EAAE,IAAK,CAAC,CAAC,KAAK,EAAE;IAEjB,OAAO,MAAM;QACX,KAAA,IAAS,CAAC;IACd,CAAG;AACH;AAEA,yCAAA;AASA,yCAAA,GACO,SAAS,WAAW,CAAC,IAAI,EAA+B;IAC7D,OAAO,IAAA,KAAS,aAAA,CAAc,IAAI,CAAC,MAAA,KAAW,CAAA,IAAK,SAAS,GAAA,IAAO,IAAA,KAAS,aAAa,CAAC;AAC5F;AAEA,2CAAA,GACO,SAAS,kBAAkB,CAAC,CAAC,EAAsB,CAAC,EAA+B;IACxF,OAAO,CAAA,KAAM,CAAA,IAAK,CAAC,OAAO,EAAE,CAAC,CAAC,CAAA,KAAA,CAAA,IAAA,CAAA,KAAA,CAAA,OAAA,EAAA,CAAA,CAAA,CAAA,IAAA,WAAA,CAAA,CAAA,CAAA,IAAA,WAAA,CAAA,CAAA,CAAA,CAAA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2041, "column": 0}, "map": {"version": 3, "file": "local-variables-async.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/integrations/local-variables/local-variables-async.ts"], "sourcesContent": ["import { Worker } from 'node:worker_threads';\nimport type { Event, EventHint, Exception, IntegrationFn } from '@sentry/core';\nimport { debug, defineIntegration } from '@sentry/core';\nimport type { NodeClient } from '../../sdk/client';\nimport { isDebuggerEnabled } from '../../utils/debug';\nimport type { FrameVariables, LocalVariablesIntegrationOptions, LocalVariablesWorkerArgs } from './common';\nimport { functionNamesMatch, LOCAL_VARIABLES_KEY } from './common';\n\n// This string is a placeholder that gets overwritten with the worker code.\nexport const base64WorkerScript = '###LocalVariablesWorkerScript###';\n\nfunction log(...args: unknown[]): void {\n  debug.log('[LocalVariables]', ...args);\n}\n\n/**\n * Adds local variables to exception frames\n */\nexport const localVariablesAsyncIntegration = defineIntegration(((\n  integrationOptions: LocalVariablesIntegrationOptions = {},\n) => {\n  function addLocalVariablesToException(exception: Exception, localVariables: FrameVariables[]): void {\n    // Filter out frames where the function name is `new Promise` since these are in the error.stack frames\n    // but do not appear in the debugger call frames\n    const frames = (exception.stacktrace?.frames || []).filter(frame => frame.function !== 'new Promise');\n\n    for (let i = 0; i < frames.length; i++) {\n      // Sentry frames are in reverse order\n      const frameIndex = frames.length - i - 1;\n\n      const frameLocalVariables = localVariables[i];\n      const frame = frames[frameIndex];\n\n      if (!frame || !frameLocalVariables) {\n        // Drop out if we run out of frames to match up\n        break;\n      }\n\n      if (\n        // We need to have vars to add\n        frameLocalVariables.vars === undefined ||\n        // We're not interested in frames that are not in_app because the vars are not relevant\n        frame.in_app === false ||\n        // The function names need to match\n        !functionNamesMatch(frame.function, frameLocalVariables.function)\n      ) {\n        continue;\n      }\n\n      frame.vars = frameLocalVariables.vars;\n    }\n  }\n\n  function addLocalVariablesToEvent(event: Event, hint: EventHint): Event {\n    if (\n      hint.originalException &&\n      typeof hint.originalException === 'object' &&\n      LOCAL_VARIABLES_KEY in hint.originalException &&\n      Array.isArray(hint.originalException[LOCAL_VARIABLES_KEY])\n    ) {\n      for (const exception of event.exception?.values || []) {\n        addLocalVariablesToException(exception, hint.originalException[LOCAL_VARIABLES_KEY]);\n      }\n\n      hint.originalException[LOCAL_VARIABLES_KEY] = undefined;\n    }\n\n    return event;\n  }\n\n  async function startInspector(): Promise<void> {\n    // We load inspector dynamically because on some platforms Node is built without inspector support\n    const inspector = await import('node:inspector');\n    if (!inspector.url()) {\n      inspector.open(0);\n    }\n  }\n\n  function startWorker(options: LocalVariablesWorkerArgs): void {\n    const worker = new Worker(new URL(`data:application/javascript;base64,${base64WorkerScript}`), {\n      workerData: options,\n      // We don't want any Node args to be passed to the worker\n      execArgv: [],\n      env: { ...process.env, NODE_OPTIONS: undefined },\n    });\n\n    process.on('exit', () => {\n      // eslint-disable-next-line @typescript-eslint/no-floating-promises\n      worker.terminate();\n    });\n\n    worker.once('error', (err: Error) => {\n      log('Worker error', err);\n    });\n\n    worker.once('exit', (code: number) => {\n      log('Worker exit', code);\n    });\n\n    // Ensure this thread can't block app exit\n    worker.unref();\n  }\n\n  return {\n    name: 'LocalVariablesAsync',\n    async setup(client: NodeClient) {\n      const clientOptions = client.getOptions();\n\n      if (!clientOptions.includeLocalVariables) {\n        return;\n      }\n\n      if (await isDebuggerEnabled()) {\n        debug.warn('Local variables capture has been disabled because the debugger was already enabled');\n        return;\n      }\n\n      const options: LocalVariablesWorkerArgs = {\n        ...integrationOptions,\n        debug: debug.isEnabled(),\n      };\n\n      startInspector().then(\n        () => {\n          try {\n            startWorker(options);\n          } catch (e) {\n            debug.error('Failed to start worker', e);\n          }\n        },\n        e => {\n          debug.error('Failed to start inspector', e);\n        },\n      );\n    },\n    processEvent(event: Event, hint: EventHint): Event {\n      return addLocalVariablesToEvent(event, hint);\n    },\n  };\n}) satisfies IntegrationFn);\n"], "names": ["debug", "defineIntegration", "functionNamesMatch", "LOCAL_VARIABLES_KEY", "Worker", "isDebuggerEnabled"], "mappings": ";;;;;;;AAQA,2EAAA;AACO,MAAM,kBAAA,GAAqB;AAElC,SAAS,GAAG,CAAC,GAAG,IAAI,EAAmB;IACrCA,KAAAA,KAAK,CAAC,GAAG,CAAC,kBAAkB,EAAE,GAAG,IAAI,CAAC;AACxC;AAEA;;CAEA,GACO,MAAM,8BAAA,GAAiCC,KAAAA,iBAAiB,CAAE,CAC/D,kBAAkB,GAAqC,CAAA,CAAE;IAEzD,SAAS,4BAA4B,CAAC,SAAS,EAAa,cAAc,EAA0B;QACtG,uGAAA;QACA,gDAAA;QACI,MAAM,SAAS,CAAC,SAAS,CAAC,UAAU,EAAE,MAAA,IAAU,EAAE,EAAE,MAAM,EAAC,KAAA,GAAS,KAAK,CAAC,QAAA,KAAa,aAAa,CAAC;QAErG,IAAK,IAAI,CAAA,GAAI,CAAC,EAAE,CAAA,GAAI,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE;YAC5C,qCAAA;YACM,MAAM,aAAa,MAAM,CAAC,MAAA,GAAS,CAAA,GAAI,CAAC;YAExC,MAAM,mBAAA,GAAsB,cAAc,CAAC,CAAC,CAAC;YAC7C,MAAM,KAAA,GAAQ,MAAM,CAAC,UAAU,CAAC;YAEhC,IAAI,CAAC,SAAS,CAAC,mBAAmB,EAAE;gBAElC;YACR;YAEM,IACN,8BAAA;YACQ,mBAAmB,CAAC,IAAA,KAAS,SAAA,IACrC,uFAAA;YACQ,KAAK,CAAC,MAAA,KAAW,KAAA,IACzB,mCAAA;YACQ,CAACC,OAAAA,kBAAkB,CAAC,KAAK,CAAC,QAAQ,EAAE,mBAAmB,CAAC,QAAQ,GAChE;gBACA;YACR;YAEM,KAAK,CAAC,IAAA,GAAO,mBAAmB,CAAC,IAAI;QAC3C;IACA;IAEE,SAAS,wBAAwB,CAAC,KAAK,EAAS,IAAI,EAAoB;QACtE,IACE,IAAI,CAAC,iBAAA,IACL,OAAO,IAAI,CAAC,iBAAA,KAAsB,QAAA,IAClCC,OAAAA,mBAAA,IAAuB,IAAI,CAAC,iBAAA,IAC5B,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAACA,OAAAA,mBAAmB,CAAC,GACzD;YACA,KAAK,MAAM,SAAA,IAAa,KAAK,CAAC,SAAS,EAAE,MAAA,IAAU,EAAE,CAAE;gBACrD,4BAA4B,CAAC,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAACA,OAAAA,mBAAmB,CAAC,CAAC;YAC5F;YAEM,IAAI,CAAC,iBAAiB,CAACA,OAAAA,mBAAmB,CAAA,GAAI,SAAS;QAC7D;QAEI,OAAO,KAAK;IAChB;IAEE,eAAe,cAAc,GAAkB;QACjD,kGAAA;QACI,MAAM,SAAA,GAAY,MAAM,OAAO,gBAAgB,CAAC;QAChD,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,EAAE;YACpB,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;QACvB;IACA;IAEE,SAAS,WAAW,CAAC,OAAO,EAAkC;QAC5D,MAAM,MAAA,GAAS,IAAIC,oBAAAA,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC,mCAAmC,EAAE,kBAAkB,CAAC,CAAA,CAAA,EAAA;YACA,UAAA,EAAA,OAAA;YACA,yDAAA;YACA,QAAA,EAAA,EAAA;YACA,GAAA,EAAA;gBAAA,GAAA,OAAA,CAAA,GAAA;gBAAA,YAAA,EAAA,SAAA;YAAA,CAAA;QACA,CAAA,CAAA;QAEA,OAAA,CAAA,EAAA,CAAA,MAAA,EAAA,MAAA;YACA,mEAAA;YACA,MAAA,CAAA,SAAA,EAAA;QACA,CAAA,CAAA;QAEA,MAAA,CAAA,IAAA,CAAA,OAAA,EAAA,CAAA,GAAA,KAAA;YACA,GAAA,CAAA,cAAA,EAAA,GAAA,CAAA;QACA,CAAA,CAAA;QAEA,MAAA,CAAA,IAAA,CAAA,MAAA,EAAA,CAAA,IAAA,KAAA;YACA,GAAA,CAAA,aAAA,EAAA,IAAA,CAAA;QACA,CAAA,CAAA;QAEA,0CAAA;QACA,MAAA,CAAA,KAAA,EAAA;IACA;IAEA,OAAA;QACA,IAAA,EAAA,qBAAA;QACA,MAAA,KAAA,EAAA,MAAA,EAAA;YACA,MAAA,aAAA,GAAA,MAAA,CAAA,UAAA,EAAA;YAEA,IAAA,CAAA,aAAA,CAAA,qBAAA,EAAA;gBACA;YACA;YAEA,IAAA,MAAAC,MAAAA,iBAAA,EAAA,EAAA;gBACAL,KAAAA,KAAA,CAAA,IAAA,CAAA,oFAAA,CAAA;gBACA;YACA;YAEA,MAAA,OAAA,GAAA;gBACA,GAAA,kBAAA;gBACA,KAAA,EAAAA,KAAAA,KAAA,CAAA,SAAA,EAAA;YACA,CAAA;YAEA,cAAA,EAAA,CAAA,IAAA,CACA,MAAA;gBACA,IAAA;oBACA,WAAA,CAAA,OAAA,CAAA;gBACA,CAAA,CAAA,OAAA,CAAA,EAAA;oBACAA,KAAAA,KAAA,CAAA,KAAA,CAAA,wBAAA,EAAA,CAAA,CAAA;gBACA;YACA,CAAA,GACA,CAAA,IAAA;gBACAA,KAAAA,KAAA,CAAA,KAAA,CAAA,2BAAA,EAAA,CAAA,CAAA;YACA,CAAA;QAEA,CAAA;QACA,YAAA,EAAA,KAAA,EAAA,IAAA,EAAA;YACA,OAAA,wBAAA,CAAA,KAAA,EAAA,IAAA,CAAA;QACA,CAAA;IACA,CAAA;AACA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2153, "column": 0}, "map": {"version": 3, "file": "local-variables-sync.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/integrations/local-variables/local-variables-sync.ts"], "sourcesContent": ["import type { Debugger, InspectorNotification, Runtime, Session } from 'node:inspector';\nimport type { Event, Exception, IntegrationFn, StackFrame, StackParser } from '@sentry/core';\nimport { debug, defineIntegration, getClient, LRUMap } from '@sentry/core';\nimport { NODE_MAJOR } from '../../nodeVersion';\nimport type { NodeClient } from '../../sdk/client';\nimport { isDebuggerEnabled } from '../../utils/debug';\nimport type {\n  FrameVariables,\n  LocalVariablesIntegrationOptions,\n  PausedExceptionEvent,\n  RateLimitIncrement,\n  Variables,\n} from './common';\nimport { createRateLimiter, functionNamesMatch } from './common';\n\n/** Creates a unique hash from stack frames */\nexport function hashFrames(frames: StackFrame[] | undefined): string | undefined {\n  if (frames === undefined) {\n    return;\n  }\n\n  // Only hash the 10 most recent frames (ie. the last 10)\n  return frames.slice(-10).reduce((acc, frame) => `${acc},${frame.function},${frame.lineno},${frame.colno}`, '');\n}\n\n/**\n * We use the stack parser to create a unique hash from the exception stack trace\n * This is used to lookup vars when the exception passes through the event processor\n */\nexport function hashFromStack(stackParser: StackParser, stack: string | undefined): string | undefined {\n  if (stack === undefined) {\n    return undefined;\n  }\n\n  return hashFrames(stackParser(stack, 1));\n}\n\ntype OnPauseEvent = InspectorNotification<Debugger.PausedEventDataType>;\nexport interface DebugSession {\n  /** Configures and connects to the debug session */\n  configureAndConnect(onPause: (message: OnPauseEvent, complete: () => void) => void, captureAll: boolean): void;\n  /** Updates which kind of exceptions to capture */\n  setPauseOnExceptions(captureAll: boolean): void;\n  /** Gets local variables for an objectId */\n  getLocalVariables(objectId: string, callback: (vars: Variables) => void): void;\n}\n\ntype Next<T> = (result: T) => void;\ntype Add<T> = (fn: Next<T>) => void;\ntype CallbackWrapper<T> = { add: Add<T>; next: Next<T> };\n\n/** Creates a container for callbacks to be called sequentially */\nexport function createCallbackList<T>(complete: Next<T>): CallbackWrapper<T> {\n  // A collection of callbacks to be executed last to first\n  let callbacks: Next<T>[] = [];\n\n  let completedCalled = false;\n  function checkedComplete(result: T): void {\n    callbacks = [];\n    if (completedCalled) {\n      return;\n    }\n    completedCalled = true;\n    complete(result);\n  }\n\n  // complete should be called last\n  callbacks.push(checkedComplete);\n\n  function add(fn: Next<T>): void {\n    callbacks.push(fn);\n  }\n\n  function next(result: T): void {\n    const popped = callbacks.pop() || checkedComplete;\n\n    try {\n      popped(result);\n    } catch {\n      // If there is an error, we still want to call the complete callback\n      checkedComplete(result);\n    }\n  }\n\n  return { add, next };\n}\n\n/**\n * Promise API is available as `Experimental` and in Node 19 only.\n *\n * Callback-based API is `Stable` since v14 and `Experimental` since v8.\n * Because of that, we are creating our own `AsyncSession` class.\n *\n * https://nodejs.org/docs/latest-v19.x/api/inspector.html#promises-api\n * https://nodejs.org/docs/latest-v14.x/api/inspector.html\n */\nclass AsyncSession implements DebugSession {\n  /** Throws if inspector API is not available */\n  private constructor(private readonly _session: Session) {\n    //\n  }\n\n  public static async create(orDefault?: DebugSession | undefined): Promise<DebugSession> {\n    if (orDefault) {\n      return orDefault;\n    }\n\n    const inspector = await import('node:inspector');\n    return new AsyncSession(new inspector.Session());\n  }\n\n  /** @inheritdoc */\n  public configureAndConnect(onPause: (event: OnPauseEvent, complete: () => void) => void, captureAll: boolean): void {\n    this._session.connect();\n\n    this._session.on('Debugger.paused', event => {\n      onPause(event, () => {\n        // After the pause work is complete, resume execution or the exception context memory is leaked\n        this._session.post('Debugger.resume');\n      });\n    });\n\n    this._session.post('Debugger.enable');\n    this._session.post('Debugger.setPauseOnExceptions', { state: captureAll ? 'all' : 'uncaught' });\n  }\n\n  public setPauseOnExceptions(captureAll: boolean): void {\n    this._session.post('Debugger.setPauseOnExceptions', { state: captureAll ? 'all' : 'uncaught' });\n  }\n\n  /** @inheritdoc */\n  public getLocalVariables(objectId: string, complete: (vars: Variables) => void): void {\n    this._getProperties(objectId, props => {\n      const { add, next } = createCallbackList<Variables>(complete);\n\n      for (const prop of props) {\n        if (prop.value?.objectId && prop.value.className === 'Array') {\n          const id = prop.value.objectId;\n          add(vars => this._unrollArray(id, prop.name, vars, next));\n        } else if (prop.value?.objectId && prop.value.className === 'Object') {\n          const id = prop.value.objectId;\n          add(vars => this._unrollObject(id, prop.name, vars, next));\n        } else if (prop.value) {\n          add(vars => this._unrollOther(prop, vars, next));\n        }\n      }\n\n      next({});\n    });\n  }\n\n  /**\n   * Gets all the PropertyDescriptors of an object\n   */\n  private _getProperties(objectId: string, next: (result: Runtime.PropertyDescriptor[]) => void): void {\n    this._session.post(\n      'Runtime.getProperties',\n      {\n        objectId,\n        ownProperties: true,\n      },\n      (err, params) => {\n        if (err) {\n          next([]);\n        } else {\n          next(params.result);\n        }\n      },\n    );\n  }\n\n  /**\n   * Unrolls an array property\n   */\n  private _unrollArray(objectId: string, name: string, vars: Variables, next: (vars: Variables) => void): void {\n    this._getProperties(objectId, props => {\n      vars[name] = props\n        .filter(v => v.name !== 'length' && !isNaN(parseInt(v.name, 10)))\n        .sort((a, b) => parseInt(a.name, 10) - parseInt(b.name, 10))\n        .map(v => v.value?.value);\n\n      next(vars);\n    });\n  }\n\n  /**\n   * Unrolls an object property\n   */\n  private _unrollObject(objectId: string, name: string, vars: Variables, next: (obj: Variables) => void): void {\n    this._getProperties(objectId, props => {\n      vars[name] = props\n        .map<[string, unknown]>(v => [v.name, v.value?.value])\n        .reduce((obj, [key, val]) => {\n          obj[key] = val;\n          return obj;\n        }, {} as Variables);\n\n      next(vars);\n    });\n  }\n\n  /**\n   * Unrolls other properties\n   */\n  private _unrollOther(prop: Runtime.PropertyDescriptor, vars: Variables, next: (vars: Variables) => void): void {\n    if (prop.value) {\n      if ('value' in prop.value) {\n        if (prop.value.value === undefined || prop.value.value === null) {\n          vars[prop.name] = `<${prop.value.value}>`;\n        } else {\n          vars[prop.name] = prop.value.value;\n        }\n      } else if ('description' in prop.value && prop.value.type !== 'function') {\n        vars[prop.name] = `<${prop.value.description}>`;\n      } else if (prop.value.type === 'undefined') {\n        vars[prop.name] = '<undefined>';\n      }\n    }\n\n    next(vars);\n  }\n}\n\nconst INTEGRATION_NAME = 'LocalVariables';\n\n/**\n * Adds local variables to exception frames\n */\nconst _localVariablesSyncIntegration = ((\n  options: LocalVariablesIntegrationOptions = {},\n  sessionOverride?: DebugSession,\n) => {\n  const cachedFrames: LRUMap<string, FrameVariables[]> = new LRUMap(20);\n  let rateLimiter: RateLimitIncrement | undefined;\n  let shouldProcessEvent = false;\n\n  function addLocalVariablesToException(exception: Exception): void {\n    const hash = hashFrames(exception.stacktrace?.frames);\n\n    if (hash === undefined) {\n      return;\n    }\n\n    // Check if we have local variables for an exception that matches the hash\n    // remove is identical to get but also removes the entry from the cache\n    const cachedFrame = cachedFrames.remove(hash);\n\n    if (cachedFrame === undefined) {\n      return;\n    }\n\n    // Filter out frames where the function name is `new Promise` since these are in the error.stack frames\n    // but do not appear in the debugger call frames\n    const frames = (exception.stacktrace?.frames || []).filter(frame => frame.function !== 'new Promise');\n\n    for (let i = 0; i < frames.length; i++) {\n      // Sentry frames are in reverse order\n      const frameIndex = frames.length - i - 1;\n\n      const cachedFrameVariable = cachedFrame[i];\n      const frameVariable = frames[frameIndex];\n\n      // Drop out if we run out of frames to match up\n      if (!frameVariable || !cachedFrameVariable) {\n        break;\n      }\n\n      if (\n        // We need to have vars to add\n        cachedFrameVariable.vars === undefined ||\n        // We're not interested in frames that are not in_app because the vars are not relevant\n        frameVariable.in_app === false ||\n        // The function names need to match\n        !functionNamesMatch(frameVariable.function, cachedFrameVariable.function)\n      ) {\n        continue;\n      }\n\n      frameVariable.vars = cachedFrameVariable.vars;\n    }\n  }\n\n  function addLocalVariablesToEvent(event: Event): Event {\n    for (const exception of event.exception?.values || []) {\n      addLocalVariablesToException(exception);\n    }\n\n    return event;\n  }\n\n  return {\n    name: INTEGRATION_NAME,\n    async setupOnce() {\n      const client = getClient<NodeClient>();\n      const clientOptions = client?.getOptions();\n\n      if (!clientOptions?.includeLocalVariables) {\n        return;\n      }\n\n      // Only setup this integration if the Node version is >= v18\n      // https://github.com/getsentry/sentry-javascript/issues/7697\n      const unsupportedNodeVersion = NODE_MAJOR < 18;\n\n      if (unsupportedNodeVersion) {\n        debug.log('The `LocalVariables` integration is only supported on Node >= v18.');\n        return;\n      }\n\n      if (await isDebuggerEnabled()) {\n        debug.warn('Local variables capture has been disabled because the debugger was already enabled');\n        return;\n      }\n\n      AsyncSession.create(sessionOverride).then(\n        session => {\n          function handlePaused(\n            stackParser: StackParser,\n            { params: { reason, data, callFrames } }: InspectorNotification<PausedExceptionEvent>,\n            complete: () => void,\n          ): void {\n            if (reason !== 'exception' && reason !== 'promiseRejection') {\n              complete();\n              return;\n            }\n\n            rateLimiter?.();\n\n            // data.description contains the original error.stack\n            const exceptionHash = hashFromStack(stackParser, data.description);\n\n            if (exceptionHash == undefined) {\n              complete();\n              return;\n            }\n\n            const { add, next } = createCallbackList<FrameVariables[]>(frames => {\n              cachedFrames.set(exceptionHash, frames);\n              complete();\n            });\n\n            // Because we're queuing up and making all these calls synchronously, we can potentially overflow the stack\n            // For this reason we only attempt to get local variables for the first 5 frames\n            for (let i = 0; i < Math.min(callFrames.length, 5); i++) {\n              // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n              const { scopeChain, functionName, this: obj } = callFrames[i]!;\n\n              const localScope = scopeChain.find(scope => scope.type === 'local');\n\n              // obj.className is undefined in ESM modules\n              const fn =\n                obj.className === 'global' || !obj.className ? functionName : `${obj.className}.${functionName}`;\n\n              if (localScope?.object.objectId === undefined) {\n                add(frames => {\n                  frames[i] = { function: fn };\n                  next(frames);\n                });\n              } else {\n                const id = localScope.object.objectId;\n                add(frames =>\n                  session.getLocalVariables(id, vars => {\n                    frames[i] = { function: fn, vars };\n                    next(frames);\n                  }),\n                );\n              }\n            }\n\n            next([]);\n          }\n\n          const captureAll = options.captureAllExceptions !== false;\n\n          session.configureAndConnect(\n            (ev, complete) =>\n              handlePaused(clientOptions.stackParser, ev as InspectorNotification<PausedExceptionEvent>, complete),\n            captureAll,\n          );\n\n          if (captureAll) {\n            const max = options.maxExceptionsPerSecond || 50;\n\n            rateLimiter = createRateLimiter(\n              max,\n              () => {\n                debug.log('Local variables rate-limit lifted.');\n                session.setPauseOnExceptions(true);\n              },\n              seconds => {\n                debug.log(\n                  `Local variables rate-limit exceeded. Disabling capturing of caught exceptions for ${seconds} seconds.`,\n                );\n                session.setPauseOnExceptions(false);\n              },\n            );\n          }\n\n          shouldProcessEvent = true;\n        },\n        error => {\n          debug.log('The `LocalVariables` integration failed to start.', error);\n        },\n      );\n    },\n    processEvent(event: Event): Event {\n      if (shouldProcessEvent) {\n        return addLocalVariablesToEvent(event);\n      }\n\n      return event;\n    },\n    // These are entirely for testing\n    _getCachedFramesCount(): number {\n      return cachedFrames.size;\n    },\n    _getFirstCachedFrame(): FrameVariables[] | undefined {\n      return cachedFrames.values()[0];\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Adds local variables to exception frames.\n */\nexport const localVariablesSyncIntegration = defineIntegration(_localVariablesSyncIntegration);\n"], "names": ["LRUMap", "functionNamesMatch", "getClient", "NODE_MAJOR", "debug", "isDebuggerEnabled", "createRateLimiter", "defineIntegration"], "mappings": ";;;;;;;AAeA,4CAAA,GACO,SAAS,UAAU,CAAC,MAAM,EAAgD;IAC/E,IAAI,MAAA,KAAW,SAAS,EAAE;QACxB;IACJ;IAEA,wDAAA;IACE,OAAO,MAAM,CAAC,KAAK,CAAC,CAAA,EAAG,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,GAAK,CAAC,EAAA,GAAA,CAAA,CAAA,EAAA,KAAA,CAAA,QAAA,CAAA,CAAA,EAAA,KAAA,CAAA,MAAA,CAAA,CAAA,EAAA,KAAA,CAAA,KAAA,CAAA,CAAA,EAAA,EAAA,CAAA;AACA;AAEA;;;CAGA,GACA,SAAA,aAAA,CAAA,WAAA,EAAA,KAAA,EAAA;IACA,IAAA,KAAA,KAAA,SAAA,EAAA;QACA,OAAA,SAAA;IACA;IAEA,OAAA,UAAA,CAAA,WAAA,CAAA,KAAA,EAAA,CAAA,CAAA,CAAA;AACA;AAgBA,gEAAA,GACA,SAAA,kBAAA,CAAA,QAAA,EAAA;IACA,yDAAA;IACA,IAAA,SAAA,GAAA,EAAA;IAEA,IAAA,eAAA,GAAA,KAAA;IACA,SAAA,eAAA,CAAA,MAAA,EAAA;QACA,SAAA,GAAA,EAAA;QACA,IAAA,eAAA,EAAA;YACA;QACA;QACA,eAAA,GAAA,IAAA;QACA,QAAA,CAAA,MAAA,CAAA;IACA;IAEA,iCAAA;IACA,SAAA,CAAA,IAAA,CAAA,eAAA,CAAA;IAEA,SAAA,GAAA,CAAA,EAAA,EAAA;QACA,SAAA,CAAA,IAAA,CAAA,EAAA,CAAA;IACA;IAEA,SAAA,IAAA,CAAA,MAAA,EAAA;QACA,MAAA,MAAA,GAAA,SAAA,CAAA,GAAA,EAAA,IAAA,eAAA;QAEA,IAAA;YACA,MAAA,CAAA,MAAA,CAAA;QACA,CAAA,CAAA,OAAA;YACA,oEAAA;YACA,eAAA,CAAA,MAAA,CAAA;QACA;IACA;IAEA,OAAA;QAAA,GAAA;QAAA,IAAA;IAAA,CAAA;AACA;AAEA;;;;;;;;CAQA,GACA,MAAA,YAAA,EAAA;IACA,6CAAA,GACA,WAAA,CAAA,QAAA,CAAA;QAAA,IAAA,CAAA,QAAA,GAAA,QAAA;IACA,EAAA;IACA;IAEA,aAAA,MAAA,CAAA,SAAA,EAAA;QACA,IAAA,SAAA,EAAA;YACA,OAAA,SAAA;QACA;QAEA,MAAA,SAAA,GAAA,MAAA,OAAA,gBAAA,CAAA;QACA,OAAA,IAAA,YAAA,CAAA,IAAA,SAAA,CAAA,OAAA,EAAA,CAAA;IACA;IAEA,gBAAA,GACA,mBAAA,CAAA,OAAA,EAAA,UAAA,EAAA;QACA,IAAA,CAAA,QAAA,CAAA,OAAA,EAAA;QAEA,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA,iBAAA,GAAA,KAAA,IAAA;YACA,OAAA,CAAA,KAAA,EAAA,MAAA;gBACA,+FAAA;gBACA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA,iBAAA,CAAA;YACA,CAAA,CAAA;QACA,CAAA,CAAA;QAEA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA,iBAAA,CAAA;QACA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA,+BAAA,EAAA;YAAA,KAAA,EAAA,UAAA,GAAA,KAAA,GAAA,UAAA;QAAA,CAAA,CAAA;IACA;IAEA,oBAAA,CAAA,UAAA,EAAA;QACA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA,+BAAA,EAAA;YAAA,KAAA,EAAA,UAAA,GAAA,KAAA,GAAA,UAAA;QAAA,CAAA,CAAA;IACA;IAEA,gBAAA,GACA,iBAAA,CAAA,QAAA,EAAA,QAAA,EAAA;QACA,IAAA,CAAA,cAAA,CAAA,QAAA,GAAA,KAAA,IAAA;YACA,MAAA,EAAA,GAAA,EAAA,IAAA,EAAA,GAAA,kBAAA,CAAA,QAAA,CAAA;YAEA,KAAA,MAAA,IAAA,IAAA,KAAA,CAAA;gBACA,IAAA,IAAA,CAAA,KAAA,EAAA,QAAA,IAAA,IAAA,CAAA,KAAA,CAAA,SAAA,KAAA,OAAA,EAAA;oBACA,MAAA,EAAA,GAAA,IAAA,CAAA,KAAA,CAAA,QAAA;oBACA,GAAA,EAAA,IAAA,GAAA,IAAA,CAAA,YAAA,CAAA,EAAA,EAAA,IAAA,CAAA,IAAA,EAAA,IAAA,EAAA,IAAA,CAAA,CAAA;gBACA,CAAA,MAAA,IAAA,IAAA,CAAA,KAAA,EAAA,QAAA,IAAA,IAAA,CAAA,KAAA,CAAA,SAAA,KAAA,QAAA,EAAA;oBACA,MAAA,EAAA,GAAA,IAAA,CAAA,KAAA,CAAA,QAAA;oBACA,GAAA,EAAA,IAAA,GAAA,IAAA,CAAA,aAAA,CAAA,EAAA,EAAA,IAAA,CAAA,IAAA,EAAA,IAAA,EAAA,IAAA,CAAA,CAAA;gBACA,CAAA,MAAA,IAAA,IAAA,CAAA,KAAA,EAAA;oBACA,GAAA,EAAA,IAAA,GAAA,IAAA,CAAA,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,IAAA,CAAA,CAAA;gBACA;YACA;YAEA,IAAA,CAAA,CAAA,CAAA,CAAA;QACA,CAAA,CAAA;IACA;IAEA;;GAEA,GACA,cAAA,CAAA,QAAA,EAAA,IAAA,EAAA;QACA,IAAA,CAAA,QAAA,CAAA,IAAA,CACA,uBAAA,EACA;YACA,QAAA;YACA,aAAA,EAAA,IAAA;QACA,CAAA,EACA,CAAA,GAAA,EAAA,MAAA,KAAA;YACA,IAAA,GAAA,EAAA;gBACA,IAAA,CAAA,EAAA,CAAA;YACA,CAAA,MAAA;gBACA,IAAA,CAAA,MAAA,CAAA,MAAA,CAAA;YACA;QACA,CAAA;IAEA;IAEA;;GAEA,GACA,YAAA,CAAA,QAAA,EAAA,IAAA,EAAA,IAAA,EAAA,IAAA,EAAA;QACA,IAAA,CAAA,cAAA,CAAA,QAAA,GAAA,KAAA,IAAA;YACA,IAAA,CAAA,IAAA,CAAA,GAAA,MACA,MAAA,EAAA,CAAA,GAAA,CAAA,CAAA,IAAA,KAAA,QAAA,IAAA,CAAA,KAAA,CAAA,QAAA,CAAA,CAAA,CAAA,IAAA,EAAA,EAAA,CAAA,CAAA,EACA,IAAA,CAAA,CAAA,CAAA,EAAA,CAAA,GAAA,QAAA,CAAA,CAAA,CAAA,IAAA,EAAA,EAAA,CAAA,GAAA,QAAA,CAAA,CAAA,CAAA,IAAA,EAAA,EAAA,CAAA,EACA,GAAA,EAAA,CAAA,GAAA,CAAA,CAAA,KAAA,EAAA,KAAA,CAAA;YAEA,IAAA,CAAA,IAAA,CAAA;QACA,CAAA,CAAA;IACA;IAEA;;GAEA,GACA,aAAA,CAAA,QAAA,EAAA,IAAA,EAAA,IAAA,EAAA,IAAA,EAAA;QACA,IAAA,CAAA,cAAA,CAAA,QAAA,GAAA,KAAA,IAAA;YACA,IAAA,CAAA,IAAA,CAAA,GAAA,MACA,GAAA,EAAA,CAAA,GAAA;oBAAA,CAAA,CAAA,IAAA;oBAAA,CAAA,CAAA,KAAA,EAAA,KAAA;iBAAA,EACA,MAAA,CAAA,CAAA,GAAA,EAAA,CAAA,GAAA,EAAA,GAAA,CAAA,KAAA;gBACA,GAAA,CAAA,GAAA,CAAA,GAAA,GAAA;gBACA,OAAA,GAAA;YACA,CAAA,EAAA,CAAA,CAAA,EAAA;YAEA,IAAA,CAAA,IAAA,CAAA;QACA,CAAA,CAAA;IACA;IAEA;;GAEA,GACA,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,IAAA,EAAA;QACA,IAAA,IAAA,CAAA,KAAA,EAAA;YACA,IAAA,OAAA,IAAA,IAAA,CAAA,KAAA,EAAA;gBACA,IAAA,IAAA,CAAA,KAAA,CAAA,KAAA,KAAA,SAAA,IAAA,IAAA,CAAA,KAAA,CAAA,KAAA,KAAA,IAAA,EAAA;oBACA,IAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,KAAA,CAAA,CAAA,CAAA;gBACA,CAAA,MAAA;oBACA,IAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA,IAAA,CAAA,KAAA,CAAA,KAAA;gBACA;YACA,CAAA,MAAA,IAAA,aAAA,IAAA,IAAA,CAAA,KAAA,IAAA,IAAA,CAAA,KAAA,CAAA,IAAA,KAAA,UAAA,EAAA;gBACA,IAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,WAAA,CAAA,CAAA,CAAA;YACA,CAAA,MAAA,IAAA,IAAA,CAAA,KAAA,CAAA,IAAA,KAAA,WAAA,EAAA;gBACA,IAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA,aAAA;YACA;QACA;QAEA,IAAA,CAAA,IAAA,CAAA;IACA;AACA;AAEA,MAAA,gBAAA,GAAA,gBAAA;AAEA;;CAEA,GACA,MAAA,8BAAA,GAAA,CACA,OAAA,GAAA,CAAA,CAAA,EACA,eAAA;IAEA,MAAA,YAAA,GAAA,IAAAA,KAAAA,MAAA,CAAA,EAAA,CAAA;IACA,IAAA,WAAA;IACA,IAAA,kBAAA,GAAA,KAAA;IAEA,SAAA,4BAAA,CAAA,SAAA,EAAA;QACA,MAAA,IAAA,GAAA,UAAA,CAAA,SAAA,CAAA,UAAA,EAAA,MAAA,CAAA;QAEA,IAAA,IAAA,KAAA,SAAA,EAAA;YACA;QACA;QAEA,0EAAA;QACA,uEAAA;QACA,MAAA,WAAA,GAAA,YAAA,CAAA,MAAA,CAAA,IAAA,CAAA;QAEA,IAAA,WAAA,KAAA,SAAA,EAAA;YACA;QACA;QAEA,uGAAA;QACA,gDAAA;QACA,MAAA,MAAA,GAAA,CAAA,SAAA,CAAA,UAAA,EAAA,MAAA,IAAA,EAAA,EAAA,MAAA,EAAA,KAAA,GAAA,KAAA,CAAA,QAAA,KAAA,aAAA,CAAA;QAEA,IAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,MAAA,CAAA,MAAA,EAAA,CAAA,EAAA,CAAA;YACA,qCAAA;YACA,MAAA,UAAA,GAAA,MAAA,CAAA,MAAA,GAAA,CAAA,GAAA,CAAA;YAEA,MAAA,mBAAA,GAAA,WAAA,CAAA,CAAA,CAAA;YACA,MAAA,aAAA,GAAA,MAAA,CAAA,UAAA,CAAA;YAEA,+CAAA;YACA,IAAA,CAAA,aAAA,IAAA,CAAA,mBAAA,EAAA;gBACA;YACA;YAEA,IACA,8BAAA;YACA,mBAAA,CAAA,IAAA,KAAA,SAAA,IACA,uFAAA;YACA,aAAA,CAAA,MAAA,KAAA,KAAA,IACA,mCAAA;YACA,CAAAC,OAAAA,kBAAA,CAAA,aAAA,CAAA,QAAA,EAAA,mBAAA,CAAA,QAAA,GACA;gBACA;YACA;YAEA,aAAA,CAAA,IAAA,GAAA,mBAAA,CAAA,IAAA;QACA;IACA;IAEA,SAAA,wBAAA,CAAA,KAAA,EAAA;QACA,KAAA,MAAA,SAAA,IAAA,KAAA,CAAA,SAAA,EAAA,MAAA,IAAA,EAAA,CAAA;YACA,4BAAA,CAAA,SAAA,CAAA;QACA;QAEA,OAAA,KAAA;IACA;IAEA,OAAA;QACA,IAAA,EAAA,gBAAA;QACA,MAAA,SAAA,GAAA;YACA,MAAA,MAAA,GAAAC,KAAAA,SAAA,EAAA;YACA,MAAA,aAAA,GAAA,MAAA,EAAA,UAAA,EAAA;YAEA,IAAA,CAAA,aAAA,EAAA,qBAAA,EAAA;gBACA;YACA;YAEA,4DAAA;YACA,6DAAA;YACA,MAAA,sBAAA,GAAAC,YAAAA,UAAA,GAAA,EAAA;YAEA,IAAA,sBAAA,EAAA;gBACAC,KAAAA,KAAA,CAAA,GAAA,CAAA,oEAAA,CAAA;gBACA;YACA;YAEA,IAAA,MAAAC,MAAAA,iBAAA,EAAA,EAAA;gBACAD,KAAAA,KAAA,CAAA,IAAA,CAAA,oFAAA,CAAA;gBACA;YACA;YAEA,YAAA,CAAA,MAAA,CAAA,eAAA,CAAA,CAAA,IAAA,EACA,OAAA,IAAA;gBACA,SAAA,YAAA,CACA,WAAA,EACA,EAAA,MAAA,EAAA,EAAA,MAAA,EAAA,IAAA,EAAA,UAAA,EAAA,EAAA,EACA,QAAA;oBAEA,IAAA,MAAA,KAAA,WAAA,IAAA,MAAA,KAAA,kBAAA,EAAA;wBACA,QAAA,EAAA;wBACA;oBACA;oBAEA,WAAA,IAAA;oBAEA,qDAAA;oBACA,MAAA,aAAA,GAAA,aAAA,CAAA,WAAA,EAAA,IAAA,CAAA,WAAA,CAAA;oBAEA,IAAA,aAAA,IAAA,SAAA,EAAA;wBACA,QAAA,EAAA;wBACA;oBACA;oBAEA,MAAA,EAAA,GAAA,EAAA,IAAA,EAAA,GAAA,kBAAA,EAAA,MAAA,IAAA;wBACA,YAAA,CAAA,GAAA,CAAA,aAAA,EAAA,MAAA,CAAA;wBACA,QAAA,EAAA;oBACA,CAAA,CAAA;oBAEA,2GAAA;oBACA,gFAAA;oBACA,IAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,IAAA,CAAA,GAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA;wBACA,oEAAA;wBACA,MAAA,EAAA,UAAA,EAAA,YAAA,EAAA,IAAA,EAAA,GAAA,EAAA,GAAA,UAAA,CAAA,CAAA,CAAA;wBAEA,MAAA,UAAA,GAAA,UAAA,CAAA,IAAA,EAAA,KAAA,GAAA,KAAA,CAAA,IAAA,KAAA,OAAA,CAAA;wBAEA,4CAAA;wBACA,MAAA,EAAA,GACA,GAAA,CAAA,SAAA,KAAA,QAAA,IAAA,CAAA,GAAA,CAAA,SAAA,GAAA,YAAA,GAAA,CAAA,EAAA,GAAA,CAAA,SAAA,CAAA,CAAA,EAAA,YAAA,CAAA,CAAA;wBAEA,IAAA,UAAA,EAAA,MAAA,CAAA,QAAA,KAAA,SAAA,EAAA;4BACA,GAAA,EAAA,MAAA,IAAA;gCACA,MAAA,CAAA,CAAA,CAAA,GAAA;oCAAA,QAAA,EAAA,EAAA;gCAAA,CAAA;gCACA,IAAA,CAAA,MAAA,CAAA;4BACA,CAAA,CAAA;wBACA,CAAA,MAAA;4BACA,MAAA,EAAA,GAAA,UAAA,CAAA,MAAA,CAAA,QAAA;4BACA,GAAA,EAAA,MAAA,GACA,OAAA,CAAA,iBAAA,CAAA,EAAA,GAAA,IAAA,IAAA;oCACA,MAAA,CAAA,CAAA,CAAA,GAAA;wCAAA,QAAA,EAAA,EAAA;wCAAA,IAAA;oCAAA,CAAA;oCACA,IAAA,CAAA,MAAA,CAAA;gCACA,CAAA,CAAA;wBAEA;oBACA;oBAEA,IAAA,CAAA,EAAA,CAAA;gBACA;gBAEA,MAAA,UAAA,GAAA,OAAA,CAAA,oBAAA,KAAA,KAAA;gBAEA,OAAA,CAAA,mBAAA,CACA,CAAA,EAAA,EAAA,QAAA,GACA,YAAA,CAAA,aAAA,CAAA,WAAA,EAAA,EAAA,EAAA,QAAA,CAAA,EACA,UAAA;gBAGA,IAAA,UAAA,EAAA;oBACA,MAAA,GAAA,GAAA,OAAA,CAAA,sBAAA,IAAA,EAAA;oBAEA,WAAA,GAAAE,OAAAA,iBAAA,CACA,GAAA,EACA,MAAA;wBACAF,KAAAA,KAAA,CAAA,GAAA,CAAA,oCAAA,CAAA;wBACA,OAAA,CAAA,oBAAA,CAAA,IAAA,CAAA;oBACA,CAAA,GACA,OAAA,IAAA;wBACAA,KAAAA,KAAA,CAAA,GAAA,CACA,CAAA,kFAAA,EAAA,OAAA,CAAA,SAAA,CAAA;wBAEA,OAAA,CAAA,oBAAA,CAAA,KAAA,CAAA;oBACA,CAAA;gBAEA;gBAEA,kBAAA,GAAA,IAAA;YACA,CAAA,GACA,KAAA,IAAA;gBACAA,KAAAA,KAAA,CAAA,GAAA,CAAA,mDAAA,EAAA,KAAA,CAAA;YACA,CAAA;QAEA,CAAA;QACA,YAAA,EAAA,KAAA,EAAA;YACA,IAAA,kBAAA,EAAA;gBACA,OAAA,wBAAA,CAAA,KAAA,CAAA;YACA;YAEA,OAAA,KAAA;QACA,CAAA;QACA,iCAAA;QACA,qBAAA,GAAA;YACA,OAAA,YAAA,CAAA,IAAA;QACA,CAAA;QACA,oBAAA,GAAA;YACA,OAAA,YAAA,CAAA,MAAA,EAAA,CAAA,CAAA,CAAA;QACA,CAAA;IACA,CAAA;AACA,CAAA,CAAA;AAEA;;CAEA,GACA,MAAA,6BAAA,GAAAG,KAAAA,iBAAA,CAAA,8BAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2470, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/integrations/local-variables/index.ts"], "sourcesContent": ["import type { Integration } from '@sentry/core';\nimport { NODE_VERSION } from '../../nodeVersion';\nimport type { LocalVariablesIntegrationOptions } from './common';\nimport { localVariablesAsyncIntegration } from './local-variables-async';\nimport { localVariablesSyncIntegration } from './local-variables-sync';\n\nexport const localVariablesIntegration = (options: LocalVariablesIntegrationOptions = {}): Integration => {\n  return NODE_VERSION.major < 19 ? localVariablesSyncIntegration(options) : localVariablesAsyncIntegration(options);\n};\n"], "names": ["NODE_VERSION", "localVariablesSyncIntegration", "localVariablesAsyncIntegration"], "mappings": ";;;;;;AAMO,MAAM,4BAA4B,CAAC,OAAO,GAAqC,CAAA,CAAE,KAAkB;IACxG,OAAOA,YAAAA,YAAY,CAAC,KAAA,GAAQ,EAAA,GAAKC,mBAAAA,6BAA6B,CAAC,OAAO,CAAA,GAAIC,oBAAAA,8BAA8B,CAAC,OAAO,CAAC;AACnH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2485, "column": 0}, "map": {"version": 3, "file": "commonjs.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/utils/commonjs.ts"], "sourcesContent": ["/** Detect CommonJS. */\nexport function isCjs(): boolean {\n  try {\n    return typeof module !== 'undefined' && typeof module.exports !== 'undefined';\n  } catch {\n    return false;\n  }\n}\n"], "names": [], "mappings": ";;;AAAA,qBAAA,GACO,SAAS,KAAK,GAAY;IAC/B,IAAI;QACF,OAAO,OAAO,MAAA,kCAAW,WAAA,IAAe,OAAO,MAAM,CAAC,OAAA,KAAY,WAAW;IACjF,EAAI,OAAM;QACN,OAAO,KAAK;IAChB;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2501, "column": 0}, "map": {"version": 3, "file": "modules.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/integrations/modules.ts"], "sourcesContent": ["import { existsSync, readFileSync } from 'node:fs';\nimport { dirname, join } from 'node:path';\nimport type { IntegrationFn } from '@sentry/core';\nimport { isCjs } from '../utils/commonjs';\n\ntype ModuleInfo = Record<string, string>;\n\nlet moduleCache: ModuleInfo | undefined;\n\nconst INTEGRATION_NAME = 'Modules';\n\ndeclare const __SENTRY_SERVER_MODULES__: Record<string, string>;\n\n/**\n * `__SENTRY_SERVER_MODULES__` can be replaced at build time with the modules loaded by the server.\n * Right now, we leverage this in Next.js to circumvent the problem that we do not get access to these things at runtime.\n */\nconst SERVER_MODULES = typeof __SENTRY_SERVER_MODULES__ === 'undefined' ? {} : __SENTRY_SERVER_MODULES__;\n\nconst _modulesIntegration = (() => {\n  return {\n    name: INTEGRATION_NAME,\n    processEvent(event) {\n      event.modules = {\n        ...event.modules,\n        ..._getModules(),\n      };\n\n      return event;\n    },\n    getModules: _getModules,\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Add node modules / packages to the event.\n * For this, multiple sources are used:\n * - They can be injected at build time into the __SENTRY_SERVER_MODULES__ variable (e.g. in Next.js)\n * - They are extracted from the dependencies & devDependencies in the package.json file\n * - They are extracted from the require.cache (CJS only)\n */\nexport const modulesIntegration = _modulesIntegration;\n\nfunction getRequireCachePaths(): string[] {\n  try {\n    return require.cache ? Object.keys(require.cache as Record<string, unknown>) : [];\n  } catch {\n    return [];\n  }\n}\n\n/** Extract information about package.json modules */\nfunction collectModules(): ModuleInfo {\n  return {\n    ...SERVER_MODULES,\n    ...getModulesFromPackageJson(),\n    ...(isCjs() ? collectRequireModules() : {}),\n  };\n}\n\n/** Extract information about package.json modules from require.cache */\nfunction collectRequireModules(): ModuleInfo {\n  const mainPaths = require.main?.paths || [];\n  const paths = getRequireCachePaths();\n\n  // We start with the modules from package.json (if possible)\n  // These may be overwritten by more specific versions from the require.cache\n  const infos: ModuleInfo = {};\n  const seen = new Set<string>();\n\n  paths.forEach(path => {\n    let dir = path;\n\n    /** Traverse directories upward in the search of package.json file */\n    const updir = (): void | (() => void) => {\n      const orig = dir;\n      dir = dirname(orig);\n\n      if (!dir || orig === dir || seen.has(orig)) {\n        return undefined;\n      }\n      if (mainPaths.indexOf(dir) < 0) {\n        return updir();\n      }\n\n      const pkgfile = join(orig, 'package.json');\n      seen.add(orig);\n\n      if (!existsSync(pkgfile)) {\n        return updir();\n      }\n\n      try {\n        const info = JSON.parse(readFileSync(pkgfile, 'utf8')) as {\n          name: string;\n          version: string;\n        };\n        infos[info.name] = info.version;\n      } catch {\n        // no-empty\n      }\n    };\n\n    updir();\n  });\n\n  return infos;\n}\n\n/** Fetches the list of modules and the versions loaded by the entry file for your node.js app. */\nfunction _getModules(): ModuleInfo {\n  if (!moduleCache) {\n    moduleCache = collectModules();\n  }\n  return moduleCache;\n}\n\ninterface PackageJson {\n  dependencies?: Record<string, string>;\n  devDependencies?: Record<string, string>;\n}\n\nfunction getPackageJson(): PackageJson {\n  try {\n    const filePath = join(process.cwd(), 'package.json');\n    const packageJson = JSON.parse(readFileSync(filePath, 'utf8')) as PackageJson;\n\n    return packageJson;\n  } catch {\n    return {};\n  }\n}\n\nfunction getModulesFromPackageJson(): ModuleInfo {\n  const packageJson = getPackageJson();\n\n  return {\n    ...packageJson.dependencies,\n    ...packageJson.devDependencies,\n  };\n}\n"], "names": ["isCjs", "dirname", "join", "existsSync", "readFileSync"], "mappings": ";;;;;;AAOA,IAAI,WAAW;AAEf,MAAM,gBAAA,GAAmB,SAAS;AAIlC;;;CAGA,GACA,MAAM,cAAA,GAAiB,OAAO,yBAAA,KAA8B,cAAc,CAAA,CAAC,GAAI,yBAAyB;AAExG,MAAM,mBAAA,GAAuB,MAAM;IACjC,OAAO;QACL,IAAI,EAAE,gBAAgB;QACtB,YAAY,EAAC,KAAK,EAAE;YAClB,KAAK,CAAC,OAAA,GAAU;gBACd,GAAG,KAAK,CAAC,OAAO;gBAChB,GAAG,WAAW,EAAE;YACxB,CAAO;YAED,OAAO,KAAK;QAClB,CAAK;QACD,UAAU,EAAE,WAAW;IAC3B,CAAG;AACH,CAAC,CAAA;AAED;;;;;;CAMA,GACO,MAAM,kBAAA,GAAqB;AAElC,SAAS,oBAAoB,GAAa;IACxC,IAAI;QACF,OAAO,OAAO,CAAC,KAAA,0BAAQ,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,KAAA,EAAM,GAA8B,EAAE;IACrF,EAAI,OAAM;QACN,OAAO,EAAE;IACb;AACA;AAEA,mDAAA,GACA,SAAS,cAAc,GAAe;IACpC,OAAO;QACL,GAAG,cAAc;QACjB,GAAG,yBAAyB,EAAE;QAC9B,GAAIA,SAAAA,KAAK,EAAC,GAAI,qBAAqB,EAAC,GAAI,CAAA,CAAE,CAAC;IAC/C,CAAG;AACH;AAEA,sEAAA,GACA,SAAS,qBAAqB,GAAe;IAC3C,MAAM,SAAA,GAAY,OAAO,mDAAC,IAAI,EAAE,KAAA,IAAS,EAAE;IAC3C,MAAM,KAAA,GAAQ,oBAAoB,EAAE;IAEtC,4DAAA;IACA,4EAAA;IACE,MAAM,KAAK,GAAe,CAAA,CAAE;IAC5B,MAAM,IAAA,GAAO,IAAI,GAAG,EAAU;IAE9B,KAAK,CAAC,OAAO,EAAC,QAAQ;QACpB,IAAI,GAAA,GAAM,IAAI;QAElB,mEAAA,GACI,MAAM,KAAA,GAAQ,MAA2B;YACvC,MAAM,IAAA,GAAO,GAAG;YAChB,GAAA,GAAMC,UAAAA,OAAO,CAAC,IAAI,CAAC;YAEnB,IAAI,CAAC,GAAA,IAAO,IAAA,KAAS,GAAA,IAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;gBAC1C,OAAO,SAAS;YACxB;YACM,IAAI,SAAS,CAAC,OAAO,CAAC,GAAG,CAAA,GAAI,CAAC,EAAE;gBAC9B,OAAO,KAAK,EAAE;YACtB;YAEM,MAAM,UAAUC,UAAAA,IAAI,CAAC,IAAI,EAAE,cAAc,CAAC;YAC1C,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;YAEd,IAAI,CAACC,QAAAA,UAAU,CAAC,OAAO,CAAC,EAAE;gBACxB,OAAO,KAAK,EAAE;YACtB;YAEM,IAAI;gBACF,MAAM,IAAA,GAAO,IAAI,CAAC,KAAK,CAACC,QAAAA,YAAY,CAAC,OAAO,EAAE,MAAM,CAAC;gBAIrD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAA,GAAI,IAAI,CAAC,OAAO;YACvC,EAAQ,OAAM;YACd,WAAA;YACA;QACA,CAAK;QAED,KAAK,EAAE;IACX,CAAG,CAAC;IAEF,OAAO,KAAK;AACd;AAEA,gGAAA,GACA,SAAS,WAAW,GAAe;IACjC,IAAI,CAAC,WAAW,EAAE;QAChB,WAAA,GAAc,cAAc,EAAE;IAClC;IACE,OAAO,WAAW;AACpB;AAOA,SAAS,cAAc,GAAgB;IACrC,IAAI;QACF,MAAM,QAAA,GAAWF,UAAAA,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,cAAc,CAAC;QACpD,MAAM,WAAA,GAAc,IAAI,CAAC,KAAK,CAACE,QAAAA,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAA;QAE7D,OAAO,WAAW;IACtB,EAAI,OAAM;QACN,OAAO,CAAA,CAAE;IACb;AACA;AAEA,SAAS,yBAAyB,GAAe;IAC/C,MAAM,WAAA,GAAc,cAAc,EAAE;IAEpC,OAAO;QACL,GAAG,WAAW,CAAC,YAAY;QAC3B,GAAG,WAAW,CAAC,eAAe;IAClC,CAAG;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2609, "column": 0}, "map": {"version": 3, "file": "errorhandling.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/utils/errorhandling.ts"], "sourcesContent": ["import { consoleSandbox, debug, getClient } from '@sentry/core';\nimport { DEBUG_BUILD } from '../debug-build';\nimport type { NodeClient } from '../sdk/client';\n\nconst DEFAULT_SHUTDOWN_TIMEOUT = 2000;\n\n/**\n * @hidden\n */\nexport function logAndExitProcess(error: unknown): void {\n  consoleSandbox(() => {\n    // eslint-disable-next-line no-console\n    console.error(error);\n  });\n\n  const client = getClient<NodeClient>();\n\n  if (client === undefined) {\n    DEBUG_BUILD && debug.warn('No NodeClient was defined, we are exiting the process now.');\n    global.process.exit(1);\n    return;\n  }\n\n  const options = client.getOptions();\n  const timeout =\n    options?.shutdownTimeout && options.shutdownTimeout > 0 ? options.shutdownTimeout : DEFAULT_SHUTDOWN_TIMEOUT;\n  client.close(timeout).then(\n    (result: boolean) => {\n      if (!result) {\n        DEBUG_BUILD && debug.warn('We reached the timeout for emptying the request buffer, still exiting now!');\n      }\n      global.process.exit(1);\n    },\n    error => {\n      DEBUG_BUILD && debug.error(error);\n    },\n  );\n}\n"], "names": ["consoleSandbox", "getClient", "DEBUG_BUILD", "debug"], "mappings": ";;;;;AAIA,MAAM,wBAAA,GAA2B,IAAI;AAErC;;CAEA,GACO,SAAS,iBAAiB,CAAC,KAAK,EAAiB;IACtDA,KAAAA,cAAc,CAAC,MAAM;QACvB,sCAAA;QACI,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC;IACxB,CAAG,CAAC;IAEF,MAAM,MAAA,GAASC,KAAAA,SAAS,EAAc;IAEtC,IAAI,MAAA,KAAW,SAAS,EAAE;QACxBC,WAAAA,WAAAA,IAAeC,KAAAA,KAAK,CAAC,IAAI,CAAC,4DAA4D,CAAC;QACvF,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;QACtB;IACJ;IAEE,MAAM,OAAA,GAAU,MAAM,CAAC,UAAU,EAAE;IACnC,MAAM,OAAA,GACJ,OAAO,EAAE,eAAA,IAAmB,OAAO,CAAC,eAAA,GAAkB,CAAA,GAAI,OAAO,CAAC,eAAA,GAAkB,wBAAwB;IAC9G,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CACxB,CAAC,MAAM,KAAc;QACnB,IAAI,CAAC,MAAM,EAAE;YACXD,WAAAA,WAAAA,IAAeC,KAAAA,KAAK,CAAC,IAAI,CAAC,4EAA4E,CAAC;QAC/G;QACM,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;IAC5B,CAAK,GACD,SAAS;QACPD,WAAAA,WAAAA,IAAeC,KAAAA,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC;IACvC,CAAK;AAEL", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2645, "column": 0}, "map": {"version": 3, "file": "onuncaughtexception.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/integrations/onuncaughtexception.ts"], "sourcesContent": ["import { captureException, debug, defineIntegration, getClient } from '@sentry/core';\nimport { DEBUG_BUILD } from '../debug-build';\nimport type { NodeClient } from '../sdk/client';\nimport { logAndExitProcess } from '../utils/errorhandling';\n\ntype OnFatalErrorHandler = (firstError: Error, secondError?: Error) => void;\n\ntype TaggedListener = NodeJS.UncaughtExceptionListener & {\n  tag?: string;\n};\n\ninterface OnUncaughtExceptionOptions {\n  /**\n   * Controls if the SDK should register a handler to exit the process on uncaught errors:\n   * - `true`: The SDK will exit the process on all uncaught errors.\n   * - `false`: The SDK will only exit the process when there are no other `uncaughtException` handlers attached.\n   *\n   * Default: `false`\n   */\n  exitEvenIfOtherHandlersAreRegistered: boolean;\n\n  /**\n   * This is called when an uncaught error would cause the process to exit.\n   *\n   * @param firstError Uncaught error causing the process to exit\n   * @param secondError Will be set if the handler was called multiple times. This can happen either because\n   * `onFatalError` itself threw, or because an independent error happened somewhere else while `onFatalError`\n   * was running.\n   */\n  onFatalError?(this: void, firstError: Error, secondError?: Error): void;\n}\n\nconst INTEGRATION_NAME = 'OnUncaughtException';\n\n/**\n * Add a global exception handler.\n */\nexport const onUncaughtExceptionIntegration = defineIntegration((options: Partial<OnUncaughtExceptionOptions> = {}) => {\n  const optionsWithDefaults = {\n    exitEvenIfOtherHandlersAreRegistered: false,\n    ...options,\n  };\n\n  return {\n    name: INTEGRATION_NAME,\n    setup(client: NodeClient) {\n      global.process.on('uncaughtException', makeErrorHandler(client, optionsWithDefaults));\n    },\n  };\n});\n\ntype ErrorHandler = { _errorHandler: boolean } & ((error: Error) => void);\n\n/** Exported only for tests */\nexport function makeErrorHandler(client: NodeClient, options: OnUncaughtExceptionOptions): ErrorHandler {\n  const timeout = 2000;\n  let caughtFirstError: boolean = false;\n  let caughtSecondError: boolean = false;\n  let calledFatalError: boolean = false;\n  let firstError: Error;\n\n  const clientOptions = client.getOptions();\n\n  return Object.assign(\n    (error: Error): void => {\n      let onFatalError: OnFatalErrorHandler = logAndExitProcess;\n\n      if (options.onFatalError) {\n        onFatalError = options.onFatalError;\n      } else if (clientOptions.onFatalError) {\n        onFatalError = clientOptions.onFatalError as OnFatalErrorHandler;\n      }\n\n      // Attaching a listener to `uncaughtException` will prevent the node process from exiting. We generally do not\n      // want to alter this behaviour so we check for other listeners that users may have attached themselves and adjust\n      // exit behaviour of the SDK accordingly:\n      // - If other listeners are attached, do not exit.\n      // - If the only listener attached is ours, exit.\n      const userProvidedListenersCount = (global.process.listeners('uncaughtException') as TaggedListener[]).filter(\n        listener => {\n          // There are 3 listeners we ignore:\n          return (\n            // as soon as we're using domains this listener is attached by node itself\n            listener.name !== 'domainUncaughtExceptionClear' &&\n            // the handler we register for tracing\n            listener.tag !== 'sentry_tracingErrorCallback' &&\n            // the handler we register in this integration\n            (listener as ErrorHandler)._errorHandler !== true\n          );\n        },\n      ).length;\n\n      const processWouldExit = userProvidedListenersCount === 0;\n      const shouldApplyFatalHandlingLogic = options.exitEvenIfOtherHandlersAreRegistered || processWouldExit;\n\n      if (!caughtFirstError) {\n        // this is the first uncaught error and the ultimate reason for shutting down\n        // we want to do absolutely everything possible to ensure it gets captured\n        // also we want to make sure we don't go recursion crazy if more errors happen after this one\n        firstError = error;\n        caughtFirstError = true;\n\n        if (getClient() === client) {\n          captureException(error, {\n            originalException: error,\n            captureContext: {\n              level: 'fatal',\n            },\n            mechanism: {\n              handled: false,\n              type: 'onuncaughtexception',\n            },\n          });\n        }\n\n        if (!calledFatalError && shouldApplyFatalHandlingLogic) {\n          calledFatalError = true;\n          onFatalError(error);\n        }\n      } else {\n        if (shouldApplyFatalHandlingLogic) {\n          if (calledFatalError) {\n            // we hit an error *after* calling onFatalError - pretty boned at this point, just shut it down\n            DEBUG_BUILD &&\n              debug.warn(\n                'uncaught exception after calling fatal error shutdown callback - this is bad! forcing shutdown',\n              );\n            logAndExitProcess(error);\n          } else if (!caughtSecondError) {\n            // two cases for how we can hit this branch:\n            //   - capturing of first error blew up and we just caught the exception from that\n            //     - quit trying to capture, proceed with shutdown\n            //   - a second independent error happened while waiting for first error to capture\n            //     - want to avoid causing premature shutdown before first error capture finishes\n            // it's hard to immediately tell case 1 from case 2 without doing some fancy/questionable domain stuff\n            // so let's instead just delay a bit before we proceed with our action here\n            // in case 1, we just wait a bit unnecessarily but ultimately do the same thing\n            // in case 2, the delay hopefully made us wait long enough for the capture to finish\n            // two potential nonideal outcomes:\n            //   nonideal case 1: capturing fails fast, we sit around for a few seconds unnecessarily before proceeding correctly by calling onFatalError\n            //   nonideal case 2: case 2 happens, 1st error is captured but slowly, timeout completes before capture and we treat second error as the sendErr of (nonexistent) failure from trying to capture first error\n            // note that after hitting this branch, we might catch more errors where (caughtSecondError && !calledFatalError)\n            //   we ignore them - they don't matter to us, we're just waiting for the second error timeout to finish\n            caughtSecondError = true;\n            setTimeout(() => {\n              if (!calledFatalError) {\n                // it was probably case 1, let's treat err as the sendErr and call onFatalError\n                calledFatalError = true;\n                onFatalError(firstError, error);\n              } else {\n                // it was probably case 2, our first error finished capturing while we waited, cool, do nothing\n              }\n            }, timeout); // capturing could take at least sendTimeout to fail, plus an arbitrary second for how long it takes to collect surrounding source etc\n          }\n        }\n      }\n    },\n    { _errorHandler: true },\n  );\n}\n"], "names": ["defineIntegration", "logAndExitProcess", "getClient", "captureException", "DEBUG_BUILD", "debug"], "mappings": ";;;;;;AAgCA,MAAM,gBAAA,GAAmB,qBAAqB;AAE9C;;CAEA,GACO,MAAM,8BAAA,GAAiCA,KAAAA,iBAAiB,CAAC,CAAC,OAAO,GAAwC,CAAA,CAAE,KAAK;IACrH,MAAM,sBAAsB;QAC1B,oCAAoC,EAAE,KAAK;QAC3C,GAAG,OAAO;IACd,CAAG;IAED,OAAO;QACL,IAAI,EAAE,gBAAgB;QACtB,KAAK,EAAC,MAAM,EAAc;YACxB,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,mBAAmB,EAAE,gBAAgB,CAAC,MAAM,EAAE,mBAAmB,CAAC,CAAC;QAC3F,CAAK;IACL,CAAG;AACH,CAAC;AAID,4BAAA,GACO,SAAS,gBAAgB,CAAC,MAAM,EAAc,OAAO,EAA4C;IACtG,MAAM,OAAA,GAAU,IAAI;IACpB,IAAI,gBAAgB,GAAY,KAAK;IACrC,IAAI,iBAAiB,GAAY,KAAK;IACtC,IAAI,gBAAgB,GAAY,KAAK;IACrC,IAAI,UAAU;IAEd,MAAM,aAAA,GAAgB,MAAM,CAAC,UAAU,EAAE;IAEzC,OAAO,MAAM,CAAC,MAAM,CAClB,CAAC,KAAK,KAAkB;QACtB,IAAI,YAAY,GAAwBC,cAAAA,iBAAiB;QAEzD,IAAI,OAAO,CAAC,YAAY,EAAE;YACxB,YAAA,GAAe,OAAO,CAAC,YAAY;QAC3C,OAAa,IAAI,aAAa,CAAC,YAAY,EAAE;YACrC,YAAA,GAAe,aAAa,CAAC,YAAA;QACrC;QAEA,8GAAA;QACA,kHAAA;QACA,yCAAA;QACA,kDAAA;QACA,iDAAA;QACM,MAAM,0BAAA,GAA6B,AAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,mBAAmB,CAAA,CAAuB,MAAM,EAC3G,YAAY;YACpB,mCAAA;YACU,OACV,0EAAA;YACY,QAAQ,CAAC,IAAA,KAAS,8BAAA,IAC9B,sCAAA;YACY,QAAQ,CAAC,GAAA,KAAQ,6BAAA,IAC7B,8CAAA;YACa,QAAA,CAA0B,aAAA,KAAkB;QAEzD,CAAS,EACD,MAAM;QAER,MAAM,gBAAA,GAAmB,0BAAA,KAA+B,CAAC;QACzD,MAAM,6BAAA,GAAgC,OAAO,CAAC,oCAAA,IAAwC,gBAAgB;QAEtG,IAAI,CAAC,gBAAgB,EAAE;YAC7B,6EAAA;YACA,0EAAA;YACA,6FAAA;YACQ,UAAA,GAAa,KAAK;YAClB,gBAAA,GAAmB,IAAI;YAEvB,IAAIC,KAAAA,SAAS,EAAC,KAAM,MAAM,EAAE;gBAC1BC,KAAAA,gBAAgB,CAAC,KAAK,EAAE;oBACtB,iBAAiB,EAAE,KAAK;oBACxB,cAAc,EAAE;wBACd,KAAK,EAAE,OAAO;oBAC5B,CAAa;oBACD,SAAS,EAAE;wBACT,OAAO,EAAE,KAAK;wBACd,IAAI,EAAE,qBAAqB;oBACzC,CAAa;gBACb,CAAW,CAAC;YACZ;YAEQ,IAAI,CAAC,gBAAA,IAAoB,6BAA6B,EAAE;gBACtD,gBAAA,GAAmB,IAAI;gBACvB,YAAY,CAAC,KAAK,CAAC;YAC7B;QACA,OAAa;YACL,IAAI,6BAA6B,EAAE;gBACjC,IAAI,gBAAgB,EAAE;oBAChC,+FAAA;oBACYC,WAAAA,WAAA,IACEC,KAAAA,KAAK,CAAC,IAAI,CACR,gGAAgG;oBAEpGJ,cAAAA,iBAAiB,CAAC,KAAK,CAAC;gBACpC,OAAiB,IAAI,CAAC,iBAAiB,EAAE;oBACzC,4CAAA;oBACA,kFAAA;oBACA,sDAAA;oBACA,mFAAA;oBACA,qFAAA;oBACA,sGAAA;oBACA,2EAAA;oBACA,+EAAA;oBACA,oFAAA;oBACA,mCAAA;oBACA,6IAAA;oBACA,6MAAA;oBACA,iHAAA;oBACA,wGAAA;oBACY,iBAAA,GAAoB,IAAI;oBACxB,UAAU,CAAC,MAAM;wBACf,IAAI,CAAC,gBAAgB,EAAE;4BACrC,+EAAA;4BACgB,gBAAA,GAAmB,IAAI;4BACvB,YAAY,CAAC,UAAU,EAAE,KAAK,CAAC;wBAC/C;oBAGA,CAAa,EAAE,OAAO,CAAC,CAAA,CAAA,sIAAA;gBACvB;YACA;QACA;IACA,CAAK,EACD;QAAE,aAAa,EAAE,IAAA;IAAA,CAAM;AAE3B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2759, "column": 0}, "map": {"version": 3, "file": "onunhandledrejection.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/integrations/onunhandledrejection.ts"], "sourcesContent": ["import type { Client, IntegrationFn, SeverityLevel, Span } from '@sentry/core';\nimport { captureException, consoleSandbox, defineIntegration, getClient, withActiveSpan } from '@sentry/core';\nimport { logAndExitProcess } from '../utils/errorhandling';\n\ntype UnhandledRejectionMode = 'none' | 'warn' | 'strict';\n\ninterface OnUnhandledRejectionOptions {\n  /**\n   * Option deciding what to do after capturing unhandledRejection,\n   * that mimicks behavior of node's --unhandled-rejection flag.\n   */\n  mode: UnhandledRejectionMode;\n}\n\nconst INTEGRATION_NAME = 'OnUnhandledRejection';\n\nconst _onUnhandledRejectionIntegration = ((options: Partial<OnUnhandledRejectionOptions> = {}) => {\n  const opts = {\n    mode: 'warn',\n    ...options,\n  } satisfies OnUnhandledRejectionOptions;\n\n  return {\n    name: INTEGRATION_NAME,\n    setup(client) {\n      global.process.on('unhandledRejection', makeUnhandledPromiseHandler(client, opts));\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Add a global promise rejection handler.\n */\nexport const onUnhandledRejectionIntegration = defineIntegration(_onUnhandledRejectionIntegration);\n\n/**\n * Send an exception with reason\n * @param reason string\n * @param promise promise\n *\n * Exported only for tests.\n */\nexport function makeUnhandledPromiseHandler(\n  client: Client,\n  options: OnUnhandledRejectionOptions,\n): (reason: unknown, promise: unknown) => void {\n  return function sendUnhandledPromise(reason: unknown, promise: unknown): void {\n    if (getClient() !== client) {\n      return;\n    }\n\n    const level: SeverityLevel = options.mode === 'strict' ? 'fatal' : 'error';\n\n    // this can be set in places where we cannot reliably get access to the active span/error\n    // when the error bubbles up to this handler, we can use this to set the active span\n    const activeSpanForError =\n      reason && typeof reason === 'object' ? (reason as { _sentry_active_span?: Span })._sentry_active_span : undefined;\n\n    const activeSpanWrapper = activeSpanForError\n      ? (fn: () => void) => withActiveSpan(activeSpanForError, fn)\n      : (fn: () => void) => fn();\n\n    activeSpanWrapper(() => {\n      captureException(reason, {\n        originalException: promise,\n        captureContext: {\n          extra: { unhandledPromiseRejection: true },\n          level,\n        },\n        mechanism: {\n          handled: false,\n          type: 'onunhandledrejection',\n        },\n      });\n    });\n\n    handleRejection(reason, options.mode);\n  };\n}\n\n/**\n * Handler for `mode` option\n */\nfunction handleRejection(reason: unknown, mode: UnhandledRejectionMode): void {\n  // https://github.com/nodejs/node/blob/7cf6f9e964aa00772965391c23acda6d71972a9a/lib/internal/process/promises.js#L234-L240\n  const rejectionWarning =\n    'This error originated either by ' +\n    'throwing inside of an async function without a catch block, ' +\n    'or by rejecting a promise which was not handled with .catch().' +\n    ' The promise rejected with the reason:';\n\n  /* eslint-disable no-console */\n  if (mode === 'warn') {\n    consoleSandbox(() => {\n      console.warn(rejectionWarning);\n      console.error(reason && typeof reason === 'object' && 'stack' in reason ? reason.stack : reason);\n    });\n  } else if (mode === 'strict') {\n    consoleSandbox(() => {\n      console.warn(rejectionWarning);\n    });\n    logAndExitProcess(reason);\n  }\n  /* eslint-enable no-console */\n}\n"], "names": ["defineIntegration", "getClient", "withActiveSpan", "captureException", "consoleSandbox", "logAndExitProcess"], "mappings": ";;;;;AAcA,MAAM,gBAAA,GAAmB,sBAAsB;AAE/C,MAAM,gCAAA,GAAoC,CAAC,OAAO,GAAyC,CAAA,CAAE,KAAK;IAChG,MAAM,OAAO;QACX,IAAI,EAAE,MAAM;QACZ,GAAG,OAAO;IACd,CAAE;IAEA,OAAO;QACL,IAAI,EAAE,gBAAgB;QACtB,KAAK,EAAC,MAAM,EAAE;YACZ,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,oBAAoB,EAAE,2BAA2B,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACxF,CAAK;IACL,CAAG;AACH,CAAC,CAAA;AAED;;CAEA,SACa,+BAAA,GAAkCA,KAAAA,iBAAiB,CAAC,gCAAgC;AAEjG;;;;;;CAMA,GACO,SAAS,2BAA2B,CACzC,MAAM,EACN,OAAO;IAEP,OAAO,SAAS,oBAAoB,CAAC,MAAM,EAAW,OAAO,EAAiB;QAC5E,IAAIC,KAAAA,SAAS,EAAC,KAAM,MAAM,EAAE;YAC1B;QACN;QAEI,MAAM,KAAK,GAAkB,OAAO,CAAC,IAAA,KAAS,QAAA,GAAW,OAAA,GAAU,OAAO;QAE9E,yFAAA;QACA,oFAAA;QACI,MAAM,kBAAA,GACJ,MAAA,IAAU,OAAO,MAAA,KAAW,QAAA,GAAW,AAAC,MAAA,CAA0C,mBAAA,GAAsB,SAAS;QAEnH,MAAM,oBAAoB,qBACtB,CAAC,EAAE,GAAiBC,KAAAA,cAAc,CAAC,kBAAkB,EAAE,EAAE,IACzD,CAAC,EAAE,GAAiB,EAAE,EAAE;QAE5B,iBAAiB,CAAC,MAAM;YACtBC,KAAAA,gBAAgB,CAAC,MAAM,EAAE;gBACvB,iBAAiB,EAAE,OAAO;gBAC1B,cAAc,EAAE;oBACd,KAAK,EAAE;wBAAE,yBAAyB,EAAE;oBAAA,CAAM;oBAC1C,KAAK;gBACf,CAAS;gBACD,SAAS,EAAE;oBACT,OAAO,EAAE,KAAK;oBACd,IAAI,EAAE,sBAAsB;gBACtC,CAAS;YACT,CAAO,CAAC;QACR,CAAK,CAAC;QAEF,eAAe,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC;IACzC,CAAG;AACH;AAEA;;CAEA,GACA,SAAS,eAAe,CAAC,MAAM,EAAW,IAAI,EAAgC;IAC9E,0HAAA;IACE,MAAM,gBAAA,GACJ,kCAAA,GACA,8DAAA,GACA,gEAAA,GACA,wCAAwC;IAE5C,6BAAA,GACE,IAAI,IAAA,KAAS,MAAM,EAAE;QACnBC,KAAAA,cAAc,CAAC,MAAM;YACnB,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC;YAC9B,OAAO,CAAC,KAAK,CAAC,UAAU,OAAO,WAAW,QAAA,IAAY,OAAA,IAAW,SAAS,MAAM,CAAC,KAAA,GAAQ,MAAM,CAAC;QACtG,CAAK,CAAC;IACN,OAAS,IAAI,IAAA,KAAS,QAAQ,EAAE;QAC5BA,KAAAA,cAAc,CAAC,MAAM;YACnB,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC;QACpC,CAAK,CAAC;QACFC,cAAAA,iBAAiB,CAAC,MAAM,CAAC;IAC7B;AACA,4BAAA,GACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2838, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/integrations/anr/index.ts"], "sourcesContent": ["import { types } from 'node:util';\nimport { Worker } from 'node:worker_threads';\nimport type { Contexts, Event, EventHint, Integration, IntegrationFn, ScopeData } from '@sentry/core';\nimport {\n  debug,\n  defineIntegration,\n  getClient,\n  getCurrentScope,\n  getFilenameToDebugIdMap,\n  getGlobalScope,\n  getIsolationScope,\n  GLOBAL_OBJ,\n  mergeScopeData,\n} from '@sentry/core';\nimport { NODE_VERSION } from '../../nodeVersion';\nimport type { NodeClient } from '../../sdk/client';\nimport { isDebuggerEnabled } from '../../utils/debug';\nimport type { AnrIntegrationOptions, WorkerStartData } from './common';\n\nconst { isPromise } = types;\n\n// This string is a placeholder that gets overwritten with the worker code.\nexport const base64WorkerScript = '###AnrWorkerScript###';\n\nconst DEFAULT_INTERVAL = 50;\nconst DEFAULT_HANG_THRESHOLD = 5000;\n\nfunction log(message: string, ...args: unknown[]): void {\n  debug.log(`[ANR] ${message}`, ...args);\n}\n\nfunction globalWithScopeFetchFn(): typeof GLOBAL_OBJ & { __SENTRY_GET_SCOPES__?: () => ScopeData } {\n  return GLOBAL_OBJ;\n}\n\n/** Fetches merged scope data */\nfunction getScopeData(): ScopeData {\n  const scope = getGlobalScope().getScopeData();\n  mergeScopeData(scope, getIsolationScope().getScopeData());\n  mergeScopeData(scope, getCurrentScope().getScopeData());\n\n  // We remove attachments because they likely won't serialize well as json\n  scope.attachments = [];\n  // We can't serialize event processor functions\n  scope.eventProcessors = [];\n\n  return scope;\n}\n\n/**\n * Gets contexts by calling all event processors. This shouldn't be called until all integrations are setup\n */\nasync function getContexts(client: NodeClient): Promise<Contexts> {\n  let event: Event | null = { message: 'ANR' };\n  const eventHint: EventHint = {};\n\n  for (const processor of client.getEventProcessors()) {\n    if (event === null) break;\n    event = await processor(event, eventHint);\n  }\n\n  return event?.contexts || {};\n}\n\nconst INTEGRATION_NAME = 'Anr';\n\ntype AnrInternal = { startWorker: () => void; stopWorker: () => void };\n\n// eslint-disable-next-line deprecation/deprecation\nconst _anrIntegration = ((options: Partial<AnrIntegrationOptions> = {}) => {\n  if (NODE_VERSION.major < 16 || (NODE_VERSION.major === 16 && NODE_VERSION.minor < 17)) {\n    throw new Error('ANR detection requires Node 16.17.0 or later');\n  }\n\n  let worker: Promise<() => void> | undefined;\n  let client: NodeClient | undefined;\n\n  // Hookup the scope fetch function to the global object so that it can be called from the worker thread via the\n  // debugger when it pauses\n  const gbl = globalWithScopeFetchFn();\n  gbl.__SENTRY_GET_SCOPES__ = getScopeData;\n\n  return {\n    name: INTEGRATION_NAME,\n    startWorker: () => {\n      if (worker) {\n        return;\n      }\n\n      if (client) {\n        worker = _startWorker(client, options);\n      }\n    },\n    stopWorker: () => {\n      if (worker) {\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\n        worker.then(stop => {\n          stop();\n          worker = undefined;\n        });\n      }\n    },\n    async setup(initClient: NodeClient) {\n      client = initClient;\n\n      if (options.captureStackTrace && (await isDebuggerEnabled())) {\n        debug.warn('ANR captureStackTrace has been disabled because the debugger was already enabled');\n        options.captureStackTrace = false;\n      }\n\n      // setImmediate is used to ensure that all other integrations have had their setup called first.\n      // This allows us to call into all integrations to fetch the full context\n      setImmediate(() => this.startWorker());\n    },\n  } as Integration & AnrInternal;\n}) satisfies IntegrationFn;\n\n// eslint-disable-next-line deprecation/deprecation\ntype AnrReturn = (options?: Partial<AnrIntegrationOptions>) => Integration & AnrInternal;\n\n/**\n * Application Not Responding (ANR) integration for Node.js applications.\n *\n * @deprecated The ANR integration has been deprecated. Use `eventLoopBlockIntegration` from `@sentry/node-native` instead.\n *\n * Detects when the Node.js main thread event loop is blocked for more than the configured\n * threshold (5 seconds by default) and reports these as Sentry events.\n *\n * ANR detection uses a worker thread to monitor the event loop in the main app thread.\n * The main app thread sends a heartbeat message to the ANR worker thread every 50ms by default.\n * If the ANR worker does not receive a heartbeat message for the configured threshold duration,\n * it triggers an ANR event.\n *\n * - Node.js 16.17.0 or higher\n * - Only supported in the Node.js runtime (not browsers)\n * - Not supported for Node.js clusters\n *\n * Overhead should be minimal:\n * - Main thread: Only polling the ANR worker over IPC every 50ms\n * - Worker thread: Consumes around 10-20 MB of RAM\n * - When ANR detected: Brief pause in debugger to capture stack trace (negligible compared to the blocking)\n *\n * @example\n * ```javascript\n * Sentry.init({\n *   dsn: \"https://<EMAIL>/0\",\n *   integrations: [\n *     Sentry.anrIntegration({\n *       anrThreshold: 5000,\n *       captureStackTrace: true,\n *       pollInterval: 50,\n *     }),\n *   ],\n * });\n * ```\n */\nexport const anrIntegration = defineIntegration(_anrIntegration) as AnrReturn;\n\n/**\n * Starts the ANR worker thread\n *\n * @returns A function to stop the worker\n */\nasync function _startWorker(\n  client: NodeClient,\n  // eslint-disable-next-line deprecation/deprecation\n  integrationOptions: Partial<AnrIntegrationOptions>,\n): Promise<() => void> {\n  const dsn = client.getDsn();\n\n  if (!dsn) {\n    return () => {\n      //\n    };\n  }\n\n  const contexts = await getContexts(client);\n\n  // These will not be accurate if sent later from the worker thread\n  delete contexts.app?.app_memory;\n  delete contexts.device?.free_memory;\n\n  const initOptions = client.getOptions();\n\n  const sdkMetadata = client.getSdkMetadata() || {};\n  if (sdkMetadata.sdk) {\n    sdkMetadata.sdk.integrations = initOptions.integrations.map(i => i.name);\n  }\n\n  const options: WorkerStartData = {\n    debug: debug.isEnabled(),\n    dsn,\n    tunnel: initOptions.tunnel,\n    environment: initOptions.environment || 'production',\n    release: initOptions.release,\n    dist: initOptions.dist,\n    sdkMetadata,\n    appRootPath: integrationOptions.appRootPath,\n    pollInterval: integrationOptions.pollInterval || DEFAULT_INTERVAL,\n    anrThreshold: integrationOptions.anrThreshold || DEFAULT_HANG_THRESHOLD,\n    captureStackTrace: !!integrationOptions.captureStackTrace,\n    maxAnrEvents: integrationOptions.maxAnrEvents || 1,\n    staticTags: integrationOptions.staticTags || {},\n    contexts,\n  };\n\n  if (options.captureStackTrace) {\n    const inspector = await import('node:inspector');\n    if (!inspector.url()) {\n      inspector.open(0);\n    }\n  }\n\n  const worker = new Worker(new URL(`data:application/javascript;base64,${base64WorkerScript}`), {\n    workerData: options,\n    // We don't want any Node args to be passed to the worker\n    execArgv: [],\n    env: { ...process.env, NODE_OPTIONS: undefined },\n  });\n\n  process.on('exit', () => {\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    worker.terminate();\n  });\n\n  const timer = setInterval(() => {\n    try {\n      const currentSession = getIsolationScope().getSession();\n      // We need to copy the session object and remove the toJSON method so it can be sent to the worker\n      // serialized without making it a SerializedSession\n      const session = currentSession ? { ...currentSession, toJSON: undefined } : undefined;\n      // message the worker to tell it the main event loop is still running\n      worker.postMessage({ session, debugImages: getFilenameToDebugIdMap(initOptions.stackParser) });\n    } catch {\n      //\n    }\n  }, options.pollInterval);\n  // Timer should not block exit\n  timer.unref();\n\n  worker.on('message', (msg: string) => {\n    if (msg === 'session-ended') {\n      log('ANR event sent from ANR worker. Clearing session in this thread.');\n      getIsolationScope().setSession(undefined);\n    }\n  });\n\n  worker.once('error', (err: Error) => {\n    clearInterval(timer);\n    log('ANR worker error', err);\n  });\n\n  worker.once('exit', (code: number) => {\n    clearInterval(timer);\n    log('ANR worker exit', code);\n  });\n\n  // Ensure this thread can't block app exit\n  worker.unref();\n\n  return () => {\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    worker.terminate();\n    clearInterval(timer);\n  };\n}\n\nexport function disableAnrDetectionForCallback<T>(callback: () => T): T;\nexport function disableAnrDetectionForCallback<T>(callback: () => Promise<T>): Promise<T>;\n/**\n * Temporarily disables ANR detection for the duration of a callback function.\n *\n * This utility function allows you to disable ANR detection during operations that\n * are expected to block the event loop, such as intensive computational tasks or\n * synchronous I/O operations.\n *\n * @deprecated The ANR integration has been deprecated. Use `eventLoopBlockIntegration` from `@sentry/node-native` instead.\n */\nexport function disableAnrDetectionForCallback<T>(callback: () => T | Promise<T>): T | Promise<T> {\n  const integration = getClient()?.getIntegrationByName(INTEGRATION_NAME) as AnrInternal | undefined;\n\n  if (!integration) {\n    return callback();\n  }\n\n  integration.stopWorker();\n\n  const result = callback();\n  if (isPromise(result)) {\n    return result.finally(() => integration.startWorker());\n  }\n\n  integration.startWorker();\n  return result;\n}\n"], "names": ["types", "debug", "GLOBAL_OBJ", "getGlobalScope", "mergeScopeData", "getIsolationScope", "getCurrentScope", "NODE_VERSION", "isDebuggerEnabled", "defineIntegration", "Worker", "getFilenameToDebugIdMap", "getClient"], "mappings": ";;;;;;;;AAmBA,MAAM,EAAE,SAAA,EAAU,GAAIA,UAAAA,KAAK;AAE3B,2EAAA;AACO,MAAM,kBAAA,GAAqB;AAElC,MAAM,gBAAA,GAAmB,EAAE;AAC3B,MAAM,sBAAA,GAAyB,IAAI;AAEnC,SAAS,GAAG,CAAC,OAAO,EAAU,GAAG,IAAI,EAAmB;IACtDC,KAAAA,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA,EAAA,GAAA,IAAA,CAAA;AACA;AAEA,SAAA,sBAAA,GAAA;IACA,OAAAC,KAAAA,UAAA;AACA;AAEA,8BAAA,GACA,SAAA,YAAA,GAAA;IACA,MAAA,KAAA,GAAAC,KAAAA,cAAA,EAAA,CAAA,YAAA,EAAA;IACAC,KAAAA,cAAA,CAAA,KAAA,EAAAC,KAAAA,iBAAA,EAAA,CAAA,YAAA,EAAA,CAAA;IACAD,KAAAA,cAAA,CAAA,KAAA,EAAAE,KAAAA,eAAA,EAAA,CAAA,YAAA,EAAA,CAAA;IAEA,yEAAA;IACA,KAAA,CAAA,WAAA,GAAA,EAAA;IACA,+CAAA;IACA,KAAA,CAAA,eAAA,GAAA,EAAA;IAEA,OAAA,KAAA;AACA;AAEA;;CAEA,GACA,eAAA,WAAA,CAAA,MAAA,EAAA;IACA,IAAA,KAAA,GAAA;QAAA,OAAA,EAAA,KAAA;IAAA,CAAA;IACA,MAAA,SAAA,GAAA,CAAA,CAAA;IAEA,KAAA,MAAA,SAAA,IAAA,MAAA,CAAA,kBAAA,EAAA,CAAA;QACA,IAAA,KAAA,KAAA,IAAA,EAAA;QACA,KAAA,GAAA,MAAA,SAAA,CAAA,KAAA,EAAA,SAAA,CAAA;IACA;IAEA,OAAA,KAAA,EAAA,QAAA,IAAA,CAAA,CAAA;AACA;AAEA,MAAA,gBAAA,GAAA,KAAA;AAIA,mDAAA;AACA,MAAA,eAAA,GAAA,CAAA,OAAA,GAAA,CAAA,CAAA,KAAA;IACA,IAAAC,YAAAA,YAAA,CAAA,KAAA,GAAA,EAAA,IAAAA,YAAAA,YAAA,CAAA,KAAA,KAAA,EAAA,IAAAA,YAAAA,YAAA,CAAA,KAAA,GAAA,EAAA,CAAA,CAAA;QACA,MAAA,IAAA,KAAA,CAAA,8CAAA,CAAA;IACA;IAEA,IAAA,MAAA;IACA,IAAA,MAAA;IAEA,+GAAA;IACA,0BAAA;IACA,MAAA,GAAA,GAAA,sBAAA,EAAA;IACA,GAAA,CAAA,qBAAA,GAAA,YAAA;IAEA,OAAA;QACA,IAAA,EAAA,gBAAA;QACA,WAAA,EAAA,MAAA;YACA,IAAA,MAAA,EAAA;gBACA;YACA;YAEA,IAAA,MAAA,EAAA;gBACA,MAAA,GAAA,YAAA,CAAA,MAAA,EAAA,OAAA,CAAA;YACA;QACA,CAAA;QACA,UAAA,EAAA,MAAA;YACA,IAAA,MAAA,EAAA;gBACA,mEAAA;gBACA,MAAA,CAAA,IAAA,EAAA,IAAA,IAAA;oBACA,IAAA,EAAA;oBACA,MAAA,GAAA,SAAA;gBACA,CAAA,CAAA;YACA;QACA,CAAA;QACA,MAAA,KAAA,EAAA,UAAA,EAAA;YACA,MAAA,GAAA,UAAA;YAEA,IAAA,OAAA,CAAA,iBAAA,IAAA,MAAAC,MAAAA,iBAAA,EAAA,CAAA,CAAA;gBACAP,KAAAA,KAAA,CAAA,IAAA,CAAA,kFAAA,CAAA;gBACA,OAAA,CAAA,iBAAA,GAAA,KAAA;YACA;YAEA,gGAAA;YACA,yEAAA;YACA,YAAA,CAAA,IAAA,IAAA,CAAA,WAAA,EAAA,CAAA;QACA,CAAA;IACA,CAAA;AACA,CAAA,CAAA;AAEA,mDAAA;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAmCA,GACA,MAAA,cAAA,GAAAQ,KAAAA,iBAAA,CAAA,eAAA,CAAA;AAEA;;;;CAIA,GACA,eAAA,YAAA,CACA,MAAA,EACA,mDAAA;AACA,kBAAA;IAEA,MAAA,GAAA,GAAA,MAAA,CAAA,MAAA,EAAA;IAEA,IAAA,CAAA,GAAA,EAAA;QACA,OAAA,MAAA;QACA,EAAA;QACA,CAAA;IACA;IAEA,MAAA,QAAA,GAAA,MAAA,WAAA,CAAA,MAAA,CAAA;IAEA,kEAAA;IACA,OAAA,QAAA,CAAA,GAAA,EAAA,UAAA;IACA,OAAA,QAAA,CAAA,MAAA,EAAA,WAAA;IAEA,MAAA,WAAA,GAAA,MAAA,CAAA,UAAA,EAAA;IAEA,MAAA,WAAA,GAAA,MAAA,CAAA,cAAA,EAAA,IAAA,CAAA,CAAA;IACA,IAAA,WAAA,CAAA,GAAA,EAAA;QACA,WAAA,CAAA,GAAA,CAAA,YAAA,GAAA,WAAA,CAAA,YAAA,CAAA,GAAA,EAAA,CAAA,GAAA,CAAA,CAAA,IAAA,CAAA;IACA;IAEA,MAAA,OAAA,GAAA;QACA,KAAA,EAAAR,KAAAA,KAAA,CAAA,SAAA,EAAA;QACA,GAAA;QACA,MAAA,EAAA,WAAA,CAAA,MAAA;QACA,WAAA,EAAA,WAAA,CAAA,WAAA,IAAA,YAAA;QACA,OAAA,EAAA,WAAA,CAAA,OAAA;QACA,IAAA,EAAA,WAAA,CAAA,IAAA;QACA,WAAA;QACA,WAAA,EAAA,kBAAA,CAAA,WAAA;QACA,YAAA,EAAA,kBAAA,CAAA,YAAA,IAAA,gBAAA;QACA,YAAA,EAAA,kBAAA,CAAA,YAAA,IAAA,sBAAA;QACA,iBAAA,EAAA,CAAA,CAAA,kBAAA,CAAA,iBAAA;QACA,YAAA,EAAA,kBAAA,CAAA,YAAA,IAAA,CAAA;QACA,UAAA,EAAA,kBAAA,CAAA,UAAA,IAAA,CAAA,CAAA;QACA,QAAA;IACA,CAAA;IAEA,IAAA,OAAA,CAAA,iBAAA,EAAA;QACA,MAAA,SAAA,GAAA,MAAA,OAAA,gBAAA,CAAA;QACA,IAAA,CAAA,SAAA,CAAA,GAAA,EAAA,EAAA;YACA,SAAA,CAAA,IAAA,CAAA,CAAA,CAAA;QACA;IACA;IAEA,MAAA,MAAA,GAAA,IAAAS,oBAAAA,MAAA,CAAA,IAAA,GAAA,CAAA,CAAA,mCAAA,EAAA,kBAAA,CAAA,CAAA,CAAA,EAAA;QACA,UAAA,EAAA,OAAA;QACA,yDAAA;QACA,QAAA,EAAA,EAAA;QACA,GAAA,EAAA;YAAA,GAAA,OAAA,CAAA,GAAA;YAAA,YAAA,EAAA,SAAA;QAAA,CAAA;IACA,CAAA,CAAA;IAEA,OAAA,CAAA,EAAA,CAAA,MAAA,EAAA,MAAA;QACA,mEAAA;QACA,MAAA,CAAA,SAAA,EAAA;IACA,CAAA,CAAA;IAEA,MAAA,KAAA,GAAA,WAAA,CAAA,MAAA;QACA,IAAA;YACA,MAAA,cAAA,GAAAL,KAAAA,iBAAA,EAAA,CAAA,UAAA,EAAA;YACA,kGAAA;YACA,mDAAA;YACA,MAAA,OAAA,GAAA,cAAA,GAAA;gBAAA,GAAA,cAAA;gBAAA,MAAA,EAAA,SAAA;YAAA,CAAA,GAAA,SAAA;YACA,qEAAA;YACA,MAAA,CAAA,WAAA,CAAA;gBAAA,OAAA;gBAAA,WAAA,EAAAM,KAAAA,uBAAA,CAAA,WAAA,CAAA,WAAA,CAAA;YAAA,CAAA,CAAA;QACA,CAAA,CAAA,OAAA;QACA,EAAA;QACA;IACA,CAAA,EAAA,OAAA,CAAA,YAAA,CAAA;IACA,8BAAA;IACA,KAAA,CAAA,KAAA,EAAA;IAEA,MAAA,CAAA,EAAA,CAAA,SAAA,EAAA,CAAA,GAAA,KAAA;QACA,IAAA,GAAA,KAAA,eAAA,EAAA;YACA,GAAA,CAAA,kEAAA,CAAA;YACAN,KAAAA,iBAAA,EAAA,CAAA,UAAA,CAAA,SAAA,CAAA;QACA;IACA,CAAA,CAAA;IAEA,MAAA,CAAA,IAAA,CAAA,OAAA,EAAA,CAAA,GAAA,KAAA;QACA,aAAA,CAAA,KAAA,CAAA;QACA,GAAA,CAAA,kBAAA,EAAA,GAAA,CAAA;IACA,CAAA,CAAA;IAEA,MAAA,CAAA,IAAA,CAAA,MAAA,EAAA,CAAA,IAAA,KAAA;QACA,aAAA,CAAA,KAAA,CAAA;QACA,GAAA,CAAA,iBAAA,EAAA,IAAA,CAAA;IACA,CAAA,CAAA;IAEA,0CAAA;IACA,MAAA,CAAA,KAAA,EAAA;IAEA,OAAA,MAAA;QACA,mEAAA;QACA,MAAA,CAAA,SAAA,EAAA;QACA,aAAA,CAAA,KAAA,CAAA;IACA,CAAA;AACA;AAIA;;;;;;;;CAQA,GACA,SAAA,8BAAA,CAAA,QAAA,EAAA;IACA,MAAA,WAAA,GAAAO,KAAAA,SAAA,EAAA,EAAA,oBAAA,CAAA,gBAAA,CAAA;IAEA,IAAA,CAAA,WAAA,EAAA;QACA,OAAA,QAAA,EAAA;IACA;IAEA,WAAA,CAAA,UAAA,EAAA;IAEA,MAAA,MAAA,GAAA,QAAA,EAAA;IACA,IAAA,SAAA,CAAA,MAAA,CAAA,EAAA;QACA,OAAA,MAAA,CAAA,OAAA,CAAA,IAAA,WAAA,CAAA,WAAA,EAAA,CAAA;IACA;IAEA,WAAA,CAAA,WAAA,EAAA;IACA,OAAA,MAAA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3087, "column": 0}, "map": {"version": 3, "file": "spotlight.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/integrations/spotlight.ts"], "sourcesContent": ["import * as http from 'node:http';\nimport type { Client, Envelope, IntegrationFn } from '@sentry/core';\nimport { debug, defineIntegration, serializeEnvelope, suppressTracing } from '@sentry/core';\n\ntype SpotlightConnectionOptions = {\n  /**\n   * Set this if the Spotlight Sidecar is not running on localhost:8969\n   * By default, the Url is set to http://localhost:8969/stream\n   */\n  sidecarUrl?: string;\n};\n\nexport const INTEGRATION_NAME = 'Spotlight';\n\nconst _spotlightIntegration = ((options: Partial<SpotlightConnectionOptions> = {}) => {\n  const _options = {\n    sidecarUrl: options.sidecarUrl || 'http://localhost:8969/stream',\n  };\n\n  return {\n    name: INTEGRATION_NAME,\n    setup(client) {\n      try {\n        if (process.env.NODE_ENV && process.env.NODE_ENV !== 'development') {\n          debug.warn(\"[Spotlight] It seems you're not in dev mode. Do you really want to have Spotlight enabled?\");\n        }\n      } catch {\n        // ignore\n      }\n      connectToSpotlight(client, _options);\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Use this integration to send errors and transactions to Spotlight.\n *\n * Learn more about spotlight at https://spotlightjs.com\n *\n * Important: This integration only works with Node 18 or newer.\n */\nexport const spotlightIntegration = defineIntegration(_spotlightIntegration);\n\nfunction connectToSpotlight(client: Client, options: Required<SpotlightConnectionOptions>): void {\n  const spotlightUrl = parseSidecarUrl(options.sidecarUrl);\n  if (!spotlightUrl) {\n    return;\n  }\n\n  let failedRequests = 0;\n\n  client.on('beforeEnvelope', (envelope: Envelope) => {\n    if (failedRequests > 3) {\n      debug.warn('[Spotlight] Disabled Sentry -> Spotlight integration due to too many failed requests');\n      return;\n    }\n\n    const serializedEnvelope = serializeEnvelope(envelope);\n    suppressTracing(() => {\n      const req = http.request(\n        {\n          method: 'POST',\n          path: spotlightUrl.pathname,\n          hostname: spotlightUrl.hostname,\n          port: spotlightUrl.port,\n          headers: {\n            'Content-Type': 'application/x-sentry-envelope',\n          },\n        },\n        res => {\n          if (res.statusCode && res.statusCode >= 200 && res.statusCode < 400) {\n            // Reset failed requests counter on success\n            failedRequests = 0;\n          }\n          res.on('data', () => {\n            // Drain socket\n          });\n\n          res.on('end', () => {\n            // Drain socket\n          });\n          res.setEncoding('utf8');\n        },\n      );\n\n      req.on('error', () => {\n        failedRequests++;\n        debug.warn('[Spotlight] Failed to send envelope to Spotlight Sidecar');\n      });\n      req.write(serializedEnvelope);\n      req.end();\n    });\n  });\n}\n\nfunction parseSidecarUrl(url: string): URL | undefined {\n  try {\n    return new URL(`${url}`);\n  } catch {\n    debug.warn(`[Spotlight] Invalid sidecar URL: ${url}`);\n    return undefined;\n  }\n}\n"], "names": ["debug", "defineIntegration", "serializeEnvelope", "suppressTracing"], "mappings": ";;;;;AAYO,MAAM,gBAAA,GAAmB;AAEhC,MAAM,qBAAA,GAAyB,CAAC,OAAO,GAAwC,CAAA,CAAE,KAAK;IACpF,MAAM,WAAW;QACf,UAAU,EAAE,OAAO,CAAC,UAAA,IAAc,8BAA8B;IACpE,CAAG;IAED,OAAO;QACL,IAAI,EAAE,gBAAgB;QACtB,KAAK,EAAC,MAAM,EAAE;YACZ,IAAI;gBACF,IAAI,OAAO,CAAC,GAAG,CAAC,QAAA,IAAY,OAAO,CAAC,GAAG,AAA6B,CAA5B,QAAA,KAAa,aAAa;;gBAE1E;YACA,EAAQ,OAAM;YACd,SAAA;YACA;YACM,kBAAkB,CAAC,MAAM,EAAE,QAAQ,CAAC;QAC1C,CAAK;IACL,CAAG;AACH,CAAC,CAAA;AAED;;;;;;CAMA,SACa,oBAAA,GAAuBC,KAAAA,iBAAiB,CAAC,qBAAqB;AAE3E,SAAS,kBAAkB,CAAC,MAAM,EAAU,OAAO,EAA8C;IAC/F,MAAM,eAAe,eAAe,CAAC,OAAO,CAAC,UAAU,CAAC;IACxD,IAAI,CAAC,YAAY,EAAE;QACjB;IACJ;IAEE,IAAI,cAAA,GAAiB,CAAC;IAEtB,MAAM,CAAC,EAAE,CAAC,gBAAgB,EAAE,CAAC,QAAQ,KAAe;QAClD,IAAI,cAAA,GAAiB,CAAC,EAAE;YACtBD,KAAAA,KAAK,CAAC,IAAI,CAAC,sFAAsF,CAAC;YAClG;QACN;QAEI,MAAM,kBAAA,GAAqBE,KAAAA,iBAAiB,CAAC,QAAQ,CAAC;QACtDC,KAAAA,eAAe,CAAC,MAAM;YACpB,MAAM,GAAA,GAAM,IAAI,CAAC,OAAO,CACtB;gBACE,MAAM,EAAE,MAAM;gBACd,IAAI,EAAE,YAAY,CAAC,QAAQ;gBAC3B,QAAQ,EAAE,YAAY,CAAC,QAAQ;gBAC/B,IAAI,EAAE,YAAY,CAAC,IAAI;gBACvB,OAAO,EAAE;oBACP,cAAc,EAAE,+BAA+B;gBAC3D,CAAW;YACX,CAAS,GACD,OAAO;gBACL,IAAI,GAAG,CAAC,UAAA,IAAc,GAAG,CAAC,UAAA,IAAc,OAAO,GAAG,CAAC,UAAA,GAAa,GAAG,EAAE;oBAC/E,2CAAA;oBACY,cAAA,GAAiB,CAAC;gBAC9B;gBACU,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM;gBAC/B,eAAA;gBACA,CAAW,CAAC;gBAEF,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM;gBAC9B,eAAA;gBACA,CAAW,CAAC;gBACF,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC;YACjC,CAAS;YAGH,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM;gBACpB,cAAc,EAAE;gBAChBH,KAAAA,KAAK,CAAC,IAAI,CAAC,0DAA0D,CAAC;YAC9E,CAAO,CAAC;YACF,GAAG,CAAC,KAAK,CAAC,kBAAkB,CAAC;YAC7B,GAAG,CAAC,GAAG,EAAE;QACf,CAAK,CAAC;IACN,CAAG,CAAC;AACJ;AAEA,SAAS,eAAe,CAAC,GAAG,EAA2B;IACrD,IAAI;QACF,OAAO,IAAI,GAAG,CAAC,CAAC,EAAA,GAAA,CAAA,CAAA,CAAA;IACA,CAAA,CAAA,OAAA;QACAA,KAAAA,KAAA,CAAA,IAAA,CAAA,CAAA,iCAAA,EAAA,GAAA,CAAA,CAAA,CAAA;QACA,OAAA,SAAA;IACA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3176, "column": 0}, "map": {"version": 3, "file": "childProcess.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/integrations/childProcess.ts"], "sourcesContent": ["import type { ChildProcess } from 'node:child_process';\nimport * as diagnosticsChannel from 'node:diagnostics_channel';\nimport type { Worker } from 'node:worker_threads';\nimport { addBreadcrumb, captureException, defineIntegration } from '@sentry/core';\n\ninterface Options {\n  /**\n   * Whether to include child process arguments in breadcrumbs data.\n   *\n   * @default false\n   */\n  includeChildProcessArgs?: boolean;\n\n  /**\n   * Whether to capture errors from worker threads.\n   *\n   * @default true\n   */\n  captureWorkerErrors?: boolean;\n}\n\nconst INTEGRATION_NAME = 'ChildProcess';\n\n/**\n * Capture breadcrumbs and events for child processes and worker threads.\n */\nexport const childProcessIntegration = defineIntegration((options: Options = {}) => {\n  return {\n    name: INTEGRATION_NAME,\n    setup() {\n      diagnosticsChannel.channel('child_process').subscribe((event: unknown) => {\n        if (event && typeof event === 'object' && 'process' in event) {\n          captureChildProcessEvents(event.process as ChildProcess, options);\n        }\n      });\n\n      diagnosticsChannel.channel('worker_threads').subscribe((event: unknown) => {\n        if (event && typeof event === 'object' && 'worker' in event) {\n          captureWorkerThreadEvents(event.worker as Worker, options);\n        }\n      });\n    },\n  };\n});\n\nfunction captureChildProcessEvents(child: ChildProcess, options: Options): void {\n  let hasExited = false;\n  let data: Record<string, unknown> | undefined;\n\n  child\n    .on('spawn', () => {\n      // This is Sentry getting macOS OS context\n      if (child.spawnfile === '/usr/bin/sw_vers') {\n        hasExited = true;\n        return;\n      }\n\n      data = { spawnfile: child.spawnfile };\n      if (options.includeChildProcessArgs) {\n        data.spawnargs = child.spawnargs;\n      }\n    })\n    .on('exit', code => {\n      if (!hasExited) {\n        hasExited = true;\n\n        // Only log for non-zero exit codes\n        if (code !== null && code !== 0) {\n          addBreadcrumb({\n            category: 'child_process',\n            message: `Child process exited with code '${code}'`,\n            level: code === 0 ? 'info' : 'warning',\n            data,\n          });\n        }\n      }\n    })\n    .on('error', error => {\n      if (!hasExited) {\n        hasExited = true;\n\n        addBreadcrumb({\n          category: 'child_process',\n          message: `Child process errored with '${error.message}'`,\n          level: 'error',\n          data,\n        });\n      }\n    });\n}\n\nfunction captureWorkerThreadEvents(worker: Worker, options: Options): void {\n  let threadId: number | undefined;\n\n  worker\n    .on('online', () => {\n      threadId = worker.threadId;\n    })\n    .on('error', error => {\n      if (options.captureWorkerErrors !== false) {\n        captureException(error, {\n          mechanism: { type: 'instrument', handled: false, data: { threadId: String(threadId) } },\n        });\n      } else {\n        addBreadcrumb({\n          category: 'worker_thread',\n          message: `Worker thread errored with '${error.message}'`,\n          level: 'error',\n          data: { threadId },\n        });\n      }\n    });\n}\n"], "names": ["defineIntegration", "addBreadcrumb", "captureException"], "mappings": ";;;;;AAqBA,MAAM,gBAAA,GAAmB,cAAc;AAEvC;;CAEA,GACO,MAAM,uBAAA,GAA0BA,KAAAA,iBAAiB,CAAC,CAAC,OAAO,GAAY,CAAA,CAAE,KAAK;IAClF,OAAO;QACL,IAAI,EAAE,gBAAgB;QACtB,KAAK,GAAG;YACN,kBAAkB,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,CAAC,KAAK,KAAc;gBACxE,IAAI,KAAA,IAAS,OAAO,KAAA,KAAU,QAAA,IAAY,SAAA,IAAa,KAAK,EAAE;oBAC5D,yBAAyB,CAAC,KAAK,CAAC,OAAA,EAAyB,OAAO,CAAC;gBAC3E;YACA,CAAO,CAAC;YAEF,kBAAkB,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,SAAS,CAAC,CAAC,KAAK,KAAc;gBACzE,IAAI,KAAA,IAAS,OAAO,KAAA,KAAU,QAAA,IAAY,QAAA,IAAY,KAAK,EAAE;oBAC3D,yBAAyB,CAAC,KAAK,CAAC,MAAA,EAAkB,OAAO,CAAC;gBACpE;YACA,CAAO,CAAC;QACR,CAAK;IACL,CAAG;AACH,CAAC;AAED,SAAS,yBAAyB,CAAC,KAAK,EAAgB,OAAO,EAAiB;IAC9E,IAAI,SAAA,GAAY,KAAK;IACrB,IAAI,IAAI;IAER,MACG,EAAE,CAAC,OAAO,EAAE,MAAM;QACvB,0CAAA;QACM,IAAI,KAAK,CAAC,SAAA,KAAc,kBAAkB,EAAE;YAC1C,SAAA,GAAY,IAAI;YAChB;QACR;QAEM,IAAA,GAAO;YAAE,SAAS,EAAE,KAAK,CAAC,SAAA;QAAA,CAAW;QACrC,IAAI,OAAO,CAAC,uBAAuB,EAAE;YACnC,IAAI,CAAC,SAAA,GAAY,KAAK,CAAC,SAAS;QACxC;IACA,CAAK,EACA,EAAE,CAAC,MAAM,GAAE,QAAQ;QAClB,IAAI,CAAC,SAAS,EAAE;YACd,SAAA,GAAY,IAAI;YAExB,mCAAA;YACQ,IAAI,IAAA,KAAS,QAAQ,IAAA,KAAS,CAAC,EAAE;gBAC/BC,KAAAA,aAAa,CAAC;oBACZ,QAAQ,EAAE,eAAe;oBACzB,OAAO,EAAE,CAAC,gCAAgC,EAAE,IAAI,CAAC,CAAC,CAAC;oBACnD,KAAK,EAAE,IAAA,KAAS,IAAI,MAAA,GAAS,SAAS;oBACtC,IAAI;gBAChB,CAAW,CAAC;YACZ;QACA;IACA,CAAK,EACA,EAAE,CAAC,OAAO,GAAE,SAAS;QACpB,IAAI,CAAC,SAAS,EAAE;YACd,SAAA,GAAY,IAAI;YAEhBA,KAAAA,aAAa,CAAC;gBACZ,QAAQ,EAAE,eAAe;gBACzB,OAAO,EAAE,CAAC,4BAA4B,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;gBACxD,KAAK,EAAE,OAAO;gBACd,IAAI;YACd,CAAS,CAAC;QACV;IACA,CAAK,CAAC;AACN;AAEA,SAAS,yBAAyB,CAAC,MAAM,EAAU,OAAO,EAAiB;IACzE,IAAI,QAAQ;IAEZ,OACG,EAAE,CAAC,QAAQ,EAAE,MAAM;QAClB,QAAA,GAAW,MAAM,CAAC,QAAQ;IAChC,CAAK,EACA,EAAE,CAAC,OAAO,GAAE,SAAS;QACpB,IAAI,OAAO,CAAC,mBAAA,KAAwB,KAAK,EAAE;YACzCC,KAAAA,gBAAgB,CAAC,KAAK,EAAE;gBACtB,SAAS,EAAE;oBAAE,IAAI,EAAE,YAAY;oBAAE,OAAO,EAAE,KAAK;oBAAE,IAAI,EAAE;wBAAE,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAA;oBAAA,CAAE;gBAAA,CAAG;YACjG,CAAS,CAAC;QACV,OAAa;YACLD,KAAAA,aAAa,CAAC;gBACZ,QAAQ,EAAE,eAAe;gBACzB,OAAO,EAAE,CAAC,4BAA4B,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;gBACxD,KAAK,EAAE,OAAO;gBACd,IAAI,EAAE;oBAAE,QAAA;gBAAA,CAAU;YAC5B,CAAS,CAAC;QACV;IACA,CAAK,CAAC;AACN", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3274, "column": 0}, "map": {"version": 3, "file": "winston.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/integrations/winston.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/ban-ts-comment */\nimport type { LogSeverityLevel } from '@sentry/core';\nimport { captureLog } from '../logs/capture';\n\nconst DEFAULT_CAPTURED_LEVELS: Array<LogSeverityLevel> = ['trace', 'debug', 'info', 'warn', 'error', 'fatal'];\n\n// See: https://github.com/winstonjs/triple-beam\nconst LEVEL_SYMBOL = Symbol.for('level');\nconst MESSAGE_SYMBOL = Symbol.for('message');\nconst SPLAT_SYMBOL = Symbol.for('splat');\n\n/**\n * Options for the Sentry Winston transport.\n */\ninterface WinstonTransportOptions {\n  /**\n   * Use this option to filter which levels should be captured. By default, all levels are captured.\n   *\n   * @example\n   * ```ts\n   * const SentryWinstonTransport = Sentry.createSentryWinstonTransport(Transport, {\n   *   // Only capture error and warn logs\n   *   levels: ['error', 'warn'],\n   * });\n   * ```\n   */\n  levels?: Array<LogSeverityLevel>;\n}\n\n/**\n * Creates a new Sentry Winston transport that fowards logs to Sentry. Requires the `enableLogs` option to be enabled.\n *\n * Supports Winston 3.x.x.\n *\n * @param TransportClass - The Winston transport class to extend.\n * @returns The extended transport class.\n *\n * @experimental This method will experience breaking changes. This is not yet part of\n * the stable Sentry SDK API and can be changed or removed without warning.\n *\n * @example\n * ```ts\n * const winston = require('winston');\n * const Transport = require('winston-transport');\n *\n * const SentryWinstonTransport = Sentry.createSentryWinstonTransport(Transport);\n *\n * const logger = winston.createLogger({\n *   transports: [new SentryWinstonTransport()],\n * });\n * ```\n */\nexport function createSentryWinstonTransport<TransportStreamInstance extends object>(\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  TransportClass: new (options?: any) => TransportStreamInstance,\n  sentryWinstonOptions?: WinstonTransportOptions,\n): typeof TransportClass {\n  // @ts-ignore - We know this is safe because SentryWinstonTransport extends TransportClass\n  class SentryWinstonTransport extends TransportClass {\n    private _levels: Set<LogSeverityLevel>;\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    public constructor(options?: any) {\n      super(options);\n      this._levels = new Set(sentryWinstonOptions?.levels ?? DEFAULT_CAPTURED_LEVELS);\n    }\n\n    /**\n     * Forwards a winston log to the Sentry SDK.\n     */\n    public log(info: unknown, callback: () => void): void {\n      try {\n        setImmediate(() => {\n          // @ts-ignore - We know this is safe because SentryWinstonTransport extends TransportClass\n          this.emit('logged', info);\n        });\n\n        if (!isObject(info)) {\n          return;\n        }\n\n        const levelFromSymbol = info[LEVEL_SYMBOL];\n\n        // See: https://github.com/winstonjs/winston?tab=readme-ov-file#streams-objectmode-and-info-objects\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const { level, message, timestamp, ...attributes } = info;\n        // Remove all symbols from the remaining attributes\n        attributes[LEVEL_SYMBOL] = undefined;\n        attributes[MESSAGE_SYMBOL] = undefined;\n        attributes[SPLAT_SYMBOL] = undefined;\n\n        const logSeverityLevel = WINSTON_LEVEL_TO_LOG_SEVERITY_LEVEL_MAP[levelFromSymbol as string] ?? 'info';\n        if (this._levels.has(logSeverityLevel)) {\n          captureLog(logSeverityLevel, message as string, {\n            ...attributes,\n            'sentry.origin': 'auto.logging.winston',\n          });\n        }\n      } catch {\n        // do nothing\n      }\n\n      if (callback) {\n        callback();\n      }\n    }\n  }\n\n  return SentryWinstonTransport as typeof TransportClass;\n}\n\nfunction isObject(anything: unknown): anything is Record<string | symbol, unknown> {\n  return typeof anything === 'object' && anything != null;\n}\n\n// npm\n// {\n//   error: 0,\n//   warn: 1,\n//   info: 2,\n//   http: 3,\n//   verbose: 4,\n//   debug: 5,\n//   silly: 6\n// }\n//\n// syslog\n// {\n//   emerg: 0,\n//   alert: 1,\n//   crit: 2,\n//   error: 3,\n//   warning: 4,\n//   notice: 5,\n//   info: 6,\n//   debug: 7,\n// }\nconst WINSTON_LEVEL_TO_LOG_SEVERITY_LEVEL_MAP: Record<string, LogSeverityLevel> = {\n  // npm\n  silly: 'trace',\n  // npm and syslog\n  debug: 'debug',\n  // npm\n  verbose: 'debug',\n  // npm\n  http: 'debug',\n  // npm and syslog\n  info: 'info',\n  // syslog\n  notice: 'info',\n  // npm\n  warn: 'warn',\n  // syslog\n  warning: 'warn',\n  // npm and syslog\n  error: 'error',\n  // syslog\n  emerg: 'fatal',\n  // syslog\n  alert: 'fatal',\n  // syslog\n  crit: 'fatal',\n};\n"], "names": ["captureLog"], "mappings": ";;;;AAIA,MAAM,uBAAuB,GAA4B;IAAC,OAAO;IAAE,OAAO;IAAE,MAAM;IAAE,MAAM;IAAE,OAAO;IAAE,OAAO;CAAC;AAE7G,gDAAA;AACA,MAAM,eAAe,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC;AACxC,MAAM,iBAAiB,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC;AAC5C,MAAM,eAAe,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC;AAExC;;CAEA,GAgBA;;;;;;;;;;;;;;;;;;;;;;CAsBA,GACO,SAAS,4BAA4B,CAC5C,8DAAA;AACE,cAAc,EACd,oBAAoB;IAEtB,0FAAA;IACE,MAAM,sBAAA,SAA+B,cAAA,CAAe;QAEtD,8DAAA;QACW,WAAW,CAAC,OAAO,CAAQ;YAChC,KAAK,CAAC,OAAO,CAAC;YACd,IAAI,CAAC,OAAA,GAAU,IAAI,GAAG,CAAC,oBAAoB,EAAE,MAAA,IAAU,uBAAuB,CAAC;QACrF;QAEA;;KAEA,GACW,GAAG,CAAC,IAAI,EAAW,QAAQ,EAAoB;YACpD,IAAI;gBACF,YAAY,CAAC,MAAM;oBAC3B,0FAAA;oBACU,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC;gBACnC,CAAS,CAAC;gBAEF,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;oBACnB;gBACV;gBAEQ,MAAM,eAAA,GAAkB,IAAI,CAAC,YAAY,CAAC;gBAElD,mGAAA;gBACA,6DAAA;gBACQ,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,UAAA,EAAW,GAAI,IAAI;gBACjE,mDAAA;gBACQ,UAAU,CAAC,YAAY,CAAA,GAAI,SAAS;gBACpC,UAAU,CAAC,cAAc,CAAA,GAAI,SAAS;gBACtC,UAAU,CAAC,YAAY,CAAA,GAAI,SAAS;gBAEpC,MAAM,mBAAmB,uCAAuC,CAAC,eAAA,CAAgB,IAAc,MAAM;gBACrG,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE;oBACtCA,QAAAA,UAAU,CAAC,gBAAgB,EAAE,SAAmB;wBAC9C,GAAG,UAAU;wBACb,eAAe,EAAE,sBAAsB;oBACnD,CAAW,CAAC;gBACZ;YACA,EAAQ,OAAM;YACd,aAAA;YACA;YAEM,IAAI,QAAQ,EAAE;gBACZ,QAAQ,EAAE;YAClB;QACA;IACA;IAEE,OAAO,sBAAA;AACT;AAEA,SAAS,QAAQ,CAAC,QAAQ,EAAyD;IACjF,OAAO,OAAO,QAAA,KAAa,YAAY,QAAA,IAAY,IAAI;AACzD;AAEA,MAAA;AACA,IAAA;AACA,cAAA;AACA,aAAA;AACA,aAAA;AACA,aAAA;AACA,gBAAA;AACA,cAAA;AACA,aAAA;AACA,IAAA;AACA,EAAA;AACA,SAAA;AACA,IAAA;AACA,cAAA;AACA,cAAA;AACA,aAAA;AACA,cAAA;AACA,gBAAA;AACA,eAAA;AACA,aAAA;AACA,cAAA;AACA,IAAA;AACA,MAAM,uCAAuC,GAAqC;IAClF,MAAA;IACE,KAAK,EAAE,OAAO;IAChB,iBAAA;IACE,KAAK,EAAE,OAAO;IAChB,MAAA;IACE,OAAO,EAAE,OAAO;IAClB,MAAA;IACE,IAAI,EAAE,OAAO;IACf,iBAAA;IACE,IAAI,EAAE,MAAM;IACd,SAAA;IACE,MAAM,EAAE,MAAM;IAChB,MAAA;IACE,IAAI,EAAE,MAAM;IACd,SAAA;IACE,OAAO,EAAE,MAAM;IACjB,iBAAA;IACE,KAAK,EAAE,OAAO;IAChB,SAAA;IACE,KAAK,EAAE,OAAO;IAChB,SAAA;IACE,KAAK,EAAE,OAAO;IAChB,SAAA;IACE,IAAI,EAAE,OAAO;AACf,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3416, "column": 0}, "map": {"version": 3, "file": "contextManager.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/otel/contextManager.ts"], "sourcesContent": ["import { AsyncLocalStorageContextManager } from '@opentelemetry/context-async-hooks';\nimport { wrapContextManagerClass } from '@sentry/opentelemetry';\n\n/**\n * This is a custom ContextManager for OpenTelemetry, which extends the default AsyncLocalStorageContextManager.\n * It ensures that we create a new hub per context, so that the OTEL Context & the Sentry Scopes are always in sync.\n *\n * Note that we currently only support AsyncHooks with this,\n * but since this should work for Node 14+ anyhow that should be good enough.\n */\nexport const SentryContextManager = wrapContextManagerClass(AsyncLocalStorageContextManager);\n"], "names": ["wrapContextManagerClass", "AsyncLocalStorageContextManager"], "mappings": ";;;;;AAGA;;;;;;CAMA,SACa,oBAAA,GAAuBA,cAAAA,uBAAuB,CAACC,kBAAAA,+BAA+B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3434, "column": 0}, "map": {"version": 3, "file": "logger.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/otel/logger.ts"], "sourcesContent": ["import { diag, DiagLogLevel } from '@opentelemetry/api';\nimport { debug } from '@sentry/core';\n\n/**\n * Setup the OTEL logger to use our own debug logger.\n */\nexport function setupOpenTelemetryLogger(): void {\n  // Disable diag, to ensure this works even if called multiple times\n  diag.disable();\n  diag.setLogger(\n    {\n      error: debug.error,\n      warn: debug.warn,\n      info: debug.log,\n      debug: debug.log,\n      verbose: debug.log,\n    },\n    DiagLogLevel.DEBUG,\n  );\n}\n"], "names": ["diag", "debug", "DiagLogLevel"], "mappings": ";;;;;AAGA;;CAEA,GACO,SAAS,wBAAwB,GAAS;IACjD,mEAAA;IACEA,IAAAA,IAAI,CAAC,OAAO,EAAE;IACdA,IAAAA,IAAI,CAAC,SAAS,CACZ;QACE,KAAK,EAAEC,KAAAA,KAAK,CAAC,KAAK;QAClB,IAAI,EAAEA,KAAAA,KAAK,CAAC,IAAI;QAChB,IAAI,EAAEA,KAAAA,KAAK,CAAC,GAAG;QACf,KAAK,EAAEA,KAAAA,KAAK,CAAC,GAAG;QAChB,OAAO,EAAEA,KAAAA,KAAK,CAAC,GAAG;IACxB,CAAK,EACDC,IAAAA,YAAY,CAAC,KAAK;AAEtB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3458, "column": 0}, "map": {"version": 3, "file": "processSession.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/integrations/processSession.ts"], "sourcesContent": ["import { defineIntegration, endSession, getIsolationScope, startSession } from '@sentry/core';\n\nconst INTEGRATION_NAME = 'ProcessSession';\n\n/**\n * Records a Session for the current process to track release health.\n */\nexport const processSessionIntegration = defineIntegration(() => {\n  return {\n    name: INTEGRATION_NAME,\n    setupOnce() {\n      startSession();\n\n      // Emitted in the case of healthy sessions, error of `mechanism.handled: true` and unhandledrejections because\n      // The 'beforeExit' event is not emitted for conditions causing explicit termination,\n      // such as calling process.exit() or uncaught exceptions.\n      // Ref: https://nodejs.org/api/process.html#process_event_beforeexit\n      process.on('beforeExit', () => {\n        const session = getIsolationScope().getSession();\n\n        // Only call endSession, if the Session exists on Scope and SessionStatus is not a\n        // Terminal Status i.e. Exited or Crashed because\n        // \"When a session is moved away from ok it must not be updated anymore.\"\n        // Ref: https://develop.sentry.dev/sdk/sessions/\n        if (session?.status !== 'ok') {\n          endSession();\n        }\n      });\n    },\n  };\n});\n"], "names": ["defineIntegration", "startSession", "getIsolationScope", "endSession"], "mappings": ";;;;AAEA,MAAM,gBAAA,GAAmB,gBAAgB;AAEzC;;CAEA,SACa,yBAAA,GAA4BA,KAAAA,iBAAiB,CAAC,MAAM;IAC/D,OAAO;QACL,IAAI,EAAE,gBAAgB;QACtB,SAAS,GAAG;YACVC,KAAAA,YAAY,EAAE;YAEpB,8GAAA;YACA,qFAAA;YACA,yDAAA;YACA,oEAAA;YACM,OAAO,CAAC,EAAE,CAAC,YAAY,EAAE,MAAM;gBAC7B,MAAM,UAAUC,KAAAA,iBAAiB,EAAE,CAAC,UAAU,EAAE;gBAExD,kFAAA;gBACA,iDAAA;gBACA,yEAAA;gBACA,gDAAA;gBACQ,IAAI,OAAO,EAAE,MAAA,KAAW,IAAI,EAAE;oBAC5BC,KAAAA,UAAU,EAAE;gBACtB;YACA,CAAO,CAAC;QACR,CAAK;IACL,CAAG;AACH,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3493, "column": 0}, "map": {"version": 3, "file": "base.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/proxy/base.ts"], "sourcesContent": ["/**\n * This code was originally forked from https://github.com/TooTallNate/proxy-agents/tree/b133295fd16f6475578b6b15bd9b4e33ecb0d0b7\n * With the following LICENSE:\n *\n * (The MIT License)\n *\n * Copyright (c) 2013 <PERSON> <<EMAIL>>*\n *\n * Permission is hereby granted, free of charge, to any person obtaining\n * a copy of this software and associated documentation files (the\n * 'Software'), to deal in the Software without restriction, including\n * without limitation the rights to use, copy, modify, merge, publish,\n * distribute, sublicense, and/or sell copies of the Software, and to\n * permit persons to whom the Software is furnished to do so, subject to\n * the following conditions:*\n *\n * The above copyright notice and this permission notice shall be\n * included in all copies or substantial portions of the Software.*\n *\n * THE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\n * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\n * IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\n * CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\n * TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\n * SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n */\n\n/* eslint-disable @typescript-eslint/explicit-member-accessibility */\n/* eslint-disable @typescript-eslint/member-ordering */\n/* eslint-disable jsdoc/require-jsdoc */\nimport * as http from 'node:http';\nimport type * as net from 'node:net';\nimport type { Duplex } from 'node:stream';\nimport type * as tls from 'node:tls';\n\nexport * from './helpers';\n\ninterface HttpConnectOpts extends net.TcpNetConnectOpts {\n  secureEndpoint: false;\n  protocol?: string;\n}\n\ninterface HttpsConnectOpts extends tls.ConnectionOptions {\n  secureEndpoint: true;\n  protocol?: string;\n  port: number;\n}\n\nexport type AgentConnectOpts = HttpConnectOpts | HttpsConnectOpts;\n\nconst INTERNAL = Symbol('AgentBaseInternalState');\n\ninterface InternalState {\n  defaultPort?: number;\n  protocol?: string;\n  currentSocket?: Duplex;\n}\n\nexport abstract class Agent extends http.Agent {\n  private [INTERNAL]: InternalState;\n\n  // Set by `http.Agent` - missing from `@types/node`\n  options!: Partial<net.TcpNetConnectOpts & tls.ConnectionOptions>;\n  keepAlive!: boolean;\n\n  constructor(opts?: http.AgentOptions) {\n    super(opts);\n    this[INTERNAL] = {};\n  }\n\n  abstract connect(\n    req: http.ClientRequest,\n    options: AgentConnectOpts,\n  ): Promise<Duplex | http.Agent> | Duplex | http.Agent;\n\n  /**\n   * Determine whether this is an `http` or `https` request.\n   */\n  isSecureEndpoint(options?: AgentConnectOpts): boolean {\n    if (options) {\n      // First check the `secureEndpoint` property explicitly, since this\n      // means that a parent `Agent` is \"passing through\" to this instance.\n      if (typeof (options as Partial<typeof options>).secureEndpoint === 'boolean') {\n        return options.secureEndpoint;\n      }\n\n      // If no explicit `secure` endpoint, check if `protocol` property is\n      // set. This will usually be the case since using a full string URL\n      // or `URL` instance should be the most common usage.\n      if (typeof options.protocol === 'string') {\n        return options.protocol === 'https:';\n      }\n    }\n\n    // Finally, if no `protocol` property was set, then fall back to\n    // checking the stack trace of the current call stack, and try to\n    // detect the \"https\" module.\n    const { stack } = new Error();\n    if (typeof stack !== 'string') return false;\n    return stack.split('\\n').some(l => l.indexOf('(https.js:') !== -1 || l.indexOf('node:https:') !== -1);\n  }\n\n  createSocket(req: http.ClientRequest, options: AgentConnectOpts, cb: (err: Error | null, s?: Duplex) => void): void {\n    const connectOpts = {\n      ...options,\n      secureEndpoint: this.isSecureEndpoint(options),\n    };\n    Promise.resolve()\n      .then(() => this.connect(req, connectOpts))\n      .then(socket => {\n        if (socket instanceof http.Agent) {\n          // @ts-expect-error `addRequest()` isn't defined in `@types/node`\n          return socket.addRequest(req, connectOpts);\n        }\n        this[INTERNAL].currentSocket = socket;\n        // @ts-expect-error `createSocket()` isn't defined in `@types/node`\n        super.createSocket(req, options, cb);\n      }, cb);\n  }\n\n  createConnection(): Duplex {\n    const socket = this[INTERNAL].currentSocket;\n    this[INTERNAL].currentSocket = undefined;\n    if (!socket) {\n      throw new Error('No socket was returned in the `connect()` function');\n    }\n    return socket;\n  }\n\n  get defaultPort(): number {\n    return this[INTERNAL].defaultPort ?? (this.protocol === 'https:' ? 443 : 80);\n  }\n\n  set defaultPort(v: number) {\n    if (this[INTERNAL]) {\n      this[INTERNAL].defaultPort = v;\n    }\n  }\n\n  get protocol(): string {\n    return this[INTERNAL].protocol ?? (this.isSecureEndpoint() ? 'https:' : 'http:');\n  }\n\n  set protocol(v: string) {\n    if (this[INTERNAL]) {\n      this[INTERNAL].protocol = v;\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;CA0BA,GAyBA,MAAM,QAAA,GAAW,MAAM,CAAC,wBAAwB,CAAC;AAQjC,MAAM,KAAA,SAAc,IAAI,CAAC,KAAA,CAAM;IAG/C,mDAAA;IAIE,WAAW,CAAC,IAAI,CAAsB;QACpC,KAAK,CAAC,IAAI,CAAC;QACX,IAAI,CAAC,QAAQ,CAAA,GAAI,CAAA,CAAE;IACvB;IAOA;;GAEA,GACE,gBAAgB,CAAC,OAAO,EAA8B;QACpD,IAAI,OAAO,EAAE;YACjB,mEAAA;YACA,qEAAA;YACM,IAAI,OAAO,AAAC,OAAA,CAAoC,cAAA,KAAmB,SAAS,EAAE;gBAC5E,OAAO,OAAO,CAAC,cAAc;YACrC;YAEA,oEAAA;YACA,mEAAA;YACA,qDAAA;YACM,IAAI,OAAO,OAAO,CAAC,QAAA,KAAa,QAAQ,EAAE;gBACxC,OAAO,OAAO,CAAC,QAAA,KAAa,QAAQ;YAC5C;QACA;QAEA,gEAAA;QACA,iEAAA;QACA,6BAAA;QACI,MAAM,EAAE,KAAA,EAAM,GAAI,IAAI,KAAK,EAAE;QAC7B,IAAI,OAAO,KAAA,KAAU,QAAQ,EAAE,OAAO,KAAK;QAC3C,OAAO,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,EAAC,CAAA,GAAK,CAAC,CAAC,OAAO,CAAC,YAAY,CAAA,KAAM,CAAA,CAAC,IAAK,CAAC,CAAC,OAAO,CAAC,aAAa,CAAA,KAAM,CAAA,CAAE,CAAC;IACzG;IAEE,YAAY,CAAC,GAAG,EAAsB,OAAO,EAAoB,EAAE,EAAiD;QAClH,MAAM,cAAc;YAClB,GAAG,OAAO;YACV,cAAc,EAAE,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;QACpD,CAAK;QACD,OAAO,CAAC,OAAO,GACZ,IAAI,CAAC,IAAM,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,WAAW,CAAC,EACzC,IAAI,EAAC,MAAA,IAAU;YACd,IAAI,MAAA,YAAkB,IAAI,CAAC,KAAK,EAAE;gBAC1C,iEAAA;gBACU,OAAO,MAAM,CAAC,UAAU,CAAC,GAAG,EAAE,WAAW,CAAC;YACpD;YACQ,IAAI,CAAC,QAAQ,CAAC,CAAC,aAAA,GAAgB,MAAM;YAC7C,mEAAA;YACQ,KAAK,CAAC,YAAY,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE,CAAC;QAC5C,CAAO,EAAE,EAAE,CAAC;IACZ;IAEE,gBAAgB,GAAW;QACzB,MAAM,SAAS,IAAI,CAAC,QAAQ,CAAC,CAAC,aAAa;QAC3C,IAAI,CAAC,QAAQ,CAAC,CAAC,aAAA,GAAgB,SAAS;QACxC,IAAI,CAAC,MAAM,EAAE;YACX,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC;QAC3E;QACI,OAAO,MAAM;IACjB;IAEE,IAAI,WAAW,GAAW;QACxB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,WAAA,IAAA,CAAgB,IAAI,CAAC,QAAA,KAAa,QAAA,GAAW,GAAA,GAAM,EAAE,CAAC;IAChF;IAEE,IAAI,WAAW,CAAC,CAAC,EAAU;QACzB,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE;YAClB,IAAI,CAAC,QAAQ,CAAC,CAAC,WAAA,GAAc,CAAC;QACpC;IACA;IAEE,IAAI,QAAQ,GAAW;QACrB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,QAAA,IAAA,CAAa,IAAI,CAAC,gBAAgB,EAAC,GAAI,QAAA,GAAW,OAAO,CAAC;IACpF;IAEE,IAAI,QAAQ,CAAC,CAAC,EAAU;QACtB,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE;YAClB,IAAI,CAAC,QAAQ,CAAC,CAAC,QAAA,GAAW,CAAC;QACjC;IACA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3600, "column": 0}, "map": {"version": 3, "file": "parse-proxy-response.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/proxy/parse-proxy-response.ts"], "sourcesContent": ["/**\n * This code was originally forked from https://github.com/TooTallNate/proxy-agents/tree/b133295fd16f6475578b6b15bd9b4e33ecb0d0b7\n * With the following LICENSE:\n *\n * (The MIT License)\n *\n * Copyright (c) 2013 <PERSON> <<EMAIL>>*\n *\n * Permission is hereby granted, free of charge, to any person obtaining\n * a copy of this software and associated documentation files (the\n * 'Software'), to deal in the Software without restriction, including\n * without limitation the rights to use, copy, modify, merge, publish,\n * distribute, sublicense, and/or sell copies of the Software, and to\n * permit persons to whom the Software is furnished to do so, subject to\n * the following conditions:*\n *\n * The above copyright notice and this permission notice shall be\n * included in all copies or substantial portions of the Software.*\n *\n * THE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\n * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\n * IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\n * CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\n * TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\n * SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n */\n\n/* eslint-disable @typescript-eslint/explicit-function-return-type */\n/* eslint-disable jsdoc/require-jsdoc */\nimport type { IncomingHttpHeaders } from 'node:http';\nimport type { Readable } from 'node:stream';\nimport { debug } from '@sentry/core';\n\nfunction debugLog(...args: unknown[]): void {\n  debug.log('[https-proxy-agent:parse-proxy-response]', ...args);\n}\n\nexport interface ConnectResponse {\n  statusCode: number;\n  statusText: string;\n  headers: IncomingHttpHeaders;\n}\n\nexport function parseProxyResponse(socket: Readable): Promise<{ connect: ConnectResponse; buffered: Buffer }> {\n  return new Promise((resolve, reject) => {\n    // we need to buffer any HTTP traffic that happens with the proxy before we get\n    // the CONNECT response, so that if the response is anything other than an \"200\"\n    // response code, then we can re-play the \"data\" events on the socket once the\n    // HTTP parser is hooked up...\n    let buffersLength = 0;\n    const buffers: Buffer[] = [];\n\n    function read() {\n      const b = socket.read();\n      if (b) ondata(b);\n      else socket.once('readable', read);\n    }\n\n    function cleanup() {\n      socket.removeListener('end', onend);\n      socket.removeListener('error', onerror);\n      socket.removeListener('readable', read);\n    }\n\n    function onend() {\n      cleanup();\n      debugLog('onend');\n      reject(new Error('Proxy connection ended before receiving CONNECT response'));\n    }\n\n    function onerror(err: Error) {\n      cleanup();\n      debugLog('onerror %o', err);\n      reject(err);\n    }\n\n    function ondata(b: Buffer) {\n      buffers.push(b);\n      buffersLength += b.length;\n\n      const buffered = Buffer.concat(buffers, buffersLength);\n      const endOfHeaders = buffered.indexOf('\\r\\n\\r\\n');\n\n      if (endOfHeaders === -1) {\n        // keep buffering\n        debugLog('have not received end of HTTP headers yet...');\n        read();\n        return;\n      }\n\n      const headerParts = buffered.subarray(0, endOfHeaders).toString('ascii').split('\\r\\n');\n      const firstLine = headerParts.shift();\n      if (!firstLine) {\n        socket.destroy();\n        return reject(new Error('No header received from proxy CONNECT response'));\n      }\n      const firstLineParts = firstLine.split(' ');\n      const statusCode = +(firstLineParts[1] || 0);\n      const statusText = firstLineParts.slice(2).join(' ');\n      const headers: IncomingHttpHeaders = {};\n      for (const header of headerParts) {\n        if (!header) continue;\n        const firstColon = header.indexOf(':');\n        if (firstColon === -1) {\n          socket.destroy();\n          return reject(new Error(`Invalid header from proxy CONNECT response: \"${header}\"`));\n        }\n        const key = header.slice(0, firstColon).toLowerCase();\n        const value = header.slice(firstColon + 1).trimStart();\n        const current = headers[key];\n        if (typeof current === 'string') {\n          headers[key] = [current, value];\n        } else if (Array.isArray(current)) {\n          current.push(value);\n        } else {\n          headers[key] = value;\n        }\n      }\n      debugLog('got proxy server response: %o %o', firstLine, headers);\n      cleanup();\n      resolve({\n        connect: {\n          statusCode,\n          statusText,\n          headers,\n        },\n        buffered,\n      });\n    }\n\n    socket.on('error', onerror);\n    socket.on('end', onend);\n\n    read();\n  });\n}\n"], "names": ["debug"], "mappings": ";;;;AAkCA,SAAS,QAAQ,CAAC,GAAG,IAAI,EAAmB;IAC1CA,KAAAA,KAAK,CAAC,GAAG,CAAC,0CAA0C,EAAE,GAAG,IAAI,CAAC;AAChE;AAQO,SAAS,kBAAkB,CAAC,MAAM,EAAqE;IAC5G,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAK;QAC1C,+EAAA;QACA,gFAAA;QACA,8EAAA;QACA,8BAAA;QACI,IAAI,aAAA,GAAgB,CAAC;QACrB,MAAM,OAAO,GAAa,EAAE;QAE5B,SAAS,IAAI,GAAG;YACd,MAAM,CAAA,GAAI,MAAM,CAAC,IAAI,EAAE;YACvB,IAAI,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;iBACX,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;QACxC;QAEI,SAAS,OAAO,GAAG;YACjB,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC;YACnC,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC;YACvC,MAAM,CAAC,cAAc,CAAC,UAAU,EAAE,IAAI,CAAC;QAC7C;QAEI,SAAS,KAAK,GAAG;YACf,OAAO,EAAE;YACT,QAAQ,CAAC,OAAO,CAAC;YACjB,MAAM,CAAC,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC;QACnF;QAEI,SAAS,OAAO,CAAC,GAAG,EAAS;YAC3B,OAAO,EAAE;YACT,QAAQ,CAAC,YAAY,EAAE,GAAG,CAAC;YAC3B,MAAM,CAAC,GAAG,CAAC;QACjB;QAEI,SAAS,MAAM,CAAC,CAAC,EAAU;YACzB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;YACf,aAAA,IAAiB,CAAC,CAAC,MAAM;YAEzB,MAAM,QAAA,GAAW,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,aAAa,CAAC;YACtD,MAAM,eAAe,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC;YAEjD,IAAI,YAAA,KAAiB,CAAA,CAAE,EAAE;gBAC/B,iBAAA;gBACQ,QAAQ,CAAC,8CAA8C,CAAC;gBACxD,IAAI,EAAE;gBACN;YACR;YAEM,MAAM,cAAc,QAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC;YACtF,MAAM,SAAA,GAAY,WAAW,CAAC,KAAK,EAAE;YACrC,IAAI,CAAC,SAAS,EAAE;gBACd,MAAM,CAAC,OAAO,EAAE;gBAChB,OAAO,MAAM,CAAC,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;YAClF;YACM,MAAM,iBAAiB,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC;YAC3C,MAAM,UAAA,GAAa,CAAA,CAAE,cAAc,CAAC,CAAC,CAAA,IAAK,CAAC,CAAC;YAC5C,MAAM,UAAA,GAAa,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;YACpD,MAAM,OAAO,GAAwB,CAAA,CAAE;YACvC,KAAK,MAAM,MAAA,IAAU,WAAW,CAAE;gBAChC,IAAI,CAAC,MAAM,EAAE;gBACb,MAAM,aAAa,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC;gBACtC,IAAI,UAAA,KAAe,CAAA,CAAE,EAAE;oBACrB,MAAM,CAAC,OAAO,EAAE;oBAChB,OAAO,MAAM,CAAC,IAAI,KAAK,CAAC,CAAC,6CAA6C,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7F;gBACQ,MAAM,GAAA,GAAM,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,WAAW,EAAE;gBACrD,MAAM,KAAA,GAAQ,MAAM,CAAC,KAAK,CAAC,UAAA,GAAa,CAAC,CAAC,CAAC,SAAS,EAAE;gBACtD,MAAM,OAAA,GAAU,OAAO,CAAC,GAAG,CAAC;gBAC5B,IAAI,OAAO,OAAA,KAAY,QAAQ,EAAE;oBAC/B,OAAO,CAAC,GAAG,CAAA,GAAI;wBAAC,OAAO;wBAAE,KAAK;qBAAC;gBACzC,CAAQ,MAAO,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;oBACjC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;gBAC7B,OAAe;oBACL,OAAO,CAAC,GAAG,CAAA,GAAI,KAAK;gBAC9B;YACA;YACM,QAAQ,CAAC,kCAAkC,EAAE,SAAS,EAAE,OAAO,CAAC;YAChE,OAAO,EAAE;YACT,OAAO,CAAC;gBACN,OAAO,EAAE;oBACP,UAAU;oBACV,UAAU;oBACV,OAAO;gBACjB,CAAS;gBACD,QAAQ;YAChB,CAAO,CAAC;QACR;QAEI,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;QAC3B,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;QAEvB,IAAI,EAAE;IACV,CAAG,CAAC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3699, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/proxy/index.ts"], "sourcesContent": ["/**\n * This code was originally forked from https://github.com/TooTallNate/proxy-agents/tree/b133295fd16f6475578b6b15bd9b4e33ecb0d0b7\n * With the following LICENSE:\n *\n * (The MIT License)\n *\n * Copyright (c) 2013 <PERSON> <<EMAIL>>*\n *\n * Permission is hereby granted, free of charge, to any person obtaining\n * a copy of this software and associated documentation files (the\n * 'Software'), to deal in the Software without restriction, including\n * without limitation the rights to use, copy, modify, merge, publish,\n * distribute, sublicense, and/or sell copies of the Software, and to\n * permit persons to whom the Software is furnished to do so, subject to\n * the following conditions:*\n *\n * The above copyright notice and this permission notice shall be\n * included in all copies or substantial portions of the Software.*\n *\n * THE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\n * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\n * IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\n * CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\n * TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\n * SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n */\n\n/* eslint-disable @typescript-eslint/explicit-member-accessibility */\n/* eslint-disable @typescript-eslint/no-unused-vars */\n// eslint-disable-next-line import/no-duplicates\nimport type * as http from 'node:http';\n// eslint-disable-next-line import/no-duplicates\nimport type { OutgoingHttpHeaders } from 'node:http';\nimport * as net from 'node:net';\nimport * as tls from 'node:tls';\nimport { debug } from '@sentry/core';\nimport type { AgentConnectOpts } from './base';\nimport { Agent } from './base';\nimport { parseProxyResponse } from './parse-proxy-response';\n\nfunction debugLog(...args: unknown[]): void {\n  debug.log('[https-proxy-agent]', ...args);\n}\n\ntype Protocol<T> = T extends `${infer Protocol}:${infer _}` ? Protocol : never;\n\ntype ConnectOptsMap = {\n  http: Omit<net.TcpNetConnectOpts, 'host' | 'port'>;\n  https: Omit<tls.ConnectionOptions, 'host' | 'port'>;\n};\n\ntype ConnectOpts<T> = {\n  [P in keyof ConnectOptsMap]: Protocol<T> extends P ? ConnectOptsMap[P] : never;\n}[keyof ConnectOptsMap];\n\nexport type HttpsProxyAgentOptions<T> = ConnectOpts<T> &\n  http.AgentOptions & {\n    headers?: OutgoingHttpHeaders | (() => OutgoingHttpHeaders);\n  };\n\n/**\n * The `HttpsProxyAgent` implements an HTTP Agent subclass that connects to\n * the specified \"HTTP(s) proxy server\" in order to proxy HTTPS requests.\n *\n * Outgoing HTTP requests are first tunneled through the proxy server using the\n * `CONNECT` HTTP request method to establish a connection to the proxy server,\n * and then the proxy server connects to the destination target and issues the\n * HTTP request from the proxy server.\n *\n * `https:` requests have their socket connection upgraded to TLS once\n * the connection to the proxy server has been established.\n */\nexport class HttpsProxyAgent<Uri extends string> extends Agent {\n  static protocols = ['http', 'https'] as const;\n\n  readonly proxy: URL;\n  proxyHeaders: OutgoingHttpHeaders | (() => OutgoingHttpHeaders);\n  connectOpts: net.TcpNetConnectOpts & tls.ConnectionOptions;\n\n  constructor(proxy: Uri | URL, opts?: HttpsProxyAgentOptions<Uri>) {\n    super(opts);\n    this.options = {};\n    this.proxy = typeof proxy === 'string' ? new URL(proxy) : proxy;\n    this.proxyHeaders = opts?.headers ?? {};\n    debugLog('Creating new HttpsProxyAgent instance: %o', this.proxy.href);\n\n    // Trim off the brackets from IPv6 addresses\n    const host = (this.proxy.hostname || this.proxy.host).replace(/^\\[|\\]$/g, '');\n    const port = this.proxy.port ? parseInt(this.proxy.port, 10) : this.proxy.protocol === 'https:' ? 443 : 80;\n    this.connectOpts = {\n      // Attempt to negotiate http/1.1 for proxy servers that support http/2\n      ALPNProtocols: ['http/1.1'],\n      ...(opts ? omit(opts, 'headers') : null),\n      host,\n      port,\n    };\n  }\n\n  /**\n   * Called when the node-core HTTP client library is creating a\n   * new HTTP request.\n   */\n  async connect(req: http.ClientRequest, opts: AgentConnectOpts): Promise<net.Socket> {\n    const { proxy } = this;\n\n    if (!opts.host) {\n      throw new TypeError('No \"host\" provided');\n    }\n\n    // Create a socket connection to the proxy server.\n    let socket: net.Socket;\n    if (proxy.protocol === 'https:') {\n      debugLog('Creating `tls.Socket`: %o', this.connectOpts);\n      const servername = this.connectOpts.servername || this.connectOpts.host;\n      socket = tls.connect({\n        ...this.connectOpts,\n        servername: servername && net.isIP(servername) ? undefined : servername,\n      });\n    } else {\n      debugLog('Creating `net.Socket`: %o', this.connectOpts);\n      socket = net.connect(this.connectOpts);\n    }\n\n    const headers: OutgoingHttpHeaders =\n      typeof this.proxyHeaders === 'function' ? this.proxyHeaders() : { ...this.proxyHeaders };\n    const host = net.isIPv6(opts.host) ? `[${opts.host}]` : opts.host;\n    let payload = `CONNECT ${host}:${opts.port} HTTP/1.1\\r\\n`;\n\n    // Inject the `Proxy-Authorization` header if necessary.\n    if (proxy.username || proxy.password) {\n      const auth = `${decodeURIComponent(proxy.username)}:${decodeURIComponent(proxy.password)}`;\n      headers['Proxy-Authorization'] = `Basic ${Buffer.from(auth).toString('base64')}`;\n    }\n\n    headers.Host = `${host}:${opts.port}`;\n\n    if (!headers['Proxy-Connection']) {\n      headers['Proxy-Connection'] = this.keepAlive ? 'Keep-Alive' : 'close';\n    }\n    for (const name of Object.keys(headers)) {\n      payload += `${name}: ${headers[name]}\\r\\n`;\n    }\n\n    const proxyResponsePromise = parseProxyResponse(socket);\n\n    socket.write(`${payload}\\r\\n`);\n\n    const { connect, buffered } = await proxyResponsePromise;\n    req.emit('proxyConnect', connect);\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    // @ts-ignore Not EventEmitter in Node types\n    this.emit('proxyConnect', connect, req);\n\n    if (connect.statusCode === 200) {\n      req.once('socket', resume);\n\n      if (opts.secureEndpoint) {\n        // The proxy is connecting to a TLS server, so upgrade\n        // this socket connection to a TLS connection.\n        debugLog('Upgrading socket connection to TLS');\n        const servername = opts.servername || opts.host;\n        return tls.connect({\n          ...omit(opts, 'host', 'path', 'port'),\n          socket,\n          servername: net.isIP(servername) ? undefined : servername,\n        });\n      }\n\n      return socket;\n    }\n\n    // Some other status code that's not 200... need to re-play the HTTP\n    // header \"data\" events onto the socket once the HTTP machinery is\n    // attached so that the node core `http` can parse and handle the\n    // error status code.\n\n    // Close the original socket, and a new \"fake\" socket is returned\n    // instead, so that the proxy doesn't get the HTTP request\n    // written to it (which may contain `Authorization` headers or other\n    // sensitive data).\n    //\n    // See: https://hackerone.com/reports/541502\n    socket.destroy();\n\n    const fakeSocket = new net.Socket({ writable: false });\n    fakeSocket.readable = true;\n\n    // Need to wait for the \"socket\" event to re-play the \"data\" events.\n    req.once('socket', (s: net.Socket) => {\n      debugLog('Replaying proxy buffer for failed request');\n      // Replay the \"buffered\" Buffer onto the fake `socket`, since at\n      // this point the HTTP module machinery has been hooked up for\n      // the user.\n      s.push(buffered);\n      s.push(null);\n    });\n\n    return fakeSocket;\n  }\n}\n\nfunction resume(socket: net.Socket | tls.TLSSocket): void {\n  socket.resume();\n}\n\nfunction omit<T extends object, K extends [...(keyof T)[]]>(\n  obj: T,\n  ...keys: K\n): {\n  [K2 in Exclude<keyof T, K[number]>]: T[K2];\n} {\n  const ret = {} as {\n    [K in keyof typeof obj]: (typeof obj)[K];\n  };\n  let key: keyof typeof obj;\n  for (key in obj) {\n    if (!keys.includes(key)) {\n      ret[key] = obj[key];\n    }\n  }\n  return ret;\n}\n"], "names": ["debug", "Agent", "parseProxyResponse"], "mappings": ";;;;;;;;AAyCA,SAAS,QAAQ,CAAC,GAAG,IAAI,EAAmB;IAC1CA,KAAAA,KAAK,CAAC,GAAG,CAAC,qBAAqB,EAAE,GAAG,IAAI,CAAC;AAC3C;AAkBA;;;;;;;;;;;CAWA,GACA,MAAA,eAAA,SAAAC,KAAAA,KAAA,CAAA;IACA,OAAA,YAAA,GAAA;QAAA,IAAA,CAAA,SAAA,GAAA;YAAA,MAAA;YAAA,OAAA;SAAA,CAAA;IAAA;IAMA,WAAA,CAAA,KAAA,EAAA,IAAA,CAAA;QACA,KAAA,CAAA,IAAA,CAAA;QACA,IAAA,CAAA,OAAA,GAAA,CAAA,CAAA;QACA,IAAA,CAAA,KAAA,GAAA,OAAA,KAAA,KAAA,QAAA,GAAA,IAAA,GAAA,CAAA,KAAA,CAAA,GAAA,KAAA;QACA,IAAA,CAAA,YAAA,GAAA,IAAA,EAAA,OAAA,IAAA,CAAA,CAAA;QACA,QAAA,CAAA,2CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA;QAEA,4CAAA;QACA,MAAA,IAAA,GAAA,CAAA,IAAA,CAAA,KAAA,CAAA,QAAA,IAAA,IAAA,CAAA,KAAA,CAAA,IAAA,EAAA,OAAA,CAAA,UAAA,EAAA,EAAA,CAAA;QACA,MAAA,IAAA,GAAA,IAAA,CAAA,KAAA,CAAA,IAAA,GAAA,QAAA,CAAA,IAAA,CAAA,KAAA,CAAA,IAAA,EAAA,EAAA,CAAA,GAAA,IAAA,CAAA,KAAA,CAAA,QAAA,KAAA,QAAA,GAAA,GAAA,GAAA,EAAA;QACA,IAAA,CAAA,WAAA,GAAA;YACA,sEAAA;YACA,aAAA,EAAA;gBAAA,UAAA;aAAA;YACA,GAAA,IAAA,GAAA,IAAA,CAAA,IAAA,EAAA,SAAA,CAAA,GAAA,IAAA,CAAA;YACA,IAAA;YACA,IAAA;QACA,CAAA;IACA;IAEA;;;GAGA,GACA,MAAA,OAAA,CAAA,GAAA,EAAA,IAAA,EAAA;QACA,MAAA,EAAA,KAAA,EAAA,GAAA,IAAA;QAEA,IAAA,CAAA,IAAA,CAAA,IAAA,EAAA;YACA,MAAA,IAAA,SAAA,CAAA,oBAAA,CAAA;QACA;QAEA,kDAAA;QACA,IAAA,MAAA;QACA,IAAA,KAAA,CAAA,QAAA,KAAA,QAAA,EAAA;YACA,QAAA,CAAA,2BAAA,EAAA,IAAA,CAAA,WAAA,CAAA;YACA,MAAA,UAAA,GAAA,IAAA,CAAA,WAAA,CAAA,UAAA,IAAA,IAAA,CAAA,WAAA,CAAA,IAAA;YACA,MAAA,GAAA,GAAA,CAAA,OAAA,CAAA;gBACA,GAAA,IAAA,CAAA,WAAA;gBACA,UAAA,EAAA,UAAA,IAAA,GAAA,CAAA,IAAA,CAAA,UAAA,CAAA,GAAA,SAAA,GAAA,UAAA;YACA,CAAA,CAAA;QACA,CAAA,MAAA;YACA,QAAA,CAAA,2BAAA,EAAA,IAAA,CAAA,WAAA,CAAA;YACA,MAAA,GAAA,GAAA,CAAA,OAAA,CAAA,IAAA,CAAA,WAAA,CAAA;QACA;QAEA,MAAA,OAAA,GACA,OAAA,IAAA,CAAA,YAAA,KAAA,UAAA,GAAA,IAAA,CAAA,YAAA,EAAA,GAAA;YAAA,GAAA,IAAA,CAAA,YAAA;QAAA,CAAA;QACA,MAAA,IAAA,GAAA,GAAA,CAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,EAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,GAAA,IAAA,CAAA,IAAA;QACA,IAAA,OAAA,GAAA,CAAA,QAAA,EAAA,IAAA,CAAA,CAAA,EAAA,IAAA,CAAA,IAAA,CAAA,aAAA,CAAA;QAEA,wDAAA;QACA,IAAA,KAAA,CAAA,QAAA,IAAA,KAAA,CAAA,QAAA,EAAA;YACA,MAAA,IAAA,GAAA,CAAA,EAAA,kBAAA,CAAA,KAAA,CAAA,QAAA,CAAA,CAAA,CAAA,EAAA,kBAAA,CAAA,KAAA,CAAA,QAAA,CAAA,CAAA,CAAA;YACA,OAAA,CAAA,qBAAA,CAAA,GAAA,CAAA,MAAA,EAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,QAAA,CAAA,QAAA,CAAA,CAAA,CAAA;QACA;QAEA,OAAA,CAAA,IAAA,GAAA,CAAA,EAAA,IAAA,CAAA,CAAA,EAAA,IAAA,CAAA,IAAA,CAAA,CAAA;QAEA,IAAA,CAAA,OAAA,CAAA,kBAAA,CAAA,EAAA;YACA,OAAA,CAAA,kBAAA,CAAA,GAAA,IAAA,CAAA,SAAA,GAAA,YAAA,GAAA,OAAA;QACA;QACA,KAAA,MAAA,IAAA,IAAA,MAAA,CAAA,IAAA,CAAA,OAAA,CAAA,CAAA;YACA,OAAA,IAAA,CAAA,EAAA,IAAA,CAAA,EAAA,EAAA,OAAA,CAAA,IAAA,CAAA,CAAA,IAAA,CAAA;QACA;QAEA,MAAA,oBAAA,GAAAC,mBAAAA,kBAAA,CAAA,MAAA,CAAA;QAEA,MAAA,CAAA,KAAA,CAAA,CAAA,EAAA,OAAA,CAAA,IAAA,CAAA,CAAA;QAEA,MAAA,EAAA,OAAA,EAAA,QAAA,EAAA,GAAA,MAAA,oBAAA;QACA,GAAA,CAAA,IAAA,CAAA,cAAA,EAAA,OAAA,CAAA;QACA,6DAAA;QACA,4CAAA;QACA,IAAA,CAAA,IAAA,CAAA,cAAA,EAAA,OAAA,EAAA,GAAA,CAAA;QAEA,IAAA,OAAA,CAAA,UAAA,KAAA,GAAA,EAAA;YACA,GAAA,CAAA,IAAA,CAAA,QAAA,EAAA,MAAA,CAAA;YAEA,IAAA,IAAA,CAAA,cAAA,EAAA;gBACA,sDAAA;gBACA,8CAAA;gBACA,QAAA,CAAA,oCAAA,CAAA;gBACA,MAAA,UAAA,GAAA,IAAA,CAAA,UAAA,IAAA,IAAA,CAAA,IAAA;gBACA,OAAA,GAAA,CAAA,OAAA,CAAA;oBACA,GAAA,IAAA,CAAA,IAAA,EAAA,MAAA,EAAA,MAAA,EAAA,MAAA,CAAA;oBACA,MAAA;oBACA,UAAA,EAAA,GAAA,CAAA,IAAA,CAAA,UAAA,CAAA,GAAA,SAAA,GAAA,UAAA;gBACA,CAAA,CAAA;YACA;YAEA,OAAA,MAAA;QACA;QAEA,oEAAA;QACA,kEAAA;QACA,iEAAA;QACA,qBAAA;QAEA,iEAAA;QACA,0DAAA;QACA,oEAAA;QACA,mBAAA;QACA,EAAA;QACA,4CAAA;QACA,MAAA,CAAA,OAAA,EAAA;QAEA,MAAA,UAAA,GAAA,IAAA,GAAA,CAAA,MAAA,CAAA;YAAA,QAAA,EAAA,KAAA;QAAA,CAAA,CAAA;QACA,UAAA,CAAA,QAAA,GAAA,IAAA;QAEA,oEAAA;QACA,GAAA,CAAA,IAAA,CAAA,QAAA,EAAA,CAAA,CAAA,KAAA;YACA,QAAA,CAAA,2CAAA,CAAA;YACA,gEAAA;YACA,8DAAA;YACA,YAAA;YACA,CAAA,CAAA,IAAA,CAAA,QAAA,CAAA;YACA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA;QACA,CAAA,CAAA;QAEA,OAAA,UAAA;IACA;AACA,CAAA;AAAA,eAAA,CAAA,YAAA,EAAA;AAEA,SAAA,MAAA,CAAA,MAAA,EAAA;IACA,MAAA,CAAA,MAAA,EAAA;AACA;AAEA,SAAA,IAAA,CACA,GAAA,EACA,GAAA,IAAA;IAIA,MAAA,GAAA,GAAA,CAAA;IAGA,IAAA,GAAA;IACA,IAAA,GAAA,IAAA,GAAA,CAAA;QACA,IAAA,CAAA,IAAA,CAAA,QAAA,CAAA,GAAA,CAAA,EAAA;YACA,GAAA,CAAA,GAAA,CAAA,GAAA,GAAA,CAAA,GAAA,CAAA;QACA;IACA;IACA,OAAA,GAAA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3854, "column": 0}, "map": {"version": 3, "file": "http.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/transports/http.ts"], "sourcesContent": ["import * as http from 'node:http';\nimport * as https from 'node:https';\nimport { Readable } from 'node:stream';\nimport { createGzip } from 'node:zlib';\nimport type {\n  BaseTransportOptions,\n  Transport,\n  TransportMakeRequestResponse,\n  TransportRequest,\n  TransportRequestExecutor,\n} from '@sentry/core';\nimport { consoleSandbox, createTransport, suppressTracing } from '@sentry/core';\nimport { HttpsProxyAgent } from '../proxy';\nimport type { HTTPModule } from './http-module';\n\nexport interface NodeTransportOptions extends BaseTransportOptions {\n  /** Define custom headers */\n  headers?: Record<string, string>;\n  /** Set a proxy that should be used for outbound requests. */\n  proxy?: string;\n  /** HTTPS proxy CA certificates */\n  caCerts?: string | Buffer | Array<string | Buffer>;\n  /** Custom HTTP module. Defaults to the native 'http' and 'https' modules. */\n  httpModule?: HTTPModule;\n  /** Allow overriding connection keepAlive, defaults to false */\n  keepAlive?: boolean;\n}\n\n// Estimated maximum size for reasonable standalone event\nconst GZIP_THRESHOLD = 1024 * 32;\n\n/**\n * Gets a stream from a Uint8Array or string\n * Readable.from is ideal but was added in node.js v12.3.0 and v10.17.0\n */\nfunction streamFromBody(body: Uint8Array | string): Readable {\n  return new Readable({\n    read() {\n      this.push(body);\n      this.push(null);\n    },\n  });\n}\n\n/**\n * Creates a Transport that uses native the native 'http' and 'https' modules to send events to Sentry.\n */\nexport function makeNodeTransport(options: NodeTransportOptions): Transport {\n  let urlSegments: URL;\n\n  try {\n    urlSegments = new URL(options.url);\n  } catch (e) {\n    consoleSandbox(() => {\n      // eslint-disable-next-line no-console\n      console.warn(\n        '[@sentry/node]: Invalid dsn or tunnel option, will not send any events. The tunnel option must be a full URL when used.',\n      );\n    });\n    return createTransport(options, () => Promise.resolve({}));\n  }\n\n  const isHttps = urlSegments.protocol === 'https:';\n\n  // Proxy prioritization: http => `options.proxy` | `process.env.http_proxy`\n  // Proxy prioritization: https => `options.proxy` | `process.env.https_proxy` | `process.env.http_proxy`\n  const proxy = applyNoProxyOption(\n    urlSegments,\n    options.proxy || (isHttps ? process.env.https_proxy : undefined) || process.env.http_proxy,\n  );\n\n  const nativeHttpModule = isHttps ? https : http;\n  const keepAlive = options.keepAlive === undefined ? false : options.keepAlive;\n\n  // TODO(v10): Evaluate if we can set keepAlive to true. This would involve testing for memory leaks in older node\n  // versions(>= 8) as they had memory leaks when using it: #2555\n  const agent = proxy\n    ? (new HttpsProxyAgent(proxy) as http.Agent)\n    : new nativeHttpModule.Agent({ keepAlive, maxSockets: 30, timeout: 2000 });\n\n  const requestExecutor = createRequestExecutor(options, options.httpModule ?? nativeHttpModule, agent);\n  return createTransport(options, requestExecutor);\n}\n\n/**\n * Honors the `no_proxy` env variable with the highest priority to allow for hosts exclusion.\n *\n * @param transportUrl The URL the transport intends to send events to.\n * @param proxy The client configured proxy.\n * @returns A proxy the transport should use.\n */\nfunction applyNoProxyOption(transportUrlSegments: URL, proxy: string | undefined): string | undefined {\n  const { no_proxy } = process.env;\n\n  const urlIsExemptFromProxy = no_proxy\n    ?.split(',')\n    .some(\n      exemption => transportUrlSegments.host.endsWith(exemption) || transportUrlSegments.hostname.endsWith(exemption),\n    );\n\n  if (urlIsExemptFromProxy) {\n    return undefined;\n  } else {\n    return proxy;\n  }\n}\n\n/**\n * Creates a RequestExecutor to be used with `createTransport`.\n */\nfunction createRequestExecutor(\n  options: NodeTransportOptions,\n  httpModule: HTTPModule,\n  agent: http.Agent,\n): TransportRequestExecutor {\n  const { hostname, pathname, port, protocol, search } = new URL(options.url);\n  return function makeRequest(request: TransportRequest): Promise<TransportMakeRequestResponse> {\n    return new Promise((resolve, reject) => {\n      // This ensures we do not generate any spans in OpenTelemetry for the transport\n      suppressTracing(() => {\n        let body = streamFromBody(request.body);\n\n        const headers: Record<string, string> = { ...options.headers };\n\n        if (request.body.length > GZIP_THRESHOLD) {\n          headers['content-encoding'] = 'gzip';\n          body = body.pipe(createGzip());\n        }\n\n        const req = httpModule.request(\n          {\n            method: 'POST',\n            agent,\n            headers,\n            hostname,\n            path: `${pathname}${search}`,\n            port,\n            protocol,\n            ca: options.caCerts,\n          },\n          res => {\n            res.on('data', () => {\n              // Drain socket\n            });\n\n            res.on('end', () => {\n              // Drain socket\n            });\n\n            res.setEncoding('utf8');\n\n            // \"Key-value pairs of header names and values. Header names are lower-cased.\"\n            // https://nodejs.org/api/http.html#http_message_headers\n            const retryAfterHeader = res.headers['retry-after'] ?? null;\n            const rateLimitsHeader = res.headers['x-sentry-rate-limits'] ?? null;\n\n            resolve({\n              statusCode: res.statusCode,\n              headers: {\n                'retry-after': retryAfterHeader,\n                'x-sentry-rate-limits': Array.isArray(rateLimitsHeader)\n                  ? rateLimitsHeader[0] || null\n                  : rateLimitsHeader,\n              },\n            });\n          },\n        );\n\n        req.on('error', reject);\n        body.pipe(req);\n      });\n    });\n  };\n}\n"], "names": ["Readable", "consoleSandbox", "createTransport", "HttpsProxyAgent", "suppressTracing", "createGzip"], "mappings": ";;;;;;;;;AA4BA,yDAAA;AACA,MAAM,cAAA,GAAiB,IAAA,GAAO,EAAE;AAEhC;;;CAGA,GACA,SAAS,cAAc,CAAC,IAAI,EAAiC;IAC3D,OAAO,IAAIA,YAAAA,QAAQ,CAAC;QAClB,IAAI,GAAG;YACL,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;YACf,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;QACrB,CAAK;IACL,CAAG,CAAC;AACJ;AAEA;;CAEA,GACO,SAAS,iBAAiB,CAAC,OAAO,EAAmC;IAC1E,IAAI,WAAW;IAEf,IAAI;QACF,WAAA,GAAc,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC;IACtC,CAAE,CAAE,OAAO,CAAC,EAAE;QACVC,KAAAA,cAAc,CAAC,MAAM;YACzB,sCAAA;YACM,OAAO,CAAC,IAAI,CACV,yHAAyH;QAEjI,CAAK,CAAC;QACF,OAAOC,KAAAA,eAAe,CAAC,OAAO,EAAE,IAAM,OAAO,CAAC,OAAO,CAAC,CAAA,CAAE,CAAC,CAAC;IAC9D;IAEE,MAAM,OAAA,GAAU,WAAW,CAAC,QAAA,KAAa,QAAQ;IAEnD,2EAAA;IACA,wGAAA;IACE,MAAM,KAAA,GAAQ,kBAAkB,CAC9B,WAAW,EACX,OAAO,CAAC,KAAA,IAAA,CAAU,OAAA,GAAU,OAAO,CAAC,GAAG,CAAC,WAAA,GAAc,SAAS,CAAA,IAAK,OAAO,CAAC,GAAG,CAAC,UAAU;IAG5F,MAAM,gBAAA,GAAmB,UAAU,KAAA,GAAQ,IAAI;IAC/C,MAAM,SAAA,GAAY,OAAO,CAAC,SAAA,KAAc,SAAA,GAAY,KAAA,GAAQ,OAAO,CAAC,SAAS;IAE/E,iHAAA;IACA,+DAAA;IACE,MAAM,QAAQ,QACT,IAAIC,MAAAA,eAAe,CAAC,KAAK,CAAA,GAC1B,IAAI,gBAAgB,CAAC,KAAK,CAAC;QAAE,SAAS;QAAE,UAAU,EAAE,EAAE;QAAE,OAAO,EAAE,IAAA;IAAA,CAAM,CAAC;IAE5E,MAAM,eAAA,GAAkB,qBAAqB,CAAC,OAAO,EAAE,OAAO,CAAC,UAAA,IAAc,gBAAgB,EAAE,KAAK,CAAC;IACrG,OAAOD,KAAAA,eAAe,CAAC,OAAO,EAAE,eAAe,CAAC;AAClD;AAEA;;;;;;CAMA,GACA,SAAS,kBAAkB,CAAC,oBAAoB,EAAO,KAAK,EAA0C;IACpG,MAAM,EAAE,QAAA,EAAA,GAAa,OAAO,CAAC,GAAG;IAEhC,MAAM,uBAAuB,UACzB,KAAK,CAAC,GAAG,EACV,IAAI,EACH,YAAa,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAA,IAAK,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC;IAGnH,IAAI,oBAAoB,EAAE;QACxB,OAAO,SAAS;IACpB,OAAS;QACL,OAAO,KAAK;IAChB;AACA;AAEA;;CAEA,GACA,SAAS,qBAAqB,CAC5B,OAAO,EACP,UAAU,EACV,KAAK;IAEL,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAA,EAAO,GAAI,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC;IAC3E,OAAO,SAAS,WAAW,CAAC,OAAO,EAA2D;QAC5F,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAK;YAC5C,+EAAA;YACME,KAAAA,eAAe,CAAC,MAAM;gBACpB,IAAI,OAAO,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC;gBAEvC,MAAM,OAAO,GAA2B;oBAAE,GAAG,OAAO,CAAC,OAAA;gBAAA,CAAS;gBAE9D,IAAI,OAAO,CAAC,IAAI,CAAC,MAAA,GAAS,cAAc,EAAE;oBACxC,OAAO,CAAC,kBAAkB,CAAA,GAAI,MAAM;oBACpC,IAAA,GAAO,IAAI,CAAC,IAAI,CAACC,UAAAA,UAAU,EAAE,CAAC;gBACxC;gBAEQ,MAAM,GAAA,GAAM,UAAU,CAAC,OAAO,CAC5B;oBACE,MAAM,EAAE,MAAM;oBACd,KAAK;oBACL,OAAO;oBACP,QAAQ;oBACR,IAAI,EAAE,CAAC,EAAA,QAAA,CAAA,EAAA,MAAA,CAAA,CAAA;oBACA,IAAA;oBACA,QAAA;oBACA,EAAA,EAAA,OAAA,CAAA,OAAA;gBACA,CAAA,GACA,GAAA,IAAA;oBACA,GAAA,CAAA,EAAA,CAAA,MAAA,EAAA,MAAA;oBACA,eAAA;oBACA,CAAA,CAAA;oBAEA,GAAA,CAAA,EAAA,CAAA,KAAA,EAAA,MAAA;oBACA,eAAA;oBACA,CAAA,CAAA;oBAEA,GAAA,CAAA,WAAA,CAAA,MAAA,CAAA;oBAEA,8EAAA;oBACA,wDAAA;oBACA,MAAA,gBAAA,GAAA,GAAA,CAAA,OAAA,CAAA,aAAA,CAAA,IAAA,IAAA;oBACA,MAAA,gBAAA,GAAA,GAAA,CAAA,OAAA,CAAA,sBAAA,CAAA,IAAA,IAAA;oBAEA,OAAA,CAAA;wBACA,UAAA,EAAA,GAAA,CAAA,UAAA;wBACA,OAAA,EAAA;4BACA,aAAA,EAAA,gBAAA;4BACA,sBAAA,EAAA,KAAA,CAAA,OAAA,CAAA,gBAAA,IACA,gBAAA,CAAA,CAAA,CAAA,IAAA,OACA,gBAAA;wBACA,CAAA;oBACA,CAAA,CAAA;gBACA,CAAA;gBAGA,GAAA,CAAA,EAAA,CAAA,OAAA,EAAA,MAAA,CAAA;gBACA,IAAA,CAAA,IAAA,CAAA,GAAA,CAAA;YACA,CAAA,CAAA;QACA,CAAA,CAAA;IACA,CAAA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3977, "column": 0}, "map": {"version": 3, "file": "envToBool.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/utils/envToBool.ts"], "sourcesContent": ["export const FALSY_ENV_VALUES = new Set(['false', 'f', 'n', 'no', 'off', '0']);\nexport const TRUTHY_ENV_VALUES = new Set(['true', 't', 'y', 'yes', 'on', '1']);\n\nexport type StrictBoolCast = {\n  strict: true;\n};\n\nexport type LooseBoolCast = {\n  strict?: false;\n};\n\nexport type BoolCastOptions = StrictBoolCast | LooseBoolCast;\n\nexport function envToBool(value: unknown, options?: LooseBoolCast): boolean;\nexport function envToBool(value: unknown, options: StrictBoolCast): boolean | null;\nexport function envToBool(value: unknown, options?: BoolCastOptions): boolean | null;\n/**\n * A helper function which casts an ENV variable value to `true` or `false` using the constants defined above.\n * In strict mode, it may return `null` if the value doesn't match any of the predefined values.\n *\n * @param value The value of the env variable\n * @param options -- Only has `strict` key for now, which requires a strict match for `true` in TRUTHY_ENV_VALUES\n * @returns true/false if the lowercase value matches the predefined values above. If not, null in strict mode,\n *          and Boolean(value) in loose mode.\n */\nexport function envToBool(value: unknown, options?: BoolCastOptions): boolean | null {\n  const normalized = String(value).toLowerCase();\n\n  if (FALSY_ENV_VALUES.has(normalized)) {\n    return false;\n  }\n\n  if (TRUTHY_ENV_VALUES.has(normalized)) {\n    return true;\n  }\n\n  return options?.strict ? null : Boolean(value);\n}\n"], "names": [], "mappings": ";;;MAAa,gBAAA,GAAmB,IAAI,GAAG,CAAC;IAAC,OAAO;IAAE,GAAG;IAAE,GAAG;IAAE,IAAI;IAAE,KAAK;IAAE,GAAG;CAAC;MAChE,iBAAA,GAAoB,IAAI,GAAG,CAAC;IAAC,MAAM;IAAE,GAAG;IAAE,GAAG;IAAE,KAAK;IAAE,IAAI;IAAE,GAAG;CAAC;AAe7E;;;;;;;;CAQA,GACO,SAAS,SAAS,CAAC,KAAK,EAAW,OAAO,EAAoC;IACnF,MAAM,UAAA,GAAa,MAAM,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE;IAE9C,IAAI,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;QACpC,OAAO,KAAK;IAChB;IAEE,IAAI,iBAAiB,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;QACrC,OAAO,IAAI;IACf;IAEE,OAAO,OAAO,EAAE,MAAA,GAAS,OAAO,OAAO,CAAC,KAAK,CAAC;AAChD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4022, "column": 0}, "map": {"version": 3, "file": "module.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/utils/module.ts"], "sourcesContent": ["import { posix, sep } from 'node:path';\nimport { dirname } from '@sentry/core';\n\n/** normalizes Windows paths */\nfunction normalizeWindowsPath(path: string): string {\n  return path\n    .replace(/^[A-Z]:/, '') // remove Windows-style prefix\n    .replace(/\\\\/g, '/'); // replace all `\\` instances with `/`\n}\n\n/** Creates a function that gets the module name from a filename */\nexport function createGetModuleFromFilename(\n  basePath: string = process.argv[1] ? dirname(process.argv[1]) : process.cwd(),\n  isWindows: boolean = sep === '\\\\',\n): (filename: string | undefined) => string | undefined {\n  const normalizedBase = isWindows ? normalizeWindowsPath(basePath) : basePath;\n\n  return (filename: string | undefined) => {\n    if (!filename) {\n      return;\n    }\n\n    const normalizedFilename = isWindows ? normalizeWindowsPath(filename) : filename;\n\n    // eslint-disable-next-line prefer-const\n    let { dir, base: file, ext } = posix.parse(normalizedFilename);\n\n    if (ext === '.js' || ext === '.mjs' || ext === '.cjs') {\n      file = file.slice(0, ext.length * -1);\n    }\n\n    // The file name might be URI-encoded which we want to decode to\n    // the original file name.\n    const decodedFile = decodeURIComponent(file);\n\n    if (!dir) {\n      // No dirname whatsoever\n      dir = '.';\n    }\n\n    const n = dir.lastIndexOf('/node_modules');\n    if (n > -1) {\n      return `${dir.slice(n + 14).replace(/\\//g, '.')}:${decodedFile}`;\n    }\n\n    // Let's see if it's a part of the main module\n    // To be a part of main module, it has to share the same base\n    if (dir.startsWith(normalizedBase)) {\n      const moduleName = dir.slice(normalizedBase.length + 1).replace(/\\//g, '.');\n      return moduleName ? `${moduleName}:${decodedFile}` : decodedFile;\n    }\n\n    return decodedFile;\n  };\n}\n"], "names": ["dirname", "sep", "posix"], "mappings": ";;;;;AAGA,6BAAA,GACA,SAAS,oBAAoB,CAAC,IAAI,EAAkB;IAClD,OAAO,KACJ,OAAO,CAAC,SAAS,EAAE,EAAE,CAAA,CAAA,8BAAA;KACrB,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA,CAAA,qCAAA;AACxB;AAEA,iEAAA,GACO,SAAS,2BAA2B,CACzC,QAAQ,GAAW,OAAO,CAAC,IAAI,CAAC,CAAC,CAAA,GAAIA,KAAAA,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA,GAAI,OAAO,CAAC,GAAG,EAAE,EAC7E,SAAS,GAAYC,UAAAA,GAAA,KAAQ,IAAI;IAEjC,MAAM,cAAA,GAAiB,SAAA,GAAY,oBAAoB,CAAC,QAAQ,CAAA,GAAI,QAAQ;IAE5E,OAAO,CAAC,QAAQ,KAAyB;QACvC,IAAI,CAAC,QAAQ,EAAE;YACb;QACN;QAEI,MAAM,kBAAA,GAAqB,SAAA,GAAY,oBAAoB,CAAC,QAAQ,CAAA,GAAI,QAAQ;QAEpF,wCAAA;QACI,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,GAAA,EAAI,GAAIC,UAAAA,KAAK,CAAC,KAAK,CAAC,kBAAkB,CAAC;QAE9D,IAAI,GAAA,KAAQ,KAAA,IAAS,GAAA,KAAQ,MAAA,IAAU,GAAA,KAAQ,MAAM,EAAE;YACrD,IAAA,GAAO,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,MAAA,GAAS,CAAA,CAAE,CAAC;QAC3C;QAEA,gEAAA;QACA,0BAAA;QACI,MAAM,WAAA,GAAc,kBAAkB,CAAC,IAAI,CAAC;QAE5C,IAAI,CAAC,GAAG,EAAE;YACd,wBAAA;YACM,GAAA,GAAM,GAAG;QACf;QAEI,MAAM,IAAI,GAAG,CAAC,WAAW,CAAC,eAAe,CAAC;QAC1C,IAAI,CAAA,GAAI,CAAA,CAAE,EAAE;YACV,OAAO,CAAC,EAAA,GAAA,CAAA,KAAA,CAAA,CAAA,GAAA,EAAA,CAAA,CAAA,OAAA,CAAA,KAAA,EAAA,GAAA,CAAA,CAAA,CAAA,EAAA,WAAA,CAAA,CAAA;QACA;QAEA,8CAAA;QACA,6DAAA;QACA,IAAA,GAAA,CAAA,UAAA,CAAA,cAAA,CAAA,EAAA;YACA,MAAA,UAAA,GAAA,GAAA,CAAA,KAAA,CAAA,cAAA,CAAA,MAAA,GAAA,CAAA,CAAA,CAAA,OAAA,CAAA,KAAA,EAAA,GAAA,CAAA;YACA,OAAA,UAAA,GAAA,CAAA,EAAA,UAAA,CAAA,CAAA,EAAA,WAAA,CAAA,CAAA,GAAA,WAAA;QACA;QAEA,OAAA,WAAA;IACA,CAAA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4069, "column": 0}, "map": {"version": 3, "file": "api.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/sdk/api.ts"], "sourcesContent": ["// PUBLIC APIS\n\nimport type { StackParser } from '@sentry/core';\nimport { createStackParser, GLOBAL_OBJ, nodeStackLineParser } from '@sentry/core';\nimport { createGetModuleFromFilename } from '../utils/module';\n\n/**\n * Returns a release dynamically from environment variables.\n */\n// eslint-disable-next-line complexity\nexport function getSentryRelease(fallback?: string): string | undefined {\n  // Always read first as <PERSON><PERSON> takes this as precedence\n  if (process.env.SENTRY_RELEASE) {\n    return process.env.SENTRY_RELEASE;\n  }\n\n  // This supports the variable that sentry-webpack-plugin injects\n  if (GLOBAL_OBJ.SENTRY_RELEASE?.id) {\n    return GLOBAL_OBJ.SENTRY_RELEASE.id;\n  }\n\n  // This list is in approximate alpha order, separated into 3 categories:\n  // 1. Git providers\n  // 2. CI providers with specific environment variables (has the provider name in the variable name)\n  // 3. CI providers with generic environment variables (checked for last to prevent possible false positives)\n\n  const possibleReleaseNameOfGitProvider =\n    // GitHub Actions - https://help.github.com/en/actions/configuring-and-managing-workflows/using-environment-variables#default-environment-variables\n    process.env['GITHUB_SHA'] ||\n    // GitLab CI - https://docs.gitlab.com/ee/ci/variables/predefined_variables.html\n    process.env['CI_MERGE_REQUEST_SOURCE_BRANCH_SHA'] ||\n    process.env['CI_BUILD_REF'] ||\n    process.env['CI_COMMIT_SHA'] ||\n    // Bitbucket - https://support.atlassian.com/bitbucket-cloud/docs/variables-and-secrets/\n    process.env['BITBUCKET_COMMIT'];\n\n  const possibleReleaseNameOfCiProvidersWithSpecificEnvVar =\n    // AppVeyor - https://www.appveyor.com/docs/environment-variables/\n    process.env['APPVEYOR_PULL_REQUEST_HEAD_COMMIT'] ||\n    process.env['APPVEYOR_REPO_COMMIT'] ||\n    // AWS CodeBuild - https://docs.aws.amazon.com/codebuild/latest/userguide/build-env-ref-env-vars.html\n    process.env['CODEBUILD_RESOLVED_SOURCE_VERSION'] ||\n    // AWS Amplify - https://docs.aws.amazon.com/amplify/latest/userguide/environment-variables.html\n    process.env['AWS_COMMIT_ID'] ||\n    // Azure Pipelines - https://docs.microsoft.com/en-us/azure/devops/pipelines/build/variables?view=azure-devops&tabs=yaml\n    process.env['BUILD_SOURCEVERSION'] ||\n    // Bitrise - https://devcenter.bitrise.io/builds/available-environment-variables/\n    process.env['GIT_CLONE_COMMIT_HASH'] ||\n    // Buddy CI - https://buddy.works/docs/pipelines/environment-variables#default-environment-variables\n    process.env['BUDDY_EXECUTION_REVISION'] ||\n    // Builtkite - https://buildkite.com/docs/pipelines/environment-variables\n    process.env['BUILDKITE_COMMIT'] ||\n    // CircleCI - https://circleci.com/docs/variables/\n    process.env['CIRCLE_SHA1'] ||\n    // Cirrus CI - https://cirrus-ci.org/guide/writing-tasks/#environment-variables\n    process.env['CIRRUS_CHANGE_IN_REPO'] ||\n    // Codefresh - https://codefresh.io/docs/docs/codefresh-yaml/variables/\n    process.env['CF_REVISION'] ||\n    // Codemagic - https://docs.codemagic.io/yaml-basic-configuration/environment-variables/\n    process.env['CM_COMMIT'] ||\n    // Cloudflare Pages - https://developers.cloudflare.com/pages/platform/build-configuration/#environment-variables\n    process.env['CF_PAGES_COMMIT_SHA'] ||\n    // Drone - https://docs.drone.io/pipeline/environment/reference/\n    process.env['DRONE_COMMIT_SHA'] ||\n    // Flightcontrol - https://www.flightcontrol.dev/docs/guides/flightcontrol/environment-variables#built-in-environment-variables\n    process.env['FC_GIT_COMMIT_SHA'] ||\n    // Heroku #1 https://devcenter.heroku.com/articles/heroku-ci\n    process.env['HEROKU_TEST_RUN_COMMIT_VERSION'] ||\n    // Heroku #2 https://docs.sentry.io/product/integrations/deployment/heroku/#configure-releases\n    process.env['HEROKU_SLUG_COMMIT'] ||\n    // Railway - https://docs.railway.app/reference/variables#git-variables\n    process.env['RAILWAY_GIT_COMMIT_SHA'] ||\n    // Render - https://render.com/docs/environment-variables\n    process.env['RENDER_GIT_COMMIT'] ||\n    // Semaphore CI - https://docs.semaphoreci.com/ci-cd-environment/environment-variables\n    process.env['SEMAPHORE_GIT_SHA'] ||\n    // TravisCI - https://docs.travis-ci.com/user/environment-variables/#default-environment-variables\n    process.env['TRAVIS_PULL_REQUEST_SHA'] ||\n    // Vercel - https://vercel.com/docs/v2/build-step#system-environment-variables\n    process.env['VERCEL_GIT_COMMIT_SHA'] ||\n    process.env['VERCEL_GITHUB_COMMIT_SHA'] ||\n    process.env['VERCEL_GITLAB_COMMIT_SHA'] ||\n    process.env['VERCEL_BITBUCKET_COMMIT_SHA'] ||\n    // Zeit (now known as Vercel)\n    process.env['ZEIT_GITHUB_COMMIT_SHA'] ||\n    process.env['ZEIT_GITLAB_COMMIT_SHA'] ||\n    process.env['ZEIT_BITBUCKET_COMMIT_SHA'];\n\n  const possibleReleaseNameOfCiProvidersWithGenericEnvVar =\n    // CloudBees CodeShip - https://docs.cloudbees.com/docs/cloudbees-codeship/latest/pro-builds-and-configuration/environment-variables\n    process.env['CI_COMMIT_ID'] ||\n    // Coolify - https://coolify.io/docs/knowledge-base/environment-variables\n    process.env['SOURCE_COMMIT'] ||\n    // Heroku #3 https://devcenter.heroku.com/changelog-items/630\n    process.env['SOURCE_VERSION'] ||\n    // Jenkins - https://plugins.jenkins.io/git/#environment-variables\n    process.env['GIT_COMMIT'] ||\n    // Netlify - https://docs.netlify.com/configure-builds/environment-variables/#build-metadata\n    process.env['COMMIT_REF'] ||\n    // TeamCity - https://www.jetbrains.com/help/teamcity/predefined-build-parameters.html\n    process.env['BUILD_VCS_NUMBER'] ||\n    // Woodpecker CI - https://woodpecker-ci.org/docs/usage/environment\n    process.env['CI_COMMIT_SHA'];\n\n  return (\n    possibleReleaseNameOfGitProvider ||\n    possibleReleaseNameOfCiProvidersWithSpecificEnvVar ||\n    possibleReleaseNameOfCiProvidersWithGenericEnvVar ||\n    fallback\n  );\n}\n\n/** Node.js stack parser */\nexport const defaultStackParser: StackParser = createStackParser(nodeStackLineParser(createGetModuleFromFilename()));\n"], "names": ["GLOBAL_OBJ", "createStackParser", "nodeStackLineParser", "createGetModuleFromFilename"], "mappings": ";;;;;AAMA;;CAEA,GACA,sCAAA;AACO,SAAS,gBAAgB,CAAC,QAAQ,EAA+B;IACxE,uDAAA;IACE,IAAI,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE;QAC9B,OAAO,OAAO,CAAC,GAAG,CAAC,cAAc;IACrC;IAEA,gEAAA;IACE,IAAIA,KAAAA,UAAU,CAAC,cAAc,EAAE,EAAE,EAAE;QACjC,OAAOA,KAAAA,UAAU,CAAC,cAAc,CAAC,EAAE;IACvC;IAEA,wEAAA;IACA,mBAAA;IACA,mGAAA;IACA,4GAAA;IAEE,MAAM,gCAAA,GACR,mJAAA;IACI,OAAO,CAAC,GAAG,CAAC,YAAY,CAAA,IAC5B,gFAAA;IACI,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAA,IAChD,OAAO,CAAC,GAAG,CAAC,cAAc,CAAA,IAC1B,OAAO,CAAC,GAAG,CAAC,eAAe,CAAA,IAC/B,wFAAA;IACI,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC;IAEjC,MAAM,kDAAA,GACR,kEAAA;IACI,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAA,IAC/C,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAA,IACtC,qGAAA;IACI,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAA,IACnD,gGAAA;IACI,OAAO,CAAC,GAAG,CAAC,eAAe,CAAA,IAC/B,wHAAA;IACI,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAA,IACrC,iFAAA;IACI,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAA,IACvC,oGAAA;IACI,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAA,IAC1C,yEAAA;IACI,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAA,IAClC,kDAAA;IACI,OAAO,CAAC,GAAG,CAAC,aAAa,CAAA,IAC7B,+EAAA;IACI,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAA,IACvC,uEAAA;IACI,OAAO,CAAC,GAAG,CAAC,aAAa,CAAA,IAC7B,wFAAA;IACI,OAAO,CAAC,GAAG,CAAC,WAAW,CAAA,IAC3B,iHAAA;IACI,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAA,IACrC,gEAAA;IACI,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAA,IAClC,+HAAA;IACI,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAA,IACnC,4DAAA;IACI,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAA,IAChD,8FAAA;IACI,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAA,IACpC,uEAAA;IACI,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAA,IACxC,yDAAA;IACI,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAA,IACnC,sFAAA;IACI,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAA,IACnC,kGAAA;IACI,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAA,IACzC,8EAAA;IACI,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAA,IACnC,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAA,IACtC,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAA,IACtC,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAA,IAC7C,6BAAA;IACI,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAA,IACpC,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAA,IACpC,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC;IAE1C,MAAM,iDAAA,GACR,oIAAA;IACI,OAAO,CAAC,GAAG,CAAC,cAAc,CAAA,IAC9B,yEAAA;IACI,OAAO,CAAC,GAAG,CAAC,eAAe,CAAA,IAC/B,6DAAA;IACI,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAA,IAChC,kEAAA;IACI,OAAO,CAAC,GAAG,CAAC,YAAY,CAAA,IAC5B,4FAAA;IACI,OAAO,CAAC,GAAG,CAAC,YAAY,CAAA,IAC5B,sFAAA;IACI,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAA,IAClC,mEAAA;IACI,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC;IAE9B,OACE,gCAAA,IACA,kDAAA,IACA,iDAAA,IACA;AAEJ;AAEA,yBAAA,GACO,MAAM,kBAAkB,GAAgBC,KAAAA,iBAAiB,CAACC,KAAAA,mBAAmB,CAACC,SAAAA,2BAA2B,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4135, "column": 0}, "map": {"version": 3, "file": "client.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/sdk/client.ts"], "sourcesContent": ["import * as os from 'node:os';\nimport type { Tracer } from '@opentelemetry/api';\nimport { trace } from '@opentelemetry/api';\nimport { registerInstrumentations } from '@opentelemetry/instrumentation';\nimport type { BasicTracerProvider } from '@opentelemetry/sdk-trace-base';\nimport type { DynamicSamplingContext, Scope, ServerRuntimeClientOptions, TraceContext } from '@sentry/core';\nimport { _INTERNAL_flushLogsBuffer, applySdkMetadata, debug, SDK_VERSION, ServerRuntimeClient } from '@sentry/core';\nimport { getTraceContextForScope } from '@sentry/opentelemetry';\nimport { isMainThread, threadId } from 'worker_threads';\nimport { DEBUG_BUILD } from '../debug-build';\nimport type { NodeClientOptions } from '../types';\n\nconst DEFAULT_CLIENT_REPORT_FLUSH_INTERVAL_MS = 60_000; // 60s was chosen arbitrarily\n\n/** A client for using Sentry with Node & OpenTelemetry. */\nexport class NodeClient extends ServerRuntimeClient<NodeClientOptions> {\n  public traceProvider: BasicTracerProvider | undefined;\n  private _tracer: Tracer | undefined;\n  private _clientReportInterval: NodeJS.Timeout | undefined;\n  private _clientReportOnExitFlushListener: (() => void) | undefined;\n  private _logOnExitFlushListener: (() => void) | undefined;\n\n  public constructor(options: NodeClientOptions) {\n    const serverName =\n      options.includeServerName === false\n        ? undefined\n        : options.serverName || global.process.env.SENTRY_NAME || os.hostname();\n\n    const clientOptions: ServerRuntimeClientOptions = {\n      ...options,\n      platform: 'node',\n      runtime: { name: 'node', version: global.process.version },\n      serverName,\n    };\n\n    if (options.openTelemetryInstrumentations) {\n      registerInstrumentations({\n        instrumentations: options.openTelemetryInstrumentations,\n      });\n    }\n\n    applySdkMetadata(clientOptions, 'node');\n\n    debug.log(`Initializing Sentry: process: ${process.pid}, thread: ${isMainThread ? 'main' : `worker-${threadId}`}.`);\n\n    super(clientOptions);\n\n    const { enableLogs, _experiments } = this.getOptions();\n    // eslint-disable-next-line deprecation/deprecation\n    const shouldEnableLogs = enableLogs ?? _experiments?.enableLogs;\n    if (shouldEnableLogs) {\n      this._logOnExitFlushListener = () => {\n        _INTERNAL_flushLogsBuffer(this);\n      };\n\n      if (serverName) {\n        this.on('beforeCaptureLog', log => {\n          log.attributes = {\n            ...log.attributes,\n            'server.address': serverName,\n          };\n        });\n      }\n\n      process.on('beforeExit', this._logOnExitFlushListener);\n    }\n  }\n\n  /** Get the OTEL tracer. */\n  public get tracer(): Tracer {\n    if (this._tracer) {\n      return this._tracer;\n    }\n\n    const name = '@sentry/node';\n    const version = SDK_VERSION;\n    const tracer = trace.getTracer(name, version);\n    this._tracer = tracer;\n\n    return tracer;\n  }\n\n  // Eslint ignore explanation: This is already documented in super.\n  // eslint-disable-next-line jsdoc/require-jsdoc\n  public async flush(timeout?: number): Promise<boolean> {\n    const provider = this.traceProvider;\n\n    await provider?.forceFlush();\n\n    if (this.getOptions().sendClientReports) {\n      this._flushOutcomes();\n    }\n\n    return super.flush(timeout);\n  }\n\n  // Eslint ignore explanation: This is already documented in super.\n  // eslint-disable-next-line jsdoc/require-jsdoc\n  public close(timeout?: number | undefined): PromiseLike<boolean> {\n    if (this._clientReportInterval) {\n      clearInterval(this._clientReportInterval);\n    }\n\n    if (this._clientReportOnExitFlushListener) {\n      process.off('beforeExit', this._clientReportOnExitFlushListener);\n    }\n\n    if (this._logOnExitFlushListener) {\n      process.off('beforeExit', this._logOnExitFlushListener);\n    }\n\n    return super.close(timeout);\n  }\n\n  /**\n   * Will start tracking client reports for this client.\n   *\n   * NOTICE: This method will create an interval that is periodically called and attach a `process.on('beforeExit')`\n   * hook. To clean up these resources, call `.close()` when you no longer intend to use the client. Not doing so will\n   * result in a memory leak.\n   */\n  // The reason client reports need to be manually activated with this method instead of just enabling them in a\n  // constructor, is that if users periodically and unboundedly create new clients, we will create more and more\n  // intervals and beforeExit listeners, thus leaking memory. In these situations, users are required to call\n  // `client.close()` in order to dispose of the acquired resources.\n  // We assume that calling this method in Sentry.init() is a sensible default, because calling Sentry.init() over and\n  // over again would also result in memory leaks.\n  // Note: We have experimented with using `FinalizationRegisty` to clear the interval when the client is garbage\n  // collected, but it did not work, because the cleanup function never got called.\n  public startClientReportTracking(): void {\n    const clientOptions = this.getOptions();\n    if (clientOptions.sendClientReports) {\n      this._clientReportOnExitFlushListener = () => {\n        this._flushOutcomes();\n      };\n\n      this._clientReportInterval = setInterval(() => {\n        DEBUG_BUILD && debug.log('Flushing client reports based on interval.');\n        this._flushOutcomes();\n      }, clientOptions.clientReportFlushInterval ?? DEFAULT_CLIENT_REPORT_FLUSH_INTERVAL_MS)\n        // Unref is critical for not preventing the process from exiting because the interval is active.\n        .unref();\n\n      process.on('beforeExit', this._clientReportOnExitFlushListener);\n    }\n  }\n\n  /** Custom implementation for OTEL, so we can handle scope-span linking. */\n  protected _getTraceInfoFromScope(\n    scope: Scope | undefined,\n  ): [dynamicSamplingContext: Partial<DynamicSamplingContext> | undefined, traceContext: TraceContext | undefined] {\n    if (!scope) {\n      return [undefined, undefined];\n    }\n\n    return getTraceContextForScope(this, scope);\n  }\n}\n"], "names": ["ServerRuntimeClient", "registerInstrumentations", "applySdkMetadata", "debug", "isMainThread", "threadId", "_INTERNAL_flushLogsBuffer", "SDK_VERSION", "trace", "DEBUG_BUILD", "getTraceContextForScope"], "mappings": ";;;;;;;;;;AAYA,MAAM,uCAAA,GAA0C,KAAM,CAAA,CAAA,6BAAA;AAEtD,yDAAA,GACO,MAAM,UAAA,SAAmBA,KAAAA,mBAAmB,CAAoB;IAO9D,WAAW,CAAC,OAAO,CAAqB;QAC7C,MAAM,UAAA,GACJ,OAAO,CAAC,iBAAA,KAAsB,QAC1B,YACA,OAAO,CAAC,UAAA,IAAc,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,WAAA,IAAe,EAAE,CAAC,QAAQ,EAAE;QAE3E,MAAM,aAAa,GAA+B;YAChD,GAAG,OAAO;YACV,QAAQ,EAAE,MAAM;YAChB,OAAO,EAAE;gBAAE,IAAI,EAAE,MAAM;gBAAE,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,OAAA;YAAA,CAAS;YAC1D,UAAU;QAChB,CAAK;QAED,IAAI,OAAO,CAAC,6BAA6B,EAAE;YACzCC,gBAAAA,wBAAwB,CAAC;gBACvB,gBAAgB,EAAE,OAAO,CAAC,6BAA6B;YAC/D,CAAO,CAAC;QACR;QAEIC,KAAAA,gBAAgB,CAAC,aAAa,EAAE,MAAM,CAAC;QAEvCC,KAAAA,KAAK,CAAC,GAAG,CAAC,CAAC,8BAA8B,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,EAAEC,eAAAA,YAAA,GAAe,MAAA,GAAS,CAAC,OAAO,EAAEC,eAAAA,QAAQ,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA;QAEA,KAAA,CAAA,aAAA,CAAA;QAEA,MAAA,EAAA,UAAA,EAAA,YAAA,EAAA,GAAA,IAAA,CAAA,UAAA,EAAA;QACA,mDAAA;QACA,MAAA,gBAAA,GAAA,UAAA,IAAA,YAAA,EAAA,UAAA;QACA,IAAA,gBAAA,EAAA;YACA,IAAA,CAAA,uBAAA,GAAA,MAAA;gBACAC,KAAAA,yBAAA,CAAA,IAAA,CAAA;YACA,CAAA;YAEA,IAAA,UAAA,EAAA;gBACA,IAAA,CAAA,EAAA,CAAA,kBAAA,GAAA,GAAA,IAAA;oBACA,GAAA,CAAA,UAAA,GAAA;wBACA,GAAA,GAAA,CAAA,UAAA;wBACA,gBAAA,EAAA,UAAA;oBACA,CAAA;gBACA,CAAA,CAAA;YACA;YAEA,OAAA,CAAA,EAAA,CAAA,YAAA,EAAA,IAAA,CAAA,uBAAA,CAAA;QACA;IACA;IAEA,yBAAA,GACA,IAAA,MAAA,GAAA;QACA,IAAA,IAAA,CAAA,OAAA,EAAA;YACA,OAAA,IAAA,CAAA,OAAA;QACA;QAEA,MAAA,IAAA,GAAA,cAAA;QACA,MAAA,OAAA,GAAAC,KAAAA,WAAA;QACA,MAAA,MAAA,GAAAC,IAAAA,KAAA,CAAA,SAAA,CAAA,IAAA,EAAA,OAAA,CAAA;QACA,IAAA,CAAA,OAAA,GAAA,MAAA;QAEA,OAAA,MAAA;IACA;IAEA,kEAAA;IACA,+CAAA;IACA,MAAA,KAAA,CAAA,OAAA,EAAA;QACA,MAAA,QAAA,GAAA,IAAA,CAAA,aAAA;QAEA,MAAA,QAAA,EAAA,UAAA,EAAA;QAEA,IAAA,IAAA,CAAA,UAAA,EAAA,CAAA,iBAAA,EAAA;YACA,IAAA,CAAA,cAAA,EAAA;QACA;QAEA,OAAA,KAAA,CAAA,KAAA,CAAA,OAAA,CAAA;IACA;IAEA,kEAAA;IACA,+CAAA;IACA,KAAA,CAAA,OAAA,EAAA;QACA,IAAA,IAAA,CAAA,qBAAA,EAAA;YACA,aAAA,CAAA,IAAA,CAAA,qBAAA,CAAA;QACA;QAEA,IAAA,IAAA,CAAA,gCAAA,EAAA;YACA,OAAA,CAAA,GAAA,CAAA,YAAA,EAAA,IAAA,CAAA,gCAAA,CAAA;QACA;QAEA,IAAA,IAAA,CAAA,uBAAA,EAAA;YACA,OAAA,CAAA,GAAA,CAAA,YAAA,EAAA,IAAA,CAAA,uBAAA,CAAA;QACA;QAEA,OAAA,KAAA,CAAA,KAAA,CAAA,OAAA,CAAA;IACA;IAEA;;;;;;GAMA,GACA,8GAAA;IACA,8GAAA;IACA,2GAAA;IACA,kEAAA;IACA,oHAAA;IACA,gDAAA;IACA,+GAAA;IACA,iFAAA;IACA,yBAAA,GAAA;QACA,MAAA,aAAA,GAAA,IAAA,CAAA,UAAA,EAAA;QACA,IAAA,aAAA,CAAA,iBAAA,EAAA;YACA,IAAA,CAAA,gCAAA,GAAA,MAAA;gBACA,IAAA,CAAA,cAAA,EAAA;YACA,CAAA;YAEA,IAAA,CAAA,qBAAA,GAAA,WAAA,CAAA,MAAA;gBACAC,WAAAA,WAAA,IAAAN,KAAAA,KAAA,CAAA,GAAA,CAAA,4CAAA,CAAA;gBACA,IAAA,CAAA,cAAA,EAAA;YACA,CAAA,EAAA,aAAA,CAAA,yBAAA,IAAA,uCAAA,CACA,gGAAA;aACA,KAAA,EAAA;YAEA,OAAA,CAAA,EAAA,CAAA,YAAA,EAAA,IAAA,CAAA,gCAAA,CAAA;QACA;IACA;IAEA,yEAAA,GACA,sBAAA,CACA,KAAA,EACA;QACA,IAAA,CAAA,KAAA,EAAA;YACA,OAAA;gBAAA,SAAA;gBAAA,SAAA;aAAA;QACA;QAEA,OAAAO,cAAAA,uBAAA,CAAA,IAAA,EAAA,KAAA,CAAA;IACA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4262, "column": 0}, "map": {"version": 3, "file": "esmLoader.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/sdk/esmLoader.ts"], "sourcesContent": ["import { consoleSandbox, debug, GLOBAL_OBJ } from '@sentry/core';\nimport { createAddHookMessageChannel } from 'import-in-the-middle';\nimport moduleModule from 'module';\n\n/** Initialize the ESM loader. */\nexport function maybeInitializeEsmLoader(): void {\n  const [nodeMajor = 0, nodeMinor = 0] = process.versions.node.split('.').map(Number);\n\n  // Register hook was added in v20.6.0 and v18.19.0\n  if (nodeMajor >= 21 || (nodeMajor === 20 && nodeMinor >= 6) || (nodeMajor === 18 && nodeMinor >= 19)) {\n    if (!GLOBAL_OBJ._sentryEsmLoaderHookRegistered) {\n      try {\n        const { addHookMessagePort } = createAddHookMessageChannel();\n        // @ts-expect-error register is available in these versions\n        moduleModule.register('import-in-the-middle/hook.mjs', import.meta.url, {\n          data: { addHookMessagePort, include: [] },\n          transferList: [addHookMessagePort],\n        });\n      } catch (error) {\n        debug.warn('Failed to register ESM hook', error);\n      }\n    }\n  } else {\n    consoleSandbox(() => {\n      // eslint-disable-next-line no-console\n      console.warn(\n        `[Sentry] You are using Node.js v${process.versions.node} in ESM mode (\"import syntax\"). The Sentry Node.js SDK is not compatible with ESM in Node.js versions before 18.19.0 or before 20.6.0. Please either build your application with CommonJS (\"require() syntax\"), or upgrade your Node.js version.`,\n      );\n    });\n  }\n}\n"], "names": ["GLOBAL_OBJ", "createAddHookMessageChannel", "moduleModule", "debug", "consoleSandbox"], "mappings": ";;;;;;;AAIA,+BAAA,GACO,SAAS,wBAAwB,GAAS;IAC/C,MAAM,CAAC,SAAA,GAAY,CAAC,EAAE,SAAA,GAAY,CAAC,CAAA,GAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC;IAErF,kDAAA;IACE,IAAI,SAAA,IAAa,EAAA,IAAO,SAAA,KAAc,EAAA,IAAM,SAAA,IAAa,CAAC,CAAA,GAAM,SAAA,KAAc,EAAA,IAAM,SAAA,IAAa,EAAE,CAAC,CAAE;QACpG,IAAI,CAACA,KAAAA,UAAU,CAAC,8BAA8B,EAAE;YAC9C,IAAI;gBACF,MAAM,EAAE,kBAAA,EAAA,GAAuBC,kBAAAA,2BAA2B,EAAE;gBACpE,2DAAA;gBACQC,aAAAA,OAAY,CAAC,QAAQ,CAAC,+BAA+B,EAAE,OAAA,aAAA,cAAA,iEAAA,aAAA,CAAA,YAAA,IAAA,GAAA,0BAAA,uBAAA,OAAA,CAAA,WAAA,OAAA,YAAA,uBAAA,GAAA,IAAA,IAAA,IAAA,oBAAA,SAAA,OAAA,EAAA,IAAA,EAAe,AAAE;oBACtE,IAAI,EAAE;wBAAE,kBAAkB;wBAAE,OAAO,EAAE,EAAC;oBAAA,CAAG;oBACzC,YAAY,EAAE;wBAAC,kBAAkB;qBAAC;gBAC5C,CAAS,CAAC;YACV,CAAM,CAAE,OAAO,KAAK,EAAE;gBACdC,KAAAA,KAAK,CAAC,IAAI,CAAC,6BAA6B,EAAE,KAAK,CAAC;YACxD;QACA;IACA,OAAS;QACLC,KAAAA,cAAc,CAAC,MAAM;YACzB,sCAAA;YACM,OAAO,CAAC,IAAI,CACV,CAAC,gCAAgC,EAAE,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,gPAAgP,CAAC;QAElT,CAAK,CAAC;IACN;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4303, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/sdk/index.ts"], "sourcesContent": ["import type { Integration, Options } from '@sentry/core';\nimport {\n  applySdkMetadata,\n  consoleIntegration,\n  consoleSandbox,\n  debug,\n  functionToStringIntegration,\n  getCurrentScope,\n  getIntegrationsToSetup,\n  hasSpansEnabled,\n  inboundFiltersIntegration,\n  linkedErrorsIntegration,\n  propagationContextFromHeaders,\n  requestDataIntegration,\n  stackParserFromStackParserOptions,\n} from '@sentry/core';\nimport {\n  enhanceDscWithOpenTelemetryRootSpanName,\n  openTelemetrySetupCheck,\n  setOpenTelemetryContextAsyncContextStrategy,\n  setupEventContextTrace,\n} from '@sentry/opentelemetry';\nimport { DEBUG_BUILD } from '../debug-build';\nimport { childProcessIntegration } from '../integrations/childProcess';\nimport { nodeContextIntegration } from '../integrations/context';\nimport { contextLinesIntegration } from '../integrations/contextlines';\nimport { httpIntegration } from '../integrations/http';\nimport { localVariablesIntegration } from '../integrations/local-variables';\nimport { modulesIntegration } from '../integrations/modules';\nimport { nativeNodeFetchIntegration } from '../integrations/node-fetch';\nimport { onUncaughtExceptionIntegration } from '../integrations/onuncaughtexception';\nimport { onUnhandledRejectionIntegration } from '../integrations/onunhandledrejection';\nimport { processSessionIntegration } from '../integrations/processSession';\nimport { INTEGRATION_NAME as SPOTLIGHT_INTEGRATION_NAME, spotlightIntegration } from '../integrations/spotlight';\nimport { makeNodeTransport } from '../transports';\nimport type { NodeClientOptions, NodeOptions } from '../types';\nimport { isCjs } from '../utils/commonjs';\nimport { envToBool } from '../utils/envToBool';\nimport { defaultStackParser, getSentryRelease } from './api';\nimport { NodeClient } from './client';\nimport { maybeInitializeEsmLoader } from './esmLoader';\n\n/**\n * Get default integrations for the Node-Core SDK.\n */\nexport function getDefaultIntegrations(): Integration[] {\n  return [\n    // Common\n    // TODO(v10): Replace with `eventFiltersIntegration` once we remove the deprecated `inboundFiltersIntegration`\n    // eslint-disable-next-line deprecation/deprecation\n    inboundFiltersIntegration(),\n    functionToStringIntegration(),\n    linkedErrorsIntegration(),\n    requestDataIntegration(),\n    // Native Wrappers\n    consoleIntegration(),\n    httpIntegration(),\n    nativeNodeFetchIntegration(),\n    // Global Handlers\n    onUncaughtExceptionIntegration(),\n    onUnhandledRejectionIntegration(),\n    // Event Info\n    contextLinesIntegration(),\n    localVariablesIntegration(),\n    nodeContextIntegration(),\n    childProcessIntegration(),\n    processSessionIntegration(),\n    modulesIntegration(),\n  ];\n}\n\n/**\n * Initialize Sentry for Node.\n */\nexport function init(options: NodeOptions | undefined = {}): NodeClient | undefined {\n  return _init(options, getDefaultIntegrations);\n}\n\n/**\n * Initialize Sentry for Node, without any integrations added by default.\n */\nexport function initWithoutDefaultIntegrations(options: NodeOptions | undefined = {}): NodeClient {\n  return _init(options, () => []);\n}\n\n/**\n * Initialize Sentry for Node, without performance instrumentation.\n */\nfunction _init(\n  _options: NodeOptions | undefined = {},\n  getDefaultIntegrationsImpl: (options: Options) => Integration[],\n): NodeClient {\n  const options = getClientOptions(_options, getDefaultIntegrationsImpl);\n\n  if (options.debug === true) {\n    if (DEBUG_BUILD) {\n      debug.enable();\n    } else {\n      // use `console.warn` rather than `debug.warn` since by non-debug bundles have all `debug.x` statements stripped\n      consoleSandbox(() => {\n        // eslint-disable-next-line no-console\n        console.warn('[Sentry] Cannot initialize SDK with `debug` option using a non-debug bundle.');\n      });\n    }\n  }\n\n  if (!isCjs() && options.registerEsmLoaderHooks !== false) {\n    maybeInitializeEsmLoader();\n  }\n\n  setOpenTelemetryContextAsyncContextStrategy();\n\n  const scope = getCurrentScope();\n  scope.update(options.initialScope);\n\n  if (options.spotlight && !options.integrations.some(({ name }) => name === SPOTLIGHT_INTEGRATION_NAME)) {\n    options.integrations.push(\n      spotlightIntegration({\n        sidecarUrl: typeof options.spotlight === 'string' ? options.spotlight : undefined,\n      }),\n    );\n  }\n\n  applySdkMetadata(options, 'node-core');\n\n  const client = new NodeClient(options);\n  // The client is on the current scope, from where it generally is inherited\n  getCurrentScope().setClient(client);\n\n  client.init();\n\n  debug.log(`Running in ${isCjs() ? 'CommonJS' : 'ESM'} mode.`);\n\n  client.startClientReportTracking();\n\n  updateScopeFromEnvVariables();\n\n  enhanceDscWithOpenTelemetryRootSpanName(client);\n  setupEventContextTrace(client);\n\n  return client;\n}\n\n/**\n * Validate that your OpenTelemetry setup is correct.\n */\nexport function validateOpenTelemetrySetup(): void {\n  if (!DEBUG_BUILD) {\n    return;\n  }\n\n  const setup = openTelemetrySetupCheck();\n\n  const required: ReturnType<typeof openTelemetrySetupCheck> = ['SentryContextManager', 'SentryPropagator'];\n\n  if (hasSpansEnabled()) {\n    required.push('SentrySpanProcessor');\n  }\n\n  for (const k of required) {\n    if (!setup.includes(k)) {\n      debug.error(\n        `You have to set up the ${k}. Without this, the OpenTelemetry & Sentry integration will not work properly.`,\n      );\n    }\n  }\n\n  if (!setup.includes('SentrySampler')) {\n    debug.warn(\n      'You have to set up the SentrySampler. Without this, the OpenTelemetry & Sentry integration may still work, but sample rates set for the Sentry SDK will not be respected. If you use a custom sampler, make sure to use `wrapSamplingDecision`.',\n    );\n  }\n}\n\nfunction getClientOptions(\n  options: NodeOptions,\n  getDefaultIntegrationsImpl: (options: Options) => Integration[],\n): NodeClientOptions {\n  const release = getRelease(options.release);\n  const spotlight =\n    options.spotlight ?? envToBool(process.env.SENTRY_SPOTLIGHT, { strict: true }) ?? process.env.SENTRY_SPOTLIGHT;\n  const tracesSampleRate = getTracesSampleRate(options.tracesSampleRate);\n\n  const mergedOptions = {\n    ...options,\n    dsn: options.dsn ?? process.env.SENTRY_DSN,\n    environment: options.environment ?? process.env.SENTRY_ENVIRONMENT,\n    sendClientReports: options.sendClientReports ?? true,\n    transport: options.transport ?? makeNodeTransport,\n    stackParser: stackParserFromStackParserOptions(options.stackParser || defaultStackParser),\n    release,\n    tracesSampleRate,\n    spotlight,\n    debug: envToBool(options.debug ?? process.env.SENTRY_DEBUG),\n  };\n\n  const integrations = options.integrations;\n  const defaultIntegrations = options.defaultIntegrations ?? getDefaultIntegrationsImpl(mergedOptions);\n\n  return {\n    ...mergedOptions,\n    integrations: getIntegrationsToSetup({\n      defaultIntegrations,\n      integrations,\n    }),\n  };\n}\n\nfunction getRelease(release: NodeOptions['release']): string | undefined {\n  if (release !== undefined) {\n    return release;\n  }\n\n  const detectedRelease = getSentryRelease();\n  if (detectedRelease !== undefined) {\n    return detectedRelease;\n  }\n\n  return undefined;\n}\n\nfunction getTracesSampleRate(tracesSampleRate: NodeOptions['tracesSampleRate']): number | undefined {\n  if (tracesSampleRate !== undefined) {\n    return tracesSampleRate;\n  }\n\n  const sampleRateFromEnv = process.env.SENTRY_TRACES_SAMPLE_RATE;\n  if (!sampleRateFromEnv) {\n    return undefined;\n  }\n\n  const parsed = parseFloat(sampleRateFromEnv);\n  return isFinite(parsed) ? parsed : undefined;\n}\n\n/**\n * Update scope and propagation context based on environmental variables.\n *\n * See https://github.com/getsentry/rfcs/blob/main/text/0071-continue-trace-over-process-boundaries.md\n * for more details.\n */\nfunction updateScopeFromEnvVariables(): void {\n  if (envToBool(process.env.SENTRY_USE_ENVIRONMENT) !== false) {\n    const sentryTraceEnv = process.env.SENTRY_TRACE;\n    const baggageEnv = process.env.SENTRY_BAGGAGE;\n    const propagationContext = propagationContextFromHeaders(sentryTraceEnv, baggageEnv);\n    getCurrentScope().setPropagationContext(propagationContext);\n  }\n}\n"], "names": ["inboundFiltersIntegration", "functionToStringIntegration", "linkedErrorsIntegration", "requestDataIntegration", "consoleIntegration", "httpIntegration", "nativeNodeFetchIntegration", "onUncaughtExceptionIntegration", "onUnhandledRejectionIntegration", "contextLinesIntegration", "localVariablesIntegration", "nodeContextIntegration", "childProcessIntegration", "processSessionIntegration", "modulesIntegration", "DEBUG_BUILD", "debug", "consoleSandbox", "isCjs", "maybeInitializeEsmLoader", "setOpenTelemetryContextAsyncContextStrategy", "getCurrentScope", "SPOTLIGHT_INTEGRATION_NAME", "spotlightIntegration", "applySdkMetadata", "client", "NodeClient", "enhanceDscWithOpenTelemetryRootSpanName", "setupEventContextTrace", "openTelemetrySetupCheck", "hasSpansEnabled", "envToBool", "makeNodeTransport", "stackParserFromStackParserOptions", "defaultStackParser", "getIntegrationsToSetup", "getSentryRelease", "propagationContextFromHeaders"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AA0CA;;CAEA,GACO,SAAS,sBAAsB,GAAkB;IACtD,OAAO;QACT,SAAA;QACA,8GAAA;QACA,mDAAA;QACIA,KAAAA,yBAAyB,EAAE;QAC3BC,KAAAA,2BAA2B,EAAE;QAC7BC,KAAAA,uBAAuB,EAAE;QACzBC,KAAAA,sBAAsB,EAAE;QAC5B,kBAAA;QACIC,KAAAA,kBAAkB,EAAE;QACpBC,MAAAA,eAAe,EAAE;QACjBC,QAAAA,0BAA0B,EAAE;QAChC,kBAAA;QACIC,oBAAAA,8BAA8B,EAAE;QAChCC,qBAAAA,+BAA+B,EAAE;QACrC,aAAA;QACIC,aAAAA,uBAAuB,EAAE;QACzBC,QAAAA,yBAAyB,EAAE;QAC3BC,QAAAA,sBAAsB,EAAE;QACxBC,aAAAA,uBAAuB,EAAE;QACzBC,eAAAA,yBAAyB,EAAE;QAC3BC,QAAAA,kBAAkB,EAAE;KACrB;AACH;AAEA;;CAEA,GACO,SAAS,IAAI,CAAC,OAAO,GAA4B,CAAA,CAAE,EAA0B;IAClF,OAAO,KAAK,CAAC,OAAO,EAAE,sBAAsB,CAAC;AAC/C;AAEA;;CAEA,GACO,SAAS,8BAA8B,CAAC,OAAO,GAA4B,CAAA,CAAE,EAAc;IAChG,OAAO,KAAK,CAAC,OAAO,EAAE,IAAM,EAAE,CAAC;AACjC;AAEA;;CAEA,GACA,SAAS,KAAK,CACZ,QAAQ,GAA4B,CAAA,CAAE,EACtC,0BAA0B;IAE1B,MAAM,UAAU,gBAAgB,CAAC,QAAQ,EAAE,0BAA0B,CAAC;IAEtE,IAAI,OAAO,CAAC,KAAA,KAAU,IAAI,EAAE;QAC1B,IAAIC,WAAAA,WAAW,EAAE;YACfC,KAAAA,KAAK,CAAC,MAAM,EAAE;QACpB,OAAW;YACX,gHAAA;YACMC,KAAAA,cAAc,CAAC,MAAM;gBAC3B,sCAAA;gBACQ,OAAO,CAAC,IAAI,CAAC,8EAA8E,CAAC;YACpG,CAAO,CAAC;QACR;IACA;IAEE,IAAI,CAACC,SAAAA,KAAK,EAAC,IAAK,OAAO,CAAC,sBAAA,KAA2B,KAAK,EAAE;QACxDC,UAAAA,wBAAwB,EAAE;IAC9B;IAEEC,cAAAA,2CAA2C,EAAE;IAE7C,MAAM,KAAA,GAAQC,KAAAA,eAAe,EAAE;IAC/B,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC;IAElC,IAAI,OAAO,CAAC,SAAA,IAAa,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,EAAE,IAAA,EAAM,GAAK,IAAA,KAASC,UAAAA,gBAA0B,CAAC,EAAE;QACtG,OAAO,CAAC,YAAY,CAAC,IAAI,CACvBC,UAAAA,oBAAoB,CAAC;YACnB,UAAU,EAAE,OAAO,OAAO,CAAC,SAAA,KAAc,QAAA,GAAW,OAAO,CAAC,SAAA,GAAY,SAAS;QACzF,CAAO,CAAC;IAER;IAEEC,KAAAA,gBAAgB,CAAC,OAAO,EAAE,WAAW,CAAC;IAEtC,MAAMC,QAAA,GAAS,IAAIC,OAAAA,UAAU,CAAC,OAAO,CAAC;IACxC,2EAAA;IACEL,KAAAA,eAAe,EAAE,CAAC,SAAS,CAACI,QAAM,CAAC;IAEnCA,QAAM,CAAC,IAAI,EAAE;IAEbT,KAAAA,KAAK,CAAC,GAAG,CAAC,CAAC,WAAW,EAAEE,SAAAA,KAAK,EAAC,GAAI,UAAA,GAAa,KAAK,CAAC,MAAM,CAAC,CAAC;IAE7DO,QAAM,CAAC,yBAAyB,EAAE;IAElC,2BAA2B,EAAE;IAE7BE,cAAAA,uCAAuC,CAACF,QAAM,CAAC;IAC/CG,cAAAA,sBAAsB,CAACH,QAAM,CAAC;IAE9B,OAAOA,QAAM;AACf;AAEA;;CAEA,GACO,SAAS,0BAA0B,GAAS;IACjD,IAAI,CAACV,WAAAA,WAAW,EAAE;QAChB;IACJ;IAEE,MAAM,KAAA,GAAQc,cAAAA,uBAAuB,EAAE;IAEvC,MAAM,QAAQ,GAA+C;QAAC,sBAAsB;QAAE,kBAAkB;KAAC;IAEzG,IAAIC,KAAAA,eAAe,EAAE,EAAE;QACrB,QAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC;IACxC;IAEE,KAAK,MAAM,CAAA,IAAK,QAAQ,CAAE;QACxB,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;YACtBd,KAAAA,KAAK,CAAC,KAAK,CACT,CAAC,uBAAuB,EAAE,CAAC,CAAC,8EAA8E,CAAC;QAEnH;IACA;IAEE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE;QACpCA,KAAAA,KAAK,CAAC,IAAI,CACR,iPAAiP;IAEvP;AACA;AAEA,SAAS,gBAAgB,CACvB,OAAO,EACP,0BAA0B;IAE1B,MAAM,UAAU,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAM,SAAA,GACJ,OAAO,CAAC,SAAA,IAAae,UAAAA,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE;QAAE,MAAM,EAAE,IAAA;IAAA,CAAM,CAAA,IAAK,OAAO,CAAC,GAAG,CAAC,gBAAgB;IAChH,MAAM,mBAAmB,mBAAmB,CAAC,OAAO,CAAC,gBAAgB,CAAC;IAEtE,MAAM,gBAAgB;QACpB,GAAG,OAAO;QACV,GAAG,EAAE,OAAO,CAAC,GAAA,IAAO,OAAO,CAAC,GAAG,CAAC,UAAU;QAC1C,WAAW,EAAE,OAAO,CAAC,WAAA,IAAe,OAAO,CAAC,GAAG,CAAC,kBAAkB;QAClE,iBAAiB,EAAE,OAAO,CAAC,iBAAA,IAAqB,IAAI;QACpD,SAAS,EAAE,OAAO,CAAC,SAAA,IAAaC,KAAAA,iBAAiB;QACjD,WAAW,EAAEC,KAAAA,iCAAiC,CAAC,OAAO,CAAC,WAAA,IAAeC,IAAAA,kBAAkB,CAAC;QACzF,OAAO;QACP,gBAAgB;QAChB,SAAS;QACT,KAAK,EAAEH,UAAAA,SAAS,CAAC,OAAO,CAAC,KAAA,IAAS,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;IAC/D,CAAG;IAED,MAAM,YAAA,GAAe,OAAO,CAAC,YAAY;IACzC,MAAM,mBAAA,GAAsB,OAAO,CAAC,mBAAA,IAAuB,0BAA0B,CAAC,aAAa,CAAC;IAEpG,OAAO;QACL,GAAG,aAAa;QAChB,YAAY,EAAEI,KAAAA,sBAAsB,CAAC;YACnC,mBAAmB;YACnB,YAAY;QAClB,CAAK,CAAC;IACN,CAAG;AACH;AAEA,SAAS,UAAU,CAAC,OAAO,EAA8C;IACvE,IAAI,OAAA,KAAY,SAAS,EAAE;QACzB,OAAO,OAAO;IAClB;IAEE,MAAM,eAAA,GAAkBC,IAAAA,gBAAgB,EAAE;IAC1C,IAAI,eAAA,KAAoB,SAAS,EAAE;QACjC,OAAO,eAAe;IAC1B;IAEE,OAAO,SAAS;AAClB;AAEA,SAAS,mBAAmB,CAAC,gBAAgB,EAAuD;IAClG,IAAI,gBAAA,KAAqB,SAAS,EAAE;QAClC,OAAO,gBAAgB;IAC3B;IAEE,MAAM,iBAAA,GAAoB,OAAO,CAAC,GAAG,CAAC,yBAAyB;IAC/D,IAAI,CAAC,iBAAiB,EAAE;QACtB,OAAO,SAAS;IACpB;IAEE,MAAM,MAAA,GAAS,UAAU,CAAC,iBAAiB,CAAC;IAC5C,OAAO,QAAQ,CAAC,MAAM,IAAI,MAAA,GAAS,SAAS;AAC9C;AAEA;;;;;CAKA,GACA,SAAS,2BAA2B,GAAS;IAC3C,IAAIL,UAAAA,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAA,KAAM,KAAK,EAAE;QAC3D,MAAM,cAAA,GAAiB,OAAO,CAAC,GAAG,CAAC,YAAY;QAC/C,MAAM,UAAA,GAAa,OAAO,CAAC,GAAG,CAAC,cAAc;QAC7C,MAAM,qBAAqBM,KAAAA,6BAA6B,CAAC,cAAc,EAAE,UAAU,CAAC;QACpFhB,KAAAA,eAAe,EAAE,CAAC,qBAAqB,CAAC,kBAAkB,CAAC;IAC/D;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4495, "column": 0}, "map": {"version": 3, "file": "scope.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/sdk/scope.ts"], "sourcesContent": ["import { context } from '@opentelemetry/api';\nimport type { Scope } from '@sentry/core';\nimport { getScopesFromContext } from '@sentry/opentelemetry';\n\n/**\n * Update the active isolation scope.\n * Should be used with caution!\n */\nexport function setIsolationScope(isolationScope: Scope): void {\n  const scopes = getScopesFromContext(context.active());\n  if (scopes) {\n    scopes.isolationScope = isolationScope;\n  }\n}\n"], "names": ["getScopesFromContext", "context"], "mappings": ";;;;;AAIA;;;CAGA,GACO,SAAS,iBAAiB,CAAC,cAAc,EAAe;IAC7D,MAAM,MAAA,GAASA,cAAAA,oBAAoB,CAACC,IAAAA,OAAO,CAAC,MAAM,EAAE,CAAC;IACrD,IAAI,MAAM,EAAE;QACV,MAAM,CAAC,cAAA,GAAiB,cAAc;IAC1C;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4515, "column": 0}, "map": {"version": 3, "file": "addOriginToSpan.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/utils/addOriginToSpan.ts"], "sourcesContent": ["import type { Span } from '@opentelemetry/api';\nimport type { SpanOrigin } from '@sentry/core';\nimport { SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN } from '@sentry/core';\n\n/** Adds an origin to an OTEL Span. */\nexport function addOriginToSpan(span: Span, origin: SpanOrigin): void {\n  span.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN, origin);\n}\n"], "names": ["SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN"], "mappings": ";;;;AAIA,oCAAA,GACO,SAAS,eAAe,CAAC,IAAI,EAAQ,MAAM,EAAoB;IACpE,IAAI,CAAC,YAAY,CAACA,KAAAA,gCAAgC,EAAE,MAAM,CAAC;AAC7D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4528, "column": 0}, "map": {"version": 3, "file": "createMissingInstrumentationContext.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/utils/createMissingInstrumentationContext.ts"], "sourcesContent": ["import type { MissingInstrumentationContext } from '@sentry/core';\nimport { isCjs } from './commonjs';\n\nexport const createMissingInstrumentationContext = (pkg: string): MissingInstrumentationContext => ({\n  package: pkg,\n  'javascript.is_cjs': isCjs(),\n});\n"], "names": ["isCjs"], "mappings": ";;;;MAGa,mCAAA,GAAsC,CAAC,GAAG,GAAA,CAA6C;QAClG,OAAO,EAAE,GAAG;QACZ,mBAAmB,EAAEA,SAAAA,KAAK,EAAE;IAC9B,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4542, "column": 0}, "map": {"version": 3, "file": "ensureIsWrapped.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/utils/ensureIsWrapped.ts"], "sourcesContent": ["import { isWrapped } from '@opentelemetry/instrumentation';\nimport { consoleSandbox, getClient, getGlobalScope, hasSpansEnabled, isEnabled } from '@sentry/core';\nimport type { NodeClient } from '../sdk/client';\nimport { isCjs } from './commonjs';\nimport { createMissingInstrumentationContext } from './createMissingInstrumentationContext';\n\n/**\n * Checks and warns if a framework isn't wrapped by opentelemetry.\n */\nexport function ensureIsWrapped(\n  maybeWrappedFunction: unknown,\n  name: 'express' | 'connect' | 'fastify' | 'hapi' | 'koa',\n): void {\n  const clientOptions = getClient<NodeClient>()?.getOptions();\n  if (\n    !clientOptions?.disableInstrumentationWarnings &&\n    !isWrapped(maybeWrappedFunction) &&\n    isEnabled() &&\n    hasSpansEnabled(clientOptions)\n  ) {\n    consoleSandbox(() => {\n      if (isCjs()) {\n        // eslint-disable-next-line no-console\n        console.warn(\n          `[Sentry] ${name} is not instrumented. This is likely because you required/imported ${name} before calling \\`Sentry.init()\\`.`,\n        );\n      } else {\n        // eslint-disable-next-line no-console\n        console.warn(\n          `[Sentry] ${name} is not instrumented. Please make sure to initialize Sentry in a separate file that you \\`--import\\` when running node, see: https://docs.sentry.io/platforms/javascript/guides/${name}/install/esm/.`,\n        );\n      }\n    });\n\n    getGlobalScope().setContext('missing_instrumentation', createMissingInstrumentationContext(name));\n  }\n}\n"], "names": ["getClient", "isWrapped", "isEnabled", "hasSpansEnabled", "consoleSandbox", "isCjs", "getGlobalScope", "createMissingInstrumentationContext"], "mappings": ";;;;;;;AAMA;;CAEA,GACO,SAAS,eAAe,CAC7B,oBAAoB,EACpB,IAAI;IAEJ,MAAM,gBAAgBA,KAAAA,SAAS,EAAc,EAAE,UAAU,EAAE;IAC3D,IACE,CAAC,aAAa,EAAE,8BAAA,IAChB,CAACC,gBAAAA,SAAS,CAAC,oBAAoB,CAAA,IAC/BC,KAAAA,SAAS,EAAC,IACVC,KAAAA,eAAe,CAAC,aAAa,GAC7B;QACAC,KAAAA,cAAc,CAAC,MAAM;YACnB,IAAIC,SAAAA,KAAK,EAAE,EAAE;gBACnB,sCAAA;gBACQ,OAAO,CAAC,IAAI,CACV,CAAC,SAAS,EAAE,IAAI,CAAC,mEAAmE,EAAE,IAAI,CAAC,kCAAkC,CAAC;YAExI,OAAa;gBACb,sCAAA;gBACQ,OAAO,CAAC,IAAI,CACV,CAAC,SAAS,EAAE,IAAI,CAAC,gLAAgL,EAAE,IAAI,CAAC,cAAc,CAAC;YAEjO;QACA,CAAK,CAAC;QAEFC,KAAAA,cAAc,EAAE,CAAC,UAAU,CAAC,yBAAyB,EAAEC,oCAAAA,mCAAmC,CAAC,IAAI,CAAC,CAAC;IACrG;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4572, "column": 0}, "map": {"version": 3, "file": "common.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/cron/common.ts"], "sourcesContent": ["const replacements: [string, string][] = [\n  ['january', '1'],\n  ['february', '2'],\n  ['march', '3'],\n  ['april', '4'],\n  ['may', '5'],\n  ['june', '6'],\n  ['july', '7'],\n  ['august', '8'],\n  ['september', '9'],\n  ['october', '10'],\n  ['november', '11'],\n  ['december', '12'],\n  ['jan', '1'],\n  ['feb', '2'],\n  ['mar', '3'],\n  ['apr', '4'],\n  ['may', '5'],\n  ['jun', '6'],\n  ['jul', '7'],\n  ['aug', '8'],\n  ['sep', '9'],\n  ['oct', '10'],\n  ['nov', '11'],\n  ['dec', '12'],\n  ['sunday', '0'],\n  ['monday', '1'],\n  ['tuesday', '2'],\n  ['wednesday', '3'],\n  ['thursday', '4'],\n  ['friday', '5'],\n  ['saturday', '6'],\n  ['sun', '0'],\n  ['mon', '1'],\n  ['tue', '2'],\n  ['wed', '3'],\n  ['thu', '4'],\n  ['fri', '5'],\n  ['sat', '6'],\n];\n\n/**\n * Replaces names in cron expressions\n */\nexport function replaceCronNames(cronExpression: string): string {\n  return replacements.reduce(\n    // eslint-disable-next-line @sentry-internal/sdk/no-regexp-constructor\n    (acc, [name, replacement]) => acc.replace(new RegExp(name, 'gi'), replacement),\n    cronExpression,\n  );\n}\n"], "names": [], "mappings": ";;;AAAA,MAAM,YAAY,GAAuB;IACvC;QAAC,SAAS;QAAE,GAAG;KAAC;IAChB;QAAC,UAAU;QAAE,GAAG;KAAC;IACjB;QAAC,OAAO;QAAE,GAAG;KAAC;IACd;QAAC,OAAO;QAAE,GAAG;KAAC;IACd;QAAC,KAAK;QAAE,GAAG;KAAC;IACZ;QAAC,MAAM;QAAE,GAAG;KAAC;IACb;QAAC,MAAM;QAAE,GAAG;KAAC;IACb;QAAC,QAAQ;QAAE,GAAG;KAAC;IACf;QAAC,WAAW;QAAE,GAAG;KAAC;IAClB;QAAC,SAAS;QAAE,IAAI;KAAC;IACjB;QAAC,UAAU;QAAE,IAAI;KAAC;IAClB;QAAC,UAAU;QAAE,IAAI;KAAC;IAClB;QAAC,KAAK;QAAE,GAAG;KAAC;IACZ;QAAC,KAAK;QAAE,GAAG;KAAC;IACZ;QAAC,KAAK;QAAE,GAAG;KAAC;IACZ;QAAC,KAAK;QAAE,GAAG;KAAC;IACZ;QAAC,KAAK;QAAE,GAAG;KAAC;IACZ;QAAC,KAAK;QAAE,GAAG;KAAC;IACZ;QAAC,KAAK;QAAE,GAAG;KAAC;IACZ;QAAC,KAAK;QAAE,GAAG;KAAC;IACZ;QAAC,KAAK;QAAE,GAAG;KAAC;IACZ;QAAC,KAAK;QAAE,IAAI;KAAC;IACb;QAAC,KAAK;QAAE,IAAI;KAAC;IACb;QAAC,KAAK;QAAE,IAAI;KAAC;IACb;QAAC,QAAQ;QAAE,GAAG;KAAC;IACf;QAAC,QAAQ;QAAE,GAAG;KAAC;IACf;QAAC,SAAS;QAAE,GAAG;KAAC;IAChB;QAAC,WAAW;QAAE,GAAG;KAAC;IAClB;QAAC,UAAU;QAAE,GAAG;KAAC;IACjB;QAAC,QAAQ;QAAE,GAAG;KAAC;IACf;QAAC,UAAU;QAAE,GAAG;KAAC;IACjB;QAAC,KAAK;QAAE,GAAG;KAAC;IACZ;QAAC,KAAK;QAAE,GAAG;KAAC;IACZ;QAAC,KAAK;QAAE,GAAG;KAAC;IACZ;QAAC,KAAK;QAAE,GAAG;KAAC;IACZ;QAAC,KAAK;QAAE,GAAG;KAAC;IACZ;QAAC,KAAK;QAAE,GAAG;KAAC;IACZ;QAAC,KAAK;QAAE,GAAG;KAAC;CACb;AAED;;CAEA,GACO,SAAS,gBAAgB,CAAC,cAAc,EAAkB;IAC/D,OAAO,YAAY,CAAC,MAAM,CAC5B,sEAAA;IACI,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,WAAW,CAAC,GAAK,GAAG,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,WAAW,CAAC,EAC9E,cAAc;AAElB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4741, "column": 0}, "map": {"version": 3, "file": "cron.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/cron/cron.ts"], "sourcesContent": ["import { captureException, withMonitor } from '@sentry/core';\nimport { replaceCronNames } from './common';\n\nexport type CronJobParams = {\n  cronTime: string | Date;\n  onTick: (context: unknown, onComplete?: unknown) => void | Promise<void>;\n  onComplete?: () => void | Promise<void>;\n  start?: boolean | null;\n  context?: unknown;\n  runOnInit?: boolean | null;\n  unrefTimeout?: boolean | null;\n} & (\n  | {\n      timeZone?: string | null;\n      utcOffset?: never;\n    }\n  | {\n      timeZone?: never;\n      utcOffset?: number | null;\n    }\n);\n\nexport type CronJob = {\n  //\n};\n\nexport type CronJobConstructor = {\n  from: (param: CronJobParams) => CronJob;\n\n  new (\n    cronTime: CronJobParams['cronTime'],\n    onTick: CronJobParams['onTick'],\n    onComplete?: CronJobParams['onComplete'],\n    start?: CronJobParams['start'],\n    timeZone?: CronJobParams['timeZone'],\n    context?: CronJobParams['context'],\n    runOnInit?: CronJobParams['runOnInit'],\n    utcOffset?: null,\n    unrefTimeout?: CronJobParams['unrefTimeout'],\n  ): CronJob;\n  new (\n    cronTime: CronJobParams['cronTime'],\n    onTick: CronJobParams['onTick'],\n    onComplete?: CronJobParams['onComplete'],\n    start?: CronJobParams['start'],\n    timeZone?: null,\n    context?: CronJobParams['context'],\n    runOnInit?: CronJobParams['runOnInit'],\n    utcOffset?: CronJobParams['utcOffset'],\n    unrefTimeout?: CronJobParams['unrefTimeout'],\n  ): CronJob;\n};\n\nconst ERROR_TEXT = 'Automatic instrumentation of CronJob only supports crontab string';\n\n/**\n * Instruments the `cron` library to send a check-in event to Sentry for each job execution.\n *\n * ```ts\n * import * as Sentry from '@sentry/node';\n * import { CronJob } from 'cron';\n *\n * const CronJobWithCheckIn = Sentry.cron.instrumentCron(CronJob, 'my-cron-job');\n *\n * // use the constructor\n * const job = new CronJobWithCheckIn('* * * * *', () => {\n *  console.log('You will see this message every minute');\n * });\n *\n * // or from\n * const job = CronJobWithCheckIn.from({ cronTime: '* * * * *', onTick: () => {\n *   console.log('You will see this message every minute');\n * });\n * ```\n */\nexport function instrumentCron<T>(lib: T & CronJobConstructor, monitorSlug: string): T {\n  let jobScheduled = false;\n\n  return new Proxy(lib, {\n    construct(target, args: ConstructorParameters<CronJobConstructor>) {\n      const [cronTime, onTick, onComplete, start, timeZone, ...rest] = args;\n\n      if (typeof cronTime !== 'string') {\n        throw new Error(ERROR_TEXT);\n      }\n\n      if (jobScheduled) {\n        throw new Error(`A job named '${monitorSlug}' has already been scheduled`);\n      }\n\n      jobScheduled = true;\n\n      const cronString = replaceCronNames(cronTime);\n\n      async function monitoredTick(context: unknown, onComplete?: unknown): Promise<void> {\n        return withMonitor(\n          monitorSlug,\n          async () => {\n            try {\n              await onTick(context, onComplete);\n            } catch (e) {\n              captureException(e);\n              throw e;\n            }\n          },\n          {\n            schedule: { type: 'crontab', value: cronString },\n            timezone: timeZone || undefined,\n          },\n        );\n      }\n\n      return new target(cronTime, monitoredTick, onComplete, start, timeZone, ...rest);\n    },\n    get(target, prop: keyof CronJobConstructor) {\n      if (prop === 'from') {\n        return (param: CronJobParams) => {\n          const { cronTime, onTick, timeZone } = param;\n\n          if (typeof cronTime !== 'string') {\n            throw new Error(ERROR_TEXT);\n          }\n\n          if (jobScheduled) {\n            throw new Error(`A job named '${monitorSlug}' has already been scheduled`);\n          }\n\n          jobScheduled = true;\n\n          const cronString = replaceCronNames(cronTime);\n\n          param.onTick = async (context: unknown, onComplete?: unknown) => {\n            return withMonitor(\n              monitorSlug,\n              async () => {\n                try {\n                  await onTick(context, onComplete);\n                } catch (e) {\n                  captureException(e);\n                  throw e;\n                }\n              },\n              {\n                schedule: { type: 'crontab', value: cronString },\n                timezone: timeZone || undefined,\n              },\n            );\n          };\n\n          return target.from(param);\n        };\n      } else {\n        return target[prop];\n      }\n    },\n  });\n}\n"], "names": ["replaceCronNames", "withMonitor", "captureException"], "mappings": ";;;;;AAqDA,MAAM,UAAA,GAAa,mEAAmE;AAEtF;;;;;;;;;;;;;;;;;;;CAmBA,GACO,SAAS,cAAc,CAAI,GAAG,EAA0B,WAAW,EAAa;IACrF,IAAI,YAAA,GAAe,KAAK;IAExB,OAAO,IAAI,KAAK,CAAC,GAAG,EAAE;QACpB,SAAS,EAAC,MAAM,EAAE,IAAI,EAA6C;YACjE,MAAM,CAAC,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAA,GAAI,IAAI;YAErE,IAAI,OAAO,QAAA,KAAa,QAAQ,EAAE;gBAChC,MAAM,IAAI,KAAK,CAAC,UAAU,CAAC;YACnC;YAEM,IAAI,YAAY,EAAE;gBAChB,MAAM,IAAI,KAAK,CAAC,CAAC,aAAa,EAAE,WAAW,CAAC,4BAA4B,CAAC,CAAC;YAClF;YAEM,YAAA,GAAe,IAAI;YAEnB,MAAM,UAAA,GAAaA,OAAAA,gBAAgB,CAAC,QAAQ,CAAC;YAE7C,eAAe,aAAa,CAAC,OAAO,EAAW,UAAU,EAA2B;gBAClF,OAAOC,KAAAA,WAAW,CAChB,WAAW,EACX,YAAY;oBACV,IAAI;wBACF,MAAM,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC;oBAC/C,CAAY,CAAE,OAAO,CAAC,EAAE;wBACVC,KAAAA,gBAAgB,CAAC,CAAC,CAAC;wBACnB,MAAM,CAAC;oBACrB;gBACA,CAAW,EACD;oBACE,QAAQ,EAAE;wBAAE,IAAI,EAAE,SAAS;wBAAE,KAAK,EAAE,UAAA;oBAAA,CAAY;oBAChD,QAAQ,EAAE,QAAA,IAAY,SAAS;gBAC3C,CAAW;YAEX;YAEM,OAAO,IAAI,MAAM,CAAC,QAAQ,EAAE,aAAa,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;QACtF,CAAK;QACD,GAAG,EAAC,MAAM,EAAE,IAAI,EAA4B;YAC1C,IAAI,IAAA,KAAS,MAAM,EAAE;gBACnB,OAAO,CAAC,KAAK,KAAoB;oBAC/B,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAA,EAAS,GAAI,KAAK;oBAE5C,IAAI,OAAO,QAAA,KAAa,QAAQ,EAAE;wBAChC,MAAM,IAAI,KAAK,CAAC,UAAU,CAAC;oBACvC;oBAEU,IAAI,YAAY,EAAE;wBAChB,MAAM,IAAI,KAAK,CAAC,CAAC,aAAa,EAAE,WAAW,CAAC,4BAA4B,CAAC,CAAC;oBACtF;oBAEU,YAAA,GAAe,IAAI;oBAEnB,MAAM,UAAA,GAAaF,OAAAA,gBAAgB,CAAC,QAAQ,CAAC;oBAE7C,KAAK,CAAC,MAAA,GAAS,OAAO,OAAO,EAAW,UAAU,KAAe;wBAC/D,OAAOC,KAAAA,WAAW,CAChB,WAAW,EACX,YAAY;4BACV,IAAI;gCACF,MAAM,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC;4BACnD,CAAgB,CAAE,OAAO,CAAC,EAAE;gCACVC,KAAAA,gBAAgB,CAAC,CAAC,CAAC;gCACnB,MAAM,CAAC;4BACzB;wBACA,CAAe,EACD;4BACE,QAAQ,EAAE;gCAAE,IAAI,EAAE,SAAS;gCAAE,KAAK,EAAE,UAAA;4BAAA,CAAY;4BAChD,QAAQ,EAAE,QAAA,IAAY,SAAS;wBAC/C,CAAe;oBAEf,CAAW;oBAED,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;gBACnC,CAAS;YACT,OAAa;gBACL,OAAO,MAAM,CAAC,IAAI,CAAC;YAC3B;QACA,CAAK;IACL,CAAG,CAAC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4839, "column": 0}, "map": {"version": 3, "file": "node-cron.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/cron/node-cron.ts"], "sourcesContent": ["import { captureException, with<PERSON><PERSON><PERSON> } from '@sentry/core';\nimport { replaceCronNames } from './common';\n\nexport interface NodeCronOptions {\n  name: string;\n  timezone?: string;\n}\n\nexport interface NodeCron {\n  schedule: (cronExpression: string, callback: () => void, options: NodeCronOptions | undefined) => unknown;\n}\n\n/**\n * Wraps the `node-cron` library with check-in monitoring.\n *\n * ```ts\n * import * as Sen<PERSON> from \"@sentry/node\";\n * import * as cron from \"node-cron\";\n *\n * const cronWithCheckIn = Sentry.cron.instrumentNodeCron(cron);\n *\n * cronWithCheckIn.schedule(\n *   \"* * * * *\",\n *   () => {\n *     console.log(\"running a task every minute\");\n *   },\n *   { name: \"my-cron-job\" },\n * );\n * ```\n */\nexport function instrumentNodeCron<T>(lib: Partial<NodeCron> & T): T {\n  return new Proxy(lib, {\n    get(target, prop) {\n      if (prop === 'schedule' && target.schedule) {\n        // When 'get' is called for schedule, return a proxied version of the schedule function\n        return new Proxy(target.schedule, {\n          apply(target, thisArg, argArray: Parameters<NodeCron['schedule']>) {\n            const [expression, callback, options] = argArray;\n\n            const name = options?.name;\n            const timezone = options?.timezone;\n\n            if (!name) {\n              throw new Error('Missing \"name\" for scheduled job. A name is required for Sentry check-in monitoring.');\n            }\n\n            const monitoredCallback = async (): Promise<void> => {\n              return withMonitor(\n                name,\n                async () => {\n                  // We have to manually catch here and capture the exception because node-cron swallows errors\n                  // https://github.com/node-cron/node-cron/issues/399\n                  try {\n                    return await callback();\n                  } catch (e) {\n                    captureException(e);\n                    throw e;\n                  }\n                },\n                {\n                  schedule: { type: 'crontab', value: replaceCronNames(expression) },\n                  timezone,\n                },\n              );\n            };\n\n            return target.apply(thisArg, [expression, monitoredCallback, options]);\n          },\n        });\n      } else {\n        return target[prop as keyof T];\n      }\n    },\n  });\n}\n"], "names": ["withMonitor", "captureException", "replaceCronNames"], "mappings": ";;;;;AAYA;;;;;;;;;;;;;;;;;CAiBA,GACO,SAAS,kBAAkB,CAAI,GAAG,EAA4B;IACnE,OAAO,IAAI,KAAK,CAAC,GAAG,EAAE;QACpB,GAAG,EAAC,MAAM,EAAE,IAAI,EAAE;YAChB,IAAI,IAAA,KAAS,cAAc,MAAM,CAAC,QAAQ,EAAE;gBAClD,uFAAA;gBACQ,OAAO,IAAI,KAAK,CAAC,MAAM,CAAC,QAAQ,EAAE;oBAChC,KAAK,EAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAoC;wBACjE,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,OAAO,CAAA,GAAI,QAAQ;wBAEhD,MAAM,IAAA,GAAO,OAAO,EAAE,IAAI;wBAC1B,MAAM,QAAA,GAAW,OAAO,EAAE,QAAQ;wBAElC,IAAI,CAAC,IAAI,EAAE;4BACT,MAAM,IAAI,KAAK,CAAC,sFAAsF,CAAC;wBACrH;wBAEY,MAAM,iBAAA,GAAoB,YAA2B;4BACnD,OAAOA,KAAAA,WAAW,CAChB,IAAI,EACJ,YAAY;gCAC5B,6FAAA;gCACA,oDAAA;gCACkB,IAAI;oCACF,OAAO,MAAM,QAAQ,EAAE;gCAC3C,CAAkB,CAAE,OAAO,CAAC,EAAE;oCACVC,KAAAA,gBAAgB,CAAC,CAAC,CAAC;oCACnB,MAAM,CAAC;gCAC3B;4BACA,CAAiB,EACD;gCACE,QAAQ,EAAE;oCAAE,IAAI,EAAE,SAAS;oCAAE,KAAK,EAAEC,OAAAA,gBAAgB,CAAC,UAAU;gCAAA,CAAG;gCAClE,QAAQ;4BAC1B,CAAiB;wBAEjB,CAAa;wBAED,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE;4BAAC,UAAU;4BAAE,iBAAiB;4BAAE,OAAO;yBAAC,CAAC;oBAClF,CAAW;gBACX,CAAS,CAAC;YACV,OAAa;gBACL,OAAO,MAAM,CAAC,IAAA,CAAgB;YACtC;QACA,CAAK;IACL,CAAG,CAAC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4911, "column": 0}, "map": {"version": 3, "file": "node-schedule.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/cron/node-schedule.ts"], "sourcesContent": ["import { with<PERSON><PERSON><PERSON> } from '@sentry/core';\nimport { replaceCronNames } from './common';\n\nexport interface NodeSchedule {\n  scheduleJob(\n    nameOrExpression: string | Date | object,\n    expressionOrCallback: string | Date | object | (() => void),\n    callback?: () => void,\n  ): unknown;\n}\n\n/**\n * Instruments the `node-schedule` library to send a check-in event to <PERSON><PERSON> for each job execution.\n *\n * ```ts\n * import * as Sentry from '@sentry/node';\n * import * as schedule from 'node-schedule';\n *\n * const scheduleWithCheckIn = Sentry.cron.instrumentNodeSchedule(schedule);\n *\n * const job = scheduleWithCheckIn.scheduleJob('my-cron-job', '* * * * *', () => {\n *  console.log('You will see this message every minute');\n * });\n * ```\n */\nexport function instrumentNodeSchedule<T>(lib: T & NodeSchedule): T {\n  return new Proxy(lib, {\n    get(target, prop: keyof NodeSchedule) {\n      if (prop === 'scheduleJob') {\n        // eslint-disable-next-line @typescript-eslint/unbound-method\n        return new Proxy(target.scheduleJob, {\n          apply(target, thisArg, argArray: Parameters<NodeSchedule['scheduleJob']>) {\n            const [nameOrExpression, expressionOrCallback, callback] = argArray;\n\n            if (\n              typeof nameOrExpression !== 'string' ||\n              typeof expressionOrCallback !== 'string' ||\n              typeof callback !== 'function'\n            ) {\n              throw new Error(\n                \"Automatic instrumentation of 'node-schedule' requires the first parameter of 'scheduleJob' to be a job name string and the second parameter to be a crontab string\",\n              );\n            }\n\n            const monitorSlug = nameOrExpression;\n            const expression = expressionOrCallback;\n\n            async function monitoredCallback(): Promise<void> {\n              return withMonitor(\n                monitorSlug,\n                async () => {\n                  await callback?.();\n                },\n                {\n                  schedule: { type: 'crontab', value: replaceCronNames(expression) },\n                },\n              );\n            }\n\n            return target.apply(thisArg, [monitorSlug, expression, monitoredCallback]);\n          },\n        });\n      }\n\n      return target[prop];\n    },\n  });\n}\n"], "names": ["withMonitor", "replaceCronNames"], "mappings": ";;;;;AAWA;;;;;;;;;;;;;CAaA,GACO,SAAS,sBAAsB,CAAI,GAAG,EAAuB;IAClE,OAAO,IAAI,KAAK,CAAC,GAAG,EAAE;QACpB,GAAG,EAAC,MAAM,EAAE,IAAI,EAAsB;YACpC,IAAI,IAAA,KAAS,aAAa,EAAE;gBAClC,6DAAA;gBACQ,OAAO,IAAI,KAAK,CAAC,MAAM,CAAC,WAAW,EAAE;oBACnC,KAAK,EAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAA2C;wBACxE,MAAM,CAAC,gBAAgB,EAAE,oBAAoB,EAAE,QAAQ,CAAA,GAAI,QAAQ;wBAEnE,IACE,OAAO,gBAAA,KAAqB,QAAA,IAC5B,OAAO,oBAAA,KAAyB,QAAA,IAChC,OAAO,aAAa,YACpB;4BACA,MAAM,IAAI,KAAK,CACb,oKAAoK;wBAEpL;wBAEY,MAAM,WAAA,GAAc,gBAAgB;wBACpC,MAAM,UAAA,GAAa,oBAAoB;wBAEvC,eAAe,iBAAiB,GAAkB;4BAChD,OAAOA,KAAAA,WAAW,CAChB,WAAW,EACX,YAAY;gCACV,MAAM,QAAQ,IAAI;4BACpC,CAAiB,EACD;gCACE,QAAQ,EAAE;oCAAE,IAAI,EAAE,SAAS;oCAAE,KAAK,EAAEC,OAAAA,gBAAgB,CAAC,UAAU;gCAAA,CAAG;4BACpF,CAAiB;wBAEjB;wBAEY,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE;4BAAC,WAAW;4BAAE,UAAU;4BAAE,iBAAiB;yBAAC,CAAC;oBACtF,CAAW;gBACX,CAAS,CAAC;YACV;YAEM,OAAO,MAAM,CAAC,IAAI,CAAC;QACzB,CAAK;IACL,CAAG,CAAC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4970, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/node-core/src/cron/index.ts"], "sourcesContent": ["import { instrumentCron } from './cron';\nimport { instrumentNodeCron } from './node-cron';\nimport { instrumentNodeSchedule } from './node-schedule';\n\n/** Methods to instrument cron libraries for Sentry check-ins */\nexport const cron = {\n  instrumentCron,\n  instrumentNodeCron,\n  instrumentNodeSchedule,\n};\n"], "names": ["instrumentCron", "instrumentNodeCron", "instrumentNodeSchedule"], "mappings": ";;;;;;AAIA,8DAAA,GACO,MAAM,OAAO;IACpB,gBAAEA,OAAAA,cAAc;IAChB,oBAAEC,SAAAA,kBAAkB;IACpB,wBAAEC,aAAAA,sBAAsB;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4987, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": [], "names": [], "mappings": "", "debugId": null}}]}