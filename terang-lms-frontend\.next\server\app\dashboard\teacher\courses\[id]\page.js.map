{"version": 3, "file": "../app/dashboard/teacher/courses/[id]/page.js", "mappings": "ubAAA,yDCAA,0YCgBA,OACA,UACA,GACA,CACA,UACA,YACA,CACA,UACA,UACA,CACA,UACA,UACA,CACA,UACA,OACA,CACA,uBAAiC,EACjC,MA1BA,IAAoB,uCAAkL,CA0BtM,iJAES,EACF,CACP,CAGA,EACA,CACO,CACP,CAGA,EACA,CACO,CACP,CACA,QA5CA,IAAsB,uCAAqK,CA4C3L,qIAGA,CACO,CACP,CACA,QAnDA,IAAsB,uCAA4J,CAmDlL,2HACA,UACA,sBAAoC,wCAAyS,4BAA2C,wCAAmS,aAC3pB,SACA,aACA,WACA,eACA,CACA,EACA,CACO,CACP,CACA,QApEA,IAAsB,sCAAiJ,CAoEvK,gHACA,gBApEA,IAAsB,uCAAuJ,CAoE7K,sHACA,aApEA,IAAsB,uCAAoJ,CAoE1K,mHACA,WApEA,IAAsB,4CAAgF,CAoEtG,+CACA,cApEA,IAAsB,4CAAmF,CAoEzG,kDACA,UACA,sBAAoC,wCAAyS,4BAA2C,wCAAmS,aAC3pB,SACA,aACA,WACA,eACA,CACA,EACA,CACO,UACP,oJAOO,GACP,QAH6B,EAI7B,UAHA,OADgD,KAChD,SAIA,EAKO,MAAwB,oBAAkB,EACjD,YACA,KAAc,WAAS,UACvB,4CACA,2CAEA,cACA,YACA,YACK,CACL,UACA,YACA,CACA,CAAC,yBC/GD,oDCAA,qGCAA,mECAA,0GCAA,qDCAA,gDCAA,iDCAA,iDCAA,wGCAA,+DCAA,6DCmBI,sBAAsB,4tBAX1B,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAJ6B,KALd,KAKwB,EAArC,OAAO,CAIa,CAAG,IAAI,KAAK,CAAC,EAAiB,CAClD,KAAK,CAAE,CAAC,EAAkB,EAAS,CADa,GACT,CAAN,IAC3B,EACA,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IADM,EADI,CAO/B,CADuB,CACH,GADyB,OACC,CAAC,GAAG,CAAC,EAAd,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EADyC,KAClC,CACrC,KAAO,CAER,CAEA,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,iCAAiC,CACjD,aAAa,CAAE,MAAM,mBACrB,gBACA,CADiB,SAEjB,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CADuB,CAAN,CA7BEA,EAoCnB,IAAC,EAOF,OAEE,EAOF,KAhBkB,EAkBhB,EAOF,KA7DiD,EA+D/C,EAA2B,CAlBN,IASL,iBASQ,IChF9B,iECAA,uDCAA,sDCAA,uDCAA,wDCAA,qDCAA,yPC4GA,SAASC,EAAkBC,CAAwB,EACjD,MAAO,CACLC,GAAID,EAAYC,EAAE,CAACC,QAAQ,GAC3BC,KAAMH,EAAYG,IAAI,CACtBC,SAAUJ,EAAYI,QAAQ,EAAI,CAAC,CAAED,KAAM,OAAQE,MAAO,EAAG,EAAE,CAC/DC,QAASN,EAAYM,OAAO,EAAIC,OAChCC,YAAaR,EAAYQ,WAAW,CACpCC,YAAaT,EAAYS,WAAW,OAAIF,EACxCG,OAAQV,EAAYU,MAAM,CAC1BC,WAAYX,EAAYW,UAAU,CAEtC,CAKA,SAASC,EAAcC,CAAgB,EACrC,MAAO,CACLZ,GAAIY,EAAQZ,EAAE,CAACC,QAAQ,GACvBY,KAAMD,EAAQC,IAAI,CAClBC,YAAaF,EAAQE,WAAW,CAChCC,UAAWH,EAAQG,SAAS,CAAGH,EAAQG,SAAS,CAACC,GAAG,CAAClB,GAAqB,EAAE,CAC5EmB,UAAWL,EAAQK,SAAS,CAC5BC,aAAcC,SAASP,EAAQM,YAAY,CAC7C,CACF,CAKA,SAASE,EAAiBC,CAAsB,EAC9C,IAAMC,EAAcD,EAAWE,OAAO,EAAEC,KAAKC,GAA0B,YAAlBA,EAAKC,QAAQ,EAG9DC,EAAU,EAAE,CAChB,GAAIN,EAAWM,OAAO,CACpB,CADsB,EACY,UAA9B,OAAON,EAAWM,OAAO,CAC3B,GAAI,CACFA,EAAUC,KAAKC,KAAK,CAACR,EAAWM,OAAO,CACzC,CAAE,MAAOG,EAAO,CACdC,QAAQD,KAAK,CAAC,iCAAkCA,GAChDH,EAAU,EAAE,MAIdA,EAAUN,EAAWM,OAAO,CAIhC,MAAO,CACL3B,GAAIqB,EAAWrB,EAAE,CAACC,QAAQ,GAC1BY,KAAMQ,EAAWR,IAAI,CACrBc,QAASA,EACTjB,WAAYW,EAAWX,UAAU,CACjCsB,eAAgB,CAAC,CAACV,EAClBA,YAAaA,EAAcX,EAAcW,QAAehB,CAC1D,CACF,CCxJe,SAAS2B,EAAe,QACrCC,CAAM,CAKP,EACC,GAAM,IACJlC,CAAE,CACH,CAAGmC,CAAAA,EAAAA,EAAAA,GAAAA,CAAGA,CAACD,GACFE,EAASC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GAClB,CAACC,EAAYC,EAAc,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAoB,MAC1D,CAACC,EAAWC,EAAa,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACrC,CAACV,EAAOa,EAAS,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAgB,MAC5C,CAACI,EAAYC,EAAc,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAMvCM,EAAkB,UACtB,GAAI,CACFJ,GAAa,GACbC,EAAS,MACT,IAAMI,EAAOC,EAAAA,EAAWA,CAACC,OAAO,GAChC,GAAI,CAACF,EAAM,YACTJ,EAAS,gCAKX,IAAMO,EAAW,MAAMC,MAAM,CAAC,aAAa,EAAEnD,EAAG,WAAW,EAAE+C,EAAK/C,EAAE,EAAE,EAChEoD,EAAO,MAAMF,EAASG,IAAI,GAChC,GAAI,CAACH,EAASI,EAAE,CACd,CADgB,KACV,MAAUF,EAAKtB,KAAK,EAAI,+BAEhC,GAAI,CAACsB,EAAKG,OAAO,EAAI,CAACH,EAAKI,MAAM,CAC/B,CADiC,KAC3B,MAAU,oBAElB,IAAMC,EAAuBL,EAAKI,MAAM,CACxCzB,QAAQ2B,GAAG,CAAC,uBAAwBD,GAGpC,IAAME,EAA2BF,EAAUE,CAHK,YAGQ,EAAI,EAAE,CACxDC,EAAiCD,EAAcnC,IAAI,CAACC,GAA0B,UAAlBA,EAAKC,QAAQ,EACzEmC,EAAsBF,EAAcG,MAAM,CAACrC,GAA0B,WAAlBA,EAAKC,QAAQ,EACtEK,QAAQ2B,GAAG,CAAC,kBAAmB,CAC7BK,aAAcJ,EAAcK,MAAM,CAClCL,cAAeE,EAAoBG,MAAM,CACzCJ,UAAWA,EAAYA,EAAU5D,EAAE,CAAG,OACtCuB,QAASoC,EAAc3C,GAAG,CAACiD,GAAM,EAC/BjE,GAAIiE,EAAEjE,EAAE,CACRE,KAAM+D,EAAEvC,QAAQ,CAChBwC,SAAUD,EAAEC,QAAQ,CACpBC,SAAUF,EAAEE,QAAQ,CACtB,EACF,GAGA,IAAMC,ED0HL,SAASC,CAAmD,CAAEV,EAA2B,EAAE,CAAEC,CAAmB,EACrH7B,QAAQ2B,GC3HkDW,CD2H9C,gCAAiC,CAC3CF,SAAUV,EAAUzD,EAAE,CACtBsE,mBAAoBX,EAAcK,MAAM,CACxCJ,UAAWA,EAAYA,EAAU5D,EAAE,CAAG,OACtCuE,WAAYZ,EAAc3C,GAAG,CAACiD,GAAM,EAAEjE,GAAIiE,EAAEjE,EAAE,CAAEE,KAAM+D,EAAEvC,QAAQ,CAAEwC,SAAUD,EAAEC,QAAQ,CAAEC,SAAUF,EAAEE,QAAQ,CAAC,EAC/G,GAGA,IAAMN,EAAsBF,EAAcG,MAAM,CAACrC,GAAQA,aAAKC,QAAQ,EAChE8C,EAAkBZ,GAAaD,EAAcnC,IAAI,CAACC,GAA0B,UAAlBA,EAAKC,QAAQ,EAO7E,OALAK,QAAQ2B,GAAG,CAAC,oBAAqB,CAC/BC,cAAeE,EAAoBG,MAAM,CACzCJ,UAAWY,EAAkBA,EAAgBxE,EAAE,CAAG,MACpD,GAEO,CACLa,KAAM4C,EAAU5C,IAAI,CACpBC,YAAa2C,EAAU3C,WAAW,CAClC2D,WAAYhB,EAAUgB,UAAU,EAAI,GACpCC,WAAYjB,EAAUiB,UAAU,CAChCxE,KAAMuD,EAAUvD,IAAI,CACpByE,eAAgBlB,EAAUkB,cAAc,EAAI,OAC5CC,UAAWnB,EAAUmB,SAAS,CAAG,IAAIC,KAAKpB,EAAUmB,SAAS,OAAItE,EACjEwE,QAASrB,EAAUqB,OAAO,CAAG,IAAID,KAAKpB,EAAUqB,OAAO,OAAIxE,EAC3DyE,kBAAmBtB,EAAUuB,UAAU,EAAIvB,EAAUwB,YAAY,CACjEC,cAAezB,EAAUyB,aAAa,CACtCC,MAAO1B,EAAU0B,KAAK,CACtBC,SAAU3B,EAAU2B,QAAQ,CAC5BC,YAAa5B,EAAU4B,WAAW,CAClCC,QAAS7B,EAAU6B,OAAO,CAACtE,GAAG,CAACuE,GAAUC,CAtD7C,SAASA,CAAoC,CAAEjB,EAAwB,EAAE,EAEvE,IAAMkB,EAAalB,EAAW/C,IAAI,CAACC,GACjCA,EAAKyC,QAAQ,GAAKwB,EAAU1F,EAAE,EAAsB,WAAlByB,EAAKC,QAAQ,EAKjD,OAFAK,QAAQ2B,GAAG,CAAC,CAAC,OAAO,EAAEgC,EAAU1F,EAAE,CAAC,oBAAoB,CAAC,CAAEyF,GAEnD,CACLzF,GAAI0F,EAAU1F,EAAE,CAACC,CAHoD,OAG5C,GACzBY,EAJiF,GAI3E6E,EAAU7E,IAAI,CACpBC,YAAa4E,EAAU5E,WAAW,CAClCJ,WAAYgF,EAAUhF,UAAU,CAChCiF,SAAUD,EAAUC,QAAQ,CAAC3E,GAAG,CAACI,GACjCwE,cAAe,CAAC,CAACH,EACjBA,WAAYA,EAAa9E,EAAc8E,QAAcnF,CACvD,CACF,GAqC6DiF,EAAQ5B,IACjEkC,YAAa,GACbC,gBAAiB,EAAE,CACnBlC,UAAWY,EAAkB7D,EAAc6D,QAAmBlE,EAE9DyF,WAAYtC,EAAUsC,UAAU,CAAG,CACjCC,aAAcvC,EAAUsC,UAAU,CAACC,YAAY,EAAI,EAAE,CACrDC,oBAAqBxC,EAAUsC,UAAU,CAACE,mBAAmB,EAAI,GACjEC,cAAezC,EAAUsC,UAAU,CAACG,aAAa,EAAI,EAAE,OACrD5F,EACJ6F,UAAW1C,EAAU0C,SAAS,CAAG,CAC/BC,QAAS3C,EAAU0C,SAAS,CAACC,OAAO,EAAI,EACxCC,SAAU5C,EAAU0C,SAAS,CAACE,QAAQ,EAAI,GAC1CC,WAAY7C,EAAU0C,SAAS,CAACG,UAAU,EAAI,EAAE,OAC9ChG,EACJiG,oBAAqB9C,EAAU8C,mBAAmB,CAAG,CACnDC,UAAW/C,EAAU8C,mBAAmB,CAACC,SAAS,EAAI,EACtDC,eAAgBhD,EAAU8C,mBAAmB,CAACE,cAAc,EAAI,EAAE,CAClEC,aAAcjD,EAAU8C,mBAAmB,CAACG,YAAY,EAAI,EAAE,OAC5DpG,EACJqG,QAASlD,EAAUkD,OAAO,CAAG,CAC3BC,SAAUnD,EAAUkD,OAAO,CAACC,QAAQ,EAAI,EAAE,CAC1CC,WAAYpD,EAAUkD,OAAO,CAACE,UAAU,EAAI,EAAE,CAC9CC,cAAerD,EAAUkD,OAAO,CAACG,aAAa,EAAI,EACpD,EAAIxG,OACJyG,kBAAmBtD,EAAUsD,iBAAiB,CAAG,CAC/CC,aAAcvD,EAAUsD,iBAAiB,CAACC,YAAY,EAAI,EAAE,CAC5DC,WAAYxD,EAAUsD,iBAAiB,CAACE,UAAU,EAAI,EAAE,CACxDC,QAASzD,EAAUsD,iBAAiB,CAACG,OAAO,EAAI,EAAE,OAChD5G,CACN,CACF,ECxL6DmD,EAAWE,EAAeC,GACjF7B,QAAQ2B,GAAG,CAAC,oBAAqBU,GACjC7B,EAAc6B,EAChB,CAAE,MAAOtC,EAAO,CACdC,CAHmD,OAG3CD,KAAK,CAAC,yBAA0BA,GACxCa,EAASb,aAAiBqF,MAAQrF,EAAMsF,OAAO,CAAG,8BACpD,QAAU,CACR1E,GAAa,EACf,CACF,EACM2E,EAAqB,MAAOC,IAChC,GAAI,CACFzE,GAAc,GACdd,QAAQ2B,GAAG,CAAC,oCAAqC4D,GAEjD,IAAMvE,EAAOC,EAAAA,EAAWA,CAACC,MAF4C,CAErC,GAChC,GAAI,CAACF,EAAM,EAHsE,UAI/EwE,EAAAA,EAAKA,CAACzF,KAAK,CAAC,kCAKd,IAAM0F,GDyKVzF,QAAQ2B,ECzKe+D,CDyKZ,CAAC,4BCzKyCA,cDyKE,CACrDtD,SC1KuEnE,ED2KvE0H,aAAcpF,EAAWgD,OAAO,CAACtB,MAAM,CACvC2D,gBAAiB,CAAC,CAACrF,EAAWsB,SAAS,CACvCD,cAAerB,EAAWgD,OAAO,CAACxB,MAAM,CAAC8D,GAAKA,EAAEhC,aAAa,EAAE5B,MAAM,GAGhE,CACLG,SAAUhD,SAASgD,GACnBtD,KAAMyB,EAAWzB,IAAI,CACrBC,YAAawB,EAAWxB,WAAW,CACnC4D,WAAYpC,EAAWoC,UAAU,CACjCxE,KAAMoC,EAAWpC,IAAI,CACrByE,eAAgBrC,EAAWqC,cAAc,CACzCQ,MAAO7C,EAAW6C,KAAK,CACvBC,SAAU9C,EAAW8C,QAAQ,CAC7BF,cAA6C,aAA9B5C,EAAWqC,cAAc,EAAiD,SAA9BrC,EAAWqC,cAAc,CACpFC,UAAWtC,EAAWsC,SAAS,EAAEiD,cACjC/C,QAASxC,EAAWwC,OAAO,EAAE+C,cAC7BvC,QAAShD,EAAWgD,OAAO,CAACtE,GAAG,CAACuE,IAC9BxD,QAAQ2B,GAAG,CAAC,CAAC,oBAAoB,EAAE6B,EAAO1E,IAAI,CAAC,CAAC,CAAC,CAAE,CACjDb,GAAIuF,EAAOvF,EAAE,CACb4F,cAAeL,EAAOK,aAAa,CACnCkC,aAAcvC,EAAOE,UAAU,EAAEzF,EACnC,GAEO,CACLA,GAAIuF,EAAOvF,EAAE,EAAI,CAAC+H,MAAM5G,SAASoE,EAAOvF,EAAE,GAAKmB,SAASoE,EAAOvF,EAAE,OAAIM,EACrEO,KAAM0E,EAAO1E,IAAI,CACjBC,YAAayE,EAAOzE,WAAW,CAC/BJ,WAAY6E,EAAO7E,UAAU,CAC7BiF,SAAUJ,EAAOI,QAAQ,CAAC3E,GAAG,CAACgH,GAAY,EACxChI,GAAIgI,EAAQhI,EAAE,EAAI,CAAC+H,MAAM5G,SAAS6G,EAAQhI,EAAE,GAAKmB,SAAS6G,EAAQhI,EAAE,OAAIM,EACxEO,KAAMmH,EAAQnH,IAAI,CAClBc,QAASqG,EAAQrG,OAAO,CACxBjB,WAAYsH,EAAQtH,UAAU,CAC9Be,KAAMuG,EAAQhG,cAAc,EAAIgG,EAAQ1G,WAAW,CAAG,CACpDtB,GAAIgI,EAAQ1G,WAAW,CAACtB,EAAE,EAAI,CAAC+H,MAAM5G,SAAS6G,EAAQ1G,WAAW,CAACtB,EAAE,GAAKmB,SAAS6G,EAAQ1G,WAAW,CAACtB,EAAE,OAAIM,EAC5GO,KAAMmH,EAAQ1G,WAAW,CAACT,IAAI,CAC9BC,YAAakH,EAAQ1G,WAAW,CAACR,WAAW,CAC5CY,SAAU,UACVT,UAAW+G,EAAQ1G,WAAW,CAACL,SAAS,CACxCC,aAAc8G,EAAQ1G,WAAW,CAACJ,YAAY,CAC9CH,UAAWiH,EAAQ1G,WAAW,CAACP,SAAS,CAACC,GAAG,CAACb,GAAa,EACxDH,GAAIG,EAASH,CAD2C,CACzC,EAAI,CAAC+H,MAAM5G,SAAShB,EAASH,EAAE,GAAKmB,SAAShB,EAASH,EAAE,OAAIM,EAC3EJ,KAAMC,EAASD,IAAI,CACnBC,SAAUA,EAASA,QAAQ,CAC3BE,QAASF,EAASE,OAAO,EAAI,KAC7BE,YAA+B,UAAlBJ,EAASD,IAAI,CAAeC,EAASI,WAAW,CAAG,KAChEC,YAAaL,EAASK,WAAW,EAAI,KACrCC,OAAQN,EAASM,MAAM,CACvBC,WAAYP,EAASO,UAAU,CACjC,EACF,OAAIJ,EACN,GACAmB,KAAM8D,EAAOK,aAAa,EAAIL,EAAOE,UAAU,CAAG,CAChDzF,GAAIuF,EAAOE,UAAU,CAACzF,EAAE,EAAI,CAAC+H,MAAM5G,SAASoE,EAAOE,UAAU,CAACzF,EAAE,GAAKmB,SAASoE,EAAOE,UAAU,CAACzF,EAAE,EAAIM,OACtGO,KAAM0E,EAAOE,UAAU,CAAC5E,IAAI,CAC5BC,YAAayE,EAAOE,UAAU,CAAC3E,WAAW,CAC1CY,SAAU,SACVT,UAAWsE,EAAOE,UAAU,CAACxE,SAAS,CACtCC,aAAcqE,EAAOE,UAAU,CAACvE,YAAY,CAC5CH,UAAWwE,EAAOE,UAAU,CAAC1E,SAAS,CAACC,GAAG,CAACb,GAAa,EACtDH,GAAIG,EAASH,CADyC,CACvC,EAAI,CAAC+H,MAAM5G,SAAShB,EAASH,EAAE,GAAKmB,SAAShB,EAASH,EAAE,EAAIM,OAC3EJ,KAAMC,EAASD,IAAI,CACnBC,SAAUA,EAASA,QAAQ,CAC3BE,QAASF,EAASE,OAAO,EAAI,KAC7BE,YAA+B,UAAlBJ,EAASD,IAAI,CAAeC,EAASI,WAAW,CAAG,KAChEC,YAAaL,EAASK,WAAW,EAAI,KACrCC,OAAQN,EAASM,MAAM,CACvBC,WAAYP,EAASO,UAAU,GAEnC,OAAIJ,CACN,IAEFsD,UAAWtB,EAAWsB,SAAS,CAAG,CAChC5D,GAAIsC,EAAWsB,SAAS,CAAC5D,EAAE,EAAI,CAAC+H,MAAM5G,SAASmB,EAAWsB,SAAS,CAAC5D,EAAE,GAAKmB,SAASmB,EAAWsB,SAAS,CAAC5D,EAAE,OAAIM,EAC/GO,KAAMyB,EAAWsB,SAAS,CAAC/C,IAAI,CAC/BC,YAAawB,EAAWsB,SAAS,CAAC9C,WAAW,CAC7CY,SAAU,QACVT,UCzPkDqG,EDyP5B1D,SAAS,CAAC3C,SAAS,CACzCC,aAAcoB,EAAWsB,SAAS,CAAC1C,YAAY,CAC/CH,UAAWuB,EAAWsB,SAAS,CAAC7C,SAAS,CAACC,GAAG,CAACb,GAAa,EACzDH,GAAIG,EAASH,CAD4C,CAC1C,EAAI,CAAC+H,MAAM5G,SAAShB,EAASH,EAAE,GAAKmB,SAAShB,EAASH,EAAE,OAAIM,EAC3EJ,KAAMC,EAASD,IAAI,CACnBC,SAAUA,EAASA,QAAQ,CAC3BE,QAASF,EAASE,OAAO,EAAI,KAC7BE,YAA+B,UAAlBJ,EAASD,IAAI,CAAeC,EAASI,WAAW,CAAG,KAChEC,YAAaL,EAASK,WAAW,EAAI,KACrCC,OAAQN,EAASM,MAAM,CACvBC,WAAYP,EAASO,UAAU,CACjC,EACF,OAAIJ,EAEJyF,WAAYzD,EAAWyD,UAAU,CACjCI,UAAW7D,EAAW6D,SAAS,CAC/BI,oBAAqBjE,EAAWiE,mBAAmB,CACnDI,QAASrE,EAAWqE,OAAO,CAC3BI,kBAAmBzE,EAAWyE,iBAAiB,GC1Q7ChF,QAAQ2B,GAAG,CAAC,wBAAyB8D,GAGrC,IAAMS,EAAiB,IAH2B,EAGrB9E,MAAM,CAAC,GAH0B,UAGb,EAAEnD,EAAAA,CAAI,CAAE,CACvDkI,OAAQ,MACRC,QAAS,CACP,eAAgB,kBAClB,EACAC,KAAMxG,KAAKyG,SAAS,CAAC,CACnBxH,KAAM2G,EAAW3G,IAAI,CACrBC,YAAa0G,EAAW1G,WAAW,CACnC4D,WAAY8C,EAAW9C,UAAU,CACjCxE,KAAMsH,EAAWtH,IAAI,CACrByE,eAAgB6C,EAAW7C,cAAc,CACzCQ,MAAOqC,EAAWrC,KAAK,CACvBC,SAAUoC,EAAWpC,QAAQ,CAC7BF,cAAesC,EAAWtC,aAAa,CACvCN,UAAW4C,EAAW5C,SAAS,CAC/BE,QAAS0C,EAAW1C,OAAO,CAC3BwD,UAAWvF,EAAK/C,EAAE,CAElB+F,WAAYyB,EAAWzB,UAAU,CACjCI,UAAWqB,EAAWrB,SAAS,CAC/BI,oBAAqBiB,EAAWjB,mBAAmB,CACnDI,QAASa,EAAWb,OAAO,CAC3BI,kBAAmBS,EAAWT,iBAAiB,EAEnD,GACA,GAAI,CAACkB,EAAe3E,EAAE,CAAE,CACtB,IAAMiF,EAAY,MAAMN,EAAe5E,IAAI,EAC3C,OAAM,MAAUkF,EAAUzG,KAAK,EAAI,0BACrC,CAGA,IAAK,IAAM0G,KAAchB,EAAWlC,OAAO,CAAE,CAC3CvD,QAAQ2B,GAAG,CAAC,CAAC,mBAAmB,EAAE8E,EAAW3H,IAAI,EAAE,CAAE,CACnDb,GAAIwI,EAAWxI,EAAE,CACjB4F,cAAe,CAAC,CAAC4C,EAAW/G,IAAI,CAChCqG,aAAcU,EAAW/G,IAAI,EAAEzB,EACjC,GACA,IAAIyI,EAAkBD,EAAWxI,EAAE,CACnC,GAAIwI,EAAWxI,EAAE,CAcX,CAda,MAEYmD,MAAM,CAAC,aAAa,EAAEqF,EAAWxI,EAAE,EAAE,CAAE,CAClEkI,OAAQ,MACRC,QAAS,CACP,eAAgB,kBAClB,EACAC,KAAMxG,KAAKyG,SAAS,CAAC,CACnBxH,KAAM2H,EAAW3H,IAAI,CACrBC,YAAa0H,EAAW1H,WAAW,CACnCJ,WAAY8H,EAAW9H,UAAU,CACjC4H,UAAWvF,EAAK/C,EAAE,EAEtB,IACoBsD,EAAE,EAAE,QACdxB,KAAK,CAAC,2BAA4B0G,EAAWxI,EAAE,MAEpD,CAEL,IAAM0I,EAAoB,MAAMvF,MAAM,eAAgB,CACpD+E,OAAQ,OACRC,QAAS,CACP,eAAgB,kBAClB,EACAC,KAAMxG,KAAKyG,SAAS,CAAC,CACnBxH,KAAM2H,EAAW3H,IAAI,CACrBC,YAAa0H,EAAW1H,WAAW,CACnCJ,WAAY8H,EAAW9H,UAAU,CACjCyD,SAAUhD,SAASnB,GACnBsI,UAAWvF,EAAK/C,EAAE,EAEtB,EACI0I,GAAkBpF,EAAE,EAAE,CAExBmF,EAAkBE,CADA,MAAMD,EAAkBrF,IAAI,IAClBkC,MAAM,CAACvF,EAAE,CACrC+B,QAAQ2B,GAAG,CAAC,8BAA+B+E,GAE/C,CAGA,IAAK,IAAMT,KAAWQ,EAAW7C,QAAQ,CAAE,CACzC,IAAIiD,EAAmBZ,EAAQhI,EAAE,CACjC,GAAIgI,EAAQhI,EAAE,CAcR,CAdU,MAEgBmD,MAAM,CAAC,cAAc,EAAE6E,EAAQhI,EAAE,EAAE,CAAE,CACjEkI,OAAQ,MACRC,QAAS,CACP,eAAgB,kBAClB,EACAC,KAAMxG,KAAKyG,SAAS,CAAC,CACnBxH,KAAMmH,EAAQnH,IAAI,CAClBc,QAASqG,EAAQrG,OAAO,CACxBjB,WAAYsH,EAAQtH,UAAU,CAC9B4H,UAAWvF,EAAK/C,EAAE,EAEtB,IACqBsD,EAAE,EAAE,QACfxB,KAAK,CAAC,4BAA6BkG,EAAQhI,EAAE,MAElD,CAEL,IAAM6I,EAAqB,MAAM1F,MAAM,gBAAiB,CACtD+E,OAAQ,OACRC,QAAS,CACP,eAAgB,kBAClB,EACAC,KAAMxG,KAAKyG,SAAS,CAAC,CACnBxH,KAAMmH,EAAQnH,IAAI,CAClBc,QAASqG,EAAQrG,OAAO,CACxBjB,WAAYsH,EAAQtH,UAAU,CAC9BwD,SAAUuE,EACVH,UAAWvF,EAAK/C,EAAE,EAEtB,GACI6I,EAAmBvF,EAAE,EAAE,CAEzBsF,EAAmBE,CADA,MAAMD,EAAmBxF,IAAI,IAClB2E,OAAO,CAAChI,EAAE,CACxC+B,QAAQ2B,GAAG,CAAC,+BAAgCkF,GAEhD,CAGA,GAAIZ,EAAQvG,IAAI,CAEd,CAFgB,EAChBM,QAAQ2B,GAAG,CAAC,CAAC,oCAAoC,EAAEkF,EAAiB,CAAC,CAAC,CAAEZ,EAAQvG,IAAI,CAACzB,EAAE,EACnFgI,EAAQvG,IAAI,CAACzB,EAAE,CAAE,CAEnB,IAAM+I,EAAe,MAAM5F,MAAM,CAAC,aAAa,EAAE6E,EAAQvG,IAAI,CAACzB,EAAE,EAAE,CAAE,CAClEkI,OAAQ,MACRC,QAAS,CACP,eAAgB,kBAClB,EACAC,KAAMxG,KAAKyG,SAAS,CAAC,CACnBxH,KAAMmH,EAAQvG,IAAI,CAACZ,IAAI,CACvBC,YAAakH,EAAQvG,IAAI,CAACX,WAAW,CACrCY,SAAU,UACVT,UAAW+G,EAAQvG,IAAI,CAACR,SAAS,CACjCC,aAAc8G,EAAQvG,IAAI,CAACP,YAAY,CACvCoH,UAAWvF,EAAK/C,EAAE,CAClBe,UAAWiH,EAAQvG,IAAI,CAACV,SAC1B,EACF,GACA,GAAI,CAACgI,EAAazF,EAAE,CAAE,CACpBvB,QAAQD,KAAK,CAAC,iCAAkCkG,EAAQvG,IAAI,CAACzB,EAAE,EAC/D,IAAMuI,EAAY,MAAMQ,EAAa1F,IAAI,GACzCtB,QAAQD,KAAK,CAAC,6BAA8ByG,EAC9C,CACF,KAAO,CAEL,IAAMQ,EAAe,MAAM5F,MAAM,eAAgB,CAC/C+E,OAAQ,OACRC,QAAS,CACP,eAAgB,kBAClB,EACAC,KAAMxG,KAAKyG,SAAS,CAAC,CACnBxH,KAAMmH,EAAQvG,IAAI,CAACZ,IAAI,CACvBC,YAAakH,EAAQvG,IAAI,CAACX,WAAW,CACrCY,SAAU,UACVT,UAAW+G,EAAQvG,IAAI,CAACR,SAAS,CACjCC,aAAc8G,EAAQvG,IAAI,CAACP,YAAY,CACvC8H,UAAWJ,EACXN,UAAWvF,EAAK/C,EAAE,CAClBe,UAAWiH,EAAQvG,IAAI,CAACV,SAAS,EAErC,GACA,GAAI,CAACgI,EAAazF,EAAE,CAAE,CACpBvB,QAAQD,KAAK,CAAC,iCACd,IAAMyG,EAAY,MAAMQ,EAAa1F,IAAI,GACzCtB,QAAQD,KAAK,CAAC,+BAAgCyG,EAChD,CACF,CAEJ,CAGA,GAAIC,EAAW/G,IAAI,CAMjB,CANmB,EACnBM,QAAQ2B,GAAG,CAAC,CAAC,kCAAkC,EAAE+E,EAAgB,CAAC,CAAC,CAAE,CACnEQ,OAAQT,EAAW/G,IAAI,CAACzB,EAAE,CAC1BkJ,SAAUV,EAAW/G,IAAI,CAACZ,IAAI,CAC9BsI,eAAgBX,EAAW/G,IAAI,CAACV,SAAS,CAACiD,MAAM,GAE9CwE,EAAW/G,IAAI,CAACzB,EAAE,CAAE,CAEtB,IAAM+I,EAAe,MAAM5F,MAAM,CAAC,aAAa,EAAEqF,EAAW/G,IAAI,CAACzB,EAAE,EAAE,CAAE,CACrEkI,OAAQ,MACRC,QAAS,CACP,eAAgB,kBAClB,EACAC,KAAMxG,KAAKyG,SAAS,CAAC,CACnBxH,KAAM2H,EAAW/G,IAAI,CAACZ,IAAI,CAC1BC,YAAa0H,EAAW/G,IAAI,CAACX,WAAW,CACxCY,SAAU,SACVT,UAAWuH,EAAW/G,IAAI,CAACR,SAAS,CACpCC,aAAcsH,EAAW/G,IAAI,CAACP,YAAY,CAC1CgD,SAAUuE,EAEVH,UAAWvF,EAAK/C,EAAE,CAClBe,UAAWyH,EAAW/G,IAAI,CAACV,SAAS,EAExC,GACA,GAAKgI,CAAD,CAAczF,EAAE,CAKlBvB,QAAQ2B,GAAG,CAAC,oCAAqC8E,EAAW/G,IAAI,CAACzB,EAAE,MAL/C,CACpB+B,QAAQD,KAAK,CAAC,gCAAiC0G,EAAW/G,IAAI,CAACzB,EAAE,EACjE,IAAMuI,EAAY,MAAMQ,EAAa1F,IAAI,GACzCtB,QAAQD,KAAK,CAAC,4BAA6ByG,EAC7C,CAGF,KAAO,CAEL,IAAMQ,EAAe,MAAM5F,MAAM,eAAgB,CAC/C+E,OAAQ,OACRC,QAAS,CACP,eAAgB,kBAClB,EACAC,KAAMxG,KAAKyG,SAAS,CAAC,CACnBxH,KAAM2H,EAAW/G,IAAI,CAACZ,IAAI,CAC1BC,YAAa0H,EAAW/G,IAAI,CAACX,WAAW,CACxCY,SAAU,SACVT,UAAWuH,EAAW/G,IAAI,CAACR,SAAS,CACpCC,aAAcsH,EAAW/G,IAAI,CAACP,YAAY,CAC1CgD,SAAUuE,EACVH,UAAWvF,EAAK/C,EAAE,CAClBe,UAAWyH,EAAW/G,IAAI,CAACV,SAAS,EAExC,GACA,GAAKgI,CAAD,CAAczF,EAAE,CAIb,CACL,IAAM8F,EAAU,MAAML,EAAa1F,IAAI,GACvCtB,QAAQ2B,GAAG,CAAC,oCAAqC0F,EAAQ3H,IAAI,EAAEzB,GACjE,KAPsB,CACpB+B,QAAQD,KAAK,CAAC,gCACd,IAAMyG,EAAY,MAAMQ,EAAa1F,IAAI,GACzCtB,QAAQD,KAAK,CAAC,8BAA+ByG,EAC/C,CAIF,CAEJ,CAGA,GAAIf,EAAW5D,SAAS,CAMtB,CANwB,EACxB7B,QAAQ2B,GAAG,CAAC,yBAA0B,CACpC2F,OAAQ7B,EAAW5D,SAAS,CAAC5D,EAAE,CAC/BsJ,SAAU9B,EAAW5D,SAAS,CAAC/C,IAAI,CACnCsI,eAAgB3B,EAAW5D,SAAS,CAAC7C,SAAS,CAACiD,MAAM,GAEnDwD,EAAW5D,SAAS,CAAC5D,EAAE,CAAE,CAE3B,IAAMuJ,EAAe,MAAMpG,MAAM,CAAC,aAAa,EAAEqE,EAAW5D,SAAS,CAAC5D,EAAE,EAAE,CAAE,CAC1EkI,OAAQ,MACRC,QAAS,CACP,eAAgB,kBAClB,EACAC,KAAMxG,KAAKyG,SAAS,CAAC,CACnBxH,KAAM2G,EAAW5D,SAAS,CAAC/C,IAAI,CAC/BC,YAAa0G,EAAW5D,SAAS,CAAC9C,WAAW,CAC7CY,SAAU,QACVT,UAAWuG,EAAW5D,SAAS,CAAC3C,SAAS,CACzCC,aAAcsG,EAAW5D,SAAS,CAAC1C,YAAY,CAC/CiD,SAAUhD,SAASnB,GAEnBsI,UAAWvF,EAAK/C,EAAE,CAClBe,UAAWyG,EAAW5D,SAAS,CAAC7C,SAAS,EAE7C,GACA,GAAKwI,CAAD,CAAcjG,EAAE,CAKlBvB,QAAQ2B,GAAG,CAAC,mCAAoC8D,EAAW5D,SAAS,CAAC5D,EAAE,MALnD,CACpB+B,QAAQD,KAAK,CAAC,+BAAgC0F,EAAW5D,SAAS,CAAC5D,EAAE,EACrE,IAAMuI,EAAY,MAAMgB,EAAalG,IAAI,GACzCtB,QAAQD,KAAK,CAAC,2BAA4ByG,EAC5C,CAGF,KAAO,CAHE,IAKDgB,EAAe,MAAMpG,MAAM,eAAgB,CAC/C+E,OAAQ,OACRC,QAAS,CACP,eAAgB,kBAClB,EACAC,KAAMxG,KAAKyG,SAAS,CAAC,CACnBxH,KAAM2G,EAAW5D,SAAS,CAAC/C,IAAI,CAC/BC,YAAa0G,EAAW5D,SAAS,CAAC9C,WAAW,CAC7CY,SAAU,QACVT,UAAWuG,EAAW5D,SAAS,CAAC3C,SAAS,CACzCC,aAAcsG,EAAW5D,SAAS,CAAC1C,YAAY,CAC/CiD,SAAUhD,SAASnB,GACnBsI,UAAWvF,EAAK/C,EAAE,CAClBe,UAAWyG,EAAW5D,SAAS,CAAC7C,SAAS,EAE7C,GACA,GAAKwI,CAAD,CAAcjG,EAAE,CAIb,CACL,IAAMkG,EAAU,MAAMD,EAAalG,IAAI,GACvCtB,QAAQ2B,GAAG,CAAC,mCAAoC8F,EAAQ/H,IAAI,EAAEzB,GAChE,KAPsB,CACpB+B,QAAQD,KAAK,CAAC,+BACd,IAAMyG,EAAY,MAAMgB,EAAalG,IAAI,GACzCtB,QAAQD,KAAK,CAAC,6BAA8ByG,EAC9C,CAIF,CAEFhB,EAAAA,EAAKA,CAAChE,OAAO,CAAC,gCACdnB,EAAOqH,IAAI,CAAC,6BACd,CAAE,MAAO3H,EAAO,CACdC,QAAQD,KAAK,CAAC,yBAA0BA,GACxCyF,EAAAA,EAAKA,CAACzF,KAAK,CAACA,aAAiBqF,MAAQrF,EAAMsF,OAAO,CAAG,0BACvD,QAAU,CACRvE,GAAc,EAChB,CACF,SAIA,EACS,SADM,EACL6G,MAAAA,CAAIC,UAAU,kCAClB,WAACD,MAAAA,CAAIC,UAAU,iBACb,UAACC,EAAAA,CAAQA,CAAAA,CAACD,UAAU,kBACpB,UAACC,EAAAA,CAAQA,CAAAA,CAACD,UAAU,gBAEtB,WAACE,EAAAA,EAAIA,CAAAA,WACH,WAACC,EAAAA,EAAUA,CAAAA,WACT,UAACF,EAAAA,CAAQA,CAAAA,CAACD,UAAU,aACpB,UAACC,EAAAA,CAAQA,CAAAA,CAACD,UAAU,gBAEtB,UAACI,EAAAA,EAAWA,CAAAA,UACV,WAACL,MAAAA,CAAIC,UAAU,sBACb,UAACC,EAAAA,CAAQA,CAAAA,CAACD,UAAU,gBACpB,UAACC,EAAAA,CAAQA,CAAAA,CAACD,UAAU,gBACpB,UAACC,EAAAA,CAAQA,CAAAA,CAACD,UAAU,2BAM5B7H,EACK,KADE,IACF,EAAC4H,MAAAA,CAAIC,UAAU,kCAClB,WAACD,MAAAA,CAAIC,UAAU,iBACb,UAACK,IAAIA,CAACC,KAAK,KAAND,iCACH,WAACE,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,QAAQR,UAAU,iBAChC,UAACS,EAAAA,CAASA,CAAAA,CAACT,UAAU,iBAAiB,uBAI1C,UAACU,KAAAA,CAAGV,UAAU,8BAAqB,gBACnC,UAACW,IAAAA,CAAEX,UAAU,iCAAwB,6CAGvC,WAACE,EAAAA,EAAIA,CAAAA,WACH,WAACC,EAAAA,EAAUA,CAAAA,WACT,WAACS,EAAAA,EAASA,CAAAA,CAACZ,UAAU,qDACnB,UAACa,EAAAA,CAAWA,CAAAA,CAACb,UAAU,YAAY,0BAGrC,UAACc,EAAAA,EAAeA,CAAAA,UAAE3I,OAEpB,UAACiI,EAAAA,EAAWA,CAAAA,UACV,UAACG,EAAAA,CAAMA,CAAAA,CAACQ,QAAS5H,EAAiBqH,QAAQ,mBAAU,sBAOzD7H,EA0BE,UA1BU,CA0BToH,MAAAA,CAAIC,UAAU,wBAAwBgB,wBAAsB,iBAAiBC,0BAAwB,qBACzG,WAAClB,MAAAA,CAAIC,UAAU,iBACb,UAACK,IAAIA,CAACC,KAAK,KAAND,wBAAmCa,sBAAoB,OAAOD,0BAAwB,oBACzF,WAACV,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,QAAQR,UAAU,OAAOkB,sBAAoB,SAASD,0BAAwB,qBAC5F,UAACR,EAAAA,CAASA,CAAAA,CAACT,UAAU,eAAekB,sBAAoB,YAAYD,0BAAwB,aAAa,uBAI7G,UAACP,KAAAA,CAAGV,UAAU,8BAAqB,gBACnC,UAACW,IAAAA,CAAEX,UAAU,iCAAwB,6CAGvC,UAACmB,EAAAA,CAAoBA,CAAAA,CAACC,YAAazI,EAAY0I,WAAY3D,EAAoB4D,SA3F9D,CA2FwEC,IA1F3F9I,EAAOqH,IAAI,CAAC,6BACd,EAyF2GoB,sBAAoB,uBAAuBD,0BAAwB,gBArCrK,WAAClB,MAAAA,CAAIC,UAAU,kCAClB,WAACD,MAAAA,CAAIC,UAAU,iBACb,UAACK,IAAIA,CAACC,KAAK,KAAND,iCACH,WAACE,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,QAAQR,UAAU,iBAChC,UAACS,EAAAA,CAASA,CAAAA,CAACT,UAAU,iBAAiB,uBAI1C,UAACU,KAAAA,CAAGV,UAAU,8BAAqB,gBACnC,UAACW,IAAAA,CAAEX,UAAU,iCAAwB,6CAGvC,WAACE,EAAAA,EAAIA,CAAAA,WACH,WAACC,EAAAA,EAAUA,CAAAA,WACT,UAACS,EAAAA,EAASA,CAAAA,UAAC,qBACX,UAACE,EAAAA,EAAeA,CAAAA,UAAC,gDAEnB,UAACV,EAAAA,EAAWA,CAAAA,UACV,UAACC,IAAIA,CAACC,KAAK,KAAND,iCACH,UAACE,EAAAA,CAAMA,CAAAA,UAAC,6BAoBtB,0BC/eA,oDCAA,kECAA,yDCAA,uDCAA,sGCAA,uCAAkL,yBCAlL,qDCAA,sDCAA,2DCAA,yDCAA,iECAA,uDCAA,sDCAA,gDCAA,4DCAA,2DCAA,iDCAA,yDCAA,qDCAA,uCAAkL,yBCAlL", "sources": ["webpack://terang-lms-ui/external node-commonjs \"node:process\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://terang-lms-ui/?29fb", "webpack://terang-lms-ui/external commonjs2 \"module\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://terang-lms-ui/external commonjs \"require-in-the-middle\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/action-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs2 \"process\"", "webpack://terang-lms-ui/external commonjs2 \"os\"", "webpack://terang-lms-ui/external commonjs2 \"util\"", "webpack://terang-lms-ui/external commonjs2 \"fs\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:child_process\"", "webpack://terang-lms-ui/external commonjs2 \"path\"", "webpack://terang-lms-ui/sentry-wrapper-module", "webpack://terang-lms-ui/external commonjs2 \"diagnostics_channel\"", "webpack://terang-lms-ui/external node-commonjs \"node:http\"", "webpack://terang-lms-ui/external node-commonjs \"node:zlib\"", "webpack://terang-lms-ui/external node-commonjs \"node:tls\"", "webpack://terang-lms-ui/external node-commonjs \"node:https\"", "webpack://terang-lms-ui/external node-commonjs \"node:os\"", "webpack://terang-lms-ui/external node-commonjs \"node:diagnostics_channel\"", "webpack://terang-lms-ui/./src/utils/course-data-transformer.ts", "webpack://terang-lms-ui/./src/app/dashboard/teacher/courses/[id]/page.tsx", "webpack://terang-lms-ui/external commonjs2 \"crypto\"", "webpack://terang-lms-ui/external commonjs \"import-in-the-middle\"", "webpack://terang-lms-ui/external node-commonjs \"node:stream\"", "webpack://terang-lms-ui/external node-commonjs \"node:util\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://terang-lms-ui/?0dea", "webpack://terang-lms-ui/external node-commonjs \"node:fs\"", "webpack://terang-lms-ui/external node-commonjs \"node:url\"", "webpack://terang-lms-ui/external commonjs2 \"worker_threads\"", "webpack://terang-lms-ui/external commonjs2 \"perf_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:worker_threads\"", "webpack://terang-lms-ui/external node-commonjs \"node:path\"", "webpack://terang-lms-ui/external node-commonjs \"node:net\"", "webpack://terang-lms-ui/external commonjs2 \"url\"", "webpack://terang-lms-ui/external commonjs2 \"child_process\"", "webpack://terang-lms-ui/external node-commonjs \"node:readline\"", "webpack://terang-lms-ui/external commonjs2 \"tty\"", "webpack://terang-lms-ui/external commonjs2 \"async_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:inspector\"", "webpack://terang-lms-ui/?7a43", "webpack://terang-lms-ui/external commonjs2 \"events\""], "sourcesContent": ["module.exports = require(\"node:process\");", "module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "const module0 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>a\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\layout.tsx\");\nconst module1 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>ffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\");\nconst module2 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\not-found.tsx\");\nconst module3 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/forbidden-error\");\nconst module4 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/unauthorized-error\");\nconst module5 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\");\nconst module6 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\layout.tsx\");\nconst page7 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\courses\\\\[id]\\\\page.tsx\");\nimport { AppPageRouteModule } from \"next/dist/server/route-modules/app-page/module.compiled\" with {\n    'turbopack-transition': 'next-ssr'\n};\nimport { RouteKind } from \"next/dist/server/route-kind\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'teacher',\n        {\n        children: [\n        'courses',\n        {\n        children: [\n        '[id]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page7, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\courses\\\\[id]\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module6, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [module5, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props)),(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\icon.png?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\layout.tsx\"],\n'global-error': [module1, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\"],\n'not-found': [module2, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\not-found.tsx\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props)),(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\icon.png?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\courses\\\\[id]\\\\page.tsx\"];\nexport { tree, pages };\nexport { default as GlobalError } from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\" with {\n    'turbopack-transition': 'next-server-utility'\n};\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nexport const __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\nexport * from \"next/dist/server/app-render/entry-base\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n    definition: {\n        kind: RouteKind.APP_PAGE,\n        page: \"/dashboard/teacher/courses/[id]/page\",\n        pathname: \"/dashboard/teacher/courses/[id]\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"next/dist/server/app-render/action-async-storage.external.js\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nconst serverComponent = serverComponentModule.default;\n\nlet wrappedServerComponent;\nif (typeof serverComponent === 'function') {\n  // For some odd Next.js magic reason, `headers()` will not work if used inside `wrapServerComponentsWithSentry`.\n  // Current assumption is that Next.js applies some loader magic to userfiles, but not files in node_modules. This file\n  // is technically a userfile so it gets the loader magic applied.\n  wrappedServerComponent = new Proxy(serverComponent, {\n    apply: (originalFunction, thisArg, args) => {\n      let sentryTraceHeader = undefined;\n      let baggageHeader = undefined;\n      let headers = undefined;\n\n      // We try-catch here just in `requestAsyncStorage` is undefined since it may not be defined\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        sentryTraceHeader = requestAsyncStore?.headers.get('sentry-trace') ?? undefined;\n        baggageHeader = requestAsyncStore?.headers.get('baggage') ?? undefined;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      return Sentry.wrapServerComponentWithSentry(originalFunction, {\n        componentRoute: '/dashboard/teacher/courses/[id]',\n        componentType: 'Page',\n        sentryTraceHeader,\n        baggageHeader,\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n} else {\n  wrappedServerComponent = serverComponent;\n}\n\nconst generateMetadata = serverComponentModule.generateMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateMetadata, {\n      componentRoute: '/dashboard/teacher/courses/[id]',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateImageMetadata = serverComponentModule.generateImageMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateImageMetadata, {\n      componentRoute: '/dashboard/teacher/courses/[id]',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateImageMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateViewport = serverComponentModule.generateViewport\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateViewport, {\n      componentRoute: '/dashboard/teacher/courses/[id]',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateViewport',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst wrappedServerComponent$1 = wrappedServerComponent;\n\nexport { wrappedServerComponent$1 as default, generateImageMetadata, generateMetadata, generateViewport };\n", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "import { CourseData, ModuleData, ChapterData, QuizData, QuestionData } from '@/components/course/course-creation-wizard';\r\n\r\n// Type definitions for API response data\r\nexport interface ApiCourse {\r\n  id: number;\r\n  name: string;\r\n  description: string;\r\n  instructor?: string;\r\n  courseCode: string;\r\n  type: 'self_paced' | 'verified';\r\n  enrollmentType?: 'code' | 'invitation' | 'both' | 'purchase';\r\n  startDate?: string;\r\n  endDate?: string;\r\n  coverImage?: string;\r\n  coverPicture?: string;\r\n  isPurchasable?: boolean;\r\n  price?: number;\r\n  currency?: string;\r\n  previewMode?: boolean;\r\n  teacherId: number;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n  modules: ApiModule[];\r\n  moduleQuizzes?: ApiQuiz[];\r\n  // Related course data\r\n  admissions?: {\r\n    requirements: string[];\r\n    applicationDeadline?: string;\r\n    prerequisites: string[];\r\n  };\r\n  academics?: {\r\n    credits: number;\r\n    workload: string;\r\n    assessment: string[];\r\n  };\r\n  tuitionAndFinancing?: {\r\n    totalCost: number;\r\n    paymentOptions: string[];\r\n    scholarships: string[];\r\n  };\r\n  careers?: {\r\n    outcomes: string[];\r\n    industries: string[];\r\n    averageSalary?: string;\r\n  };\r\n  studentExperience?: {\r\n    testimonials: { name: string; feedback: string }[];\r\n    facilities: string[];\r\n    support: string[];\r\n  };\r\n}\r\n\r\nexport interface ApiModule {\r\n  id: number;\r\n  name: string;\r\n  description: string;\r\n  orderIndex: number;\r\n  courseId: number;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n  chapters: ApiChapter[];\r\n}\r\n\r\nexport interface ApiChapter {\r\n  id: number;\r\n  name: string;\r\n  content: string;\r\n  orderIndex: number;\r\n  moduleId: number;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n  quizzes?: ApiQuiz[];\r\n}\r\n\r\nexport interface ApiQuiz {\r\n  id: number;\r\n  name: string;\r\n  description: string;\r\n  quizType: 'chapter' | 'module' | 'final';\r\n  timeLimit?: number;\r\n  minimumScore: string;\r\n  isActive: boolean;\r\n  chapterId?: number;\r\n  moduleId?: number;\r\n  courseId?: number;\r\n  teacherId: number;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n  questions?: ApiQuestion[];\r\n}\r\n\r\nexport interface ApiQuestion {\r\n  id: number;\r\n  type: 'multiple_choice' | 'true_false' | 'essay';\r\n  question: any[]; // JSON content\r\n  options?: { content: any[]; isCorrect: boolean }[] | null; // JSON content\r\n  essayAnswer?: string | null; // Renamed from correctAnswer\r\n  explanation?: any[] | null; // New column\r\n  points: number;\r\n  orderIndex: number;\r\n  quizId: number;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n}\r\n\r\n/**\r\n * Transform API question data to CourseData question format\r\n */\r\nfunction transformQuestion(apiQuestion: ApiQuestion): QuestionData {\r\n  return {\r\n    id: apiQuestion.id.toString(),\r\n    type: apiQuestion.type,\r\n    question: apiQuestion.question || [{ type: 'text', value: '' }],\r\n    options: apiQuestion.options || undefined,\r\n    essayAnswer: apiQuestion.essayAnswer,\r\n    explanation: apiQuestion.explanation || undefined,\r\n    points: apiQuestion.points,\r\n    orderIndex: apiQuestion.orderIndex\r\n  };\r\n}\r\n\r\n/**\r\n * Transform API quiz data to CourseData quiz format\r\n */\r\nfunction transformQuiz(apiQuiz: ApiQuiz): QuizData {\r\n  return {\r\n    id: apiQuiz.id.toString(),\r\n    name: apiQuiz.name,\r\n    description: apiQuiz.description,\r\n    questions: apiQuiz.questions ? apiQuiz.questions.map(transformQuestion) : [],\r\n    timeLimit: apiQuiz.timeLimit,\r\n    minimumScore: parseInt(apiQuiz.minimumScore)\r\n  };\r\n}\r\n\r\n/**\r\n * Transform API chapter data to CourseData chapter format\r\n */\r\nfunction transformChapter(apiChapter: ApiChapter): ChapterData {\r\n  const chapterQuiz = apiChapter.quizzes?.find(quiz => quiz.quizType === 'chapter');\r\n\r\n  // Handle content - it might be a JSON string or already parsed object\r\n  let content = [];\r\n  if (apiChapter.content) {\r\n    if (typeof apiChapter.content === 'string') {\r\n      try {\r\n        content = JSON.parse(apiChapter.content);\r\n      } catch (error) {\r\n        console.error('Error parsing chapter content:', error);\r\n        content = [];\r\n      }\r\n    } else {\r\n      // Content is already an object/array\r\n      content = apiChapter.content;\r\n    }\r\n  }\r\n\r\n  return {\r\n    id: apiChapter.id.toString(),\r\n    name: apiChapter.name,\r\n    content: content,\r\n    orderIndex: apiChapter.orderIndex,\r\n    hasChapterQuiz: !!chapterQuiz,\r\n    chapterQuiz: chapterQuiz ? transformQuiz(chapterQuiz) : undefined\r\n  };\r\n}\r\n\r\n/**\r\n * Transform API module data to CourseData module format\r\n * FIXED: Now properly handles module quizzes from the moduleQuizzes array\r\n */\r\nfunction transformModule(apiModule: ApiModule, allQuizzes: ApiQuiz[] = []): ModuleData {\r\n  // Find module quiz specifically for this module\r\n  const moduleQuiz = allQuizzes.find(quiz => \r\n    quiz.moduleId === apiModule.id && quiz.quizType === 'module'\r\n  );\r\n  \r\n  console.log(`Module ${apiModule.id}: Found module quiz:`, moduleQuiz); // Debug log\r\n  \r\n  return {\r\n    id: apiModule.id.toString(),\r\n    name: apiModule.name,\r\n    description: apiModule.description,\r\n    orderIndex: apiModule.orderIndex,\r\n    chapters: apiModule.chapters.map(transformChapter),\r\n    hasModuleQuiz: !!moduleQuiz,\r\n    moduleQuiz: moduleQuiz ? transformQuiz(moduleQuiz) : undefined\r\n  };\r\n}\r\n\r\n/**\r\n * Transform API course data to CourseData format for the wizard\r\n * FIXED: Better handling of quizzes and debugging\r\n */\r\nexport function transformApiCourseToWizardData(apiCourse: ApiCourse, moduleQuizzes: ApiQuiz[] = [], finalExam?: ApiQuiz): CourseData {\r\n  console.log('Transforming API course data:', {\r\n    courseId: apiCourse.id,\r\n    moduleQuizzesCount: moduleQuizzes.length,\r\n    finalExam: finalExam ? finalExam.id : 'none',\r\n    allQuizzes: moduleQuizzes.map(q => ({ id: q.id, type: q.quizType, moduleId: q.moduleId, courseId: q.courseId }))\r\n  });\r\n\r\n  // Filter quizzes properly\r\n  const actualModuleQuizzes = moduleQuizzes.filter(quiz => quiz.quizType === 'module');\r\n  const actualFinalExam = finalExam || moduleQuizzes.find(quiz => quiz.quizType === 'final');\r\n\r\n  console.log('Filtered quizzes:', {\r\n    moduleQuizzes: actualModuleQuizzes.length,\r\n    finalExam: actualFinalExam ? actualFinalExam.id : 'none'\r\n  });\r\n\r\n  return {\r\n    name: apiCourse.name,\r\n    description: apiCourse.description,\r\n    instructor: apiCourse.instructor || '', \r\n    courseCode: apiCourse.courseCode,\r\n    type: apiCourse.type,\r\n    enrollmentType: apiCourse.enrollmentType || 'code', \r\n    startDate: apiCourse.startDate ? new Date(apiCourse.startDate) : undefined,\r\n    endDate: apiCourse.endDate ? new Date(apiCourse.endDate) : undefined,\r\n    coverImagePreview: apiCourse.coverImage || apiCourse.coverPicture,\r\n    isPurchasable: apiCourse.isPurchasable,\r\n    price: apiCourse.price,\r\n    currency: apiCourse.currency,\r\n    previewMode: apiCourse.previewMode,\r\n    modules: apiCourse.modules.map(module => transformModule(module, moduleQuizzes)), // Pass ALL quizzes\r\n    isPublished: true, \r\n    assignedClasses: [], \r\n    finalExam: actualFinalExam ? transformQuiz(actualFinalExam) : undefined,\r\n    // Add related course data\r\n    admissions: apiCourse.admissions ? {\r\n      requirements: apiCourse.admissions.requirements || [],\r\n      applicationDeadline: apiCourse.admissions.applicationDeadline || '',\r\n      prerequisites: apiCourse.admissions.prerequisites || []\r\n    } : undefined,\r\n    academics: apiCourse.academics ? {\r\n      credits: apiCourse.academics.credits || 0,\r\n      workload: apiCourse.academics.workload || '',\r\n      assessment: apiCourse.academics.assessment || []\r\n    } : undefined,\r\n    tuitionAndFinancing: apiCourse.tuitionAndFinancing ? {\r\n      totalCost: apiCourse.tuitionAndFinancing.totalCost || 0,\r\n      paymentOptions: apiCourse.tuitionAndFinancing.paymentOptions || [],\r\n      scholarships: apiCourse.tuitionAndFinancing.scholarships || []\r\n    } : undefined,\r\n    careers: apiCourse.careers ? {\r\n      outcomes: apiCourse.careers.outcomes || [],\r\n      industries: apiCourse.careers.industries || [],\r\n      averageSalary: apiCourse.careers.averageSalary || ''\r\n    } : undefined,\r\n    studentExperience: apiCourse.studentExperience ? {\r\n      testimonials: apiCourse.studentExperience.testimonials || [],\r\n      facilities: apiCourse.studentExperience.facilities || [],\r\n      support: apiCourse.studentExperience.support || []\r\n    } : undefined\r\n  };\r\n}\r\n\r\n/**\r\n * Transform CourseData back to API format for updates\r\n * FIXED: Better handling of quiz transformations and ID parsing\r\n */\r\nexport function transformWizardDataToApiUpdate(courseData: CourseData, courseId: string) {\r\n  console.log('Transforming wizard data to API update:', {\r\n    courseId,\r\n    modulesCount: courseData.modules.length,\r\n    finalExamExists: !!courseData.finalExam,\r\n    moduleQuizzes: courseData.modules.filter(m => m.hasModuleQuiz).length\r\n  });\r\n\r\n  return {\r\n    courseId: parseInt(courseId),\r\n    name: courseData.name,\r\n    description: courseData.description,\r\n    courseCode: courseData.courseCode,\r\n    type: courseData.type,\r\n    enrollmentType: courseData.enrollmentType,\r\n    price: courseData.price,\r\n    currency: courseData.currency,\r\n    isPurchasable: courseData.enrollmentType === 'purchase' || courseData.enrollmentType === 'both',\r\n    startDate: courseData.startDate?.toISOString(),\r\n    endDate: courseData.endDate?.toISOString(),\r\n    modules: courseData.modules.map(module => {\r\n      console.log(`Transforming module ${module.name}:`, {\r\n        id: module.id,\r\n        hasModuleQuiz: module.hasModuleQuiz,\r\n        moduleQuizId: module.moduleQuiz?.id\r\n      });\r\n\r\n      return {\r\n        id: module.id && !isNaN(parseInt(module.id)) ? parseInt(module.id) : undefined,\r\n        name: module.name,\r\n        description: module.description,\r\n        orderIndex: module.orderIndex,\r\n        chapters: module.chapters.map(chapter => ({\r\n          id: chapter.id && !isNaN(parseInt(chapter.id)) ? parseInt(chapter.id) : undefined,\r\n          name: chapter.name,\r\n          content: chapter.content, \r\n          orderIndex: chapter.orderIndex,\r\n          quiz: chapter.hasChapterQuiz && chapter.chapterQuiz ? {\r\n            id: chapter.chapterQuiz.id && !isNaN(parseInt(chapter.chapterQuiz.id)) ? parseInt(chapter.chapterQuiz.id) : undefined,\r\n            name: chapter.chapterQuiz.name,\r\n            description: chapter.chapterQuiz.description,\r\n            quizType: 'chapter' as const,\r\n            timeLimit: chapter.chapterQuiz.timeLimit,\r\n            minimumScore: chapter.chapterQuiz.minimumScore,\r\n            questions: chapter.chapterQuiz.questions.map(question => ({\r\n              id: question.id && !isNaN(parseInt(question.id)) ? parseInt(question.id) : undefined,\r\n              type: question.type,\r\n              question: question.question, \r\n              options: question.options || null,\r\n              essayAnswer: question.type === 'essay' ? question.essayAnswer : null,\r\n              explanation: question.explanation || null,\r\n              points: question.points,\r\n              orderIndex: question.orderIndex\r\n            }))\r\n          } : undefined\r\n        })),\r\n        quiz: module.hasModuleQuiz && module.moduleQuiz ? {\r\n          id: module.moduleQuiz.id && !isNaN(parseInt(module.moduleQuiz.id)) ? parseInt(module.moduleQuiz.id) : undefined,\r\n          name: module.moduleQuiz.name,\r\n          description: module.moduleQuiz.description,\r\n          quizType: 'module' as const,\r\n          timeLimit: module.moduleQuiz.timeLimit,\r\n          minimumScore: module.moduleQuiz.minimumScore,\r\n          questions: module.moduleQuiz.questions.map(question => ({\r\n            id: question.id && !isNaN(parseInt(question.id)) ? parseInt(question.id) : undefined,\r\n            type: question.type,\r\n            question: question.question, \r\n            options: question.options || null,\r\n            essayAnswer: question.type === 'essay' ? question.essayAnswer : null,\r\n            explanation: question.explanation || null,\r\n            points: question.points,\r\n            orderIndex: question.orderIndex\r\n          }))\r\n        } : undefined\r\n      };\r\n    }),\r\n    finalExam: courseData.finalExam ? {\r\n      id: courseData.finalExam.id && !isNaN(parseInt(courseData.finalExam.id)) ? parseInt(courseData.finalExam.id) : undefined,\r\n      name: courseData.finalExam.name,\r\n      description: courseData.finalExam.description,\r\n      quizType: 'final' as const,\r\n      timeLimit: courseData.finalExam.timeLimit,\r\n      minimumScore: courseData.finalExam.minimumScore,\r\n      questions: courseData.finalExam.questions.map(question => ({\r\n        id: question.id && !isNaN(parseInt(question.id)) ? parseInt(question.id) : undefined,\r\n        type: question.type,\r\n        question: question.question, \r\n        options: question.options || null,\r\n        essayAnswer: question.type === 'essay' ? question.essayAnswer : null,\r\n        explanation: question.explanation || null,\r\n        points: question.points,\r\n        orderIndex: question.orderIndex\r\n      }))\r\n    } : undefined,\r\n    // Add related course data\r\n    admissions: courseData.admissions,\r\n    academics: courseData.academics,\r\n    tuitionAndFinancing: courseData.tuitionAndFinancing,\r\n    careers: courseData.careers,\r\n    studentExperience: courseData.studentExperience\r\n  };\r\n}", "'use client';\n\nimport { useState, useEffect, use } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Skeleton } from '@/components/ui/skeleton';\nimport { ArrowLeft, AlertCircle } from 'lucide-react';\nimport Link from 'next/link';\nimport { authStorage } from '@/lib/auth';\nimport { toast } from 'sonner';\nimport { CourseCreationWizard, CourseData } from '@/components/course/course-creation-wizard';\nimport { transformApiCourseToWizardData, transformWizardDataToApiUpdate, ApiCourse, ApiModule, ApiChapter, ApiQuiz, ApiQuestion } from '@/utils/course-data-transformer';\nexport default function CourseEditPage({\n  params\n}: {\n  params: Promise<{\n    id: string;\n  }>;\n}) {\n  const {\n    id\n  } = use(params);\n  const router = useRouter();\n  const [courseData, setCourseData] = useState<CourseData | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [isUpdating, setIsUpdating] = useState(false);\n  useEffect(() => {\n    if (id) {\n      fetchCourseData();\n    }\n  }, [id]);\n  const fetchCourseData = async () => {\n    try {\n      setIsLoading(true);\n      setError(null);\n      const user = authStorage.getUser();\n      if (!user) {\n        setError('Please log in to edit course');\n        return;\n      }\n\n      // Fetch course data\n      const response = await fetch(`/api/courses/${id}?teacherId=${user.id}`);\n      const data = await response.json();\n      if (!response.ok) {\n        throw new Error(data.error || 'Failed to fetch course data');\n      }\n      if (!data.success || !data.course) {\n        throw new Error('Course not found');\n      }\n      const apiCourse: ApiCourse = data.course;\n      console.log('Fetched course data:', apiCourse); // Debug log\n\n      // Extract module quizzes and final exam from API response\n      const moduleQuizzes: ApiQuiz[] = apiCourse.moduleQuizzes || [];\n      const finalExam: ApiQuiz | undefined = moduleQuizzes.find(quiz => quiz.quizType === 'final');\n      const actualModuleQuizzes = moduleQuizzes.filter(quiz => quiz.quizType === 'module');\n      console.log('Quiz breakdown:', {\n        totalQuizzes: moduleQuizzes.length,\n        moduleQuizzes: actualModuleQuizzes.length,\n        finalExam: finalExam ? finalExam.id : 'none',\n        quizzes: moduleQuizzes.map(q => ({\n          id: q.id,\n          type: q.quizType,\n          moduleId: q.moduleId,\n          courseId: q.courseId\n        }))\n      });\n\n      // Transform API data to wizard format\n      const transformedData = transformApiCourseToWizardData(apiCourse, moduleQuizzes, finalExam);\n      console.log('Transformed data:', transformedData); // Debug log\n      setCourseData(transformedData);\n    } catch (error) {\n      console.error('Error fetching course:', error);\n      setError(error instanceof Error ? error.message : 'Failed to fetch course data');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleCourseUpdate = async (updatedCourseData: CourseData) => {\n    try {\n      setIsUpdating(true);\n      console.log('Starting course update with data:', updatedCourseData); // Debug log\n\n      const user = authStorage.getUser();\n      if (!user) {\n        toast.error('Please log in to update course');\n        return;\n      }\n\n      // Transform wizard data back to API format\n      const updateData = transformWizardDataToApiUpdate(updatedCourseData, id);\n      console.log('Update data prepared:', updateData); // Debug log\n\n      // Update course basic info\n      const courseResponse = await fetch(`/api/courses/${id}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          name: updateData.name,\n          description: updateData.description,\n          courseCode: updateData.courseCode,\n          type: updateData.type,\n          enrollmentType: updateData.enrollmentType,\n          price: updateData.price,\n          currency: updateData.currency,\n          isPurchasable: updateData.isPurchasable,\n          startDate: updateData.startDate,\n          endDate: updateData.endDate,\n          teacherId: user.id,\n          // Add related course data\n          admissions: updateData.admissions,\n          academics: updateData.academics,\n          tuitionAndFinancing: updateData.tuitionAndFinancing,\n          careers: updateData.careers,\n          studentExperience: updateData.studentExperience\n        })\n      });\n      if (!courseResponse.ok) {\n        const errorData = await courseResponse.json();\n        throw new Error(errorData.error || 'Failed to update course');\n      }\n\n      // Process modules, chapters, and quizzes\n      for (const moduleData of updateData.modules) {\n        console.log(`Processing module: ${moduleData.name}`, {\n          id: moduleData.id,\n          hasModuleQuiz: !!moduleData.quiz,\n          moduleQuizId: moduleData.quiz?.id\n        });\n        let currentModuleId = moduleData.id;\n        if (moduleData.id) {\n          // Update existing module\n          const moduleResponse = await fetch(`/api/modules/${moduleData.id}`, {\n            method: 'PUT',\n            headers: {\n              'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n              name: moduleData.name,\n              description: moduleData.description,\n              orderIndex: moduleData.orderIndex,\n              teacherId: user.id\n            })\n          });\n          if (!moduleResponse.ok) {\n            console.error('Failed to update module:', moduleData.id);\n          }\n        } else {\n          // Create new module\n          const newModuleResponse = await fetch('/api/modules', {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n              name: moduleData.name,\n              description: moduleData.description,\n              orderIndex: moduleData.orderIndex,\n              courseId: parseInt(id),\n              teacherId: user.id\n            })\n          });\n          if (newModuleResponse.ok) {\n            const newModule = await newModuleResponse.json();\n            currentModuleId = newModule.module.id;\n            console.log('Created new module with ID:', currentModuleId);\n          }\n        }\n\n        // Update chapters\n        for (const chapter of moduleData.chapters) {\n          let currentChapterId = chapter.id;\n          if (chapter.id) {\n            // Update existing chapter\n            const chapterResponse = await fetch(`/api/chapters/${chapter.id}`, {\n              method: 'PUT',\n              headers: {\n                'Content-Type': 'application/json'\n              },\n              body: JSON.stringify({\n                name: chapter.name,\n                content: chapter.content,\n                orderIndex: chapter.orderIndex,\n                teacherId: user.id\n              })\n            });\n            if (!chapterResponse.ok) {\n              console.error('Failed to update chapter:', chapter.id);\n            }\n          } else {\n            // Create new chapter\n            const newChapterResponse = await fetch('/api/chapters', {\n              method: 'POST',\n              headers: {\n                'Content-Type': 'application/json'\n              },\n              body: JSON.stringify({\n                name: chapter.name,\n                content: chapter.content,\n                orderIndex: chapter.orderIndex,\n                moduleId: currentModuleId,\n                teacherId: user.id\n              })\n            });\n            if (newChapterResponse.ok) {\n              const newChapter = await newChapterResponse.json();\n              currentChapterId = newChapter.chapter.id;\n              console.log('Created new chapter with ID:', currentChapterId);\n            }\n          }\n\n          // Handle chapter quiz\n          if (chapter.quiz) {\n            console.log(`Processing chapter quiz for chapter ${currentChapterId}:`, chapter.quiz.id);\n            if (chapter.quiz.id) {\n              // Update existing chapter quiz\n              const quizResponse = await fetch(`/api/quizzes/${chapter.quiz.id}`, {\n                method: 'PUT',\n                headers: {\n                  'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                  name: chapter.quiz.name,\n                  description: chapter.quiz.description,\n                  quizType: 'chapter',\n                  timeLimit: chapter.quiz.timeLimit,\n                  minimumScore: chapter.quiz.minimumScore,\n                  teacherId: user.id,\n                  questions: chapter.quiz.questions\n                })\n              });\n              if (!quizResponse.ok) {\n                console.error('Failed to update chapter quiz:', chapter.quiz.id);\n                const errorData = await quizResponse.json();\n                console.error('Chapter quiz update error:', errorData);\n              }\n            } else {\n              // Create new chapter quiz\n              const quizResponse = await fetch('/api/quizzes', {\n                method: 'POST',\n                headers: {\n                  'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                  name: chapter.quiz.name,\n                  description: chapter.quiz.description,\n                  quizType: 'chapter',\n                  timeLimit: chapter.quiz.timeLimit,\n                  minimumScore: chapter.quiz.minimumScore,\n                  chapterId: currentChapterId,\n                  teacherId: user.id,\n                  questions: chapter.quiz.questions\n                })\n              });\n              if (!quizResponse.ok) {\n                console.error('Failed to create chapter quiz');\n                const errorData = await quizResponse.json();\n                console.error('Chapter quiz creation error:', errorData);\n              }\n            }\n          }\n        }\n\n        // Handle module quiz - FIXED: Proper handling\n        if (moduleData.quiz) {\n          console.log(`Processing module quiz for module ${currentModuleId}:`, {\n            quizId: moduleData.quiz.id,\n            quizName: moduleData.quiz.name,\n            questionsCount: moduleData.quiz.questions.length\n          });\n          if (moduleData.quiz.id) {\n            // Update existing module quiz\n            const quizResponse = await fetch(`/api/quizzes/${moduleData.quiz.id}`, {\n              method: 'PUT',\n              headers: {\n                'Content-Type': 'application/json'\n              },\n              body: JSON.stringify({\n                name: moduleData.quiz.name,\n                description: moduleData.quiz.description,\n                quizType: 'module',\n                timeLimit: moduleData.quiz.timeLimit,\n                minimumScore: moduleData.quiz.minimumScore,\n                moduleId: currentModuleId,\n                // Ensure moduleId is set\n                teacherId: user.id,\n                questions: moduleData.quiz.questions\n              })\n            });\n            if (!quizResponse.ok) {\n              console.error('Failed to update module quiz:', moduleData.quiz.id);\n              const errorData = await quizResponse.json();\n              console.error('Module quiz update error:', errorData);\n            } else {\n              console.log('Successfully updated module quiz:', moduleData.quiz.id);\n            }\n          } else {\n            // Create new module quiz\n            const quizResponse = await fetch('/api/quizzes', {\n              method: 'POST',\n              headers: {\n                'Content-Type': 'application/json'\n              },\n              body: JSON.stringify({\n                name: moduleData.quiz.name,\n                description: moduleData.quiz.description,\n                quizType: 'module',\n                timeLimit: moduleData.quiz.timeLimit,\n                minimumScore: moduleData.quiz.minimumScore,\n                moduleId: currentModuleId,\n                teacherId: user.id,\n                questions: moduleData.quiz.questions\n              })\n            });\n            if (!quizResponse.ok) {\n              console.error('Failed to create module quiz');\n              const errorData = await quizResponse.json();\n              console.error('Module quiz creation error:', errorData);\n            } else {\n              const newQuiz = await quizResponse.json();\n              console.log('Successfully created module quiz:', newQuiz.quiz?.id);\n            }\n          }\n        }\n      }\n\n      // Handle final exam - FIXED: Proper handling\n      if (updateData.finalExam) {\n        console.log('Processing final exam:', {\n          examId: updateData.finalExam.id,\n          examName: updateData.finalExam.name,\n          questionsCount: updateData.finalExam.questions.length\n        });\n        if (updateData.finalExam.id) {\n          // Update existing final exam\n          const examResponse = await fetch(`/api/quizzes/${updateData.finalExam.id}`, {\n            method: 'PUT',\n            headers: {\n              'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n              name: updateData.finalExam.name,\n              description: updateData.finalExam.description,\n              quizType: 'final',\n              timeLimit: updateData.finalExam.timeLimit,\n              minimumScore: updateData.finalExam.minimumScore,\n              courseId: parseInt(id),\n              // Ensure courseId is set\n              teacherId: user.id,\n              questions: updateData.finalExam.questions\n            })\n          });\n          if (!examResponse.ok) {\n            console.error('Failed to update final exam:', updateData.finalExam.id);\n            const errorData = await examResponse.json();\n            console.error('Final exam update error:', errorData);\n          } else {\n            console.log('Successfully updated final exam:', updateData.finalExam.id);\n          }\n        } else {\n          // Create new final exam\n          const examResponse = await fetch('/api/quizzes', {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n              name: updateData.finalExam.name,\n              description: updateData.finalExam.description,\n              quizType: 'final',\n              timeLimit: updateData.finalExam.timeLimit,\n              minimumScore: updateData.finalExam.minimumScore,\n              courseId: parseInt(id),\n              teacherId: user.id,\n              questions: updateData.finalExam.questions\n            })\n          });\n          if (!examResponse.ok) {\n            console.error('Failed to create final exam');\n            const errorData = await examResponse.json();\n            console.error('Final exam creation error:', errorData);\n          } else {\n            const newExam = await examResponse.json();\n            console.log('Successfully created final exam:', newExam.quiz?.id);\n          }\n        }\n      }\n      toast.success('Course updated successfully!');\n      router.push('/dashboard/teacher/courses');\n    } catch (error) {\n      console.error('Error updating course:', error);\n      toast.error(error instanceof Error ? error.message : 'Failed to update course');\n    } finally {\n      setIsUpdating(false);\n    }\n  };\n  const handleCancel = () => {\n    router.push('/dashboard/teacher/courses');\n  };\n  if (isLoading) {\n    return <div className=\"container mx-auto p-6\">\r\n        <div className=\"mb-6\">\r\n          <Skeleton className=\"h-8 w-48 mb-2\" />\r\n          <Skeleton className=\"h-4 w-96\" />\r\n        </div>\r\n        <Card>\r\n          <CardHeader>\r\n            <Skeleton className=\"h-6 w-32\" />\r\n            <Skeleton className=\"h-4 w-64\" />\r\n          </CardHeader>\r\n          <CardContent>\r\n            <div className=\"space-y-4\">\r\n              <Skeleton className=\"h-10 w-full\" />\r\n              <Skeleton className=\"h-20 w-full\" />\r\n              <Skeleton className=\"h-10 w-full\" />\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n      </div>;\n  }\n  if (error) {\n    return <div className=\"container mx-auto p-6\">\r\n        <div className=\"mb-6\">\r\n          <Link href=\"/dashboard/teacher/courses\">\r\n            <Button variant=\"ghost\" className=\"mb-4\">\r\n              <ArrowLeft className=\"h-4 w-4 mr-2\" />\r\n              Back to Courses\r\n            </Button>\r\n          </Link>\r\n          <h1 className=\"text-3xl font-bold\">Edit Course</h1>\r\n          <p className=\"text-muted-foreground\">Update course information and content</p>\r\n        </div>\r\n        \r\n        <Card>\r\n          <CardHeader>\r\n            <CardTitle className=\"flex items-center gap-2 text-destructive\">\r\n              <AlertCircle className=\"h-5 w-5\" />\r\n              Error Loading Course\r\n            </CardTitle>\r\n            <CardDescription>{error}</CardDescription>\r\n          </CardHeader>\r\n          <CardContent>\r\n            <Button onClick={fetchCourseData} variant=\"outline\">\r\n              Try Again\r\n            </Button>\r\n          </CardContent>\r\n        </Card>\r\n      </div>;\n  }\n  if (!courseData) {\n    return <div className=\"container mx-auto p-6\">\r\n        <div className=\"mb-6\">\r\n          <Link href=\"/dashboard/teacher/courses\">\r\n            <Button variant=\"ghost\" className=\"mb-4\">\r\n              <ArrowLeft className=\"h-4 w-4 mr-2\" />\r\n              Back to Courses\r\n            </Button>\r\n          </Link>\r\n          <h1 className=\"text-3xl font-bold\">Edit Course</h1>\r\n          <p className=\"text-muted-foreground\">Update course information and content</p>\r\n        </div>\r\n        \r\n        <Card>\r\n          <CardHeader>\r\n            <CardTitle>Course Not Found</CardTitle>\r\n            <CardDescription>The requested course could not be found.</CardDescription>\r\n          </CardHeader>\r\n          <CardContent>\r\n            <Link href=\"/dashboard/teacher/courses\">\r\n              <Button>Back to Courses</Button>\r\n            </Link>\r\n          </CardContent>\r\n        </Card>\r\n      </div>;\n  }\n  return <div className=\"container mx-auto p-6\" data-sentry-component=\"CourseEditPage\" data-sentry-source-file=\"page.tsx\">\r\n      <div className=\"mb-6\">\r\n        <Link href=\"/dashboard/teacher/courses\" data-sentry-element=\"Link\" data-sentry-source-file=\"page.tsx\">\r\n          <Button variant=\"ghost\" className=\"mb-4\" data-sentry-element=\"Button\" data-sentry-source-file=\"page.tsx\">\r\n            <ArrowLeft className=\"h-4 w-4 mr-2\" data-sentry-element=\"ArrowLeft\" data-sentry-source-file=\"page.tsx\" />\r\n            Back to Courses\r\n          </Button>\r\n        </Link>\r\n        <h1 className=\"text-3xl font-bold\">Edit Course</h1>\r\n        <p className=\"text-muted-foreground\">Update course information and content</p>\r\n      </div>\r\n\r\n      <CourseCreationWizard initialData={courseData} onComplete={handleCourseUpdate} onCancel={handleCancel} data-sentry-element=\"CourseCreationWizard\" data-sentry-source-file=\"page.tsx\" />\r\n    </div>;\n}", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\courses\\\\[id]\\\\page.tsx\");\n", "module.exports = require(\"node:fs\");", "module.exports = require(\"node:url\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\courses\\\\[id]\\\\page.tsx\");\n", "module.exports = require(\"events\");"], "names": ["serverComponentModule.default", "transformQuestion", "apiQuestion", "id", "toString", "type", "question", "value", "options", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "explanation", "points", "orderIndex", "transformQuiz", "apiQuiz", "name", "description", "questions", "map", "timeLimit", "minimumScore", "parseInt", "transformChapter", "apiChapter", "chapterQuiz", "quizzes", "find", "quiz", "quizType", "content", "JSON", "parse", "error", "console", "hasChapterQuiz", "CourseEditPage", "params", "use", "router", "useRouter", "courseData", "setCourseData", "useState", "isLoading", "setIsLoading", "setError", "isUpdating", "setIsUpdating", "fetchCourseData", "user", "authStorage", "getUser", "response", "fetch", "data", "json", "ok", "success", "course", "apiCourse", "log", "moduleQuizzes", "finalExam", "actualModuleQuizzes", "filter", "totalQuizzes", "length", "q", "moduleId", "courseId", "transformedData", "transformApiCourseToWizardData", "moduleQuizzesCount", "allQuizzes", "actualFinalExam", "instructor", "courseCode", "enrollmentType", "startDate", "Date", "endDate", "coverImagePreview", "coverImage", "coverPicture", "isPurchasable", "price", "currency", "previewMode", "modules", "module", "transformModule", "moduleQuiz", "apiModule", "chapters", "hasModuleQuiz", "isPublished", "assignedClasses", "admissions", "requirements", "applicationDeadline", "prerequisites", "academics", "credits", "workload", "assessment", "tuitionAndFinancing", "totalCost", "paymentOptions", "scholarships", "careers", "outcomes", "industries", "averageSalary", "studentExperience", "testimonials", "facilities", "support", "Error", "message", "handleCourseUpdate", "updatedCourseData", "toast", "updateData", "transformWizardDataToApiUpdate", "modulesCount", "finalExamExists", "m", "toISOString", "moduleQuizId", "isNaN", "chapter", "courseResponse", "method", "headers", "body", "stringify", "teacherId", "errorData", "moduleData", "currentModuleId", "newModuleResponse", "newModule", "currentChapterId", "newChapterResponse", "newChapter", "quizResponse", "chapterId", "quizId", "quizName", "questionsCount", "newQuiz", "examId", "examName", "examResponse", "newExam", "push", "div", "className", "Skeleton", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Link", "href", "<PERSON><PERSON>", "variant", "ArrowLeft", "h1", "p", "CardTitle", "AlertCircle", "CardDescription", "onClick", "data-sentry-component", "data-sentry-source-file", "data-sentry-element", "CourseCreationWizard", "initialData", "onComplete", "onCancel", "handleCancel"], "sourceRoot": ""}