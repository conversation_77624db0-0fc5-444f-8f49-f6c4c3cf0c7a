/* [next]/internal/font/google/mulish_d412d3cf.module.css [app-client] (css) */
@font-face {
  font-family: Mulish;
  font-style: normal;
  font-weight: 200 1000;
  font-display: swap;
  src: url("../media/1Ptvg83HX_SGhgqk0gotYKNnBcif-s.194a9483.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Mulish;
  font-style: normal;
  font-weight: 200 1000;
  font-display: swap;
  src: url("../media/1Ptvg83HX_SGhgqk2wotYKNnBcif-s.a8c34c9c.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Mulish;
  font-style: normal;
  font-weight: 200 1000;
  font-display: swap;
  src: url("../media/1Ptvg83HX_SGhgqk0AotYKNnBcif-s.574ea7be.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Mulish;
  font-style: normal;
  font-weight: 200 1000;
  font-display: swap;
  src: url("../media/1Ptvg83HX_SGhgqk0QotYKNnBcif-s.c6739679.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Mulish;
  font-style: normal;
  font-weight: 200 1000;
  font-display: swap;
  src: url("../media/1Ptvg83HX_SGhgqk3wotYKNnBQ-s.p.211e7ae2.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Mulish Fallback;
  src: local(Arial);
  ascent-override: 96.56%;
  descent-override: 24.02%;
  line-gap-override: 0.0%;
  size-adjust: 104.08%;
}

.mulish_d412d3cf-module__oKc9Da__className {
  font-family: Mulish, Mulish Fallback;
  font-style: normal;
}

.mulish_d412d3cf-module__oKc9Da__variable {
  --font-mullish: "Mulish", "Mulish Fallback";
}

/*# sourceMappingURL=%5Bnext%5D_internal_font_google_mulish_d412d3cf_module_css_f9ee138c._.single.css.map*/