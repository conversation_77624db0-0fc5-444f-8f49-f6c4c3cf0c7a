module.exports = {

"[project]/src/constants/data.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getNavItems": (()=>getNavItems),
    "navItems": (()=>navItems),
    "recentSalesData": (()=>recentSalesData)
});
const getNavItems = (pathname)=>[
        {
            title: 'Available Courses',
            url: '/courses',
            icon: 'searchList',
            isActive: pathname.startsWith('/courses'),
            shortcut: [
                'a',
                'c'
            ],
            items: [] // Empty array as there are no child items
        },
        {
            title: 'My Courses',
            url: '/my-courses',
            icon: 'graduationCap',
            isActive: pathname.startsWith('/my-courses'),
            shortcut: [
                'm',
                'c'
            ],
            items: [] // Empty array as there are no child items
        }
    ];
const navItems = getNavItems('');
const recentSalesData = [
    {
        id: 1,
        name: '<PERSON>',
        email: '<EMAIL>',
        amount: '+$1,999.00',
        image: 'https://api.slingacademy.com/public/sample-users/1.png',
        initials: 'OM'
    },
    {
        id: 2,
        name: 'Jackson Lee',
        email: '<EMAIL>',
        amount: '+$39.00',
        image: 'https://api.slingacademy.com/public/sample-users/2.png',
        initials: 'JL'
    },
    {
        id: 3,
        name: 'Isabella Nguyen',
        email: '<EMAIL>',
        amount: '+$299.00',
        image: 'https://api.slingacademy.com/public/sample-users/3.png',
        initials: 'IN'
    },
    {
        id: 4,
        name: 'William Kim',
        email: '<EMAIL>',
        amount: '+$99.00',
        image: 'https://api.slingacademy.com/public/sample-users/4.png',
        initials: 'WK'
    },
    {
        id: 5,
        name: 'Sofia Davis',
        email: '<EMAIL>',
        amount: '+$39.00',
        image: 'https://api.slingacademy.com/public/sample-users/5.png',
        initials: 'SD'
    }
];
}}),
"[project]/src/components/kbar/result-item.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
;
const ResultItem = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ action, active, currentRootActionId }, ref)=>{
    const ancestors = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        if (!currentRootActionId) return action.ancestors;
        const index = action.ancestors.findIndex((ancestor)=>ancestor.id === currentRootActionId);
        return action.ancestors.slice(index + 1);
    }, [
        action.ancestors,
        currentRootActionId
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        ref: ref,
        className: `relative z-10 flex cursor-pointer items-center justify-between px-4 py-3`,
        children: [
            active && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                id: "kbar-result-item",
                className: "border-primary bg-accent/50 absolute inset-0 z-[-1]! border-l-4"
            }, void 0, false, {
                fileName: "[project]/src/components/kbar/result-item.tsx",
                lineNumber: 31,
                columnNumber: 11
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "relative z-10 flex items-center gap-2",
                children: [
                    action.icon && action.icon,
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex flex-col",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    ancestors.length > 0 && ancestors.map((ancestor)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "text-muted-foreground mr-2",
                                                    children: ancestor.name
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/kbar/result-item.tsx",
                                                    lineNumber: 43,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "mr-2",
                                                    children: "›"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/kbar/result-item.tsx",
                                                    lineNumber: 46,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, ancestor.id, true, {
                                            fileName: "[project]/src/components/kbar/result-item.tsx",
                                            lineNumber: 42,
                                            columnNumber: 19
                                        }, this)),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: action.name
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/kbar/result-item.tsx",
                                        lineNumber: 49,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/kbar/result-item.tsx",
                                lineNumber: 39,
                                columnNumber: 13
                            }, this),
                            action.subtitle && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-muted-foreground text-sm",
                                children: action.subtitle
                            }, void 0, false, {
                                fileName: "[project]/src/components/kbar/result-item.tsx",
                                lineNumber: 52,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/kbar/result-item.tsx",
                        lineNumber: 38,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/kbar/result-item.tsx",
                lineNumber: 36,
                columnNumber: 9
            }, this),
            action.shortcut?.length ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "relative z-10 grid grid-flow-col gap-1",
                children: action.shortcut.map((sc, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("kbd", {
                        className: "bg-muted flex h-5 items-center gap-1 rounded-md border px-1.5 text-[10px] font-medium",
                        children: sc
                    }, sc + i, false, {
                        fileName: "[project]/src/components/kbar/result-item.tsx",
                        lineNumber: 61,
                        columnNumber: 15
                    }, this))
            }, void 0, false, {
                fileName: "[project]/src/components/kbar/result-item.tsx",
                lineNumber: 59,
                columnNumber: 11
            }, this) : null
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/kbar/result-item.tsx",
        lineNumber: 26,
        columnNumber: 7
    }, this);
});
ResultItem.displayName = 'KBarResultItem';
const __TURBOPACK__default__export__ = ResultItem;
}}),
"[project]/src/components/kbar/render-result.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>RenderResults)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$kbar$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/kbar/lib/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$kbar$2f$result$2d$item$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/kbar/result-item.tsx [app-ssr] (ecmascript)");
;
;
;
function RenderResults() {
    const { results, rootActionId } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$kbar$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMatches"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$kbar$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["KBarResults"], {
        items: results,
        onRender: ({ item, active })=>typeof item === 'string' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-primary-foreground px-4 py-2 text-sm uppercase opacity-50",
                children: item
            }, void 0, false, {
                fileName: "[project]/src/components/kbar/render-result.tsx",
                lineNumber: 12,
                columnNumber: 11
            }, void 0) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$kbar$2f$result$2d$item$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                action: item,
                active: active,
                currentRootActionId: rootActionId ?? ''
            }, void 0, false, {
                fileName: "[project]/src/components/kbar/render-result.tsx",
                lineNumber: 16,
                columnNumber: 11
            }, void 0)
    }, void 0, false, {
        fileName: "[project]/src/components/kbar/render-result.tsx",
        lineNumber: 8,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/components/kbar/use-theme-switching.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$kbar$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/kbar/lib/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-themes/dist/index.mjs [app-ssr] (ecmascript)");
;
;
const useThemeSwitching = ()=>{
    const { theme, setTheme } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useTheme"])();
    const toggleTheme = ()=>{
        setTheme(theme === 'light' ? 'dark' : 'light');
    };
    const themeAction = [
        {
            id: 'toggleTheme',
            name: 'Toggle Theme',
            shortcut: [
                't',
                't'
            ],
            section: 'Theme',
            perform: toggleTheme
        },
        {
            id: 'setLightTheme',
            name: 'Set Light Theme',
            section: 'Theme',
            perform: ()=>setTheme('light')
        },
        {
            id: 'setDarkTheme',
            name: 'Set Dark Theme',
            section: 'Theme',
            perform: ()=>setTheme('dark')
        }
    ];
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$kbar$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRegisterActions"])(themeAction, [
        theme
    ]);
};
const __TURBOPACK__default__export__ = useThemeSwitching;
}}),
"[project]/src/components/kbar/index.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>KBar)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/data.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$kbar$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/kbar/lib/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$kbar$2f$render$2d$result$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/kbar/render-result.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$kbar$2f$use$2d$theme$2d$switching$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/kbar/use-theme-switching.tsx [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
;
function KBar({ children }) {
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    // These action are for the navigation
    const actions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        // Define navigateTo inside the useMemo callback to avoid dependency array issues
        const navigateTo = (url)=>{
            router.push(url);
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["navItems"].flatMap((navItem)=>{
            // Only include base action if the navItem has a real URL and is not just a container
            const baseAction = navItem.url !== '#' ? {
                id: `${navItem.title.toLowerCase()}Action`,
                name: navItem.title,
                shortcut: navItem.shortcut,
                keywords: navItem.title.toLowerCase(),
                section: 'Navigation',
                subtitle: `Go to ${navItem.title}`,
                perform: ()=>navigateTo(navItem.url)
            } : null;
            // Map child items into actions
            const childActions = navItem.items?.map((childItem)=>({
                    id: `${childItem.title.toLowerCase()}Action`,
                    name: childItem.title,
                    shortcut: childItem.shortcut,
                    keywords: childItem.title.toLowerCase(),
                    section: navItem.title,
                    subtitle: `Go to ${childItem.title}`,
                    perform: ()=>navigateTo(childItem.url)
                })) ?? [];
            // Return only valid actions (ignoring null base actions for containers)
            return baseAction ? [
                baseAction,
                ...childActions
            ] : childActions;
        });
    }, [
        router
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$kbar$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["KBarProvider"], {
        actions: actions,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(KBarComponent, {
            children: children
        }, void 0, false, {
            fileName: "[project]/src/components/kbar/index.tsx",
            lineNumber: 59,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/kbar/index.tsx",
        lineNumber: 58,
        columnNumber: 5
    }, this);
}
const KBarComponent = ({ children })=>{
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$kbar$2f$use$2d$theme$2d$switching$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$kbar$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["KBarPortal"], {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$kbar$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["KBarPositioner"], {
                    className: "bg-background/80 fixed inset-0 z-99999 p-0! backdrop-blur-sm",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$kbar$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["KBarAnimator"], {
                        className: "bg-card text-card-foreground relative mt-64! w-full max-w-[600px] -translate-y-12! overflow-hidden rounded-lg border shadow-lg",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-card border-border sticky top-0 z-10 border-b",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$kbar$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["KBarSearch"], {
                                    className: "bg-card w-full border-none px-6 py-4 text-lg outline-hidden focus:ring-0 focus:ring-offset-0 focus:outline-hidden"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/kbar/index.tsx",
                                    lineNumber: 72,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/kbar/index.tsx",
                                lineNumber: 71,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "max-h-[400px]",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$kbar$2f$render$2d$result$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                    fileName: "[project]/src/components/kbar/index.tsx",
                                    lineNumber: 75,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/kbar/index.tsx",
                                lineNumber: 74,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/kbar/index.tsx",
                        lineNumber: 70,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/kbar/index.tsx",
                    lineNumber: 69,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/kbar/index.tsx",
                lineNumber: 68,
                columnNumber: 7
            }, this),
            children
        ]
    }, void 0, true);
};
}}),
"[project]/src/constants/shared-course-data.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "architectureClass": (()=>architectureClass),
    "architectureCourse": (()=>architectureCourse),
    "architectureInstitution": (()=>architectureInstitution)
});
const architectureCourse = {
    id: 'arch-cert-001',
    name: 'Sertifikasi Arsitek Profesional IAI',
    code: 'IAI-CERT-001',
    description: 'Program sertifikasi profesional untuk arsitek yang ingin memperoleh sertifikat Ikatan Arsitek Indonesia (IAI) dan meningkatkan kompetensi di bidang perencanaan dan pengawasan bangunan',
    instructor: 'Ar. Georgius Budi Yulianto, IAI, AA',
    startDate: '2024-08-01',
    endDate: '2024-12-31',
    enrollmentType: 'both',
    enrollmentCode: 'IAI-CERT-2024',
    minPassingScore: 80,
    totalProgress: 0,
    status: 'not-started',
    modules: [
        {
            id: 'mod1',
            title: 'Modul 1: Dasar-Dasar Arsitektur',
            description: 'Memahami prinsip-prinsip dasar arsitektur dan sejarahnya',
            order: 1,
            isUnlocked: true,
            completionPercentage: 0,
            chapters: [
                {
                    id: 'ch1',
                    title: 'Bab 1: Sejarah Arsitektur',
                    order: 1,
                    isUnlocked: true,
                    completionPercentage: 0,
                    contents: [
                        {
                            id: 'content1',
                            title: 'Prinsip-Prinsip Arsitektur Kuno',
                            type: 'text',
                            content: `# Ancient Architecture Principles

## Introduction

Architecture has been a **fundamental aspect** of human civilization since ancient times. From the pyramids of Egypt to the temples of Greece, architectural principles have evolved to reflect cultural values, technological advances, and environmental considerations.

## Key Civilizations and Their Contributions

### 1. Egyptian Architecture
- **Pyramids**: Monumental structures demonstrating advanced engineering
- **Materials**: Limestone, granite, and sandstone
- **Key Features**:
  - Massive scale
  - Precise geometric proportions
  - Durability across millennia

### 2. Greek Architecture
The Greeks developed the classical orders that influence architecture to this day:

1. **Doric Order** - Simple and sturdy
2. **Ionic Order** - Elegant with scroll-like capitals
3. **Corinthian Order** - Ornate with acanthus leaf decorations

### 3. Roman Architecture
Romans revolutionized construction with:
- **Concrete technology**
- **Arches and vaults**
- **Aqueducts and infrastructure**

## Fundamental Principles

> "Architecture is the learned game, correct and magnificent, of forms assembled in the light." - Le Corbusier

### Proportion and Scale
- **Golden Ratio**: φ ≈ 1.618
- **Human scale**: Buildings designed for human comfort
- **Visual balance**: Creating harmony through proportional relationships

### Materials and Structure
\`\`\`
Load-bearing elements:
├── Walls
├── Columns
├── Beams
└── Foundations
\`\`\`

## Learning Objectives
By the end of this section, you will understand:
- [ ] The evolution of architectural styles
- [ ] Key principles of proportion and scale
- [ ] Material properties and their applications
- [ ] Cultural influences on architectural design

---

*Next: We'll explore how these ancient principles influenced Renaissance and modern architecture.*`,
                            isCompleted: false,
                            createdAt: '2024-01-15'
                        },
                        {
                            id: 'content2',
                            title: 'Tata Orde Klasik dalam Arsitektur',
                            type: 'video',
                            content: 'https://example.com/classical-orders-video',
                            duration: 25,
                            isCompleted: false,
                            createdAt: '2024-01-16'
                        },
                        {
                            id: 'content3',
                            title: 'Dasar-Dasar Gambar Arsitektur',
                            type: 'pdf',
                            content: 'https://example.com/drawing-fundamentals.pdf',
                            isCompleted: false,
                            createdAt: '2024-01-17'
                        }
                    ],
                    quiz: {
                        id: 'quiz-ch1',
                        title: 'Kuis Bab 1: Sejarah Arsitektur',
                        type: 'chapter',
                        questions: [
                            {
                                id: 'q1',
                                question: 'Which architectural order is characterized by simple, sturdy columns?',
                                type: 'multiple-choice',
                                options: [
                                    'Doric',
                                    'Ionic',
                                    'Corinthian',
                                    'Composite'
                                ],
                                correctAnswer: 0,
                                explanation: 'The Doric order is the simplest and most robust of the classical orders.'
                            }
                        ],
                        minimumScore: 70,
                        timeLimit: 15,
                        attempts: 0,
                        maxAttempts: 3,
                        isPassed: false
                    }
                },
                {
                    id: 'ch2',
                    title: 'Bab 2: Prinsip-Prinsip Desain',
                    order: 2,
                    isUnlocked: false,
                    completionPercentage: 0,
                    contents: [
                        {
                            id: 'content4',
                            title: 'Proporsi dan Skala',
                            type: 'text',
                            content: `# Proportion and Scale in Architecture

## What is Proportion?

**Proportion** refers to the relationship between different elements in architectural design. It's the foundation of creating visually pleasing and harmonious structures.

## The Golden Ratio (φ = 1.618)

The **Golden Ratio** has been used in architecture for millennia:

| Building | Golden Ratio Application |
|----------|-------------------------|
| Parthenon | Façade proportions |
| Notre-Dame | Rose window design |
| Villa Savoye | Room dimensions |

### Mathematical Expression
\`\`\`
φ = (1 + √5) / 2 ≈ 1.618033988...
\`\`\`

## Types of Scale

### 1. Human Scale
Buildings should relate to human dimensions:
- **Door heights**: ~2.1m (7 feet)
- **Ceiling heights**: 2.4-3.0m (8-10 feet)
- **Stair risers**: 15-18cm (6-7 inches)

### 2. Architectural Scale
How building elements relate to each other:
- Window to wall ratios
- Column spacing
- Room proportions

### 3. Urban Scale
How buildings relate to their context:
- Street width to building height
- Public space dimensions
- Neighborhood character

## Classical Proportional Systems

### Vitruvian Proportions
Based on the human body:
> "The human body is a model of proportion because with arms or legs extended it fits into those 'perfect' geometrical forms, the square and the circle."

### Modular Systems
- **Le Corbusier's Modulor**: Based on human measurements
- **Japanese Ken**: Traditional proportional system
- **Classical Orders**: Column to entablature ratios

## Design Exercise

Try this proportional analysis:
1. Find a building you admire
2. Measure key dimensions
3. Calculate the ratios
4. Compare to golden ratio or other systems

## Key Takeaways

✅ **Good proportion creates visual harmony**  
✅ **Human scale ensures comfort**  
✅ **Mathematical ratios provide guidelines**  
✅ **Context matters in scale decisions**  

---

*Remember: Proportion is felt, not calculated. Trust your eye, but understand the principles.*`,
                            isCompleted: false,
                            createdAt: '2024-01-18'
                        },
                        {
                            id: 'content5',
                            title: 'Rasio Emas dalam Arsitektur',
                            type: 'zoom-recording',
                            content: 'https://zoom.us/rec/golden-ratio-lecture',
                            duration: 45,
                            isCompleted: false,
                            createdAt: '2024-01-19'
                        }
                    ],
                    quiz: {
                        id: 'quiz-ch2',
                        title: 'Kuis Bab 2: Prinsip-Prinsip Desain',
                        type: 'chapter',
                        questions: [
                            {
                                id: 'q2',
                                question: 'What is the Golden Ratio approximately equal to?',
                                type: 'multiple-choice',
                                options: [
                                    '1.414',
                                    '1.618',
                                    '1.732',
                                    '2.000'
                                ],
                                correctAnswer: 1,
                                explanation: 'The Golden Ratio is approximately 1.618 and is commonly used in architectural proportions.'
                            },
                            {
                                id: 'q3',
                                question: 'Proportion in architecture refers to the relationship between different elements.',
                                type: 'true-false',
                                correctAnswer: 'true',
                                explanation: 'Proportion is indeed about the harmonious relationship between different architectural elements.'
                            }
                        ],
                        minimumScore: 70,
                        attempts: 0,
                        maxAttempts: 3,
                        isPassed: false
                    }
                }
            ],
            moduleQuiz: {
                id: 'quiz-mod1',
                title: 'Kuis Akhir Modul 1',
                type: 'module',
                questions: [
                    {
                        id: 'qm1',
                        question: 'Which of the following are classical architectural orders? (Select all that apply)',
                        type: 'multiple-choice',
                        options: [
                            'Doric',
                            'Ionic',
                            'Corinthian',
                            'All of the above'
                        ],
                        correctAnswer: 3,
                        explanation: 'Doric, Ionic, and Corinthian are the three main classical architectural orders.'
                    }
                ],
                minimumScore: 75,
                attempts: 0,
                maxAttempts: 2,
                isPassed: false
            }
        },
        {
            id: 'mod2',
            title: 'Modul 2: Arsitektur Kontemporer',
            description: 'Gerakan arsitektur modern dan desain berkelanjutan',
            order: 2,
            isUnlocked: false,
            completionPercentage: 0,
            chapters: [
                {
                    id: 'ch3',
                    title: 'Bab 3: Gerakan Modern',
                    order: 1,
                    isUnlocked: false,
                    completionPercentage: 0,
                    contents: [
                        {
                            id: 'content6',
                            title: 'Gerakan Bauhaus',
                            type: 'text',
                            content: `# The Bauhaus Movement: Form Follows Function

## Overview

The **Bauhaus** (1919-1933) was a revolutionary design school that fundamentally changed architecture, art, and design. Founded by Walter Gropius in Weimar, Germany, it promoted the integration of art, craft, and industrial technology.

## Core Principles

### 1. **Form Follows Function**
- Design should be determined by purpose
- No unnecessary ornamentation
- Efficiency in both form and construction

### 2. **Gesamtkunstwerk** (Total Work of Art)
- Integration of all arts and crafts
- Architecture as the master art
- Unified design philosophy

### 3. **Machine Aesthetic**
- Embrace industrial production methods
- Clean lines and geometric forms
- Mass production capabilities

## Key Figures

| Name | Role | Contribution |
|------|------|-------------|
| **Walter Gropius** | Founder & Director | Established core philosophy |
| **Ludwig Mies van der Rohe** | Director (1930-33) | "Less is more" principle |
| **Marcel Breuer** | Faculty | Furniture design innovation |
| **László Moholy-Nagy** | Faculty | Photography and typography |

## Architectural Characteristics

### Visual Elements
- ✅ **Clean geometric forms**
- ✅ **Flat roofs**
- ✅ **Large windows**
- ✅ **Open floor plans**
- ❌ Historical references
- ❌ Decorative elements

### Materials
\`\`\`
Primary materials:
├── Steel frame construction
├── Glass curtain walls
├── Reinforced concrete
└── Industrial materials
\`\`\`

## Famous Bauhaus Buildings

### 1. Fagus Factory (1911-1913)
- **Architect**: Walter Gropius
- **Innovation**: Glass curtain wall
- **Significance**: Proto-Bauhaus design

### 2. Bauhaus Dessau (1925-1926)
- **Architect**: Walter Gropius
- **Features**: Modular design, industrial materials
- **Legacy**: Became the school's permanent home

### 3. Barcelona Pavilion (1929)
- **Architect**: Mies van der Rohe
- **Concept**: Flowing space, minimal structure
- **Impact**: Redefined modern architecture

## Impact on Modern Architecture

> "The ultimate goal of all visual arts is the complete building!" - Walter Gropius

### International Style
The Bauhaus influenced what became known as the **International Style**:
- Emphasis on volume over mass
- Regularity rather than symmetry
- No applied ornamentation

### Urban Planning
- **Functional zoning**
- **Housing for the masses**
- **Integration with landscape**

## Legacy and Criticism

### Positive Impact
- **Democratized design**: Good design for everyone
- **Functional solutions**: Buildings that work efficiently  
- **Modern materials**: Pioneered new construction techniques

### Criticisms
- **Cultural homogenization**: Loss of regional character
- **Human scale**: Sometimes overlooked human needs
- **Context insensitivity**: Universal solutions vs. local conditions

## Contemporary Relevance

The Bauhaus principles remain relevant today:
- **Sustainable design**: Efficiency and functionality
- **Digital integration**: Technology serving human needs
- **Social responsibility**: Design for collective benefit

---

*"The Bauhaus is not a style but a principle - the principle that the artistic process and the industrial process are one."* - Walter Gropius`,
                            isCompleted: false,
                            createdAt: '2024-01-20'
                        }
                    ],
                    quiz: {
                        id: 'quiz-ch3',
                        title: 'Kuis Bab 3',
                        type: 'chapter',
                        questions: [
                            {
                                id: 'q4',
                                question: 'The Bauhaus movement emphasized functionality over ornamentation.',
                                type: 'true-false',
                                correctAnswer: 'true',
                                explanation: 'The Bauhaus movement was known for its emphasis on functionality and minimalism.'
                            }
                        ],
                        minimumScore: 70,
                        attempts: 0,
                        maxAttempts: 3,
                        isPassed: false
                    }
                }
            ],
            moduleQuiz: {
                id: 'quiz-mod2',
                title: 'Kuis Akhir Modul 2',
                type: 'module',
                questions: [
                    {
                        id: 'qm2',
                        question: 'What are the key principles of modern architecture?',
                        type: 'essay',
                        correctAnswer: 'Sample answer about functionality, simplicity, and form follows function',
                        explanation: 'Modern architecture emphasizes functionality, simplicity, and the principle that form should follow function.'
                    }
                ],
                minimumScore: 75,
                attempts: 0,
                maxAttempts: 2,
                isPassed: false
            }
        }
    ],
    finalExam: {
        id: 'final-exam',
        title: 'Ujian Akhir Sertifikasi Arsitektur',
        type: 'final',
        questions: [
            {
                id: 'qf1',
                question: 'Which architectural order features ornate capitals with acanthus leaves?',
                type: 'multiple-choice',
                options: [
                    'Doric',
                    'Ionic',
                    'Corinthian',
                    'Tuscan'
                ],
                correctAnswer: 2,
                explanation: 'The Corinthian order is distinguished by its elaborate capitals decorated with acanthus leaves.'
            },
            {
                id: 'qf2',
                question: 'The Golden Ratio is frequently used in architectural design.',
                type: 'true-false',
                correctAnswer: 'true',
                explanation: 'The Golden Ratio (approximately 1.618) has been used in architecture for centuries to create pleasing proportions.'
            },
            {
                id: 'qf3',
                question: 'Describe the influence of the Bauhaus movement on modern architecture.',
                type: 'essay',
                correctAnswer: 'The Bauhaus movement emphasized functionality, simplicity, and the integration of art and technology, leading to modern minimalist design principles.',
                explanation: 'The Bauhaus movement revolutionized architecture by promoting functional design, clean lines, and the principle that form should follow function.'
            }
        ],
        minimumScore: 80,
        timeLimit: 120,
        attempts: 0,
        maxAttempts: 2,
        isPassed: false
    },
    certificate: {
        isEligible: true,
        isGenerated: false,
        type: 'professional',
        issuedBy: 'Ikatan Arsitek Indonesia (IAI)',
        validityPeriod: '5 years',
        credentialId: 'IAI-CERT-2024',
        description: 'Sertifikat Arsitek Profesional yang diakui secara nasional untuk praktik arsitektur di Indonesia'
    }
};
const architectureClass = {
    id: 1,
    name: architectureCourse.name,
    teacher: architectureCourse.instructor,
    schedule: 'Sen, Rab 10:00 WIB',
    enrollmentCode: architectureCourse.enrollmentCode || 'ARCH2024-001',
    description: architectureCourse.description,
    startDate: architectureCourse.startDate,
    endDate: architectureCourse.endDate,
    materials: [] // Will be populated from modules when enrolled
};
const architectureInstitution = {
    id: 'iai-indonesia',
    name: 'Ikatan Arsitek Indonesia',
    shortName: 'IAI',
    website: 'https://iai.or.id',
    certificateTemplate: {
        primaryColor: '#1e40af',
        secondaryColor: '#f59e0b',
        signatoryName: 'Ar. Georgius Budi Yulianto, IAI, AA',
        signatoryTitle: 'Ketua Umum IAI 2024-2027'
    }
};
}}),
"[project]/src/contexts/enrollment-context.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "EnrollmentProvider": (()=>EnrollmentProvider),
    "useEnrollment": (()=>useEnrollment)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$shared$2d$course$2d$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/shared-course-data.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
const EnrollmentContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const useEnrollment = ()=>{
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(EnrollmentContext);
    if (!context) {
        throw new Error('useEnrollment must be used within an EnrollmentProvider');
    }
    return context;
};
const EnrollmentProvider = ({ children })=>{
    const [isEnrolled, setIsEnrolled] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [courseData, setCourseData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$shared$2d$course$2d$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["architectureCourse"]);
    const [enrolledCourses, setEnrolledCourses] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const STORAGE_KEY = 'lms-enrollment-data';
    const MULTIPLE_STORAGE_KEY = 'lms-multiple-enrollment-data';
    const EXPIRATION_TIME = 10 * 60 * 1000; // 10 minutes in milliseconds
    // Load persisted data on component mount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const loadPersistedData = ()=>{
            try {
                // Try to load multiple enrollment data first
                const multipleStored = localStorage.getItem(MULTIPLE_STORAGE_KEY);
                if (multipleStored) {
                    const multipleData = JSON.parse(multipleStored);
                    const now = Date.now();
                    // Check if enrollment has expired
                    if (now < multipleData.expirationTime) {
                        setEnrolledCourses(multipleData.enrolledCourses);
                        setIsEnrolled(multipleData.enrolledCourses.length > 0);
                        if (multipleData.enrolledCourses.length > 0) {
                            setCourseData(multipleData.enrolledCourses[0]); // Set first course as primary
                        }
                    } else {
                        // Clear expired data
                        localStorage.removeItem(MULTIPLE_STORAGE_KEY);
                    }
                    return;
                }
                // Fallback to old single enrollment data for backward compatibility
                const stored = localStorage.getItem(STORAGE_KEY);
                if (stored) {
                    const enrollmentData = JSON.parse(stored);
                    const now = Date.now();
                    // Check if enrollment has expired
                    if (now < enrollmentData.expirationTime) {
                        setIsEnrolled(enrollmentData.isEnrolled);
                        setCourseData(enrollmentData.courseData);
                        setEnrolledCourses([
                            enrollmentData.courseData
                        ]);
                        // Migrate to new format
                        const multipleData = {
                            enrolledCourses: [
                                enrollmentData.courseData
                            ],
                            enrollmentTimestamp: enrollmentData.enrollmentTimestamp,
                            expirationTime: enrollmentData.expirationTime
                        };
                        localStorage.setItem(MULTIPLE_STORAGE_KEY, JSON.stringify(multipleData));
                        localStorage.removeItem(STORAGE_KEY); // Remove old format
                    } else {
                        // Clear expired data
                        localStorage.removeItem(STORAGE_KEY);
                    }
                }
            } catch (error) {
                console.error('Failed to load enrollment data:', error);
                localStorage.removeItem(STORAGE_KEY);
                localStorage.removeItem(MULTIPLE_STORAGE_KEY);
            }
        };
        loadPersistedData();
    }, []);
    // Persist enrollment data to localStorage
    const persistEnrollmentData = (course)=>{
        const now = Date.now();
        try {
            // Update enrolled courses state
            setEnrolledCourses((prev)=>{
                const isAlreadyEnrolled = prev.some((c)=>c.id === course.id);
                let updatedCourses;
                if (isAlreadyEnrolled) {
                    // Update existing course
                    updatedCourses = prev.map((c)=>c.id === course.id ? course : c);
                } else {
                    // Add new course
                    updatedCourses = [
                        ...prev,
                        course
                    ];
                }
                // Save to localStorage with new format
                const multipleData = {
                    enrolledCourses: updatedCourses,
                    enrollmentTimestamp: now,
                    expirationTime: now + EXPIRATION_TIME
                };
                localStorage.setItem(MULTIPLE_STORAGE_KEY, JSON.stringify(multipleData));
                return updatedCourses;
            });
            // Set up automatic cleanup after expiration
            setTimeout(()=>{
                localStorage.removeItem(MULTIPLE_STORAGE_KEY);
                setIsEnrolled(false);
                setEnrolledCourses([]);
                setCourseData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$shared$2d$course$2d$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["architectureCourse"]);
            }, EXPIRATION_TIME);
        } catch (error) {
            console.error('Failed to persist enrollment data:', error);
        }
    };
    const enrollInCourse = ()=>{
        setIsEnrolled(true);
        const updatedCourse = {
            ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$shared$2d$course$2d$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["architectureCourse"],
            status: 'in-progress'
        };
        setCourseData(updatedCourse);
        persistEnrollmentData(updatedCourse);
    };
    const enrollInCourseWithPurchase = (course)=>{
        setIsEnrolled(true);
        const updatedCourse = {
            ...course,
            status: 'in-progress',
            totalProgress: 0
        };
        setCourseData(updatedCourse);
        persistEnrollmentData(updatedCourse);
    };
    const updateCourseProgress = (updatedCourse)=>{
        // Update the primary courseData if it's the same course
        if (courseData.id === updatedCourse.id) {
            setCourseData(updatedCourse);
        }
        // Update the course in enrolledCourses array
        setEnrolledCourses((prev)=>prev.map((course)=>course.id === updatedCourse.id ? updatedCourse : course));
        // Update persisted data with new progress
        if (isEnrolled) {
            persistEnrollmentData(updatedCourse);
        }
    };
    // Check if user is enrolled in a specific course
    const isEnrolledInCourse = (courseId)=>{
        return enrolledCourses.some((course)=>course.id === courseId);
    };
    // Get a specific course by ID
    const getCourseById = (courseId)=>{
        return enrolledCourses.find((course)=>course.id === courseId);
    };
    const value = {
        isEnrolled,
        courseData,
        enrollInCourse,
        enrollInCourseWithPurchase,
        updateCourseProgress,
        enrolledCourses,
        isEnrolledInCourse,
        getCourseById
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(EnrollmentContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/enrollment-context.tsx",
        lineNumber: 220,
        columnNumber: 5
    }, this);
};
}}),

};

//# sourceMappingURL=src_043ba3ad._.js.map