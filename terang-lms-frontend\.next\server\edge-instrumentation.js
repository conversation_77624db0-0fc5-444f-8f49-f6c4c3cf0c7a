try{let t="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},e=(new t.Error).stack;e&&(t._sentryDebugIds=t._sentryDebugIds||{},t._sentryDebugIds[e]="6516027b-12c0-4ca1-958d-cafdf48f46d1",t._sentryDebugIdIdentifier="sentry-dbid-6516027b-12c0-4ca1-958d-cafdf48f46d1")}catch(t){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[183],{13:(t,e,n)=>{"use strict";let r,i,o,s,a;n.r(e),n.d(e,{onRequestError:()=>sF,register:()=>s$});let u=globalThis,c="9.44.0";function l(){return p(u),u}function p(t){let e=t.__SENTRY__=t.__SENTRY__||{};return e.version=e.version||c,e[c]=e[c]||{}}function f(t,e,n=u){let r=n.__SENTRY__=n.__SENTRY__||{},i=r[c]=r[c]||{};return i[t]||(i[t]=e())}let d=["debug","info","warn","error","log","assert","trace"],h={};function _(t){if(!("console"in u))return t();let e=u.console,n={},r=Object.keys(h);r.forEach(t=>{let r=h[t];n[t]=e[t],e[t]=r});try{return t()}finally{r.forEach(t=>{e[t]=n[t]})}}function g(t,...e){}let m={log:function(...t){[...t]},warn:function(...t){[...t]},error:function(...t){[...t]}},y=/\(error: (.*)\)/,v=/captureMessage|captureException/;function E(...t){let e=t.sort((t,e)=>t[0]-e[0]).map(t=>t[1]);return(t,n=0,r=0)=>{let i=[],o=t.split("\n");for(let t=n;t<o.length;t++){let n=o[t];if(n.length>1024)continue;let s=y.test(n)?n.replace(y,"$1"):n;if(!s.match(/\S*Error: /)){for(let t of e){let e=t(s);if(e){i.push(e);break}}if(i.length>=50+r)break}}var s=i.slice(r);if(!s.length)return[];let a=Array.from(s);return/sentryWrapped/.test(S(a).function||"")&&a.pop(),a.reverse(),v.test(S(a).function||"")&&(a.pop(),v.test(S(a).function||"")&&a.pop()),a.slice(0,50).map(t=>({...t,filename:t.filename||S(a).filename,function:t.function||"?"}))}}function S(t){return t[t.length-1]||{}}let T="<anonymous>";function b(t){let e=t.exception;if(e){let t=[];try{return e.values.forEach(e=>{e.stacktrace.frames&&t.push(...e.stacktrace.frames)}),t}catch{}}}let O={},R={};function A(t,e){O[t]=O[t]||[],O[t].push(e)}function P(t,e){if(!R[t]){R[t]=!0;try{e()}catch(t){}}}function L(t,e){let n=t&&O[t];if(n)for(let t of n)try{t(e)}catch(t){}}let I=null;function x(){I=u.onerror,u.onerror=function(t,e,n,r,i){return L("error",{column:r,error:i,line:n,msg:t,url:e}),!!I&&I.apply(this,arguments)},u.onerror.__SENTRY_INSTRUMENTED__=!0}let C=null;function w(){C=u.onunhandledrejection,u.onunhandledrejection=function(t){return L("unhandledrejection",t),!C||C.apply(this,arguments)},u.onunhandledrejection.__SENTRY_INSTRUMENTED__=!0}let N=Object.prototype.toString;function k(t){switch(N.call(t)){case"[object Error]":case"[object Exception]":case"[object DOMException]":case"[object WebAssembly.Exception]":return!0;default:return F(t,Error)}}function M(t,e){return N.call(t)===`[object ${e}]`}function D(t){return M(t,"String")}function j(t){return"object"==typeof t&&null!==t&&"__sentry_template_string__"in t&&"__sentry_template_values__"in t}function U(t){return null===t||j(t)||"object"!=typeof t&&"function"!=typeof t}function B(t){return M(t,"Object")}function $(t){return!!(t?.then&&"function"==typeof t.then)}function F(t,e){try{return t instanceof e}catch{return!1}}function G(t){return!!("object"==typeof t&&null!==t&&(t.__isVue||t._isVue))}function V(t){return"undefined"!=typeof Request&&F(t,Request)}function X(t,e=0){return"string"!=typeof t||0===e||t.length<=e?t:`${t.slice(0,e)}...`}function z(t,e=[],n=!1){return e.some(e=>(function(t,e,n=!1){return!!D(t)&&(M(e,"RegExp")?e.test(t):!!D(e)&&(n?t===e:t.includes(e)))})(t,e,n))}function H(t,e,n){if(!(e in t))return;let r=t[e];if("function"!=typeof r)return;let i=n(r);"function"==typeof i&&function(t,e){try{let n=e.prototype||{};t.prototype=e.prototype=n,W(t,"__sentry_original__",e)}catch{}}(i,r);try{t[e]=i}catch{}}function W(t,e,n){try{Object.defineProperty(t,e,{value:n,writable:!0,configurable:!0})}catch{}}function Y(t){if(k(t))return{message:t.message,name:t.name,stack:t.stack,...q(t)};if(!("undefined"!=typeof Event&&F(t,Event)))return t;{let e={type:t.type,target:K(t.target),currentTarget:K(t.currentTarget),...q(t)};return"undefined"!=typeof CustomEvent&&F(t,CustomEvent)&&(e.detail=t.detail),e}}function K(t){try{return"undefined"!=typeof Element&&F(t,Element)?function(t,e={}){if(!t)return"<unknown>";try{let n,r=t,i=[],o=0,s=0,a=Array.isArray(e)?e:e.keyAttrs,c=!Array.isArray(e)&&e.maxStringLength||80;for(;r&&o++<5&&(n=function(t,e){let n=[];if(!t?.tagName)return"";if(u.HTMLElement&&t instanceof HTMLElement&&t.dataset){if(t.dataset.sentryComponent)return t.dataset.sentryComponent;if(t.dataset.sentryElement)return t.dataset.sentryElement}n.push(t.tagName.toLowerCase());let r=e?.length?e.filter(e=>t.getAttribute(e)).map(e=>[e,t.getAttribute(e)]):null;if(r?.length)r.forEach(t=>{n.push(`[${t[0]}="${t[1]}"]`)});else{t.id&&n.push(`#${t.id}`);let e=t.className;if(e&&D(e))for(let t of e.split(/\s+/))n.push(`.${t}`)}for(let e of["aria-label","type","name","title","alt"]){let r=t.getAttribute(e);r&&n.push(`[${e}="${r}"]`)}return n.join("")}(r,a),"html"!==n&&(!(o>1)||!(s+3*i.length+n.length>=c)));)i.push(n),s+=n.length,r=r.parentNode;return i.reverse().join(" > ")}catch{return"<unknown>"}}(t):Object.prototype.toString.call(t)}catch{return"<unknown>"}}function q(t){if("object"!=typeof t||null===t)return{};{let e={};for(let n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e}}function Z(t=u.crypto||u.msCrypto){let e=()=>16*Math.random();try{if(t?.randomUUID)return t.randomUUID().replace(/-/g,"");t?.getRandomValues&&(e=()=>{let e=new Uint8Array(1);return t.getRandomValues(e),e[0]})}catch{}return"10000000100040008000100000000000".replace(/[018]/g,t=>(t^(15&e())>>t/4).toString(16))}function J(t){return t.exception?.values?.[0]}function Q(t,e){let n=J(t);if(!n)return;let r=n.mechanism;if(n.mechanism={type:"generic",handled:!0,...r,...e},e&&"data"in e){let t={...r?.data,...e.data};n.mechanism.data=t}}function tt(t){if(function(t){try{return t.__sentry_captured__}catch{}}(t))return!0;try{W(t,"__sentry_captured__",!0)}catch{}return!1}function te(){return Date.now()/1e3}function tn(){return(r??(r=function(){let{performance:t}=u;if(!t?.now||!t.timeOrigin)return te;let e=t.timeOrigin;return()=>(e+t.now())/1e3}()))()}function tr(t,e={}){if(e.user&&(!t.ipAddress&&e.user.ip_address&&(t.ipAddress=e.user.ip_address),t.did||e.did||(t.did=e.user.id||e.user.email||e.user.username)),t.timestamp=e.timestamp||tn(),e.abnormal_mechanism&&(t.abnormal_mechanism=e.abnormal_mechanism),e.ignoreDuration&&(t.ignoreDuration=e.ignoreDuration),e.sid&&(t.sid=32===e.sid.length?e.sid:Z()),void 0!==e.init&&(t.init=e.init),!t.did&&e.did&&(t.did=`${e.did}`),"number"==typeof e.started&&(t.started=e.started),t.ignoreDuration)t.duration=void 0;else if("number"==typeof e.duration)t.duration=e.duration;else{let e=t.timestamp-t.started;t.duration=e>=0?e:0}e.release&&(t.release=e.release),e.environment&&(t.environment=e.environment),!t.ipAddress&&e.ipAddress&&(t.ipAddress=e.ipAddress),!t.userAgent&&e.userAgent&&(t.userAgent=e.userAgent),"number"==typeof e.errors&&(t.errors=e.errors),e.status&&(t.status=e.status)}function ti(t,e,n=2){if(!e||"object"!=typeof e||n<=0)return e;if(t&&0===Object.keys(e).length)return t;let r={...t};for(let t in e)Object.prototype.hasOwnProperty.call(e,t)&&(r[t]=ti(r[t],e[t],n-1));return r}function to(){return Z().substring(16)}let ts="_sentrySpan";function ta(t,e){e?W(t,ts,e):delete t[ts]}class tu{constructor(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._attachments=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={},this._propagationContext={traceId:Z(),sampleRand:Math.random()}}clone(){let t=new tu;return t._breadcrumbs=[...this._breadcrumbs],t._tags={...this._tags},t._extra={...this._extra},t._contexts={...this._contexts},this._contexts.flags&&(t._contexts.flags={values:[...this._contexts.flags.values]}),t._user=this._user,t._level=this._level,t._session=this._session,t._transactionName=this._transactionName,t._fingerprint=this._fingerprint,t._eventProcessors=[...this._eventProcessors],t._attachments=[...this._attachments],t._sdkProcessingMetadata={...this._sdkProcessingMetadata},t._propagationContext={...this._propagationContext},t._client=this._client,t._lastEventId=this._lastEventId,ta(t,this[ts]),t}setClient(t){this._client=t}setLastEventId(t){this._lastEventId=t}getClient(){return this._client}lastEventId(){return this._lastEventId}addScopeListener(t){this._scopeListeners.push(t)}addEventProcessor(t){return this._eventProcessors.push(t),this}setUser(t){return this._user=t||{email:void 0,id:void 0,ip_address:void 0,username:void 0},this._session&&tr(this._session,{user:t}),this._notifyScopeListeners(),this}getUser(){return this._user}setTags(t){return this._tags={...this._tags,...t},this._notifyScopeListeners(),this}setTag(t,e){return this._tags={...this._tags,[t]:e},this._notifyScopeListeners(),this}setExtras(t){return this._extra={...this._extra,...t},this._notifyScopeListeners(),this}setExtra(t,e){return this._extra={...this._extra,[t]:e},this._notifyScopeListeners(),this}setFingerprint(t){return this._fingerprint=t,this._notifyScopeListeners(),this}setLevel(t){return this._level=t,this._notifyScopeListeners(),this}setTransactionName(t){return this._transactionName=t,this._notifyScopeListeners(),this}setContext(t,e){return null===e?delete this._contexts[t]:this._contexts[t]=e,this._notifyScopeListeners(),this}setSession(t){return t?this._session=t:delete this._session,this._notifyScopeListeners(),this}getSession(){return this._session}update(t){if(!t)return this;let e="function"==typeof t?t(this):t,{tags:n,extra:r,user:i,contexts:o,level:s,fingerprint:a=[],propagationContext:u}=(e instanceof tu?e.getScopeData():B(e)?t:void 0)||{};return this._tags={...this._tags,...n},this._extra={...this._extra,...r},this._contexts={...this._contexts,...o},i&&Object.keys(i).length&&(this._user=i),s&&(this._level=s),a.length&&(this._fingerprint=a),u&&(this._propagationContext=u),this}clear(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._session=void 0,ta(this,void 0),this._attachments=[],this.setPropagationContext({traceId:Z(),sampleRand:Math.random()}),this._notifyScopeListeners(),this}addBreadcrumb(t,e){let n="number"==typeof e?e:100;if(n<=0)return this;let r={timestamp:te(),...t,message:t.message?X(t.message,2048):t.message};return this._breadcrumbs.push(r),this._breadcrumbs.length>n&&(this._breadcrumbs=this._breadcrumbs.slice(-n),this._client?.recordDroppedEvent("buffer_overflow","log_item")),this._notifyScopeListeners(),this}getLastBreadcrumb(){return this._breadcrumbs[this._breadcrumbs.length-1]}clearBreadcrumbs(){return this._breadcrumbs=[],this._notifyScopeListeners(),this}addAttachment(t){return this._attachments.push(t),this}clearAttachments(){return this._attachments=[],this}getScopeData(){return{breadcrumbs:this._breadcrumbs,attachments:this._attachments,contexts:this._contexts,tags:this._tags,extra:this._extra,user:this._user,level:this._level,fingerprint:this._fingerprint||[],eventProcessors:this._eventProcessors,propagationContext:this._propagationContext,sdkProcessingMetadata:this._sdkProcessingMetadata,transactionName:this._transactionName,span:this[ts]}}setSDKProcessingMetadata(t){return this._sdkProcessingMetadata=ti(this._sdkProcessingMetadata,t,2),this}setPropagationContext(t){return this._propagationContext=t,this}getPropagationContext(){return this._propagationContext}captureException(t,e){let n=e?.event_id||Z();if(!this._client)return n;let r=Error("Sentry syntheticException");return this._client.captureException(t,{originalException:t,syntheticException:r,...e,event_id:n},this),n}captureMessage(t,e,n){let r=n?.event_id||Z();if(!this._client)return r;let i=Error(t);return this._client.captureMessage(t,e,{originalException:t,syntheticException:i,...n,event_id:r},this),r}captureEvent(t,e){let n=e?.event_id||Z();return this._client&&this._client.captureEvent(t,{...e,event_id:n},this),n}_notifyScopeListeners(){this._notifyingListeners||(this._notifyingListeners=!0,this._scopeListeners.forEach(t=>{t(this)}),this._notifyingListeners=!1)}}function tc(){return f("defaultCurrentScope",()=>new tu)}function tl(){return f("defaultIsolationScope",()=>new tu)}class tp{constructor(t,e){let n,r;n=t||new tu,r=e||new tu,this._stack=[{scope:n}],this._isolationScope=r}withScope(t){let e,n=this._pushScope();try{e=t(n)}catch(t){throw this._popScope(),t}return $(e)?e.then(t=>(this._popScope(),t),t=>{throw this._popScope(),t}):(this._popScope(),e)}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getIsolationScope(){return this._isolationScope}getStackTop(){return this._stack[this._stack.length-1]}_pushScope(){let t=this.getScope().clone();return this._stack.push({client:this.getClient(),scope:t}),t}_popScope(){return!(this._stack.length<=1)&&!!this._stack.pop()}}function tf(){let t=p(l());return t.stack=t.stack||new tp(tc(),tl())}function td(t){return tf().withScope(t)}function th(t,e){let n=tf();return n.withScope(()=>(n.getStackTop().scope=t,e(t)))}function t_(t){return tf().withScope(()=>t(tf().getIsolationScope()))}function tg(t){let e=p(t);return e.acs?e.acs:{withIsolationScope:t_,withScope:td,withSetScope:th,withSetIsolationScope:(t,e)=>t_(e),getCurrentScope:()=>tf().getScope(),getIsolationScope:()=>tf().getIsolationScope()}}function tm(){return tg(l()).getCurrentScope()}function ty(){return tg(l()).getIsolationScope()}function tv(){return f("globalScope",()=>new tu)}function tE(...t){let e=tg(l());if(2===t.length){let[n,r]=t;return n?e.withSetScope(n,r):e.withScope(r)}return e.withScope(t[0])}function tS(){return tm().getClient()}function tT(t){let{traceId:e,parentSpanId:n,propagationSpanId:r}=t.getPropagationContext(),i={trace_id:e,span_id:r||to()};return n&&(i.parent_span_id=n),i}let tb="sentry.source",tO="sentry.sample_rate",tR="sentry.op",tA="sentry.origin",tP="sentry.custom_span_name",tL="sentry.profile_id",tI="sentry.exclusive_time";function tx(t){if(t<400&&t>=100)return{code:1};if(t>=400&&t<500)switch(t){case 401:return{code:2,message:"unauthenticated"};case 403:return{code:2,message:"permission_denied"};case 404:return{code:2,message:"not_found"};case 409:return{code:2,message:"already_exists"};case 413:return{code:2,message:"failed_precondition"};case 429:return{code:2,message:"resource_exhausted"};case 499:return{code:2,message:"cancelled"};default:return{code:2,message:"invalid_argument"}}if(t>=500&&t<600)switch(t){case 501:return{code:2,message:"unimplemented"};case 503:return{code:2,message:"unavailable"};case 504:return{code:2,message:"deadline_exceeded"};default:return{code:2,message:"internal_error"}}return{code:2,message:"unknown_error"}}let tC="_sentryScope",tw="_sentryIsolationScope";function tN(t,e,n){t&&(W(t,tw,n),W(t,tC,e))}function tk(t){return{scope:t[tC],isolationScope:t[tw]}}let tM="sentry-",tD=/^sentry-/;function tj(t){let e=tB(t);if(!e)return;let n=Object.entries(e).reduce((t,[e,n])=>(e.match(tD)&&(t[e.slice(tM.length)]=n),t),{});return Object.keys(n).length>0?n:void 0}function tU(t){if(t){var e=Object.entries(t).reduce((t,[e,n])=>(n&&(t[`${tM}${e}`]=n),t),{});return 0!==Object.keys(e).length?Object.entries(e).reduce((t,[e,n],r)=>{let i=`${encodeURIComponent(e)}=${encodeURIComponent(n)}`,o=0===r?i:`${t},${i}`;return o.length>8192?t:o},""):void 0}}function tB(t){if(t&&(D(t)||Array.isArray(t)))return Array.isArray(t)?t.reduce((t,e)=>(Object.entries(t$(e)).forEach(([e,n])=>{t[e]=n}),t),{}):t$(t)}function t$(t){return t.split(",").map(t=>t.split("=").map(t=>{try{return decodeURIComponent(t.trim())}catch{return}})).reduce((t,[e,n])=>(e&&n&&(t[e]=n),t),{})}function tF(t){if("boolean"==typeof t)return Number(t);let e="string"==typeof t?parseFloat(t):t;if(!("number"!=typeof e||isNaN(e))&&!(e<0)&&!(e>1))return e}let tG=RegExp("^[ \\t]*([0-9a-f]{32})?-?([0-9a-f]{16})?-?([01])?[ \\t]*$");function tV(t=Z(),e=to(),n){let r="";return void 0!==n&&(r=n?"-1":"-0"),`${t}-${e}${r}`}let tX=!1;function tz(t){let{spanId:e,traceId:n,isRemote:r}=t.spanContext(),i=r?e:tK(t).parent_span_id,o=tk(t).scope;return{parent_span_id:i,span_id:r?o?.getPropagationContext().propagationSpanId||to():e,trace_id:n}}function tH(t){return t&&t.length>0?t.map(({context:{spanId:t,traceId:e,traceFlags:n,...r},attributes:i})=>({span_id:t,trace_id:e,sampled:1===n,attributes:i,...r})):void 0}function tW(t){return"number"==typeof t?tY(t):Array.isArray(t)?t[0]+t[1]/1e9:t instanceof Date?tY(t.getTime()):tn()}function tY(t){return t>0x2540be3ff?t/1e3:t}function tK(t){var e;if("function"==typeof t.getSpanJSON)return t.getSpanJSON();let{spanId:n,traceId:r}=t.spanContext();if((e=t).attributes&&e.startTime&&e.name&&e.endTime&&e.status){let{attributes:e,startTime:i,name:o,endTime:s,status:a,links:u}=t;return{span_id:n,trace_id:r,data:e,description:o,parent_span_id:"parentSpanId"in t?t.parentSpanId:"parentSpanContext"in t?t.parentSpanContext?.spanId:void 0,start_timestamp:tW(i),timestamp:tW(s)||void 0,status:tZ(a),op:e[tR],origin:e[tA],links:tH(u)}}return{span_id:n,trace_id:r,start_timestamp:0,data:{}}}function tq(t){let{traceFlags:e}=t.spanContext();return 1===e}function tZ(t){if(t&&0!==t.code)return 1===t.code?"ok":t.message||"unknown_error"}let tJ="_sentryChildSpans",tQ="_sentryRootSpan";function t0(t,e){let n=t[tQ]||t;W(e,tQ,n),t[tJ]?t[tJ].add(e):W(t,tJ,new Set([e]))}function t1(t){return t[tQ]||t}function t2(){let t=tg(l());return t.getActiveSpan?t.getActiveSpan():tm()[ts]}function t4(){tX||(_(()=>{console.warn("[Sentry] Returning null from `beforeSendSpan` is disallowed. To drop certain spans, configure the respective integrations directly.")}),tX=!0)}let t5=!1;function t3(){if(!t5){t.tag="sentry_tracingErrorCallback",t5=!0;let e="error";A(e,t),P(e,x);let n="unhandledrejection";A(n,t),P(n,w)}function t(){let t=t2(),e=t&&t1(t);e&&e.setStatus({code:2,message:"internal_error"})}}function t9(t,e,n=[e],r="npm"){let i=t._metadata||{};i.sdk||(i.sdk={name:`sentry.javascript.${e}`,packages:n.map(t=>({name:`${r}:@sentry/${t}`,version:c})),version:c}),t._metadata=i}function t8(t){return"isRelative"in t}function t6(t){if(!t)return{};let e=t.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!e)return{};let n=e[6]||"",r=e[8]||"";return{host:e[4],path:e[5],protocol:e[2],search:n,hash:r,relative:e[5]+n+r}}function t7(t){return t.split(/[?#]/,1)[0]}function et(t){let{protocol:e,host:n,path:r}=t,i=n?.replace(/^.*@/,"[filtered]:[filtered]@").replace(/(:80)$/,"").replace(/(:443)$/,"")||"";return`${e?`${e}://`:""}${i}${r}`}function ee(t){let e=u[Symbol.for("@vercel/request-context")],n=e?.get?.();n?.waitUntil&&n.waitUntil(t)}let en=/^o(\d+)\./,er=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;function ei(t,e=!1){let{host:n,path:r,pass:i,port:o,projectId:s,protocol:a,publicKey:u}=t;return`${a}://${u}${e&&i?`:${i}`:""}@${n}${o?`:${o}`:""}/${r?`${r}/`:r}${s}`}function eo(t){return{protocol:t.protocol,publicKey:t.publicKey||"",pass:t.pass||"",host:t.host,port:t.port||"",path:t.path||"",projectId:t.projectId}}function es(t,e=100,n=Infinity){try{return function t(e,n,r=Infinity,i=Infinity,o=function(){let t=new WeakSet;return[function(e){return!!t.has(e)||(t.add(e),!1)},function(e){t.delete(e)}]}()){let[s,a]=o;if(null==n||["boolean","string"].includes(typeof n)||"number"==typeof n&&Number.isFinite(n))return n;let u=function(t,e){try{if("domain"===t&&e&&"object"==typeof e&&e._events)return"[Domain]";if("domainEmitter"===t)return"[DomainEmitter]";if("undefined"!=typeof global&&e===global)return"[Global]";if("undefined"!=typeof window&&e===window)return"[Window]";if("undefined"!=typeof document&&e===document)return"[Document]";if(G(e))return"[VueViewModel]";if(B(e)&&"nativeEvent"in e&&"preventDefault"in e&&"stopPropagation"in e)return"[SyntheticEvent]";if("number"==typeof e&&!Number.isFinite(e))return`[${e}]`;if("function"==typeof e)return`[Function: ${function(t){try{if(!t||"function"!=typeof t)return T;return t.name||T}catch{return T}}(e)}]`;if("symbol"==typeof e)return`[${String(e)}]`;if("bigint"==typeof e)return`[BigInt: ${String(e)}]`;let n=function(t){let e=Object.getPrototypeOf(t);return e?.constructor?e.constructor.name:"null prototype"}(e);if(/^HTML(\w*)Element$/.test(n))return`[HTMLElement: ${n}]`;return`[object ${n}]`}catch(t){return`**non-serializable** (${t})`}}(e,n);if(!u.startsWith("[object "))return u;if(n.__sentry_skip_normalization__)return n;let c="number"==typeof n.__sentry_override_normalization_depth__?n.__sentry_override_normalization_depth__:r;if(0===c)return u.replace("object ","");if(s(n))return"[Circular ~]";if(n&&"function"==typeof n.toJSON)try{let e=n.toJSON();return t("",e,c-1,i,o)}catch{}let l=Array.isArray(n)?[]:{},p=0,f=Y(n);for(let e in f){if(!Object.prototype.hasOwnProperty.call(f,e))continue;if(p>=i){l[e]="[MaxProperties ~]";break}let n=f[e];l[e]=t(e,n,c-1,i,o),p++}return a(n),l}("",t,e,n)}catch(t){return{ERROR:`**non-serializable** (${t})`}}}function ea(t,e=[]){return[t,e]}function eu(t,e){for(let n of t[1]){let t=n[0].type;if(e(n,t))return!0}return!1}function ec(t){let e=p(u);return e.encodePolyfill?e.encodePolyfill(t):new TextEncoder().encode(t)}let el={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default",profile:"profile",profile_chunk:"profile",replay_event:"replay",replay_recording:"replay",check_in:"monitor",feedback:"feedback",span:"span",raw_security:"security",log:"log_item"};function ep(t){if(!t?.sdk)return;let{name:e,version:n}=t.sdk;return{name:e,version:n}}let ef="production";function ed(t){if("boolean"==typeof __SENTRY_TRACING__&&!__SENTRY_TRACING__)return!1;let e=t||tS()?.getOptions();return!!e&&(null!=e.tracesSampleRate||!!e.tracesSampler)}let eh="_frozenDsc";function e_(t,e){let n,r=e.getOptions(),{publicKey:i,host:o}=e.getDsn()||{};r.orgId?n=String(r.orgId):o&&(n=function(t){let e=t.match(en);return e?.[1]}(o));let s={environment:r.environment||ef,release:r.release,public_key:i,trace_id:t,org_id:n};return e.emit("createDsc",s),s}function eg(t,e){let n=e.getPropagationContext();return n.dsc||e_(n.traceId,t)}function em(t){let e=tS();if(!e)return{};let n=t1(t),r=tK(n),i=r.data,o=n.spanContext().traceState,s=o?.get("sentry.sample_rate")??i[tO]??i["sentry.previous_trace_sample_rate"];function a(t){return("number"==typeof s||"string"==typeof s)&&(t.sample_rate=`${s}`),t}let u=n[eh];if(u)return a(u);let c=o?.get("sentry.dsc"),l=c&&tj(c);if(l)return a(l);let p=e_(t.spanContext().traceId,e),f=i[tb],d=r.description;return"url"!==f&&d&&(p.transaction=d),ed()&&(p.sampled=String(tq(n)),p.sample_rand=o?.get("sentry.sample_rand")??tk(n).scope?.getPropagationContext().sampleRand.toString()),a(p),e.emit("createDsc",p,n),p}let ey=[];function ev(t,e){for(let n of e)n?.afterAllSetup&&n.afterAllSetup(t)}function eE(t,e,n){if(!n[e.name]){if(n[e.name]=e,-1===ey.indexOf(e.name)&&"function"==typeof e.setupOnce&&(e.setupOnce(),ey.push(e.name)),e.setup&&"function"==typeof e.setup&&e.setup(t),"function"==typeof e.preprocessEvent){let n=e.preprocessEvent.bind(e);t.on("preprocessEvent",(e,r)=>n(e,r,t))}if("function"==typeof e.processEvent){let n=e.processEvent.bind(e),r=Object.assign((e,r)=>n(e,r,t),{id:e.name});t.addEventProcessor(r)}}}function eS(t){return new eb(e=>{e(t)})}function eT(t){return new eb((e,n)=>{n(t)})}class eb{constructor(t){this._state=0,this._handlers=[],this._runExecutor(t)}then(t,e){return new eb((n,r)=>{this._handlers.push([!1,e=>{if(t)try{n(t(e))}catch(t){r(t)}else n(e)},t=>{if(e)try{n(e(t))}catch(t){r(t)}else r(t)}]),this._executeHandlers()})}catch(t){return this.then(t=>t,t)}finally(t){return new eb((e,n)=>{let r,i;return this.then(e=>{i=!1,r=e,t&&t()},e=>{i=!0,r=e,t&&t()}).then(()=>{if(i)return void n(r);e(r)})})}_executeHandlers(){if(0===this._state)return;let t=this._handlers.slice();this._handlers=[],t.forEach(t=>{t[0]||(1===this._state&&t[1](this._value),2===this._state&&t[2](this._value),t[0]=!0)})}_runExecutor(t){let e=(t,e)=>{if(0===this._state){if($(e))return void e.then(n,r);this._state=t,this._value=e,this._executeHandlers()}},n=t=>{e(1,t)},r=t=>{e(2,t)};try{t(n,r)}catch(t){r(t)}}}function eO(t,e){let{extra:n,tags:r,user:i,contexts:o,level:s,sdkProcessingMetadata:a,breadcrumbs:u,fingerprint:c,eventProcessors:l,attachments:p,propagationContext:f,transactionName:d,span:h}=e;eR(t,"extra",n),eR(t,"tags",r),eR(t,"user",i),eR(t,"contexts",o),t.sdkProcessingMetadata=ti(t.sdkProcessingMetadata,a,2),s&&(t.level=s),d&&(t.transactionName=d),h&&(t.span=h),u.length&&(t.breadcrumbs=[...t.breadcrumbs,...u]),c.length&&(t.fingerprint=[...t.fingerprint,...c]),l.length&&(t.eventProcessors=[...t.eventProcessors,...l]),p.length&&(t.attachments=[...t.attachments,...p]),t.propagationContext={...t.propagationContext,...f}}function eR(t,e,n){t[e]=ti(t[e],n,1)}let eA=["user","level","extra","contexts","tags","fingerprint","propagationContext"],eP=Symbol.for("SentryInternalError"),eL=Symbol.for("SentryDoNotSendEventError");function eI(t){return{message:t,[eP]:!0}}function ex(t){return{message:t,[eL]:!0}}class eC{constructor(t){if(this._options=t,this._integrations={},this._numProcessing=0,this._outcomes={},this._hooks={},this._eventProcessors=[],t.dsn&&(this._dsn=function(t){var e;let n="string"==typeof t?function(t){let e=er.exec(t);if(!e)return void _(()=>{console.error(`Invalid Sentry Dsn: ${t}`)});let[n,r,i="",o="",s="",a=""]=e.slice(1),u="",c=a,l=c.split("/");if(l.length>1&&(u=l.slice(0,-1).join("/"),c=l.pop()),c){let t=c.match(/^\d+/);t&&(c=t[0])}return eo({host:o,pass:i,path:u,projectId:c,port:s,protocol:n,publicKey:r})}(t):eo(t);if(n&&(e=0,1))return n}(t.dsn)),this._dsn){let e=function(t,e,n){return e||`${function(t){let e=t.protocol?`${t.protocol}:`:"",n=t.port?`:${t.port}`:"";return`${e}//${t.host}${n}${t.path?`/${t.path}`:""}/api/`}(t)}${t.projectId}/envelope/?${function(t,e){let n={sentry_version:"7"};return t.publicKey&&(n.sentry_key=t.publicKey),e&&(n.sentry_client=`${e.name}/${e.version}`),new URLSearchParams(n).toString()}(t,n)}`}(this._dsn,t.tunnel,t._metadata?t._metadata.sdk:void 0);this._transport=t.transport({tunnel:this._options.tunnel,recordDroppedEvent:this.recordDroppedEvent.bind(this),...t.transportOptions,url:e})}}captureException(t,e,n){let r=Z();if(tt(t))return r;let i={event_id:r,...e};return this._process(this.eventFromException(t,i).then(t=>this._captureEvent(t,i,n))),i.event_id}captureMessage(t,e,n,r){let i={event_id:Z(),...n},o=j(t)?t:String(t),s=U(t)?this.eventFromMessage(o,e,i):this.eventFromException(t,i);return this._process(s.then(t=>this._captureEvent(t,i,r))),i.event_id}captureEvent(t,e,n){let r=Z();if(e?.originalException&&tt(e.originalException))return r;let i={event_id:r,...e},o=t.sdkProcessingMetadata||{},s=o.capturedSpanScope,a=o.capturedSpanIsolationScope;return this._process(this._captureEvent(t,i,s||n,a)),i.event_id}captureSession(t){this.sendSession(t),tr(t,{init:!1})}getDsn(){return this._dsn}getOptions(){return this._options}getSdkMetadata(){return this._options._metadata}getTransport(){return this._transport}flush(t){let e=this._transport;return e?(this.emit("flush"),this._isClientDoneProcessing(t).then(n=>e.flush(t).then(t=>n&&t))):eS(!0)}close(t){return this.flush(t).then(t=>(this.getOptions().enabled=!1,this.emit("close"),t))}getEventProcessors(){return this._eventProcessors}addEventProcessor(t){this._eventProcessors.push(t)}init(){(this._isEnabled()||this._options.integrations.some(({name:t})=>t.startsWith("Spotlight")))&&this._setupIntegrations()}getIntegrationByName(t){return this._integrations[t]}addIntegration(t){let e=this._integrations[t.name];eE(this,t,this._integrations),e||ev(this,[t])}sendEvent(t,e={}){this.emit("beforeSendEvent",t,e);let n=function(t,e,n,r){var i;let o=ep(n),s=t.type&&"replay_event"!==t.type?t.type:"event";(i=n?.sdk)&&(t.sdk=t.sdk||{},t.sdk.name=t.sdk.name||i.name,t.sdk.version=t.sdk.version||i.version,t.sdk.integrations=[...t.sdk.integrations||[],...i.integrations||[]],t.sdk.packages=[...t.sdk.packages||[],...i.packages||[]]);let a=function(t,e,n,r){let i=t.sdkProcessingMetadata?.dynamicSamplingContext;return{event_id:t.event_id,sent_at:new Date().toISOString(),...e&&{sdk:e},...!!n&&r&&{dsn:ei(r)},...i&&{trace:i}}}(t,o,r,e);return delete t.sdkProcessingMetadata,ea(a,[[{type:s},t]])}(t,this._dsn,this._options._metadata,this._options.tunnel);for(let t of e.attachments||[])n=function(t,e){let[n,r]=t;return[n,[...r,e]]}(n,function(t){let e="string"==typeof t.data?ec(t.data):t.data;return[{type:"attachment",length:e.length,filename:t.filename,content_type:t.contentType,attachment_type:t.attachmentType},e]}(t));let r=this.sendEnvelope(n);r&&r.then(e=>this.emit("afterSendEvent",t,e),null)}sendSession(t){let{release:e,environment:n=ef}=this._options;if("aggregates"in t){let r=t.attrs||{};if(!r.release&&!e)return;r.release=r.release||e,r.environment=r.environment||n,t.attrs=r}else{if(!t.release&&!e)return;t.release=t.release||e,t.environment=t.environment||n}this.emit("beforeSendSession",t);let r=function(t,e,n,r){let i=ep(n);return ea({sent_at:new Date().toISOString(),...i&&{sdk:i},...!!r&&e&&{dsn:ei(e)}},["aggregates"in t?[{type:"sessions"},t]:[{type:"session"},t.toJSON()]])}(t,this._dsn,this._options._metadata,this._options.tunnel);this.sendEnvelope(r)}recordDroppedEvent(t,e,n=1){if(this._options.sendClientReports){let r=`${t}:${e}`;this._outcomes[r]=(this._outcomes[r]||0)+n}}on(t,e){let n=this._hooks[t]=this._hooks[t]||[];return n.push(e),()=>{let t=n.indexOf(e);t>-1&&n.splice(t,1)}}emit(t,...e){let n=this._hooks[t];n&&n.forEach(t=>t(...e))}sendEnvelope(t){return(this.emit("beforeEnvelope",t),this._isEnabled()&&this._transport)?this._transport.send(t).then(null,t=>t):eS({})}_setupIntegrations(){let{integrations:t}=this._options;this._integrations=function(t,e){let n={};return e.forEach(e=>{e&&eE(t,e,n)}),n}(this,t),ev(this,t)}_updateSessionFromEvent(t,e){let n="fatal"===e.level,r=!1,i=e.exception?.values;if(i)for(let t of(r=!0,i)){let e=t.mechanism;if(e?.handled===!1){n=!0;break}}let o="ok"===t.status;(o&&0===t.errors||o&&n)&&(tr(t,{...n&&{status:"crashed"},errors:t.errors||Number(r||n)}),this.captureSession(t))}_isClientDoneProcessing(t){return new eb(e=>{let n=0,r=setInterval(()=>{0==this._numProcessing?(clearInterval(r),e(!0)):(n+=1,t&&n>=t&&(clearInterval(r),e(!1)))},1)})}_isEnabled(){return!1!==this.getOptions().enabled&&void 0!==this._transport}_prepareEvent(t,e,n,r){let a=this.getOptions(),c=Object.keys(this._integrations);return!e.integrations&&c?.length&&(e.integrations=c),this.emit("preprocessEvent",t,e),t.type||r.setLastEventId(t.event_id||e.event_id),(function(t,e,n,r,a,c){var l,p,f,d,h,_;let{normalizeDepth:g=3,normalizeMaxBreadth:m=1e3}=t,y={...e,event_id:e.event_id||n.event_id||Z(),timestamp:e.timestamp||te()},v=n.integrations||t.integrations.map(t=>t.name);(function(t,e){let{environment:n,release:r,dist:i,maxValueLength:o=250}=e;t.environment=t.environment||n||ef,!t.release&&r&&(t.release=r),!t.dist&&i&&(t.dist=i);let s=t.request;s?.url&&(s.url=X(s.url,o))})(y,t),l=y,(p=v).length>0&&(l.sdk=l.sdk||{},l.sdk.integrations=[...l.sdk.integrations||[],...p]),a&&a.emit("applyFrameMetadata",e),void 0===e.type&&function(t,e){let n=function(t){let e=u._sentryDebugIds;if(!e)return{};let n=Object.keys(e);return s&&n.length===o?s:(o=n.length,s=n.reduce((n,r)=>{i||(i={});let o=i[r];if(o)n[o[0]]=o[1];else{let o=t(r);for(let t=o.length-1;t>=0;t--){let s=o[t],a=s?.filename,u=e[r];if(a&&u){n[a]=u,i[r]=[a,u];break}}}return n},{}))}(e);t.exception?.values?.forEach(t=>{t.stacktrace?.frames?.forEach(t=>{t.filename&&(t.debug_id=n[t.filename])})})}(y,t.stackParser);let E=function(t,e){if(!e)return t;let n=t?t.clone():new tu;return n.update(e),n}(r,n.captureContext);n.mechanism&&Q(y,n.mechanism);let S=a?a.getEventProcessors():[],T=tv().getScopeData();c&&eO(T,c.getScopeData()),E&&eO(T,E.getScopeData());let b=[...n.attachments||[],...T.attachments];b.length&&(n.attachments=b);let{fingerprint:O,span:R,breadcrumbs:A,sdkProcessingMetadata:P}=T;return function(t,e){let{extra:n,tags:r,user:i,contexts:o,level:s,transactionName:a}=e;Object.keys(n).length&&(t.extra={...n,...t.extra}),Object.keys(r).length&&(t.tags={...r,...t.tags}),Object.keys(i).length&&(t.user={...i,...t.user}),Object.keys(o).length&&(t.contexts={...o,...t.contexts}),s&&(t.level=s),a&&"transaction"!==t.type&&(t.transaction=a)}(y,T),R&&function(t,e){t.contexts={trace:tz(e),...t.contexts},t.sdkProcessingMetadata={dynamicSamplingContext:em(e),...t.sdkProcessingMetadata};let n=tK(t1(e)).description;n&&!t.transaction&&"transaction"===t.type&&(t.transaction=n)}(y,R),f=y,d=O,f.fingerprint=f.fingerprint?Array.isArray(f.fingerprint)?f.fingerprint:[f.fingerprint]:[],d&&(f.fingerprint=f.fingerprint.concat(d)),f.fingerprint.length||delete f.fingerprint,function(t,e){let n=[...t.breadcrumbs||[],...e];t.breadcrumbs=n.length?n:void 0}(y,A),h=y,_=P,h.sdkProcessingMetadata={...h.sdkProcessingMetadata,..._},(function t(e,n,r,i=0){return new eb((o,s)=>{let a=e[i];if(null===n||"function"!=typeof a)o(n);else{let u=a({...n},r);$(u)?u.then(n=>t(e,n,r,i+1).then(o)).then(null,s):t(e,u,r,i+1).then(o).then(null,s)}})})([...S,...T.eventProcessors],y,n).then(t=>(t&&function(t){let e={};if(t.exception?.values?.forEach(t=>{t.stacktrace?.frames?.forEach(t=>{t.debug_id&&(t.abs_path?e[t.abs_path]=t.debug_id:t.filename&&(e[t.filename]=t.debug_id),delete t.debug_id)})}),0===Object.keys(e).length)return;t.debug_meta=t.debug_meta||{},t.debug_meta.images=t.debug_meta.images||[];let n=t.debug_meta.images;Object.entries(e).forEach(([t,e])=>{n.push({type:"sourcemap",code_file:t,debug_id:e})})}(t),"number"==typeof g&&g>0)?function(t,e,n){if(!t)return null;let r={...t,...t.breadcrumbs&&{breadcrumbs:t.breadcrumbs.map(t=>({...t,...t.data&&{data:es(t.data,e,n)}}))},...t.user&&{user:es(t.user,e,n)},...t.contexts&&{contexts:es(t.contexts,e,n)},...t.extra&&{extra:es(t.extra,e,n)}};return t.contexts?.trace&&r.contexts&&(r.contexts.trace=t.contexts.trace,t.contexts.trace.data&&(r.contexts.trace.data=es(t.contexts.trace.data,e,n))),t.spans&&(r.spans=t.spans.map(t=>({...t,...t.data&&{data:es(t.data,e,n)}}))),t.contexts?.flags&&r.contexts&&(r.contexts.flags=es(t.contexts.flags,3,n)),r}(t,g,m):t)})(a,t,e,n,this,r).then(t=>(null===t||(this.emit("postprocessEvent",t,e),t.contexts={trace:tT(n),...t.contexts},t.sdkProcessingMetadata={dynamicSamplingContext:eg(this,n),...t.sdkProcessingMetadata}),t))}_captureEvent(t,e={},n=tm(),r=ty()){return this._processEvent(t,e,n,r).then(t=>t.event_id,t=>{})}_processEvent(t,e,n,r){let i=this.getOptions(),{sampleRate:o}=i,s=eN(t),a=ew(t),u=t.type||"error",c=`before send for type \`${u}\``,l=void 0===o?void 0:tF(o);if(a&&"number"==typeof l&&Math.random()>l)return this.recordDroppedEvent("sample_rate","error"),eT(ex(`Discarding event because it's not included in the random sample (sampling rate = ${o})`));let p="replay_event"===u?"replay":u;return this._prepareEvent(t,e,n,r).then(t=>{if(null===t)throw this.recordDroppedEvent("event_processor",p),ex("An event processor returned `null`, will not send event.");return e.data&&!0===e.data.__sentry__?t:function(t,e){let n=`${e} must return \`null\` or a valid event.`;if($(t))return t.then(t=>{if(!B(t)&&null!==t)throw eI(n);return t},t=>{throw eI(`${e} rejected with ${t}`)});if(!B(t)&&null!==t)throw eI(n);return t}(function(t,e,n,r){let{beforeSend:i,beforeSendTransaction:o,beforeSendSpan:s}=e,a=n;if(ew(a)&&i)return i(a,r);if(eN(a)){if(s){let t=s(function(t){let{trace_id:e,parent_span_id:n,span_id:r,status:i,origin:o,data:s,op:a}=t.contexts?.trace??{};return{data:s??{},description:t.transaction,op:a,parent_span_id:n,span_id:r??"",start_timestamp:t.start_timestamp??0,status:i,timestamp:t.timestamp,trace_id:e??"",origin:o,profile_id:s?.[tL],exclusive_time:s?.[tI],measurements:t.measurements,is_segment:!0}}(a));if(t)a=ti(n,{type:"transaction",timestamp:t.timestamp,start_timestamp:t.start_timestamp,transaction:t.description,contexts:{trace:{trace_id:t.trace_id,span_id:t.span_id,parent_span_id:t.parent_span_id,op:t.op,status:t.status,origin:t.origin,data:{...t.data,...t.profile_id&&{[tL]:t.profile_id},...t.exclusive_time&&{[tI]:t.exclusive_time}}}},measurements:t.measurements});else t4();if(a.spans){let t=[];for(let e of a.spans){let n=s(e);n?t.push(n):(t4(),t.push(e))}a.spans=t}}if(o){if(a.spans){let t=a.spans.length;a.sdkProcessingMetadata={...n.sdkProcessingMetadata,spanCountBeforeProcessing:t}}return o(a,r)}}return a}(0,i,t,e),c)}).then(i=>{if(null===i){if(this.recordDroppedEvent("before_send",p),s){let e=1+(t.spans||[]).length;this.recordDroppedEvent("before_send","span",e)}throw ex(`${c} returned \`null\`, will not send event.`)}let o=n.getSession()||r.getSession();if(a&&o&&this._updateSessionFromEvent(o,i),s){let t=(i.sdkProcessingMetadata?.spanCountBeforeProcessing||0)-(i.spans?i.spans.length:0);t>0&&this.recordDroppedEvent("before_send","span",t)}let u=i.transaction_info;return s&&u&&i.transaction!==t.transaction&&(i.transaction_info={...u,source:"custom"}),this.sendEvent(i,e),i}).then(null,t=>{var e,n;if((e=t)&&"object"==typeof e&&eL in e||(n=t)&&"object"==typeof n&&eP in n)throw t;throw this.captureException(t,{data:{__sentry__:!0},originalException:t}),eI(`Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.
Reason: ${t}`)})}_process(t){this._numProcessing++,t.then(t=>(this._numProcessing--,t),t=>(this._numProcessing--,t))}_clearOutcomes(){let t=this._outcomes;return this._outcomes={},Object.entries(t).map(([t,e])=>{let[n,r]=t.split(":");return{reason:n,category:r,quantity:e}})}_flushOutcomes(){var t;let e=this._clearOutcomes();if(0===e.length||!this._dsn)return;let n=ea((t=this._options.tunnel&&ei(this._dsn))?{dsn:t}:{},[[{type:"client_report"},{timestamp:te(),discarded_events:e}]]);this.sendEnvelope(n)}}function ew(t){return void 0===t.type}function eN(t){return"transaction"===t.type}function ek(t,e){return e?tE(e,()=>{let n=t2(),r=n?tz(n):tT(e);return[n?em(n):eg(t,e),r]}):[void 0,void 0]}let eM={trace:1,debug:5,info:9,warn:13,error:17,fatal:21};function eD(t,e,n,r=!0){n&&(!t[e]||r)&&(t[e]=n)}function ej(t,e){var n;let r=e??(n=t,eU().get(n))??[];if(0===r.length)return;let i=t.getOptions(),o=function(t,e,n,r){let i={};return e?.sdk&&(i.sdk={name:e.sdk.name,version:e.sdk.version}),n&&r&&(i.dsn=ei(r)),ea(i,[[{type:"log",item_count:t.length,content_type:"application/vnd.sentry.items.log+json"},{items:t}]])}(r,i._metadata,i.tunnel,t.getDsn());eU().set(t,[]),t.emit("flushLogs"),t.sendEnvelope(o)}function eU(){return f("clientToLogBufferMap",()=>new WeakMap)}function eB(t,e){return t(e.stack||"",1)}function e$(t,e){let n={type:e.name||e.constructor.name,value:e.message},r=eB(t,e);return r.length&&(n.stacktrace={frames:r}),n}class eF extends eC{constructor(t){if(t3(),super(t),this._logWeight=0,this._options.enableLogs??this._options._experiments?.enableLogs){let t=this;t.on("flushLogs",()=>{t._logWeight=0,clearTimeout(t._logFlushIdleTimeout)}),t.on("afterCaptureLog",e=>{t._logWeight+=function(t){let e=0;return t.message&&(e+=2*t.message.length),t.attributes&&Object.values(t.attributes).forEach(t=>{Array.isArray(t)?e+=t.length*eV(t[0]):U(t)?e+=eV(t):e+=100}),e}(e),t._logWeight>=8e5?ej(t):t._logFlushIdleTimeout=setTimeout(()=>{ej(t)},5e3)}),t.on("flush",()=>{ej(t)})}}eventFromException(t,e){let n=function(t,e,n,r){let i=r?.data&&r.data.mechanism||{handled:!0,type:"generic"},[o,s]=function(t,e,n,r){if(k(n))return[n,void 0];if(e.synthetic=!0,B(n)){let e={__serialized__:function t(e,n=3,r=102400){let i=es(e,n);return~-encodeURI(JSON.stringify(i)).split(/%..|./).length>r?t(e,n-1,r):i}(n,t?.getOptions().normalizeDepth)},i=function(t){for(let e in t)if(Object.prototype.hasOwnProperty.call(t,e)){let n=t[e];if(n instanceof Error)return n}}(n);if(i)return[i,e];let o=function(t){if("name"in t&&"string"==typeof t.name){let e=`'${t.name}' captured as exception`;return"message"in t&&"string"==typeof t.message&&(e+=` with message '${t.message}'`),e}if("message"in t&&"string"==typeof t.message)return t.message;let e=function(t,e=40){let n=Object.keys(Y(t));n.sort();let r=n[0];if(!r)return"[object has no keys]";if(r.length>=e)return X(r,e);for(let t=n.length;t>0;t--){let r=n.slice(0,t).join(", ");if(!(r.length>e)){if(t===n.length)return r;return X(r,e)}}return""}(t);if(M(t,"ErrorEvent"))return`Event \`ErrorEvent\` captured as exception with message \`${t.message}\``;let n=function(t){try{let e=Object.getPrototypeOf(t);return e?e.constructor.name:void 0}catch{}}(t);return`${n&&"Object"!==n?`'${n}'`:"Object"} captured as exception with keys: ${e}`}(n),s=r?.syntheticException||Error(o);return s.message=o,[s,e]}let i=r?.syntheticException||Error(n);return i.message=`${n}`,[i,void 0]}(t,i,n,r),a={exception:{values:[e$(e,o)]}};return s&&(a.extra=s),!function(t,e,n){let r=t.exception=t.exception||{},i=r.values=r.values||[],o=i[0]=i[0]||{};o.value||(o.value=e||""),o.type||(o.type=n||"Error")}(a,void 0,void 0),Q(a,i),{...a,event_id:r?.event_id}}(this,this._options.stackParser,t,e);return n.level="error",eS(n)}eventFromMessage(t,e="info",n){return eS(function(t,e,n="info",r,i){let o={event_id:r?.event_id,level:n};if(i&&r?.syntheticException){let n=eB(t,r.syntheticException);n.length&&(o.exception={values:[{value:e,stacktrace:{frames:n}}]},Q(o,{synthetic:!0}))}if(j(e)){let{__sentry_template_string__:t,__sentry_template_values__:n}=e;return o.logentry={message:t,params:n},o}return o.message=e,o}(this._options.stackParser,t,e,n,this._options.attachStacktrace))}captureException(t,e,n){return eG(e),super.captureException(t,e,n)}captureEvent(t,e,n){return!t.type&&t.exception?.values&&t.exception.values.length>0&&eG(e),super.captureEvent(t,e,n)}captureCheckIn(t,e,n){let r="checkInId"in t&&t.checkInId?t.checkInId:Z();if(!this._isEnabled())return r;let{release:i,environment:o,tunnel:s}=this.getOptions(),a={check_in_id:r,monitor_slug:t.monitorSlug,status:t.status,release:i,environment:o};"duration"in t&&(a.duration=t.duration),e&&(a.monitor_config={schedule:e.schedule,checkin_margin:e.checkinMargin,max_runtime:e.maxRuntime,timezone:e.timezone,failure_issue_threshold:e.failureIssueThreshold,recovery_threshold:e.recoveryThreshold});let[u,c]=ek(this,n);c&&(a.contexts={trace:c});let l=function(t,e,n,r,i){let o={sent_at:new Date().toISOString()};return n?.sdk&&(o.sdk={name:n.sdk.name,version:n.sdk.version}),r&&i&&(o.dsn=ei(i)),e&&(o.trace=e),ea(o,[[{type:"check_in"},t]])}(a,u,this.getSdkMetadata(),s,this.getDsn());return this.sendEnvelope(l),r}_prepareEvent(t,e,n,r){return this._options.platform&&(t.platform=t.platform||this._options.platform),this._options.runtime&&(t.contexts={...t.contexts,runtime:t.contexts?.runtime||this._options.runtime}),this._options.serverName&&(t.server_name=t.server_name||this._options.serverName),super._prepareEvent(t,e,n,r)}}function eG(t){let e=ty().getScopeData().sdkProcessingMetadata.requestSession;if(e){let n=t?.mechanism?.handled??!0;n&&"crashed"!==e.status?e.status="errored":n||(e.status="crashed")}}function eV(t){return"string"==typeof t?2*t.length:"number"==typeof t?8:4*("boolean"==typeof t)}class eX{constructor(t){this._maxSize=t,this._cache=new Map}get size(){return this._cache.size}get(t){let e=this._cache.get(t);if(void 0!==e)return this._cache.delete(t),this._cache.set(t,e),e}set(t,e){this._cache.size>=this._maxSize&&this._cache.delete(this._cache.keys().next().value),this._cache.set(t,e)}remove(t){let e=this._cache.get(t);return e&&this._cache.delete(t),e}clear(){this._cache.clear()}keys(){return Array.from(this._cache.keys())}values(){let t=[];return this._cache.forEach(e=>t.push(e)),t}}async function ez(t,e){if(t?.body){let n=t.body,r=n.getReader(),i=setTimeout(()=>{n.cancel().then(null,()=>{})},9e4),o=!0;for(;o;){let t;try{t=setTimeout(()=>{n.cancel().then(null,()=>{})},5e3);let{done:i}=await r.read();clearTimeout(t),i&&(e(),o=!1)}catch{o=!1}finally{clearTimeout(t)}}clearTimeout(i),r.releaseLock(),n.cancel().then(null,()=>{})}}function eH(t,e){return!!t&&"object"==typeof t&&!!t[e]}function eW(t){return"string"==typeof t?t:t?eH(t,"url")?t.url:t.toString?t.toString():"":""}function eY(t){return"/"===t[t.length-1]?t.slice(0,-1):t}class eK{constructor(t={}){this._traceId=t.traceId||Z(),this._spanId=t.spanId||to()}spanContext(){return{spanId:this._spanId,traceId:this._traceId,traceFlags:0}}end(t){}setAttribute(t,e){return this}setAttributes(t){return this}setStatus(t){return this}updateName(t){return this}isRecording(){return!1}addEvent(t,e,n){return this}addLink(t){return this}addLinks(t){return this}recordException(t,e){}}function eq(t){}function eZ(t){}function eJ(t,e,n){let r,i;if(!ed(t))return[!1];"function"==typeof t.tracesSampler?(r=t.tracesSampler({...e,inheritOrSampleWith:t=>"number"==typeof e.parentSampleRate?e.parentSampleRate:"boolean"==typeof e.parentSampled?Number(e.parentSampled):t}),i=!0):void 0!==e.parentSampled?r=e.parentSampled:void 0!==t.tracesSampleRate&&(r=t.tracesSampleRate,i=!0);let o=tF(r);if(void 0===o)return[!1];if(!o)return[!1,o,i];let s=n<o;return[s,o,i]}function eQ(t){if(!t||0===t.length)return;let e={};return t.forEach(t=>{let n=t.attributes||{},r=n["sentry.measurement_unit"],i=n["sentry.measurement_value"];"string"==typeof r&&"number"==typeof i&&(e[t.name]={value:i,unit:r})}),e}class e0{constructor(t={}){this._traceId=t.traceId||Z(),this._spanId=t.spanId||to(),this._startTime=t.startTimestamp||tn(),this._links=t.links,this._attributes={},this.setAttributes({[tA]:"manual",[tR]:t.op,...t.attributes}),this._name=t.name,t.parentSpanId&&(this._parentSpanId=t.parentSpanId),"sampled"in t&&(this._sampled=t.sampled),t.endTimestamp&&(this._endTime=t.endTimestamp),this._events=[],this._isStandaloneSpan=t.isStandalone,this._endTime&&this._onSpanEnded()}addLink(t){return this._links?this._links.push(t):this._links=[t],this}addLinks(t){return this._links?this._links.push(...t):this._links=t,this}recordException(t,e){}spanContext(){let{_spanId:t,_traceId:e,_sampled:n}=this;return{spanId:t,traceId:e,traceFlags:+!!n}}setAttribute(t,e){return void 0===e?delete this._attributes[t]:this._attributes[t]=e,this}setAttributes(t){return Object.keys(t).forEach(e=>this.setAttribute(e,t[e])),this}updateStartTime(t){this._startTime=tW(t)}setStatus(t){return this._status=t,this}updateName(t){return this._name=t,this.setAttribute(tb,"custom"),this}end(t){this._endTime||(this._endTime=tW(t),eZ(this),this._onSpanEnded())}getSpanJSON(){return{data:this._attributes,description:this._name,op:this._attributes[tR],parent_span_id:this._parentSpanId,span_id:this._spanId,start_timestamp:this._startTime,status:tZ(this._status),timestamp:this._endTime,trace_id:this._traceId,origin:this._attributes[tA],profile_id:this._attributes[tL],exclusive_time:this._attributes[tI],measurements:eQ(this._events),is_segment:this._isStandaloneSpan&&t1(this)===this||void 0,segment_id:this._isStandaloneSpan?t1(this).spanContext().spanId:void 0,links:tH(this._links)}}isRecording(){return!this._endTime&&!!this._sampled}addEvent(t,e,n){let r=e1(e)?e:n||tn(),i=e1(e)?{}:e||{},o={name:t,time:tW(r),attributes:i};return this._events.push(o),this}isStandaloneSpan(){return!!this._isStandaloneSpan}_onSpanEnded(){let t=tS();if(t&&t.emit("spanEnd",this),!(this._isStandaloneSpan||this===t1(this)))return;if(this._isStandaloneSpan)return void(this._sampled?function(t){let e=tS();if(!e)return;let n=t[1];if(!n||0===n.length)return e.recordDroppedEvent("before_send","span");e.sendEnvelope(t)}(function(t,e){let n=em(t[0]),r=e?.getDsn(),i=e?.getOptions().tunnel,o={sent_at:new Date().toISOString(),...!!n.trace_id&&!!n.public_key&&{trace:n},...!!i&&r&&{dsn:ei(r)}},s=e?.getOptions().beforeSendSpan,a=s?t=>{let e=tK(t),n=s(e);return n||(t4(),e)}:tK,u=[];for(let e of t){let t=a(e);t&&u.push([{type:"span"},t])}return ea(o,u)}([this],t)):t&&t.recordDroppedEvent("sample_rate","span"));let e=this._convertSpanToTransaction();e&&(tk(this).scope||tm()).captureEvent(e)}_convertSpanToTransaction(){if(!e2(tK(this)))return;this._name||(this._name="<unlabeled transaction>");let{scope:t,isolationScope:e}=tk(this),n=t?.getScopeData().sdkProcessingMetadata?.normalizedRequest;if(!0!==this._sampled)return;let r=(function(t){let e=new Set;return!function t(n){if(!e.has(n)&&tq(n))for(let r of(e.add(n),n[tJ]?Array.from(n[tJ]):[]))t(r)}(t),Array.from(e)})(this).filter(t=>{var e;return t!==this&&!((e=t)instanceof e0&&e.isStandaloneSpan())}).map(t=>tK(t)).filter(e2),i=this._attributes[tb];delete this._attributes[tP],r.forEach(t=>{delete t.data[tP]});let o={contexts:{trace:function(t){let{spanId:e,traceId:n}=t.spanContext(),{data:r,op:i,parent_span_id:o,status:s,origin:a,links:u}=tK(t);return{parent_span_id:o,span_id:e,trace_id:n,data:r,op:i,status:s,origin:a,links:u}}(this)},spans:r.length>1e3?r.sort((t,e)=>t.start_timestamp-e.start_timestamp).slice(0,1e3):r,start_timestamp:this._startTime,timestamp:this._endTime,transaction:this._name,type:"transaction",sdkProcessingMetadata:{capturedSpanScope:t,capturedSpanIsolationScope:e,dynamicSamplingContext:em(this)},request:n,...i&&{transaction_info:{source:i}}},s=eQ(this._events);return s&&Object.keys(s).length&&(o.measurements=s),o}}function e1(t){return t&&"number"==typeof t||t instanceof Date||Array.isArray(t)}function e2(t){return!!t.start_timestamp&&!!t.timestamp&&!!t.span_id&&!!t.trace_id}let e4="__SENTRY_SUPPRESS_TRACING__";function e5(t,e){let n=function(){return tg(l())}();return n.withActiveSpan?n.withActiveSpan(t,e):tE(n=>(ta(n,t||void 0),e(n)))}function e3(){return tg(l())}function e9(t,e,n){let r=tS(),i=r?.getOptions()||{},{name:o=""}=t,s={spanAttributes:{...t.attributes},spanName:o,parentSampled:n};r?.emit("beforeSampling",s,{decision:!1});let a=s.parentSampled??n,u=s.spanAttributes,c=e.getPropagationContext(),[l,p,f]=e.getScopeData().sdkProcessingMetadata[e4]?[!1]:eJ(i,{name:o,parentSampled:a,attributes:u,parentSampleRate:tF(c.dsc?.sample_rate)},c.sampleRand),d=new e0({...t,attributes:{[tb]:"custom",[tO]:void 0!==p&&f?p:void 0,...u},sampled:l});return!l&&r&&r.recordDroppedEvent("sample_rate","transaction"),r&&r.emit("spanStart",d),d}async function e8(t){let e=tS();return e?e.flush(t):Promise.resolve(!1)}function e6(t){return t.split(",").some(t=>t.trim().startsWith(tM))}function e7(t,e){let n=tS(),r=ty();if(!n)return;let{beforeBreadcrumb:i=null,maxBreadcrumbs:o=100}=n.getOptions();if(o<=0)return;let s={timestamp:te(),...t},a=i?_(()=>i(s,e)):s;null!==a&&(n.emit&&n.emit("beforeAddBreadcrumb",a,e),r.addBreadcrumb(a,o))}let nt=Symbol.for("SentryBufferFullError");function ne(t){return parseInt(t||"",10)||void 0}let nn=()=>{let t;return{name:"Dedupe",processEvent(e){if(e.type)return e;try{var n,r;if(n=e,(r=t)&&(function(t,e){let n=t.message,r=e.message;return(!!n||!!r)&&(!n||!!r)&&(!!n||!r)&&n===r&&!!ni(t,e)&&!!nr(t,e)&&!0}(n,r)||function(t,e){let n=no(e),r=no(t);return!!n&&!!r&&n.type===r.type&&n.value===r.value&&!!ni(t,e)&&!!nr(t,e)}(n,r)))return null}catch{}return t=e}}};function nr(t,e){let n=b(t),r=b(e);if(!n&&!r)return!0;if(n&&!r||!n&&r||r.length!==n.length)return!1;for(let t=0;t<r.length;t++){let e=r[t],i=n[t];if(e.filename!==i.filename||e.lineno!==i.lineno||e.colno!==i.colno||e.function!==i.function)return!1}return!0}function ni(t,e){let n=t.fingerprint,r=e.fingerprint;if(!n&&!r)return!0;if(n&&!r||!n&&r)return!1;try{return n.join("")===r.join("")}catch{return!1}}function no(t){return t.exception?.values?.[0]}let ns=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/,/^ResizeObserver loop completed with undelivered notifications.$/,/^Cannot redefine property: googletag$/,/^Can't find variable: gmo$/,/^undefined is not an object \(evaluating 'a\.[A-Z]'\)$/,'can\'t redefine non-configurable property "solana"',"vv().getRestrictions is not a function. (In 'vv().getRestrictions(1,a)', 'vv().getRestrictions' is undefined)","Can't find variable: _AutofillCallbackHandler",/^Non-Error promise rejection captured with value: Object Not Found Matching Id:\d+, MethodName:simulateEvent, ParamCount:\d+$/,/^Java exception was raised during method invocation$/],na=(t={})=>{let e;return{name:"EventFilters",setup(n){e=nc(t,n.getOptions())},processEvent:(n,r,i)=>(e||(e=nc(t,i.getOptions())),!function(t,e){if(t.type){if("transaction"===t.type&&function(t,e){if(!e?.length)return!1;let n=t.transaction;return!!n&&z(n,e)}(t,e.ignoreTransactions))return!0}else{var n,r,i;if(n=t,r=e.ignoreErrors,r?.length&&(function(t){let e=[];t.message&&e.push(t.message);try{let n=t.exception.values[t.exception.values.length-1];n?.value&&(e.push(n.value),n.type&&e.push(`${n.type}: ${n.value}`))}catch{}return e})(n).some(t=>z(t,r)))return!0;if(i=t,i.exception?.values?.length&&!i.message&&!i.exception.values.some(t=>t.stacktrace||t.type&&"Error"!==t.type||t.value)||function(t,e){if(!e?.length)return!1;let n=nl(t);return!!n&&z(n,e)}(t,e.denyUrls)||!function(t,e){if(!e?.length)return!0;let n=nl(t);return!n||z(n,e)}(t,e.allowUrls))return!0}return!1}(n,e)?n:null)}},nu=(t={})=>({...na(t),name:"InboundFilters"});function nc(t={},e={}){return{allowUrls:[...t.allowUrls||[],...e.allowUrls||[]],denyUrls:[...t.denyUrls||[],...e.denyUrls||[]],ignoreErrors:[...t.ignoreErrors||[],...e.ignoreErrors||[],...t.disableErrorDefaults?[]:ns],ignoreTransactions:[...t.ignoreTransactions||[],...e.ignoreTransactions||[]]}}function nl(t){try{let e=[...t.exception?.values??[]].reverse().find(t=>t.mechanism?.parent_id===void 0&&t.stacktrace?.frames?.length),n=e?.stacktrace?.frames;return n?function(t=[]){for(let e=t.length-1;e>=0;e--){let n=t[e];if(n&&"<anonymous>"!==n.filename&&"[native code]"!==n.filename)return n.filename||null}return null}(n):null}catch{return null}}let np=new WeakMap,nf=()=>({name:"FunctionToString",setupOnce(){a=Function.prototype.toString;try{Function.prototype.toString=function(...t){let e=this.__sentry_original__,n=np.has(tS())&&void 0!==e?e:this;return a.apply(n,t)}}catch{}},setup(t){np.set(t,!0)}});function nd(t,e){t.mechanism=t.mechanism||{type:"generic",handled:!0},t.mechanism={...t.mechanism,..."AggregateError"===t.type&&{is_exception_group:!0},exception_id:e}}function nh(t,e,n,r){t.mechanism=t.mechanism||{type:"generic",handled:!0},t.mechanism={...t.mechanism,type:"chained",source:e,exception_id:n,parent_id:r}}let n_=(t={})=>{let e=t.limit||5,n=t.key||"cause";return{name:"LinkedErrors",preprocessEvent(t,r,i){!function(t,e,n,r,i,o){if(!i.exception?.values||!o||!F(o.originalException,Error))return;let s=i.exception.values.length>0?i.exception.values[i.exception.values.length-1]:void 0;s&&(i.exception.values=function t(e,n,r,i,o,s,a,u){if(s.length>=r+1)return s;let c=[...s];if(F(i[o],Error)){nd(a,u);let s=e(n,i[o]),l=c.length;nh(s,o,l,u),c=t(e,n,r,i[o],o,[s,...c],s,l)}return Array.isArray(i.errors)&&i.errors.forEach((i,s)=>{if(F(i,Error)){nd(a,u);let l=e(n,i),p=c.length;nh(l,`errors[${s}]`,p,u),c=t(e,n,r,i,o,[l,...c],l,p)}}),c}(t,e,r,o.originalException,n,i.exception.values,s,0))}(e$,i.getOptions().stackParser,n,e,t,r)}}};function ng(){"console"in u&&d.forEach(function(t){t in u.console&&H(u.console,t,function(e){return h[t]=e,function(...e){L("console",{args:e,level:t});let n=h[t];n?.apply(u.console,e)}})})}let nm=(t={})=>{let e=new Set(t.levels||d);return{name:"Console",setup(t){let n="console";A(n,({args:n,level:r})=>{tS()===t&&e.has(r)&&function(t,e){let n={category:"console",data:{arguments:e,logger:"console"},level:"warn"===t?"warning":["fatal","error","warning","log","info","debug"].includes(t)?t:"log",message:ny(e)};if("assert"===t)if(!1!==e[0])return;else{let t=e.slice(1);n.message=t.length>0?`Assertion failed: ${ny(t)}`:"Assertion failed",n.data.arguments=t}e7(n,{input:e,level:t})}(r,n)}),P(n,ng)}}};function ny(t){return"util"in u&&"function"==typeof u.util.format?u.util.format(...t):function(t,e){if(!Array.isArray(t))return"";let n=[];for(let e=0;e<t.length;e++){let r=t[e];try{G(r)?n.push("[VueViewModel]"):n.push(String(r))}catch{n.push("[value cannot be serialized]")}}return n.join(" ")}(t,0)}let nv=["X-Client-IP","X-Forwarded-For","Fly-Client-IP","CF-Connecting-IP","Fastly-Client-Ip","True-Client-Ip","X-Real-IP","X-Cluster-Client-IP","X-Forwarded","Forwarded-For","Forwarded","X-Vercel-Forwarded-For"],nE={cookies:!0,data:!0,headers:!0,query_string:!0,url:!0},nS=(t={})=>{let e={...nE,...t.include};return{name:"RequestData",processEvent(t,n,r){let{sdkProcessingMetadata:i={}}=t,{normalizedRequest:o,ipAddress:s}=i,a={...e,ip:e.ip??r.getOptions().sendDefaultPii};return o&&function(t,e,n,r){if(t.request={...t.request,...function(t,e){let n={},r={...t.headers};return e.headers&&(n.headers=r,e.cookies||delete r.cookie,e.ip||nv.forEach(t=>{delete r[t]})),n.method=t.method,e.url&&(n.url=t.url),e.cookies&&(n.cookies=t.cookies||(r?.cookie?function(t){let e={},n=0;for(;n<t.length;){let r=t.indexOf("=",n);if(-1===r)break;let i=t.indexOf(";",n);if(-1===i)i=t.length;else if(i<r){n=t.lastIndexOf(";",r-1)+1;continue}let o=t.slice(n,r).trim();if(void 0===e[o]){let n=t.slice(r+1,i).trim();34===n.charCodeAt(0)&&(n=n.slice(1,-1));try{e[o]=-1!==n.indexOf("%")?decodeURIComponent(n):n}catch{e[o]=n}}n=i+1}return e}(r.cookie):void 0)||{}),e.query_string&&(n.query_string=t.query_string),e.data&&(n.data=t.data),n}(e,r)},r.ip){var i;let r=e.headers&&(i=e.headers,nv.map(t=>{let e=i[t],n=Array.isArray(e)?e.join(";"):e;return"Forwarded"===t?function(t){if(!t)return null;for(let e of t.split(";"))if(e.startsWith("for="))return e.slice(4);return null}(n):n?.split(",").map(t=>t.trim())}).reduce((t,e)=>e?t.concat(e):t,[]).find(t=>{var e;return null!==t&&(e=t,/(?:^(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}$)|(?:^(?:(?:[a-fA-F\d]{1,4}:){7}(?:[a-fA-F\d]{1,4}|:)|(?:[a-fA-F\d]{1,4}:){6}(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|:[a-fA-F\d]{1,4}|:)|(?:[a-fA-F\d]{1,4}:){5}(?::(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|(?::[a-fA-F\d]{1,4}){1,2}|:)|(?:[a-fA-F\d]{1,4}:){4}(?:(?::[a-fA-F\d]{1,4}){0,1}:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|(?::[a-fA-F\d]{1,4}){1,3}|:)|(?:[a-fA-F\d]{1,4}:){3}(?:(?::[a-fA-F\d]{1,4}){0,2}:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|(?::[a-fA-F\d]{1,4}){1,4}|:)|(?:[a-fA-F\d]{1,4}:){2}(?:(?::[a-fA-F\d]{1,4}){0,3}:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|(?::[a-fA-F\d]{1,4}){1,5}|:)|(?:[a-fA-F\d]{1,4}:){1}(?:(?::[a-fA-F\d]{1,4}){0,4}:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|(?::[a-fA-F\d]{1,4}){1,6}|:)|(?::(?:(?::[a-fA-F\d]{1,4}){0,5}:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|(?::[a-fA-F\d]{1,4}){1,7}|:)))(?:%[0-9a-zA-Z]{1,})?$)/.test(e))})||null)||n.ipAddress;r&&(t.user={...t.user,ip_address:r})}}(t,o,{ipAddress:s},a),t}}},nT="ai.prompt",nb="ai.model.id",nO="gen_ai.response.model",nR="gen_ai.usage.input_tokens",nA="gen_ai.usage.output_tokens",nP="ai.toolCall.name",nL="ai.toolCall.id";function nI(t,e){t.setAttribute(tA,e)}function nx(t){let{data:e,description:n}=tK(t);if(!n)return;if(e[nP]&&e[nL]&&"ai.toolCall"===n)return void function(t,e){nI(t,"auto.vercelai.otel"),t.setAttribute(tR,"gen_ai.execute_tool"),nw(e,nP,"gen_ai.tool.name"),nw(e,nL,"gen_ai.tool.call.id"),e["gen_ai.tool.type"]||t.setAttribute("gen_ai.tool.type","function");let n=e["gen_ai.tool.name"];n&&t.updateName(`execute_tool ${n}`)}(t,e);let r=e[nb],i=e["ai.model.provider"];"string"==typeof r&&"string"==typeof i&&r&&i&&function(t,e,n){nI(t,"auto.vercelai.otel");let r=e.replace("ai.","");t.setAttribute("ai.pipeline.name",r),t.updateName(r);let i=n["ai.telemetry.functionId"];if(i&&"string"==typeof i&&e.split(".").length-1==1&&(t.updateName(`${r} ${i}`),t.setAttribute("gen_ai.function_id",i)),n[nT]&&t.setAttribute("gen_ai.prompt",n[nT]),n[nb]&&!n[nO]&&t.setAttribute(nO,n[nb]),t.setAttribute("ai.streaming",e.includes("stream")),"ai.generateText"===e)return t.setAttribute(tR,"gen_ai.invoke_agent");if("ai.generateText.doGenerate"===e){t.setAttribute(tR,"gen_ai.generate_text"),t.updateName(`generate_text ${n[nb]}`);return}if("ai.streamText"===e)return t.setAttribute(tR,"gen_ai.invoke_agent");if("ai.streamText.doStream"===e){t.setAttribute(tR,"gen_ai.stream_text"),t.updateName(`stream_text ${n[nb]}`);return}if("ai.generateObject"===e)return t.setAttribute(tR,"gen_ai.invoke_agent");if("ai.generateObject.doGenerate"===e){t.setAttribute(tR,"gen_ai.generate_object"),t.updateName(`generate_object ${n[nb]}`);return}if("ai.streamObject"===e)return t.setAttribute(tR,"gen_ai.invoke_agent");if("ai.streamObject.doStream"===e){t.setAttribute(tR,"gen_ai.stream_object"),t.updateName(`stream_object ${n[nb]}`);return}if("ai.embed"===e)return t.setAttribute(tR,"gen_ai.invoke_agent");if("ai.embed.doEmbed"===e){t.setAttribute(tR,"gen_ai.embed"),t.updateName(`embed ${n[nb]}`);return}if("ai.embedMany"===e)return t.setAttribute(tR,"gen_ai.invoke_agent");if("ai.embedMany.doEmbed"===e){t.setAttribute(tR,"gen_ai.embed_many"),t.updateName(`embed_many ${n[nb]}`);return}e.startsWith("ai.stream")&&t.setAttribute(tR,"ai.run")}(t,n,e)}function nC(t){if("transaction"===t.type&&t.spans)for(let n of t.spans){var e=n;let{data:t,origin:r}=e;if("auto.vercelai.otel"===r)for(let e of(nw(t,"ai.usage.completionTokens",nA),nw(t,"ai.usage.promptTokens",nR),"number"==typeof t[nA]&&"number"==typeof t[nR]&&(t["gen_ai.usage.total_tokens"]=t[nA]+t[nR]),nw(t,"ai.prompt.messages","gen_ai.request.messages"),nw(t,"ai.response.text","gen_ai.response.text"),nw(t,"ai.response.toolCalls","gen_ai.response.tool_calls"),nw(t,"ai.response.object","gen_ai.response.object"),nw(t,"ai.prompt.tools","gen_ai.request.available_tools"),nw(t,"ai.toolCall.args","gen_ai.tool.input"),nw(t,"ai.toolCall.result","gen_ai.tool.output"),function(t){let e=t["ai.response.providerMetadata"];if(e)try{let n=JSON.parse(e);n.openai&&(nN(t,"gen_ai.usage.input_tokens.cached",n.openai.cachedPromptTokens),nN(t,"gen_ai.usage.output_tokens.reasoning",n.openai.reasoningTokens),nN(t,"gen_ai.usage.output_tokens.prediction_accepted",n.openai.acceptedPredictionTokens),nN(t,"gen_ai.usage.output_tokens.prediction_rejected",n.openai.rejectedPredictionTokens),nN(t,"gen_ai.conversation.id",n.openai.responseId)),n.anthropic&&(nN(t,"gen_ai.usage.input_tokens.cached",n.anthropic.cacheReadInputTokens),nN(t,"gen_ai.usage.input_tokens.cache_write",n.anthropic.cacheCreationInputTokens)),n.bedrock?.usage&&(nN(t,"gen_ai.usage.input_tokens.cached",n.bedrock.usage.cacheReadInputTokens),nN(t,"gen_ai.usage.input_tokens.cache_write",n.bedrock.usage.cacheWriteInputTokens)),n.deepseek&&(nN(t,"gen_ai.usage.input_tokens.cached",n.deepseek.promptCacheHitTokens),nN(t,"gen_ai.usage.input_tokens.cache_miss",n.deepseek.promptCacheMissTokens))}catch{}}(t),Object.keys(t)))e.startsWith("ai.")&&nw(t,e,`vercel.${e}`)}return t}function nw(t,e,n){null!=t[e]&&(t[n]=t[e],delete t[e])}function nN(t,e,n){null!=n&&(t[e]=n)}function nk(t){return Symbol.for(t)}var nM,nD,nj,nU,nB,n$,nF,nG,nV,nX,nz,nH,nW,nY=new function t(e){var n=this;n._currentContext=e?new Map(e):new Map,n.getValue=function(t){return n._currentContext.get(t)},n.setValue=function(e,r){var i=new t(n._currentContext);return i._currentContext.set(e,r),i},n.deleteValue=function(e){var r=new t(n._currentContext);return r._currentContext.delete(e),r}},nK=function(t,e){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var r,i,o=n.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(r=o.next()).done;)s.push(r.value)}catch(t){i={error:t}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}return s},nq=function(t,e,n){if(n||2==arguments.length)for(var r,i=0,o=e.length;i<o;i++)!r&&i in e||(r||(r=Array.prototype.slice.call(e,0,i)),r[i]=e[i]);return t.concat(r||Array.prototype.slice.call(e))},nZ=function(){function t(){}return t.prototype.active=function(){return nY},t.prototype.with=function(t,e,n){for(var r=[],i=3;i<arguments.length;i++)r[i-3]=arguments[i];return e.call.apply(e,nq([n],nK(r),!1))},t.prototype.bind=function(t,e){return e},t.prototype.enable=function(){return this},t.prototype.disable=function(){return this},t}(),nJ="object"==typeof globalThis?globalThis:"object"==typeof self?self:"object"==typeof window?window:"object"==typeof n.g?n.g:{},nQ="1.9.0",n0=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/,n1=function(t){var e=new Set([t]),n=new Set,r=t.match(n0);if(!r)return function(){return!1};var i={major:+r[1],minor:+r[2],patch:+r[3],prerelease:r[4]};if(null!=i.prerelease)return function(e){return e===t};function o(t){return n.add(t),!1}return function(t){if(e.has(t))return!0;if(n.has(t))return!1;var r=t.match(n0);if(!r)return o(t);var s={major:+r[1],minor:+r[2],patch:+r[3],prerelease:r[4]};if(null!=s.prerelease||i.major!==s.major)return o(t);if(0===i.major)return i.minor===s.minor&&i.patch<=s.patch?(e.add(t),!0):o(t);return i.minor<=s.minor?(e.add(t),!0):o(t)}}(nQ),n2=Symbol.for("opentelemetry.js.api."+nQ.split(".")[0]);function n4(t,e,n,r){void 0===r&&(r=!1);var i,o=nJ[n2]=null!=(i=nJ[n2])?i:{version:nQ};if(!r&&o[t]){var s=Error("@opentelemetry/api: Attempted duplicate registration of API: "+t);return n.error(s.stack||s.message),!1}if(o.version!==nQ){var s=Error("@opentelemetry/api: Registration of version v"+o.version+" for "+t+" does not match previously registered API v"+nQ);return n.error(s.stack||s.message),!1}return o[t]=e,n.debug("@opentelemetry/api: Registered a global for "+t+" v"+nQ+"."),!0}function n5(t){var e,n,r=null==(e=nJ[n2])?void 0:e.version;if(r&&n1(r))return null==(n=nJ[n2])?void 0:n[t]}function n3(t,e){e.debug("@opentelemetry/api: Unregistering a global for "+t+" v"+nQ+".");var n=nJ[n2];n&&delete n[t]}var n9=function(t,e){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var r,i,o=n.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(r=o.next()).done;)s.push(r.value)}catch(t){i={error:t}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}return s},n8=function(t,e,n){if(n||2==arguments.length)for(var r,i=0,o=e.length;i<o;i++)!r&&i in e||(r||(r=Array.prototype.slice.call(e,0,i)),r[i]=e[i]);return t.concat(r||Array.prototype.slice.call(e))},n6=function(){function t(t){this._namespace=t.namespace||"DiagComponentLogger"}return t.prototype.debug=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return n7("debug",this._namespace,t)},t.prototype.error=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return n7("error",this._namespace,t)},t.prototype.info=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return n7("info",this._namespace,t)},t.prototype.warn=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return n7("warn",this._namespace,t)},t.prototype.verbose=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return n7("verbose",this._namespace,t)},t}();function n7(t,e,n){var r=n5("diag");if(r)return n.unshift(e),r[t].apply(r,n8([],n9(n),!1))}!function(t){t[t.NONE=0]="NONE",t[t.ERROR=30]="ERROR",t[t.WARN=50]="WARN",t[t.INFO=60]="INFO",t[t.DEBUG=70]="DEBUG",t[t.VERBOSE=80]="VERBOSE",t[t.ALL=9999]="ALL"}(nj||(nj={}));var rt=function(t,e){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var r,i,o=n.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(r=o.next()).done;)s.push(r.value)}catch(t){i={error:t}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}return s},re=function(t,e,n){if(n||2==arguments.length)for(var r,i=0,o=e.length;i<o;i++)!r&&i in e||(r||(r=Array.prototype.slice.call(e,0,i)),r[i]=e[i]);return t.concat(r||Array.prototype.slice.call(e))},rn=function(){function t(){function t(t){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];var r=n5("diag");if(r)return r[t].apply(r,re([],rt(e),!1))}}var e=this;e.setLogger=function(t,n){if(void 0===n&&(n={logLevel:nj.INFO}),t===e){var r,i,o,s=Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return e.error(null!=(r=s.stack)?r:s.message),!1}"number"==typeof n&&(n={logLevel:n});var a=n5("diag"),u=function(t,e){function n(n,r){var i=e[n];return"function"==typeof i&&t>=r?i.bind(e):function(){}}return t<nj.NONE?t=nj.NONE:t>nj.ALL&&(t=nj.ALL),e=e||{},{error:n("error",nj.ERROR),warn:n("warn",nj.WARN),info:n("info",nj.INFO),debug:n("debug",nj.DEBUG),verbose:n("verbose",nj.VERBOSE)}}(null!=(i=n.logLevel)?i:nj.INFO,t);if(a&&!n.suppressOverrideMessage){var c=null!=(o=Error().stack)?o:"<failed to generate stacktrace>";a.warn("Current logger will be overwritten from "+c),u.warn("Current logger will overwrite one already registered from "+c)}return n4("diag",u,e,!0)},e.disable=function(){n3("diag",e)},e.createComponentLogger=function(t){return new n6(t)},e.verbose=t("verbose"),e.debug=t("debug"),e.info=t("info"),e.warn=t("warn"),e.error=t("error")}return t.instance=function(){return this._instance||(this._instance=new t),this._instance},t}(),rr=function(t,e){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var r,i,o=n.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(r=o.next()).done;)s.push(r.value)}catch(t){i={error:t}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}return s},ri=function(t,e,n){if(n||2==arguments.length)for(var r,i=0,o=e.length;i<o;i++)!r&&i in e||(r||(r=Array.prototype.slice.call(e,0,i)),r[i]=e[i]);return t.concat(r||Array.prototype.slice.call(e))},ro="context",rs=new nZ,ra=function(){function t(){}return t.getInstance=function(){return this._instance||(this._instance=new t),this._instance},t.prototype.setGlobalContextManager=function(t){return n4(ro,t,rn.instance())},t.prototype.active=function(){return this._getContextManager().active()},t.prototype.with=function(t,e,n){for(var r,i=[],o=3;o<arguments.length;o++)i[o-3]=arguments[o];return(r=this._getContextManager()).with.apply(r,ri([t,e,n],rr(i),!1))},t.prototype.bind=function(t,e){return this._getContextManager().bind(t,e)},t.prototype._getContextManager=function(){return n5(ro)||rs},t.prototype.disable=function(){this._getContextManager().disable(),n3(ro,rn.instance())},t}(),ru=ra.getInstance();!function(t){t[t.NONE=0]="NONE",t[t.SAMPLED=1]="SAMPLED"}(nU||(nU={}));var rc="0000000000000000",rl="00000000000000000000000000000000",rp={traceId:rl,spanId:rc,traceFlags:nU.NONE},rf=function(){function t(t){void 0===t&&(t=rp),this._spanContext=t}return t.prototype.spanContext=function(){return this._spanContext},t.prototype.setAttribute=function(t,e){return this},t.prototype.setAttributes=function(t){return this},t.prototype.addEvent=function(t,e){return this},t.prototype.addLink=function(t){return this},t.prototype.addLinks=function(t){return this},t.prototype.setStatus=function(t){return this},t.prototype.updateName=function(t){return this},t.prototype.end=function(t){},t.prototype.isRecording=function(){return!1},t.prototype.recordException=function(t,e){},t}(),rd=nk("OpenTelemetry Context Key SPAN");function rh(t){return t.getValue(rd)||void 0}function r_(){return rh(ra.getInstance().active())}function rg(t,e){return t.setValue(rd,e)}function rm(t){return t.deleteValue(rd)}function ry(t,e){return rg(t,new rf(e))}function rv(t){var e;return null==(e=rh(t))?void 0:e.spanContext()}var rE=/^([0-9a-f]{32})$/i,rS=/^[0-9a-f]{16}$/i;function rT(t){return rE.test(t)&&t!==rl}function rb(t){var e;return rT(t.traceId)&&(e=t.spanId,rS.test(e)&&e!==rc)}function rO(t){return new rf(t)}var rR=ra.getInstance(),rA=function(){function t(){}return t.prototype.startSpan=function(t,e,n){if(void 0===n&&(n=rR.active()),null==e?void 0:e.root)return new rf;var r,i=n&&rv(n);return"object"==typeof(r=i)&&"string"==typeof r.spanId&&"string"==typeof r.traceId&&"number"==typeof r.traceFlags&&rb(i)?new rf(i):new rf},t.prototype.startActiveSpan=function(t,e,n,r){if(!(arguments.length<2)){2==arguments.length?s=e:3==arguments.length?(i=e,s=n):(i=e,o=n,s=r);var i,o,s,a=null!=o?o:rR.active(),u=this.startSpan(t,i,a),c=rg(a,u);return rR.with(c,s,void 0,u)}},t}(),rP=new rA,rL=function(){function t(t,e,n,r){this._provider=t,this.name=e,this.version=n,this.options=r}return t.prototype.startSpan=function(t,e,n){return this._getTracer().startSpan(t,e,n)},t.prototype.startActiveSpan=function(t,e,n,r){var i=this._getTracer();return Reflect.apply(i.startActiveSpan,i,arguments)},t.prototype._getTracer=function(){if(this._delegate)return this._delegate;var t=this._provider.getDelegateTracer(this.name,this.version,this.options);return t?(this._delegate=t,this._delegate):rP},t}(),rI=new(function(){function t(){}return t.prototype.getTracer=function(t,e,n){return new rA},t}()),rx=function(){function t(){}return t.prototype.getTracer=function(t,e,n){var r;return null!=(r=this.getDelegateTracer(t,e,n))?r:new rL(this,t,e,n)},t.prototype.getDelegate=function(){var t;return null!=(t=this._delegate)?t:rI},t.prototype.setDelegate=function(t){this._delegate=t},t.prototype.getDelegateTracer=function(t,e,n){var r;return null==(r=this._delegate)?void 0:r.getTracer(t,e,n)},t}(),rC="trace",rw=(function(){function t(){this._proxyTracerProvider=new rx,this.wrapSpanContext=rO,this.isSpanContextValid=rb,this.deleteSpan=rm,this.getSpan=rh,this.getActiveSpan=r_,this.getSpanContext=rv,this.setSpan=rg,this.setSpanContext=ry}return t.getInstance=function(){return this._instance||(this._instance=new t),this._instance},t.prototype.setGlobalTracerProvider=function(t){var e=n4(rC,this._proxyTracerProvider,rn.instance());return e&&this._proxyTracerProvider.setDelegate(t),e},t.prototype.getTracerProvider=function(){return n5(rC)||this._proxyTracerProvider},t.prototype.getTracer=function(t,e){return this.getTracerProvider().getTracer(t,e)},t.prototype.disable=function(){n3(rC,rn.instance()),this._proxyTracerProvider=new rx},t})().getInstance(),rN=rn.instance();!function(t){t[t.INTERNAL=0]="INTERNAL",t[t.SERVER=1]="SERVER",t[t.CLIENT=2]="CLIENT",t[t.PRODUCER=3]="PRODUCER",t[t.CONSUMER=4]="CONSUMER"}(nB||(nB={})),function(t){t[t.NOT_RECORD=0]="NOT_RECORD",t[t.RECORD=1]="RECORD",t[t.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(n$||(n$={}));var rk=function(t,e){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var r,i,o=n.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(r=o.next()).done;)s.push(r.value)}catch(t){i={error:t}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}return s},rM=function(t){var e="function"==typeof Symbol&&Symbol.iterator,n=e&&t[e],r=0;if(n)return n.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},rD=function(){function t(t){this._entries=t?new Map(t):new Map}return t.prototype.getEntry=function(t){var e=this._entries.get(t);if(e)return Object.assign({},e)},t.prototype.getAllEntries=function(){return Array.from(this._entries.entries()).map(function(t){var e=rk(t,2);return[e[0],e[1]]})},t.prototype.setEntry=function(e,n){var r=new t(this._entries);return r._entries.set(e,n),r},t.prototype.removeEntry=function(e){var n=new t(this._entries);return n._entries.delete(e),n},t.prototype.removeEntries=function(){for(var e,n,r=[],i=0;i<arguments.length;i++)r[i]=arguments[i];var o=new t(this._entries);try{for(var s=rM(r),a=s.next();!a.done;a=s.next()){var u=a.value;o._entries.delete(u)}}catch(t){e={error:t}}finally{try{a&&!a.done&&(n=s.return)&&n.call(s)}finally{if(e)throw e.error}}return o},t.prototype.clear=function(){return new t},t}(),rj=Symbol("BaggageEntryMetadata"),rU=rn.instance();function rB(t){return void 0===t&&(t={}),new rD(new Map(Object.entries(t)))}function r$(t){return"string"!=typeof t&&(rU.error("Cannot create baggage metadata from unknown type: "+typeof t),t=""),{__TYPE__:rj,toString:function(){return t}}}var rF=function(){function t(){}return t.prototype.inject=function(t,e){},t.prototype.extract=function(t,e){return t},t.prototype.fields=function(){return[]},t}(),rG={get:function(t,e){if(null!=t)return t[e]},keys:function(t){return null==t?[]:Object.keys(t)}},rV={set:function(t,e,n){null!=t&&(t[e]=n)}},rX=nk("OpenTelemetry Baggage Key");function rz(t){return t.getValue(rX)||void 0}function rH(){return rz(ra.getInstance().active())}function rW(t,e){return t.setValue(rX,e)}function rY(t){return t.deleteValue(rX)}var rK="propagation",rq=new rF,rZ=(function(){function t(){this.createBaggage=rB,this.getBaggage=rz,this.getActiveBaggage=rH,this.setBaggage=rW,this.deleteBaggage=rY}return t.getInstance=function(){return this._instance||(this._instance=new t),this._instance},t.prototype.setGlobalPropagator=function(t){return n4(rK,t,rn.instance())},t.prototype.inject=function(t,e,n){return void 0===n&&(n=rV),this._getGlobalPropagator().inject(t,e,n)},t.prototype.extract=function(t,e,n){return void 0===n&&(n=rG),this._getGlobalPropagator().extract(t,e,n)},t.prototype.fields=function(){return this._getGlobalPropagator().fields()},t.prototype.disable=function(){n3(rK,rn.instance())},t.prototype._getGlobalPropagator=function(){return n5(rK)||rq},t})().getInstance();!function(t){t[t.UNSET=0]="UNSET",t[t.OK=1]="OK",t[t.ERROR=2]="ERROR"}(nF||(nF={}));var rJ="telemetry.sdk.name",rQ="telemetry.sdk.language",r0="telemetry.sdk.version",r1=((nG={})["telemetry.sdk.name"]="opentelemetry",nG["process.runtime.name"]="browser",nG["telemetry.sdk.language"]="webjs",nG["telemetry.sdk.version"]="1.30.1",nG),r2=function(){return(r2=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var i in e=arguments[n])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)},r4=function(t,e){var n,r,i,o,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(o){return function(a){var u=[o,a];if(n)throw TypeError("Generator is already executing.");for(;s;)try{if(n=1,r&&(i=2&u[0]?r.return:u[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,u[1])).done)return i;switch(r=0,i&&(u=[2&u[0],i.value]),u[0]){case 0:case 1:i=u;break;case 4:return s.label++,{value:u[1],done:!1};case 5:s.label++,r=u[1],u=[0];continue;case 7:u=s.ops.pop(),s.trys.pop();continue;default:if(!(i=(i=s.trys).length>0&&i[i.length-1])&&(6===u[0]||2===u[0])){s=0;continue}if(3===u[0]&&(!i||u[1]>i[0]&&u[1]<i[3])){s.label=u[1];break}if(6===u[0]&&s.label<i[1]){s.label=i[1],i=u;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(u);break}i[2]&&s.ops.pop(),s.trys.pop();continue}u=e.call(t,s)}catch(t){u=[6,t],r=0}finally{n=i=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}},r5=function(t,e){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var r,i,o=n.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(r=o.next()).done;)s.push(r.value)}catch(t){i={error:t}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}return s},r3=function(){function t(t,e){var n,r=this;this._attributes=t,this.asyncAttributesPending=null!=e,this._syncAttributes=null!=(n=this._attributes)?n:{},this._asyncAttributesPromise=null==e?void 0:e.then(function(t){return r._attributes=Object.assign({},r._attributes,t),r.asyncAttributesPending=!1,t},function(t){return rN.debug("a resource's async attributes promise rejected: %s",t),r.asyncAttributesPending=!1,{}})}return t.empty=function(){return t.EMPTY},t.default=function(){var e;return new t(((e={})["service.name"]="unknown_service",e[rQ]=r1[rQ],e[rJ]=r1[rJ],e[r0]=r1[r0],e))},Object.defineProperty(t.prototype,"attributes",{get:function(){var t;return this.asyncAttributesPending&&rN.error("Accessing resource attributes before async attributes settled"),null!=(t=this._attributes)?t:{}},enumerable:!1,configurable:!0}),t.prototype.waitForAsyncAttributes=function(){var t,e,n,r;return t=this,e=void 0,n=void 0,r=function(){return r4(this,function(t){switch(t.label){case 0:if(!this.asyncAttributesPending)return[3,2];return[4,this._asyncAttributesPromise];case 1:t.sent(),t.label=2;case 2:return[2]}})},new(n||(n=Promise))(function(i,o){function s(t){try{u(r.next(t))}catch(t){o(t)}}function a(t){try{u(r.throw(t))}catch(t){o(t)}}function u(t){var e;t.done?i(t.value):((e=t.value)instanceof n?e:new n(function(t){t(e)})).then(s,a)}u((r=r.apply(t,e||[])).next())})},t.prototype.merge=function(e){var n,r=this;if(!e)return this;var i=r2(r2({},this._syncAttributes),null!=(n=e._syncAttributes)?n:e.attributes);return this._asyncAttributesPromise||e._asyncAttributesPromise?new t(i,Promise.all([this._asyncAttributesPromise,e._asyncAttributesPromise]).then(function(t){var n,i=r5(t,2),o=i[0],s=i[1];return r2(r2(r2(r2({},r._syncAttributes),o),null!=(n=e._syncAttributes)?n:e.attributes),s)})):new t(i)},t.EMPTY=new t({}),t}();let r9="exception.type",r8="exception.message",r6="http.method",r7="http.url",it="http.status_code",ie="http.request.method",ir="http.response.status_code",ii="url.full";function io(t,e,n=()=>{}){var r,i,o;let s;try{s=t()}catch(t){throw e(t),n(),t}return r=s,i=e,o=n,$(r)?r.then(t=>(o(),t),t=>{throw i(t),o(),t}):(o(),r)}var is="[_0-9a-z-*/]",ia=RegExp("^(?:[a-z]"+is+"{0,255}|"+("[a-z0-9]"+is+"{0,240}@[a-z]")+is+"{0,13})$"),iu=/^[ -~]{0,255}[!-~]$/,ic=/,|=/,il=function(){function t(t){this._internalState=new Map,t&&this._parse(t)}return t.prototype.set=function(t,e){var n=this._clone();return n._internalState.has(t)&&n._internalState.delete(t),n._internalState.set(t,e),n},t.prototype.unset=function(t){var e=this._clone();return e._internalState.delete(t),e},t.prototype.get=function(t){return this._internalState.get(t)},t.prototype.serialize=function(){var t=this;return this._keys().reduce(function(e,n){return e.push(n+"="+t.get(n)),e},[]).join(",")},t.prototype._parse=function(t){!(t.length>512)&&(this._internalState=t.split(",").reverse().reduce(function(t,e){var n=e.trim(),r=n.indexOf("=");if(-1!==r){var i=n.slice(0,r),o=n.slice(r+1,e.length);ia.test(i)&&iu.test(o)&&!ic.test(o)&&t.set(i,o)}return t},new Map),this._internalState.size>32&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,32))))},t.prototype._keys=function(){return Array.from(this._internalState.keys()).reverse()},t.prototype._clone=function(){var e=new t;return e._internalState=new Map(this._internalState),e},t}(),ip=nk("OpenTelemetry SDK Context Key SUPPRESS_TRACING");function id(t){return t.setValue(ip,!0)}function ih(t){return!0===t.getValue(ip)}var i_="baggage",ig=function(t,e){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var r,i,o=n.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(r=o.next()).done;)s.push(r.value)}catch(t){i={error:t}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}return s},im=function(){function t(){}return t.prototype.inject=function(t,e,n){var r=rZ.getBaggage(t);if(!(!r||ih(t))){var i=r.getAllEntries().map(function(t){var e=ig(t,2),n=e[0],r=e[1],i=encodeURIComponent(n)+"="+encodeURIComponent(r.value);return void 0!==r.metadata&&(i+=";"+r.metadata.toString()),i}).filter(function(t){return t.length<=4096}).slice(0,180).reduce(function(t,e){var n=""+t+(""!==t?",":"")+e;return n.length>8192?t:n},"");i.length>0&&n.set(e,i_,i)}},t.prototype.extract=function(t,e,n){var r=n.get(e,i_),i=Array.isArray(r)?r.join(","):r;if(!i)return t;var o={};return 0===i.length||(i.split(",").forEach(function(t){var e=function(t){var e,n=t.split(";");if(!(n.length<=0)){var r=n.shift();if(r){var i=r.indexOf("=");if(!(i<=0)){var o=decodeURIComponent(r.substring(0,i).trim()),s=decodeURIComponent(r.substring(i+1).trim());return n.length>0&&(e=r$(n.join(";"))),{key:o,value:s,metadata:e}}}}}(t);if(e){var n={value:e.value};e.metadata&&(n.metadata=e.metadata),o[e.key]=n}}),0===Object.entries(o).length)?t:rZ.setBaggage(t,rZ.createBaggage(o))},t.prototype.fields=function(){return[i_]},t}();!function(t){t[t.NOT_RECORD=0]="NOT_RECORD",t[t.RECORD=1]="RECORD",t[t.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(nV||(nV={}));let iy="sentry.parentIsRemote";function iv(t){return"parentSpanId"in t?t.parentSpanId:"parentSpanContext"in t?t.parentSpanContext?.spanId:void 0}function iE(t){return!!t.attributes&&"object"==typeof t.attributes}let iS="sentry-trace",iT="baggage",ib="sentry.dsc",iO="sentry.sampled_not_recording",iR="sentry.url",iA=nk("sentry_scopes"),iP=nk("sentry_fork_isolation_scope"),iL=nk("sentry_fork_set_scope"),iI=nk("sentry_fork_set_isolation_scope"),ix="_scopeContext";function iC(t){return t.getValue(iA)}function iw(t,e){return t.setValue(iA,e)}function iN(t){let{traceFlags:e,traceState:n}=t,r=!!n&&"1"===n.get(iO);if(e===nU.SAMPLED)return!0;if(r)return!1;let i=n?n.get(ib):void 0,o=i?tj(i):void 0;return o?.sampled==="true"||o?.sampled!=="false"&&void 0}function ik(t,e,n){let r=e[ie]||e[r6];if(r)return function({name:t,kind:e,attributes:n},r){let i=["http"];switch(e){case nB.CLIENT:i.push("client");break;case nB.SERVER:i.push("server")}n["sentry.http.prefetch"]&&i.push("prefetch");let{urlPath:o,url:s,query:a,fragment:u,hasRoute:c}=function(t,e){let n=t["http.target"],r=t[r7]||t[ii],i=t["http.route"],o="string"==typeof r?t6(r):void 0,s=o?et(o):void 0,a=o?.search||void 0,u=o?.hash||void 0;return"string"==typeof i?{urlPath:i,url:s,query:a,fragment:u,hasRoute:!0}:e===nB.SERVER&&"string"==typeof n?{urlPath:t7(n),url:s,query:a,fragment:u,hasRoute:!1}:o?{urlPath:s,url:s,query:a,fragment:u,hasRoute:!1}:"string"==typeof n?{urlPath:t7(n),url:s,query:a,fragment:u,hasRoute:!1}:{urlPath:void 0,url:s,query:a,fragment:u,hasRoute:!1}}(n,e);if(!o)return{...iD(t,n),op:i.join(".")};let l=n["sentry.graphql.operation"],p=`${r} ${o}`,f=l?`${p} (${function(t){if(Array.isArray(t)){let e=t.slice().sort();return e.length<=5?e.join(", "):`${e.slice(0,5).join(", ")}, +${e.length-5}`}return`${t}`}(l)})`:p,d={};s&&(d.url=s),a&&(d["http.query"]=a),u&&(d["http.fragment"]=u);let h=e===nB.CLIENT||e===nB.SERVER,_=n[tA]||"manual",g=!`${_}`.startsWith("auto"),m="custom"===n[tb],y=n[tP],{description:v,source:E}=m||null!=y||!h&&g?iD(t,n):{description:f,source:c||"/"===o?"route":"url"};return{op:i.join("."),description:v,source:E,data:d}}({attributes:e,name:t,kind:n},r);let i=e["db.system"],o="string"==typeof e[tR]&&e[tR].startsWith("cache.");if(i&&!o)return function({attributes:t,name:e}){let n=t[tP];if("string"==typeof n)return{op:"db",description:n,source:t[tb]||"custom"};if("custom"===t[tb])return{op:"db",description:e,source:"custom"};let r=t["db.statement"];return{op:"db",description:r?r.toString():e,source:"task"}}({attributes:e,name:t});let s="custom"===e[tb]?"custom":"route";if(e["rpc.service"])return{...iD(t,e,"route"),op:"rpc"};if(e["messaging.system"])return{...iD(t,e,s),op:"message"};let a=e["faas.trigger"];return a?{...iD(t,e,s),op:a.toString()}:{op:void 0,description:t,source:"custom"}}function iM(t){let e=iE(t)?t.attributes:{};return ik(t.name?t.name:"<unknown>",e,"number"==typeof t.kind?t.kind:nB.INTERNAL)}function iD(t,e,n="custom"){let r=e[tb]||n,i=e[tP];return i&&"string"==typeof i?{description:i,source:r}:{description:t,source:r}}function ij(){return rw.getActiveSpan()}function iU({dsc:t,sampled:e}){let n=t?tU(t):void 0,r=new il,i=n?r.set(ib,n):r;return!1===e?i.set(iO,"1"):i}let iB=new Set;function i$(t){iB.add(t)}class iF extends im{constructor(){super(),i$("SentryPropagator"),this._urlMatchesTargetsMap=new eX(100)}inject(t,e,n){if(ih(t))return;let r=rw.getSpan(t);if(!function(t,e,n){if("string"!=typeof t||!e)return!0;let r=n?.get(t);if(void 0!==r)return r;let i=z(t,e);return n?.set(t,i),i}(r&&function(t){let e=tK(t).data,n=e[r7]||e[ii];if("string"==typeof n)return n;let r=t.spanContext().traceState?.get(iR);if(r)return r}(r),tS()?.getOptions()?.tracePropagationTargets,this._urlMatchesTargetsMap))return;let i=function(t){try{let e=t[iT];return Array.isArray(e)?e.join(","):e}catch{return}}(e),o=rZ.getBaggage(t)||rZ.createBaggage({}),{dynamicSamplingContext:s,traceId:a,spanId:u,sampled:c}=iG(t);if(i){let t=tB(i);t&&Object.entries(t).forEach(([t,e])=>{o=o.setEntry(t,{value:e})})}s&&(o=Object.entries(s).reduce((t,[e,n])=>n?t.setEntry(`${tM}${e}`,{value:n}):t,o)),a&&a!==rl&&n.set(e,iS,tV(a,u,c)),super.inject(rZ.setBaggage(t,o),e,n)}extract(t,e,n){let r=n.get(e,iS),i=n.get(e,iT);return iX(iV(t,{sentryTrace:r?Array.isArray(r)?r[0]:r:void 0,baggage:i}))}fields(){return[iS,iT]}}function iG(t,e={}){let n=rw.getSpan(t);if(n?.spanContext().isRemote){let t=n.spanContext();return{dynamicSamplingContext:em(n),traceId:t.traceId,spanId:void 0,sampled:iN(t)}}if(n){let t=n.spanContext();return{dynamicSamplingContext:em(n),traceId:t.traceId,spanId:t.spanId,sampled:iN(t)}}let r=e.scope||iC(t)?.scope||tm(),i=e.client||tS(),o=r.getPropagationContext();return{dynamicSamplingContext:i?eg(i,r):void 0,traceId:o.traceId,spanId:o.propagationSpanId,sampled:o.sampled}}function iV(t,{sentryTrace:e,baggage:n}){let{traceId:r,parentSpanId:i,sampled:o,dsc:s}=function(t,e){let n=function(t){let e;if(!t)return;let n=t.match(tG);if(n)return"1"===n[3]?e=!0:"0"===n[3]&&(e=!1),{traceId:n[1],parentSampled:e,parentSpanId:n[2]}}(t),r=tj(e);if(!n?.traceId)return{traceId:Z(),sampleRand:Math.random()};let i=function(t,e){let n=tF(e?.sample_rand);if(void 0!==n)return n;let r=tF(e?.sample_rate);return r&&t?.parentSampled!==void 0?t.parentSampled?Math.random()*r:r+Math.random()*(1-r):Math.random()}(n,r);r&&(r.sample_rand=i.toString());let{traceId:o,parentSpanId:s,parentSampled:a}=n;return{traceId:o,parentSpanId:s,sampled:a,dsc:r||{},sampleRand:i}}(e,n);if(!i)return t;let a=function({spanId:t,traceId:e,sampled:n,dsc:r}){let i=iU({dsc:r,sampled:n});return{traceId:e,spanId:t,isRemote:!0,traceFlags:n?nU.SAMPLED:nU.NONE,traceState:i}}({traceId:r,spanId:i,sampled:o,dsc:s});return rw.setSpanContext(t,a)}function iX(t){let e=iC(t);return iw(t,{scope:e?e.scope:tm().clone(),isolationScope:e?e.isolationScope:ty()})}function iz(t,e){let n=iK(),{name:r,parentSpan:i}=t;return iQ(i)(()=>{let i=iZ(t.scope,t.forceTransaction),o=t.onlyIfParent&&!rw.getSpan(i)?id(i):i,s=iq(t);return n.startActiveSpan(r,s,o,t=>io(()=>e(t),()=>{void 0===tK(t).status&&t.setStatus({code:nF.ERROR})},()=>t.end()))})}function iH(t,e){let n=iK(),{name:r,parentSpan:i}=t;return iQ(i)(()=>{let i=iZ(t.scope,t.forceTransaction),o=t.onlyIfParent&&!rw.getSpan(i)?id(i):i,s=iq(t);return n.startActiveSpan(r,s,o,t=>io(()=>e(t,()=>t.end()),()=>{void 0===tK(t).status&&t.setStatus({code:nF.ERROR})}))})}function iW(t){let e=iK(),{name:n,parentSpan:r}=t;return iQ(r)(()=>{let r=iZ(t.scope,t.forceTransaction),i=t.onlyIfParent&&!rw.getSpan(r)?id(r):r,o=iq(t);return e.startSpan(n,o,i)})}function iY(t,e){let n=t?rw.setSpan(ru.active(),t):rw.deleteSpan(ru.active());return ru.with(n,()=>e(tm()))}function iK(){let t=tS();return t?.tracer||rw.getTracer("@sentry/opentelemetry",c)}function iq(t){var e;let{startTime:n,attributes:r,kind:i,op:o,links:s}=t,a="number"==typeof n?(e=n)<0x2540be3ff?1e3*e:e:n;return{attributes:o?{[tR]:o,...r}:r,kind:i,links:s,startTime:a}}function iZ(t,e){let n=function(t){if(t){let e=t[ix];if(e)return e}return ru.active()}(t),r=rw.getSpan(n);if(!r||!e)return n;let i=rw.deleteSpan(n),{spanId:o,traceId:s}=r.spanContext(),a=iN(r.spanContext()),u=iU({dsc:em(t1(r)),sampled:a}),c={traceId:s,spanId:o,isRemote:!0,traceFlags:a?nU.SAMPLED:nU.NONE,traceState:u};return rw.setSpanContext(i,c)}function iJ(t,e){let n=iX(iV(ru.active(),t));return ru.with(n,e)}function iQ(t){return void 0!==t?e=>iY(t,e):t=>t()}function i0(t){let e=id(ru.active());return ru.with(e,t)}function i1({span:t,scope:e,client:n}={}){let r=(e&&e[ix])??ru.active();if(t){let{scope:e}=tk(t);r=e&&e[ix]||rw.setSpan(ru.active(),t)}let{traceId:i,spanId:o,sampled:s,dynamicSamplingContext:a}=iG(r,{scope:e,client:n});return{"sentry-trace":tV(i,o,s),baggage:tU(a)}}function i2(t){return!0===t.attributes[iy]?void 0:iv(t)}function i4(t,e){let n=t.get(e.id);return n?.span?n:n&&!n.span?(n.span=e.span,n.parentNode=e.parentNode,n):(t.set(e.id,e),e)}let i5={1:"cancelled",2:"unknown_error",3:"invalid_argument",4:"deadline_exceeded",5:"not_found",6:"already_exists",7:"permission_denied",8:"resource_exhausted",9:"failed_precondition",10:"aborted",11:"out_of_range",12:"unimplemented",13:"internal_error",14:"unavailable",15:"data_loss",16:"unauthenticated"},i3=t=>Object.values(i5).includes(t);function i9(t){let e=iE(t)?t.attributes:{},n=t.status?t.status:void 0;if(n){if(n.code===nF.OK)return{code:1};else if(n.code===nF.ERROR){if(void 0===n.message){let t=i8(e);if(t)return t}return n.message&&i3(n.message)?{code:2,message:n.message}:{code:2,message:"unknown_error"}}}let r=i8(e);return r||(n?.code===nF.UNSET?{code:1}:{code:2,message:"unknown_error"})}function i8(t){let e=t[ir]||t[it],n=t["rpc.grpc.status_code"],r="number"==typeof e?e:"string"==typeof e?parseInt(e):void 0;return"number"==typeof r?tx(r):"string"==typeof n?{code:2,message:i5[n]||"unknown_error"}:void 0}class i6{constructor(t){this._finishedSpanBucketSize=t?.timeout||300,this._finishedSpanBuckets=Array(this._finishedSpanBucketSize).fill(void 0),this._lastCleanupTimestampInS=Math.floor(Date.now()/1e3),this._spansToBucketEntry=new WeakMap,this._sentSpans=new Map,this._debouncedFlush=function(t,e,n){let r,i,o,s=n?.maxWait?Math.max(n.maxWait,1):0,a=n?.setTimeoutImpl||setTimeout;function u(){return c(),r=t()}function c(){void 0!==i&&clearTimeout(i),void 0!==o&&clearTimeout(o),i=o=void 0}function l(){return i&&clearTimeout(i),i=a(u,1),s&&void 0===o&&(o=a(u,s)),r}return l.cancel=c,l.flush=function(){return void 0!==i||void 0!==o?u():r},l}(this.flush.bind(this),1,{maxWait:100})}export(t){let e=Math.floor(Date.now()/1e3);if(this._lastCleanupTimestampInS!==e){let t=0;this._finishedSpanBuckets.forEach((n,r)=>{n&&n.timestampInS<=e-this._finishedSpanBucketSize&&(t+=n.spans.size,this._finishedSpanBuckets[r]=void 0)}),this._lastCleanupTimestampInS=e}let n=e%this._finishedSpanBucketSize,r=this._finishedSpanBuckets[n]||{timestampInS:e,spans:new Set};this._finishedSpanBuckets[n]=r,r.spans.add(t),this._spansToBucketEntry.set(t,r);let i=i2(t);(!i||this._sentSpans.has(i))&&this._debouncedFlush()}flush(){let t=this._finishedSpanBuckets.flatMap(t=>t?Array.from(t.spans):[]);this._flushSentSpanCache();let e=this._maybeSend(t),n=e.size;t.length;let r=Date.now()+3e5;for(let t of e){this._sentSpans.set(t.spanContext().spanId,r);let e=this._spansToBucketEntry.get(t);e&&e.spans.delete(t)}this._debouncedFlush.cancel()}clear(){this._finishedSpanBuckets=this._finishedSpanBuckets.fill(void 0),this._sentSpans.clear(),this._debouncedFlush.cancel()}_maybeSend(t){let e=function(t){let e=new Map;for(let n of t)!function(t,e){let n=e.spanContext().spanId,r=i2(e);if(!r)return i4(t,{id:n,span:e,children:[]});let i=function(t,e){let n=t.get(e);return n||i4(t,{id:e,children:[]})}(t,r),o=i4(t,{id:n,span:e,parentNode:i,children:[]});i.children.push(o)}(e,n);return Array.from(e,function([t,e]){return e})}(t),n=new Set;for(let t of this._getCompletedRootNodes(e)){let e=t.span;n.add(e);let r=function(t){let{op:e,description:n,data:r,origin:i="manual",source:o}=i7(t),s=tk(t),a=t.attributes[tO],u={[tb]:o,[tO]:a,[tR]:e,[tA]:i,...r,...ot(t.attributes)},{links:c}=t,{traceId:l,spanId:p}=t.spanContext(),f={parent_span_id:iv(t),span_id:p,trace_id:l,data:u,origin:i,op:e,status:tZ(i9(t)),links:tH(c)},d=u[ir];return{contexts:{trace:f,otel:{resource:t.resource.attributes},..."number"==typeof d?{response:{status_code:d}}:void 0},spans:[],start_timestamp:tW(t.startTime),timestamp:tW(t.endTime),transaction:n,type:"transaction",sdkProcessingMetadata:{capturedSpanScope:s.scope,capturedSpanIsolationScope:s.isolationScope,sampleRate:a,dynamicSamplingContext:em(t)},...o&&{transaction_info:{source:o}}}}(e);if(t.parentNode&&this._sentSpans.has(t.parentNode.id)){let t=r.contexts?.trace?.data;t&&(t["sentry.parent_span_already_sent"]=!0)}let i=r.spans||[];for(let e of t.children)!function t(e,n,r){let i=e.span;if(i&&r.add(i),!i)return void e.children.forEach(e=>{t(e,n,r)});let o=i.spanContext().spanId,s=i.spanContext().traceId,a=iv(i),{attributes:u,startTime:c,endTime:l,links:p}=i,{op:f,description:d,data:h,origin:_="manual"}=i7(i),g={[tA]:_,[tR]:f,...ot(u),...h},m=i9(i),y={span_id:o,trace_id:s,data:g,description:d,parent_span_id:a,start_timestamp:tW(c),timestamp:tW(l)||void 0,status:tZ(m),op:f,origin:_,measurements:eQ(i.events),links:tH(p)};n.push(y),e.children.forEach(e=>{t(e,n,r)})}(e,i,n);r.spans=i.length>1e3?i.sort((t,e)=>t.start_timestamp-e.start_timestamp).slice(0,1e3):i;let o=eQ(e.events);o&&(r.measurements=o),tm().captureEvent(r,void 0)}return n}_flushSentSpanCache(){let t=Date.now();for(let[e,n]of this._sentSpans.entries())n<=t&&this._sentSpans.delete(e)}_nodeIsCompletedRootNodeOrHasSentParent(t){return!!t.span&&(!t.parentNode||this._sentSpans.has(t.parentNode.id))}_getCompletedRootNodes(t){return t.filter(t=>this._nodeIsCompletedRootNodeOrHasSentParent(t))}}function i7(t){let{op:e,source:n,origin:r}=function(t){let e=t.attributes,n=e[tA];return{origin:n,op:e[tR],source:e[tb]}}(t),{op:i,description:o,source:s,data:a}=iM(t);return{op:e||i,description:o,source:n||s,origin:r,data:{...a,...function(t){let e=t.attributes,n={};t.kind!==nB.INTERNAL&&(n["otel.kind"]=nB[t.kind]);let r=e[it];r&&(n[ir]=r);let i=function(t){if(!iE(t))return{};let e=t.attributes[ii]||t.attributes[r7],n={url:e,"http.method":t.attributes[ie]||t.attributes[r6]};!n["http.method"]&&n.url&&(n["http.method"]="GET");try{if("string"==typeof e){let t=t6(e);n.url=et(t),t.search&&(n["http.query"]=t.search),t.hash&&(n["http.fragment"]=t.hash)}}catch{}return n}(t);return i.url&&(n.url=i.url),i["http.query"]&&(n["http.query"]=i["http.query"].slice(1)),i["http.fragment"]&&(n["http.fragment"]=i["http.fragment"].slice(1)),n}(t)}}}function ot(t){let e={...t};return delete e[tO],delete e[iy],delete e[tP],e}class oe{constructor(t){i$("SentrySpanProcessor"),this._exporter=new i6(t)}async forceFlush(){this._exporter.flush()}async shutdown(){this._exporter.clear()}onStart(t,e){let n=rw.getSpan(e),r=iC(e);n&&!n.spanContext().isRemote&&t0(n,t),n?.spanContext().isRemote&&t.setAttribute(iy,!0),e===nY&&(r={scope:tc(),isolationScope:tl()}),r&&tN(t,r.scope,r.isolationScope),eq(t);let i=tS();i?.emit("spanStart",t)}onEnd(t){eZ(t);let e=tS();e?.emit("spanEnd",t),this._exporter.export(t)}}class on{constructor(t){this._client=t,i$("SentrySampler")}shouldSample(t,e,n,r,i,o){let s=this._client.getOptions(),a=function(t){let e=rw.getSpan(t);return e&&rb(e.spanContext())?e:void 0}(t),u=a?.spanContext();if(!ed(s))return or({decision:void 0,context:t,spanAttributes:i});let c=i[r6]||i[ie];if(r===nB.CLIENT&&c&&(!a||u?.isRemote))return or({decision:void 0,context:t,spanAttributes:i});let l=a?function(t,e,n){let r=t.spanContext();if(rb(r)&&r.traceId===e)return r.isRemote?iN(t.spanContext()):iN(r)}(a,e,0):void 0;if(!(!a||u?.isRemote))return or({decision:l?nV.RECORD_AND_SAMPLED:nV.NOT_RECORD,context:t,spanAttributes:i});let{description:p,data:f,op:d}=ik(n,i,r),h={...f,...i};d&&(h[tR]=d);let _={decision:!0};if(this._client.emit("beforeSampling",{spanAttributes:h,spanName:p,parentSampled:l,parentContext:u},_),!_.decision)return or({decision:void 0,context:t,spanAttributes:i});let{isolationScope:g}=iC(t)??{},m=u?.traceState?u.traceState.get(ib):void 0,y=m?tj(m):void 0,v=tF(y?.sample_rand)??Math.random(),[E,S,T]=eJ(s,{name:p,attributes:h,normalizedRequest:g?.getScopeData().sdkProcessingMetadata.normalizedRequest,parentSampled:l,parentSampleRate:tF(y?.sample_rate)},v),b=`${c}`.toUpperCase();return"OPTIONS"===b||"HEAD"===b?or({decision:nV.NOT_RECORD,context:t,spanAttributes:i,sampleRand:v,downstreamTraceSampleRate:0}):(E||void 0!==l||this._client.recordDroppedEvent("sample_rate","transaction"),{...or({decision:E?nV.RECORD_AND_SAMPLED:nV.NOT_RECORD,context:t,spanAttributes:i,sampleRand:v,downstreamTraceSampleRate:T?S:void 0}),attributes:{[tO]:T?S:void 0}})}toString(){return"SentrySampler"}}function or({decision:t,context:e,spanAttributes:n,sampleRand:r,downstreamTraceSampleRate:i}){let o=function(t,e){let n=rw.getSpan(t),r=n?.spanContext(),i=r?.traceState||new il,o=e[r7]||e[ii];return o&&"string"==typeof o&&(i=i.set(iR,o)),i}(e,n);return(void 0!==i&&(o=o.set("sentry.sample_rate",`${i}`)),void 0!==r&&(o=o.set("sentry.sample_rand",`${r}`)),void 0==t)?{decision:nV.NOT_RECORD,traceState:o}:t===nV.NOT_RECORD?{decision:t,traceState:o.set(iO,"1")}:{decision:t,traceState:o}}var oi=n(356).Buffer;void 0===globalThis.performance&&(globalThis.performance={timeOrigin:0,now:()=>Date.now()});class oo extends eF{constructor(t){t9(t,"vercel-edge"),t._metadata=t._metadata||{},super({...t,platform:"javascript",runtime:{name:"vercel-edge"},serverName:t.serverName||process.env.SENTRY_NAME})}async flush(t){let e=this.traceProvider,n=e?.activeSpanProcessor;return n&&await n.forceFlush(),this.getOptions().sendClientReports&&this._flushOutcomes(),super.flush(t)}}var os=nk("OpenTelemetry SDK Context Key SUPPRESS_TRACING");function oa(t){return!0===t.getValue(os)}var ou="baggage",oc=globalThis&&globalThis.__read||function(t,e){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var r,i,o=n.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(r=o.next()).done;)s.push(r.value)}catch(t){i={error:t}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}return s},ol=function(){function t(){}return t.prototype.inject=function(t,e,n){var r=rZ.getBaggage(t);if(!(!r||oa(t))){var i=r.getAllEntries().map(function(t){var e=oc(t,2),n=e[0],r=e[1],i=encodeURIComponent(n)+"="+encodeURIComponent(r.value);return void 0!==r.metadata&&(i+=";"+r.metadata.toString()),i}).filter(function(t){return t.length<=4096}).slice(0,180).reduce(function(t,e){var n=""+t+(""!==t?",":"")+e;return n.length>8192?t:n},"");i.length>0&&n.set(e,ou,i)}},t.prototype.extract=function(t,e,n){var r=n.get(e,ou),i=Array.isArray(r)?r.join(","):r;if(!i)return t;var o={};return 0===i.length||(i.split(",").forEach(function(t){var e=function(t){var e,n=t.split(";");if(!(n.length<=0)){var r=n.shift();if(r){var i=r.indexOf("=");if(!(i<=0)){var o=decodeURIComponent(r.substring(0,i).trim()),s=decodeURIComponent(r.substring(i+1).trim());return n.length>0&&(e=r$(n.join(";"))),{key:o,value:s,metadata:e}}}}}(t);if(e){var n={value:e.value};e.metadata&&(n.metadata=e.metadata),o[e.key]=n}}),0===Object.entries(o).length)?t:rZ.setBaggage(t,rZ.createBaggage(o))},t.prototype.fields=function(){return[ou]},t}(),op=globalThis&&globalThis.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,n=e&&t[e],r=0;if(n)return n.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},of=globalThis&&globalThis.__read||function(t,e){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var r,i,o=n.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(r=o.next()).done;)s.push(r.value)}catch(t){i={error:t}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}return s};function od(t){var e,n,r={};if("object"!=typeof t||null==t)return r;try{for(var i=op(Object.entries(t)),o=i.next();!o.done;o=i.next()){var s,a=of(o.value,2),u=a[0],c=a[1];if(s=u,"string"!=typeof s||!(s.length>0)){rN.warn("Invalid attribute key: "+u);continue}if(!oh(c)){rN.warn("Invalid attribute value set for key: "+u);continue}Array.isArray(c)?r[u]=c.slice():r[u]=c}}catch(t){e={error:t}}finally{try{o&&!o.done&&(n=i.return)&&n.call(i)}finally{if(e)throw e.error}}return r}function oh(t){return null==t||(Array.isArray(t)?function(t){try{for(var e,n,r,i=op(t),o=i.next();!o.done;o=i.next()){var s=o.value;if(null!=s){if(!r){if(o_(s)){r=typeof s;continue}return!1}if(typeof s!==r)return!1}}}catch(t){e={error:t}}finally{try{o&&!o.done&&(n=i.return)&&n.call(i)}finally{if(e)throw e.error}}return!0}(t):o_(t))}function o_(t){switch(typeof t){case"number":case"boolean":case"string":return!0}return!1}var og=function(t){var e;rN.error("string"==typeof(e=t)?e:JSON.stringify(function(t){for(var e={},n=t;null!==n;)Object.getOwnPropertyNames(n).forEach(function(t){if(!e[t]){var r=n[t];r&&(e[t]=String(r))}}),n=Object.getPrototypeOf(n);return e}(e)))};function om(t){try{og(t)}catch(t){}}!function(t){t.AlwaysOff="always_off",t.AlwaysOn="always_on",t.ParentBasedAlwaysOff="parentbased_always_off",t.ParentBasedAlwaysOn="parentbased_always_on",t.ParentBasedTraceIdRatio="parentbased_traceidratio",t.TraceIdRatio="traceidratio"}(nX||(nX={}));var oy=["OTEL_SDK_DISABLED"],ov=["OTEL_BSP_EXPORT_TIMEOUT","OTEL_BSP_MAX_EXPORT_BATCH_SIZE","OTEL_BSP_MAX_QUEUE_SIZE","OTEL_BSP_SCHEDULE_DELAY","OTEL_BLRP_EXPORT_TIMEOUT","OTEL_BLRP_MAX_EXPORT_BATCH_SIZE","OTEL_BLRP_MAX_QUEUE_SIZE","OTEL_BLRP_SCHEDULE_DELAY","OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT","OTEL_ATTRIBUTE_COUNT_LIMIT","OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT","OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT","OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT","OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT","OTEL_SPAN_EVENT_COUNT_LIMIT","OTEL_SPAN_LINK_COUNT_LIMIT","OTEL_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT","OTEL_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT","OTEL_EXPORTER_OTLP_TIMEOUT","OTEL_EXPORTER_OTLP_TRACES_TIMEOUT","OTEL_EXPORTER_OTLP_METRICS_TIMEOUT","OTEL_EXPORTER_OTLP_LOGS_TIMEOUT","OTEL_EXPORTER_JAEGER_AGENT_PORT"],oE=["OTEL_NO_PATCH_MODULES","OTEL_PROPAGATORS","OTEL_SEMCONV_STABILITY_OPT_IN"],oS=1/0,oT={OTEL_SDK_DISABLED:!1,CONTAINER_NAME:"",ECS_CONTAINER_METADATA_URI_V4:"",ECS_CONTAINER_METADATA_URI:"",HOSTNAME:"",KUBERNETES_SERVICE_HOST:"",NAMESPACE:"",OTEL_BSP_EXPORT_TIMEOUT:3e4,OTEL_BSP_MAX_EXPORT_BATCH_SIZE:512,OTEL_BSP_MAX_QUEUE_SIZE:2048,OTEL_BSP_SCHEDULE_DELAY:5e3,OTEL_BLRP_EXPORT_TIMEOUT:3e4,OTEL_BLRP_MAX_EXPORT_BATCH_SIZE:512,OTEL_BLRP_MAX_QUEUE_SIZE:2048,OTEL_BLRP_SCHEDULE_DELAY:5e3,OTEL_EXPORTER_JAEGER_AGENT_HOST:"",OTEL_EXPORTER_JAEGER_AGENT_PORT:6832,OTEL_EXPORTER_JAEGER_ENDPOINT:"",OTEL_EXPORTER_JAEGER_PASSWORD:"",OTEL_EXPORTER_JAEGER_USER:"",OTEL_EXPORTER_OTLP_ENDPOINT:"",OTEL_EXPORTER_OTLP_TRACES_ENDPOINT:"",OTEL_EXPORTER_OTLP_METRICS_ENDPOINT:"",OTEL_EXPORTER_OTLP_LOGS_ENDPOINT:"",OTEL_EXPORTER_OTLP_HEADERS:"",OTEL_EXPORTER_OTLP_TRACES_HEADERS:"",OTEL_EXPORTER_OTLP_METRICS_HEADERS:"",OTEL_EXPORTER_OTLP_LOGS_HEADERS:"",OTEL_EXPORTER_OTLP_TIMEOUT:1e4,OTEL_EXPORTER_OTLP_TRACES_TIMEOUT:1e4,OTEL_EXPORTER_OTLP_METRICS_TIMEOUT:1e4,OTEL_EXPORTER_OTLP_LOGS_TIMEOUT:1e4,OTEL_EXPORTER_ZIPKIN_ENDPOINT:"http://localhost:9411/api/v2/spans",OTEL_LOG_LEVEL:nj.INFO,OTEL_NO_PATCH_MODULES:[],OTEL_PROPAGATORS:["tracecontext","baggage"],OTEL_RESOURCE_ATTRIBUTES:"",OTEL_SERVICE_NAME:"",OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT:oS,OTEL_ATTRIBUTE_COUNT_LIMIT:128,OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT:oS,OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT:128,OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT:oS,OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT:128,OTEL_SPAN_EVENT_COUNT_LIMIT:128,OTEL_SPAN_LINK_COUNT_LIMIT:128,OTEL_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT:128,OTEL_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT:128,OTEL_TRACES_EXPORTER:"",OTEL_TRACES_SAMPLER:nX.ParentBasedAlwaysOn,OTEL_TRACES_SAMPLER_ARG:"",OTEL_LOGS_EXPORTER:"",OTEL_EXPORTER_OTLP_INSECURE:"",OTEL_EXPORTER_OTLP_TRACES_INSECURE:"",OTEL_EXPORTER_OTLP_METRICS_INSECURE:"",OTEL_EXPORTER_OTLP_LOGS_INSECURE:"",OTEL_EXPORTER_OTLP_CERTIFICATE:"",OTEL_EXPORTER_OTLP_TRACES_CERTIFICATE:"",OTEL_EXPORTER_OTLP_METRICS_CERTIFICATE:"",OTEL_EXPORTER_OTLP_LOGS_CERTIFICATE:"",OTEL_EXPORTER_OTLP_COMPRESSION:"",OTEL_EXPORTER_OTLP_TRACES_COMPRESSION:"",OTEL_EXPORTER_OTLP_METRICS_COMPRESSION:"",OTEL_EXPORTER_OTLP_LOGS_COMPRESSION:"",OTEL_EXPORTER_OTLP_CLIENT_KEY:"",OTEL_EXPORTER_OTLP_TRACES_CLIENT_KEY:"",OTEL_EXPORTER_OTLP_METRICS_CLIENT_KEY:"",OTEL_EXPORTER_OTLP_LOGS_CLIENT_KEY:"",OTEL_EXPORTER_OTLP_CLIENT_CERTIFICATE:"",OTEL_EXPORTER_OTLP_TRACES_CLIENT_CERTIFICATE:"",OTEL_EXPORTER_OTLP_METRICS_CLIENT_CERTIFICATE:"",OTEL_EXPORTER_OTLP_LOGS_CLIENT_CERTIFICATE:"",OTEL_EXPORTER_OTLP_PROTOCOL:"http/protobuf",OTEL_EXPORTER_OTLP_TRACES_PROTOCOL:"http/protobuf",OTEL_EXPORTER_OTLP_METRICS_PROTOCOL:"http/protobuf",OTEL_EXPORTER_OTLP_LOGS_PROTOCOL:"http/protobuf",OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE:"cumulative",OTEL_SEMCONV_STABILITY_OPT_IN:[]},ob={ALL:nj.ALL,VERBOSE:nj.VERBOSE,DEBUG:nj.DEBUG,INFO:nj.INFO,WARN:nj.WARN,ERROR:nj.ERROR,NONE:nj.NONE};function oO(t){var e={};for(var n in oT)if("OTEL_LOG_LEVEL"===n)!function(t,e,n){var r=n[t];if("string"==typeof r){var i=ob[r.toUpperCase()];null!=i&&(e[t]=i)}}(n,e,t);else if(oy.indexOf(n)>-1){if(void 0!==t[n]){var r=String(t[n]);e[n]="true"===r.toLowerCase()}}else if(ov.indexOf(n)>-1)!function(t,e,n,r,i){if(void 0===r&&(r=-1/0),void 0===i&&(i=1/0),void 0!==n[t]){var o=Number(n[t]);isNaN(o)||(o<r?e[t]=r:o>i?e[t]=i:e[t]=o)}}(n,e,t);else if(oE.indexOf(n)>-1)!function(t,e,n,r){void 0===r&&(r=",");var i=n[t];"string"==typeof i&&(e[t]=i.split(r).map(function(t){return t.trim()}))}(n,e,t);else{var i=t[n];null!=i&&(e[n]=String(i))}return e}function oR(){return Object.assign({},oT,oO(process.env))}var oA={timeOrigin:0,now:()=>Date.now()};function oP(t){return[Math.trunc(t/1e3),Math.round(t%1e3*1e6)]}function oL(){return oA.timeOrigin}function oI(t){return Array.isArray(t)&&2===t.length&&"number"==typeof t[0]&&"number"==typeof t[1]}function ox(t){return oI(t)||"number"==typeof t||t instanceof Date}function oC(t,e){var n=[t[0]+e[0],t[1]+e[1]];return n[1]>=1e9&&(n[1]-=1e9,n[0]+=1),n}!function(t){t[t.SUCCESS=0]="SUCCESS",t[t.FAILED=1]="FAILED"}(nz||(nz={}));var ow=globalThis&&globalThis.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,n=e&&t[e],r=0;if(n)return n.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},oN=function(){function t(t){var e;void 0===t&&(t={}),this._propagators=null!=(e=t.propagators)?e:[],this._fields=Array.from(new Set(this._propagators.map(function(t){return"function"==typeof t.fields?t.fields():[]}).reduce(function(t,e){return t.concat(e)},[])))}return t.prototype.inject=function(t,e,n){var r,i;try{for(var o=ow(this._propagators),s=o.next();!s.done;s=o.next()){var a=s.value;try{a.inject(t,e,n)}catch(t){rN.warn("Failed to inject with "+a.constructor.name+". Err: "+t.message)}}}catch(t){r={error:t}}finally{try{s&&!s.done&&(i=o.return)&&i.call(o)}finally{if(r)throw r.error}}},t.prototype.extract=function(t,e,n){return this._propagators.reduce(function(t,r){try{return r.extract(t,e,n)}catch(t){rN.warn("Failed to extract with "+r.constructor.name+". Err: "+t.message)}return t},t)},t.prototype.fields=function(){return this._fields.slice()},t}(),ok="[_0-9a-z-*/]",oM=RegExp("^(?:[a-z]"+ok+"{0,255}|"+("[a-z0-9]"+ok+"{0,240}@[a-z]")+ok+"{0,13})$"),oD=/^[ -~]{0,255}[!-~]$/,oj=/,|=/,oU=function(){function t(t){this._internalState=new Map,t&&this._parse(t)}return t.prototype.set=function(t,e){var n=this._clone();return n._internalState.has(t)&&n._internalState.delete(t),n._internalState.set(t,e),n},t.prototype.unset=function(t){var e=this._clone();return e._internalState.delete(t),e},t.prototype.get=function(t){return this._internalState.get(t)},t.prototype.serialize=function(){var t=this;return this._keys().reduce(function(e,n){return e.push(n+"="+t.get(n)),e},[]).join(",")},t.prototype._parse=function(t){!(t.length>512)&&(this._internalState=t.split(",").reverse().reduce(function(t,e){var n=e.trim(),r=n.indexOf("=");if(-1!==r){var i=n.slice(0,r),o=n.slice(r+1,e.length);oM.test(i)&&oD.test(o)&&!oj.test(o)&&t.set(i,o)}return t},new Map),this._internalState.size>32&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,32))))},t.prototype._keys=function(){return Array.from(this._internalState.keys()).reverse()},t.prototype._clone=function(){var e=new t;return e._internalState=new Map(this._internalState),e},t}(),oB="traceparent",o$="tracestate",oF=RegExp("^\\s?((?!ff)[\\da-f]{2})-((?![0]{32})[\\da-f]{32})-((?![0]{16})[\\da-f]{16})-([\\da-f]{2})(-.*)?\\s?$"),oG=function(){function t(){}return t.prototype.inject=function(t,e,n){var r=rw.getSpanContext(t);if(!(!r||oa(t))&&rb(r)){var i="00-"+r.traceId+"-"+r.spanId+"-0"+Number(r.traceFlags||nU.NONE).toString(16);n.set(e,oB,i),r.traceState&&n.set(e,o$,r.traceState.serialize())}},t.prototype.extract=function(t,e,n){var r,i=n.get(e,oB);if(!i)return t;var o=Array.isArray(i)?i[0]:i;if("string"!=typeof o)return t;var s=(r=oF.exec(o))&&("00"!==r[1]||!r[5])?{traceId:r[2],spanId:r[3],traceFlags:parseInt(r[4],16)}:null;if(!s)return t;s.isRemote=!0;var a=n.get(e,o$);if(a){var u=Array.isArray(a)?a.join(","):a;s.traceState=new oU("string"==typeof u?u:void 0)}return rw.setSpanContext(t,s)},t.prototype.fields=function(){return[oB,o$]},t}(),oV=Function.prototype.toString,oX=oV.call(Object),oz=(nM=Object.getPrototypeOf,nD=Object,function(t){return nM(nD(t))}),oH=Object.prototype,oW=oH.hasOwnProperty,oY=Symbol?Symbol.toStringTag:void 0,oK=oH.toString;function oq(t){if(null==(e=t)||"object"!=typeof e||"[object Object]"!==(null==(n=t)?void 0===n?"[object Undefined]":"[object Null]":oY&&oY in Object(n)?function(t){var e=oW.call(t,oY),n=t[oY],r=!1;try{t[oY]=void 0,r=!0}catch(t){}var i=oK.call(t);return r&&(e?t[oY]=n:delete t[oY]),i}(n):(r=n,oK.call(r))))return!1;var e,n,r,i=oz(t);if(null===i)return!0;var o=oW.call(i,"constructor")&&i.constructor;return"function"==typeof o&&o instanceof o&&oV.call(o)===oX}function oZ(t){return oQ(t)?t.slice():t}function oJ(t,e,n){for(var r=n.get(t[e])||[],i=0,o=r.length;i<o;i++){var s=r[i];if(s.key===e&&s.obj===t)return!0}return!1}function oQ(t){return Array.isArray(t)}function o0(t){return"function"==typeof t}function o1(t){return!o2(t)&&!oQ(t)&&!o0(t)&&"object"==typeof t}function o2(t){return"string"==typeof t||"number"==typeof t||"boolean"==typeof t||void 0===t||t instanceof Date||t instanceof RegExp||null===t}var o4=function(){function t(){var t=this;this._promise=new Promise(function(e,n){t._resolve=e,t._reject=n})}return Object.defineProperty(t.prototype,"promise",{get:function(){return this._promise},enumerable:!1,configurable:!0}),t.prototype.resolve=function(t){this._resolve(t)},t.prototype.reject=function(t){this._reject(t)},t}(),o5=globalThis&&globalThis.__read||function(t,e){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var r,i,o=n.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(r=o.next()).done;)s.push(r.value)}catch(t){i={error:t}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}return s},o3=globalThis&&globalThis.__spreadArray||function(t,e,n){if(n||2==arguments.length)for(var r,i=0,o=e.length;i<o;i++)!r&&i in e||(r||(r=Array.prototype.slice.call(e,0,i)),r[i]=e[i]);return t.concat(r||Array.prototype.slice.call(e))},o9=function(){function t(t,e){this._callback=t,this._that=e,this._isCalled=!1,this._deferred=new o4}return Object.defineProperty(t.prototype,"isCalled",{get:function(){return this._isCalled},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"promise",{get:function(){return this._deferred.promise},enumerable:!1,configurable:!0}),t.prototype.call=function(){for(var t,e=this,n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];if(!this._isCalled){this._isCalled=!0;try{Promise.resolve((t=this._callback).call.apply(t,o3([this._that],o5(n),!1))).then(function(t){return e._deferred.resolve(t)},function(t){return e._deferred.reject(t)})}catch(t){this._deferred.reject(t)}}return this._deferred.promise},t}(),o8=globalThis&&globalThis.__assign||function(){return(o8=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var i in e=arguments[n])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)},o6=globalThis&&globalThis.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,n=e&&t[e],r=0;if(n)return n.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},o7=globalThis&&globalThis.__read||function(t,e){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var r,i,o=n.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(r=o.next()).done;)s.push(r.value)}catch(t){i={error:t}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}return s},st=globalThis&&globalThis.__spreadArray||function(t,e,n){if(n||2==arguments.length)for(var r,i=0,o=e.length;i<o;i++)!r&&i in e||(r||(r=Array.prototype.slice.call(e,0,i)),r[i]=e[i]);return t.concat(r||Array.prototype.slice.call(e))},se=function(){function t(t,e,n,r,i,o,s,a,u,c){void 0===s&&(s=[]),this.attributes={},this.links=[],this.events=[],this._droppedAttributesCount=0,this._droppedEventsCount=0,this._droppedLinksCount=0,this.status={code:nF.UNSET},this.endTime=[0,0],this._ended=!1,this._duration=[-1,-1],this.name=n,this._spanContext=r,this.parentSpanId=o,this.kind=i,this.links=s;var l=Date.now();this._performanceStartTime=oA.now(),this._performanceOffset=l-(this._performanceStartTime+oL()),this._startTimeProvided=null!=a,this.startTime=this._getTime(null!=a?a:l),this.resource=t.resource,this.instrumentationLibrary=t.instrumentationLibrary,this._spanLimits=t.getSpanLimits(),this._attributeValueLengthLimit=this._spanLimits.attributeValueLengthLimit||0,null!=c&&this.setAttributes(c),this._spanProcessor=t.getActiveSpanProcessor(),this._spanProcessor.onStart(this,e)}return t.prototype.spanContext=function(){return this._spanContext},t.prototype.setAttribute=function(t,e){return null==e||this._isSpanEnded()||(0===t.length?rN.warn("Invalid attribute key: "+t):oh(e)?Object.keys(this.attributes).length>=this._spanLimits.attributeCountLimit&&!Object.prototype.hasOwnProperty.call(this.attributes,t)?this._droppedAttributesCount++:this.attributes[t]=this._truncateToSize(e):rN.warn("Invalid attribute value set for key: "+t)),this},t.prototype.setAttributes=function(t){var e,n;try{for(var r=o6(Object.entries(t)),i=r.next();!i.done;i=r.next()){var o=o7(i.value,2),s=o[0],a=o[1];this.setAttribute(s,a)}}catch(t){e={error:t}}finally{try{i&&!i.done&&(n=r.return)&&n.call(r)}finally{if(e)throw e.error}}return this},t.prototype.addEvent=function(t,e,n){if(this._isSpanEnded())return this;if(0===this._spanLimits.eventCountLimit)return rN.warn("No events allowed."),this._droppedEventsCount++,this;this.events.length>=this._spanLimits.eventCountLimit&&(0===this._droppedEventsCount&&rN.debug("Dropping extra events."),this.events.shift(),this._droppedEventsCount++),ox(e)&&(ox(n)||(n=e),e=void 0);var r=od(e);return this.events.push({name:t,attributes:r,time:this._getTime(n),droppedAttributesCount:0}),this},t.prototype.addLink=function(t){return this.links.push(t),this},t.prototype.addLinks=function(t){var e;return(e=this.links).push.apply(e,st([],o7(t),!1)),this},t.prototype.setStatus=function(t){return this._isSpanEnded()||(this.status=o8({},t),null!=this.status.message&&"string"!=typeof t.message&&(rN.warn("Dropping invalid status.message of type '"+typeof t.message+"', expected 'string'"),delete this.status.message)),this},t.prototype.updateName=function(t){return this._isSpanEnded()||(this.name=t),this},t.prototype.end=function(t){var e,n,r,i;if(this._isSpanEnded())return void rN.error(this.name+" "+this._spanContext.traceId+"-"+this._spanContext.spanId+" - You can only call end() on a span once.");this._ended=!0,this.endTime=this._getTime(t),this._duration=(e=this.startTime,r=(n=this.endTime)[0]-e[0],(i=n[1]-e[1])<0&&(r-=1,i+=1e9),[r,i]),this._duration[0]<0&&(rN.warn("Inconsistent start and end time, startTime > endTime. Setting span duration to 0ms.",this.startTime,this.endTime),this.endTime=this.startTime.slice(),this._duration=[0,0]),this._droppedEventsCount>0&&rN.warn("Dropped "+this._droppedEventsCount+" events because eventCountLimit reached"),this._spanProcessor.onEnd(this)},t.prototype._getTime=function(t){if("number"==typeof t&&t<=oA.now()){var e;return e=t+this._performanceOffset,oC(oP(oL()),oP("number"==typeof e?e:oA.now()))}if("number"==typeof t)return oP(t);if(t instanceof Date)return oP(t.getTime());if(oI(t))return t;if(this._startTimeProvided)return oP(Date.now());var n=oA.now()-this._performanceStartTime;return oC(this.startTime,oP(n))},t.prototype.isRecording=function(){return!1===this._ended},t.prototype.recordException=function(t,e){var n={};"string"==typeof t?n[r8]=t:t&&(t.code?n[r9]=t.code.toString():t.name&&(n[r9]=t.name),t.message&&(n[r8]=t.message),t.stack&&(n["exception.stacktrace"]=t.stack)),n[r9]||n[r8]?this.addEvent("exception",n,e):rN.warn("Failed to record an exception "+t)},Object.defineProperty(t.prototype,"duration",{get:function(){return this._duration},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"ended",{get:function(){return this._ended},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"droppedAttributesCount",{get:function(){return this._droppedAttributesCount},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"droppedEventsCount",{get:function(){return this._droppedEventsCount},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"droppedLinksCount",{get:function(){return this._droppedLinksCount},enumerable:!1,configurable:!0}),t.prototype._isSpanEnded=function(){return this._ended&&rN.warn("Can not execute the operation on ended Span {traceId: "+this._spanContext.traceId+", spanId: "+this._spanContext.spanId+"}"),this._ended},t.prototype._truncateToLimitUtil=function(t,e){return t.length<=e?t:t.substring(0,e)},t.prototype._truncateToSize=function(t){var e=this,n=this._attributeValueLengthLimit;return n<=0?(rN.warn("Attribute value limit must be positive, got "+n),t):"string"==typeof t?this._truncateToLimitUtil(t,n):Array.isArray(t)?t.map(function(t){return"string"==typeof t?e._truncateToLimitUtil(t,n):t}):t},t}();!function(t){t[t.NOT_RECORD=0]="NOT_RECORD",t[t.RECORD=1]="RECORD",t[t.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(nH||(nH={}));var sn=function(){function t(){}return t.prototype.shouldSample=function(){return{decision:nH.NOT_RECORD}},t.prototype.toString=function(){return"AlwaysOffSampler"},t}(),sr=function(){function t(){}return t.prototype.shouldSample=function(){return{decision:nH.RECORD_AND_SAMPLED}},t.prototype.toString=function(){return"AlwaysOnSampler"},t}(),si=function(){function t(t){var e,n,r,i;this._root=t.root,this._root||(om(Error("ParentBasedSampler must have a root sampler configured")),this._root=new sr),this._remoteParentSampled=null!=(e=t.remoteParentSampled)?e:new sr,this._remoteParentNotSampled=null!=(n=t.remoteParentNotSampled)?n:new sn,this._localParentSampled=null!=(r=t.localParentSampled)?r:new sr,this._localParentNotSampled=null!=(i=t.localParentNotSampled)?i:new sn}return t.prototype.shouldSample=function(t,e,n,r,i,o){var s=rw.getSpanContext(t);return s&&rb(s)?s.isRemote?s.traceFlags&nU.SAMPLED?this._remoteParentSampled.shouldSample(t,e,n,r,i,o):this._remoteParentNotSampled.shouldSample(t,e,n,r,i,o):s.traceFlags&nU.SAMPLED?this._localParentSampled.shouldSample(t,e,n,r,i,o):this._localParentNotSampled.shouldSample(t,e,n,r,i,o):this._root.shouldSample(t,e,n,r,i,o)},t.prototype.toString=function(){return"ParentBased{root="+this._root.toString()+", remoteParentSampled="+this._remoteParentSampled.toString()+", remoteParentNotSampled="+this._remoteParentNotSampled.toString()+", localParentSampled="+this._localParentSampled.toString()+", localParentNotSampled="+this._localParentNotSampled.toString()+"}"},t}(),so=function(){function t(t){void 0===t&&(t=0),this._ratio=t,this._ratio=this._normalize(t),this._upperBound=Math.floor(0xffffffff*this._ratio)}return t.prototype.shouldSample=function(t,e){return{decision:rT(e)&&this._accumulate(e)<this._upperBound?nH.RECORD_AND_SAMPLED:nH.NOT_RECORD}},t.prototype.toString=function(){return"TraceIdRatioBased{"+this._ratio+"}"},t.prototype._normalize=function(t){return"number"!=typeof t||isNaN(t)?0:t>=1?1:t<=0?0:t},t.prototype._accumulate=function(t){for(var e=0,n=0;n<t.length/8;n++){var r=8*n;e=(e^parseInt(t.slice(r,r+8),16))>>>0}return e},t}(),ss=nX.AlwaysOn;function sa(){var t=oR();return{sampler:su(t),forceFlushTimeoutMillis:3e4,generalLimits:{attributeValueLengthLimit:t.OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT,attributeCountLimit:t.OTEL_ATTRIBUTE_COUNT_LIMIT},spanLimits:{attributeValueLengthLimit:t.OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT,attributeCountLimit:t.OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT,linkCountLimit:t.OTEL_SPAN_LINK_COUNT_LIMIT,eventCountLimit:t.OTEL_SPAN_EVENT_COUNT_LIMIT,attributePerEventCountLimit:t.OTEL_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT,attributePerLinkCountLimit:t.OTEL_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT},mergeResourceWithDefaults:!0}}function su(t){switch(void 0===t&&(t=oR()),t.OTEL_TRACES_SAMPLER){case nX.AlwaysOn:return new sr;case nX.AlwaysOff:return new sn;case nX.ParentBasedAlwaysOn:return new si({root:new sr});case nX.ParentBasedAlwaysOff:return new si({root:new sn});case nX.TraceIdRatio:return new so(sc(t));case nX.ParentBasedTraceIdRatio:return new si({root:new so(sc(t))});default:return rN.error('OTEL_TRACES_SAMPLER value "'+t.OTEL_TRACES_SAMPLER+" invalid, defaulting to "+ss+'".'),new sr}}function sc(t){if(void 0===t.OTEL_TRACES_SAMPLER_ARG||""===t.OTEL_TRACES_SAMPLER_ARG)return rN.error("OTEL_TRACES_SAMPLER_ARG is blank, defaulting to 1."),1;var e=Number(t.OTEL_TRACES_SAMPLER_ARG);return isNaN(e)?(rN.error("OTEL_TRACES_SAMPLER_ARG="+t.OTEL_TRACES_SAMPLER_ARG+" was given, but it is invalid, defaulting to 1."),1):e<0||e>1?(rN.error("OTEL_TRACES_SAMPLER_ARG="+t.OTEL_TRACES_SAMPLER_ARG+" was given, but it is out of range ([0..1]), defaulting to 1."),1):e}var sl=function(){function t(t,e){this._exporter=t,this._isExporting=!1,this._finishedSpans=[],this._droppedSpansCount=0;var n=oR();this._maxExportBatchSize="number"==typeof(null==e?void 0:e.maxExportBatchSize)?e.maxExportBatchSize:n.OTEL_BSP_MAX_EXPORT_BATCH_SIZE,this._maxQueueSize="number"==typeof(null==e?void 0:e.maxQueueSize)?e.maxQueueSize:n.OTEL_BSP_MAX_QUEUE_SIZE,this._scheduledDelayMillis="number"==typeof(null==e?void 0:e.scheduledDelayMillis)?e.scheduledDelayMillis:n.OTEL_BSP_SCHEDULE_DELAY,this._exportTimeoutMillis="number"==typeof(null==e?void 0:e.exportTimeoutMillis)?e.exportTimeoutMillis:n.OTEL_BSP_EXPORT_TIMEOUT,this._shutdownOnce=new o9(this._shutdown,this),this._maxExportBatchSize>this._maxQueueSize&&(rN.warn("BatchSpanProcessor: maxExportBatchSize must be smaller or equal to maxQueueSize, setting maxExportBatchSize to match maxQueueSize"),this._maxExportBatchSize=this._maxQueueSize)}return t.prototype.forceFlush=function(){return this._shutdownOnce.isCalled?this._shutdownOnce.promise:this._flushAll()},t.prototype.onStart=function(t,e){},t.prototype.onEnd=function(t){this._shutdownOnce.isCalled||(t.spanContext().traceFlags&nU.SAMPLED)!=0&&this._addToBuffer(t)},t.prototype.shutdown=function(){return this._shutdownOnce.call()},t.prototype._shutdown=function(){var t=this;return Promise.resolve().then(function(){return t.onShutdown()}).then(function(){return t._flushAll()}).then(function(){return t._exporter.shutdown()})},t.prototype._addToBuffer=function(t){if(this._finishedSpans.length>=this._maxQueueSize){0===this._droppedSpansCount&&rN.debug("maxQueueSize reached, dropping spans"),this._droppedSpansCount++;return}this._droppedSpansCount>0&&(rN.warn("Dropped "+this._droppedSpansCount+" spans because maxQueueSize reached"),this._droppedSpansCount=0),this._finishedSpans.push(t),this._maybeStartTimer()},t.prototype._flushAll=function(){var t=this;return new Promise(function(e,n){for(var r=[],i=Math.ceil(t._finishedSpans.length/t._maxExportBatchSize),o=0;o<i;o++)r.push(t._flushOneBatch());Promise.all(r).then(function(){e()}).catch(n)})},t.prototype._flushOneBatch=function(){var t=this;return(this._clearTimer(),0===this._finishedSpans.length)?Promise.resolve():new Promise(function(e,n){var r=setTimeout(function(){n(Error("Timeout"))},t._exportTimeoutMillis);ru.with(ru.active().setValue(os,!0),function(){t._finishedSpans.length<=t._maxExportBatchSize?(i=t._finishedSpans,t._finishedSpans=[]):i=t._finishedSpans.splice(0,t._maxExportBatchSize);for(var i,o=function(){return t._exporter.export(i,function(t){var i;clearTimeout(r),t.code===nz.SUCCESS?e():n(null!=(i=t.error)?i:Error("BatchSpanProcessor: span export failed"))})},s=null,a=0,u=i.length;a<u;a++){var c=i[a];c.resource.asyncAttributesPending&&c.resource.waitForAsyncAttributes&&(null!=s||(s=[]),s.push(c.resource.waitForAsyncAttributes()))}null===s?o():Promise.all(s).then(o,function(t){om(t),n(t)})})})},t.prototype._maybeStartTimer=function(){var t=this;if(!this._isExporting){var e=function(){t._isExporting=!0,t._flushOneBatch().finally(function(){t._isExporting=!1,t._finishedSpans.length>0&&(t._clearTimer(),t._maybeStartTimer())}).catch(function(e){t._isExporting=!1,om(e)})};if(this._finishedSpans.length>=this._maxExportBatchSize)return e();void 0===this._timer&&(this._timer=setTimeout(function(){return e()},this._scheduledDelayMillis),this._timer.unref())}},t.prototype._clearTimer=function(){void 0!==this._timer&&(clearTimeout(this._timer),this._timer=void 0)},t}(),sp=globalThis&&globalThis.__extends||function(){var t=function(e,n){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(e,n)};return function(e,n){if("function"!=typeof n&&null!==n)throw TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),sf=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return sp(e,t),e.prototype.onShutdown=function(){},e}(sl),sd=function(){this.generateTraceId=s_(16),this.generateSpanId=s_(8)},sh=oi.allocUnsafe(16);function s_(t){return function(){for(var e=0;e<t/4;e++)sh.writeUInt32BE(0x100000000*Math.random()>>>0,4*e);for(var e=0;e<t;e++)if(sh[e]>0)break;else e===t-1&&(sh[t-1]=1);return sh.toString("hex",0,t)}}var sg=function(){function t(t,e,n){this._tracerProvider=n;var r,i,o,s=(r={sampler:su()},(o=Object.assign({},i=sa(),r,e)).generalLimits=Object.assign({},i.generalLimits,e.generalLimits||{}),o.spanLimits=Object.assign({},i.spanLimits,e.spanLimits||{}),o);this._sampler=s.sampler,this._generalLimits=s.generalLimits,this._spanLimits=s.spanLimits,this._idGenerator=e.idGenerator||new sd,this.resource=n.resource,this.instrumentationLibrary=t}return t.prototype.startSpan=function(t,e,n){void 0===e&&(e={}),void 0===n&&(n=ru.active()),e.root&&(n=rw.deleteSpan(n));var r,i,o,s,a,u,c=rw.getSpan(n);if(oa(n)){rN.debug("Instrumentation suppressed, returning Noop Span");var l=rw.wrapSpanContext(rp);return l}var p=null==c?void 0:c.spanContext(),f=this._idGenerator.generateSpanId();p&&rw.isSpanContextValid(p)?(s=p.traceId,a=p.traceState,u=p.spanId):s=this._idGenerator.generateTraceId();var d=null!=(r=e.kind)?r:nB.INTERNAL,h=(null!=(i=e.links)?i:[]).map(function(t){return{context:t.context,attributes:od(t.attributes)}}),_=od(e.attributes),g=this._sampler.shouldSample(n,s,t,d,_,h);a=null!=(o=g.traceState)?o:a;var m={traceId:s,spanId:f,traceFlags:g.decision===n$.RECORD_AND_SAMPLED?nU.SAMPLED:nU.NONE,traceState:a};if(g.decision===n$.NOT_RECORD){rN.debug("Recording is off, propagating context in a non-recording span");var l=rw.wrapSpanContext(m);return l}var y=od(Object.assign(_,g.attributes));return new se(this,n,t,m,d,u,h,e.startTime,void 0,y)},t.prototype.startActiveSpan=function(t,e,n,r){if(!(arguments.length<2)){2==arguments.length?s=e:3==arguments.length?(i=e,s=n):(i=e,o=n,s=r);var i,o,s,a=null!=o?o:ru.active(),u=this.startSpan(t,i,a),c=rw.setSpan(a,u);return ru.with(c,s,void 0,u)}},t.prototype.getGeneralLimits=function(){return this._generalLimits},t.prototype.getSpanLimits=function(){return this._spanLimits},t.prototype.getActiveSpanProcessor=function(){return this._tracerProvider.getActiveSpanProcessor()},t}(),sm=globalThis&&globalThis.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,n=e&&t[e],r=0;if(n)return n.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},sy=function(){function t(t){this._spanProcessors=t}return t.prototype.forceFlush=function(){var t,e,n=[];try{for(var r=sm(this._spanProcessors),i=r.next();!i.done;i=r.next()){var o=i.value;n.push(o.forceFlush())}}catch(e){t={error:e}}finally{try{i&&!i.done&&(e=r.return)&&e.call(r)}finally{if(t)throw t.error}}return new Promise(function(t){Promise.all(n).then(function(){t()}).catch(function(e){om(e||Error("MultiSpanProcessor: forceFlush failed")),t()})})},t.prototype.onStart=function(t,e){var n,r;try{for(var i=sm(this._spanProcessors),o=i.next();!o.done;o=i.next())o.value.onStart(t,e)}catch(t){n={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}},t.prototype.onEnd=function(t){var e,n;try{for(var r=sm(this._spanProcessors),i=r.next();!i.done;i=r.next())i.value.onEnd(t)}catch(t){e={error:t}}finally{try{i&&!i.done&&(n=r.return)&&n.call(r)}finally{if(e)throw e.error}}},t.prototype.shutdown=function(){var t,e,n=[];try{for(var r=sm(this._spanProcessors),i=r.next();!i.done;i=r.next()){var o=i.value;n.push(o.shutdown())}}catch(e){t={error:e}}finally{try{i&&!i.done&&(e=r.return)&&e.call(r)}finally{if(t)throw t.error}}return new Promise(function(t,e){Promise.all(n).then(function(){t()},e)})},t}(),sv=function(){function t(){}return t.prototype.onStart=function(t,e){},t.prototype.onEnd=function(t){},t.prototype.shutdown=function(){return Promise.resolve()},t.prototype.forceFlush=function(){return Promise.resolve()},t}(),sE=globalThis&&globalThis.__read||function(t,e){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var r,i,o=n.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(r=o.next()).done;)s.push(r.value)}catch(t){i={error:t}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}return s},sS=globalThis&&globalThis.__spreadArray||function(t,e,n){if(n||2==arguments.length)for(var r,i=0,o=e.length;i<o;i++)!r&&i in e||(r||(r=Array.prototype.slice.call(e,0,i)),r[i]=e[i]);return t.concat(r||Array.prototype.slice.call(e))};!function(t){t[t.resolved=0]="resolved",t[t.timeout=1]="timeout",t[t.error=2]="error",t[t.unresolved=3]="unresolved"}(nW||(nW={}));var sT=function(){function t(t){void 0===t&&(t={}),this._registeredSpanProcessors=[],this._tracers=new Map;var e,n,r,i,o,s,a,u,c,l,p,f,d,h,_,g,m,y=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var n=t.shift(),r=new WeakMap;t.length>0;)n=function t(e,n,r,i){if(void 0===r&&(r=0),!(r>20)){if(r++,o2(e)||o2(n)||o0(n))a=oZ(n);else if(oQ(e)){if(a=e.slice(),oQ(n))for(var o,s,a,u=0,c=n.length;u<c;u++)a.push(oZ(n[u]));else if(o1(n))for(var l=Object.keys(n),u=0,c=l.length;u<c;u++){var p=l[u];a[p]=oZ(n[p])}}else if(o1(e))if(o1(n)){if(o=e,s=n,!(oq(o)&&oq(s)))return n;a=Object.assign({},e);for(var l=Object.keys(n),u=0,c=l.length;u<c;u++){var p=l[u],f=n[p];if(o2(f))void 0===f?delete a[p]:a[p]=f;else{var d=a[p];if(oJ(e,p,i)||oJ(n,p,i))delete a[p];else{if(o1(d)&&o1(f)){var h=i.get(d)||[],_=i.get(f)||[];h.push({obj:e,key:p}),_.push({obj:n,key:p}),i.set(d,h),i.set(f,_)}a[p]=t(a[p],f,r,i)}}}}else a=n;return a}}(n,t.shift(),0,r);return n}({},sa(),(h=Object.assign({},(e=t).spanLimits),_=oO(process.env),h.attributeCountLimit=null!=(a=null!=(s=null!=(o=null!=(r=null==(n=e.spanLimits)?void 0:n.attributeCountLimit)?r:null==(i=e.generalLimits)?void 0:i.attributeCountLimit)?o:_.OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT)?s:_.OTEL_ATTRIBUTE_COUNT_LIMIT)?a:128,h.attributeValueLengthLimit=null!=(d=null!=(f=null!=(p=null!=(c=null==(u=e.spanLimits)?void 0:u.attributeValueLengthLimit)?c:null==(l=e.generalLimits)?void 0:l.attributeValueLengthLimit)?p:_.OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT)?f:_.OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT)?d:oS,Object.assign({},e,{spanLimits:h})));if(this.resource=null!=(g=y.resource)?g:r3.empty(),y.mergeResourceWithDefaults&&(this.resource=r3.default().merge(this.resource)),this._config=Object.assign({},y,{resource:this.resource}),null==(m=t.spanProcessors)?void 0:m.length)this._registeredSpanProcessors=sS([],sE(t.spanProcessors),!1),this.activeSpanProcessor=new sy(this._registeredSpanProcessors);else{var v=this._buildExporterFromEnv();if(void 0!==v){var E=new sf(v);this.activeSpanProcessor=E}else this.activeSpanProcessor=new sv}}return t.prototype.getTracer=function(t,e,n){var r=t+"@"+(e||"")+":"+((null==n?void 0:n.schemaUrl)||"");return this._tracers.has(r)||this._tracers.set(r,new sg({name:t,version:e,schemaUrl:null==n?void 0:n.schemaUrl},this._config,this)),this._tracers.get(r)},t.prototype.addSpanProcessor=function(t){0===this._registeredSpanProcessors.length&&this.activeSpanProcessor.shutdown().catch(function(t){return rN.error("Error while trying to shutdown current span processor",t)}),this._registeredSpanProcessors.push(t),this.activeSpanProcessor=new sy(this._registeredSpanProcessors)},t.prototype.getActiveSpanProcessor=function(){return this.activeSpanProcessor},t.prototype.register=function(t){void 0===t&&(t={}),rw.setGlobalTracerProvider(this),void 0===t.propagator&&(t.propagator=this._buildPropagatorFromEnv()),t.contextManager&&ru.setGlobalContextManager(t.contextManager),t.propagator&&rZ.setGlobalPropagator(t.propagator)},t.prototype.forceFlush=function(){var t=this._config.forceFlushTimeoutMillis,e=this._registeredSpanProcessors.map(function(e){return new Promise(function(n){var r,i=setTimeout(function(){n(Error("Span processor did not completed within timeout period of "+t+" ms")),r=nW.timeout},t);e.forceFlush().then(function(){clearTimeout(i),r!==nW.timeout&&n(r=nW.resolved)}).catch(function(t){clearTimeout(i),r=nW.error,n(t)})})});return new Promise(function(t,n){Promise.all(e).then(function(e){var r=e.filter(function(t){return t!==nW.resolved});r.length>0?n(r):t()}).catch(function(t){return n([t])})})},t.prototype.shutdown=function(){return this.activeSpanProcessor.shutdown()},t.prototype._getPropagator=function(t){var e;return null==(e=this.constructor._registeredPropagators.get(t))?void 0:e()},t.prototype._getSpanExporter=function(t){var e;return null==(e=this.constructor._registeredExporters.get(t))?void 0:e()},t.prototype._buildPropagatorFromEnv=function(){var t=this,e=Array.from(new Set(oR().OTEL_PROPAGATORS)),n=e.map(function(e){var n=t._getPropagator(e);return n||rN.warn('Propagator "'+e+'" requested through environment variable is unavailable.'),n}).reduce(function(t,e){return e&&t.push(e),t},[]);return 0===n.length?void 0:1===e.length?n[0]:new oN({propagators:n})},t.prototype._buildExporterFromEnv=function(){var t=oR().OTEL_TRACES_EXPORTER;if("none"!==t&&""!==t){var e=this._getSpanExporter(t);return e||rN.error('Exporter "'+t+'" requested through environment variable is unavailable.'),e}},t._registeredPropagators=new Map([["tracecontext",function(){return new oG}],["baggage",function(){return new ol}]]),t._registeredExporters=new Map,t}();let sb=new WeakMap,sO=(t={})=>{let e=void 0===t.breadcrumbs||t.breadcrumbs,n=t.shouldCreateSpanForRequest,r=new eX(100),i=new eX(100),o={};function s(t){let e=tS();if(!e)return!1;let n=e.getOptions();if(void 0===n.tracePropagationTargets)return!0;let r=i.get(t);if(void 0!==r)return r;let o=z(t,n.tracePropagationTargets);return i.set(t,o),o}function a(t){if(void 0===n)return!0;let e=r.get(t);if(void 0!==e)return e;let i=n(t);return r.set(t,i),i}return{name:"WinterCGFetch",setupOnce(){!function(t,e){let n="fetch";A(n,t),P(n,()=>(function(t,e=!1){H(u,"fetch",function(e){return function(...n){let r=Error(),{method:i,url:o}=function(t){if(0===t.length)return{method:"GET",url:""};if(2===t.length){let[e,n]=t;return{url:eW(e),method:eH(n,"method")?String(n.method).toUpperCase():"GET"}}let e=t[0];return{url:eW(e),method:eH(e,"method")?String(e.method).toUpperCase():"GET"}}(n),s={args:n,fetchData:{method:i,url:o},startTimestamp:1e3*tn(),virtualError:r,headers:function(t){let[e,n]=t;try{if("object"==typeof n&&null!==n&&"headers"in n&&n.headers)return new Headers(n.headers);if(V(e))return new Headers(e.headers)}catch{}}(n)};return t||L("fetch",{...s}),e.apply(u,n).then(async e=>(t?t(e):L("fetch",{...s,endTimestamp:1e3*tn(),response:e}),e),t=>{if(L("fetch",{...s,endTimestamp:1e3*tn(),error:t}),k(t)&&void 0===t.stack&&(t.stack=r.stack,W(t,"framesToPop",1)),t instanceof TypeError&&("Failed to fetch"===t.message||"Load failed"===t.message||"NetworkError when attempting to fetch resource."===t.message))try{let e=new URL(s.fetchData.url);t.message=`${t.message} (${e.host})`}catch{}throw t})}})})(void 0,void 0))}(t=>{let n=tS();n&&sb.get(n)&&!function(t,e){var n,r,i,o;let s=e?.getDsn(),a=e?.getOptions().tunnel;return n=t,!!(r=s)&&n.includes(r.host)||(i=t,!!(o=a)&&eY(i)===eY(o))}(t.fetchData.url,n)&&(!function(t,e,n,r,i="auto.http.browser"){if(!t.fetchData)return;let{method:o,url:s}=t.fetchData,a=ed()&&e(s);if(t.endTimestamp&&a){let e=t.fetchData.__span;if(!e)return;let n=r[e];n&&(function(t,e){if(e.response){var n=e.response.status;t.setAttribute("http.response.status_code",n);let r=tx(n);"unknown_error"!==r.message&&t.setStatus(r);let i=e.response?.headers?.get("content-length");if(i){let e=parseInt(i);e>0&&t.setAttribute("http.response_content_length",e)}}else e.error&&t.setStatus({code:2,message:"internal_error"});t.end()}(n,t),delete r[e]);return}let u=!!t2(),c=a&&u?function(t){let e=tg(l());if(e.startInactiveSpan)return e.startInactiveSpan(t);let n=function(t){let e={isStandalone:(t.experimental||{}).standalone,...t};if(t.startTime){let n={...e};return n.startTimestamp=tW(t.startTime),delete n.startTime,n}return e}(t),{forceTransaction:r,parentSpan:i}=t;return(t.scope?e=>tE(t.scope,e):void 0!==i?t=>e5(i,t):t=>t())(()=>{let e=tm(),o=function(t,e){if(e)return e;if(null===e)return;let n=t[ts];if(!n)return;let r=tS();return(r?r.getOptions():{}).parentSpanIsAlwaysRootSpan?t1(n):n}(e,i);return t.onlyIfParent&&!o?new eK:function({parentSpan:t,spanArguments:e,forceTransaction:n,scope:r}){var i,o;let s;if(!ed()){let r=new eK;if(n||!t){let t={sampled:"false",sample_rate:"0",transaction:e.name,...em(r)};W(r,eh,t)}return r}let a=ty();if(t&&!n)s=function(t,e,n){let{spanId:r,traceId:i}=t.spanContext(),o=!e.getScopeData().sdkProcessingMetadata[e4]&&tq(t),s=o?new e0({...n,parentSpanId:r,traceId:i,sampled:o}):new eK({traceId:i});t0(t,s);let a=tS();return a&&(a.emit("spanStart",s),n.endTimestamp&&a.emit("spanEnd",s)),s}(t,r,e),t0(t,s);else if(t){let n=em(t),{traceId:i,spanId:o}=t.spanContext(),a=tq(t);W(s=e9({traceId:i,parentSpanId:o,...e},r,a),eh,n)}else{let{traceId:t,dsc:n,parentSpanId:i,sampled:o}={...a.getPropagationContext(),...r.getPropagationContext()};s=e9({traceId:t,parentSpanId:i,...e},r,o),n&&W(s,eh,n)}return eq(s),tN(s,r,a),s}({parentSpan:o,spanArguments:n,forceTransaction:r,scope:e})})}(function(t,e,n){let r=function(t,e){let n=0>=t.indexOf("://")&&0!==t.indexOf("//"),r=(void 0)??(n?"thismessage:/":void 0);try{if("canParse"in URL&&!URL.canParse(t,r))return;let e=new URL(t,r);if(n)return{isRelative:n,pathname:e.pathname,search:e.search,hash:e.hash};return e}catch{}}(t);return{name:r?`${e} ${function(t){if(t8(t))return t.pathname;let e=new URL(t);return e.search="",e.hash="",["80","443"].includes(e.port)&&(e.port=""),e.password&&(e.password="%filtered%"),e.username&&(e.username="%filtered%"),e.toString()}(r)}`:e,attributes:function(t,e,n,r){let i={url:t,type:"fetch","http.method":n,[tA]:r,[tR]:"http.client"};return e&&(t8(e)||(i["http.url"]=e.href,i["server.address"]=e.host),e.search&&(i["http.query"]=e.search),e.hash&&(i["http.fragment"]=e.hash)),i}(t,r,e,n)}}(s,o,i)):new eK;if(t.fetchData.__span=c.spanContext().spanId,r[c.spanContext().spanId]=c,n(t.fetchData.url)){let e=t.args[0],n=t.args[1]||{},r=function(t,e,n){var r;let i=function(t={}){let e=t.client||tS();if(!function(){let t=tS();return t?.getOptions().enabled!==!1&&!!t?.getTransport()}()||!e)return{};let n=tg(l());if(n.getTraceData)return n.getTraceData(t);let r=t.scope||tm(),i=t.span||t2(),o=i?function(t){let{traceId:e,spanId:n}=t.spanContext();return tV(e,n,tq(t))}(i):function(t){let{traceId:e,sampled:n,propagationSpanId:r}=t.getPropagationContext();return tV(e,r,n)}(r),s=tU(i?em(i):eg(e,r));return tG.test(o)?{"sentry-trace":o,baggage:s}:(m.warn("Invalid sentry-trace data. Cannot generate trace data"),{})}({span:n}),o=i["sentry-trace"],s=i.baggage;if(!o)return;let a=e.headers||(V(t)?t.headers:void 0);if(!a)return{...i};if(r=a,"undefined"!=typeof Headers&&F(r,Headers)){let t=new Headers(a);if(t.get("sentry-trace")||t.set("sentry-trace",o),s){let e=t.get("baggage");e?e6(e)||t.set("baggage",`${e},${s}`):t.set("baggage",s)}return t}if(Array.isArray(a)){let t=[...a];a.find(t=>"sentry-trace"===t[0])||t.push(["sentry-trace",o]);let e=a.find(t=>"baggage"===t[0]&&e6(t[1]));return s&&!e&&t.push(["baggage",s]),t}{let t="sentry-trace"in a?a["sentry-trace"]:void 0,e="baggage"in a?a.baggage:void 0,n=e?Array.isArray(e)?[...e]:[e]:[],r=e&&(Array.isArray(e)?e.find(t=>e6(t)):e6(e));return s&&!r&&n.push(s),{...a,"sentry-trace":t??o,baggage:n.length>0?n.join(","):void 0}}}(e,n,ed()&&u?c:void 0);r&&(t.args[1]=n,n.headers=r)}let p=tS();if(p){let e={input:t.args,response:t.response,startTimestamp:t.startTimestamp,endTimestamp:t.endTimestamp};p.emit("beforeOutgoingRequestSpan",c,e)}}(t,a,s,o,"auto.http.wintercg_fetch"),e&&function(t){let{startTimestamp:e,endTimestamp:n}=t;if(!n)return;let r={method:t.fetchData.method,url:t.fetchData.url};if(t.error)e7({category:"fetch",data:r,level:"error",type:"http"},{data:t.error,input:t.args,startTimestamp:e,endTimestamp:n});else{let i=t.response;r.request_body_size=t.fetchData.request_body_size,r.response_body_size=t.fetchData.response_body_size,r.status_code=i?.status;let o={input:t.args,response:i,startTimestamp:e,endTimestamp:n},s=function(t){if(void 0!==t)return t>=400&&t<500?"warning":t>=500?"error":void 0}(r.status_code);e7({category:"fetch",data:r,type:"http",level:s},o)}}(t))})},setup(t){sb.set(t,!0)}}};class sR{constructor(t=30){this.$=[],this._taskProducers=[],this._bufferSize=t}add(t){return this._taskProducers.length>=this._bufferSize?Promise.reject(nt):(this._taskProducers.push(t),Promise.resolve({}))}drain(t){let e=[...this._taskProducers];return this._taskProducers=[],new Promise(n=>{let r=setTimeout(()=>{t&&t>0&&n(!1)},t);Promise.all(e.map(t=>t().then(null,()=>{}))).then(()=>{clearTimeout(r),n(!0)})})}}function sA(t){return function(t,e,n=function(t){let e=[];function n(t){return e.splice(e.indexOf(t),1)[0]||Promise.resolve(void 0)}return{$:e,add:function(r){if(!(void 0===t||e.length<t))return eT(nt);let i=r();return -1===e.indexOf(i)&&e.push(i),i.then(()=>n(i)).then(null,()=>n(i).then(null,()=>{})),i},drain:function(t){return new eb((n,r)=>{let i=e.length;if(!i)return n(!0);let o=setTimeout(()=>{t&&t>0&&n(!1)},t);e.forEach(t=>{eS(t).then(()=>{--i||(clearTimeout(o),n(!0))},r)})})}}}(t.bufferSize||64)){let r={};return{send:function(i){let o=[];if(eu(i,(e,n)=>{let i=el[n];!function(t,e,n=Date.now()){return(t[e]||t.all||0)>n}(r,i)?o.push(e):t.recordDroppedEvent("ratelimit_backoff",i)}),0===o.length)return eS({});let s=ea(i[0],o),a=e=>{eu(s,(n,r)=>{t.recordDroppedEvent(e,el[r])})};return n.add(()=>e({body:function(t){let[e,n]=t,r=JSON.stringify(e);function i(t){"string"==typeof r?r="string"==typeof t?r+t:[ec(r),t]:r.push("string"==typeof t?ec(t):t)}for(let t of n){let[e,n]=t;if(i(`
${JSON.stringify(e)}
`),"string"==typeof n||n instanceof Uint8Array)i(n);else{let t;try{t=JSON.stringify(n)}catch{t=JSON.stringify(es(n))}i(t)}}return"string"==typeof r?r:function(t){let e=new Uint8Array(t.reduce((t,e)=>t+e.length,0)),n=0;for(let r of t)e.set(r,n),n+=r.length;return e}(r)}(s)}).then(t=>(void 0!==t.statusCode&&(t.statusCode<200||t.statusCode),r=function(t,{statusCode:e,headers:n},r=Date.now()){let i={...t},o=n?.["x-sentry-rate-limits"],s=n?.["retry-after"];if(o)for(let t of o.trim().split(",")){let[e,n,,,o]=t.split(":",5),s=parseInt(e,10),a=(isNaN(s)?60:s)*1e3;if(n)for(let t of n.split(";"))"metric_bucket"===t?(!o||o.split(";").includes("custom"))&&(i[t]=r+a):i[t]=r+a;else i.all=r+a}else s?i.all=r+function(t,e=Date.now()){let n=parseInt(`${t}`,10);if(!isNaN(n))return 1e3*n;let r=Date.parse(`${t}`);return isNaN(r)?6e4:r-e}(s,r):429===e&&(i.all=r+6e4);return i}(r,t),t),t=>{throw a("network_error"),t})).then(t=>t,t=>{if(t===nt)return a("queue_overflow"),eS({});throw t})},flush:t=>n.drain(t)}}(t,function(e){let n={body:e.body,method:"POST",headers:t.headers,...t.fetchOptions};var r=()=>fetch(t.url,n).then(t=>({statusCode:t.status,headers:{"x-sentry-rate-limits":t.headers.get("X-Sentry-Rate-Limits"),"retry-after":t.headers.get("Retry-After")}}));let i=tg(l());return i.suppressTracing?i.suppressTracing(r):tE(t=>{t.setSDKProcessingMetadata({[e4]:!0});let e=r();return t.setSDKProcessingMetadata({[e4]:void 0}),e})},new sR(t.bufferSize))}let sP=["addListener","on","once","prependListener","prependOnceListener"];class sL{constructor(){sL.prototype.__init.call(this),sL.prototype.__init2.call(this)}bind(t,e){return"object"==typeof e&&null!==e&&"on"in e?this._bindEventEmitter(t,e):"function"==typeof e?this._bindFunction(t,e):e}_bindFunction(t,e){let n=this,r=function(...r){return n.with(t,()=>e.apply(this,r))};return Object.defineProperty(r,"length",{enumerable:!1,configurable:!0,writable:!1,value:e.length}),r}_bindEventEmitter(t,e){return void 0!==this._getPatchMap(e)||(this._createPatchMap(e),sP.forEach(n=>{void 0!==e[n]&&(e[n]=this._patchAddListener(e,e[n],t))}),"function"==typeof e.removeListener&&(e.removeListener=this._patchRemoveListener(e,e.removeListener)),"function"==typeof e.off&&(e.off=this._patchRemoveListener(e,e.off)),"function"==typeof e.removeAllListeners&&(e.removeAllListeners=this._patchRemoveAllListeners(e,e.removeAllListeners))),e}_patchRemoveListener(t,e){let n=this;return function(r,i){let o=n._getPatchMap(t)?.[r];if(void 0===o)return e.call(this,r,i);let s=o.get(i);return e.call(this,r,s||i)}}_patchRemoveAllListeners(t,e){let n=this;return function(r){let i=n._getPatchMap(t);return void 0!==i&&(0==arguments.length?n._createPatchMap(t):void 0!==i[r]&&delete i[r]),e.apply(this,arguments)}}_patchAddListener(t,e,n){let r=this;return function(i,o){if(r._wrapped)return e.call(this,i,o);let s=r._getPatchMap(t);void 0===s&&(s=r._createPatchMap(t));let a=s[i];void 0===a&&(a=new WeakMap,s[i]=a);let u=r.bind(n,o);a.set(o,u),r._wrapped=!0;try{return e.call(this,i,u)}finally{r._wrapped=!1}}}_createPatchMap(t){let e=Object.create(null);return t[this._kOtListeners]=e,e}_getPatchMap(t){return t[this._kOtListeners]}__init(){this._kOtListeners=Symbol("OtListeners")}__init2(){this._wrapped=!1}}class sI extends sL{constructor(){super();let t=u.AsyncLocalStorage;t?this._asyncLocalStorage=new t:this._asyncLocalStorage={getStore(){},run(t,e,...n){return e.apply(this,n)},disable(){}}}active(){return this._asyncLocalStorage.getStore()??nY}with(t,e,n,...r){let i=null==n?e:e.bind(n);return this._asyncLocalStorage.run(t,i,...r)}enable(){return this}disable(){return this._asyncLocalStorage.disable(),this}}let sx=E([90,function(t){let e=/^\s*[-]{4,}$/,n=/at (?:async )?(?:(.+?)\s+\()?(?:(.+):(\d+):(\d+)?|([^)]+))\)?/;return r=>{let i=r.match(n);if(i){let e,n,r,o,s;if(i[1]){let t=(r=i[1]).lastIndexOf(".");if("."===r[t-1]&&t--,t>0){e=r.slice(0,t),n=r.slice(t+1);let i=e.indexOf(".Module");i>0&&(r=r.slice(i+1),e=e.slice(0,i))}o=void 0}n&&(o=e,s=n),"<anonymous>"===n&&(s=void 0,r=void 0),void 0===r&&(s=s||"?",r=o?`${o}.${s}`:s);let a=i[2]?.startsWith("file://")?i[2].slice(7):i[2],u="native"===i[5];return a?.match(/\/[A-Z]:/)&&(a=a.slice(1)),a||!i[5]||u||(a=i[5]),{filename:a?decodeURI(a):void 0,module:t?t(a):void 0,function:r,lineno:ne(i[3]),colno:ne(i[4]),in_app:function(t,e=!1){return!(e||t&&!t.startsWith("/")&&!t.match(/^[A-Z]:/)&&!t.startsWith(".")&&!t.match(/^[a-zA-Z]([a-zA-Z0-9.\-+])*:\/\//))&&void 0!==t&&!t.includes("node_modules/")}(a||"",u)}}if(r.match(e))return{filename:r}}}(void 0)]);function sC(t){return[nn(),nu(),nf(),n_(),sO(),nm(),...t.sendDefaultPii?[nS()]:[]]}Symbol.toStringTag,n(480);let sw={client:"client",server:"server",edgeServer:"edge-server"};async function sN(){try{await e8(2e3)}catch(t){}}sw.client,sw.server,sw.edgeServer,Symbol("polyfills");let sk=/^(\S+:\\|\/?)([\s\S]*?)((?:\.{1,2}|[^/\\]+?|)(\.[^./\\]*|))(?:[/\\]*)$/;function sM(...t){let e="",n=!1;for(let r=t.length-1;r>=-1&&!n;r--){let i=r>=0?t[r]:"/";i&&(e=`${i}/${e}`,n="/"===i.charAt(0))}return e=(function(t,e){let n=0;for(let e=t.length-1;e>=0;e--){let r=t[e];"."===r?t.splice(e,1):".."===r?(t.splice(e,1),n++):n&&(t.splice(e,1),n--)}if(e)for(;n--;)t.unshift("..");return t})(e.split("/").filter(t=>!!t),!n).join("/"),(n?"/":"")+e||"."}function sD(t){let e=0;for(;e<t.length&&""===t[e];e++);let n=t.length-1;for(;n>=0&&""===t[n];n--);return e>n?[]:t.slice(e,n-e+1)}let sj=(t={})=>{let e=t.root,n=t.prefix||"app:///",r="window"in u&&!!u.window,i=t.iteratee||function({isBrowser:t,root:e,prefix:n}){return r=>{if(!r.filename)return r;let i=/^[a-zA-Z]:\\/.test(r.filename)||r.filename.includes("\\")&&!r.filename.includes("/"),o=/^\//.test(r.filename);if(t){if(e){let t=r.filename;0===t.indexOf(e)&&(r.filename=t.replace(e,n))}}else if(i||o){let t=i?r.filename.replace(/^[a-zA-Z]:/,"").replace(/\\/g,"/"):r.filename,o=e?function(t,e){t=sM(t).slice(1),e=sM(e).slice(1);let n=sD(t.split("/")),r=sD(e.split("/")),i=Math.min(n.length,r.length),o=i;for(let t=0;t<i;t++)if(n[t]!==r[t]){o=t;break}let s=[];for(let t=o;t<n.length;t++)s.push("..");return(s=s.concat(r.slice(o))).join("/")}(e,t):function(t){let e=t.length>1024?`<truncated>${t.slice(-1024)}`:t,n=sk.exec(e);return n?n.slice(1):[]}(t)[2]||"";r.filename=`${n}${o}`}return r}}({isBrowser:r,root:e,prefix:n});return{name:"RewriteFrames",processEvent(t){let e=t;return t.exception&&Array.isArray(t.exception.values)&&(e=function(t){try{return{...t,exception:{...t.exception,values:t.exception.values.map(t=>{var e;return{...t,...t.stacktrace&&{stacktrace:{...e=t.stacktrace,frames:e?.frames?.map(t=>i(t))}}}})}}}catch{return t}}(e)),e}}},sU=({distDirName:t})=>{let e=t.replace(/(\/|\\)$/,""),n=RegExp(`.*${e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}`);return{...sj({iteratee:t=>(t.filename=t.filename?.replace(n,"app:///_next"),t)}),name:"DistDirRewriteFrames"}};globalThis._sentryRewritesTunnelPath="/monitoring",globalThis.SENTRY_RELEASE={id:"4f114066cfffc2184b89e81ba9495ab6e8e5cf06"},globalThis._sentryBasePath=void 0,globalThis._sentryRewriteFramesDistDir=".next";let sB={dsn:process.env.NEXT_PUBLIC_SENTRY_DSN,spotlight:!1,sendDefaultPii:!0,tracesSampleRate:1,debug:!1};async function s$(){process.env.NEXT_PUBLIC_SENTRY_DISABLED||function(t={}){if(t3(),"phase-production-build"===process.env.NEXT_PHASE)return;let e=sC(t),n=".next";n&&e.push(sU({distDirName:n}));let r={defaultIntegrations:e,release:"4f114066cfffc2184b89e81ba9495ab6e8e5cf06",...t};t9(r,"nextjs",["nextjs","vercel-edge"]);let i=function(t={}){var e,n;function r(){let t=iC(ru.active());return t||{scope:tc(),isolationScope:tl()}}function i(){return r().scope}function o(){return r().isolationScope}if(e={withScope:function(t){let e=ru.active();return ru.with(e,()=>t(i()))},withSetScope:function(t,e){let n=t[ix]||ru.active();return ru.with(n.setValue(iL,t),()=>e(t))},withSetIsolationScope:function(t,e){let n=ru.active();return ru.with(n.setValue(iI,t),()=>e(o()))},withIsolationScope:function(t){let e=ru.active();return ru.with(e.setValue(iP,!0),()=>t(o()))},getCurrentScope:i,getIsolationScope:o,startSpan:iz,startSpanManual:iH,startInactiveSpan:iW,getActiveSpan:ij,suppressTracing:i0,getTraceData:i1,continueTrace:iJ,withActiveSpan:iY},p(l()).acs=e,tm().update(t.initialScope),void 0===t.defaultIntegrations&&(t.defaultIntegrations=sC(t)),void 0===t.dsn&&process.env.SENTRY_DSN&&(t.dsn=process.env.SENTRY_DSN),void 0===t.tracesSampleRate&&process.env.SENTRY_TRACES_SAMPLE_RATE){let e=parseFloat(process.env.SENTRY_TRACES_SAMPLE_RATE);isFinite(e)&&(t.tracesSampleRate=e)}if(void 0===t.release){let e=function(t){if(process.env.SENTRY_RELEASE)return process.env.SENTRY_RELEASE;if(u.SENTRY_RELEASE?.id)return u.SENTRY_RELEASE.id;let e=process.env.GITHUB_SHA||process.env.CI_MERGE_REQUEST_SOURCE_BRANCH_SHA||process.env.CI_BUILD_REF||process.env.CI_COMMIT_SHA||process.env.BITBUCKET_COMMIT,n=process.env.APPVEYOR_PULL_REQUEST_HEAD_COMMIT||process.env.APPVEYOR_REPO_COMMIT||process.env.CODEBUILD_RESOLVED_SOURCE_VERSION||process.env.AWS_COMMIT_ID||process.env.BUILD_SOURCEVERSION||process.env.GIT_CLONE_COMMIT_HASH||process.env.BUDDY_EXECUTION_REVISION||process.env.BUILDKITE_COMMIT||process.env.CIRCLE_SHA1||process.env.CIRRUS_CHANGE_IN_REPO||process.env.CF_REVISION||process.env.CM_COMMIT||process.env.CF_PAGES_COMMIT_SHA||process.env.DRONE_COMMIT_SHA||process.env.FC_GIT_COMMIT_SHA||process.env.HEROKU_TEST_RUN_COMMIT_VERSION||process.env.HEROKU_SLUG_COMMIT||process.env.RAILWAY_GIT_COMMIT_SHA||process.env.RENDER_GIT_COMMIT||process.env.SEMAPHORE_GIT_SHA||process.env.TRAVIS_PULL_REQUEST_SHA||process.env.VERCEL_GIT_COMMIT_SHA||process.env.VERCEL_GITHUB_COMMIT_SHA||process.env.VERCEL_GITLAB_COMMIT_SHA||process.env.VERCEL_BITBUCKET_COMMIT_SHA||process.env.ZEIT_GITHUB_COMMIT_SHA||process.env.ZEIT_GITLAB_COMMIT_SHA||process.env.ZEIT_BITBUCKET_COMMIT_SHA,r=process.env.CI_COMMIT_ID||process.env.SOURCE_COMMIT||process.env.SOURCE_VERSION||process.env.GIT_COMMIT||process.env.COMMIT_REF||process.env.BUILD_VCS_NUMBER||process.env.CI_COMMIT_SHA;return e||n||r||void 0}();void 0!==e&&(t.release=e)}t.environment=t.environment||process.env.SENTRY_ENVIRONMENT||function(t){let e=process.env.VERCEL_ENV;return e?`vercel-${e}`:void 0}()||"production";let s=new oo({...t,stackParser:Array.isArray(n=t.stackParser||sx)?E(...n):n,integrations:function(t){let e,n=t.defaultIntegrations||[],r=t.integrations;if(n.forEach(t=>{t.isDefaultInstance=!0}),Array.isArray(r))e=[...n,...r];else if("function"==typeof r){let t=r(n);e=Array.isArray(t)?t:[t]}else e=n;let i={};return e.forEach(t=>{let{name:e}=t,n=i[e];n&&!n.isDefaultInstance&&t.isDefaultInstance||(i[e]=t)}),Object.values(i)}(t),transport:t.transport||sA});return tm().setClient(s),s.init(),t.skipOpenTelemetrySetup||function(t){t.getOptions().debug&&(rN.disable(),rN.setLogger({error:m.error,warn:m.warn,info:m.log,debug:m.log,verbose:m.log},nj.DEBUG));let e=new sT({sampler:new on(t),resource:new r3({"service.name":"edge","service.namespace":"sentry","service.version":c}),forceFlushTimeoutMillis:500,spanProcessors:[new oe({timeout:t.getOptions().maxSpanWaitDuration})]}),n=function(t){class e extends t{constructor(...t){super(...t),i$("SentryContextManager")}with(t,e,n,...r){let i=iC(t),o=i?.scope||tm(),s=i?.isolationScope||ty(),a=!0===t.getValue(iP),u=t.getValue(iL),c=t.getValue(iI),l=u||o.clone(),p=iw(t,{scope:l,isolationScope:c||(a?s.clone():s)}).deleteValue(iP).deleteValue(iL).deleteValue(iI);return W(l,ix,p),super.with(p,e,n,...r)}}return e}(sI);rw.setGlobalTracerProvider(e),rZ.setGlobalPropagator(new iF),ru.setGlobalContextManager(new n),t.traceProvider=e}(s),s.on("createDsc",(t,e)=>{if(!e)return;let n=tK(e).data[tb],{description:r}=e.name?iM(e):{description:void 0};if("url"!==n&&r&&(t.transaction=r),ed()){let n=iN(e.spanContext());t.sampled=void 0==n?void 0:String(n)}}),s.on("preprocessEvent",t=>{let e=ij();if(e&&"transaction"!==t.type)return t.contexts={trace:tz(e),...t.contexts},t.sdkProcessingMetadata={dynamicSamplingContext:em(t1(e)),...t.sdkProcessingMetadata},t}),s}(r);i?.on("spanStart",t=>{let e=tK(t).data;e?.["next.span_type"]!==void 0&&t.setAttribute(tA,"auto"),e?.["next.span_type"]==="Middleware.execute"&&(t.setAttribute(tR,"http.server.middleware"),t.setAttribute(tb,"url"))}),i?.on("preprocessEvent",t=>{"transaction"===t.type&&t.contexts?.trace?.data?.["next.span_type"]==="Middleware.execute"&&t.contexts?.trace?.data?.["next.span_name"]&&t.transaction&&(t.transaction=t7(t.contexts.trace.data["next.span_name"]))}),i?.on("spanEnd",t=>{t===t1(t)&&ee(sN())})}(sB)}let sF=function(t,e,n){tE(r=>{r.setSDKProcessingMetadata({normalizedRequest:{headers:function(t){let e=Object.create(null);try{Object.entries(t).forEach(([t,n])=>{"string"==typeof n&&(e[t]=n)})}catch{}return e}(e.headers),method:e.method}}),r.setContext("nextjs",{request_path:e.path,router_kind:n.routerKind,router_path:n.routePath,route_type:n.routeType}),r.setTransactionName(n.routePath),tm().captureException(t,function(t){if(t){var e;return(e=t)instanceof tu||"function"==typeof e||Object.keys(t).some(t=>eA.includes(t))?{captureContext:t}:t}}({mechanism:{handled:!1}})),ee(sN())})}},356:t=>{"use strict";t.exports=require("node:buffer")},480:t=>{"use strict";t.exports=["chrome 64","edge 79","firefox 67","opera 51","safari 12"]}},t=>{var e=t(t.s=13);(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES).middleware_instrumentation=e}]);
//# sourceMappingURL=edge-instrumentation.js.map