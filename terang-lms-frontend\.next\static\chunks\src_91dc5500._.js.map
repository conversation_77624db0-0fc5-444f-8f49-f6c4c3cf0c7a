{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='card'\r\n      className={cn(\r\n        'bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='card-header'\r\n      className={cn(\r\n        '@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='card-title'\r\n      className={cn('leading-none font-semibold', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='card-description'\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='card-action'\r\n      className={cn(\r\n        'col-start-2 row-span-2 row-start-1 self-start justify-self-end',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='card-content'\r\n      className={cn('px-6', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='card-footer'\r\n      className={cn('flex items-center px-6 [.border-t]:pt-6', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/dialog.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as DialogPrimitive from '@radix-ui/react-dialog';\r\nimport { XIcon } from 'lucide-react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Dialog({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\r\n  return <DialogPrimitive.Root data-slot='dialog' {...props} />;\r\n}\r\n\r\nfunction DialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\r\n  return <DialogPrimitive.Trigger data-slot='dialog-trigger' {...props} />;\r\n}\r\n\r\nfunction DialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\r\n  return <DialogPrimitive.Portal data-slot='dialog-portal' {...props} />;\r\n}\r\n\r\nfunction DialogClose({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\r\n  return <DialogPrimitive.Close data-slot='dialog-close' {...props} />;\r\n}\r\n\r\nfunction DialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\r\n  return (\r\n    <DialogPrimitive.Overlay\r\n      data-slot='dialog-overlay'\r\n      className={cn(\r\n        'data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DialogContent({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Content>) {\r\n  return (\r\n    <DialogPortal data-slot='dialog-portal'>\r\n      <DialogOverlay />\r\n      <DialogPrimitive.Content\r\n        data-slot='dialog-content'\r\n        className={cn(\r\n          'bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg',\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\">\r\n          <XIcon />\r\n          <span className='sr-only'>Close</span>\r\n        </DialogPrimitive.Close>\r\n      </DialogPrimitive.Content>\r\n    </DialogPortal>\r\n  );\r\n}\r\n\r\nfunction DialogHeader({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='dialog-header'\r\n      className={cn('flex flex-col gap-2 text-center sm:text-left', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DialogFooter({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='dialog-footer'\r\n      className={cn(\r\n        'flex flex-col-reverse gap-2 sm:flex-row sm:justify-end',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\r\n  return (\r\n    <DialogPrimitive.Title\r\n      data-slot='dialog-title'\r\n      className={cn('text-lg leading-none font-semibold', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\r\n  return (\r\n    <DialogPrimitive.Description\r\n      data-slot='dialog-description'\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport {\r\n  Dialog,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogOverlay,\r\n  DialogPortal,\r\n  DialogTitle,\r\n  DialogTrigger\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,GAAG,OACkD;IACrD,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAxBS;AA0BT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 319, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/breadcrumb.tsx"], "sourcesContent": ["import * as React from 'react';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { ChevronRight, MoreHorizontal } from 'lucide-react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Breadcrumb({ ...props }: React.ComponentProps<'nav'>) {\r\n  return <nav aria-label='breadcrumb' data-slot='breadcrumb' {...props} />;\r\n}\r\n\r\nfunction BreadcrumbList({ className, ...props }: React.ComponentProps<'ol'>) {\r\n  return (\r\n    <ol\r\n      data-slot='breadcrumb-list'\r\n      className={cn(\r\n        'text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction BreadcrumbItem({ className, ...props }: React.ComponentProps<'li'>) {\r\n  return (\r\n    <li\r\n      data-slot='breadcrumb-item'\r\n      className={cn('inline-flex items-center gap-1.5', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction BreadcrumbLink({\r\n  asChild,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'a'> & {\r\n  asChild?: boolean;\r\n}) {\r\n  const Comp = asChild ? Slot : 'a';\r\n\r\n  return (\r\n    <Comp\r\n      data-slot='breadcrumb-link'\r\n      className={cn('hover:text-foreground transition-colors', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction BreadcrumbPage({ className, ...props }: React.ComponentProps<'span'>) {\r\n  return (\r\n    <span\r\n      data-slot='breadcrumb-page'\r\n      role='link'\r\n      aria-disabled='true'\r\n      aria-current='page'\r\n      className={cn('text-foreground font-normal', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction BreadcrumbSeparator({\r\n  children,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'li'>) {\r\n  return (\r\n    <li\r\n      data-slot='breadcrumb-separator'\r\n      role='presentation'\r\n      aria-hidden='true'\r\n      className={cn('[&>svg]:size-3.5', className)}\r\n      {...props}\r\n    >\r\n      {children ?? <ChevronRight />}\r\n    </li>\r\n  );\r\n}\r\n\r\nfunction BreadcrumbEllipsis({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'span'>) {\r\n  return (\r\n    <span\r\n      data-slot='breadcrumb-ellipsis'\r\n      role='presentation'\r\n      aria-hidden='true'\r\n      className={cn('flex size-9 items-center justify-center', className)}\r\n      {...props}\r\n    >\r\n      <MoreHorizontal className='size-4' />\r\n      <span className='sr-only'>More</span>\r\n    </span>\r\n  );\r\n}\r\n\r\nexport {\r\n  Breadcrumb,\r\n  BreadcrumbList,\r\n  BreadcrumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbPage,\r\n  BreadcrumbSeparator,\r\n  BreadcrumbEllipsis\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;AACA;AACA;AAAA;AAEA;;;;;AAEA,SAAS,WAAW,EAAE,GAAG,OAAoC;IAC3D,qBAAO,6LAAC;QAAI,cAAW;QAAa,aAAU;QAAc,GAAG,KAAK;;;;;;AACtE;KAFS;AAIT,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4FACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QACjD,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,eAAe,EACtB,OAAO,EACP,SAAS,EACT,GAAG,OAGJ;IACC,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MAhBS;AAkBT,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAqC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,MAAK;QACL,iBAAc;QACd,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,oBAAoB,EAC3B,QAAQ,EACR,SAAS,EACT,GAAG,OACwB;IAC3B,qBACE,6LAAC;QACC,aAAU;QACV,MAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB;QACjC,GAAG,KAAK;kBAER,0BAAY,6LAAC,yNAAA,CAAA,eAAY;;;;;;;;;;AAGhC;MAhBS;AAkBT,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6LAAC;QACC,aAAU;QACV,MAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;0BAET,6LAAC,mNAAA,CAAA,iBAAc;gBAAC,WAAU;;;;;;0BAC1B,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;MAhBS", "debugId": null}}, {"offset": {"line": 469, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/hooks/use-breadcrumbs.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { usePathname } from 'next/navigation';\r\nimport { useMemo } from 'react';\r\n\r\ntype BreadcrumbItem = {\r\n  title: string;\r\n  link: string;\r\n};\r\n\r\n// This allows to add custom title as well\r\nconst routeMapping: Record<string, BreadcrumbItem[]> = {\r\n  '/dashboard': [{ title: 'Dashboard', link: '/dashboard' }],\r\n  '/dashboard/employee': [\r\n    { title: 'Dashboard', link: '/dashboard' },\r\n    { title: 'Employee', link: '/dashboard/employee' }\r\n  ],\r\n  '/dashboard/product': [\r\n    { title: 'Dashboard', link: '/dashboard' },\r\n    { title: 'Product', link: '/dashboard/product' }\r\n  ],\r\n  '/courses': [\r\n    { title: 'Home', link: '/' },\r\n    { title: 'Available Courses', link: '/courses' }\r\n  ],\r\n  '/my-courses': [\r\n    { title: 'Home', link: '/' },\r\n    { title: 'My Courses', link: '/my-courses' }\r\n  ]\r\n  // Add more custom mappings as needed\r\n};\r\n\r\nexport function useBreadcrumbs() {\r\n  const pathname = usePathname();\r\n\r\n  const breadcrumbs = useMemo(() => {\r\n    // Check if we have a custom mapping for this exact path\r\n    if (routeMapping[pathname]) {\r\n      return routeMapping[pathname];\r\n    }\r\n\r\n    // If no exact match, fall back to generating breadcrumbs from the path\r\n    const segments = pathname.split('/').filter(Boolean);\r\n    return segments.map((segment, index) => {\r\n      const path = `/${segments.slice(0, index + 1).join('/')}`;\r\n      return {\r\n        title: segment.charAt(0).toUpperCase() + segment.slice(1),\r\n        link: path\r\n      };\r\n    });\r\n  }, [pathname]);\r\n\r\n  return breadcrumbs;\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;AACA;;AAHA;;;AAUA,0CAA0C;AAC1C,MAAM,eAAiD;IACrD,cAAc;QAAC;YAAE,OAAO;YAAa,MAAM;QAAa;KAAE;IAC1D,uBAAuB;QACrB;YAAE,OAAO;YAAa,MAAM;QAAa;QACzC;YAAE,OAAO;YAAY,MAAM;QAAsB;KAClD;IACD,sBAAsB;QACpB;YAAE,OAAO;YAAa,MAAM;QAAa;QACzC;YAAE,OAAO;YAAW,MAAM;QAAqB;KAChD;IACD,YAAY;QACV;YAAE,OAAO;YAAQ,MAAM;QAAI;QAC3B;YAAE,OAAO;YAAqB,MAAM;QAAW;KAChD;IACD,eAAe;QACb;YAAE,OAAO;YAAQ,MAAM;QAAI;QAC3B;YAAE,OAAO;YAAc,MAAM;QAAc;KAC5C;AAEH;AAEO,SAAS;;IACd,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;+CAAE;YAC1B,wDAAwD;YACxD,IAAI,YAAY,CAAC,SAAS,EAAE;gBAC1B,OAAO,YAAY,CAAC,SAAS;YAC/B;YAEA,uEAAuE;YACvE,MAAM,WAAW,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;YAC5C,OAAO,SAAS,GAAG;uDAAC,CAAC,SAAS;oBAC5B,MAAM,OAAO,CAAC,CAAC,EAAE,SAAS,KAAK,CAAC,GAAG,QAAQ,GAAG,IAAI,CAAC,MAAM;oBACzD,OAAO;wBACL,OAAO,QAAQ,MAAM,CAAC,GAAG,WAAW,KAAK,QAAQ,KAAK,CAAC;wBACvD,MAAM;oBACR;gBACF;;QACF;8CAAG;QAAC;KAAS;IAEb,OAAO;AACT;GArBgB;;QACG,qIAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 567, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/breadcrumbs.tsx"], "sourcesContent": ["'use client';\r\nimport {\r\n  Bread<PERSON>rumb,\r\n  Bread<PERSON>rumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbList,\r\n  BreadcrumbPage,\r\n  BreadcrumbSeparator\r\n} from '@/components/ui/breadcrumb';\r\nimport { useBreadcrumbs } from '@/hooks/use-breadcrumbs';\r\nimport { Slash } from 'lucide-react';\r\nimport { Fragment } from 'react';\r\n\r\nexport function Breadcrumbs() {\r\n  const items = useBreadcrumbs();\r\n  if (items.length === 0) return null;\r\n\r\n  return (\r\n    <Breadcrumb>\r\n      <BreadcrumbList>\r\n        {items.map((item, index) => (\r\n          <Fragment key={item.title}>\r\n            {index !== items.length - 1 && (\r\n              <BreadcrumbItem className='hidden md:block'>\r\n                <BreadcrumbLink href={item.link}>{item.title}</BreadcrumbLink>\r\n              </BreadcrumbItem>\r\n            )}\r\n            {index < items.length - 1 && (\r\n              <BreadcrumbSeparator className='hidden md:block'>\r\n                <Slash />\r\n              </BreadcrumbSeparator>\r\n            )}\r\n            {index === items.length - 1 && (\r\n              <BreadcrumbPage>{item.title}</BreadcrumbPage>\r\n            )}\r\n          </Fragment>\r\n        ))}\r\n      </BreadcrumbList>\r\n    </Breadcrumb>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AAQA;AACA;AACA;;;AAXA;;;;;AAaO,SAAS;;IACd,MAAM,QAAQ,CAAA,GAAA,sIAAA,CAAA,iBAAc,AAAD;IAC3B,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO;IAE/B,qBACE,6LAAC,yIAAA,CAAA,aAAU;kBACT,cAAA,6LAAC,yIAAA,CAAA,iBAAc;sBACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC,6JAAA,CAAA,WAAQ;;wBACN,UAAU,MAAM,MAAM,GAAG,mBACxB,6LAAC,yIAAA,CAAA,iBAAc;4BAAC,WAAU;sCACxB,cAAA,6LAAC,yIAAA,CAAA,iBAAc;gCAAC,MAAM,KAAK,IAAI;0CAAG,KAAK,KAAK;;;;;;;;;;;wBAG/C,QAAQ,MAAM,MAAM,GAAG,mBACtB,6LAAC,yIAAA,CAAA,sBAAmB;4BAAC,WAAU;sCAC7B,cAAA,6LAAC,uMAAA,CAAA,QAAK;;;;;;;;;;wBAGT,UAAU,MAAM,MAAM,GAAG,mBACxB,6LAAC,yIAAA,CAAA,iBAAc;sCAAE,KAAK,KAAK;;;;;;;mBAZhB,KAAK,KAAK;;;;;;;;;;;;;;;AAmBnC;GA3BgB;;QACA,sIAAA,CAAA,iBAAc;;;KADd", "debugId": null}}, {"offset": {"line": 658, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/lib/feature-flags.ts"], "sourcesContent": ["export interface FeatureFlags {\r\n  enableCoursePurchase: boolean;\r\n  enableEnrollmentCode: boolean;\r\n  enableCoursePreview: boolean;\r\n  enablePaymentIntegration: boolean;\r\n}\r\n\r\nconst defaultFlags: FeatureFlags = {\r\n  enableCoursePurchase: true,\r\n  enableEnrollmentCode: true,\r\n  enableCoursePreview: true,\r\n  enablePaymentIntegration: false, // Set to true when payment system is ready\r\n};\r\n\r\nexport function getFeatureFlags(): FeatureFlags {\r\n  if (typeof window === 'undefined') {\r\n    return defaultFlags;\r\n  }\r\n\r\n  try {\r\n    const stored = localStorage.getItem('feature-flags');\r\n    if (stored) {\r\n      return { ...defaultFlags, ...JSON.parse(stored) };\r\n    }\r\n  } catch (error) {\r\n    console.warn('Failed to parse feature flags from localStorage:', error);\r\n  }\r\n\r\n  return defaultFlags;\r\n}\r\n\r\nexport function setFeatureFlag(flag: keyof FeatureFlags, value: boolean): void {\r\n  if (typeof window === 'undefined') return;\r\n\r\n  try {\r\n    const current = getFeatureFlags();\r\n    const updated = { ...current, [flag]: value };\r\n    localStorage.setItem('feature-flags', JSON.stringify(updated));\r\n  } catch (error) {\r\n    console.warn('Failed to save feature flags to localStorage:', error);\r\n  }\r\n}\r\n\r\nexport function useFeatureFlags() {\r\n  const flags = getFeatureFlags();\r\n  \r\n  return {\r\n    flags,\r\n    setFlag: setFeatureFlag,\r\n    canPurchase: flags.enableCoursePurchase,\r\n    canEnrollWithCode: flags.enableEnrollmentCode,\r\n    canPreviewCourse: flags.enableCoursePreview,\r\n    hasPaymentIntegration: flags.enablePaymentIntegration,\r\n  };\r\n}"], "names": [], "mappings": ";;;;;AAOA,MAAM,eAA6B;IACjC,sBAAsB;IACtB,sBAAsB;IACtB,qBAAqB;IACrB,0BAA0B;AAC5B;AAEO,SAAS;IACd,uCAAmC;;IAEnC;IAEA,IAAI;QACF,MAAM,SAAS,aAAa,OAAO,CAAC;QACpC,IAAI,QAAQ;YACV,OAAO;gBAAE,GAAG,YAAY;gBAAE,GAAG,KAAK,KAAK,CAAC,OAAO;YAAC;QAClD;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,oDAAoD;IACnE;IAEA,OAAO;AACT;AAEO,SAAS,eAAe,IAAwB,EAAE,KAAc;IACrE,uCAAmC;;IAAM;IAEzC,IAAI;QACF,MAAM,UAAU;QAChB,MAAM,UAAU;YAAE,GAAG,OAAO;YAAE,CAAC,KAAK,EAAE;QAAM;QAC5C,aAAa,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAAC;IACvD,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,iDAAiD;IAChE;AACF;AAEO,SAAS;IACd,MAAM,QAAQ;IAEd,OAAO;QACL;QACA,SAAS;QACT,aAAa,MAAM,oBAAoB;QACvC,mBAAmB,MAAM,oBAAoB;QAC7C,kBAAkB,MAAM,mBAAmB;QAC3C,uBAAuB,MAAM,wBAAwB;IACvD;AACF", "debugId": null}}, {"offset": {"line": 721, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from 'react';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { cva, type VariantProps } from 'class-variance-authority';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst badgeVariants = cva(\r\n  'inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden',\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          'border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90',\r\n        secondary:\r\n          'border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90',\r\n        destructive:\r\n          'border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\r\n        outline:\r\n          'text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground'\r\n      }\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default'\r\n    }\r\n  }\r\n);\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<'span'> &\r\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : 'span';\r\n\r\n  return (\r\n    <Comp\r\n      data-slot='badge'\r\n      className={cn(badgeVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Badge, badgeVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 773, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/lms/course-preview-card.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { Card } from '@/components/ui/card';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport {\r\n  BookOpen01Icon as BookOpenIcon,\r\n  UserIcon as UsersIcon,\r\n  ArrowRight01Icon as ArrowRightIcon,\r\n  ShoppingCart01Icon as ShoppingCartIcon,\r\n  DoorLockIcon as LockIcon\r\n} from 'hugeicons-react';\r\nimport Image from 'next/image'; // Import the Image component\r\nimport { CoursePreviewProps } from '@/types/lms';\r\nimport { useFeatureFlags } from '@/lib/feature-flags';\r\n\r\nconst CoursePreviewCard: React.FC<CoursePreviewProps> = ({\r\n  course,\r\n  onEnroll,\r\n  onPurchase,\r\n  onClick,\r\n  isEnrolled = false\r\n}) => {\r\n  const { canPurchase, canEnrollWithCode } = useFeatureFlags();\r\n\r\n  const formatPrice = (price: number, currency: string = 'IDR') => {\r\n    if (currency === 'IDR') {\r\n      return 'Rp' + new Intl.NumberFormat('id-ID').format(price);\r\n    }\r\n    return new Intl.NumberFormat('id-ID', {\r\n      style: 'currency',\r\n      currency: currency\r\n    }).format(price);\r\n  };\r\n\r\n  const renderActionButton = () => {\r\n    // If already enrolled, show enrolled status\r\n    if (isEnrolled) {\r\n      return (\r\n        <Button\r\n          onClick={(e) => {\r\n            e.stopPropagation();\r\n            onClick?.();\r\n          }}\r\n          variant=\"outline\"\r\n          className=\"w-full bg-green-50 border-green-200 text-green-700 hover:bg-green-100\"\r\n          disabled\r\n        >\r\n          <BookOpenIcon className=\"mr-2 h-4 w-4\" />\r\n          Sudah Terdaftar\r\n        </Button>\r\n      );\r\n    }\r\n\r\n    if (course.previewMode) {\r\n      return (\r\n        <Button\r\n          onClick={(e) => {\r\n            e.stopPropagation();\r\n            onClick?.();\r\n          }}\r\n          variant=\"outline\"\r\n          className=\"w-full\"\r\n        >\r\n          <BookOpenIcon className=\"mr-2 h-4 w-4\" />\r\n          Lihat Detail\r\n          <ArrowRightIcon className=\"ml-2 h-4 w-4\" />\r\n        </Button>\r\n      );\r\n    }\r\n\r\n    if (course.isPurchasable && canPurchase) {\r\n      return (\r\n        <div className=\"flex flex-col gap-2\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <span className=\"text-2xl font-bold text-[var(--iai-primary)]\">\r\n              {course.price ? formatPrice(course.price, course.currency) : 'Gratis'}\r\n            </span>\r\n          </div>\r\n          <Button\r\n            onClick={(e) => {\r\n              e.stopPropagation();\r\n              onPurchase?.();\r\n            }}\r\n            variant=\"iai\"\r\n            className=\"w-full\"\r\n          >\r\n            <ShoppingCartIcon className=\"mr-2 h-4 w-4\" />\r\n            Beli Kursus\r\n          </Button>\r\n          {canEnrollWithCode && course.enrollmentCode && (\r\n            <Button\r\n              onClick={(e) => {\r\n                e.stopPropagation();\r\n                onEnroll?.();\r\n              }}\r\n              variant=\"outline\"\r\n              className=\"w-full\"\r\n            >\r\n              <LockIcon className=\"mr-2 h-4 w-4\" />\r\n              Gunakan Kode Pendaftaran\r\n            </Button>\r\n          )}\r\n        </div>\r\n      );\r\n    }\r\n\r\n    if (canEnrollWithCode && course.enrollmentCode) {\r\n      return (\r\n        <Button\r\n          onClick={(e) => {\r\n            e.stopPropagation();\r\n            onEnroll?.();\r\n          }}\r\n          className=\"w-full\"\r\n        >\r\n          <LockIcon className=\"mr-2 h-4 w-4\" />\r\n          Daftar Sekarang\r\n        </Button>\r\n      );\r\n    }\r\n\r\n    return (\r\n      <Button\r\n        onClick={(e) => {\r\n          e.stopPropagation();\r\n          onClick?.();\r\n        }}\r\n        variant=\"outline\"\r\n        className=\"w-full\"\r\n      >\r\n        <BookOpenIcon className=\"mr-2 h-4 w-4\" />\r\n        Lihat Kursus\r\n      </Button>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <Card className=\"group cursor-pointer transition-all hover:shadow-lg hover:scale-[1.02] overflow-hidden p-0 h-full\" onClick={onClick}>\r\n      <div className=\"flex flex-col h-full\">\r\n        {/* Course Image/Thumbnail */}\r\n        <div className=\"aspect-[4/3] relative overflow-hidden bg-gray-100 flex-shrink-0\">\r\n          {course.thumbnail ? (\r\n            <Image \r\n              src={course.thumbnail} \r\n              alt={course.name}\r\n              fill\r\n              className=\"object-cover w-full h-full\"\r\n              sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\r\n              priority={false}\r\n            />\r\n          ) : (\r\n            <>\r\n              <div className=\"w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center\">\r\n                <div className=\"absolute inset-0 bg-black/20\" />\r\n                <div className=\"relative z-10 text-center text-white\">\r\n                  <BookOpenIcon className=\"h-12 w-12 mx-auto mb-1 opacity-80\" />\r\n                  <p className=\"text-xs font-medium\">{course.code}</p>\r\n                </div>\r\n              </div>\r\n            </>\r\n          )}\r\n          {course.isPurchasable && (\r\n            <Badge \r\n              className=\"absolute top-3 right-3 bg-[var(--iai-primary)] hover:bg-[var(--iai-primary)]\"\r\n              variant=\"default\"\r\n            >\r\n              {course.price ? formatPrice(course.price, course.currency) : 'Gratis'}\r\n            </Badge>\r\n          )}\r\n        </div>\r\n\r\n        {/* Content area that grows to fill available space */}\r\n        <div className=\"flex flex-col flex-grow p-4\">\r\n          {/* Course Header - Fixed height area */}\r\n          <div className=\"space-y-1 mb-3 min-h-[60px]\">\r\n            <h3 className=\"text-lg font-bold group-hover:text-blue-600 transition-colors line-clamp-2\">\r\n              {course.name}\r\n            </h3>\r\n            <p className=\"text-gray-600 text-xs line-clamp-2\">\r\n              {course.description}\r\n            </p>\r\n          </div>\r\n\r\n          {/* Course Meta Information - Fixed height area */}\r\n          <div className=\"space-y-1 mb-4 text-xs text-gray-600 min-h-[32px]\">\r\n            <div className=\"flex items-center\">\r\n              <UsersIcon className=\"mr-1 h-3 w-3 flex-shrink-0\" />\r\n              <span className=\"line-clamp-1\">{course.instructor}</span>\r\n            </div>\r\n            <div className=\"flex items-center\">\r\n              <BookOpenIcon className=\"mr-1 h-3 w-3 flex-shrink-0\" />\r\n              <span>{course.modules.length} modul</span>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Spacer to push button to bottom */}\r\n          <div className=\"flex-grow\" />\r\n\r\n          {/* Action Button - Always at bottom */}\r\n          <div className=\"mt-auto\">\r\n            {renderActionButton()}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </Card>\r\n  );\r\n};\r\n\r\nexport default CoursePreviewCard;"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA,kOAAgC,6BAA6B;AAE7D;;;AAfA;;;;;;;AAiBA,MAAM,oBAAkD,CAAC,EACvD,MAAM,EACN,QAAQ,EACR,UAAU,EACV,OAAO,EACP,aAAa,KAAK,EACnB;;IACC,MAAM,EAAE,WAAW,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,kBAAe,AAAD;IAEzD,MAAM,cAAc,CAAC,OAAe,WAAmB,KAAK;QAC1D,IAAI,aAAa,OAAO;YACtB,OAAO,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;QACtD;QACA,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,qBAAqB;QACzB,4CAA4C;QAC5C,IAAI,YAAY;YACd,qBACE,6LAAC,qIAAA,CAAA,SAAM;gBACL,SAAS,CAAC;oBACR,EAAE,eAAe;oBACjB;gBACF;gBACA,SAAQ;gBACR,WAAU;gBACV,QAAQ;;kCAER,6LAAC,+NAAA,CAAA,iBAAY;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;QAI/C;QAEA,IAAI,OAAO,WAAW,EAAE;YACtB,qBACE,6LAAC,qIAAA,CAAA,SAAM;gBACL,SAAS,CAAC;oBACR,EAAE,eAAe;oBACjB;gBACF;gBACA,SAAQ;gBACR,WAAU;;kCAEV,6LAAC,+NAAA,CAAA,iBAAY;wBAAC,WAAU;;;;;;oBAAiB;kCAEzC,6LAAC,mOAAA,CAAA,mBAAc;wBAAC,WAAU;;;;;;;;;;;;QAGhC;QAEA,IAAI,OAAO,aAAa,IAAI,aAAa;YACvC,qBACE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCACb,OAAO,KAAK,GAAG,YAAY,OAAO,KAAK,EAAE,OAAO,QAAQ,IAAI;;;;;;;;;;;kCAGjE,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS,CAAC;4BACR,EAAE,eAAe;4BACjB;wBACF;wBACA,SAAQ;wBACR,WAAU;;0CAEV,6LAAC,uOAAA,CAAA,qBAAgB;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;oBAG9C,qBAAqB,OAAO,cAAc,kBACzC,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS,CAAC;4BACR,EAAE,eAAe;4BACjB;wBACF;wBACA,SAAQ;wBACR,WAAU;;0CAEV,6LAAC,0NAAA,CAAA,eAAQ;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;QAM/C;QAEA,IAAI,qBAAqB,OAAO,cAAc,EAAE;YAC9C,qBACE,6LAAC,qIAAA,CAAA,SAAM;gBACL,SAAS,CAAC;oBACR,EAAE,eAAe;oBACjB;gBACF;gBACA,WAAU;;kCAEV,6LAAC,0NAAA,CAAA,eAAQ;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;QAI3C;QAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;YACL,SAAS,CAAC;gBACR,EAAE,eAAe;gBACjB;YACF;YACA,SAAQ;YACR,WAAU;;8BAEV,6LAAC,+NAAA,CAAA,iBAAY;oBAAC,WAAU;;;;;;gBAAiB;;;;;;;IAI/C;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;QAAoG,SAAS;kBAC3H,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;wBACZ,OAAO,SAAS,iBACf,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAK,OAAO,SAAS;4BACrB,KAAK,OAAO,IAAI;4BAChB,IAAI;4BACJ,WAAU;4BACV,OAAM;4BACN,UAAU;;;;;iDAGZ;sCACE,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+NAAA,CAAA,iBAAY;gDAAC,WAAU;;;;;;0DACxB,6LAAC;gDAAE,WAAU;0DAAuB,OAAO,IAAI;;;;;;;;;;;;;;;;;;;wBAKtD,OAAO,aAAa,kBACnB,6LAAC,oIAAA,CAAA,QAAK;4BACJ,WAAU;4BACV,SAAQ;sCAEP,OAAO,KAAK,GAAG,YAAY,OAAO,KAAK,EAAE,OAAO,QAAQ,IAAI;;;;;;;;;;;;8BAMnE,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX,OAAO,IAAI;;;;;;8CAEd,6LAAC;oCAAE,WAAU;8CACV,OAAO,WAAW;;;;;;;;;;;;sCAKvB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,iNAAA,CAAA,WAAS;4CAAC,WAAU;;;;;;sDACrB,6LAAC;4CAAK,WAAU;sDAAgB,OAAO,UAAU;;;;;;;;;;;;8CAEnD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+NAAA,CAAA,iBAAY;4CAAC,WAAU;;;;;;sDACxB,6LAAC;;gDAAM,OAAO,OAAO,CAAC,MAAM;gDAAC;;;;;;;;;;;;;;;;;;;sCAKjC,6LAAC;4BAAI,WAAU;;;;;;sCAGf,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb;GA/LM;;QAOuC,iIAAA,CAAA,kBAAe;;;KAPtD;uCAiMS", "debugId": null}}, {"offset": {"line": 1197, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Tabs = TabsPrimitive.Root\r\n\r\nconst TabsList = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.List>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.List\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsList.displayName = TabsPrimitive.List.displayName\r\n\r\nconst TabsTrigger = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm cursor-pointer\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\r\n\r\nconst TabsContent = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Content\r\n    ref={ref}\r\n    className={cn(\r\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsContent.displayName = TabsPrimitive.Content.displayName\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent }"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,mKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG,mKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sZACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1263, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/lms/course-detail-tabs.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport {\r\n  BookOpen01Icon as BookOpenIcon,\r\n  UserIcon as UsersIcon,\r\n  Clock01Icon as ClockIcon,\r\n  Calendar03Icon as CalendarIcon,\r\n  CheckmarkCircle01Icon as CheckCircleIcon,\r\n  DollarCircleIcon,\r\n  Briefcase01Icon as BriefcaseIcon,\r\n  GraduateMaleIcon as GraduationCapIcon,\r\n  StarsIcon as StarIcon,\r\n  Award05Icon as AwardIcon,\r\n  Building01Icon as BuildingIcon,\r\n  CustomerSupportIcon as SupportIcon\r\n} from 'hugeicons-react';\r\nimport { CourseDetailTabsProps } from '@/types/lms';\r\n\r\nconst CourseDetailTabs: React.FC<CourseDetailTabsProps> = ({\r\n  course,\r\n  activeTab,\r\n  onTabChange\r\n}) => {\r\n  const formatPrice = (price: number, currency: string = 'IDR') => {\r\n    if (currency === 'IDR') {\r\n      return 'Rp' + new Intl.NumberFormat('id-ID').format(price);\r\n    }\r\n    return new Intl.NumberFormat('id-ID', {\r\n      style: 'currency',\r\n      currency: currency\r\n    }).format(price);\r\n  };\r\n\r\n  return (\r\n    <Tabs value={activeTab} onValueChange={onTabChange} className=\"w-full\">\r\n      <TabsList className=\"grid w-full grid-cols-6 mb-6\">\r\n        <TabsTrigger value=\"overview\" className=\"flex items-center gap-2\">\r\n          <BookOpenIcon className=\"h-4 w-4\" />\r\n          Ringkasan\r\n        </TabsTrigger>\r\n        <TabsTrigger value=\"admissions\" className=\"flex items-center gap-2\">\r\n          <GraduationCapIcon className=\"h-4 w-4\" />\r\n          Penerimaan\r\n        </TabsTrigger>\r\n        <TabsTrigger value=\"academics\" className=\"flex items-center gap-2\">\r\n          <AwardIcon className=\"h-4 w-4\" />\r\n          Akademik\r\n        </TabsTrigger>\r\n        <TabsTrigger value=\"tuition\" className=\"flex items-center gap-2\">\r\n          <DollarCircleIcon className=\"h-4 w-4\" />\r\n          Biaya\r\n        </TabsTrigger>\r\n        <TabsTrigger value=\"careers\" className=\"flex items-center gap-2\">\r\n          <BriefcaseIcon className=\"h-4 w-4\" />\r\n          Karier\r\n        </TabsTrigger>\r\n        <TabsTrigger value=\"experience\" className=\"flex items-center gap-2\">\r\n          <StarIcon className=\"h-4 w-4\" />\r\n          Pengalaman\r\n        </TabsTrigger>\r\n      </TabsList>\r\n\r\n      <TabsContent value=\"overview\" className=\"space-y-6\">\r\n        <Card>\r\n          <CardHeader>\r\n            <CardTitle className=\"flex items-center gap-2\">\r\n              <BookOpenIcon className=\"h-5 w-5\" />\r\n              Ringkasan Kursus\r\n            </CardTitle>\r\n          </CardHeader>\r\n          <CardContent className=\"space-y-4\">\r\n            <div>\r\n              <h3 className=\"font-semibold mb-2\">Deskripsi</h3>\r\n              <p className=\"text-gray-700\">{course.description}</p>\r\n            </div>\r\n            \r\n            <div className=\"grid md:grid-cols-2 gap-4\">\r\n              <div>\r\n                <h4 className=\"font-semibold mb-2\">Detail Kursus</h4>\r\n                <div className=\"space-y-2 text-sm\">\r\n                  <div className=\"flex items-center\">\r\n                    <UsersIcon className=\"mr-2 h-4 w-4 text-gray-500\" />\r\n                    <span>Instruktur: {course.instructor}</span>\r\n                  </div>\r\n                  <div className=\"flex items-center\">\r\n                    <CalendarIcon className=\"mr-2 h-4 w-4 text-gray-500\" />\r\n                    <span>\r\n                      Durasi: {new Date(course.startDate).toLocaleDateString('id-ID')} - {' '}\r\n                      {new Date(course.endDate).toLocaleDateString('id-ID')}\r\n                    </span>\r\n                  </div>\r\n                  <div className=\"flex items-center\">\r\n                    <BookOpenIcon className=\"mr-2 h-4 w-4 text-gray-500\" />\r\n                    <span>{course.modules.length} modul</span>\r\n                  </div>\r\n                  <div className=\"flex items-center\">\r\n                    <CheckCircleIcon className=\"mr-2 h-4 w-4 text-gray-500\" />\r\n                    <span>Nilai kelulusan: {course.minPassingScore}%</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              \r\n              <div>\r\n                <h4 className=\"font-semibold mb-2\">Apa yang Akan Anda Pelajari</h4>\r\n                <div className=\"space-y-2\">\r\n                  {course.modules.slice(0, 3).map((module, index) => (\r\n                    <div key={module.id} className=\"flex items-start gap-2\">\r\n                      <CheckCircleIcon className=\"h-4 w-4 text-green-600 mt-0.5 flex-shrink-0\" />\r\n                      <span className=\"text-sm\">{module.title}</span>\r\n                    </div>\r\n                  ))}\r\n                  {course.modules.length > 3 && (\r\n                    <div className=\"text-sm text-gray-500\">\r\n                      +{course.modules.length - 3} modul lagi\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div>\r\n              <h4 className=\"font-semibold mb-2\">Sertifikat</h4>\r\n              <div className=\"flex items-center gap-2\">\r\n                {course.certificate.isEligible ? (\r\n                  <Badge variant=\"default\" className=\"bg-green-100 text-green-800\">\r\n                    <AwardIcon className=\"mr-1 h-3 w-3\" />\r\n                    Sertifikat Tersedia\r\n                  </Badge>\r\n                ) : (\r\n                  <Badge variant=\"secondary\">\r\n                    Tidak Ada Sertifikat\r\n                  </Badge>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n      </TabsContent>\r\n\r\n      <TabsContent value=\"admissions\" className=\"space-y-6\">\r\n        <Card>\r\n          <CardHeader>\r\n            <CardTitle className=\"flex items-center gap-2\">\r\n              <GraduationCapIcon className=\"h-5 w-5\" />\r\n              Informasi Penerimaan\r\n            </CardTitle>\r\n          </CardHeader>\r\n          <CardContent className=\"space-y-4\">\r\n            {course.admissions?.requirements && (\r\n              <div>\r\n                <h4 className=\"font-semibold mb-2\">Persyaratan</h4>\r\n                <ul className=\"space-y-1\">\r\n                  {course.admissions.requirements.map((req, index) => (\r\n                    <li key={index} className=\"flex items-start gap-2\">\r\n                      <CheckCircleIcon className=\"h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0\" />\r\n                      <span className=\"text-sm\">{req}</span>\r\n                    </li>\r\n                  ))}\r\n                </ul>\r\n              </div>\r\n            )}\r\n\r\n            {course.admissions?.prerequisites && (\r\n              <div>\r\n                <h4 className=\"font-semibold mb-2\">Prasyarat</h4>\r\n                <ul className=\"space-y-1\">\r\n                  {course.admissions.prerequisites.map((prereq, index) => (\r\n                    <li key={index} className=\"flex items-start gap-2\">\r\n                      <BookOpenIcon className=\"h-4 w-4 text-orange-600 mt-0.5 flex-shrink-0\" />\r\n                      <span className=\"text-sm\">{prereq}</span>\r\n                    </li>\r\n                  ))}\r\n                </ul>\r\n              </div>\r\n            )}\r\n\r\n            {course.admissions?.applicationDeadline && (\r\n              <div>\r\n                <h4 className=\"font-semibold mb-2\">Batas Waktu Pendaftaran</h4>\r\n                <div className=\"flex items-center gap-2\">\r\n                  <CalendarIcon className=\"h-4 w-4 text-red-600\" />\r\n                  <span className=\"text-sm\">\r\n                    {new Date(course.admissions.applicationDeadline).toLocaleDateString('id-ID')}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            <div>\r\n              <h4 className=\"font-semibold mb-2\">Opsi Pendaftaran</h4>\r\n              <div className=\"flex flex-wrap gap-2\">\r\n                {course.enrollmentType === 'code' && (\r\n                  <Badge variant=\"outline\">Kode Pendaftaran Diperlukan</Badge>\r\n                )}\r\n                {course.enrollmentType === 'invitation' && (\r\n                  <Badge variant=\"outline\">Hanya Undangan</Badge>\r\n                )}\r\n                {course.enrollmentType === 'purchase' && (\r\n                  <Badge variant=\"outline\">Pembelian Langsung</Badge>\r\n                )}\r\n                {course.enrollmentType === 'both' && (\r\n                  <>\r\n                    <Badge variant=\"outline\">Kode Pendaftaran</Badge>\r\n                    <Badge variant=\"outline\">Pembelian Langsung</Badge>\r\n                  </>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n      </TabsContent>\r\n\r\n      <TabsContent value=\"academics\" className=\"space-y-6\">\r\n        <Card>\r\n          <CardHeader>\r\n            <CardTitle className=\"flex items-center gap-2\">\r\n              <AwardIcon className=\"h-5 w-5\" />\r\n              Informasi Akademik\r\n            </CardTitle>\r\n          </CardHeader>\r\n          <CardContent className=\"space-y-4\">\r\n            {course.academics && (\r\n              <div className=\"grid md:grid-cols-2 gap-4\">\r\n                <div>\r\n                  <h4 className=\"font-semibold mb-2\">Struktur Kursus</h4>\r\n                  <div className=\"space-y-2 text-sm\">\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <span>Kredit:</span>\r\n                      <Badge variant=\"secondary\">{course.academics.credits}</Badge>\r\n                    </div>\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <span>Beban Kerja:</span>\r\n                      <span className=\"font-medium\">{course.academics.workload}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                \r\n                <div>\r\n                  <h4 className=\"font-semibold mb-2\">Metode Penilaian</h4>\r\n                  <ul className=\"space-y-1\">\r\n                    {course.academics.assessment.map((method, index) => (\r\n                      <li key={index} className=\"flex items-start gap-2\">\r\n                        <CheckCircleIcon className=\"h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0\" />\r\n                        <span className=\"text-sm\">{method}</span>\r\n                      </li>\r\n                    ))}\r\n                  </ul>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            <div>\r\n              <h4 className=\"font-semibold mb-2\">Modul Kursus</h4>\r\n              <div className=\"space-y-2\">\r\n                {course.modules.map((module, index) => (\r\n                  <Card key={module.id} className=\"p-3\">\r\n                    <div className=\"flex items-start justify-between\">\r\n                      <div>\r\n                        <h5 className=\"font-medium\">{module.title}</h5>\r\n                        <p className=\"text-sm text-gray-600\">{module.description}</p>\r\n                        <div className=\"mt-2 text-sm text-gray-500\">\r\n                          {module.chapters.length} bab\r\n                        </div>\r\n                      </div>\r\n                      <Badge variant=\"outline\">Modul {index + 1}</Badge>\r\n                    </div>\r\n                  </Card>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n      </TabsContent>\r\n\r\n      <TabsContent value=\"tuition\" className=\"space-y-6\">\r\n        <Card>\r\n          <CardHeader>\r\n            <CardTitle className=\"flex items-center gap-2\">\r\n              <DollarCircleIcon className=\"h-5 w-5\" />\r\n              Biaya & Pembiayaan\r\n            </CardTitle>\r\n          </CardHeader>\r\n          <CardContent className=\"space-y-4\">\r\n            {course.tuitionAndFinancing ? (\r\n              <>\r\n                <div>\r\n                  <h4 className=\"font-semibold mb-2\">Biaya Kursus</h4>\r\n                  <div className=\"text-3xl font-bold text-green-600\">\r\n                    {formatPrice(course.tuitionAndFinancing.totalCost, course.currency)}\r\n                  </div>\r\n                  {course.price && course.price < course.tuitionAndFinancing.totalCost && (\r\n                    <div className=\"text-sm text-gray-600\">\r\n                      Penawaran khusus: {formatPrice(course.price, course.currency)}\r\n                      <Badge variant=\"destructive\" className=\"ml-2\">\r\n                        {Math.round(((course.tuitionAndFinancing.totalCost - course.price) / course.tuitionAndFinancing.totalCost) * 100)}% OFF\r\n                      </Badge>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n\r\n                <div>\r\n                  <h4 className=\"font-semibold mb-2\">Opsi Pembayaran</h4>\r\n                  <ul className=\"space-y-1\">\r\n                    {course.tuitionAndFinancing.paymentOptions.map((option, index) => (\r\n                      <li key={index} className=\"flex items-start gap-2\">\r\n                        <CheckCircleIcon className=\"h-4 w-4 text-green-600 mt-0.5 flex-shrink-0\" />\r\n                        <span className=\"text-sm\">{option}</span>\r\n                      </li>\r\n                    ))}\r\n                  </ul>\r\n                </div>\r\n\r\n                {course.tuitionAndFinancing.scholarships && (\r\n                  <div>\r\n                    <h4 className=\"font-semibold mb-2\">Beasiswa Tersedia</h4>\r\n                    <ul className=\"space-y-1\">\r\n                      {course.tuitionAndFinancing.scholarships.map((scholarship, index) => (\r\n                        <li key={index} className=\"flex items-start gap-2\">\r\n                          <AwardIcon className=\"h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0\" />\r\n                          <span className=\"text-sm\">{scholarship}</span>\r\n                        </li>\r\n                      ))}\r\n                    </ul>\r\n                  </div>\r\n                )}\r\n              </>\r\n            ) : (\r\n              <div className=\"text-center py-8\">\r\n                <div className=\"text-4xl font-bold text-green-600 mb-2\">GRATIS</div>\r\n                <p className=\"text-gray-600\">Kursus ini tersedia tanpa biaya</p>\r\n              </div>\r\n            )}\r\n          </CardContent>\r\n        </Card>\r\n      </TabsContent>\r\n\r\n      <TabsContent value=\"careers\" className=\"space-y-6\">\r\n        <Card>\r\n          <CardHeader>\r\n            <CardTitle className=\"flex items-center gap-2\">\r\n              <BriefcaseIcon className=\"h-5 w-5\" />\r\n              Prospek Karier\r\n            </CardTitle>\r\n          </CardHeader>\r\n          <CardContent className=\"space-y-4\">\r\n            {course.careers ? (\r\n              <>\r\n                <div>\r\n                  <h4 className=\"font-semibold mb-2\">Prospek Karier</h4>\r\n                  <ul className=\"space-y-1\">\r\n                    {course.careers.outcomes.map((outcome, index) => (\r\n                      <li key={index} className=\"flex items-start gap-2\">\r\n                        <CheckCircleIcon className=\"h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0\" />\r\n                        <span className=\"text-sm\">{outcome}</span>\r\n                      </li>\r\n                    ))}\r\n                  </ul>\r\n                </div>\r\n\r\n                <div>\r\n                  <h4 className=\"font-semibold mb-2\">Industri</h4>\r\n                  <div className=\"flex flex-wrap gap-2\">\r\n                    {course.careers.industries.map((industry, index) => (\r\n                      <Badge key={index} variant=\"outline\">\r\n                        {industry}\r\n                      </Badge>\r\n                    ))}\r\n                  </div>\r\n                </div>\r\n\r\n                {course.careers.averageSalary && (\r\n                  <div>\r\n                    <h4 className=\"font-semibold mb-2\">Gaji Rata-rata</h4>\r\n                    <div className=\"text-2xl font-bold text-green-600\">\r\n                      {course.careers.averageSalary}\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </>\r\n            ) : (\r\n              <div className=\"text-center py-8\">\r\n                <BriefcaseIcon className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\r\n                <p className=\"text-gray-600\">Informasi karier tidak tersedia</p>\r\n              </div>\r\n            )}\r\n          </CardContent>\r\n        </Card>\r\n      </TabsContent>\r\n\r\n      <TabsContent value=\"experience\" className=\"space-y-6\">\r\n        <Card>\r\n          <CardHeader>\r\n            <CardTitle className=\"flex items-center gap-2\">\r\n              <StarIcon className=\"h-5 w-5\" />\r\n              Pengalaman Mahasiswa\r\n            </CardTitle>\r\n          </CardHeader>\r\n          <CardContent className=\"space-y-6\">\r\n            {course.studentExperience ? (\r\n              <>\r\n                {course.studentExperience.testimonials.length > 0 && (\r\n                  <div>\r\n                    <h4 className=\"font-semibold mb-4\">Testimoni Mahasiswa</h4>\r\n                    <div className=\"space-y-4\">\r\n                      {course.studentExperience.testimonials.map((testimonial, index) => (\r\n                        <Card key={index} className=\"p-4\">\r\n                          <div className=\"flex items-start gap-3\">\r\n                            <div className=\"flex-shrink-0\">\r\n                              <div className=\"h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center\">\r\n                                <UsersIcon className=\"h-5 w-5 text-blue-600\" />\r\n                              </div>\r\n                            </div>\r\n                            <div>\r\n                              <p className=\"text-sm italic mb-2\">&quot;{testimonial.feedback}&quot;</p>\r\n                              <p className=\"font-medium text-sm\">- {testimonial.name}</p>\r\n                            </div>\r\n                          </div>\r\n                        </Card>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                )}\r\n\r\n                <div className=\"grid md:grid-cols-2 gap-6\">\r\n                  {course.studentExperience.facilities.length > 0 && (\r\n                    <div>\r\n                      <h4 className=\"font-semibold mb-2 flex items-center gap-2\">\r\n                        <BuildingIcon className=\"h-4 w-4\" />\r\n                        Fasilitas\r\n                      </h4>\r\n                      <ul className=\"space-y-1\">\r\n                        {course.studentExperience.facilities.map((facility, index) => (\r\n                          <li key={index} className=\"flex items-start gap-2\">\r\n                            <CheckCircleIcon className=\"h-4 w-4 text-green-600 mt-0.5 flex-shrink-0\" />\r\n                            <span className=\"text-sm\">{facility}</span>\r\n                          </li>\r\n                        ))}\r\n                      </ul>\r\n                    </div>\r\n                  )}\r\n\r\n                  {course.studentExperience.support.length > 0 && (\r\n                    <div>\r\n                      <h4 className=\"font-semibold mb-2 flex items-center gap-2\">\r\n                        <SupportIcon className=\"h-4 w-4\" />\r\n                        Dukungan Mahasiswa\r\n                      </h4>\r\n                      <ul className=\"space-y-1\">\r\n                        {course.studentExperience.support.map((supportItem, index) => (\r\n                          <li key={index} className=\"flex items-start gap-2\">\r\n                            <CheckCircleIcon className=\"h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0\" />\r\n                            <span className=\"text-sm\">{supportItem}</span>\r\n                          </li>\r\n                        ))}\r\n                      </ul>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </>\r\n            ) : (\r\n              <div className=\"text-center py-8\">\r\n                <StarIcon className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\r\n                <p className=\"text-gray-600\">Informasi pengalaman mahasiswa tidak tersedia</p>\r\n              </div>\r\n            )}\r\n          </CardContent>\r\n        </Card>\r\n      </TabsContent>\r\n    </Tabs>\r\n  );\r\n};\r\n\r\nexport default CourseDetailTabs;"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;AAsBA,MAAM,mBAAoD,CAAC,EACzD,MAAM,EACN,SAAS,EACT,WAAW,EACZ;IACC,MAAM,cAAc,CAAC,OAAe,WAAmB,KAAK;QAC1D,IAAI,aAAa,OAAO;YACtB,OAAO,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;QACtD;QACA,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,OAAO;QAAW,eAAe;QAAa,WAAU;;0BAC5D,6LAAC,mIAAA,CAAA,WAAQ;gBAAC,WAAU;;kCAClB,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAW,WAAU;;0CACtC,6LAAC,+NAAA,CAAA,iBAAY;gCAAC,WAAU;;;;;;4BAAY;;;;;;;kCAGtC,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAa,WAAU;;0CACxC,6LAAC,kOAAA,CAAA,mBAAiB;gCAAC,WAAU;;;;;;4BAAY;;;;;;;kCAG3C,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAY,WAAU;;0CACvC,6LAAC,wNAAA,CAAA,cAAS;gCAAC,WAAU;;;;;;4BAAY;;;;;;;kCAGnC,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAU,WAAU;;0CACrC,6LAAC,kOAAA,CAAA,mBAAgB;gCAAC,WAAU;;;;;;4BAAY;;;;;;;kCAG1C,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAU,WAAU;;0CACrC,6LAAC,gOAAA,CAAA,kBAAa;gCAAC,WAAU;;;;;;4BAAY;;;;;;;kCAGvC,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAa,WAAU;;0CACxC,6LAAC,mNAAA,CAAA,YAAQ;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;;0BAKpC,6LAAC,mIAAA,CAAA,cAAW;gBAAC,OAAM;gBAAW,WAAU;0BACtC,cAAA,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,+NAAA,CAAA,iBAAY;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;sCAIxC,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,6LAAC;4CAAE,WAAU;sDAAiB,OAAO,WAAW;;;;;;;;;;;;8CAGlD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAqB;;;;;;8DACnC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,iNAAA,CAAA,WAAS;oEAAC,WAAU;;;;;;8EACrB,6LAAC;;wEAAK;wEAAa,OAAO,UAAU;;;;;;;;;;;;;sEAEtC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,8NAAA,CAAA,iBAAY;oEAAC,WAAU;;;;;;8EACxB,6LAAC;;wEAAK;wEACK,IAAI,KAAK,OAAO,SAAS,EAAE,kBAAkB,CAAC;wEAAS;wEAAI;wEACnE,IAAI,KAAK,OAAO,OAAO,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;sEAGjD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,+NAAA,CAAA,iBAAY;oEAAC,WAAU;;;;;;8EACxB,6LAAC;;wEAAM,OAAO,OAAO,CAAC,MAAM;wEAAC;;;;;;;;;;;;;sEAE/B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,6OAAA,CAAA,wBAAe;oEAAC,WAAU;;;;;;8EAC3B,6LAAC;;wEAAK;wEAAkB,OAAO,eAAe;wEAAC;;;;;;;;;;;;;;;;;;;;;;;;;sDAKrD,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAqB;;;;;;8DACnC,6LAAC;oDAAI,WAAU;;wDACZ,OAAO,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,QAAQ,sBACvC,6LAAC;gEAAoB,WAAU;;kFAC7B,6LAAC,6OAAA,CAAA,wBAAe;wEAAC,WAAU;;;;;;kFAC3B,6LAAC;wEAAK,WAAU;kFAAW,OAAO,KAAK;;;;;;;+DAF/B,OAAO,EAAE;;;;;wDAKpB,OAAO,OAAO,CAAC,MAAM,GAAG,mBACvB,6LAAC;4DAAI,WAAU;;gEAAwB;gEACnC,OAAO,OAAO,CAAC,MAAM,GAAG;gEAAE;;;;;;;;;;;;;;;;;;;;;;;;;8CAOtC,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,6LAAC;4CAAI,WAAU;sDACZ,OAAO,WAAW,CAAC,UAAU,iBAC5B,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAU;;kEACjC,6LAAC,wNAAA,CAAA,cAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;qEAIxC,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUvC,6LAAC,mIAAA,CAAA,cAAW;gBAAC,OAAM;gBAAa,WAAU;0BACxC,cAAA,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,kOAAA,CAAA,mBAAiB;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;sCAI7C,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;gCACpB,OAAO,UAAU,EAAE,8BAClB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,6LAAC;4CAAG,WAAU;sDACX,OAAO,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,KAAK,sBACxC,6LAAC;oDAAe,WAAU;;sEACxB,6LAAC,6OAAA,CAAA,wBAAe;4DAAC,WAAU;;;;;;sEAC3B,6LAAC;4DAAK,WAAU;sEAAW;;;;;;;mDAFpB;;;;;;;;;;;;;;;;gCAShB,OAAO,UAAU,EAAE,+BAClB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,6LAAC;4CAAG,WAAU;sDACX,OAAO,UAAU,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC5C,6LAAC;oDAAe,WAAU;;sEACxB,6LAAC,+NAAA,CAAA,iBAAY;4DAAC,WAAU;;;;;;sEACxB,6LAAC;4DAAK,WAAU;sEAAW;;;;;;;mDAFpB;;;;;;;;;;;;;;;;gCAShB,OAAO,UAAU,EAAE,qCAClB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,8NAAA,CAAA,iBAAY;oDAAC,WAAU;;;;;;8DACxB,6LAAC;oDAAK,WAAU;8DACb,IAAI,KAAK,OAAO,UAAU,CAAC,mBAAmB,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;8CAM5E,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,6LAAC;4CAAI,WAAU;;gDACZ,OAAO,cAAc,KAAK,wBACzB,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;gDAE1B,OAAO,cAAc,KAAK,8BACzB,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;gDAE1B,OAAO,cAAc,KAAK,4BACzB,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;gDAE1B,OAAO,cAAc,KAAK,wBACzB;;sEACE,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAU;;;;;;sEACzB,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASvC,6LAAC,mIAAA,CAAA,cAAW;gBAAC,OAAM;gBAAY,WAAU;0BACvC,cAAA,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,wNAAA,CAAA,cAAS;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;sCAIrC,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;gCACpB,OAAO,SAAS,kBACf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAqB;;;;;;8DACnC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;8EAAK;;;;;;8EACN,6LAAC,oIAAA,CAAA,QAAK;oEAAC,SAAQ;8EAAa,OAAO,SAAS,CAAC,OAAO;;;;;;;;;;;;sEAEtD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;8EAAK;;;;;;8EACN,6LAAC;oEAAK,WAAU;8EAAe,OAAO,SAAS,CAAC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;sDAK9D,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAqB;;;;;;8DACnC,6LAAC;oDAAG,WAAU;8DACX,OAAO,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACxC,6LAAC;4DAAe,WAAU;;8EACxB,6LAAC,6OAAA,CAAA,wBAAe;oEAAC,WAAU;;;;;;8EAC3B,6LAAC;oEAAK,WAAU;8EAAW;;;;;;;2DAFpB;;;;;;;;;;;;;;;;;;;;;;8CAUnB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,6LAAC;4CAAI,WAAU;sDACZ,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC3B,6LAAC,mIAAA,CAAA,OAAI;oDAAiB,WAAU;8DAC9B,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;kFAAe,OAAO,KAAK;;;;;;kFACzC,6LAAC;wEAAE,WAAU;kFAAyB,OAAO,WAAW;;;;;;kFACxD,6LAAC;wEAAI,WAAU;;4EACZ,OAAO,QAAQ,CAAC,MAAM;4EAAC;;;;;;;;;;;;;0EAG5B,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAQ;;oEAAU;oEAAO,QAAQ;;;;;;;;;;;;;mDATjC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAmBhC,6LAAC,mIAAA,CAAA,cAAW;gBAAC,OAAM;gBAAU,WAAU;0BACrC,cAAA,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,kOAAA,CAAA,mBAAgB;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;sCAI5C,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACpB,OAAO,mBAAmB,iBACzB;;kDACE,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAqB;;;;;;0DACnC,6LAAC;gDAAI,WAAU;0DACZ,YAAY,OAAO,mBAAmB,CAAC,SAAS,EAAE,OAAO,QAAQ;;;;;;4CAEnE,OAAO,KAAK,IAAI,OAAO,KAAK,GAAG,OAAO,mBAAmB,CAAC,SAAS,kBAClE,6LAAC;gDAAI,WAAU;;oDAAwB;oDAClB,YAAY,OAAO,KAAK,EAAE,OAAO,QAAQ;kEAC5D,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAc,WAAU;;4DACpC,KAAK,KAAK,CAAC,AAAC,CAAC,OAAO,mBAAmB,CAAC,SAAS,GAAG,OAAO,KAAK,IAAI,OAAO,mBAAmB,CAAC,SAAS,GAAI;4DAAK;;;;;;;;;;;;;;;;;;;kDAM1H,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAqB;;;;;;0DACnC,6LAAC;gDAAG,WAAU;0DACX,OAAO,mBAAmB,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACtD,6LAAC;wDAAe,WAAU;;0EACxB,6LAAC,6OAAA,CAAA,wBAAe;gEAAC,WAAU;;;;;;0EAC3B,6LAAC;gEAAK,WAAU;0EAAW;;;;;;;uDAFpB;;;;;;;;;;;;;;;;oCAQd,OAAO,mBAAmB,CAAC,YAAY,kBACtC,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAqB;;;;;;0DACnC,6LAAC;gDAAG,WAAU;0DACX,OAAO,mBAAmB,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,aAAa,sBACzD,6LAAC;wDAAe,WAAU;;0EACxB,6LAAC,wNAAA,CAAA,cAAS;gEAAC,WAAU;;;;;;0EACrB,6LAAC;gEAAK,WAAU;0EAAW;;;;;;;uDAFpB;;;;;;;;;;;;;;;;;6DAUnB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAyC;;;;;;kDACxD,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvC,6LAAC,mIAAA,CAAA,cAAW;gBAAC,OAAM;gBAAU,WAAU;0BACrC,cAAA,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,gOAAA,CAAA,kBAAa;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;sCAIzC,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACpB,OAAO,OAAO,iBACb;;kDACE,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAqB;;;;;;0DACnC,6LAAC;gDAAG,WAAU;0DACX,OAAO,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBACrC,6LAAC;wDAAe,WAAU;;0EACxB,6LAAC,6OAAA,CAAA,wBAAe;gEAAC,WAAU;;;;;;0EAC3B,6LAAC;gEAAK,WAAU;0EAAW;;;;;;;uDAFpB;;;;;;;;;;;;;;;;kDAQf,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAqB;;;;;;0DACnC,6LAAC;gDAAI,WAAU;0DACZ,OAAO,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,UAAU,sBACxC,6LAAC,oIAAA,CAAA,QAAK;wDAAa,SAAQ;kEACxB;uDADS;;;;;;;;;;;;;;;;oCAOjB,OAAO,OAAO,CAAC,aAAa,kBAC3B,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAqB;;;;;;0DACnC,6LAAC;gDAAI,WAAU;0DACZ,OAAO,OAAO,CAAC,aAAa;;;;;;;;;;;;;6DAMrC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,gOAAA,CAAA,kBAAa;wCAAC,WAAU;;;;;;kDACzB,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvC,6LAAC,mIAAA,CAAA,cAAW;gBAAC,OAAM;gBAAa,WAAU;0BACxC,cAAA,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,mNAAA,CAAA,YAAQ;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;sCAIpC,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACpB,OAAO,iBAAiB,iBACvB;;oCACG,OAAO,iBAAiB,CAAC,YAAY,CAAC,MAAM,GAAG,mBAC9C,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAqB;;;;;;0DACnC,6LAAC;gDAAI,WAAU;0DACZ,OAAO,iBAAiB,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,aAAa,sBACvD,6LAAC,mIAAA,CAAA,OAAI;wDAAa,WAAU;kEAC1B,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,iNAAA,CAAA,WAAS;4EAAC,WAAU;;;;;;;;;;;;;;;;8EAGzB,6LAAC;;sFACC,6LAAC;4EAAE,WAAU;;gFAAsB;gFAAO,YAAY,QAAQ;gFAAC;;;;;;;sFAC/D,6LAAC;4EAAE,WAAU;;gFAAsB;gFAAG,YAAY,IAAI;;;;;;;;;;;;;;;;;;;uDATjD;;;;;;;;;;;;;;;;kDAkBnB,6LAAC;wCAAI,WAAU;;4CACZ,OAAO,iBAAiB,CAAC,UAAU,CAAC,MAAM,GAAG,mBAC5C,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC,8NAAA,CAAA,iBAAY;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAGtC,6LAAC;wDAAG,WAAU;kEACX,OAAO,iBAAiB,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,UAAU,sBAClD,6LAAC;gEAAe,WAAU;;kFACxB,6LAAC,6OAAA,CAAA,wBAAe;wEAAC,WAAU;;;;;;kFAC3B,6LAAC;wEAAK,WAAU;kFAAW;;;;;;;+DAFpB;;;;;;;;;;;;;;;;4CAShB,OAAO,iBAAiB,CAAC,OAAO,CAAC,MAAM,GAAG,mBACzC,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC,wOAAA,CAAA,sBAAW;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAGrC,6LAAC;wDAAG,WAAU;kEACX,OAAO,iBAAiB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,aAAa,sBAClD,6LAAC;gEAAe,WAAU;;kFACxB,6LAAC,6OAAA,CAAA,wBAAe;wEAAC,WAAU;;;;;;kFAC3B,6LAAC;wEAAK,WAAU;kFAAW;;;;;;;+DAFpB;;;;;;;;;;;;;;;;;;;;;;;6DAWrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,mNAAA,CAAA,YAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ7C;KApcM;uCAscS", "debugId": null}}, {"offset": {"line": 2950, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/label.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as LabelPrimitive from '@radix-ui/react-label';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Label({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  return (\r\n    <LabelPrimitive.Root\r\n      data-slot='label'\r\n      className={cn(\r\n        'flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Label };\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 2984, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/lms/payment-modal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON>alogContent,\r\n  DialogHeader,\r\n  DialogTitle,\r\n  DialogDescription\r\n} from '@/components/ui/dialog';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Separator } from '@/components/ui/separator';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport {\r\n  CreditCardIcon,\r\n  DollarCircleIcon,\r\n  ShoppingCart01Icon as ShoppingCartIcon,\r\n  CheckmarkCircle01Icon as CheckCircleIcon,\r\n  Cancel01Icon as XCircleIcon,\r\n  LockIcon as LockIcon\r\n} from 'hugeicons-react';\r\nimport { Course } from '@/types/lms';\r\n\r\ninterface PaymentModalProps {\r\n  course: Course;\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  onPaymentSuccess: () => void;\r\n}\r\n\r\nconst PaymentModal: React.FC<PaymentModalProps> = ({\r\n  course,\r\n  isOpen,\r\n  onClose,\r\n  onPaymentSuccess\r\n}) => {\r\n  const [paymentMethod, setPaymentMethod] = useState<'card' | 'paypal' | 'bank'>('card');\r\n  const [isProcessing, setIsProcessing] = useState(false);\r\n  const [paymentStep, setPaymentStep] = useState<'details' | 'processing' | 'success' | 'error'>('details');\r\n  const [formData, setFormData] = useState({\r\n    cardNumber: '',\r\n    expiryDate: '',\r\n    cvv: '',\r\n    cardholderName: '',\r\n    email: '',\r\n    billingAddress: ''\r\n  });\r\n\r\n  const formatPrice = (price: number, currency: string = 'IDR') => {\r\n    if (currency === 'IDR') {\r\n      return 'Rp' + new Intl.NumberFormat('id-ID').format(price);\r\n    }\r\n    return new Intl.NumberFormat('id-ID', {\r\n      style: 'currency',\r\n      currency: currency\r\n    }).format(price);\r\n  };\r\n\r\n  const handleInputChange = (field: string, value: string) => {\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [field]: value\r\n    }));\r\n  };\r\n\r\n  const validateForm = () => {\r\n    if (paymentMethod === 'card') {\r\n      return formData.cardNumber && \r\n             formData.expiryDate && \r\n             formData.cvv && \r\n             formData.cardholderName &&\r\n             formData.email;\r\n    }\r\n    return formData.email;\r\n  };\r\n\r\n  const processPayment = async () => {\r\n    if (!validateForm()) {\r\n      return;\r\n    }\r\n\r\n    setIsProcessing(true);\r\n    setPaymentStep('processing');\r\n\r\n    try {\r\n      // Simulate payment processing\r\n      await new Promise(resolve => setTimeout(resolve, 3000));\r\n      \r\n      // For demo purposes, randomly succeed or fail\r\n      if (Math.random() > 0.1) { // 90% success rate\r\n        setPaymentStep('success');\r\n        setTimeout(() => {\r\n          onPaymentSuccess();\r\n          onClose();\r\n          resetModal();\r\n        }, 2000);\r\n      } else {\r\n        setPaymentStep('error');\r\n      }\r\n    } catch (error) {\r\n      setPaymentStep('error');\r\n    } finally {\r\n      setIsProcessing(false);\r\n    }\r\n  };\r\n\r\n  const resetModal = () => {\r\n    setPaymentStep('details');\r\n    setFormData({\r\n      cardNumber: '',\r\n      expiryDate: '',\r\n      cvv: '',\r\n      cardholderName: '',\r\n      email: '',\r\n      billingAddress: ''\r\n    });\r\n    setIsProcessing(false);\r\n  };\r\n\r\n  const handleClose = () => {\r\n    if (!isProcessing && paymentStep !== 'success') {\r\n      onClose();\r\n      resetModal();\r\n    }\r\n  };\r\n\r\n  const renderPaymentDetails = () => (\r\n    <div className=\"space-y-6\">\r\n      {/* Course Summary */}\r\n      <div className=\"bg-gray-50 rounded-lg p-4\">\r\n        <h4 className=\"font-semibold mb-2\">Ringkasan Kursus</h4>\r\n        <div className=\"flex justify-between items-start mb-2\">\r\n          <div>\r\n            <p className=\"font-medium\">{course.name}</p>\r\n            <p className=\"text-sm text-gray-600\">by {course.instructor}</p>\r\n          </div>\r\n          <div className=\"text-right\">\r\n            <p className=\"text-xl font-bold text-green-600\">\r\n              {course.price ? formatPrice(course.price, course.currency) : 'Free'}\r\n            </p>\r\n          </div>\r\n        </div>\r\n        <div className=\"flex gap-2 text-sm text-gray-600\">\r\n          <Badge variant=\"outline\">{course.modules.length} modul</Badge>\r\n          {course.certificate.isEligible && (\r\n            <Badge variant=\"outline\">Sertifikat disertakan</Badge>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Payment Method Selection */}\r\n      <div>\r\n        <Label className=\"text-base font-semibold mb-3 block\">Metode Pembayaran</Label>\r\n        <div className=\"grid grid-cols-3 gap-2\">\r\n          <Button\r\n            variant={paymentMethod === 'card' ? 'default' : 'outline'}\r\n            onClick={() => setPaymentMethod('card')}\r\n            className=\"h-16 flex-col gap-1\"\r\n          >\r\n            <CreditCardIcon className=\"h-5 w-5\" />\r\n            <span className=\"text-xs\">Kartu Kredit</span>\r\n          </Button>\r\n          <Button\r\n            variant={paymentMethod === 'paypal' ? 'default' : 'outline'}\r\n            onClick={() => setPaymentMethod('paypal')}\r\n            className=\"h-16 flex-col gap-1\"\r\n          >\r\n            <DollarCircleIcon className=\"h-5 w-5\" />\r\n            <span className=\"text-xs\">PayPal</span>\r\n          </Button>\r\n          <Button\r\n            variant={paymentMethod === 'bank' ? 'default' : 'outline'}\r\n            onClick={() => setPaymentMethod('bank')}\r\n            className=\"h-16 flex-col gap-1\"\r\n          >\r\n            <ShoppingCartIcon className=\"h-5 w-5\" />\r\n            <span className=\"text-xs\">Transfer Bank</span>\r\n          </Button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Payment Form */}\r\n      {paymentMethod === 'card' && (\r\n        <div className=\"space-y-4\">\r\n          <div>\r\n            <Label htmlFor=\"email\">Alamat Email</Label>\r\n            <Input\r\n              id=\"email\"\r\n              type=\"email\"\r\n              placeholder=\"<EMAIL>\"\r\n              value={formData.email}\r\n              onChange={(e) => handleInputChange('email', e.target.value)}\r\n            />\r\n          </div>\r\n          \r\n          <div>\r\n            <Label htmlFor=\"cardholderName\">Nama Pemegang Kartu</Label>\r\n            <Input\r\n              id=\"cardholderName\"\r\n              placeholder=\"Nama lengkap pada kartu\"\r\n              value={formData.cardholderName}\r\n              onChange={(e) => handleInputChange('cardholderName', e.target.value)}\r\n            />\r\n          </div>\r\n\r\n          <div>\r\n            <Label htmlFor=\"cardNumber\">Nomor Kartu</Label>\r\n            <Input\r\n              id=\"cardNumber\"\r\n              placeholder=\"1234 5678 9012 3456\"\r\n              value={formData.cardNumber}\r\n              onChange={(e) => handleInputChange('cardNumber', e.target.value)}\r\n            />\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-2 gap-4\">\r\n            <div>\r\n              <Label htmlFor=\"expiryDate\">Tanggal Kedaluwarsa</Label>\r\n              <Input\r\n                id=\"expiryDate\"\r\n                placeholder=\"BB/TT\"\r\n                value={formData.expiryDate}\r\n                onChange={(e) => handleInputChange('expiryDate', e.target.value)}\r\n              />\r\n            </div>\r\n            <div>\r\n              <Label htmlFor=\"cvv\">CVV</Label>\r\n              <Input\r\n                id=\"cvv\"\r\n                placeholder=\"123\"\r\n                value={formData.cvv}\r\n                onChange={(e) => handleInputChange('cvv', e.target.value)}\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {paymentMethod === 'paypal' && (\r\n        <div className=\"space-y-4\">\r\n          <div>\r\n            <Label htmlFor=\"email\">Alamat Email</Label>\r\n            <Input\r\n              id=\"email\"\r\n              type=\"email\"\r\n              placeholder=\"<EMAIL>\"\r\n              value={formData.email}\r\n              onChange={(e) => handleInputChange('email', e.target.value)}\r\n            />\r\n          </div>\r\n          <div className=\"text-center py-4\">\r\n            <p className=\"text-sm text-gray-600\">\r\n              Anda akan dialihkan ke PayPal untuk menyelesaikan pembayaran\r\n            </p>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {paymentMethod === 'bank' && (\r\n        <div className=\"space-y-4\">\r\n          <div>\r\n            <Label htmlFor=\"email\">Alamat Email</Label>\r\n            <Input\r\n              id=\"email\"\r\n              type=\"email\"\r\n              placeholder=\"<EMAIL>\"\r\n              value={formData.email}\r\n              onChange={(e) => handleInputChange('email', e.target.value)}\r\n            />\r\n          </div>\r\n          <div className=\"text-center py-4\">\r\n            <p className=\"text-sm text-gray-600\">\r\n              Instruksi transfer bank akan dikirim ke email Anda\r\n            </p>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Security Notice */}\r\n      <div className=\"flex items-center gap-2 text-sm text-gray-600 bg-blue-50 p-3 rounded-lg\">\r\n        <LockIcon className=\"h-4 w-4\" />\r\n        <span>Informasi pembayaran Anda dienkripsi dan aman</span>\r\n      </div>\r\n\r\n      <Separator />\r\n\r\n      {/* Action Buttons */}\r\n      <div className=\"flex gap-3\">\r\n        <Button\r\n          variant=\"outline\"\r\n          onClick={handleClose}\r\n          className=\"flex-1\"\r\n          disabled={isProcessing}\r\n        >\r\n          Batal\r\n        </Button>\r\n        <Button\r\n          onClick={processPayment}\r\n          className=\"flex-1 bg-green-600 hover:bg-green-700\"\r\n          disabled={!validateForm() || isProcessing}\r\n        >\r\n          <ShoppingCartIcon className=\"mr-2 h-4 w-4\" />\r\n          Selesaikan Pembelian\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const renderProcessing = () => (\r\n    <div className=\"text-center py-8\">\r\n      <div className=\"animate-spin h-12 w-12 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4\" />\r\n      <h4 className=\"text-lg font-semibold mb-2\">Memproses Pembayaran</h4>\r\n      <p className=\"text-gray-600\">Harap tunggu sementara kami memproses pembayaran Anda...</p>\r\n    </div>\r\n  );\r\n\r\n  const renderSuccess = () => (\r\n    <div className=\"text-center py-8\">\r\n      <CheckCircleIcon className=\"h-16 w-16 text-green-600 mx-auto mb-4\" />\r\n      <h4 className=\"text-xl font-semibold mb-2\">Pembayaran Berhasil!</h4>\r\n      <p className=\"text-gray-600 mb-4\">\r\n        Anda telah berhasil terdaftar di {course.name}\r\n      </p>\r\n      <p className=\"text-sm text-gray-500\">\r\n        Mengalihkan ke konten kursus...\r\n      </p>\r\n    </div>\r\n  );\r\n\r\n  const renderError = () => (\r\n    <div className=\"text-center py-8\">\r\n      <XCircleIcon className=\"h-16 w-16 text-red-600 mx-auto mb-4\" />\r\n      <h4 className=\"text-xl font-semibold mb-2\">Pembayaran Gagal</h4>\r\n      <p className=\"text-gray-600 mb-6\">\r\n        Terjadi masalah saat memproses pembayaran Anda. Silakan coba lagi.\r\n      </p>\r\n      <div className=\"flex gap-3 justify-center\">\r\n        <Button variant=\"outline\" onClick={handleClose}>\r\n          Batal\r\n        </Button>\r\n        <Button onClick={() => setPaymentStep('details')}>\r\n          Coba Lagi\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const getDialogContent = () => {\r\n    switch (paymentStep) {\r\n      case 'processing':\r\n        return renderProcessing();\r\n      case 'success':\r\n        return renderSuccess();\r\n      case 'error':\r\n        return renderError();\r\n      default:\r\n        return renderPaymentDetails();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={handleClose}>\r\n      <DialogContent className=\"sm:max-w-md max-h-[90vh] overflow-y-auto\">\r\n        <DialogHeader>\r\n          <DialogTitle className=\"flex items-center gap-2\">\r\n            <ShoppingCartIcon className=\"h-5 w-5\" />\r\n            {paymentStep === 'success' ? 'Pembelian Selesai' : 'Beli Kursus'}\r\n          </DialogTitle>\r\n          {paymentStep === 'details' && (\r\n            <DialogDescription>\r\n              Selesaikan pembelian Anda untuk mendapatkan akses instan ke kursus\r\n            </DialogDescription>\r\n          )}\r\n        </DialogHeader>\r\n        {getDialogContent()}\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n};\r\n\r\nexport default PaymentModal;"], "names": [], "mappings": ";;;;AAEA;AACA;AAOA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAfA;;;;;;;;;AAgCA,MAAM,eAA4C,CAAC,EACjD,MAAM,EACN,MAAM,EACN,OAAO,EACP,gBAAgB,EACjB;;IACC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8B;IAC/E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkD;IAC/F,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,YAAY;QACZ,YAAY;QACZ,KAAK;QACL,gBAAgB;QAChB,OAAO;QACP,gBAAgB;IAClB;IAEA,MAAM,cAAc,CAAC,OAAe,WAAmB,KAAK;QAC1D,IAAI,aAAa,OAAO;YACtB,OAAO,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;QACtD;QACA,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,oBAAoB,CAAC,OAAe;QACxC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,MAAM,eAAe;QACnB,IAAI,kBAAkB,QAAQ;YAC5B,OAAO,SAAS,UAAU,IACnB,SAAS,UAAU,IACnB,SAAS,GAAG,IACZ,SAAS,cAAc,IACvB,SAAS,KAAK;QACvB;QACA,OAAO,SAAS,KAAK;IACvB;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,gBAAgB;QAChB,eAAe;QAEf,IAAI;YACF,8BAA8B;YAC9B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,8CAA8C;YAC9C,IAAI,KAAK,MAAM,KAAK,KAAK;gBACvB,eAAe;gBACf,WAAW;oBACT;oBACA;oBACA;gBACF,GAAG;YACL,OAAO;gBACL,eAAe;YACjB;QACF,EAAE,OAAO,OAAO;YACd,eAAe;QACjB,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,aAAa;QACjB,eAAe;QACf,YAAY;YACV,YAAY;YACZ,YAAY;YACZ,KAAK;YACL,gBAAgB;YAChB,OAAO;YACP,gBAAgB;QAClB;QACA,gBAAgB;IAClB;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,gBAAgB,gBAAgB,WAAW;YAC9C;YACA;QACF;IACF;IAEA,MAAM,uBAAuB,kBAC3B,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAqB;;;;;;sCACnC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAe,OAAO,IAAI;;;;;;sDACvC,6LAAC;4CAAE,WAAU;;gDAAwB;gDAAI,OAAO,UAAU;;;;;;;;;;;;;8CAE5D,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;kDACV,OAAO,KAAK,GAAG,YAAY,OAAO,KAAK,EAAE,OAAO,QAAQ,IAAI;;;;;;;;;;;;;;;;;sCAInE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;;wCAAW,OAAO,OAAO,CAAC,MAAM;wCAAC;;;;;;;gCAC/C,OAAO,WAAW,CAAC,UAAU,kBAC5B,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAU;;;;;;;;;;;;;;;;;;8BAM/B,6LAAC;;sCACC,6LAAC,oIAAA,CAAA,QAAK;4BAAC,WAAU;sCAAqC;;;;;;sCACtD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS,kBAAkB,SAAS,YAAY;oCAChD,SAAS,IAAM,iBAAiB;oCAChC,WAAU;;sDAEV,6LAAC,8NAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;sDAC1B,6LAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;8CAE5B,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS,kBAAkB,WAAW,YAAY;oCAClD,SAAS,IAAM,iBAAiB;oCAChC,WAAU;;sDAEV,6LAAC,kOAAA,CAAA,mBAAgB;4CAAC,WAAU;;;;;;sDAC5B,6LAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;8CAE5B,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS,kBAAkB,SAAS,YAAY;oCAChD,SAAS,IAAM,iBAAiB;oCAChC,WAAU;;sDAEV,6LAAC,uOAAA,CAAA,qBAAgB;4CAAC,WAAU;;;;;;sDAC5B,6LAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;;;;;;;;gBAM/B,kBAAkB,wBACjB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAQ;;;;;;8CACvB,6LAAC,oIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,MAAK;oCACL,aAAY;oCACZ,OAAO,SAAS,KAAK;oCACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;sCAI9D,6LAAC;;8CACC,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAiB;;;;;;8CAChC,6LAAC,oIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,aAAY;oCACZ,OAAO,SAAS,cAAc;oCAC9B,UAAU,CAAC,IAAM,kBAAkB,kBAAkB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;sCAIvE,6LAAC;;8CACC,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAa;;;;;;8CAC5B,6LAAC,oIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,aAAY;oCACZ,OAAO,SAAS,UAAU;oCAC1B,UAAU,CAAC,IAAM,kBAAkB,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;sCAInE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAa;;;;;;sDAC5B,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,aAAY;4CACZ,OAAO,SAAS,UAAU;4CAC1B,UAAU,CAAC,IAAM,kBAAkB,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAGnE,6LAAC;;sDACC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAM;;;;;;sDACrB,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,aAAY;4CACZ,OAAO,SAAS,GAAG;4CACnB,UAAU,CAAC,IAAM,kBAAkB,OAAO,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;gBAOjE,kBAAkB,0BACjB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAQ;;;;;;8CACvB,6LAAC,oIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,MAAK;oCACL,aAAY;oCACZ,OAAO,SAAS,KAAK;oCACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;sCAG9D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;gBAO1C,kBAAkB,wBACjB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAQ;;;;;;8CACvB,6LAAC,oIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,MAAK;oCACL,aAAY;oCACZ,OAAO,SAAS,KAAK;oCACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;sCAG9D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;8BAQ3C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,iNAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,6LAAC;sCAAK;;;;;;;;;;;;8BAGR,6LAAC,wIAAA,CAAA,YAAS;;;;;8BAGV,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;4BACT,WAAU;4BACV,UAAU;sCACX;;;;;;sCAGD,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,WAAU;4BACV,UAAU,CAAC,kBAAkB;;8CAE7B,6LAAC,uOAAA,CAAA,qBAAgB;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;IAOrD,MAAM,mBAAmB,kBACvB,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAG,WAAU;8BAA6B;;;;;;8BAC3C,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;IAIjC,MAAM,gBAAgB,kBACpB,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6OAAA,CAAA,wBAAe;oBAAC,WAAU;;;;;;8BAC3B,6LAAC;oBAAG,WAAU;8BAA6B;;;;;;8BAC3C,6LAAC;oBAAE,WAAU;;wBAAqB;wBACE,OAAO,IAAI;;;;;;;8BAE/C,6LAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;;IAMzC,MAAM,cAAc,kBAClB,6LAAC;YAAI,WAAU;;8BACb,6LAAC,0NAAA,CAAA,eAAW;oBAAC,WAAU;;;;;;8BACvB,6LAAC;oBAAG,WAAU;8BAA6B;;;;;;8BAC3C,6LAAC;oBAAE,WAAU;8BAAqB;;;;;;8BAGlC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;sCAAa;;;;;;sCAGhD,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAS,IAAM,eAAe;sCAAY;;;;;;;;;;;;;;;;;;IAOxD,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC,uOAAA,CAAA,qBAAgB;oCAAC,WAAU;;;;;;gCAC3B,gBAAgB,YAAY,sBAAsB;;;;;;;wBAEpD,gBAAgB,2BACf,6LAAC,qIAAA,CAAA,oBAAiB;sCAAC;;;;;;;;;;;;gBAKtB;;;;;;;;;;;;AAIT;GA5VM;KAAA;uCA8VS", "debugId": null}}, {"offset": {"line": 3826, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/lms/course-success-modal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON>alogHeader, DialogTitle } from '@/components/ui/dialog';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Card, CardContent } from '@/components/ui/card';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport {\r\n  CheckmarkCircle01Icon as CheckCircleIcon,\r\n  BookOpen01Icon as BookOpenIcon,\r\n  PlayIcon,\r\n  ArrowRight01Icon as ArrowRightIcon,\r\n  UserIcon as UsersIcon,\r\n  Calendar03Icon as CalendarIcon,\r\n  Award05Icon as AwardIcon\r\n} from 'hugeicons-react';\r\nimport Image from 'next/image'; // Import the Image component\r\nimport { Course } from '@/types/lms';\r\nimport Link from 'next/link';\r\n\r\ninterface CourseSuccessModalProps {\r\n  course: Course;\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  actionType: 'purchase' | 'enrollment';\r\n}\r\n\r\nconst CourseSuccessModal: React.FC<CourseSuccessModalProps> = ({\r\n  course,\r\n  isOpen,\r\n  onClose,\r\n  actionType\r\n}) => {\r\n  const formatPrice = (price: number, currency: string = 'IDR') => {\r\n    if (currency === 'IDR') {\r\n      return 'Rp' + new Intl.NumberFormat('id-ID').format(price);\r\n    }\r\n    return new Intl.NumberFormat('id-ID', {\r\n      style: 'currency',\r\n      currency: currency\r\n    }).format(price);\r\n  };\r\n\r\n  const getSuccessMessage = () => {\r\n    if (actionType === 'purchase') {\r\n      return 'Pembelian Berhasil!';\r\n    }\r\n    return 'Pendaftaran Berhasil!';\r\n  };\r\n\r\n  const getSuccessDescription = () => {\r\n    if (actionType === 'purchase') {\r\n      return 'Selamat! Anda telah berhasil membeli dan terdaftar di kursus ini. Anda sekarang dapat mengakses semua materi pembelajaran.';\r\n    }\r\n    return 'Selamat! Anda telah berhasil terdaftar di kursus ini. Anda sekarang dapat mengakses semua materi pembelajaran.';\r\n  };\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={() => {}}>\r\n      <DialogContent className=\"sm:max-w-2xl w-[95vw] max-h-[85vh] flex flex-col p-0 gap-0\">\r\n        {/* Fixed Header */}\r\n        <div className=\"flex-shrink-0 p-6 pb-4\">\r\n          <DialogHeader className=\"text-center\">\r\n            <div className=\"mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4\">\r\n              <CheckCircleIcon className=\"h-8 w-8 text-green-600\" />\r\n            </div>\r\n            <DialogTitle className=\"text-2xl font-bold text-green-900 text-center\">\r\n              {getSuccessMessage()}\r\n            </DialogTitle>\r\n            <p className=\"text-gray-600 mt-2\">\r\n              {getSuccessDescription()}\r\n            </p>\r\n          </DialogHeader>\r\n        </div>\r\n\r\n        {/* Scrollable Content Area */}\r\n        <div className=\"flex-1 overflow-y-auto px-6 min-h-0\">\r\n          {/* Course Card */}\r\n          <Card className=\"border-2 border-green-200 bg-green-50/30\">\r\n            <CardContent className=\"p-0\">\r\n              {/* Course Image/Thumbnail */}\r\n              <div className=\"h-32 rounded-t-lg flex items-center justify-center relative overflow-hidden\">\r\n                {course.thumbnail ? (\r\n                  <Image \r\n                    src={course.thumbnail} \r\n                    alt={course.name}\r\n                    fill // Use fill to make the image cover the parent div\r\n                    className=\"object-cover\"\r\n                    sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\" // Recommended for performance\r\n                  />\r\n                ) : (\r\n                  <div className=\"h-32 bg-gradient-to-br from-blue-500 to-purple-600 w-full flex items-center justify-center\">\r\n                    <div className=\"absolute inset-0 bg-black/20\" />\r\n                    <div className=\"relative z-10 text-center text-white\">\r\n                      <BookOpenIcon className=\"h-12 w-12 mx-auto mb-2 opacity-80\" />\r\n                      <p className=\"text-sm font-medium\">{course.code}</p>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n                {actionType === 'purchase' && course.price && (\r\n                  <Badge \r\n                    className=\"absolute top-3 right-3 bg-green-600 hover:bg-green-600\"\r\n                    variant=\"default\"\r\n                  >\r\n                    {formatPrice(course.price, course.currency)}\r\n                  </Badge>\r\n                )}\r\n              </div>\r\n\r\n              <div className=\"p-6\">\r\n                {/* Course Header */}\r\n                <div className=\"space-y-2 mb-4\">\r\n                  <h3 className=\"text-lg font-bold text-gray-900 line-clamp-2\">\r\n                    {course.name}\r\n                  </h3>\r\n                  <p className=\"text-gray-600 text-sm line-clamp-2\">\r\n                    {course.description}\r\n                  </p>\r\n                </div>\r\n\r\n                {/* Course Meta Information */}\r\n                <div className=\"grid grid-cols-2 gap-4 mb-4 text-sm text-gray-600\">\r\n                  <div className=\"flex items-center\">\r\n                    <UsersIcon className=\"mr-2 h-4 w-4\" />\r\n                    <span>Instruktur: {course.instructor}</span>\r\n                  </div>\r\n                  <div className=\"flex items-center\">\r\n                    <CalendarIcon className=\"mr-2 h-4 w-4\" />\r\n                    <span>\r\n                      {new Date(course.startDate).toLocaleDateString('id-ID')} - {' '}\r\n                      {new Date(course.endDate).toLocaleDateString('id-ID')}\r\n                    </span>\r\n                  </div>\r\n                  <div className=\"flex items-center\">\r\n                    <BookOpenIcon className=\"mr-2 h-4 w-4\" />\r\n                    <span>{course.modules.length} modul</span>\r\n                  </div>\r\n                  {course.certificate.isEligible && (\r\n                    <div className=\"flex items-center\">\r\n                      <AwardIcon className=\"mr-2 h-4 w-4\" />\r\n                      <span>Sertifikat tersedia</span>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n\r\n                {/* Course Features */}\r\n                <div className=\"flex flex-wrap gap-2 mb-4\">\r\n                  {course.certificate.isEligible && (\r\n                    <Badge variant=\"secondary\">\r\n                      <AwardIcon className=\"mr-1 h-3 w-3\" />\r\n                      Sertifikat\r\n                    </Badge>\r\n                  )}\r\n                  {course.academics?.workload && (\r\n                    <Badge variant=\"outline\">{course.academics.workload}</Badge>\r\n                  )}\r\n                  {course.academics?.credits && (\r\n                    <Badge variant=\"outline\">{course.academics.credits} kredit</Badge>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n        </div>\r\n\r\n        {/* Fixed Footer with Action Buttons */}\r\n        <div className=\"flex-shrink-0 border-t bg-white p-6 space-y-4\">\r\n          {/* Action Buttons */}\r\n          <div className=\"flex flex-col sm:flex-row gap-3\">\r\n            <Link href={`/my-courses/${course.id}`} className=\"flex-1\">\r\n              <Button className=\"w-full bg-green-600 hover:bg-green-700\" size=\"lg\">\r\n                <PlayIcon className=\"mr-2 h-4 w-4\" />\r\n                Mulai Belajar Sekarang\r\n                <ArrowRightIcon className=\"ml-2 h-4 w-4\" />\r\n              </Button>\r\n            </Link>\r\n            <Link href=\"/my-courses\" className=\"flex-1\">\r\n              <Button variant=\"outline\" className=\"w-full\" size=\"lg\">\r\n                <BookOpenIcon className=\"mr-2 h-4 w-4\" />\r\n                Lihat Semua Kursus Saya\r\n              </Button>\r\n            </Link>\r\n          </div>\r\n\r\n          {/* Close Option */}\r\n          <div className=\"text-center\">\r\n            <Button \r\n              variant=\"ghost\" \r\n              onClick={onClose}\r\n              className=\"text-gray-500 hover:text-gray-700\"\r\n            >\r\n              Tutup dan kembali ke katalog\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n};\r\n\r\nexport default CourseSuccessModal;"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,kOAAgC,6BAA6B;AAE7D;AAlBA;;;;;;;;;AA2BA,MAAM,qBAAwD,CAAC,EAC7D,MAAM,EACN,MAAM,EACN,OAAO,EACP,UAAU,EACX;IACC,MAAM,cAAc,CAAC,OAAe,WAAmB,KAAK;QAC1D,IAAI,aAAa,OAAO;YACtB,OAAO,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;QACtD;QACA,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,oBAAoB;QACxB,IAAI,eAAe,YAAY;YAC7B,OAAO;QACT;QACA,OAAO;IACT;IAEA,MAAM,wBAAwB;QAC5B,IAAI,eAAe,YAAY;YAC7B,OAAO;QACT;QACA,OAAO;IACT;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc,KAAO;kBACzC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BAEvB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,qIAAA,CAAA,eAAY;wBAAC,WAAU;;0CACtB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,6OAAA,CAAA,wBAAe;oCAAC,WAAU;;;;;;;;;;;0CAE7B,6LAAC,qIAAA,CAAA,cAAW;gCAAC,WAAU;0CACpB;;;;;;0CAEH,6LAAC;gCAAE,WAAU;0CACV;;;;;;;;;;;;;;;;;8BAMP,6LAAC;oBAAI,WAAU;8BAEb,cAAA,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CAErB,6LAAC;oCAAI,WAAU;;wCACZ,OAAO,SAAS,iBACf,6LAAC,gIAAA,CAAA,UAAK;4CACJ,KAAK,OAAO,SAAS;4CACrB,KAAK,OAAO,IAAI;4CAChB,IAAI;4CACJ,WAAU;4CACV,OAAM,2DAA2D,8BAA8B;;;;;iEAGjG,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,+NAAA,CAAA,iBAAY;4DAAC,WAAU;;;;;;sEACxB,6LAAC;4DAAE,WAAU;sEAAuB,OAAO,IAAI;;;;;;;;;;;;;;;;;;wCAIpD,eAAe,cAAc,OAAO,KAAK,kBACxC,6LAAC,oIAAA,CAAA,QAAK;4CACJ,WAAU;4CACV,SAAQ;sDAEP,YAAY,OAAO,KAAK,EAAE,OAAO,QAAQ;;;;;;;;;;;;8CAKhD,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DACX,OAAO,IAAI;;;;;;8DAEd,6LAAC;oDAAE,WAAU;8DACV,OAAO,WAAW;;;;;;;;;;;;sDAKvB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,iNAAA,CAAA,WAAS;4DAAC,WAAU;;;;;;sEACrB,6LAAC;;gEAAK;gEAAa,OAAO,UAAU;;;;;;;;;;;;;8DAEtC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,8NAAA,CAAA,iBAAY;4DAAC,WAAU;;;;;;sEACxB,6LAAC;;gEACE,IAAI,KAAK,OAAO,SAAS,EAAE,kBAAkB,CAAC;gEAAS;gEAAI;gEAC3D,IAAI,KAAK,OAAO,OAAO,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;8DAGjD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,+NAAA,CAAA,iBAAY;4DAAC,WAAU;;;;;;sEACxB,6LAAC;;gEAAM,OAAO,OAAO,CAAC,MAAM;gEAAC;;;;;;;;;;;;;gDAE9B,OAAO,WAAW,CAAC,UAAU,kBAC5B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,wNAAA,CAAA,cAAS;4DAAC,WAAU;;;;;;sEACrB,6LAAC;sEAAK;;;;;;;;;;;;;;;;;;sDAMZ,6LAAC;4CAAI,WAAU;;gDACZ,OAAO,WAAW,CAAC,UAAU,kBAC5B,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;;sEACb,6LAAC,wNAAA,CAAA,cAAS;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;gDAIzC,OAAO,SAAS,EAAE,0BACjB,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAW,OAAO,SAAS,CAAC,QAAQ;;;;;;gDAEpD,OAAO,SAAS,EAAE,yBACjB,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;;wDAAW,OAAO,SAAS,CAAC,OAAO;wDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAS/D,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAM,CAAC,YAAY,EAAE,OAAO,EAAE,EAAE;oCAAE,WAAU;8CAChD,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCAAC,WAAU;wCAAyC,MAAK;;0DAC9D,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;0DAErC,6LAAC,mOAAA,CAAA,mBAAc;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAG9B,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAc,WAAU;8CACjC,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,WAAU;wCAAS,MAAK;;0DAChD,6LAAC,+NAAA,CAAA,iBAAY;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;sCAO/C,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;KA3KM;uCA6KS", "debugId": null}}, {"offset": {"line": 4328, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/app/%28students-page%29/courses/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useRef } from 'react';\r\nimport { Card, CardContent } from '@/components/ui/card';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Input } from '@/components/ui/input';\r\nimport {\r\n  <PERSON><PERSON>,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogTitle\r\n} from '@/components/ui/dialog';\r\nimport { Breadcrumbs } from '@/components/breadcrumbs';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport {\r\n  BookOpen01Icon as BookOpenIcon,\r\n  Key01Icon as KeyIcon,\r\n  UserIcon as UsersIcon,\r\n  Search01Icon as SearchIcon,\r\n  Clock01Icon as ClockIcon,\r\n  Calendar03Icon as CalendarIcon,\r\n  ArrowRight01Icon as ArrowRightIcon,\r\n  Settings01Icon as SettingsIcon,\r\n  ArrowDown01Icon as ArrowDownIcon,\r\n  CheckmarkCircle01Icon as CheckCircleIcon,\r\n  Cancel01Icon as XCircleIcon\r\n} from 'hugeicons-react';\r\n\r\nimport { useEnrollment } from '@/contexts/enrollment-context';\r\nimport { useFeatureFlags } from '@/lib/feature-flags';\r\nimport { Course } from '@/types/lms';\r\nimport CoursePreviewCard from '@/components/lms/course-preview-card';\r\nimport CourseDetailTabs from '@/components/lms/course-detail-tabs';\r\nimport PaymentModal from '@/components/lms/payment-modal';\r\nimport CourseSuccessModal from '@/components/lms/course-success-modal';\r\nimport Link from 'next/link';\r\n\r\n// Configuration\r\nconst SHOW_FEATURE_SETTINGS = false; // Set to true to show feature settings UI\r\n\r\n\r\n\r\n\r\n\r\nconst AvailableCoursesPage: React.FC = () => {\r\n  // Context\r\n  const { isEnrolled, enrollInCourseWithPurchase, isEnrolledInCourse } = useEnrollment();\r\n  const { flags, setFlag } = useFeatureFlags();\r\n\r\n  // State management\r\n  const [selectedCourse, setSelectedCourse] = useState<Course | null>(null);\r\n  const [enrollmentCode, setEnrollmentCode] = useState<string>('');\r\n  const [showEnrollModal, setShowEnrollModal] = useState<boolean>(false);\r\n  const [showDetailModal, setShowDetailModal] = useState<boolean>(false);\r\n  const [showPaymentModal, setShowPaymentModal] = useState<boolean>(false);\r\n  const [showFeatureFlagsModal, setShowFeatureFlagsModal] = useState<boolean>(false);\r\n  const [showSuccessModal, setShowSuccessModal] = useState<boolean>(false);\r\n  const [successActionType, setSuccessActionType] = useState<'purchase' | 'enrollment'>('purchase');\r\n  const [detailTab, setDetailTab] = useState<string>('overview');\r\n  const [error, setError] = useState<string>('');\r\n  const [toast, setToast] = useState<{\r\n    show: boolean;\r\n    message: string;\r\n    type: 'success' | 'error' | 'loading';\r\n  }>({\r\n    show: false,\r\n    message: '',\r\n    type: 'success'\r\n  });\r\n  const [showScrollHint, setShowScrollHint] = useState(true);\r\n  const [canScrollUp, setCanScrollUp] = useState(false);\r\n  const [canScrollDown, setCanScrollDown] = useState(false);\r\n  const scrollContainerRef = useRef<HTMLDivElement>(null);\r\n\r\n  // API state\r\n  const [courses, setCourses] = useState<Course[]>([]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n\r\n  // Scroll Detection with 200px threshold\r\n  const checkScrollable = () => {\r\n    const container = scrollContainerRef.current;\r\n    if (!container) return;\r\n\r\n    const { scrollTop, scrollHeight, clientHeight } = container;\r\n    const scrollableHeight = scrollHeight - clientHeight;\r\n    const SCROLL_THRESHOLD = 100; // Only show indicator if there's more than 100px to scroll\r\n    \r\n    const canScrollUpNow = scrollTop > 5;\r\n    const canScrollDownNow = scrollableHeight > SCROLL_THRESHOLD && scrollTop < scrollHeight - clientHeight - 5;\r\n    \r\n    \r\n    setCanScrollUp(canScrollUpNow);\r\n    setCanScrollDown(canScrollDownNow);\r\n\r\n    // Hide scroll hint after first interaction\r\n    if (scrollTop > 0) {\r\n      setShowScrollHint(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    const container = scrollContainerRef.current;\r\n    if (!container) return;\r\n\r\n    // Initial check\r\n    checkScrollable();\r\n    \r\n    // Check after content loads\r\n    const timer1 = setTimeout(() => checkScrollable(), 100);\r\n    const timer2 = setTimeout(() => checkScrollable(), 500);\r\n    const timer3 = setTimeout(() => checkScrollable(), 1000);\r\n\r\n    // Observe size changes\r\n    const resizeObserver = new ResizeObserver(() => {\r\n      checkScrollable();\r\n    });\r\n    resizeObserver.observe(container);\r\n\r\n    return () => {\r\n      clearTimeout(timer1);\r\n      clearTimeout(timer2);\r\n      clearTimeout(timer3);\r\n      resizeObserver.disconnect();\r\n    };\r\n  }, [showDetailModal, selectedCourse]);\r\n\r\n  // Also check when modal content changes\r\n  useEffect(() => {\r\n    if (showDetailModal && selectedCourse) {\r\n      const timer = setTimeout(() => checkScrollable(), 200);\r\n      return () => clearTimeout(timer);\r\n    }\r\n  }, [detailTab, showDetailModal, selectedCourse]);\r\n\r\n  // Fetch courses from API\r\n  useEffect(() => {\r\n    fetchCourses();\r\n  }, []);\r\n\r\n  const fetchCourses = async () => {\r\n    try {\r\n      setIsLoading(true);\r\n      const response = await fetch('/api/courses?public=true');\r\n      const data = await response.json();\r\n\r\n      if (data.success) {\r\n        setCourses(data.courses || []);\r\n      } else {\r\n        showToast('Failed to fetch courses', 'error');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching courses:', error);\r\n      showToast('Failed to fetch courses', 'error');\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  // Handlers\r\n  const showToast = (\r\n    message: string,\r\n    type: 'success' | 'error' = 'success'\r\n  ) => {\r\n    setToast({ show: true, message, type });\r\n    setTimeout(() => {\r\n      setToast((prev) => ({ ...prev, show: false }));\r\n    }, 3000);\r\n  };\r\n\r\n  // Course handlers\r\n  const handleCourseClick = (course: Course) => {\r\n    setSelectedCourse(course);\r\n    setDetailTab('overview');\r\n    setShowDetailModal(true);\r\n  };\r\n\r\n  const handleEnrollClick = (course: Course) => {\r\n    setSelectedCourse(course);\r\n    setShowEnrollModal(true);\r\n  };\r\n\r\n  const handlePurchaseClick = (course: Course) => {\r\n    setSelectedCourse(course);\r\n    setShowPaymentModal(true);\r\n  };\r\n\r\n  const handlePaymentSuccess = () => {\r\n    if (selectedCourse) {\r\n      enrollInCourseWithPurchase(selectedCourse);\r\n      \r\n      // Close payment and detail modals\r\n      setShowPaymentModal(false);\r\n      setShowDetailModal(false);\r\n      \r\n      // Show success modal\r\n      setSuccessActionType('purchase');\r\n      setShowSuccessModal(true);\r\n      \r\n      // Keep selectedCourse for success modal, don't reset here\r\n    }\r\n  };\r\n\r\n  const handleEnrollment = () => {\r\n    if (!selectedCourse) return;\r\n\r\n    const targetEnrollmentCode = selectedCourse.enrollmentCode;\r\n\r\n    // Check if already enrolled in this specific course\r\n    if (isEnrolledInCourse(selectedCourse.id)) {\r\n      setError('You are already enrolled in this course.');\r\n      return;\r\n    }\r\n\r\n    if (targetEnrollmentCode === enrollmentCode) {\r\n      // Enroll in course using context\r\n      enrollInCourseWithPurchase(selectedCourse);\r\n\r\n      // Close enrollment and detail modals\r\n      setShowEnrollModal(false);\r\n      setShowDetailModal(false);\r\n\r\n      // Reset form state\r\n      setError('');\r\n      setEnrollmentCode('');\r\n\r\n      // Show success modal\r\n      setSuccessActionType('enrollment');\r\n      setShowSuccessModal(true);\r\n    } else {\r\n      setError('Invalid enrollment code. Please try again.');\r\n    }\r\n  };\r\n\r\n\r\n  const handleSuccessModalClose = () => {\r\n    setShowSuccessModal(false);\r\n    setSelectedCourse(null);\r\n  };\r\n\r\n  // All available courses\r\n  const availableCourses = courses;\r\n\r\n  return (\r\n    <div className='min-h-screen bg-gray-50 p-8'>\r\n      <div className='mx-auto max-w-7xl space-y-6 pb-8'>\r\n        {/* Breadcrumbs - Top Level */}\r\n        <Breadcrumbs />\r\n        \r\n        {/* Header Section */}\r\n        <div className='flex items-center justify-between'>\r\n          <div className='flex items-center space-x-3'>\r\n            <BookOpenIcon className='h-8 w-8 text-[var(--iai-primary)]' />\r\n            <div>\r\n              <h1 className='text-3xl font-bold'>\r\n                Kursus Tersedia\r\n              </h1>\r\n              <p className='text-gray-600'>\r\n                Jelajahi dan daftar kursus profesional\r\n              </p>\r\n            </div>\r\n          </div>\r\n          {SHOW_FEATURE_SETTINGS && (\r\n            <div className='flex gap-2'>\r\n              <Button\r\n                variant='outline'\r\n                onClick={() => setShowFeatureFlagsModal(true)}\r\n                size='sm'\r\n              >\r\n                <SettingsIcon className='h-4 w-4 mr-2' />\r\n                Pengaturan Fitur\r\n              </Button>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Feature Flags Info */}\r\n        {SHOW_FEATURE_SETTINGS && (\r\n          <div className='bg-blue-50 border border-blue-200 rounded-lg p-4'>\r\n            <div className='flex items-center justify-between'>\r\n              <div>\r\n                <h3 className='font-medium text-blue-900'>Pengaturan Saat Ini</h3>\r\n                <div className='flex gap-4 mt-1 text-sm'>\r\n                  <span className={`${flags.enableCoursePurchase ? 'text-green-700' : 'text-gray-500'}`}>\r\n                    Pembelian: {flags.enableCoursePurchase ? 'AKTIF' : 'NONAKTIF'}\r\n                  </span>\r\n                  <span className={`${flags.enableEnrollmentCode ? 'text-green-700' : 'text-gray-500'}`}>\r\n                    Kode Pendaftaran: {flags.enableEnrollmentCode ? 'AKTIF' : 'NONAKTIF'}\r\n                  </span>\r\n                  <span className={`${flags.enableCoursePreview ? 'text-green-700' : 'text-gray-500'}`}>\r\n                    Pratinjau: {flags.enableCoursePreview ? 'AKTIF' : 'NONAKTIF'}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n\r\n        {/* Navigation to existing enrollment page */}\r\n        {isEnrolled && (\r\n          <div className='bg-green-50 border border-green-200 rounded-lg p-4'>\r\n            <div className='flex items-center justify-between'>\r\n              <div>\r\n                <h3 className='font-medium text-green-900'>Anda sudah terdaftar!</h3>\r\n                <p className='text-sm text-green-700'>\r\n                  Akses kursus yang Anda daftari dan lanjutkan belajar\r\n                </p>\r\n              </div>\r\n              <Link href='/my-courses'>\r\n                <Button variant=\"iai\">\r\n                  Ke Kursus Saya\r\n                  <ArrowRightIcon className='ml-2 h-4 w-4' />\r\n                </Button>\r\n              </Link>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Courses Grid */}\r\n        {isLoading ? (\r\n          <div className='grid gap-6 md:grid-cols-2 lg:grid-cols-3'>\r\n            {[...Array(6)].map((_, i) => (\r\n              <Card key={i} className='overflow-hidden animate-pulse'>\r\n                <div className='aspect-video bg-gray-200'></div>\r\n                <CardContent className='p-6'>\r\n                  <div className='h-6 bg-gray-200 rounded mb-2'></div>\r\n                  <div className='h-4 bg-gray-200 rounded mb-2'></div>\r\n                  <div className='h-4 bg-gray-200 rounded w-2/3 mb-4'></div>\r\n                  <div className='flex items-center justify-between'>\r\n                    <div className='h-8 bg-gray-200 rounded w-20'></div>\r\n                    <div className='h-8 bg-gray-200 rounded w-16'></div>\r\n                  </div>\r\n                </CardContent>\r\n              </Card>\r\n            ))}\r\n          </div>\r\n        ) : (\r\n          <div className='grid gap-6 md:grid-cols-2 lg:grid-cols-3'>\r\n            {availableCourses.map((course) => (\r\n              <CoursePreviewCard\r\n                key={course.id}\r\n                course={course}\r\n                onClick={() => handleCourseClick(course)}\r\n                onEnroll={() => handleEnrollClick(course)}\r\n                onPurchase={() => handlePurchaseClick(course)}\r\n                isEnrolled={isEnrolledInCourse(course.id)}\r\n              />\r\n            ))}\r\n          </div>\r\n        )}\r\n\r\n        {!isLoading && availableCourses.length === 0 && (\r\n          <div className='text-center py-12'>\r\n            <BookOpenIcon className='h-12 w-12 text-gray-400 mx-auto mb-4' />\r\n            <h3 className='text-xl font-semibold text-gray-900'>Tidak ada kursus ditemukan</h3>\r\n            <p className='text-gray-600'>Tidak ada kursus yang tersedia saat ini</p>\r\n          </div>\r\n        )}\r\n\r\n        {/* Course Detail Modal with ScrollArea for full height scrollable content */}\r\n        <Dialog\r\n          open={showDetailModal}\r\n          onOpenChange={(open) => {\r\n            setShowDetailModal(open);\r\n            if (!open) setSelectedCourse(null);\r\n          }}\r\n        >\r\n          <DialogContent className='sm:max-w-4xl p-0 gap-0 h-[90vh] flex flex-col'>\r\n            {/* Header - Fixed */}\r\n            <div className=\"p-6 pb-0 flex-shrink-0\">\r\n              <DialogHeader>\r\n                <DialogTitle className='text-2xl'>\r\n                  {selectedCourse?.name}\r\n                </DialogTitle>\r\n              </DialogHeader>\r\n            </div>\r\n\r\n            {/* Scrollable Content Area */}\r\n            <div \r\n              ref={scrollContainerRef}\r\n              onScroll={checkScrollable}\r\n              className={`flex-1 overflow-y-auto scrollbar-visible min-h-0 relative scroll-container ${\r\n                showScrollHint ? 'scroll-hint' : ''\r\n              } ${canScrollUp ? 'can-scroll-up' : ''} ${canScrollDown ? 'can-scroll-down' : ''}`}\r\n            >\r\n              <style dangerouslySetInnerHTML={{\r\n                __html: `\r\n                  .scrollbar-visible::-webkit-scrollbar {\r\n                    width: 12px;\r\n                  }\r\n                  .scrollbar-visible::-webkit-scrollbar-track {\r\n                    background: rgb(243 244 246); /* gray-100 */\r\n                    border-radius: 6px;\r\n                  }\r\n                  .scrollbar-visible::-webkit-scrollbar-thumb {\r\n                    background: rgb(209 213 219); /* gray-300 */\r\n                    border-radius: 6px;\r\n                  }\r\n                  .scrollbar-visible::-webkit-scrollbar-thumb:hover {\r\n                    background: rgb(156 163 175); /* gray-400 */\r\n                  }\r\n                  /* For Firefox */\r\n                  .scrollbar-visible {\r\n                    scrollbar-width: thin;\r\n                    scrollbar-color: rgb(209 213 219) rgb(243 244 246);\r\n                  }\r\n                  \r\n                  /* Scroll Indicators */\r\n                  .scroll-container::before {\r\n                    content: \"\";\r\n                    position: absolute;\r\n                    top: 0;\r\n                    left: 0;\r\n                    right: 0;\r\n                    height: 20px;\r\n                    background: linear-gradient(to bottom, rgba(255,255,255,0.9), transparent);\r\n                    pointer-events: none;\r\n                    z-index: 10;\r\n                    opacity: 0;\r\n                    transition: opacity 0.3s ease;\r\n                  }\r\n                  \r\n                  .scroll-container::after {\r\n                    content: \"\";\r\n                    position: absolute;\r\n                    bottom: 0;\r\n                    left: 0;\r\n                    right: 0;\r\n                    height: 20px;\r\n                    background: linear-gradient(to top, rgba(255,255,255,0.9), transparent);\r\n                    pointer-events: none;\r\n                    z-index: 10;\r\n                    opacity: 0;\r\n                    transition: opacity 0.3s ease;\r\n                  }\r\n                  \r\n                  .scroll-container.can-scroll-up::before {\r\n                    opacity: 1;\r\n                  }\r\n                  \r\n                  .scroll-container.can-scroll-down::after {\r\n                    opacity: 1;\r\n                  }\r\n                  \r\n                  /* Pulsing scrollbar animation for initial hint */\r\n                  @keyframes pulse-scrollbar {\r\n                    0%, 100% { opacity: 0.6; }\r\n                    50% { opacity: 1; }\r\n                  }\r\n                  \r\n                  .scroll-hint .scrollbar-visible::-webkit-scrollbar-thumb {\r\n                    animation: pulse-scrollbar 2s infinite;\r\n                  }\r\n                  \r\n                  /* Scroll instruction overlay */\r\n                  .scroll-instruction {\r\n                    position: absolute;\r\n                    top: 50%;\r\n                    right: 20px;\r\n                    transform: translateY(-50%);\r\n                    background: rgba(59, 130, 246, 0.9);\r\n                    color: white;\r\n                    padding: 8px 12px;\r\n                    border-radius: 8px;\r\n                    font-size: 12px;\r\n                    z-index: 20;\r\n                    animation: fadeInOut 4s ease-in-out;\r\n                    pointer-events: none;\r\n                  }\r\n                  \r\n                  @keyframes fadeInOut {\r\n                    0%, 100% { opacity: 0; transform: translateY(-50%) translateX(10px); }\r\n                    10%, 90% { opacity: 1; transform: translateY(-50%) translateX(0); }\r\n                  }\r\n                `\r\n              }} />\r\n              \r\n              <div className=\"p-6 pt-4\">\r\n                {selectedCourse && (\r\n                  <CourseDetailTabs\r\n                    course={selectedCourse}\r\n                    activeTab={detailTab}\r\n                    onTabChange={setDetailTab}\r\n                  />\r\n                )}\r\n              </div>\r\n            </div>\r\n\r\n\r\n\r\n\r\n            {/* Scroll Indicator above footer */}\r\n            {canScrollDown && (\r\n              <div className=\"flex justify-center py-2 bg-gray-50 border-t border-gray-100\">\r\n                <div className=\"flex items-center gap-2 text-gray-500 text-sm animate-bounce\">\r\n                  <ArrowDownIcon className=\"h-4 w-4\" />\r\n                  <span>Gulir ke bawah untuk melihat semua detail</span>\r\n                  <ArrowDownIcon className=\"h-4 w-4\" />\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Action Buttons - Fixed Footer */}\r\n            {selectedCourse && (\r\n              <div className='p-6 pt-4 border-t bg-white flex-shrink-0'>\r\n                <div className='flex gap-4'>\r\n                  {selectedCourse.isPurchasable && flags.enableCoursePurchase && (\r\n                    <Button\r\n                      onClick={() => handlePurchaseClick(selectedCourse)}\r\n                      variant=\"iai\"\r\n                      className='flex-1'\r\n                    >\r\n                      Beli seharga {selectedCourse.price ? (selectedCourse.currency === 'IDR' ? 'Rp' + new Intl.NumberFormat('id-ID').format(selectedCourse.price) : new Intl.NumberFormat('id-ID', { style: 'currency', currency: selectedCourse.currency || 'IDR' }).format(selectedCourse.price)) : 'Gratis'}\r\n                    </Button>\r\n                  )}\r\n                  {flags.enableEnrollmentCode && selectedCourse.enrollmentCode && (\r\n                    <Button\r\n                      onClick={() => handleEnrollClick(selectedCourse)}\r\n                      variant='outline'\r\n                      className='flex-1'\r\n                    >\r\n                      Gunakan Kode Pendaftaran\r\n                    </Button>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            )}\r\n          </DialogContent>\r\n        </Dialog>\r\n\r\n        {/* Payment Modal */}\r\n        {selectedCourse && (\r\n          <PaymentModal\r\n            course={selectedCourse}\r\n            isOpen={showPaymentModal}\r\n            onClose={() => setShowPaymentModal(false)}\r\n            onPaymentSuccess={handlePaymentSuccess}\r\n          />\r\n        )}\r\n\r\n        {/* Course Success Modal */}\r\n        {selectedCourse && (\r\n          <CourseSuccessModal\r\n            course={selectedCourse}\r\n            isOpen={showSuccessModal}\r\n            onClose={handleSuccessModalClose}\r\n            actionType={successActionType}\r\n          />\r\n        )}\r\n\r\n        {/* Enrollment Modal */}\r\n        <Dialog\r\n          open={showEnrollModal}\r\n          onOpenChange={(open) => {\r\n            setShowEnrollModal(open);\r\n            if (!open) {\r\n              setError('');\r\n              setEnrollmentCode('');\r\n              setSelectedCourse(null);\r\n            }\r\n          }}\r\n        >\r\n          <DialogContent className='sm:max-w-md'>\r\n            <DialogHeader>\r\n              <DialogTitle className='text-xl'>\r\n                Daftar di {selectedCourse?.name}\r\n              </DialogTitle>\r\n              <p className='mt-1 text-sm text-gray-600'>\r\n                Masukkan kode pendaftaran yang diberikan oleh instruktur Anda\r\n              </p>\r\n            </DialogHeader>\r\n            <div className='mt-4 space-y-4'>\r\n              <div className='space-y-2 rounded-lg bg-gray-50 p-4'>\r\n                <p className='flex items-center text-gray-700'>\r\n                  <UsersIcon className='mr-2 h-4 w-4' />\r\n                  Instruktur: {selectedCourse?.instructor}\r\n                </p>\r\n                {selectedCourse && (\r\n                  <p className='flex items-center text-gray-700'>\r\n                    <CalendarIcon className='mr-2 h-4 w-4' />\r\n                    Durasi: {new Date(selectedCourse.startDate).toLocaleDateString('id-ID')} - {new Date(selectedCourse.endDate).toLocaleDateString('id-ID')}\r\n                  </p>\r\n                )}\r\n              </div>\r\n              <div className='space-y-1'>\r\n                <div className='relative'>\r\n                  <KeyIcon\r\n                    className={`absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform ${\r\n                      error ? 'text-red-500' : 'text-gray-400'\r\n                    }`}\r\n                  />\r\n                  <Input\r\n                    placeholder={selectedCourse ? `Masukkan kode (contoh: ${selectedCourse.enrollmentCode})` : 'Masukkan kode pendaftaran'}\r\n                    value={enrollmentCode}\r\n                    onChange={(e) => setEnrollmentCode(e.target.value)}\r\n                    className={`pl-10 ${error ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}`}\r\n                  />\r\n                </div>\r\n                {error && <p className='text-sm text-red-600'>{error}</p>}\r\n              </div>\r\n              <Button onClick={handleEnrollment} className='w-full' size='lg'>\r\n                Selesaikan Pendaftaran\r\n              </Button>\r\n            </div>\r\n          </DialogContent>\r\n        </Dialog>\r\n\r\n        {/* Feature Flags Modal */}\r\n        {SHOW_FEATURE_SETTINGS && (\r\n          <Dialog\r\n            open={showFeatureFlagsModal}\r\n            onOpenChange={setShowFeatureFlagsModal}\r\n          >\r\n            <DialogContent className='sm:max-w-md'>\r\n              <DialogHeader>\r\n                <DialogTitle>Pengaturan Fitur</DialogTitle>\r\n              </DialogHeader>\r\n              <div className='space-y-4'>\r\n                {Object.entries(flags).map(([key, value]) => (\r\n                  <div key={key} className='flex items-center justify-between'>\r\n                    <label className='text-sm font-medium'>\r\n                      {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}\r\n                    </label>\r\n                    <Button\r\n                      variant={value ? 'default' : 'outline'}\r\n                      size='sm'\r\n                      onClick={() => setFlag(key as keyof typeof flags, !value)}\r\n                    >\r\n                      {value ? 'AKTIF' : 'NONAKTIF'}\r\n                    </Button>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </DialogContent>\r\n          </Dialog>\r\n        )}\r\n\r\n\r\n        {/* Toast Notification */}\r\n        {toast.show && (\r\n          <div className='animate-in slide-in-from-bottom-2 fixed right-4 bottom-4 z-50'>\r\n            <div\r\n              className={`flex min-w-[300px] items-center space-x-3 rounded-lg px-6 py-4 shadow-lg ${\r\n                toast.type === 'success'\r\n                  ? 'bg-[var(--iai-primary)] text-white'\r\n                  : toast.type === 'loading'\r\n                  ? 'bg-blue-600 text-white'\r\n                  : 'bg-red-600 text-white'\r\n              } `}\r\n            >\r\n              {toast.type === 'success' ? (\r\n                <CheckCircleIcon className='h-5 w-5 flex-shrink-0' />\r\n              ) : toast.type === 'loading' ? (\r\n                <div className='h-5 w-5 flex-shrink-0 animate-spin border-2 border-white border-t-transparent rounded-full' />\r\n              ) : (\r\n                <XCircleIcon className='h-5 w-5 flex-shrink-0' />\r\n              )}\r\n              <p className='font-medium'>{toast.message}</p>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AvailableCoursesPage;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAMA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AACA;AAEA;AACA;AACA;AACA;AACA;;;AAnCA;;;;;;;;;;;;;;;AAqCA,gBAAgB;AAChB,MAAM,wBAAwB,OAAO,0CAA0C;AAM/E,MAAM,uBAAiC;;IACrC,UAAU;IACV,MAAM,EAAE,UAAU,EAAE,0BAA0B,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,4IAAA,CAAA,gBAAa,AAAD;IACnF,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,kBAAe,AAAD;IAEzC,mBAAmB;IACnB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAChE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAChE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAClE,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAC5E,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAClE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B;IACtF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACnD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAI9B;QACD,MAAM;QACN,SAAS;QACT,MAAM;IACR;IACA,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAElD,YAAY;IACZ,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,wCAAwC;IACxC,MAAM,kBAAkB;QACtB,MAAM,YAAY,mBAAmB,OAAO;QAC5C,IAAI,CAAC,WAAW;QAEhB,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG;QAClD,MAAM,mBAAmB,eAAe;QACxC,MAAM,mBAAmB,KAAK,2DAA2D;QAEzF,MAAM,iBAAiB,YAAY;QACnC,MAAM,mBAAmB,mBAAmB,oBAAoB,YAAY,eAAe,eAAe;QAG1G,eAAe;QACf,iBAAiB;QAEjB,2CAA2C;QAC3C,IAAI,YAAY,GAAG;YACjB,kBAAkB;QACpB;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,MAAM,YAAY,mBAAmB,OAAO;YAC5C,IAAI,CAAC,WAAW;YAEhB,gBAAgB;YAChB;YAEA,4BAA4B;YAC5B,MAAM,SAAS;yDAAW,IAAM;wDAAmB;YACnD,MAAM,SAAS;yDAAW,IAAM;wDAAmB;YACnD,MAAM,SAAS;yDAAW,IAAM;wDAAmB;YAEnD,uBAAuB;YACvB,MAAM,iBAAiB,IAAI;kDAAe;oBACxC;gBACF;;YACA,eAAe,OAAO,CAAC;YAEvB;kDAAO;oBACL,aAAa;oBACb,aAAa;oBACb,aAAa;oBACb,eAAe,UAAU;gBAC3B;;QACF;yCAAG;QAAC;QAAiB;KAAe;IAEpC,wCAAwC;IACxC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,IAAI,mBAAmB,gBAAgB;gBACrC,MAAM,QAAQ;4DAAW,IAAM;2DAAmB;gBAClD;sDAAO,IAAM,aAAa;;YAC5B;QACF;yCAAG;QAAC;QAAW;QAAiB;KAAe;IAE/C,yBAAyB;IACzB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR;QACF;yCAAG,EAAE;IAEL,MAAM,eAAe;QACnB,IAAI;YACF,aAAa;YACb,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,WAAW,KAAK,OAAO,IAAI,EAAE;YAC/B,OAAO;gBACL,UAAU,2BAA2B;YACvC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,UAAU,2BAA2B;QACvC,SAAU;YACR,aAAa;QACf;IACF;IAEA,WAAW;IACX,MAAM,YAAY,CAChB,SACA,OAA4B,SAAS;QAErC,SAAS;YAAE,MAAM;YAAM;YAAS;QAAK;QACrC,WAAW;YACT,SAAS,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,MAAM;gBAAM,CAAC;QAC9C,GAAG;IACL;IAEA,kBAAkB;IAClB,MAAM,oBAAoB,CAAC;QACzB,kBAAkB;QAClB,aAAa;QACb,mBAAmB;IACrB;IAEA,MAAM,oBAAoB,CAAC;QACzB,kBAAkB;QAClB,mBAAmB;IACrB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,kBAAkB;QAClB,oBAAoB;IACtB;IAEA,MAAM,uBAAuB;QAC3B,IAAI,gBAAgB;YAClB,2BAA2B;YAE3B,kCAAkC;YAClC,oBAAoB;YACpB,mBAAmB;YAEnB,qBAAqB;YACrB,qBAAqB;YACrB,oBAAoB;QAEpB,0DAA0D;QAC5D;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,gBAAgB;QAErB,MAAM,uBAAuB,eAAe,cAAc;QAE1D,oDAAoD;QACpD,IAAI,mBAAmB,eAAe,EAAE,GAAG;YACzC,SAAS;YACT;QACF;QAEA,IAAI,yBAAyB,gBAAgB;YAC3C,iCAAiC;YACjC,2BAA2B;YAE3B,qCAAqC;YACrC,mBAAmB;YACnB,mBAAmB;YAEnB,mBAAmB;YACnB,SAAS;YACT,kBAAkB;YAElB,qBAAqB;YACrB,qBAAqB;YACrB,oBAAoB;QACtB,OAAO;YACL,SAAS;QACX;IACF;IAGA,MAAM,0BAA0B;QAC9B,oBAAoB;QACpB,kBAAkB;IACpB;IAEA,wBAAwB;IACxB,MAAM,mBAAmB;IAEzB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,oIAAA,CAAA,cAAW;;;;;8BAGZ,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+NAAA,CAAA,iBAAY;oCAAC,WAAU;;;;;;8CACxB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAqB;;;;;;sDAGnC,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;wBAKhC,uCACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS,IAAM,yBAAyB;gCACxC,MAAK;;kDAEL,6LAAC,8NAAA,CAAA,iBAAY;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;gBAQhD,uCACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA4B;;;;;;8CAC1C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAW,GAAG,MAAM,oBAAoB,GAAG,mBAAmB,iBAAiB;;gDAAE;gDACzE,MAAM,oBAAoB,GAAG,UAAU;;;;;;;sDAErD,6LAAC;4CAAK,WAAW,GAAG,MAAM,oBAAoB,GAAG,mBAAmB,iBAAiB;;gDAAE;gDAClE,MAAM,oBAAoB,GAAG,UAAU;;;;;;;sDAE5D,6LAAC;4CAAK,WAAW,GAAG,MAAM,mBAAmB,GAAG,mBAAmB,iBAAiB;;gDAAE;gDACxE,MAAM,mBAAmB,GAAG,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAU7D,4BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAE,WAAU;kDAAyB;;;;;;;;;;;;0CAIxC,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;;wCAAM;sDAEpB,6LAAC,mOAAA,CAAA,mBAAc;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAQnC,0BACC,6LAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,mIAAA,CAAA,OAAI;4BAAS,WAAU;;8CACtB,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;;2BARV;;;;;;;;;yCAef,6LAAC;oBAAI,WAAU;8BACZ,iBAAiB,GAAG,CAAC,CAAC,uBACrB,6LAAC,yJAAA,CAAA,UAAiB;4BAEhB,QAAQ;4BACR,SAAS,IAAM,kBAAkB;4BACjC,UAAU,IAAM,kBAAkB;4BAClC,YAAY,IAAM,oBAAoB;4BACtC,YAAY,mBAAmB,OAAO,EAAE;2BALnC,OAAO,EAAE;;;;;;;;;;gBAWrB,CAAC,aAAa,iBAAiB,MAAM,KAAK,mBACzC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,+NAAA,CAAA,iBAAY;4BAAC,WAAU;;;;;;sCACxB,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;sCACpD,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAKjC,6LAAC,qIAAA,CAAA,SAAM;oBACL,MAAM;oBACN,cAAc,CAAC;wBACb,mBAAmB;wBACnB,IAAI,CAAC,MAAM,kBAAkB;oBAC/B;8BAEA,cAAA,6LAAC,qIAAA,CAAA,gBAAa;wBAAC,WAAU;;0CAEvB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qIAAA,CAAA,eAAY;8CACX,cAAA,6LAAC,qIAAA,CAAA,cAAW;wCAAC,WAAU;kDACpB,gBAAgB;;;;;;;;;;;;;;;;0CAMvB,6LAAC;gCACC,KAAK;gCACL,UAAU;gCACV,WAAW,CAAC,2EAA2E,EACrF,iBAAiB,gBAAgB,GAClC,CAAC,EAAE,cAAc,kBAAkB,GAAG,CAAC,EAAE,gBAAgB,oBAAoB,IAAI;;kDAElF,6LAAC;wCAAM,yBAAyB;4CAC9B,QAAQ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAwFT,CAAC;wCACH;;;;;;kDAEA,6LAAC;wCAAI,WAAU;kDACZ,gCACC,6LAAC,wJAAA,CAAA,UAAgB;4CACf,QAAQ;4CACR,WAAW;4CACX,aAAa;;;;;;;;;;;;;;;;;4BAUpB,+BACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,iOAAA,CAAA,kBAAa;4CAAC,WAAU;;;;;;sDACzB,6LAAC;sDAAK;;;;;;sDACN,6LAAC,iOAAA,CAAA,kBAAa;4CAAC,WAAU;;;;;;;;;;;;;;;;;4BAM9B,gCACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;wCACZ,eAAe,aAAa,IAAI,MAAM,oBAAoB,kBACzD,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,IAAM,oBAAoB;4CACnC,SAAQ;4CACR,WAAU;;gDACX;gDACe,eAAe,KAAK,GAAI,eAAe,QAAQ,KAAK,QAAQ,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC,eAAe,KAAK,IAAI,IAAI,KAAK,YAAY,CAAC,SAAS;oDAAE,OAAO;oDAAY,UAAU,eAAe,QAAQ,IAAI;gDAAM,GAAG,MAAM,CAAC,eAAe,KAAK,IAAK;;;;;;;wCAGpR,MAAM,oBAAoB,IAAI,eAAe,cAAc,kBAC1D,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,IAAM,kBAAkB;4CACjC,SAAQ;4CACR,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAWZ,gCACC,6LAAC,gJAAA,CAAA,UAAY;oBACX,QAAQ;oBACR,QAAQ;oBACR,SAAS,IAAM,oBAAoB;oBACnC,kBAAkB;;;;;;gBAKrB,gCACC,6LAAC,0JAAA,CAAA,UAAkB;oBACjB,QAAQ;oBACR,QAAQ;oBACR,SAAS;oBACT,YAAY;;;;;;8BAKhB,6LAAC,qIAAA,CAAA,SAAM;oBACL,MAAM;oBACN,cAAc,CAAC;wBACb,mBAAmB;wBACnB,IAAI,CAAC,MAAM;4BACT,SAAS;4BACT,kBAAkB;4BAClB,kBAAkB;wBACpB;oBACF;8BAEA,cAAA,6LAAC,qIAAA,CAAA,gBAAa;wBAAC,WAAU;;0CACvB,6LAAC,qIAAA,CAAA,eAAY;;kDACX,6LAAC,qIAAA,CAAA,cAAW;wCAAC,WAAU;;4CAAU;4CACpB,gBAAgB;;;;;;;kDAE7B,6LAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;0CAI5C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;;kEACX,6LAAC,iNAAA,CAAA,WAAS;wDAAC,WAAU;;;;;;oDAAiB;oDACzB,gBAAgB;;;;;;;4CAE9B,gCACC,6LAAC;gDAAE,WAAU;;kEACX,6LAAC,8NAAA,CAAA,iBAAY;wDAAC,WAAU;;;;;;oDAAiB;oDAChC,IAAI,KAAK,eAAe,SAAS,EAAE,kBAAkB,CAAC;oDAAS;oDAAI,IAAI,KAAK,eAAe,OAAO,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;kDAItI,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oNAAA,CAAA,YAAO;wDACN,WAAW,CAAC,2DAA2D,EACrE,QAAQ,iBAAiB,iBACzB;;;;;;kEAEJ,6LAAC,oIAAA,CAAA,QAAK;wDACJ,aAAa,iBAAiB,CAAC,uBAAuB,EAAE,eAAe,cAAc,CAAC,CAAC,CAAC,GAAG;wDAC3F,OAAO;wDACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wDACjD,WAAW,CAAC,MAAM,EAAE,QAAQ,2DAA2D,IAAI;;;;;;;;;;;;4CAG9F,uBAAS,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAEjD,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAS;wCAAkB,WAAU;wCAAS,MAAK;kDAAK;;;;;;;;;;;;;;;;;;;;;;;gBAQrE,uCACC,6LAAC,qIAAA,CAAA,SAAM;oBACL,MAAM;oBACN,cAAc;8BAEd,cAAA,6LAAC,qIAAA,CAAA,gBAAa;wBAAC,WAAU;;0CACvB,6LAAC,qIAAA,CAAA,eAAY;0CACX,cAAA,6LAAC,qIAAA,CAAA,cAAW;8CAAC;;;;;;;;;;;0CAEf,6LAAC;gCAAI,WAAU;0CACZ,OAAO,OAAO,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBACtC,6LAAC;wCAAc,WAAU;;0DACvB,6LAAC;gDAAM,WAAU;0DACd,IAAI,OAAO,CAAC,YAAY,OAAO,OAAO,CAAC,MAAM,CAAA,MAAO,IAAI,WAAW;;;;;;0DAEtE,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAS,QAAQ,YAAY;gDAC7B,MAAK;gDACL,SAAS,IAAM,QAAQ,KAA2B,CAAC;0DAElD,QAAQ,UAAU;;;;;;;uCATb;;;;;;;;;;;;;;;;;;;;;gBAoBnB,MAAM,IAAI,kBACT,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBACC,WAAW,CAAC,yEAAyE,EACnF,MAAM,IAAI,KAAK,YACX,uCACA,MAAM,IAAI,KAAK,YACf,2BACA,wBACL,CAAC,CAAC;;4BAEF,MAAM,IAAI,KAAK,0BACd,6LAAC,6OAAA,CAAA,wBAAe;gCAAC,WAAU;;;;;uCACzB,MAAM,IAAI,KAAK,0BACjB,6LAAC;gCAAI,WAAU;;;;;qDAEf,6LAAC,0NAAA,CAAA,eAAW;gCAAC,WAAU;;;;;;0CAEzB,6LAAC;gCAAE,WAAU;0CAAe,MAAM,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvD;GA5mBM;;QAEmE,4IAAA,CAAA,gBAAa;QACzD,iIAAA,CAAA,kBAAe;;;KAHtC;uCA8mBS", "debugId": null}}]}