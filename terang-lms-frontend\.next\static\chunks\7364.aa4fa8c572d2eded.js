try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},a=(new e.Error).stack;a&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[a]="017ba263-c1af-4565-ac0c-e7f8b3936105",e._sentryDebugIdIdentifier="sentry-dbid-017ba263-c1af-4565-ac0c-e7f8b3936105")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7364],{47364:(e,a,r)=>{r.r(a),r.d(a,{default:()=>S});var t=r(95155);r(12115);var n=r(18720),s=r(43909),o=r(33289),d=r(31561),l=r(70062),i=r(43042),c=r(9813),p=r(4556),u=r(62331),f=r(2066),y=r(4831),m=r(90140),x=r(2806),h=r(45757),b=r(16178),g=r(43066),j=r(33045),w=r(75481),k=r(37796),_=r(5498),E=r(97325),C=r(65259),I=r(61981),N=r(71937);function S(e){let{markdown:a,onChange:r,placeholder:S="Enter your content here...",className:v="min-h-[200px]"}=e;return(0,t.jsx)("div",{className:"mdx-editor-wrapper","data-sentry-component":"MDXEditorWrapper","data-sentry-source-file":"mdx-editor-wrapper.tsx",children:(0,t.jsx)(s.R,{markdown:a,onChange:r,contentEditableClassName:"prose prose-sm max-w-none",plugins:[(0,o.xO)(),(0,d.Zq)(),(0,l.G)(),(0,i.Y)(),(0,c.r)(),(0,p.O)(),(0,u.Mi)(),(0,f.Pz)({imageUploadHandler:async e=>{try{let a=await fetch("/api/upload?filename=".concat(e.name),{method:"POST",body:e});if(!a.ok)throw Error("Upload failed");return(await a.json()).url}catch(e){return console.error("Image upload failed:",e),n.oR.error("Failed to upload image"),""}}}),(0,y.c3)(),(0,m.oe)({defaultCodeBlockLanguage:"javascript"}),(0,x.sl)({codeBlockLanguages:{javascript:"JavaScript",typescript:"TypeScript",python:"Python",html:"HTML",css:"CSS",json:"JSON",markdown:"Markdown"}}),(0,h.F7)({toolbarContents:()=>(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(b.l,{}),(0,t.jsx)(g.p,{}),(0,t.jsx)(j.S,{}),(0,t.jsx)(w._,{}),(0,t.jsx)(k.d,{}),(0,t.jsx)(_.N,{}),(0,t.jsx)(E.I,{}),(0,t.jsx)(C._,{}),(0,t.jsx)(I.a,{}),(0,t.jsx)(N.O,{})]})})],placeholder:S,className:v,"data-sentry-element":"MDXEditor","data-sentry-source-file":"mdx-editor-wrapper.tsx"})})}}}]);