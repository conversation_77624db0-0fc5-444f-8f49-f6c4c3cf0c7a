{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/kbar/index.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/kbar/index.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/kbar/index.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiS,GAC9T,+DACA", "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/kbar/index.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/kbar/index.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/kbar/index.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6Q,GAC1S,2CACA", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/contexts/enrollment-context.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const EnrollmentProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call EnrollmentProvider() from the server but EnrollmentProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/enrollment-context.tsx <module evaluation>\",\n    \"EnrollmentProvider\",\n);\nexport const useEnrollment = registerClientReference(\n    function() { throw new Error(\"Attempted to call useEnrollment() from the server but useEnrollment is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/enrollment-context.tsx <module evaluation>\",\n    \"useEnrollment\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,qEACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,qEACA", "debugId": null}}, {"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/contexts/enrollment-context.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const EnrollmentProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call EnrollmentProvider() from the server but EnrollmentProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/enrollment-context.tsx\",\n    \"EnrollmentProvider\",\n);\nexport const useEnrollment = registerClientReference(\n    function() { throw new Error(\"Attempted to call useEnrollment() from the server but useEnrollment is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/enrollment-context.tsx\",\n    \"useEnrollment\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,iDACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,iDACA", "debugId": null}}, {"offset": {"line": 81, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/app/%28course-view%29/layout.tsx"], "sourcesContent": ["import KBar from '@/components/kbar';\r\nimport { EnrollmentProvider } from '@/contexts/enrollment-context';\r\nimport type { Metadata } from 'next';\r\n\r\nexport const metadata: Metadata = {\r\n  title: 'Course - LMS',\r\n  description: 'Learning Management System - Course View'\r\n};\r\n\r\nexport default function CourseViewLayout({\r\n  children\r\n}: {\r\n  children: React.ReactNode;\r\n}) {\r\n  return (\r\n    <EnrollmentProvider>\r\n      <KBar>{children}</KBar>\r\n    </EnrollmentProvider>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAGO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS,iBAAiB,EACvC,QAAQ,EAGT;IACC,qBACE,8OAAC,yIAAA,CAAA,qBAAkB;kBACjB,cAAA,8OAAC,mIAAA,CAAA,UAAI;sBAAE;;;;;;;;;;;AAGb", "debugId": null}}]}