{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/active-theme.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport {\r\n  ReactNode,\r\n  createContext,\r\n  useContext,\r\n  useEffect,\r\n  useState\r\n} from 'react';\r\n\r\nconst COOKIE_NAME = 'active_theme';\r\nconst DEFAULT_THEME = 'default';\r\n\r\nfunction setThemeCookie(theme: string) {\r\n  if (typeof window === 'undefined') return;\r\n\r\n  document.cookie = `${COOKIE_NAME}=${theme}; path=/; max-age=31536000; SameSite=Lax; ${window.location.protocol === 'https:' ? 'Secure;' : ''}`;\r\n}\r\n\r\ntype ThemeContextType = {\r\n  activeTheme: string;\r\n  setActiveTheme: (theme: string) => void;\r\n};\r\n\r\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\r\n\r\nexport function ActiveThemeProvider({\r\n  children,\r\n  initialTheme\r\n}: {\r\n  children: ReactNode;\r\n  initialTheme?: string;\r\n}) {\r\n  const [activeTheme, setActiveTheme] = useState<string>(\r\n    () => initialTheme || DEFAULT_THEME\r\n  );\r\n\r\n  useEffect(() => {\r\n    setThemeCookie(activeTheme);\r\n\r\n    Array.from(document.body.classList)\r\n      .filter((className) => className.startsWith('theme-'))\r\n      .forEach((className) => {\r\n        document.body.classList.remove(className);\r\n      });\r\n    document.body.classList.add(`theme-${activeTheme}`);\r\n    if (activeTheme.endsWith('-scaled')) {\r\n      document.body.classList.add('theme-scaled');\r\n    }\r\n  }, [activeTheme]);\r\n\r\n  return (\r\n    <ThemeContext.Provider value={{ activeTheme, setActiveTheme }}>\r\n      {children}\r\n    </ThemeContext.Provider>\r\n  );\r\n}\r\n\r\nexport function useThemeConfig() {\r\n  const context = useContext(ThemeContext);\r\n  if (context === undefined) {\r\n    throw new Error(\r\n      'useThemeConfig must be used within an ActiveThemeProvider'\r\n    );\r\n  }\r\n  return context;\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAUA,MAAM,cAAc;AACpB,MAAM,gBAAgB;AAEtB,SAAS,eAAe,KAAa;IACnC,wCAAmC;;AAGrC;AAOA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,SAAS,oBAAoB,EAClC,QAAQ,EACR,YAAY,EAIb;IACC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAC3C,IAAM,gBAAgB;IAGxB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,eAAe;QAEf,MAAM,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,EAC/B,MAAM,CAAC,CAAC,YAAc,UAAU,UAAU,CAAC,WAC3C,OAAO,CAAC,CAAC;YACR,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;QACjC;QACF,SAAS,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,aAAa;QAClD,IAAI,YAAY,QAAQ,CAAC,YAAY;YACnC,SAAS,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;QAC9B;IACF,GAAG;QAAC;KAAY;IAEhB,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;YAAa;QAAe;kBACzD;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MACR;IAEJ;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/layout/providers.tsx"], "sourcesContent": ["'use client';\nimport { useTheme } from 'next-themes';\nimport React from 'react';\nimport { ActiveThemeProvider } from '../active-theme';\n\nexport default function Providers({\n  activeThemeValue,\n  children\n}: {\n  activeThemeValue: string;\n  children: React.ReactNode;\n}) {\n  return (\n    <>\n      <ActiveThemeProvider initialTheme={activeThemeValue}>\n        {children}\n      </ActiveThemeProvider>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKe,SAAS,UAAU,EAChC,gBAAgB,EAChB,QAAQ,EAIT;IACC,qBACE;kBACE,cAAA,8OAAC,qIAAA,CAAA,sBAAmB;YAAC,cAAc;sBAChC;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 96, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/sonner.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useTheme } from 'next-themes';\r\nimport { Toaster as Sonner, ToasterProps } from 'sonner';\r\n\r\nconst Toaster = ({ ...props }: ToasterProps) => {\r\n  const { theme = 'system' } = useTheme();\r\n\r\n  return (\r\n    <Sonner\r\n      theme={theme as ToasterProps['theme']}\r\n      className='toaster group'\r\n      style={\r\n        {\r\n          '--normal-bg': 'var(--popover)',\r\n          '--normal-text': 'var(--popover-foreground)',\r\n          '--normal-border': 'var(--border)'\r\n        } as React.CSSProperties\r\n      }\r\n      {...props}\r\n    />\r\n  );\r\n};\r\n\r\nexport { Toaster };\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,UAAU,CAAC,EAAE,GAAG,OAAqB;IACzC,MAAM,EAAE,QAAQ,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAEpC,qBACE,8OAAC,wIAAA,CAAA,UAAM;QACL,OAAO;QACP,WAAU;QACV,OACE;YACE,eAAe;YACf,iBAAiB;YACjB,mBAAmB;QACrB;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/layout/ThemeToggle/theme-provider.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport {\r\n  ThemeProvider as NextThemesProvider,\r\n  ThemeProviderProps\r\n} from 'next-themes';\r\n\r\nexport default function ThemeProvider({\r\n  children,\r\n  ...props\r\n}: ThemeProviderProps) {\r\n  return <NextThemesProvider {...props}>{children}</NextThemesProvider>;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAOe,SAAS,cAAc,EACpC,QAAQ,EACR,GAAG,OACgB;IACnB,qBAAO,8OAAC,gJAAA,CAAA,gBAAkB;QAAE,GAAG,KAAK;kBAAG;;;;;;AACzC", "debugId": null}}]}